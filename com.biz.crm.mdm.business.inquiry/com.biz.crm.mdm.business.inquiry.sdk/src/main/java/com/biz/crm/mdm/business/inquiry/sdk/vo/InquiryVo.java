package com.biz.crm.mdm.business.inquiry.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 询价InquiryVo
 *
 * <AUTHOR>
 * @date 2022/1/5
 */
@Data
@ApiModel("询价InquiryVo")
public class InquiryVo {
    /**
     * 价格编码
     */
    @ApiModelProperty("价格编码")
    private String priceCode;
    /**
     * 价格类型编码
     */
    @ApiModelProperty("价格类型编码")
    private String typeCode;
    /**
     * 定价维度编码
     */
    @ApiModelProperty("定价维度编码")
    private String typeDetailCode;
    /**
     * 价格
     */
    @ApiModelProperty("价格")
    private BigDecimal price;

    @ApiModelProperty("成本价格")
    private BigDecimal costPrice;

    @ApiModelProperty("成本价格单位")
    private Integer costPriceUnit;

    @ApiModelProperty("物料成本单价")
    private BigDecimal materialUnitPrice;

    @ApiModelProperty("奶卡价格")
    private BigDecimal milkPrice;

    /**
     * 价格绑定维度，商品维度
     */
    @ApiModelProperty("价格绑定维度")
    private String dimensionCode;
    /**
     * 价格绑定维度源数据编码,如商品编码
     */
    @ApiModelProperty("价格绑定维度源数据编码")
    private String relateCode;
    /**
     * 优先级
     */
    @ApiModelProperty("优先级")
    private Integer sort;
    /**
     * 维度关联key
     */
    @ApiModelProperty("维度关联key")
    private String relateCodeJoin;
}
