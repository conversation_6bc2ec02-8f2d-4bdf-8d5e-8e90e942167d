package com.biz.crm.mdm.business.inquiry.sdk.service;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.mdm.business.inquiry.sdk.vo.InquiryVo;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * 价格查询sdk接口
 *
 * <AUTHOR>
 * @date 2022/1/4
 */
public interface InquiryVoService {

  /**
   * 查询价格
   *
   * @param object
   * @return
   */
  default Map<String, InquiryVo> handleSearchPrice(JSONObject object) {
    return Maps.newHashMap();
  }

  /**
   * 询价，sdk建议使用该方法
   *
   * @param object
   * @return
   */
  default Map<String, InquiryVo> findPrice(JSONObject object) {
    return Maps.newHashMap();
  }

  /**
   * 询价，sdk建议使用该方法
   *
   * @param object
   * @return
   */
  default Map<String, InquiryVo> findPriceNew(JSONObject object) {
    return Maps.newHashMap();
  }

  default Map<String, InquiryVo> findMaterialUnitPrice(JSONObject object) {
    return Maps.newHashMap();
  }

  /**
   * 方式1询价，sdk建议使用该方法
   *
   * @param object
   * @return
   */
  default Map<String, InquiryVo> findBusinessPrice(JSONObject object) {
    return Maps.newHashMap();
  }

}
