package com.biz.crm.mdm.business.position.level.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 职位级别dto
 *
 * <AUTHOR>
 * @date 2021/9/28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "职位级别dto", description = "职位级别")
public class PositionLevelDto extends TenantFlagOpDto {

  /**
   * 职位级别编码
   */
  @ApiModelProperty("职位级别编码")
  private String positionLevelCode;
  /**
   * 职位级别名称
   */
  @ApiModelProperty("职位级别名称")
  private String positionLevelName;
  /**
   * 职位级别生成职位名称后缀增长序列号
   */
  @ApiModelProperty("职位级别生成职位名称后缀增长序列号")
  private Integer suffixSequence;
  /**
   * 角色编码，英文逗号间隔
   */
  @ApiModelProperty("角色编码，英文逗号间隔")
  private String roleCode;
  /**
   * 角色名称，英文逗号间隔
   */
  @ApiModelProperty("角色名称，英文逗号间隔")
  private String roleName;
  /**
   * 数据业务状态（启用状态）
   */
  @ApiModelProperty("数据业务状态（启用状态）")
  private String enableStatus;

  /**
   * 用于下拉列表选择,输入回显编码集合，字符串集合，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据
   */
  @ApiModelProperty("用于下拉列表选择,输入回显编码集合，字符串集合，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据")
  private List<String> selectedCode;
}