package com.biz.crm.mdm.business.position.level.sdk.deprecated.vo;

import com.biz.crm.business.common.sdk.deprecated.vo.CrmExtTenVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 职位级别 返回vo
 *
 * <AUTHOR>
 * @date 2020-10-22 10:56:51
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "职位级别")
@Deprecated
public class MdmPositionLevelSelectVo extends CrmExtTenVo {

  @ApiModelProperty("职位级别编码")
  private String positionLevelCode;

  @ApiModelProperty("职位级别名称")
  private String positionLevelName;

}