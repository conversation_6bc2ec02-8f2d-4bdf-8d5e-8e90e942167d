package com.biz.crm.mdm.business.position.level.sdk.event;

import com.biz.crm.mdm.business.position.level.sdk.vo.PositionLevelVo;

import java.util.List;

/**
 * 职位级别信息变更事件通知接口
 *
 * <AUTHOR>
 * @date 2021/9/30
 */
public interface PositionLevelEventListener {

  /**
   * 当职位级别创建时触发
   *
   * @param vo 创建时的VO
   */
  void onCreate(PositionLevelVo vo);

  /**
   * 当职位级别修改时触发
   *
   * @param oldVo 修改之前的VO
   * @param newVo 修改之后的VO
   */
  void onUpdate(PositionLevelVo oldVo, PositionLevelVo newVo);

  /**
   * 当职位级别禁用时触发
   *
   * @param voList 禁用vo信息
   */
  void onDisable(List<PositionLevelVo> voList);

  /**
   * 当职位级别启用时触发
   *
   * @param voList 禁用vo信息
   */
  void onEnable(List<PositionLevelVo> voList);

  /**
   * 当职位级别删除时触发
   *
   * @param voList 删除vo信息
   */
  void onDelete(List<PositionLevelVo> voList);
}
