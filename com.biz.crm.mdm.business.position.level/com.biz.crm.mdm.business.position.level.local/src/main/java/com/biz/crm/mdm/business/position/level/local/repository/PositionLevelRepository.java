package com.biz.crm.mdm.business.position.level.local.repository;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.position.level.local.entity.PositionLevelEntity;
import com.biz.crm.mdm.business.position.level.local.mapper.PositionLevelMapper;
import com.biz.crm.mdm.business.position.level.sdk.dto.PositionLevelDto;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 职位级别的数据库访问类 {@link PositionLevelEntity}
 *
 * <AUTHOR>
 * @date 2021/9/28
 */
@Component
public class PositionLevelRepository extends ServiceImpl<PositionLevelMapper, PositionLevelEntity> {

  @Autowired(required = false)
  private PositionLevelMapper positionLevelMapper;

  /**
   * 职位级别分页列表
   *
   * @param pageable 分页信息
   * @param dto      分页参数dto
   * @return 分页列表
   */
  public Page<PositionLevelEntity> findByConditions(Pageable pageable, PositionLevelDto dto) {
    pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
    dto = ObjectUtils.defaultIfNull(dto, new PositionLevelDto());
    if (StringUtil.isEmpty(dto.getTenantCode())) {
      dto.setTenantCode(TenantUtils.getTenantCode());
    }
    if (StringUtil.isEmpty(dto.getDelFlag())) {
      dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    }
    Page<PositionLevelEntity> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return this.baseMapper.findByConditions(page, dto);
  }


  /**
   * 根据职位级别ID集合或者code集合查询职位级别列表
   *
   * @param ids                职位级别ID集合
   * @param positionLevelCodes 职位级别编码集合
   * @param tenantCode         租户编码
   * @return 职位级别实体
   */
  public List<PositionLevelEntity> findByIdsOrCodes(List<String> ids, List<String> positionLevelCodes, String tenantCode) {
    if (CollectionUtils.isNotEmpty(ids)) {
      ids.removeIf(Objects::isNull);
    }
    if (CollectionUtils.isNotEmpty(positionLevelCodes)) {
      positionLevelCodes.removeIf(Objects::isNull);
    }
    return this.lambdaQuery()
            .eq(PositionLevelEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(PositionLevelEntity::getTenantCode, tenantCode)
            .in(CollectionUtils.isNotEmpty(ids), PositionLevelEntity::getId, ids)
            .in(CollectionUtils.isNotEmpty(positionLevelCodes), PositionLevelEntity::getPositionLevelCode, positionLevelCodes)
            .list();
  }

  /**
   * 通过职位级别编码集合获取职位级别实体集合
   *
   * @param positionLevelCodeList 职位级别编码集合
   * @param tenantCode            租户编码
   * @return 职位级别实体集合
   */
  public List<PositionLevelEntity> findByCodeIn(List<String> positionLevelCodeList, String tenantCode) {
    return this.lambdaQuery()
            .eq(PositionLevelEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(PositionLevelEntity::getTenantCode, tenantCode)//设置租户编号信息
            .in(PositionLevelEntity::getPositionLevelCode, positionLevelCodeList)
            .select(PositionLevelEntity::getPositionLevelCode)
            .list();
  }

  /**
   * 查询相同职级名称的数据列表
   *
   * @param positionLevelName 职级名称
   * @param tenantCode        租户编码
   * @return 相同职级名称的职位级别列表
   */
  public List<PositionLevelEntity> findByName(String positionLevelName, String tenantCode) {
    return positionLevelMapper.findByName(positionLevelName, tenantCode);
  }

  /**
   * 批量更新启用禁用状态
   *
   * @param ids          职级ID集合
   * @param enableStatus 启用禁用状态
   */
  public void updateEnableStatusByIds(List<String> ids, EnableStatusEnum enableStatus) {
    this.lambdaUpdate()
            .in(PositionLevelEntity::getId, ids)
            .eq(PositionLevelEntity::getTenantCode, TenantUtils.getTenantCode())//设置租户编号信息
            .set(PositionLevelEntity::getEnableStatus, enableStatus.getCode())
            .update();
  }

  /**
   * 批量删除职位级别
   *
   * @param ids 职级ID集合
   */
  public void updateDelFlagByIds(List<String> ids) {
    this.lambdaUpdate()
            .in(PositionLevelEntity::getId, ids)
            .eq(PositionLevelEntity::getTenantCode, TenantUtils.getTenantCode())//设置租户编号信息
            .set(PositionLevelEntity::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
            .update();
  }

  /**
   * 更新职位名称后缀增长序列号
   *
   * @param suffixSequence 职位名称后缀增长序列号
   * @param id             职位级别Id
   */
  public void updateSuffixSequenceById(Integer suffixSequence, String id) {
    this.lambdaUpdate()
            .eq(PositionLevelEntity::getId, id)
            .eq(PositionLevelEntity::getTenantCode, TenantUtils.getTenantCode())//设置租户编号信息
            .set(PositionLevelEntity::getSuffixSequence, suffixSequence)
            .update();
  }

  /**
   * 通过角色编码集合获取职位级别信息
   *
   * @param roleCodes  角色编码集合
   * @param tenantCode 租户编码
   * @return 职位级别实体集合
   */
  public List<PositionLevelEntity> findByRoleCodes(List<String> roleCodes, String tenantCode) {
    return this.baseMapper.findByRoleCodes(roleCodes, tenantCode);
  }

  /**
   * 重构查询方法  通过id和租户编号查询
   * @param id
   * @param tenantCode
   * @return
   */
  public PositionLevelEntity findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(PositionLevelEntity::getTenantCode,tenantCode)
        .in(PositionLevelEntity::getId,id)
        .one();
  }

  /**
   * 重构修改方法
   * @param entity
   * @param tenantCode
   */
  public void updateByIdAndTenantCode(PositionLevelEntity entity, String tenantCode) {
    LambdaUpdateWrapper<PositionLevelEntity>lambdaUpdateWrapper= Wrappers.lambdaUpdate();
    lambdaUpdateWrapper.eq(PositionLevelEntity::getTenantCode,tenantCode);
    lambdaUpdateWrapper.in(PositionLevelEntity::getId,entity.getId());
    this.baseMapper.update(entity,lambdaUpdateWrapper);
  }

  public List<PositionLevelEntity> listByIdsAndTenantCode(List<String> ids, String tenantCode) {
    return this.lambdaQuery()
        .eq(PositionLevelEntity::getTenantCode,tenantCode)
        .in(PositionLevelEntity::getId,ids)
        .list();
  }

  /**
   * 通过职位级别名称模糊查询职位级别列表
   *
   * @param positionLevelName 职位级别名称
   * @param tenantCode   租户编码
   * @return 职位级别列表
   */
  public List<PositionLevelEntity> findByPositionLevelNameLike(String positionLevelName, String tenantCode) {
    return this.lambdaQuery()
        .eq(PositionLevelEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(PositionLevelEntity::getTenantCode, tenantCode)
        .like(PositionLevelEntity::getPositionLevelName, positionLevelName)
        .select(PositionLevelEntity::getPositionLevelName, PositionLevelEntity::getPositionLevelCode)
        .list();
  }
}
