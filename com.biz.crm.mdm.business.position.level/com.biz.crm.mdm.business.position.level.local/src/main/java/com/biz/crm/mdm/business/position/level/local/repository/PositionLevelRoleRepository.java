package com.biz.crm.mdm.business.position.level.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.position.level.local.mapper.PositionLevelRoleMapper;
import com.biz.crm.mdm.business.position.level.local.entity.PositionLevelRoleEntity;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 职位级别角色关联的数据库访问类 {@link PositionLevelRoleEntity}
 *
 * <AUTHOR>
 * @date 2021/9/28
 */
@Component
public class PositionLevelRoleRepository extends ServiceImpl<PositionLevelRoleMapper, PositionLevelRoleEntity> {

  /**
   * 通过职位级别编码集合获取职位级别角色关联实体列表
   *
   * @param positionLevelCodeList 职位级别编码集合
   * @param tenantCode            租户编码
   * @return 职位级别角色关联列表
   */
  public List<PositionLevelRoleEntity> findByPositionLevelCodeIn(List<String> positionLevelCodeList, String tenantCode) {
    return super.lambdaQuery()
            .eq(PositionLevelRoleEntity::getTenantCode, tenantCode)
            .in(PositionLevelRoleEntity::getPositionLevelCode, positionLevelCodeList)
            .list();
  }

  /**
   * 通过职位级别编码获取职位级别角色关联实体列表
   *
   * @param positionLevelCode 职位级别编码
   * @param tenantCode        租户编码
   * @return 职位级别角色关联列表
   */
  public List<PositionLevelRoleEntity> findByPositionLevelCode(String positionLevelCode, String tenantCode) {
    return super.lambdaQuery()
            .eq(PositionLevelRoleEntity::getTenantCode, tenantCode)
            .eq(PositionLevelRoleEntity::getPositionLevelCode, positionLevelCode)
            .list();
  }

  /**
   * 通过职位级别编码和角色编码集合获取职位级别角色关联实体列表
   *
   * @param positionLevelCode 职位级别编码
   * @param roleCodes         角色编码集合
   * @param tenantCode        租户编码
   * @return 职位级别角色关联列表
   */
  public List<PositionLevelRoleEntity> findByLevelCodeAndRoleCodeIn(String positionLevelCode, List<String> roleCodes
          , String tenantCode) {
    return super.lambdaQuery()
            .eq(PositionLevelRoleEntity::getTenantCode, tenantCode)
            .eq(PositionLevelRoleEntity::getPositionLevelCode, positionLevelCode)
            .in(PositionLevelRoleEntity::getRoleCode, roleCodes)
            .list();
  }

  /**
   * 通过职位级别编码集合删除角色关联关系
   *
   * @param positionLevelCodeList 职位级别编码集合
   * @param tenantCode            租户编码
   */
  public void deleteByLevelCodeIn(List<String> positionLevelCodeList, String tenantCode) {
    this.lambdaUpdate()
            .eq(PositionLevelRoleEntity::getTenantCode, tenantCode)
            .in(PositionLevelRoleEntity::getPositionLevelCode, positionLevelCodeList)
            .remove();
  }

  /**
   * 通过角色编码集合删除角色关联关系
   *
   * @param roleCodes  角色编码集合
   * @param tenantCode 租户编码
   */
  public void deleteByRoleCodeIn(List<String> roleCodes, String tenantCode) {
    this.lambdaUpdate()
            .eq(PositionLevelRoleEntity::getTenantCode, tenantCode)
            .in(PositionLevelRoleEntity::getRoleCode, roleCodes)
            .remove();
  }

  /**
   * 通过角色编码集合获取职位级别角色关联实体列表
   *
   * @param roleCodes  角色编码集合
   * @param tenantCode 租户编码
   * @return 职位级别角色关联实体列表
   */
  public List<PositionLevelRoleEntity> findByRoleCodes(List<String> roleCodes, String tenantCode) {
    return super.lambdaQuery()
            .eq(PositionLevelRoleEntity::getTenantCode, tenantCode)
            .in(PositionLevelRoleEntity::getRoleCode, roleCodes)
            .list();
  }
}
