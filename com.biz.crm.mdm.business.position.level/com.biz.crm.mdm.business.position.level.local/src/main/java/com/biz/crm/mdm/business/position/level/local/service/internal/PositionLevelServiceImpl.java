package com.biz.crm.mdm.business.position.level.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.mdm.business.position.level.local.entity.PositionLevelEntity;
import com.biz.crm.mdm.business.position.level.local.repository.PositionLevelRepository;
import com.biz.crm.mdm.business.position.level.local.service.PositionLevelRoleService;
import com.biz.crm.mdm.business.position.level.local.service.PositionLevelService;
import com.biz.crm.mdm.business.position.level.sdk.dto.PositionLevelDto;
import com.biz.crm.mdm.business.position.level.sdk.dto.PositionLevelLogDto;
import com.biz.crm.mdm.business.position.level.sdk.event.PositionLevelEventListener;
import com.biz.crm.mdm.business.position.level.sdk.event.PositionLevelLogEventListener;
import com.biz.crm.mdm.business.position.level.sdk.service.PositionLevelVoService;
import com.biz.crm.mdm.business.position.level.sdk.vo.PositionLevelVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 职位级别 接口实现
 *
 * <AUTHOR>
 * @date 2020-10-22 10:56:51
 */
@Service
public class PositionLevelServiceImpl implements PositionLevelService {

  @Autowired(required = false)
  @Lazy
  private List<PositionLevelEventListener> listeners;
  @Autowired(required = false)
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;
  @Autowired(required = false)
  private PositionLevelRepository positionLevelRepository;
  @Autowired(required = false)
  private PositionLevelRoleService positionLevelRoleService;
  @Autowired(required = false)
  private GenerateCodeService generateCodeService;
  @Autowired(required = false)
  private NebulaNetEventClient nebulaNetEventClient;
  @Autowired(required = false)
  private PositionLevelVoService positionLevelVoService;
  @Autowired(required = false)
  @Lazy
  private List<PositionLevelLogEventListener> positionLevelLogEventListeners;

  @Override
  @Transactional
  public PositionLevelEntity create(PositionLevelDto dto) {
    this.createValidation(dto);
    dto.setSuffixSequence(0);
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    dto.setTenantCode(TenantUtils.getTenantCode());   //新增租户编号
    PositionLevelEntity entity = nebulaToolkitService.copyObjectByWhiteList(dto, PositionLevelEntity.class, HashSet.class, ArrayList.class);
    if (StringUtils.isEmpty(entity.getPositionLevelCode())) {
      entity.setPositionLevelCode(generateCodeService.generateCode("ZWJB"));
    }
    List<PositionLevelEntity> list = this.positionLevelRepository.findByCodeIn(Lists.newArrayList(entity.getPositionLevelCode()),dto.getTenantCode());
    Validate.isTrue(CollectionUtils.isEmpty(list), "职位级别编码已经存在");
    this.positionLevelRepository.save(entity);
    if (StringUtils.isNotEmpty(dto.getRoleCode())) {
      this.positionLevelRoleService.bindByPositionLevelCodeAndRoleCodes(entity.getPositionLevelCode()
          , Arrays.asList(dto.getRoleCode().split(",")));
    }
    //创建职位级别事件通知
    if (CollectionUtils.isNotEmpty(this.listeners)) {
      PositionLevelVo vo = this.nebulaToolkitService.copyObjectByWhiteList(entity, PositionLevelVo.class, HashSet.class, ArrayList.class);
      this.listeners.forEach(eventListener -> {
        eventListener.onCreate(vo);
      });
    }
    PositionLevelVo newVo = positionLevelVoService.findById(entity.getId());
    PositionLevelDto newDto = this.nebulaToolkitService.copyObjectByWhiteList(newVo, PositionLevelDto.class, HashSet.class, ArrayList.class);
    //日志处理
    PositionLevelLogDto positionLevelLogDto = new PositionLevelLogDto();
    positionLevelLogDto.setNewest(newDto);
    SerializableBiConsumer<PositionLevelLogEventListener, PositionLevelLogDto> onCreate =
        PositionLevelLogEventListener::onCreate;
    this.nebulaNetEventClient.publish(positionLevelLogDto, PositionLevelLogEventListener.class, onCreate);
    return entity;
  }

  @Override
  @Transactional
  public PositionLevelEntity update(PositionLevelDto dto) {
    this.updateValidation(dto);
    PositionLevelEntity entity = this.positionLevelRepository.findByIdAndTenantCode(dto.getId(),dto.getTenantCode());   //重构查询方法
    //查询日志旧对象
    PositionLevelVo oldVo = positionLevelVoService.findById(dto.getId());
    PositionLevelDto oldDto = this.nebulaToolkitService.copyObjectByWhiteList(oldVo, PositionLevelDto.class, HashSet.class, ArrayList.class);
    Validate.notNull(entity, "职位级别不存在");
    Validate.isTrue(entity.getPositionLevelCode().equals(dto.getPositionLevelCode()), "职位级别编码不能修改");
    Validate.isTrue(entity.getTenantCode().equals(dto.getTenantCode()), "租户编码不匹配");
    entity.setPositionLevelName(dto.getPositionLevelName());
    entity.setEnableStatus(dto.getEnableStatus());
    entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    entity.setModifyAccount(null);
    entity.setModifyTime(null);
    entity.setModifyName(null);
    //重构修改方法
    this.positionLevelRepository.updateByIdAndTenantCode(entity,TenantUtils.getTenantCode());
    this.positionLevelRoleService.unbindByPositionLevelCode(entity.getPositionLevelCode());
    if (StringUtils.isNotEmpty(dto.getRoleCode())) {
      this.positionLevelRoleService.bindByPositionLevelCodeAndRoleCodes(entity.getPositionLevelCode()
          , Arrays.asList(dto.getRoleCode().split(",")));
    }
    //修改职位级别事件通知
    if (CollectionUtils.isNotEmpty(this.listeners)) {
      PositionLevelVo newVo = this.nebulaToolkitService.copyObjectByWhiteList(entity, PositionLevelVo.class, HashSet.class, ArrayList.class);
      newVo.setRoleCode(dto.getRoleCode());
      newVo.setRoleName(dto.getRoleName());
      this.listeners.forEach(eventListener -> {
        eventListener.onUpdate(oldVo, newVo);
      });
    }
    //日志处理
    PositionLevelLogDto positionLevelLogDto = new PositionLevelLogDto();
    PositionLevelVo newVo = positionLevelVoService.findById(dto.getId());
    positionLevelLogDto.setNewest(this.nebulaToolkitService.copyObjectByWhiteList(newVo, PositionLevelDto.class, HashSet.class, ArrayList.class));
    positionLevelLogDto.setOriginal(oldDto);
    SerializableBiConsumer<PositionLevelLogEventListener, PositionLevelLogDto> onUpdate =
        PositionLevelLogEventListener::onUpdate;
    this.nebulaNetEventClient.publish(positionLevelLogDto, PositionLevelLogEventListener.class, onUpdate);
    return entity;
  }

  @Override
  @Transactional
  public void deleteBatch(List<String> ids) {
    Validate.isTrue(CollectionUtils.isNotEmpty(ids), "缺失id");
    List<PositionLevelEntity> entities = this.positionLevelRepository.listByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
    Validate.isTrue(CollectionUtils.isNotEmpty(entities) && entities.size() == ids.size(), "数据删除个数不匹配");
    List<String> positionLevelCodeList = entities.stream().map(PositionLevelEntity::getPositionLevelCode).collect(Collectors.toList());
    //解绑角色
    this.positionLevelRoleService.unbindByPositionLevelCodeIn(positionLevelCodeList);
    //逻辑删除
    this.positionLevelRepository.updateDelFlagByIds(ids);
    //删除职位级别事件通知
    if (CollectionUtils.isNotEmpty(this.listeners)) {
      List<PositionLevelVo> voList = Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(entities, PositionLevelEntity.class
          , PositionLevelVo.class, HashSet.class, ArrayList.class));
      this.listeners.forEach(eventListener -> {
        eventListener.onDelete(voList);
      });
    }
  }

  @Override
  @Transactional
  public void enableBatch(List<String> ids) {
    Validate.isTrue(CollectionUtils.isNotEmpty(ids), "缺失id");
    List<PositionLevelEntity> entities = this.positionLevelRepository.listByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
    Validate.isTrue(CollectionUtils.isNotEmpty(entities) && entities.size() == ids.size(), "数据启用个数不匹配");
    this.positionLevelRepository.updateEnableStatusByIds(ids, EnableStatusEnum.ENABLE);
    //启用职位级别事件通知
    if (CollectionUtils.isNotEmpty(this.listeners)) {
      List<PositionLevelVo> voList = Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(entities, PositionLevelEntity.class
          , PositionLevelVo.class, HashSet.class, ArrayList.class));
      this.listeners.forEach(eventListener -> {
        eventListener.onEnable(voList);
      });
    }
    //日志处理
    for (PositionLevelEntity entity : entities) {
      PositionLevelDto oldDto =new PositionLevelDto();
      PositionLevelDto newDto =new PositionLevelDto();
      oldDto.setId(entity.getId());
      oldDto.setEnableStatus(entity.getEnableStatus());
      newDto.setId(entity.getId());
      newDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
      PositionLevelLogDto positionLevelLogDto = new PositionLevelLogDto();
      positionLevelLogDto.setNewest(newDto);
      positionLevelLogDto.setOriginal(oldDto);
      SerializableBiConsumer<PositionLevelLogEventListener, PositionLevelLogDto> onUpdate =
          PositionLevelLogEventListener::onUpdate;
      this.nebulaNetEventClient.publish(positionLevelLogDto, PositionLevelLogEventListener.class, onUpdate);
    }
  }

  @Override
  @Transactional
  public void disableBatch(List<String> ids) {
    Validate.isTrue(CollectionUtils.isNotEmpty(ids), "缺失id");
    List<PositionLevelEntity> entities = this.positionLevelRepository.listByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
    Validate.isTrue(CollectionUtils.isNotEmpty(entities) && entities.size() == ids.size(), "数据禁用个数不匹配");
    this.positionLevelRepository.updateEnableStatusByIds(ids, EnableStatusEnum.DISABLE);
    //禁用职位级别事件通知
    if (CollectionUtils.isNotEmpty(this.listeners)) {
      List<PositionLevelVo> voList = Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(entities, PositionLevelEntity.class
          , PositionLevelVo.class, HashSet.class, ArrayList.class));
      this.listeners.forEach(eventListener -> {
        eventListener.onDisable(voList);
      });
    }
    //日志处理
    for (PositionLevelEntity entity : entities) {
      PositionLevelDto oldDto =new PositionLevelDto();
      PositionLevelDto newDto =new PositionLevelDto();
      oldDto.setId(entity.getId());
      oldDto.setEnableStatus(entity.getEnableStatus());
      newDto.setId(entity.getId());
      newDto.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
      PositionLevelLogDto positionLevelLogDto = new PositionLevelLogDto();
      positionLevelLogDto.setNewest(newDto);
      positionLevelLogDto.setOriginal(oldDto);
      SerializableBiConsumer<PositionLevelLogEventListener, PositionLevelLogDto> onUpdate =
          PositionLevelLogEventListener::onUpdate;
      this.nebulaNetEventClient.publish(positionLevelLogDto, PositionLevelLogEventListener.class, onUpdate);
    }
  }

  /**
   * 职位级别分页列表
   *
   * @param pageable 分页信息
   * @param dto      分页参数dto
   * @return 分页列表
   */
  @Override
  public Page<PositionLevelEntity> findByConditions(Pageable pageable, PositionLevelDto dto) {
    return this.positionLevelRepository.findByConditions(pageable, dto);
  }

  @Override
  public List<PositionLevelEntity> findByIdsOrCodes(List<String> ids, List<String> positionLevelCodes) {
    if (CollectionUtils.isEmpty(ids) && CollectionUtils.isEmpty(positionLevelCodes)) {
      return Lists.newArrayList();
    }
    return this.positionLevelRepository.findByIdsOrCodes(ids, positionLevelCodes, TenantUtils.getTenantCode());
  }

  @Override
  public void updateSuffixSequenceById(Integer suffixSequence, String id) {
    Validate.notBlank(id, "职位id不能为空");
    Validate.isTrue(Objects.nonNull(suffixSequence) && suffixSequence >= 0, "增长序列号不符合规范");
    this.positionLevelRepository.updateSuffixSequenceById(suffixSequence, id);
  }

  @Override
  public List<PositionLevelEntity> findByRoleCodes(List<String> roleCodes, String tenantCode) {
    if (CollectionUtils.isEmpty(roleCodes) || StringUtils.isBlank(tenantCode)) {
      return Lists.newArrayList();
    }
    return this.positionLevelRepository.findByRoleCodes(roleCodes, tenantCode);
  }

  /**
   * 在创建positionLevel模型对象之前，检查对象各属性的正确性，其主键属性必须没有值
   *
   * @param dto 检查对象
   */
  private void createValidation(PositionLevelDto dto) {
    Validate.notNull(dto, "进行当前操作时，信息对象必须传入!");
    dto.setId(null);
    Validate.notBlank(dto.getPositionLevelName(), "缺失职位级别名称");
    Validate.notBlank(dto.getEnableStatus(), "缺失启用状态");
    Validate.isTrue(dto.getPositionLevelName().length() < 64, "职位级别名称，在进行添加时填入值超过了限定长度(64)，请检查!");
    Validate.isTrue(StringUtils.isBlank(dto.getPositionLevelCode()) || dto.getPositionLevelCode().length() < 64, "职位级别编码，在进行添加时填入值超过了限定长度(64)，请检查!");
    dto.setTenantCode(TenantUtils.getTenantCode());
    List<PositionLevelEntity> sameNameList = this.positionLevelRepository.findByName(dto.getPositionLevelName(), dto.getTenantCode());
    Validate.isTrue(CollectionUtils.isEmpty(sameNameList), "当前职位级别名称已存在，请重新输入");
  }

  /**
   * 在修改positionLevel模型对象之前，检查对象各属性的正确性，其主键属性必须没有值
   *
   * @param dto 检查对象
   */
  private void updateValidation(PositionLevelDto dto) {
    Validate.notNull(dto, "进行当前操作时，信息对象必须传入!");
    Validate.notBlank(dto.getId(), "修改信息时，id不能为空！");
    Validate.notBlank(dto.getPositionLevelCode(), "缺失职位级别编码");
    Validate.notBlank(dto.getPositionLevelName(), "缺失职位级别名称");
    Validate.notBlank(dto.getEnableStatus(), "缺失启用状态");
    Validate.isTrue(dto.getPositionLevelName().length() < 64, "职位级别名称，在进行修改时填入值超过了限定长度(64)，请检查!");
    dto.setTenantCode(TenantUtils.getTenantCode());
    List<PositionLevelEntity> sameNameList = this.positionLevelRepository.findByName(dto.getPositionLevelName(), dto.getTenantCode());
    if (CollectionUtils.isNotEmpty(sameNameList)) {
      sameNameList = sameNameList.stream().filter(x -> !dto.getId().equals(x.getId())).collect(Collectors.toList());
      Validate.isTrue(CollectionUtils.isEmpty(sameNameList), "当前职位级别名称已存在，请重新输入");
    }
  }
}
