package com.biz.crm.mdm.business.position.level.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.position.level.sdk.dto.PositionLevelDto;
import com.biz.crm.mdm.business.position.level.sdk.service.PositionLevelVoService;
import com.biz.crm.mdm.business.position.level.sdk.vo.PositionLevelVo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 职位管理: PositionLevelVo: 职位级别
 *
 * <AUTHOR>
 * @date 2021/9/30
 */
@Slf4j
@RestController
@RequestMapping("/v1/positionLevel/positionLevel")
@Api(tags = "职位管理: PositionLevelVo: 职位级别")
public class PositionLevelVoController {

  @Autowired(required = false)
  private PositionLevelVoService positionLevelVoService;

  @ApiOperation(value = "职位级别分页查询")
  @GetMapping("/findByConditions")
  public Result<Page<PositionLevelVo>> findByConditions(@PageableDefault(50) Pageable pageable,
                                                        @ApiParam(name = "PositionLevelDto", value = "分页Dto") PositionLevelDto positionLevelDto) {
    try {
      return Result.ok(positionLevelVoService.findByConditions(pageable, positionLevelDto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  @ApiOperation(value = "详情(编辑页面使用),通过主键进行数据的查询")
  @GetMapping("/findById")
  public Result<PositionLevelVo> findById(@RequestParam(value = "id", required = false) @ApiParam(name = "id", value = "主键ID") String id) {
    try {
      return Result.ok(positionLevelVoService.findById(id));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "详情,通过主键或者职位级别编码进行数据的查询")
  @GetMapping("/findByIdOrCode")
  public Result<PositionLevelVo> findByIdOrCode(@RequestParam(value = "id", required = false) @ApiParam(name = "id", value = "主键ID") String id
      , @RequestParam(value = "positionLevelCode", required = false) @ApiParam(name = "positionLevelCode", value = "职位级别编码") String positionLevelCode) {
    try {
      if (StringUtils.isBlank(id) && StringUtils.isBlank(positionLevelCode)) {
        return Result.ok((PositionLevelVo) null);
      }
      List<PositionLevelVo> list = positionLevelVoService.findByIdsOrCodes(Lists.newArrayList(id), Lists.newArrayList(positionLevelCode));
      if (CollectionUtils.isEmpty(list)) {
        return Result.ok((PositionLevelVo) null);
      }
      return Result.ok(list.get(0));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据职位级别ID集合或者code集合查询职位级别列表")
  @GetMapping("/findByIdsOrCodes")
  public Result<List<PositionLevelVo>> findByIdsOrCodes(@RequestParam(value = "ids", required = false) @ApiParam(name = "ids", value = "主键ID集合") List<String> ids
      , @RequestParam(value = "positionLevelCodes", required = false) @ApiParam(name = "positionLevelCodes", value = "职位级别编码集合") List<String> positionLevelCodes) {
    try {
      return Result.ok(positionLevelVoService.findByIdsOrCodes(ids, positionLevelCodes));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
