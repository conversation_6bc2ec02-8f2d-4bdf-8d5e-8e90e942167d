/*
 * @Author: lei.chen
 * @Date: 2023-12-13 10:56:58
 * @LastEditors: lei.chen
 * @LastEditTime: 2023-12-14 16:20:24
 * @Description: 
 */
package com.biz.crm.mdm.business.dictionary.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.dictionary.sdk.dto.DictTypeDto;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictTypeVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 数据字典类型表接口
 *
 * <AUTHOR>
 */
public interface DictTypeVoService {

  /**
   * 分页列表
   *
   * @param pageable 分页参数
   * @param dto      业务参数
   * @return
   */
  Page<DictTypeVo> findByConditions(Pageable pageable, DictTypeDto dto);

  /**
   * 通过id查询详情
   *
   * @param id 字典类型ID
   * @return
   */
  DictTypeVo findById(String id);

  /**
   * 通过编码查询详情
   *
   * @param dictTypeCode 字典类型
   * @return
   */
  DictTypeVo findByDictTypeCode(String dictTypeCode);

  /**
   * 通过编码数组，查询详情列表
   * @param reqVo
   */
  List<DictTypeVo> findByDictTypeCodeList(List<String> dictTypeCodeList);
  /**
   * 新增
   *
   * @param reqVo
   */
  void save(DictTypeDto reqVo);

  /**
   * 更新
   *
   * @param reqVo
   */
  void update(DictTypeDto reqVo);

  /**
   * 删除
   *
   * @param ids
   * @return
   */
  void deleteBatch(List<String> ids);

  /**
   * 启用
   *
   * @param ids ID集合
   */
  void enableBatch(List<String> ids);

  /**
   * 禁用
   *
   * @param ids ID集合
   */
  void disableBatch(List<String> ids);
}

