package com.biz.crm.mdm.business.dictionary.sdk.event;

import com.biz.crm.mdm.business.dictionary.sdk.vo.DictAttrConfVo;

import java.util.List;

/**
 * DictAttrConfEventListener 字典数据操作事件通知
 *
 * <AUTHOR>
 */
public interface DictAttrConfEventListener {

  /**
   * 当删除时，触发事件
   *
   * @param vos
   * @return
   */
  void onDelete(List<DictAttrConfVo> vos);

  /**
   * 当启用时，触发事件
   *
   * @param vos
   * @return
   */
  void onEnable(List<DictAttrConfVo> vos);

  /**
   * 当禁用时，触发事件
   *
   * @param vos
   * @return
   */
  void onDisable(List<DictAttrConfVo> vos);

  /**
   * 当修改时，触发事件
   *
   * @param oldVo
   * @param newVo
   * @return
   */
  void onChange(DictAttrConfVo oldVo, DictAttrConfVo newVo);

}
