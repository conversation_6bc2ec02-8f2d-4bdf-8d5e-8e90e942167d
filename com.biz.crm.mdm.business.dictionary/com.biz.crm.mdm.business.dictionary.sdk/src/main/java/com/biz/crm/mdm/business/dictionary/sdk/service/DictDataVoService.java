package com.biz.crm.mdm.business.dictionary.sdk.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.dictionary.sdk.dto.DictDataDto;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * 数据字典明细表接口
 *
 * <AUTHOR>
 */
public interface DictDataVoService {

  /**
   * 根据dictTypeCode获取字段数据列表
   *
   * @param dictTypeCode 字典类型编码
   * @return
   */
  List<DictDataVo> findByDictTypeCode(String dictTypeCode);

  /**
   * 根据dictTypeCode获取树形结构
   *
   * @param dictTypeCode 字典类型
   * @return
   */
  List<DictDataVo> findTreeByDictTypeCode(String dictTypeCode);

  /**
   * 根据dictTypeCode获取树形结构
   * @param dictTypeCode 字典类型
   * @param dictCode 字典编码 模糊搜索使用
   * @return
   */
  List<DictDataVo> findTreeByDictTypeCode(String dictTypeCode, String dictCode);

  /**
   * 根据dictTypeCode获取树形结构 分页
   * @param pageable 分页
   * @param dictTypeCode 字典类型
   * @param dictCode 字典编码 模糊搜索使用
   * @return
   */
  Page<DictDataVo> findTreeByDictTypeCode(Pageable pageable, String dictTypeCode, String dictCode);

  /**
   * 查询详情
   *
   * @param id ID主键
   * @return
   */
  DictDataVo findById(String id);

  /**
   * 根据字典类型和字典编码查询详情
   *
   * @param dictTypeCode 字典类型
   * @param dictCode     字典编码
   * @return
   */
  DictDataVo findByDictTypeCodeAndDictCode(String dictTypeCode, String dictCode);

  /**
   * 批量获取数据字典下拉框
   *
   * @param dictTypeCodeList 字典类型集合
   * @return
   */
  Map<String, List<DictDataVo>> findByDictTypeCodeList(List<String> dictTypeCodeList);

  /**
   * 获取包含扩展信息（属性设置）的字典列表
   *
   * @param dto
   * @return
   */
  List<JSONObject> findContainExtendByConditions(DictDataDto dto);

  /**
   * 根据字典类型编码获取字典数据
   * @param dictTypeCode 字典类型编码
   * @return List
   * */
  List<DictDataVo> tree(String dictTypeCode);

  /**
   * 根据数据字典编码获取数据字典MAP
   *
   * @param dictTypeCode
   * @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, java.lang.String>>
   * @author: huxmld
   * @version: v1.0.0
   * @date: 2024/5/26 11:36
   */
  Map<String, String> findMapByDictTypeCode(String dictTypeCode);


  /**
   * 根据数据字典编码获取数据字典MAP
   *
   * @param dictTypeCodeList
   * @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, java.lang.String>>
   * @author: huxmld
   * @version: v1.0.0
   * @date: 2024/5/26 11:36
   */
  Map<String, Map<String, String>> findMapByDictTypeCodeList(List<String> dictTypeCodeList);

  /**
   * 根据数据字典编码获取数据字典反转MAP
   *
   * @param dictTypeCode
   * @return java.util.Map<java.lang.String, java.lang.String>
   * @author: huxmld
   * @version: v1.0.0
   * @date: 2024/5/26 11:31
   */
  Map<String, String> findReverseMapByDictTypeCode(String dictTypeCode);

  /**
   * 根据数据字典编码获取数据字典反转MAP
   *
   * @param dictTypeCodeList
   * @return java.util.Map<java.lang.String, java.lang.String>
   * @author: huxmld
   * @version: v1.0.0
   * @date: 2024/5/26 11:31
   */
  Map<String, Map<String, String>> findReverseMapByDictTypeCodeList(List<String> dictTypeCodeList);

}

