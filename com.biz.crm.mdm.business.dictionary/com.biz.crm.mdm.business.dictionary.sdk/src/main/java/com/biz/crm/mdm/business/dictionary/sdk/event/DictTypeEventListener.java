package com.biz.crm.mdm.business.dictionary.sdk.event;

import com.biz.crm.mdm.business.dictionary.sdk.vo.DictTypeVo;

import java.util.List;

/**
 * DictTypeEventListener 字典数据操作事件通知
 *
 * <AUTHOR>
 */
public interface DictTypeEventListener {

  /**
   * 当删除时，触发事件
   *
   * @param vos
   * @return
   */
  void onDelete(List<DictTypeVo> vos);

  /**
   * 当启用时，触发事件
   *
   * @param vos
   * @return
   */
  void onEnable(List<DictTypeVo> vos);

  /**
   * 当禁用时，触发事件
   *
   * @param vos
   * @return
   */
  void onDisable(List<DictTypeVo> vos);

  /**
   * 当修改时，触发事件
   *
   * @param oldVo
   * @param newVo
   * @return
   */
  void onChange(DictTypeVo oldVo, DictTypeVo newVo);

}
