package com.biz.crm.mdm.business.dictionary.sdk.dto;

import com.bizunited.nebula.event.sdk.service.NebulaEventDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 数据字典明细表事件引擎事件通知Dto
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "DictDataNebulaEventBatchDto", description = "数据字典明细表事件引擎事件通知Dto")
public class DictDataNebulaEventBatchDto implements NebulaEventDto {

  @ApiModelProperty("数据字典信息集合")
  private List<DictDataNebulaEventDto> dictDataNebulaEventDtoList;
}