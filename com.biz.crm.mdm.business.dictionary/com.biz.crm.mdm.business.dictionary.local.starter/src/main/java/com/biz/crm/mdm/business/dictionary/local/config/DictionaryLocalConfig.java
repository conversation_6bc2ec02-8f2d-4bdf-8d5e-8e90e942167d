package com.biz.crm.mdm.business.dictionary.local.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 数据字典模块配置
 *
 * <AUTHOR>
 * @date 2021/10/21
 */
@Configuration
@EntityScan(basePackages = "com.biz.crm.mdm.business.dictionary.local.entity")
@ComponentScan(basePackages = {"com.biz.crm.mdm.business.dictionary"})
public class DictionaryLocalConfig {

}