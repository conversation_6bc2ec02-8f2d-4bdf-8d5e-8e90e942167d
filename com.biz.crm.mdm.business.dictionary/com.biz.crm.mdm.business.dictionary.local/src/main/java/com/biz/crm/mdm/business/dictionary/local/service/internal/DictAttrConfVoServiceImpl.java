package com.biz.crm.mdm.business.dictionary.local.service.internal;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.dictionary.local.entity.DictAttrConfEntity;
import com.biz.crm.mdm.business.dictionary.local.entity.DictTypeEntity;
import com.biz.crm.mdm.business.dictionary.local.repository.DictAttrConfRepository;
import com.biz.crm.mdm.business.dictionary.local.repository.DictTypeRepository;
import com.biz.crm.mdm.business.dictionary.local.service.DictDataService;
import com.biz.crm.mdm.business.dictionary.sdk.dto.DictAttrConfDto;
import com.biz.crm.mdm.business.dictionary.sdk.event.DictAttrConfEventListener;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictAttrConfVoService;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictAttrConfVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 数据字典扩展字段配置表接口实现
 * <AUTHOR>
 */
@Service
public class DictAttrConfVoServiceImpl implements DictAttrConfVoService {
  @Autowired(required = false)
  private DictAttrConfRepository dictAttrConfRepository;
  @Autowired(required = false)
  private DictTypeRepository dictTypeRepository;

  @Autowired(required = false)
  @Lazy
  private DictDataVoService dictDataVoService;

  @Autowired(required = false)
  @Lazy
  private DictDataService dictDataService;

  @Autowired(required = false)
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;
  /**
   * 字段事件监听器
   */
  @Autowired(required = false)
  @Lazy
  private List<DictAttrConfEventListener> dictAttrConfEventListeners;

  /**
   * 基于数据库执行的数据视图执行内容缓存（最多500毫秒）
   */
  private static volatile Cache<String, List<DictAttrConfVo>> cache = null;

  public DictAttrConfVoServiceImpl(){
    if(cache == null) {
      synchronized (DictAttrConfVoServiceImpl.class) {
        while(cache == null) {
          cache = CacheBuilder.newBuilder()
                  .initialCapacity(10000)
                  .expireAfterWrite(500, TimeUnit.MILLISECONDS)
                  .maximumSize(100000)
                  .build();
        }
      }
    }
  }

  @Override
  public List<DictAttrConfVo> findByDictTypeCode(String dictTypeCode) {
    if (StringUtils.isBlank(dictTypeCode)) {
      return new ArrayList<>();
    }
    // 优先从缓存中获取
    String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), dictTypeCode);
    List<DictAttrConfVo> graph = cache.getIfPresent(cacheKey);
    if (graph != null) {
      return graph;
    }
    List<DictAttrConfEntity> list = this.dictAttrConfRepository.findByDictAttrConfCode(TenantUtils.getTenantCode(), dictTypeCode);
    if (CollectionUtils.isEmpty(list)) {
      return new ArrayList<>();
    }
    graph = (List<DictAttrConfVo>) this.nebulaToolkitService.copyCollectionByWhiteList(list, DictAttrConfEntity.class, DictAttrConfVo.class, HashSet.class, ArrayList.class);
    // 设置缓存
    cache.put(cacheKey, graph);
    return graph;
  }

  @Override
  public List<DictAttrConfVo> findByDictTypeCodes(List<String> dictTypeCodes) {
    if (CollectionUtils.isEmpty(dictTypeCodes)) {
      return new ArrayList<>();
    }
    List<DictAttrConfEntity> list = this.dictAttrConfRepository.findByDictTypeCodes(TenantUtils.getTenantCode(), dictTypeCodes);
    if (CollectionUtils.isEmpty(list)) {
      return new ArrayList<>();
    }
    return (List<DictAttrConfVo>) this.nebulaToolkitService.copyCollectionByWhiteList(list, DictAttrConfEntity.class, DictAttrConfVo.class, HashSet.class, ArrayList.class);
  }

  @Override
  public DictAttrConfVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    //重构查询方法
    DictAttrConfEntity entity = this.dictAttrConfRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
    if (entity == null) {
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(entity, DictAttrConfVo.class, LinkedHashSet.class, ArrayList.class);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void save(DictAttrConfDto dto) {
    verify(dto);
    dto.setId(null);
    //已经占用的
    Set<String> extFieldList = new HashSet<>(16);
    List<DictAttrConfEntity> list = this.dictAttrConfRepository.findByDictAttrConfCode(TenantUtils.getTenantCode(), dto.getDictTypeCode());
    if (CollectionUtils.isNotEmpty(list)) {
      extFieldList.addAll(list.stream().map(DictAttrConfEntity::getExtField).collect(Collectors.toSet()));
    }
    //分配一个扩展字段
    String extField = "";
    //总共有哪些扩展字段
    List<String> defaultExtFieldList = Arrays.asList("ext1", "ext2", "ext3", "ext4", "ext5", "ext6", "ext7", "ext8", "ext9", "ext10");
    for (String item : defaultExtFieldList) {
      if (!extFieldList.contains(item)) {
        extField = item;
        break;
      }
    }
    Validate.notBlank(extField, "扩展字段范围（ext1 ~ ext10）");
    DictAttrConfEntity entity = this.nebulaToolkitService.copyObjectByWhiteList(dto, DictAttrConfEntity.class, HashSet.class, ArrayList.class);
    entity.setExtField(extField);
    dictDataService.deleteExtFieldVal(entity.getDictTypeCode(), Collections.singletonList(extField));
    entity.setTenantCode(TenantUtils.getTenantCode());
    entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    this.dictAttrConfRepository.save(entity);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void update(DictAttrConfDto dto) {
    verify(dto);
    Validate.notBlank(dto.getId(), "id不能为空");
    DictAttrConfEntity oldEntity = this.dictAttrConfRepository.findByIdAndTenantCode(dto.getId(),TenantUtils.getTenantCode());
    Validate.notNull(oldEntity, "已删除或不存在");
    Validate.notBlank(dto.getExtField(), "占用数据表的扩展字段不能为空");
    Validate.isTrue(oldEntity.getExtField().equals(dto.getExtField()), "占用数据表的扩展字段不能修改");
    if ((dto.getSelectDictTypeCode() != null && !dto.getSelectDictTypeCode().equals(oldEntity.getSelectDictTypeCode())) || (oldEntity.getSelectDictTypeCode() != null && !oldEntity.getSelectDictTypeCode().equals(dto.getSelectDictTypeCode()))) {
      dictDataService.deleteExtFieldVal(oldEntity.getDictTypeCode(), Collections.singletonList(oldEntity.getExtField()));
    }
    // TODO 清理缓存
    DictAttrConfEntity entity = this.nebulaToolkitService.copyObjectByWhiteList(dto, DictAttrConfEntity.class, HashSet.class, ArrayList.class);
    if (StringUtil.isEmpty(entity.getTenantCode())){
      entity.setTenantCode(TenantUtils.getTenantCode());
    }
    //重构修改方法
    this.dictAttrConfRepository.updateByIdAndTenantCode(entity,TenantUtils.getTenantCode());
    // 更新物料时通知商品及商品的所有关联主数据信息做相应逻辑处理
    if (CollectionUtils.isNotEmpty(dictAttrConfEventListeners)) {
      DictAttrConfVo oldVo = this.nebulaToolkitService.copyObjectByWhiteList(oldEntity, DictAttrConfVo.class, HashSet.class, ArrayList.class);
      DictAttrConfVo newVo = this.nebulaToolkitService.copyObjectByWhiteList(entity, DictAttrConfVo.class, HashSet.class, ArrayList.class);
      dictAttrConfEventListeners.forEach(event -> event.onChange(oldVo, newVo));
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void deleteBatch(List<String> ids) {
    Validate.notEmpty(ids, "缺失参数");
    List<DictAttrConfEntity> entities = this.dictAttrConfRepository.getBaseMapper().selectBatchIds(ids);
    Validate.isTrue(CollectionUtils.isNotEmpty(entities), "已删除或不存在");
    // 删除关联信息
    Map<String, List<DictAttrConfEntity>> map = entities.stream().collect(Collectors.groupingBy(DictAttrConfEntity::getDictTypeCode));
    for (Map.Entry<String, List<DictAttrConfEntity>> entry : map.entrySet()) {
      List<String> extFieldList = entry.getValue().stream().map(DictAttrConfEntity::getExtField).collect(Collectors.toList());
      dictDataService.deleteExtFieldVal(entry.getKey(), extFieldList);
    }
    // TODO 清理缓存
    this.dictAttrConfRepository.updateDelFlagByIdIn(DelFlagStatusEnum.DELETE, ids);
    // 更新物料时通知商品及商品的所有关联主数据信息做相应逻辑处理
    if (CollectionUtils.isNotEmpty(dictAttrConfEventListeners)) {
      List<DictAttrConfVo> voList = (List<DictAttrConfVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entities, DictAttrConfEntity.class, DictAttrConfVo.class, HashSet.class, ArrayList.class);
      dictAttrConfEventListeners.forEach(event -> event.onDelete(voList));
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void deleteByDictTypeCode(String dictTypeCode) {
    Validate.notBlank(dictTypeCode, "字典类型编码不能为空");
    List<DictAttrConfEntity> entities = this.dictAttrConfRepository.findByDictAttrConfCode(TenantUtils.getTenantCode(), dictTypeCode);
    if (CollectionUtils.isEmpty(entities)) {
      return;
    }
    Set<String> ids = entities.stream().map(DictAttrConfEntity::getId).collect(Collectors.toSet());
    // TODO 清理缓存
    this.dictAttrConfRepository.updateDelFlagByIdIn(DelFlagStatusEnum.DELETE, ids);
    // 更新物料时通知商品及商品的所有关联主数据信息做相应逻辑处理
    if (CollectionUtils.isNotEmpty(dictAttrConfEventListeners)) {
      List<DictAttrConfVo> voList = (List<DictAttrConfVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entities, DictAttrConfEntity.class, DictAttrConfVo.class, HashSet.class, ArrayList.class);
      dictAttrConfEventListeners.forEach(event -> event.onDelete(voList));
    }
  }

  /**
   * 校验
   *
   * @param dto
   */
  private void verify(DictAttrConfDto dto) {
    Validate.notNull(dto, "请求参数对象不能为空");
    Validate.notBlank(dto.getDictTypeCode(), "缺失数据字典");
    Validate.notBlank(dto.getFieldCode(), "字段编码不能为空");
    Validate.notBlank(dto.getFieldName(), "字段名称不能为空");
    Validate.notNull(dto.getShowOrder(), "显示顺序不能为空");
    Validate.notNull(dto.getRequired(), "是否必填不能为空");
    if (StringUtils.isNotEmpty(dto.getSelectDictTypeCode())) {
      DictTypeEntity selectDict = dictTypeRepository.findByDictTypeCode(TenantUtils.getTenantCode() , dto.getSelectDictTypeCode());
      Validate.notNull(selectDict, "下拉框数据字典类型编码无效");
    }
    DictTypeEntity one = dictTypeRepository.findByDictTypeCode(TenantUtils.getTenantCode() , dto.getDictTypeCode());
    Validate.notNull(one, "未找到关联数据字典类型");
    List<DictAttrConfEntity> list = this.dictAttrConfRepository.findByDictAttrConfCode(TenantUtils.getTenantCode(), dto.getDictTypeCode())
        .stream().filter(x -> StringUtils.isEmpty(dto.getId()) || !dto.getId().equals(x.getId())).collect(Collectors.toList());
    if (CollectionUtils.isNotEmpty(list)) {
      Set<String> fieldList = list.stream().map(DictAttrConfEntity::getFieldCode).collect(Collectors.toSet());
      Validate.isTrue(!fieldList.contains(dto.getFieldCode()), "当前数据字典下已经存在该字段：" + dto.getFieldCode());
    }
  }
}
