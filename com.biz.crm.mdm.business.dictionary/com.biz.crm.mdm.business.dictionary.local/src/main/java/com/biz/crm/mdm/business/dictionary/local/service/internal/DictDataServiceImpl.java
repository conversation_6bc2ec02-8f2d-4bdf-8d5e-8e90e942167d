package com.biz.crm.mdm.business.dictionary.local.service.internal;

import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.dictionary.local.entity.DictAttrConfEntity;
import com.biz.crm.mdm.business.dictionary.local.entity.DictDataEntity;
import com.biz.crm.mdm.business.dictionary.local.repository.DictAttrConfRepository;
import com.biz.crm.mdm.business.dictionary.local.repository.DictDataRepository;
import com.biz.crm.mdm.business.dictionary.local.service.DictDataService;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.dto.DictDataDto;
import com.biz.crm.mdm.business.dictionary.sdk.dto.DictDataNebulaEventBatchDto;
import com.biz.crm.mdm.business.dictionary.sdk.dto.DictDataNebulaEventDto;
import com.biz.crm.mdm.business.dictionary.sdk.dto.DictDataNebulaEventUpdateDto;
import com.biz.crm.mdm.business.dictionary.sdk.event.DictDataEventListener;
import com.biz.crm.mdm.business.dictionary.sdk.event.DictDataNebulaEventListener;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictTypeVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictTypeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据字典明细表接口实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DictDataServiceImpl implements DictDataService {

  @Autowired(required = false)
  private DictDataRepository dictDataRepository;

  @Autowired(required = false)
  private DictTypeVoService dictTypeVoService;

  @Autowired(required = false)
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;

  @Autowired(required = false)
  @Lazy
  private List<DictDataEventListener> dictDataEventListeners;

  @Autowired(required = false)
  private NebulaNetEventClient nebulaNetEventClient;
  @Autowired(required = false)
  private DictAttrConfRepository dictAttrConfRepository;

  /**
   * 通过反射给对象字段赋值
   *
   * @param object    对象
   * @param filedName 字段名
   * @param value     字段值
   */
  private static void setFieldValue(Object object, String filedName, Object value) {
    if (object == null || StringUtils.isBlank(filedName)) {
      return;
    }
    Field field = ReflectionUtils.findField(object.getClass(), filedName);
    if (field == null) {
      return;
    }
    ReflectionUtils.makeAccessible(field);
    ReflectionUtils.setField(field, object, value);
  }

  /**
   * 反射获取对象中的字段值
   *
   * @param obj  对象
   * @param name 字段名
   * @return
   */
  private Object getFiledValueByName(Object obj, String name) {
    Object simpleProperty = "";
    try {
      Field field = ReflectionUtils.findField(obj.getClass(), name);
      if (field == null) {
        return "";
      }
      ReflectionUtils.makeAccessible(field);
      simpleProperty = ReflectionUtils.getField(field, obj);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
    return simpleProperty;
  }

  @Override
  @Transactional
  public void save(DictDataDto dto) {
    Validate.notNull(dto, "字典对象不能为空");
    dto.setId(null);
    Validate.notBlank(dto.getDictCode(), "字典编码不能为空");
    Validate.isTrue(!dto.getDictCode().contains(DictConstant.PATH_SPLIT), "字典编码不能包含字符“" + DictConstant.PATH_SPLIT + "”");
    //Validate.isTrue(!dto.getDictCode().contains(","), "字典编码不能包含字符“,”");
    Validate.notBlank(dto.getDictValue(), "字典值不能为空");
    Validate.notBlank(dto.getDictTypeCode(), "对应字典类型不能为空");
    DictTypeVo dict = dictTypeVoService.findByDictTypeCode(dto.getDictTypeCode());
    Validate.notNull(dict, "对应字典不存在");
    DictDataEntity oldEntity = this.dictDataRepository.findByDictTypeCodeAndDictCode(TenantUtils.getTenantCode(), dto.getDictTypeCode(), dto.getDictCode());
    Validate.isTrue(Objects.isNull(oldEntity), "字典编码已存在");
    if (StringUtils.isNotBlank(dto.getParentDictCode())) {
      DictDataEntity parent = this.dictDataRepository.findByDictTypeCodeAndDictCode(TenantUtils.getTenantCode(), dict.getDictTypeCode(), dto.getParentDictCode());
      Validate.notNull(parent, "上级字典不存在");
    }

    //校验扩展字段
    List<DictAttrConfEntity> confList = this.dictAttrConfRepository.findByDictAttrConfCode(TenantUtils.getTenantCode(), dict.getDictTypeCode());
    if (CollectionUtils.isNotEmpty(confList)) {
      for (DictAttrConfEntity conf : confList) {
        if (BooleanEnum.TRUE.getNumStr().equals(String.valueOf(conf.getRequired()))) {
          Object filedValueByName = this.getFiledValueByName(dto, conf.getExtField());
          if (filedValueByName == null || StringUtils.isEmpty(filedValueByName.toString())) {
            throw new IllegalArgumentException(conf.getFieldName() + "是必填项");
          }
        }
      }
    }

    DictDataEntity entity = this.nebulaToolkitService.copyObjectByWhiteList(dto, DictDataEntity.class, HashSet.class, ArrayList.class);
    entity.setTenantCode(TenantUtils.getTenantCode());
    entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    entity.setParentDictCode(Optional.ofNullable(dto.getParentDictCode()).orElse(""));
    this.dictDataRepository.save(entity);

    // TODO 删除缓存

    // 更新物料时通知商品及商品的所有关联主数据信息做相应逻辑处理 
    if (CollectionUtils.isNotEmpty(dictDataEventListeners)) {
      DictDataVo vo = this.nebulaToolkitService.copyObjectByWhiteList(entity, DictDataVo.class, HashSet.class, ArrayList.class);
      dictDataEventListeners.forEach(event -> event.onCreate(vo));
    }

    //事件引擎通知
    DictDataNebulaEventDto dictDataNebulaEventDto = this.nebulaToolkitService.copyObjectByWhiteList(entity, DictDataNebulaEventDto.class, HashSet.class, ArrayList.class);
    SerializableBiConsumer<DictDataNebulaEventListener, DictDataNebulaEventDto> nebulaEvent = DictDataNebulaEventListener::onCreate;
    this.nebulaNetEventClient.publish(dictDataNebulaEventDto, DictDataNebulaEventListener.class, nebulaEvent);
  }

  @Override
  @Transactional
  public void update(DictDataDto dto) {
    Validate.notNull(dto, "字典对象不能为空");
    Validate.notBlank(dto.getId(), "ID不能为空");
    final DictDataEntity oldEntity = this.dictDataRepository.findByIdAndTenantCode(dto.getId(),TenantUtils.getTenantCode());
    Validate.notNull(oldEntity, "不存在或已删除");
    Validate.notBlank(dto.getDictCode(), "字典编码不能为空");
    Validate.notBlank(dto.getDictValue(), "字典值不能为空");
    Validate.notBlank(dto.getDictTypeCode(), "对应字典类型不能为空");
    Validate.isTrue(oldEntity.getDictCode().equals(dto.getDictCode()), "字典编码不能修改");
    Validate.isTrue(oldEntity.getDictTypeCode().equals(dto.getDictTypeCode()), "字典类型编码不能修改");

    DictDataEntity excludeEntity = this.dictDataRepository.findByDictTypeCodeAndDictCode(TenantUtils.getTenantCode(), dto.getDictTypeCode(), dto.getDictCode());
    Validate.notNull(excludeEntity, "字典编码不存在或已删除");
    Validate.isTrue(StringUtils.equals(excludeEntity.getId(), dto.getId()), "字典编码已存在");
    if (StringUtils.isNotBlank(dto.getParentDictCode())) {
      DictDataEntity parent = this.dictDataRepository.findByDictTypeCodeAndDictCode(TenantUtils.getTenantCode(), oldEntity.getDictTypeCode(), dto.getParentDictCode());
      Validate.notNull(parent, "上级字典不存在");
    }

    //校验扩展字段
    List<DictAttrConfEntity> confList = this.dictAttrConfRepository.findByDictAttrConfCode(TenantUtils.getTenantCode(), dto.getDictTypeCode());
    if (CollectionUtils.isNotEmpty(confList)) {
      for (DictAttrConfEntity conf : confList) {
        if (BooleanEnum.TRUE.getNumStr().equals(String.valueOf(conf.getRequired()))) {
          Object filedValueByName = this.getFiledValueByName(dto, conf.getExtField());
          if (filedValueByName == null || StringUtils.isEmpty(filedValueByName.toString())) {
            throw new IllegalArgumentException(conf.getFieldName() + "是必填项");
          }
        }
      }
    }
    DictDataEntity entity = this.nebulaToolkitService.copyObjectByWhiteList(dto, DictDataEntity.class, HashSet.class, ArrayList.class);
    if (StringUtil.isEmpty(entity.getTenantCode())){
      entity.setTenantCode(TenantUtils.getTenantCode());
    }
    entity.setParentDictCode(Optional.ofNullable(dto.getParentDictCode()).orElse(""));
    //重构修改方法
    this.dictDataRepository.updateByIdAndTenantCode(entity,TenantUtils.getTenantCode());

    // 更新物料时通知商品及商品的所有关联主数据信息做相应逻辑处理
    if (CollectionUtils.isNotEmpty(dictDataEventListeners)) {
      DictDataVo oldVo = this.nebulaToolkitService.copyObjectByWhiteList(oldEntity, DictDataVo.class, HashSet.class, ArrayList.class);
      DictDataVo newVo = this.nebulaToolkitService.copyObjectByWhiteList(entity, DictDataVo.class, HashSet.class, ArrayList.class);
      dictDataEventListeners.forEach(event -> event.onChange(oldVo, newVo));
    }

    //事件引擎通知
    DictDataNebulaEventDto oldDictDataNebulaEventDto = this.nebulaToolkitService.copyObjectByWhiteList(dto, DictDataNebulaEventDto.class, HashSet.class, ArrayList.class);
    DictDataNebulaEventDto newDictDataNebulaEventDto = this.nebulaToolkitService.copyObjectByWhiteList(entity, DictDataNebulaEventDto.class, HashSet.class, ArrayList.class);
    DictDataNebulaEventUpdateDto nebulaEventUpdateDto = new DictDataNebulaEventUpdateDto();
    nebulaEventUpdateDto.setOldDictDataNebulaEventDto(oldDictDataNebulaEventDto);
    nebulaEventUpdateDto.setNewDictDataNebulaEventDto(newDictDataNebulaEventDto);
    SerializableBiConsumer<DictDataNebulaEventListener, DictDataNebulaEventUpdateDto> nebulaEvent = DictDataNebulaEventListener::onUpdate;
    this.nebulaNetEventClient.publish(nebulaEventUpdateDto, DictDataNebulaEventListener.class, nebulaEvent);
  }

  @Override
  @Transactional
  public void deleteBatch(List<String> ids) {
    Validate.notEmpty(ids, "ID集合不能为空");
    List<DictDataEntity> entities = this.dictDataRepository.getBaseMapper().selectBatchIds(ids);
    Validate.isTrue(CollectionUtils.isNotEmpty(entities), "已删除或不存在");
    List<String> dictCodeList = entities.stream().map(DictDataEntity::getDictCode).collect(Collectors.toList());
    List<DictDataEntity> children = this.dictDataRepository.findByParentDictCodes(TenantUtils.getTenantCode(), dictCodeList);
    if (CollectionUtils.isNotEmpty(children)) {
      List<String> collect = children.stream().map(DictDataEntity::getId).filter(ids::contains).collect(Collectors.toList());
      Validate.isTrue(CollectionUtils.isEmpty(collect), "字典存在下级不能删除！");
    }
    // TODO 清理缓存
    this.dictDataRepository.updateDelFlagByIdIn(DelFlagStatusEnum.DELETE, ids);

    // 删除物料时通知商品及商品的所有关联主数据信息做相应逻辑处理 
    if (CollectionUtils.isNotEmpty(dictDataEventListeners)) {
      List<DictDataVo> voList = (List<DictDataVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entities, DictDataEntity.class, DictDataVo.class, HashSet.class, ArrayList.class);
      dictDataEventListeners.forEach(event -> event.onDelete(voList));
    }

    //事件引擎通知
    List<DictDataNebulaEventDto> nebulaEventDtoList = Lists.newArrayList(this.nebulaToolkitService.copyCollectionByWhiteList(entities, DictDataEntity.class, DictDataNebulaEventDto.class, HashSet.class, ArrayList.class));
    DictDataNebulaEventBatchDto nebulaEventBatchDto = new DictDataNebulaEventBatchDto();
    nebulaEventBatchDto.setDictDataNebulaEventDtoList(nebulaEventDtoList);
    SerializableBiConsumer<DictDataNebulaEventListener, DictDataNebulaEventBatchDto> nebulaEvent = DictDataNebulaEventListener::onDelete;
    this.nebulaNetEventClient.publish(nebulaEventBatchDto, DictDataNebulaEventListener.class, nebulaEvent);
  }

  @Override
  @Transactional
  public void enableBatch(List<String> ids) {
    Validate.notEmpty(ids, "ID集合不能为空");
    List<DictDataEntity> entities = this.dictDataRepository.getBaseMapper().selectBatchIds(ids);
    Validate.isTrue(CollectionUtils.isNotEmpty(entities), "已删除或不存在");
    // TODO 清理缓存
    this.dictDataRepository.updateEnableStatusByIdIn(EnableStatusEnum.ENABLE, ids);

    // 删除物料时通知商品及商品的所有关联主数据信息做相应逻辑处理 
    if (CollectionUtils.isNotEmpty(dictDataEventListeners)) {
      List<DictDataVo> voList = (List<DictDataVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entities, DictDataEntity.class, DictDataVo.class, HashSet.class, ArrayList.class);
      dictDataEventListeners.forEach(event -> event.onEnable(voList));
    }

    //事件引擎通知
    List<DictDataNebulaEventDto> nebulaEventDtoList = Lists.newArrayList(this.nebulaToolkitService.copyCollectionByWhiteList(entities, DictDataEntity.class, DictDataNebulaEventDto.class, HashSet.class, ArrayList.class));
    DictDataNebulaEventBatchDto nebulaEventBatchDto = new DictDataNebulaEventBatchDto();
    nebulaEventBatchDto.setDictDataNebulaEventDtoList(nebulaEventDtoList);
    SerializableBiConsumer<DictDataNebulaEventListener, DictDataNebulaEventBatchDto> nebulaEvent = DictDataNebulaEventListener::onEnable;
    this.nebulaNetEventClient.publish(nebulaEventBatchDto, DictDataNebulaEventListener.class, nebulaEvent);
  }

  @Override
  @Transactional
  public void disableBatch(List<String> ids) {
    Validate.notEmpty(ids, "ID集合不能为空");
    List<DictDataEntity> entities = this.dictDataRepository.getBaseMapper().selectBatchIds(ids);
    Validate.isTrue(CollectionUtils.isNotEmpty(entities), "已删除或不存在");
    // TODO 清理缓存
    this.dictDataRepository.updateEnableStatusByIdIn(EnableStatusEnum.DISABLE, ids);

    // 删除物料时通知商品及商品的所有关联主数据信息做相应逻辑处理 
    if (CollectionUtils.isNotEmpty(dictDataEventListeners)) {
      List<DictDataVo> voList = (List<DictDataVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entities, DictDataEntity.class, DictDataVo.class, HashSet.class, ArrayList.class);
      dictDataEventListeners.forEach(event -> event.onDisable(voList));
    }

    //事件引擎通知
    List<DictDataNebulaEventDto> nebulaEventDtoList = Lists.newArrayList(this.nebulaToolkitService.copyCollectionByWhiteList(entities, DictDataEntity.class, DictDataNebulaEventDto.class, HashSet.class, ArrayList.class));
    DictDataNebulaEventBatchDto nebulaEventBatchDto = new DictDataNebulaEventBatchDto();
    nebulaEventBatchDto.setDictDataNebulaEventDtoList(nebulaEventDtoList);
    SerializableBiConsumer<DictDataNebulaEventListener, DictDataNebulaEventBatchDto> nebulaEvent = DictDataNebulaEventListener::onDisable;
    this.nebulaNetEventClient.publish(nebulaEventBatchDto, DictDataNebulaEventListener.class, nebulaEvent);
  }

  @Override
  @Transactional
  public void deleteByDictTypeCode(String dictTypeCode) {
    Validate.notBlank(dictTypeCode, "字典类型编码不能为空");
    List<DictDataEntity> entities = this.dictDataRepository.findByDictTypeCode(TenantUtils.getTenantCode(), dictTypeCode);
    if (CollectionUtils.isEmpty(entities)) {
      return;
    }
    Set<String> ids = entities.stream().map(DictDataEntity::getId).collect(Collectors.toSet());
    this.dictDataRepository.updateDelFlagByIdIn(DelFlagStatusEnum.DELETE, ids);

    // 删除物料时通知商品及商品的所有关联主数据信息做相应逻辑处理
    if (CollectionUtils.isNotEmpty(dictDataEventListeners)) {
      List<DictDataVo> voList = (List<DictDataVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entities, DictDataEntity.class, DictDataVo.class, HashSet.class, ArrayList.class);
      dictDataEventListeners.forEach(event -> event.onDelete(voList));
    }

    //事件引擎通知
    List<DictDataNebulaEventDto> nebulaEventDtoList = Lists.newArrayList(this.nebulaToolkitService.copyCollectionByWhiteList(entities, DictDataEntity.class, DictDataNebulaEventDto.class, HashSet.class, ArrayList.class));
    DictDataNebulaEventBatchDto nebulaEventBatchDto = new DictDataNebulaEventBatchDto();
    nebulaEventBatchDto.setDictDataNebulaEventDtoList(nebulaEventDtoList);
    SerializableBiConsumer<DictDataNebulaEventListener, DictDataNebulaEventBatchDto> nebulaEvent = DictDataNebulaEventListener::onDelete;
    this.nebulaNetEventClient.publish(nebulaEventBatchDto, DictDataNebulaEventListener.class, nebulaEvent);
  }

  @Override
  @Transactional
  public void deleteExtFieldVal(String dictTypeCode, List<String> extList) {
    if (StringUtils.isBlank(dictTypeCode) || CollectionUtils.isEmpty(extList)) {
      return;
    }
    List<DictDataEntity> list = this.dictDataRepository.findByDictTypeCode(TenantUtils.getTenantCode(), dictTypeCode);
    if (CollectionUtils.isEmpty(list)) {
      return;
    }
    list.forEach(item -> extList.forEach(extField -> setFieldValue(item, extField, "")));
    //重构修改方法
    this.dictDataRepository.updateBatchByIdAndTenantCode(list,TenantUtils.getTenantCode());

    //事件引擎通知
    List<DictDataNebulaEventDto> nebulaEventDtoList = Lists.newArrayList(this.nebulaToolkitService.copyCollectionByWhiteList(list, DictDataEntity.class, DictDataNebulaEventDto.class, HashSet.class, ArrayList.class));
    DictDataNebulaEventBatchDto nebulaEventBatchDto = new DictDataNebulaEventBatchDto();
    nebulaEventBatchDto.setDictDataNebulaEventDtoList(nebulaEventDtoList);
    SerializableBiConsumer<DictDataNebulaEventListener, DictDataNebulaEventBatchDto> nebulaEvent = DictDataNebulaEventListener::onDelete;
    this.nebulaNetEventClient.publish(nebulaEventBatchDto, DictDataNebulaEventListener.class, nebulaEvent);
  }

  /**
   * 构建树形结构
   *
   * @param totalList
   * @return
   */
  private List<DictDataVo> generateTree(List<DictDataVo> totalList) {
    //构建树list
    List<DictDataVo> treeList = new ArrayList<>();
    //当前操作层级数据
    List<DictDataVo> curLevelList = new ArrayList<>();
    //未操作数据
    List<DictDataVo> restList = new ArrayList<>();
    //key:id
    Map<String, DictDataVo> totalMap = totalList.stream().collect(Collectors.toMap(DictDataVo::getDictCode, v -> v));

    //查找第一层
    for (DictDataVo item : totalList) {
      if (StringUtils.isBlank(item.getParentDictCode()) || !totalMap.containsKey(item.getParentDictCode())) {
        treeList.add(item);
        curLevelList.add(item);
      } else {
        restList.add(item);
      }
    }

    //构建数据，从第二层开始
    while (curLevelList.size() > 0 && restList.size() > 0) {
      List<DictDataVo> restTempList = new ArrayList<>();
      List<DictDataVo> curLevelTempList = new ArrayList<>();
      Map<String, String> curLevelMap = curLevelList.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictCode));
      Map<String, List<DictDataVo>> curLevelChildrenMap = new LinkedHashMap<>(16);

      for (DictDataVo item : restList) {
        if (curLevelMap.containsKey(item.getParentDictCode())) {
          curLevelTempList.add(item);

          List<DictDataVo> childrenList = new ArrayList<>();
          if (curLevelChildrenMap.containsKey(item.getParentDictCode())) {
            childrenList.addAll(curLevelChildrenMap.get(item.getParentDictCode()));
          }
          childrenList.add(item);
          curLevelChildrenMap.put(item.getParentDictCode(), childrenList);
        } else {
          restTempList.add(item);
        }
      }

      for (DictDataVo item : curLevelList) {
        if (curLevelChildrenMap.containsKey(item.getDictCode())) {
          item.setChildren(curLevelChildrenMap.get(item.getDictCode()));
        }
      }

      curLevelList.clear();
      curLevelList.addAll(curLevelTempList);
      restList.clear();
      restList.addAll(restTempList);
    }

    return treeList;
  }

  /**
   * 写入树的path以及open状态 当一个节点以及下级所有节点都不包含字典编码,将会从树中移除
   * @param dictTypeCode 构建树path的前缀
   * @param list 字典的下级
   * @param dictCode 匹配字典的编码
   * @return
   */
  private void setPathAndOpen(String dictTypeCode, List<DictDataVo> list, String dictCode) {
    if(CollectionUtils.isNotEmpty(list)) {
      List<DictDataVo> removes = new ArrayList<>();
      for (DictDataVo item : list) {
        item.setPath(dictTypeCode + DictConstant.PATH_SPLIT + item.getDictCode());
        this.setPathAndOpen(item.getPath(), item.getChildren(), dictCode);
        //取当前节点字典编码是否包含dictCode与是否下级字典编码是否包含dictCode
        boolean contains = item.getDictCode().contains(dictCode);
        boolean hasChildren = CollectionUtils.isNotEmpty(item.getChildren());
        //下级包含字典编码的字符串 需要展开
        if (contains || hasChildren) {
          item.setOpen(hasChildren);
        }else {
          //节点以及下级所有节点都不包含字典编码,将会从树中移除
          removes.add(item);
        }
      }
      list.removeAll(removes);
    }
  }

  private void setPath(String dictTypeCode, List<DictDataVo> list){
    if (CollectionUtils.isEmpty(list)) {
      return;
    }
    for (DictDataVo item : list) {
      item.setPath(dictTypeCode + DictConstant.PATH_SPLIT + item.getDictCode());
      this.setPath(item.getPath(), item.getChildren());
    }
  }
}
