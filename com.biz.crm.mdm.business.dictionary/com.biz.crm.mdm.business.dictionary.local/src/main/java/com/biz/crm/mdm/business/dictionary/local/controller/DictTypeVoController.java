package com.biz.crm.mdm.business.dictionary.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.dictionary.sdk.dto.DictTypeDto;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictTypeVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictTypeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 字典类型管理
 *
 * <AUTHOR>
 * @date 2021-09-27 13:55:17
 */
@Slf4j
@RestController
@RequestMapping("/v1/dictionary/dicttype")
@Api(tags = "数据字典：类型管理：DictTypeVo")
public class DictTypeVoController {

  @Autowired(required = false)
  private DictTypeVoService dictTypeVoService;

  @ApiOperation(value = "查询列表（分页）", httpMethod = "GET")
  @GetMapping("/findByConditions")
  public Result<Page<DictTypeVo>> findByConditions(@PageableDefault(50) Pageable pageable, DictTypeDto dto) {
    try {
      Page<DictTypeVo> result = this.dictTypeVoService.findByConditions(pageable, dto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据ID查询", httpMethod = "GET")
  @GetMapping("/findById")
  public Result<DictTypeVo> findById(@RequestParam(value = "id") String id) {
    try {
      return Result.ok(this.dictTypeVoService.findById(id));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据字典类型查询", httpMethod = "GET")
  @GetMapping("/findByDictTypeCode")
  public Result<DictTypeVo> findByDictTypeCode(@RequestParam(value = "dictTypeCode") String dictTypeCode) {
    try {
      return Result.ok(this.dictTypeVoService.findByDictTypeCode(dictTypeCode));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "新增", httpMethod = "POST")
  @PostMapping
  public Result<?> create(@RequestBody DictTypeDto dto) {
    try {
      this.dictTypeVoService.save(dto);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "更新", httpMethod = "PATCH")
  @PatchMapping
  public Result<?> update(@RequestBody DictTypeDto dto) {
    try {
      this.dictTypeVoService.update(dto);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "删除", httpMethod = "DELETE")
  @DeleteMapping
  public Result<?> delete(@RequestParam List<String> ids) {
    try {
      this.dictTypeVoService.deleteBatch(ids);
      return Result.ok("删除成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "启用", httpMethod = "PATCH")
  @PatchMapping("/enable")
  public Result<?> enable(@RequestBody List<String> ids) {
    try {
      this.dictTypeVoService.enableBatch(ids);
      return Result.ok("启用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "禁用", httpMethod = "PATCH")
  @PatchMapping("/disable")
  public Result<?> disable(@RequestBody List<String> ids) {
    try {
      this.dictTypeVoService.disableBatch(ids);
      return Result.ok("禁用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

}
