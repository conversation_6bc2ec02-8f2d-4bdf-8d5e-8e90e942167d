package com.biz.crm.mdm.business.dictionary.local.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.dictionary.sdk.dto.DictDataDto;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 字典明细管理
 *
 * <AUTHOR>
 * @date 2021-09-27 13:55:17
 */
@Slf4j
@RestController
@RequestMapping("/v1/dictionary/dictdata")
@Api(tags = "数据字典：明细管理：DictDataVo")
public class DictDataVoController {

  @Autowired(required = false)
  private DictDataVoService dictDataVoService;

  @ApiOperation(value = "（字典维护页面专用）树形列表", httpMethod = "GET")
  @GetMapping("/findTreeByDictTypeCode")
  public Result<Page<DictDataVo>> findTreeByDictTypeCode(@PageableDefault(50) Pageable pageable,
                                                         @RequestParam String dictTypeCode,
                                                         @RequestParam(required = false) String dictCode) {
    try {
      Page<DictDataVo> result = this.dictDataVoService.findTreeByDictTypeCode(pageable, dictTypeCode, dictCode);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据ID查询详情", httpMethod = "GET")
  @GetMapping("/findById")
  public Result<DictDataVo> findById(@RequestParam(value = "id") String id) {
    try {
      return Result.ok(this.dictDataVoService.findById(id));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据字典类型和字典编码查询详情", httpMethod = "GET")
  @GetMapping("/findByDictTypeCodeAndDictCode")
  public Result<DictDataVo> findByDictTypeCodeAndDictCode(@RequestParam(value = "dictTypeCode") String dictTypeCode,
                                                          @RequestParam(value = "dictCode") String dictCode) {
    try {
      return Result.ok(this.dictDataVoService.findByDictTypeCodeAndDictCode(dictTypeCode, dictCode));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "数据字典树", notes = "dictTypeCode必传，dictCode 可不传", httpMethod = "GET")
  @GetMapping("/findTreeByDictTypeCodeOrDictCode")
  public Result<List<DictDataVo>> findTreeByDictTypeCodeOrDictCode(@RequestParam(value = "dictTypeCode", required = true) String dictTypeCode,
                                                         @RequestParam(value = "dictCode", required = false) String dictCode) {
    try {
      return Result.ok(dictDataVoService.findTreeByDictTypeCode(dictTypeCode, dictCode));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "包含扩展字段的数据字典下拉", notes = "dictTypeCode必传，返回值里dictCode:编码；dictValue:值（模糊查询），其余为扩展字段", httpMethod = "GET")
  @GetMapping("/findContainExtendByConditions")
  public Result<List<JSONObject>> findContainExtendByConditions(@RequestParam(value = "dictTypeCode", required = true) String dictTypeCode,
                                                                @RequestParam(value = "parentDictCode", required = false) String parentDictCode,
                                                                @RequestParam(value = "dictValue", required = false) String dictValue) {
    try {
      DictDataDto dto = new DictDataDto();
      dto.setParentDictCode(parentDictCode);
      dto.setDictTypeCode(dictTypeCode);
      dto.setDictValue(dictValue);
      return Result.ok(dictDataVoService.findContainExtendByConditions(dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "批量获取数据字典下拉框", notes = "返回值没有扩展字段，传字典类型编码集合数组", httpMethod = "GET")
  @GetMapping("/findByDictTypeCodeList")
  public Result<Map<String, List<DictDataVo>>> findByDictTypeCodeList(@RequestParam(value = "dictTypeCodeList", required = false) List<String> dictTypeCodeList) {
    try {
      return Result.ok(dictDataVoService.findByDictTypeCodeList(dictTypeCodeList));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据dictTypeCode获取字段数据列表", notes = "返回值没有扩展字段，传字典类型编码", httpMethod = "GET")
  @GetMapping("/findByDictTypeCode")
  public Result<List<DictDataVo>> findByDictTypeCode(@RequestParam(value = "dictTypeCode") String dictTypeCode) {
    try {
      return Result.ok(dictDataVoService.findByDictTypeCode(dictTypeCode));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据dictTypeCode获取数据字典树", httpMethod = "GET")
  @GetMapping("/tree")
  public Result<List<DictDataVo>> tree(@RequestParam(value = "dictTypeCode", required = false) String dictTypeCode) {
    try {
      return Result.ok(dictDataVoService.tree(dictTypeCode));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据dictTypeCode获取数据字典MAP", httpMethod = "GET")
  @GetMapping("/findMapByDictTypeCode")
  public Result<Map<String, String>> findMapByDictTypeCode(@RequestParam(value = "dictTypeCode", required = false) String dictTypeCode) {
    try {
      return Result.ok(dictDataVoService.findMapByDictTypeCode(dictTypeCode));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据dictTypeCode集合获取数据字典MAP", httpMethod = "GET")
  @GetMapping("/findMapByDictTypeCodeList")
  public Result<Map<String, Map<String, String>>> findMapByDictTypeCodeList(@RequestParam(value = "dictTypeCodeList", required = false) List<String> dictTypeCodeList) {
    try {
      return Result.ok(dictDataVoService.findMapByDictTypeCodeList(dictTypeCodeList));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据dictTypeCode集合获取数据字典反转MAP", httpMethod = "GET")
  @GetMapping("/findReverseMapByDictTypeCode")
  public Result<Map<String, String>> findReverseMapByDictTypeCode(@RequestParam(value = "dictTypeCode", required = false) String dictTypeCode) {
    try {
      return Result.ok(dictDataVoService.findReverseMapByDictTypeCode(dictTypeCode));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据dictTypeCode集合获取数据字典反转MAP", httpMethod = "GET")
  @GetMapping("/findReverseMapByDictTypeCodeList")
  public Result<Map<String, Map<String, String>>> findReverseMapByDictTypeCodeList(@RequestParam(value = "dictTypeCodeList", required = false) List<String> dictTypeCodeList) {
    try {
      return Result.ok(dictDataVoService.findReverseMapByDictTypeCodeList(dictTypeCodeList));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


}
