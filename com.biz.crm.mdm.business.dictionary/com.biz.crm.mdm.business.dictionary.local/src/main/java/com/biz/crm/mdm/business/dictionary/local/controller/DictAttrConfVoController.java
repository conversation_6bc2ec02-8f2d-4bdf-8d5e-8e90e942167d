package com.biz.crm.mdm.business.dictionary.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.dictionary.sdk.dto.DictAttrConfDto;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictAttrConfVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictAttrConfVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据字典属性扩展管理
 *
 * <AUTHOR>
 * @date 2021-09-27 13:55:17
 */
@Slf4j
@RestController
@RequestMapping("/v1/dictionary/dictattrconf")
@Api(tags = "数据字典：属性扩展管理：DictAttrConfVo")
public class DictAttrConfVoController {

  @Autowired(required = false)
  private DictAttrConfVoService dictAttrConfVoService;

  @ApiOperation(value = "查询列表（分页）", httpMethod = "GET")
  @GetMapping("/findByDictTypeCode")
  public Result<List<DictAttrConfVo>> findByDictTypeCode(@RequestParam("dictTypeCode") String dictTypeCode) {
    try {
      List<DictAttrConfVo> result = this.dictAttrConfVoService.findByDictTypeCode(dictTypeCode);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据ID查询", httpMethod = "GET")
  @GetMapping("/findById")
  public Result<DictAttrConfVo> findById(@RequestParam(value = "id") String id) {
    try {
      return Result.ok(this.dictAttrConfVoService.findById(id));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "新增", httpMethod = "POST")
  @PostMapping
  public Result<?> create(@RequestBody DictAttrConfDto dto) {
    try {
      this.dictAttrConfVoService.save(dto);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "更新", httpMethod = "PATCH")
  @PatchMapping
  public Result<?> update(@RequestBody DictAttrConfDto dto) {
    try {
      this.dictAttrConfVoService.update(dto);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "删除", httpMethod = "DELETE")
  @DeleteMapping
  public Result<?> delete(@RequestParam List<String> ids) {
    try {
      this.dictAttrConfVoService.deleteBatch(ids);
      return Result.ok("删除成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

}
