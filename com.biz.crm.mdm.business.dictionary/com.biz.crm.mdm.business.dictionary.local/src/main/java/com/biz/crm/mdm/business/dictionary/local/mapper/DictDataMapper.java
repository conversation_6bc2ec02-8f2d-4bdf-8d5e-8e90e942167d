package com.biz.crm.mdm.business.dictionary.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.dictionary.local.entity.DictDataEntity;
import com.biz.crm.mdm.business.dictionary.sdk.dto.DictDataDto;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 数据字典明细表
 *
 * <AUTHOR>
 */
public interface DictDataMapper extends BaseMapper<DictDataEntity> {

    /**
     * 列表
     *
     * @param page
     * @param dto
     * @return
     */
    Page<DictDataVo> findByConditions(Page<DictDataVo> page, @Param("vo") DictDataDto dto);

    /**
     * 根据字典类型编码查询
     *
     * @param tenantCode    租户编码
     * @param dictTypeCodes 字典编码
     * @return
     */
    List<DictDataEntity> findByDictTypeCodes(@Param("tenantCode") String tenantCode, @Param("dictTypeCodes") Set<String> dictTypeCodes);
}
