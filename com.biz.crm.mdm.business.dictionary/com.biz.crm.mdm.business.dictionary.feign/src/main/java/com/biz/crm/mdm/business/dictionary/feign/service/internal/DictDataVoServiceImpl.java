package com.biz.crm.mdm.business.dictionary.feign.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.dictionary.feign.feign.DictDataVoFeign;
import com.biz.crm.mdm.business.dictionary.sdk.dto.DictDataDto;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 数据字典明细表接口实现类
 *
 * <AUTHOR>
 * @date 2022/2/18
 */
@Service
public class DictDataVoServiceImpl implements DictDataVoService {

  @Autowired(required = false)
  private DictDataVoFeign dictDataVoFeign;

  @Override
  public Page<DictDataVo> findTreeByDictTypeCode(Pageable pageable, String dictTypeCode, String dictCode) {
    if (StringUtil.isEmpty(dictTypeCode)) {
      return new Page<>();
    }
    return this.dictDataVoFeign.findTreeByDictTypeCode(pageable.getPageNumber(), pageable.getPageSize(),
            dictTypeCode, dictCode).checkFeignResult();
  }

  @Override
  public List<DictDataVo> findByDictTypeCode(String dictTypeCode) {
    if (StringUtil.isEmpty(dictTypeCode)) {
      return Lists.newArrayList();
    }
    return this.dictDataVoFeign.findByDictTypeCode(dictTypeCode).checkFeignResult();
  }

  @Override
  public List<DictDataVo> findTreeByDictTypeCode(String dictTypeCode) {
    if (StringUtil.isEmpty(dictTypeCode)) {
      return Lists.newArrayList();
    }
    return this.findTreeByDictTypeCode(dictTypeCode, null);
  }

  @Override
  public List<DictDataVo> findTreeByDictTypeCode(String dictTypeCode, String dictCode) {
    if (StringUtil.isEmpty(dictTypeCode)) {
      return Lists.newArrayList();
    }
    return this.dictDataVoFeign.findTreeByDictTypeCodeOrDictCode(dictTypeCode, dictCode).checkFeignResult();
  }

  @Override
  public DictDataVo findById(String id) {
    if (StringUtil.isEmpty(id)) {
      return null;
    }
    return this.dictDataVoFeign.findById(id).checkFeignResult();
  }

  @Override
  public DictDataVo findByDictTypeCodeAndDictCode(String dictTypeCode, String dictCode) {
    if (StringUtil.isEmpty(dictTypeCode)
            || StringUtil.isEmpty(dictCode)) {
      return null;
    }
    return this.dictDataVoFeign.findByDictTypeCodeAndDictCode(dictTypeCode, dictCode).checkFeignResult();
  }

  @Override
  public Map<String, List<DictDataVo>> findByDictTypeCodeList(List<String> dictTypeCodeList) {
    if (CollectionUtil.isEmpty(dictTypeCodeList)){
      return Maps.newHashMap();
    }
    return this.dictDataVoFeign.findByDictTypeCodeList(dictTypeCodeList).checkFeignResult();
  }

  @Override
  public List<JSONObject> findContainExtendByConditions(DictDataDto dto) {
    dto = Optional.ofNullable(dto).orElse(new DictDataDto());
    if (StringUtils.isBlank(dto.getDictTypeCode())) {
      return new ArrayList<>();
    }
    return this.dictDataVoFeign.findContainExtendByConditions(dto.getDictTypeCode(), dto.getParentDictCode(), dto.getDictValue())
            .checkFeignResult();
  }

  @Override
  public List<DictDataVo> tree(String dictTypeCode) {
    if (StringUtil.isEmpty(dictTypeCode)){
      return Lists.newArrayList();
    }
    return dictDataVoFeign.tree(dictTypeCode).checkFeignResult();
  }

  /**
   * 根据数据字典编码获取数据字典MAP
   *
   * @param dictTypeCode
   * @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, java.lang.String>>
   * @author: huxmld
   * @version: v1.0.0
   * @date: 2024/5/26 11:36
   */
  @Override
  public Map<String, String> findMapByDictTypeCode(String dictTypeCode) {
    if (StringUtil.isEmpty(dictTypeCode)){
      return Maps.newHashMap();
    }
    return dictDataVoFeign.findMapByDictTypeCode(dictTypeCode).checkFeignResult();

  }

  /**
   * 根据数据字典编码获取数据字典MAP
   *
   * @param dictTypeCodeList
   * @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, java.lang.String>>
   * @author: huxmld
   * @version: v1.0.0
   * @date: 2024/5/26 11:36
   */
  @Override
  public Map<String, Map<String, String>> findMapByDictTypeCodeList(List<String> dictTypeCodeList) {
    if (CollectionUtil.isEmpty(dictTypeCodeList)){
      return Maps.newHashMap();
    }
    return dictDataVoFeign.findMapByDictTypeCodeList(dictTypeCodeList).checkFeignResult();
  }


  /**
   * 根据数据字典编码获取数据字典反转MAP
   *
   * @param dictTypeCode
   * @return java.util.Map<java.lang.String, java.lang.String>
   * @author: huxmld
   * @version: v1.0.0
   * @date: 2024/5/26 11:31
   */
  @Override
  public Map<String, String> findReverseMapByDictTypeCode(String dictTypeCode) {
    if (StringUtil.isEmpty(dictTypeCode)){
      return Maps.newHashMap();
    }
    return dictDataVoFeign.findReverseMapByDictTypeCode(dictTypeCode).checkFeignResult();
  }

  /**
   * 根据数据字典编码获取数据字典反转MAP
   *
   * @param dictTypeCodeList
   * @return java.util.Map<java.lang.String, java.lang.String>
   * @author: huxmld
   * @version: v1.0.0
   * @date: 2024/5/26 11:31
   */
  @Override
  public Map<String, Map<String, String>>  findReverseMapByDictTypeCodeList(List<String> dictTypeCodeList) {
    if (CollectionUtil.isEmpty(dictTypeCodeList)){
      return Maps.newHashMap();
    }
    return dictDataVoFeign.findReverseMapByDictTypeCodeList(dictTypeCodeList).checkFeignResult();
  }
}
