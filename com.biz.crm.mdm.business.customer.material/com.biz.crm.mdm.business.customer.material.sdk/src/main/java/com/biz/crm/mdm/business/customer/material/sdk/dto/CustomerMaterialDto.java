package com.biz.crm.mdm.business.customer.material.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 客户物料请求vo
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CustomerMaterialDto", description = "客户物料请求DTO")
public class CustomerMaterialDto extends TenantFlagOpDto {

    /**
     * ID集合
     */
    @ApiModelProperty("ID集合")
    private List<String> ids;

    /**
     * 客户组织编码
     */
    @ApiModelProperty("客户组织编码")
    private String customerOrgCode;

    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    private String materialCode;

    /**
     * 客户组织名称
     */
    @ApiModelProperty("客户组织名称")
    private String customerOrgName;

    /**
     * 物料名称
     */
    @ApiModelProperty("物料名称")
    private String materialName;

    /**
     * 条形码
     */
    @ApiModelProperty("条形码")
    private String barCode;

}