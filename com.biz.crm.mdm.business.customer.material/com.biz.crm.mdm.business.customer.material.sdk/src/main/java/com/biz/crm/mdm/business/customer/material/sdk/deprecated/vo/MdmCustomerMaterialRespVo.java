package com.biz.crm.mdm.business.customer.material.sdk.deprecated.vo;

import com.biz.crm.business.common.sdk.deprecated.vo.CrmTreeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 客户物料返回vo(旧版弃用)
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "客户物料回显VO(旧版弃用)")
@Deprecated
public class MdmCustomerMaterialRespVo extends CrmTreeVo {
  private static final long serialVersionUID = -159752724386236121L;

  @ApiModelProperty("客户组织编码")
  private String customerOrgCode;

  @ApiModelProperty("客户组织名称")
  private String customerOrgName;

  @ApiModelProperty("物料编码")
  private String materialCode;

  @ApiModelProperty("物料名称")
  private String materialName;

  @ApiModelProperty("条形码")
  private String barCode;

}
