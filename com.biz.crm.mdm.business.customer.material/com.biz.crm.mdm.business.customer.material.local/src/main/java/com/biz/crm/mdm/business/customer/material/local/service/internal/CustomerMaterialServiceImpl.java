package com.biz.crm.mdm.business.customer.material.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.customer.material.local.entity.CustomerMaterial;
import com.biz.crm.mdm.business.customer.material.local.repository.CustomerMaterialRepository;
import com.biz.crm.mdm.business.customer.material.local.service.CustomerMaterialService;
import com.biz.crm.mdm.business.customer.material.sdk.dto.CustomerMaterialDto;
import com.biz.crm.mdm.business.customer.material.sdk.dto.CustomerMaterialEventDto;
import com.biz.crm.mdm.business.customer.material.sdk.event.CustomerMaterialEventListener;
import com.biz.crm.mdm.business.customer.material.sdk.vo.CustomerMaterialVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 客户物料service实现类
 *
 * <AUTHOR>
 */
@Service
public class CustomerMaterialServiceImpl implements CustomerMaterialService {

  @Autowired(required = false)
  private CustomerMaterialRepository customerMaterialRepository;

  @Autowired(required = false)
  private LoginUserService loginUserService;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @Autowired(required = false)
  private NebulaNetEventClient nebulaNetEventClient;

  @Override
  public Page<CustomerMaterial> findByConditions(Pageable pageable, CustomerMaterialDto dto) {
    pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
    dto = Optional.ofNullable(dto).orElse(new CustomerMaterialDto());
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    Page<CustomerMaterial> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return customerMaterialRepository.findByConditions(page, dto);
  }

  @Override
  public CustomerMaterial findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    return this.customerMaterialRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public CustomerMaterial create(CustomerMaterial customerMaterial) {
    //校验数据
    this.createValidate(customerMaterial);
    customerMaterial.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    customerMaterial.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    customerMaterial.setCreateTime(new Date());
    customerMaterial.setCreateAccount(loginUserService.getLoginAccountName());
    customerMaterial.setTenantCode(TenantUtils.getTenantCode());    //新增租户编号判断条件
    this.customerMaterialRepository.save(customerMaterial);

    // 业务日志创建
    CustomerMaterialEventDto customerMaterialEventDto = new CustomerMaterialEventDto();
    CustomerMaterialVo nowVo =
            this.nebulaToolkitService.copyObjectByWhiteList(
                    customerMaterial, CustomerMaterialVo.class, HashSet.class, ArrayList.class);
    customerMaterialEventDto.setNewest(nowVo);
    customerMaterialEventDto.setOriginal(null);
    // 发送通知
    SerializableBiConsumer<CustomerMaterialEventListener, CustomerMaterialEventDto> onCreate = CustomerMaterialEventListener::onCreate;
    this.nebulaNetEventClient.publish(customerMaterialEventDto, CustomerMaterialEventListener.class, onCreate);
    return customerMaterial;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public CustomerMaterial update(CustomerMaterial customerMaterial) {
    Validate.notNull(customerMaterial, "更新客户物料对象不能为空");
    Validate.notBlank(customerMaterial.getId(), "更新时数据主键不能为空！");
    //重构查询方法
    CustomerMaterial entity = this.customerMaterialRepository.findByIdAndTenantCode(customerMaterial.getId(),TenantUtils.getTenantCode());
    Validate.notNull(entity, "待更新的客户物料不存在！");
    this.createValidate(customerMaterial);
    //重构修改方法
    this.customerMaterialRepository.updateByIdAndTenantCode(customerMaterial,TenantUtils.getTenantCode());
    customerMaterial.setModifyTime(new Date());
    customerMaterial.setModifyAccount(loginUserService.getLoginAccountName());

    // 业务日志创建
    CustomerMaterialEventDto customerMaterialEventDto = new CustomerMaterialEventDto();
    CustomerMaterial newLogEntity = this.customerMaterialRepository.getById(customerMaterial.getId());
    CustomerMaterialVo nowVo =
            this.nebulaToolkitService.copyObjectByWhiteList(
                    newLogEntity, CustomerMaterialVo.class, HashSet.class, ArrayList.class);
    customerMaterialEventDto.setNewest(nowVo);

    //获取旧数据
    CustomerMaterialVo original =
            this.nebulaToolkitService.copyObjectByWhiteList(
                    entity, CustomerMaterialVo.class, HashSet.class, ArrayList.class);
    customerMaterialEventDto.setOriginal(original);
    // 发送通知
    SerializableBiConsumer<CustomerMaterialEventListener, CustomerMaterialEventDto> onUpdate = CustomerMaterialEventListener::onUpdate;
    this.nebulaNetEventClient.publish(customerMaterialEventDto, CustomerMaterialEventListener.class, onUpdate);
    return customerMaterial;
  }

  /**
   * 批量删除
   * 此删除方法非物理删除，而是将数据的删除标记置为true
   *
   * @param ids 客户物料主键编码
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void deleteBatch(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "待删除的数据主键不能为空");
    //先查询需要删除的所有数据
    List<CustomerMaterial> customerMaterials = this.customerMaterialRepository.findByIds(ids);
    this.customerMaterialRepository.updateDelStatusByIdIn(DelFlagStatusEnum.DELETE, ids);
    if (!CollectionUtils.isEmpty(customerMaterials)) {
      customerMaterials.forEach(customerMaterial -> {
        // 业务日志创建
        CustomerMaterialEventDto customerMaterialEventDto = new CustomerMaterialEventDto();
        CustomerMaterialVo original =
                this.nebulaToolkitService.copyObjectByWhiteList(
                        customerMaterial, CustomerMaterialVo.class, HashSet.class, ArrayList.class);
        customerMaterialEventDto.setNewest(null);
        customerMaterialEventDto.setOriginal(original);
        // 发送通知
        SerializableBiConsumer<CustomerMaterialEventListener, CustomerMaterialEventDto> onDelete = CustomerMaterialEventListener::onDelete;
        this.nebulaNetEventClient.publish(customerMaterialEventDto, CustomerMaterialEventListener.class, onDelete);
      });
    }
  }

  /**
   * 批量启用
   *
   * @param ids 客户物料主键编码
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void enableBatch(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "待修正的数据主键不能为空");
    List<CustomerMaterial> customerMaterials = this.customerMaterialRepository.findByIds(ids);
    this.customerMaterialRepository.updateEnableStatusByIdIn(EnableStatusEnum.ENABLE, ids);
    if (!CollectionUtils.isEmpty(customerMaterials)){
      customerMaterials.forEach(customerMaterial ->{
        //获取旧数据
        CustomerMaterialEventDto customerMaterialEventDto = new CustomerMaterialEventDto();
        CustomerMaterialVo original = new CustomerMaterialVo();
        original.setId(customerMaterial.getId());
        original.setEnableStatus(EnableStatusEnum.codeToEnum(customerMaterial.getEnableStatus()).getDes());
        CustomerMaterialVo nowVo = new CustomerMaterialVo();
        nowVo.setId(customerMaterial.getId());
        nowVo.setEnableStatus(EnableStatusEnum.ENABLE.getDes());
        customerMaterialEventDto.setNewest(nowVo);
        customerMaterialEventDto.setOriginal(original);
        // 发送通知
        SerializableBiConsumer<CustomerMaterialEventListener, CustomerMaterialEventDto> onEnable = CustomerMaterialEventListener::onEnable;
        this.nebulaNetEventClient.publish(customerMaterialEventDto, CustomerMaterialEventListener.class, onEnable);
      });
    }
  }

  /**
   * 批量禁用
   *
   * @param ids 客户物料主键编码
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void disableBatch(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "待修正的数据主键不能为空");
    List<CustomerMaterial> customerMaterials = this.customerMaterialRepository.findByIds(ids);
    this.customerMaterialRepository.updateEnableStatusByIdIn(EnableStatusEnum.DISABLE, ids);
    if (!CollectionUtils.isEmpty(customerMaterials)) {
      customerMaterials.forEach(customerMaterial -> {
        //获取旧数据
        CustomerMaterialEventDto customerMaterialEventDto = new CustomerMaterialEventDto();
        CustomerMaterialVo original = new CustomerMaterialVo();
        original.setId(customerMaterial.getId());
        original.setEnableStatus(EnableStatusEnum.codeToEnum(customerMaterial.getEnableStatus()).getDes());
        CustomerMaterialVo nowVo = new CustomerMaterialVo();
        nowVo.setId(customerMaterial.getId());
        nowVo.setEnableStatus(EnableStatusEnum.DISABLE.getDes());
        customerMaterialEventDto.setNewest(nowVo);
        customerMaterialEventDto.setOriginal(original);
        // 发送通知
        SerializableBiConsumer<CustomerMaterialEventListener, CustomerMaterialEventDto> onDisable = CustomerMaterialEventListener::onDisable;
        this.nebulaNetEventClient.publish(customerMaterialEventDto, CustomerMaterialEventListener.class, onDisable);
      });
    }
  }

  /**
   * 通过客户组织编码批量查询
   *
   * @param customerOrgCodes
   * @return
   */
  @Override
  public List<CustomerMaterial> findByCustomerOrgCodes(List<String> customerOrgCodes) {
    Validate.isTrue(!CollectionUtils.isEmpty(customerOrgCodes), "组织编码集合不能为空");
    return customerMaterialRepository.findByCustomerOrgCodes(customerOrgCodes,DelFlagStatusEnum.NORMAL.getCode());
  }

  /**
   * 客户物料新增校验
   *
   * @param customerMaterial 客户物料信息
   */
  public void createValidate(CustomerMaterial customerMaterial) {
    Validate.notNull(customerMaterial, "新增客户物料对象不能为空");
    Validate.notBlank(customerMaterial.getCustomerOrgCode(), "客户组织编码不能为空");
    Validate.notBlank(customerMaterial.getMaterialCode(), "物料编码不能为空");
    Validate.notBlank(customerMaterial.getBarCode(), "条形码不能为空");
    Validate.isTrue(customerMaterial.getMaterialCode().length() <= 64, "新增客户物料时物料编码长度不能超过64");
    Validate.isTrue(customerMaterial.getCustomerOrgCode().length() <= 64, "新增客户物料时客户组织编码长度不能超过64");
    Validate.isTrue(customerMaterial.getMaterialCode().length() <= 64, "新增客户物料时条形码长度不能超过64");
  }
}
