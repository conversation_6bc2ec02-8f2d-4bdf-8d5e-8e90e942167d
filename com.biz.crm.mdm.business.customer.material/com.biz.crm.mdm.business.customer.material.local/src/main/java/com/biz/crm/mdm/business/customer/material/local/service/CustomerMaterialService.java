package com.biz.crm.mdm.business.customer.material.local.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.customer.material.local.entity.CustomerMaterial;
import com.biz.crm.mdm.business.customer.material.sdk.dto.CustomerMaterialDto;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 客户物料接口
 *
 * <AUTHOR>
 */
public interface CustomerMaterialService {

  /**
   * 分页条件查询列表
   * @param pageable 分页参数
   * @param dto 客户物料查询参数
   * @return 客户物料实体
   */
  Page<CustomerMaterial> findByConditions(Pageable pageable, CustomerMaterialDto dto);

  /**
   * 根据主体id查询实体
   * @param id 主体id
   * @return 客户物料实体
   */
  CustomerMaterial findById(String id);

  /**
   * 创建客户物料
   * @param customerMaterial 客户物料实体
   */
  CustomerMaterial create(CustomerMaterial customerMaterial);

  /**
   * 更新客户物料
   *
   * @param customerMaterial
   */
  CustomerMaterial update(CustomerMaterial customerMaterial);

  /**
   * 批量删除
   *
   * @param ids 客户物料主键
   */
  void deleteBatch(List<String> ids);

  /**
   * 批量删除
   *
   * @param ids 客户物料主键
   */
  void enableBatch(List<String> ids);

  /**
   * 批量删除
   *
   * @param ids 客户物料主键
   */
  void disableBatch(List<String> ids);


  List<CustomerMaterial> findByCustomerOrgCodes(List<String> customerCodes);
}
