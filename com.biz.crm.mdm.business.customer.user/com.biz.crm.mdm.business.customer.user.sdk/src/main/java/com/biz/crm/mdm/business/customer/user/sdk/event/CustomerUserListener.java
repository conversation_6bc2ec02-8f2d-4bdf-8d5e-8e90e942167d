package com.biz.crm.mdm.business.customer.user.sdk.event;

import com.biz.crm.mdm.business.customer.user.sdk.dto.CustomerUserEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * 客户用户变更事件通知接口
 *
 * <AUTHOR>
 * @since 2021-10-20 16:37:36
 */
public interface CustomerUserListener {

  /**
   * 创建时触发
   *
   * @param vo
   */
  default void onCreate(CustomerUserEventDto vo) { }

  /**
   * 编辑时触发
   *
   * @param customerUserEventDto
   */
  default void onUpdate(CustomerUserEventDto customerUserEventDto) {}

  /**
   * 启用时触发
   *
   * @param customerUserEventDto
   */
  default void onEnable(CustomerUserEventDto customerUserEventDto) {}

  /**
   * 禁用时触发
   *
   * @param customerUserEventDto
   */
  default void onDisable(CustomerUserEventDto customerUserEventDto) {}

  /**
   * 删除时触发
   *
   * @param customerUserEventDto
   */
  default void onDelete(CustomerUserEventDto customerUserEventDto) {}

}
