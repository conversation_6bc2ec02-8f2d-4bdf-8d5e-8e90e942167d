package com.biz.crm.mdm.business.customer.user.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户用户关联客户信息vo
 *
 * <AUTHOR>
 * @date 2021/11/25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "客户用户关联客户信息vo")
public class CustomerUserRelateCustomerVo {

  /**
   * 用户账号登录信息
   */
  @ApiModelProperty("用户账号登录信息")
  private String userName;

  /**
   * 用户编码
   */
  @ApiModelProperty("用户编码")
  private String userCode;

  /**
   * 用户类型
   */
  @ApiModelProperty("用户类型")
  private String userType;

  /**
   * 人员姓名
   */
  @ApiModelProperty("人员姓名")
  private String fullName;

  /**
   * 客户编码
   */
  @ApiModelProperty("客户编码")
  private String customerCode;
}
