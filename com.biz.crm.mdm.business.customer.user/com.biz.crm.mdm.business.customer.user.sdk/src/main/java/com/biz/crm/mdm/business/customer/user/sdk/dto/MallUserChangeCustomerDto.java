package com.biz.crm.mdm.business.customer.user.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Project crm-ems
 * @PackageName com.biz.crm.ems.business.mall.user.sdk.dto
 * @ClassName MallUserChangeCustomerDto
 * <AUTHOR>
 * @Date 2022/3/16 上午10:17
 * @Description 切换当前客户dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MallUserChangeCustomerDto", description = "切换当前客户dto")
public class MallUserChangeCustomerDto extends TenantDto {
  /**
   * 用户账号登录信息
   */
  @ApiModelProperty("用户账号登录信息")
  private String userName;

  @ApiModelProperty("客户编码")
  private String customerCode;
}