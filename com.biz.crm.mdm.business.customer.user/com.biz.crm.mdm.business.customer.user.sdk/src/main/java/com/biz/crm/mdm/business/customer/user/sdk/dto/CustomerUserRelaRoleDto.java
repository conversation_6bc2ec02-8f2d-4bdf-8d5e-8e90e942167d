package com.biz.crm.mdm.business.customer.user.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 客户用户关联角色dto
 *
 * <AUTHOR>
 * @date 2021-11-01 09:31:09
 */
@Data
@ApiModel(value = "CustomerUserRRoleDto", description = "客户用户关联角色dto")
public class CustomerUserRelaRoleDto extends TenantDto {

  /** 客户用户编码 */
  @ApiModelProperty("客户用户编码")
  private String userCode;

  /** 角色编码 */
  @ApiModelProperty("角色编码")
  private String roleCode;
}
