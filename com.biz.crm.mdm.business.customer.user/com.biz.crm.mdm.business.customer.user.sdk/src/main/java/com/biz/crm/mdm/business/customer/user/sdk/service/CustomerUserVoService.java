package com.biz.crm.mdm.business.customer.user.sdk.service;

import com.biz.crm.mdm.business.customer.user.sdk.dto.CustomerUserDto;
import com.biz.crm.mdm.business.customer.user.sdk.dto.CustomerUserRelWeChatDto;
import com.biz.crm.mdm.business.customer.user.sdk.dto.CustomerUserRelaCustomerDto;
import com.biz.crm.mdm.business.customer.user.sdk.dto.CustomerUserResetPasswordDto;
import com.biz.crm.mdm.business.customer.user.sdk.vo.CustomerUserVo;

import com.google.common.collect.Lists;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Set;

/**
 * 客户用户Vo接口
 *
 * <AUTHOR>
 * @since 2021-10-20 16:37:36
 */
public interface CustomerUserVoService {

  /**
   * 根据id或编码获取集合
   *
   * @param ids
   * @param userCodes
   * @return
   */
  List<CustomerUserVo> findDetailsByIdsOrUserCodes(List<String> ids, List<String> userCodes);

  /**
   * 新增
   *
   * @param dto
   * @return
   */
  CustomerUserVo create(CustomerUserDto dto);

  /**
   * 编辑
   *
   * @param dto
   * @return
   */
  CustomerUserVo update(CustomerUserDto dto);

  /**
   * 强制修改密码
   *
   * @param dto
   */
  void updatePasswordByIds(CustomerUserResetPasswordDto dto);

  /**
   * 根据用户名查询
   * @param userName
   * @return  CustomerUserVo
   */
  CustomerUserVo findByUserName(String userName);

  /**
   * 根据用户名查询
   * @param userName
   * @return  CustomerUserVo
   */
  CustomerUserVo findByUserName(String userName,Boolean shareBenefits);

  /**
   * 根据用户名查询
   * @param phone
   * @return  CustomerUserVo
   */
  CustomerUserVo findByPhone(String phone);

  /**
   * 根据用户账号查询
   *
   * @param userNameSet
   */
  default List<CustomerUserVo> findByUserNames(Set<String> userNameSet) {
    return Lists.newLinkedList();
  }

  /**
   * 通过用户手机号获取客户用户信息(包含经客户信息)
   *
   * @param userPhone 用户手机号
   * @return
   */
  default CustomerUserVo findDetailsByUserPhone(String userPhone) {
    return null;
  }

  /**
   * 根据用户名查询
   * @param openid
   * @return  CustomerUserVo
   */
  CustomerUserVo findByOpenid(String openid);

  /***
   * 绑定经销商用户和openid的关系
   * @param customerUserRelWeChatDto
   */
  void bindWx(CustomerUserRelWeChatDto customerUserRelWeChatDto);

  /***
   * 标记当前用户
   * @param dto
   */
 void  flagCurrent(CustomerUserRelaCustomerDto dto);
}
