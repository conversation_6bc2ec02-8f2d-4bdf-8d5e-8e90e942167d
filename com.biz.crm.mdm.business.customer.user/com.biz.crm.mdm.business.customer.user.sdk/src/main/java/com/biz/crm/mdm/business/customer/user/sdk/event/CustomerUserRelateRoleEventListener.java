package com.biz.crm.mdm.business.customer.user.sdk.event;

import com.biz.crm.mdm.business.customer.user.sdk.vo.CustomerUserRelaRoleVo;
import java.util.List;

/**
 * 客户用户角色绑定关系变更事件通知接口
 *
 * <AUTHOR>
 * @date 2021/11/11
 */
public interface CustomerUserRelateRoleEventListener {

  /**
   * 重新绑定时触发
   *
   * @param oldRoleCode 旧角色编码
   * @param newRoleCode 新角色编码
   * @param customerCodeList 客户用户编码集合
   */
  default void onBind(String oldRoleCode, String newRoleCode, List<String> customerCodeList) {}

  /**
   * 解除绑定时触发
   *
   * @param list
   */
  default void onUnbind(List<CustomerUserRelaRoleVo> list) {}
}
