package com.biz.crm.mdm.business.customer.user.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户用户关联角色(CustomerUserRRole)实体类
 *
 * <AUTHOR>
 * @since 2021-11-01 09:30:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("mdm_customer_user_r_role")
@Table(
    name = "mdm_customer_user_r_role",
    indexes = {
      @Index(
          name = "mdm_customer_user_r_role_index",
          columnList = "tenant_code,user_code,role_code",
          unique = true),
    })
@ApiModel(value = "CustomerUserRRole", description = "客户用户关联角色")
@org.hibernate.annotations.Table(appliesTo = "mdm_customer_user_r_role", comment = "客户用户关联角色")
public class CustomerUserRelaRole extends TenantEntity {
  private static final long serialVersionUID = 7078244175345045425L;

  /** 客户用户编码 */
  @ApiModelProperty("客户用户编码")
  @TableField(value = "user_code")
  @Column(name = "user_code", length = 32, columnDefinition = "varchar(32) COMMENT '客户用户编码'")
  private String userCode;

  /** 角色编码 */
  @ApiModelProperty("角色编码")
  @TableField(value = "role_code")
  @Column(name = "role_code", length = 32, columnDefinition = "varchar(32) COMMENT '角色编码'")
  private String roleCode;
}
