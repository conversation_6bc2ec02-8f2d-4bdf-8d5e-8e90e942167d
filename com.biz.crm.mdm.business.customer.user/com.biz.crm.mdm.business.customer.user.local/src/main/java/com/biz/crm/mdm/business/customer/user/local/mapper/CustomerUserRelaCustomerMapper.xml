<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
  namespace="com.biz.crm.mdm.business.customer.user.local.mapper.CustomerUserRelaCustomerMapper">

  <!--查询关联客户-->
  <select id="findCustomerUserRelaVoByUserCodes"
          resultType="com.biz.crm.mdm.business.customer.user.sdk.vo.CustomerUserRelaCustomerVo">
    select u.user_code, u.customer_code,u.customer_name,u.current_flag,u.primary_flag
    from mdm_customer_user_r_customer u
    <where>
      and u.user_code in
      <foreach collection="userCodeList" open="(" close=")" separator="," item="userCode">
        #{userCode}
      </foreach>
      and u.tenant_code = #{tenantCode}
    </where>
  </select>
</mapper>
