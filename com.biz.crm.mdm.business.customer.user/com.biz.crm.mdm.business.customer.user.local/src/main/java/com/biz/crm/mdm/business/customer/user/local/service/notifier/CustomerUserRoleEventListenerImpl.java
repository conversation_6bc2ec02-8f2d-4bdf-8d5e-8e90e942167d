package com.biz.crm.mdm.business.customer.user.local.service.notifier;

import com.biz.crm.mdm.business.customer.user.local.entity.CustomerUserRelaRole;
import com.biz.crm.mdm.business.customer.user.local.service.CustomerUserRelaRoleService;
import com.bizunited.nebula.rbac.sdk.event.RoleEventListener;
import com.bizunited.nebula.rbac.sdk.vo.RoleVo;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 客户用户实现角色事件监听
 *
 * <AUTHOR>
 * @date 2021/10/29
 */
@Component
public class CustomerUserRoleEventListenerImpl implements RoleEventListener {

  @Autowired(required = false) private CustomerUserRelaRoleService customerUserRelaRoleService;

  @Override
  public void onCreated(RoleVo roleVo) {
    // 无实现
  }

  @Override
  public void onUpdate(RoleVo roleVo) {
    // 无实现
  }

  /**
   * 当角色发生删除事件时，根据触发事件的角色信息中的角色编码,验证角色是否存在与客户用户的关联关系。
   *
   * <p>1.存在与客户用户的关联关系,则抛出角色已关联客户用户的异常,阻断当前角色的删除操作 2.不存在与客户用户的关联关系,则什么也不做.
   */
  @Override
  public void onDeleted(RoleVo roleVo) {
    if (Objects.isNull(roleVo) || StringUtils.isBlank(roleVo.getRoleCode())) {
      return;
    }
    List<CustomerUserRelaRole> entities =
        this.customerUserRelaRoleService.findByRoleCodesAndTenantCode(
            Lists.newArrayList(roleVo.getRoleCode()), roleVo.getTenantCode());
    Validate.isTrue(
        CollectionUtils.isEmpty(entities), "角色" + roleVo.getRoleName() + "已经关联了客户用户，不能删除");
  }
}
