package com.biz.crm.mdm.business.customer.user.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerRelateOrgVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.customer.user.local.entity.CustomerUser;
import com.biz.crm.mdm.business.customer.user.local.entity.CustomerUserRelWeChat;
import com.biz.crm.mdm.business.customer.user.local.entity.CustomerUserRelaCustomer;
import com.biz.crm.mdm.business.customer.user.local.entity.CustomerUserRelaRole;
import com.biz.crm.mdm.business.customer.user.local.repository.CustomerUserRelWechatRepository;
import com.biz.crm.mdm.business.customer.user.local.repository.CustomerUserRelaCustomerRepository;
import com.biz.crm.mdm.business.customer.user.local.service.CustomerUserRelWeChatService;
import com.biz.crm.mdm.business.customer.user.local.service.CustomerUserRelaCustomerService;
import com.biz.crm.mdm.business.customer.user.local.service.CustomerUserRelaRoleService;
import com.biz.crm.mdm.business.customer.user.local.service.CustomerUserService;
import com.biz.crm.mdm.business.customer.user.sdk.dto.*;
import com.biz.crm.mdm.business.customer.user.sdk.event.CustomerUserEventListener;
import com.biz.crm.mdm.business.customer.user.sdk.event.CustomerUserListener;
import com.biz.crm.mdm.business.customer.user.sdk.service.CustomerUserVoService;
import com.biz.crm.mdm.business.customer.user.sdk.vo.CustomerUserRelaCustomerVo;
import com.biz.crm.mdm.business.customer.user.sdk.vo.CustomerUserRelaRoleVo;
import com.biz.crm.mdm.business.customer.user.sdk.vo.CustomerUserVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户用户(CustomerUser)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-20 16:35:05
 */
@Service
public class CustomerUserVoServiceImpl implements CustomerUserVoService {

    @Autowired(required = false)
    private CustomerUserService customerUserService;

    @Autowired(required = false)
    private CustomerUserRelaCustomerService customerUserRelaCustomerService;

    @Autowired(required = false)
    private CustomerUserRelaRoleService customerUserRelaRoleService;

    @Autowired(required = false)
    @Qualifier("nebulaToolkitService")
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private CustomerUserRelWechatRepository customerUserRelWechatRepository;

    @Autowired(required = false)
    @Lazy
    private List<CustomerUserEventListener> eventListeners;

    @Autowired(required = false)
    private CustomerUserRelaCustomerRepository customerUserRelaCustomerRepository;

    @Autowired(required = false)
    private CustomerVoService customerVoService;

    @Autowired(required = false)
    private CustomerUserRelWeChatService customerUserRelWeChatService;

    @Autowired
    @Lazy
    private List<CustomerUserListener> customerUserListeners;

    @Override
    public List<CustomerUserVo> findDetailsByIdsOrUserCodes(
            List<String> ids, List<String> userCodes) {
        List<CustomerUserVo> re = Lists.newLinkedList();
        if (CollectionUtils.isEmpty(ids) && CollectionUtils.isEmpty(userCodes)) {
            return re;
        }
        List<CustomerUser> customerUserList =
                customerUserService.findDetailsByIdsOrUserCodes(ids, userCodes);
        if (CollectionUtils.isEmpty(customerUserList)) {
            return re;
        }

        List<String> userCodeList =
                customerUserList.stream()
                        .filter(a -> StringUtils.isNotBlank(a.getUserCode()))
                        .map(CustomerUser::getUserCode)
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userCodeList)) {
            return re;
        }

        List<CustomerUserRelaCustomer> customerList =
                customerUserRelaCustomerService.findByUserCodes(userCodeList);

        List<CustomerUserRelaRole> roleList = customerUserRelaRoleService.findByUserCodes(userCodeList);

        re = this.buildCustomerUserVoList(customerUserList, customerList, roleList);
        return re;
    }


    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;

    @Override
    @Transactional
    public CustomerUserVo create(CustomerUserDto dto) {
        Validate.notNull(dto, "客户用户信息缺失");
        CustomerUser customerUser =
                this.nebulaToolkitService.copyObjectByWhiteList(
                        dto, CustomerUser.class, HashSet.class, ArrayList.class);
        dto.setLockState(
                Optional.ofNullable(dto.getLockState()).orElse(EnableStatusEnum.ENABLE.getCode()));
        //新增租户编号
        customerUser.setTenantCode(TenantUtils.getTenantCode());
        this.customerUserService.create(customerUser);
        dto.setUserCode(customerUser.getUserCode());
        this.bindExtInfo(dto);
        //创建日志
        CustomerUserVo re = this.buildByDtoAndCustomerUser(dto, customerUser);
        CustomerUserEventDto customerUserEventDto = new CustomerUserEventDto();
        customerUserEventDto.setNewest(re);
        customerUserEventDto.setOriginal(null);

        // 发送新增通知
//    if (CollectionUtils.isNotEmpty(customerUserListeners)) {
//      this.customerUserListeners.forEach(event -> event.onCreate(customerUserEventDto));
//    }
        // 发送通知
        SerializableBiConsumer<CustomerUserEventListener, CustomerUserEventDto> onCreate = CustomerUserEventListener::onCreate;
        this.nebulaNetEventClient.publish(customerUserEventDto, CustomerUserEventListener.class, onCreate);
        return re;
    }

    @Override
    @Transactional
    public CustomerUserVo update(CustomerUserDto dto) {
        Validate.notNull(dto, "客户用户信息缺失");
        dto.setLockState(
                Optional.ofNullable(dto.getLockState()).orElse(EnableStatusEnum.ENABLE.getCode()));
        Boolean flag = CollectionUtils.isNotEmpty(eventListeners);
        CustomerUserVo oldVo = null;
        if (Boolean.TRUE.equals(flag)) {
            List<CustomerUserVo> list =
                    this.findDetailsByIdsOrUserCodes(Lists.newArrayList(dto.getId()), null);
            if (CollectionUtils.isNotEmpty(list)) {
                oldVo = list.get(0);
            }
        }
        CustomerUser customerUser =
                this.nebulaToolkitService.copyObjectByWhiteList(
                        dto, CustomerUser.class, HashSet.class, ArrayList.class);
        this.customerUserService.update(customerUser);
        dto.setUserCode(customerUser.getUserCode());
        this.bindExtInfo(dto);
        CustomerUserVo re = this.buildByDtoAndCustomerUser(dto, customerUser);
        // 发送通知
        if (Boolean.FALSE.equals(flag)) {
            return re;
        }
        //创建更新日志
        CustomerUserEventDto customerUserEventDto = new CustomerUserEventDto();
        customerUserEventDto.setOriginal(oldVo);
        //拉取新的日志对象
        CustomerUserVo newLogVo = null;
        List<CustomerUserVo> list = this.findDetailsByIdsOrUserCodes(Lists.newArrayList(dto.getId()), null);
        Optional<CustomerUserVo> optional = list.stream().findFirst();
        if (optional.isPresent()) {
            newLogVo = optional.get();
        }
        customerUserEventDto.setNewest(newLogVo);

//    // 发送修改通知
//    if (CollectionUtils.isNotEmpty(customerUserListeners)) {
//      //封装vo对象dto
//      this.customerUserListeners.forEach(event -> event.onUpdate(customerUserEventDto));
//    }
        SerializableBiConsumer<CustomerUserEventListener, CustomerUserEventDto> onUpdate = CustomerUserEventListener::onUpdate;
        this.nebulaNetEventClient.publish(customerUserEventDto, CustomerUserEventListener.class, onUpdate);
        return re;
    }

    @Override
    public CustomerUserVo findDetailsByUserPhone(String userPhone) {
        if (StringUtils.isBlank(userPhone)) {
            return null;
        }
        List<CustomerUser> customerUsers =
                this.customerUserService.findByUserPhone(userPhone, TenantUtils.getTenantCode());
        if (CollectionUtils.isEmpty(customerUsers)) {
            return null;
        }
        CustomerUserVo customerUserVo =
                this.nebulaToolkitService.copyObjectByWhiteList(
                        customerUsers.get(0), CustomerUserVo.class, HashSet.class, ArrayList.class);
        ArrayList<String> userCodeList = Lists.newArrayList(customerUserVo.getUserCode());
        List<CustomerUserRelaCustomer> customerList =
                customerUserRelaCustomerService.findByUserCodes(userCodeList);
        if (CollectionUtils.isEmpty(customerList)) {
            return customerUserVo;
        }
        List<CustomerUserRelaCustomerVo> customerUserRelaCustomerVos =
                (List<CustomerUserRelaCustomerVo>)
                        this.nebulaToolkitService.copyCollectionByWhiteList(
                                customerList,
                                CustomerUserRelaCustomer.class,
                                CustomerUserRelaCustomerVo.class,
                                HashSet.class,
                                ArrayList.class);
        customerUserVo.setCustomerInfoList(customerUserRelaCustomerVos);
        return customerUserVo;
    }

    @Override
    public void updatePasswordByIds(CustomerUserResetPasswordDto dto) {
        this.customerUserService.resetPassword(dto);
    }

    /**
     * 保存客户用户关联信息
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void bindExtInfo(CustomerUserDto dto) {
        /*
         * 保存客户用户关联信息：
         * 1、保存关联客户信息
         * 2、保存关联劫色信息
         * */
        Validate.notNull(dto, "客户用户信息缺失");
        List<CustomerUserRelaCustomer> customerList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dto.getCustomerInfoList())) {
            customerList = (List<CustomerUserRelaCustomer>)
                    this.nebulaToolkitService.copyCollectionByWhiteList(dto.getCustomerInfoList(), CustomerUserRelaCustomerDto.class,
                            CustomerUserRelaCustomer.class, HashSet.class, ArrayList.class);
            List<String> customerCodeList = customerList.stream().filter(k -> StringUtil.isNotEmpty(k.getCustomerCode()))
                    .map(CustomerUserRelaCustomer::getCustomerCode).distinct().collect(Collectors.toList());
            List<CustomerVo> customerVoList = customerVoService.findByCustomerCodes(customerCodeList);
            Map<String, CustomerVo> customerVoMap = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(customerVoList)) {
                customerVoMap.putAll(customerVoList.stream()
                        .collect(Collectors.toMap(CustomerVo::getCustomerCode, t -> t, (a, b) -> b)));
            }
            customerList.forEach(a -> {
                a.setUserCode(dto.getUserCode());
                a.setCustomerName(customerVoMap.getOrDefault(a.getCustomerCode(), new CustomerVo()).getCustomerName());
            });
        }
        customerUserRelaCustomerService.saveBatch(customerList, dto.getUserCode());

        List<CustomerUserRelaRole> roleList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dto.getRoleInfoList())) {
            roleList = (List<CustomerUserRelaRole>) this.nebulaToolkitService.copyCollectionByWhiteList(dto.getRoleInfoList(),
                    CustomerUserRelaRoleDto.class, CustomerUserRelaRole.class, HashSet.class, ArrayList.class);
            roleList.forEach(a -> a.setUserCode(dto.getUserCode()));
            //新增租户编号
            roleList.forEach(a -> a.setTenantCode(TenantUtils.getTenantCode()));
        }
        customerUserRelaRoleService.saveBatch(roleList, dto.getUserCode());
    }

    /**
     * 构建返回vo
     *
     * @param customerUserList
     * @param customerList
     * @return
     */
    private List<CustomerUserVo> buildCustomerUserVoList(
            List<CustomerUser> customerUserList,
            List<CustomerUserRelaCustomer> customerList,
            List<CustomerUserRelaRole> roleList) {
        List<CustomerUserVo> re = Lists.newLinkedList();
        Map<String, List<CustomerUserRelaCustomerVo>> mapCustomer = Maps.newHashMap();
        Map<String, List<CustomerUserRelaRoleVo>> mapRole = Maps.newHashMap();
        if (CollectionUtils.isEmpty(customerUserList)) {
            return re;
        }
        re =
                (List<CustomerUserVo>)
                        this.nebulaToolkitService.copyCollectionByWhiteList(
                                customerUserList,
                                CustomerUser.class,
                                CustomerUserVo.class,
                                HashSet.class,
                                ArrayList.class);

        if (CollectionUtils.isNotEmpty(customerList)) {
            List<CustomerUserRelaCustomerVo> voList =
                    (List<CustomerUserRelaCustomerVo>)
                            this.nebulaToolkitService.copyCollectionByWhiteList(
                                    customerList,
                                    CustomerUserRelaCustomer.class,
                                    CustomerUserRelaCustomerVo.class,
                                    HashSet.class,
                                    ArrayList.class);
            mapCustomer =
                    voList.stream().collect(Collectors.groupingBy(CustomerUserRelaCustomerVo::getUserCode));
        }

        if (CollectionUtils.isNotEmpty(roleList)) {
            List<CustomerUserRelaRoleVo> voList =
                    (List<CustomerUserRelaRoleVo>)
                            this.nebulaToolkitService.copyCollectionByWhiteList(
                                    roleList,
                                    CustomerUserRelaRole.class,
                                    CustomerUserRelaRoleVo.class,
                                    HashSet.class,
                                    ArrayList.class);
            mapRole = voList.stream().collect(Collectors.groupingBy(CustomerUserRelaRoleVo::getUserCode));
        }

        for (CustomerUserVo item : re) {
            item.setCustomerInfoList(mapCustomer.get(item.getUserCode()));
            item.setRoleInfoList(mapRole.get(item.getUserCode()));
        }
        return re;
    }

    /**
     * 构建响应vo信息
     *
     * @param dto
     * @param customerUser
     * @return
     */
    private CustomerUserVo buildByDtoAndCustomerUser(CustomerUserDto dto, CustomerUser customerUser) {
        if (Objects.isNull(customerUser) || Objects.isNull(dto)) {
            return null;
        }
        CustomerUserVo vo =
                this.nebulaToolkitService.copyObjectByWhiteList(
                        customerUser, CustomerUserVo.class, HashSet.class, ArrayList.class);
        if (CollectionUtils.isNotEmpty(dto.getCustomerInfoList())) {
            vo.setCustomerInfoList(
                    (List<CustomerUserRelaCustomerVo>)
                            this.nebulaToolkitService.copyCollectionByWhiteList(
                                    dto.getCustomerInfoList(),
                                    CustomerUserRelaCustomerDto.class,
                                    CustomerUserRelaCustomerVo.class,
                                    HashSet.class,
                                    ArrayList.class));
        }
        return vo;
    }

    /**
     * 根据用户名查询
     *
     * @param userName
     * @return
     */
    @Override
    public CustomerUserVo findByUserName(String userName) {
        return findByUserName(userName, false);
    }

    /**
     * 根据用户名查询
     *
     * @param userName
     * @return
     */
    @Override
    public CustomerUserVo findByUserName(String userName, Boolean shareBenefits) {
        CustomerUser customerUser = this.customerUserService.findByUserName(userName);
        if (ObjectUtils.isEmpty(customerUser)) {
            return null;
        }
        List<String> userCodeList = Lists.newArrayList();
        userCodeList.add(customerUser.getUserCode());
        // 查询关联客户
        List<CustomerUserRelaCustomerVo> customerList =
                customerUserRelaCustomerRepository.findCustomerUserRelaVoByUserCodes(userCodeList);
        //查询关联客户信息
        List<String> customerCodes = customerList.stream().map(CustomerUserRelaCustomerVo::getCustomerCode).collect(Collectors.toList());
        List<CustomerVo> userVoList = customerVoService.findByCustomerCodes(customerCodes);
        // 查询关联角色
        List<CustomerUserRelaRole> roleList = customerUserRelaRoleService.findByUserCodes(userCodeList);

        CustomerUserVo customerUserVo = this.buildCustomerUserVo(customerUser, customerList, userVoList, roleList);
        if (shareBenefits && CollectionUtils.isNotEmpty(customerUserVo.getCustomerInfoList())) {
            customerUserVo.setCustomerInfoList(customerUserVo.getCustomerInfoList().stream()
                    .filter(o -> o.getShareBenefits() != null && o.getShareBenefits()).collect(Collectors.toList()));
        }
        return customerUserVo;
    }

    @Override
    public CustomerUserVo findByPhone(String phone) {
        CustomerUser customerUser = this.customerUserService.findByPhone(phone);
        if (customerUser == null) {
            return null;
        }
        List<String> userCodeList = Lists.newArrayList();
        userCodeList.add(customerUser.getUserCode());
        // 查询关联客户
        List<CustomerUserRelaCustomerVo> customerList =
                customerUserRelaCustomerRepository.findCustomerUserRelaVoByUserCodes(userCodeList);
        //查询关联客户信息
        List<String> customerCodes = customerList.stream().map(CustomerUserRelaCustomerVo::getCustomerCode).collect(Collectors.toList());
        List<CustomerVo> userVoList = customerVoService.findByCustomerCodes(customerCodes);
        // 查询关联角色
        List<CustomerUserRelaRole> roleList = customerUserRelaRoleService.findByUserCodes(userCodeList);

        CustomerUserVo customerUserVo = this.buildCustomerUserVo(customerUser, customerList, userVoList, roleList);
        return customerUserVo;
    }

    @Override
    public List<CustomerUserVo> findByUserNames(Set<String> userNameSet) {
        List<CustomerUser> list = this.customerUserService.findByUserNames(userNameSet);
        if (ObjectUtils.isEmpty(list)) {
            return Lists.newLinkedList();
        }
        List<CustomerUserVo> customerUserVos = (List<CustomerUserVo>)
                this.nebulaToolkitService.copyCollectionByBlankList(
                        list, CustomerUser.class, CustomerUserVo.class, HashSet.class, ArrayList.class);
        if (CollectionUtils.isNotEmpty(customerUserVos)) {
            List<String> collect = customerUserVos.stream().map(CustomerUserVo::getUserCode).collect(Collectors.toList());
            List<CustomerUserRelaCustomer> byUserCodes = customerUserRelaCustomerService.findByUserCodes(collect);
            if (CollectionUtils.isNotEmpty(byUserCodes)) {
                Map<String, List<CustomerUserRelaCustomer>> collect1 = byUserCodes.stream().collect(Collectors.groupingBy(CustomerUserRelaCustomer::getUserCode));
                for (CustomerUserVo customerUserVo : customerUserVos) {
                    List<CustomerUserRelaCustomer> customerUserRelaCustomers = collect1.get(customerUserVo.getUserCode());
                    if (CollectionUtils.isNotEmpty(customerUserRelaCustomers)) {
                        customerUserVo.setCustomerInfoList((List<CustomerUserRelaCustomerVo>)
                                this.nebulaToolkitService.copyCollectionByBlankList(
                                        customerUserRelaCustomers, CustomerUserRelaCustomer.class, CustomerUserRelaCustomerVo.class, HashSet.class, ArrayList.class));
                    }
                }
            }

        }

        return customerUserVos;
    }

    /**
     * 构建客户用户管理客户和角色信息
     *
     * @param customerUser
     * @param customerUserRelaCustomerVoList
     * @param customerVoList
     * @param roleList
     * @return {@link CustomerUserVo}
     */
    private CustomerUserVo buildCustomerUserVo(CustomerUser customerUser
            , List<CustomerUserRelaCustomerVo> customerUserRelaCustomerVoList, List<CustomerVo> customerVoList, List<CustomerUserRelaRole> roleList) {
        CustomerUserVo customerUserVo = this.nebulaToolkitService
                .copyObjectByWhiteList(customerUser, CustomerUserVo.class, HashSet.class, ArrayList.class);
        //组装客户企业组织和客户组织
        if (CollectionUtils.isNotEmpty(customerUserRelaCustomerVoList)) {
            Map<String, CustomerVo> customerVoMap = customerVoList.stream()
                    .collect(Collectors.toMap(CustomerVo::getCustomerCode, Function.identity()));
            customerUserRelaCustomerVoList.forEach(o -> {
                CustomerVo customerVo = customerVoMap.get(o.getCustomerCode());
                if (!ObjectUtils.isEmpty(customerVo)) {
                    if (!org.springframework.util.CollectionUtils.isEmpty(customerVo.getOrgList())) {
                        CustomerRelateOrgVo customerRelateOrgVo = customerVo.getOrgList().get(0);
                        o.setOrgCode(customerRelateOrgVo.getOrgCode());
                        o.setOrgName(customerRelateOrgVo.getOrgName());
                    }
                    o.setCustomerOrgCode(customerVo.getCustomerOrgCode());
                    o.setCustomerOrgName(customerVo.getCustomerOrgName());
                    o.setShareBenefits(customerVo.getShareBenefits());
                }
            });
            customerUserVo.setCustomerInfoList(customerUserRelaCustomerVoList);
        }
        //组装客户用户关联角色
        if (CollectionUtils.isNotEmpty(roleList)) {
            List<CustomerUserRelaRoleVo> voList =
                    (List<CustomerUserRelaRoleVo>)
                            this.nebulaToolkitService.copyCollectionByWhiteList(
                                    roleList,
                                    CustomerUserRelaRole.class,
                                    CustomerUserRelaRoleVo.class,
                                    HashSet.class,
                                    ArrayList.class);
            customerUserVo.setRoleInfoList(voList);
        }
        return customerUserVo;
    }

    @Override
    public CustomerUserVo findByOpenid(String openid) {
        if (StringUtils.isEmpty(openid)) {
            return null;
        }
        //1.根据openid查询username
        List<CustomerUserRelWeChat> list = customerUserRelWechatRepository.lambdaQuery().eq(CustomerUserRelWeChat::getOpenId, openid).list();
        if (CollectionUtils.isNotEmpty(list) && list.get(0) != null) {
            String userName = list.get(0).getUserName();
            return this.findByUserName(userName);
        }
        return null;
    }

    @Override
    public void bindWx(CustomerUserRelWeChatDto customerUserRelWeChatDto) {
        if (customerUserRelWeChatDto == null) {
            return;
        }
        CustomerUserRelWeChat customerUserRelWeChat = nebulaToolkitService.copyObjectByBlankList(customerUserRelWeChatDto, CustomerUserRelWeChat.class, Set.class, ArrayList.class);
        customerUserRelWeChatService.bindTerminalUserRelWechat(customerUserRelWeChat);


    }


    @Override
    public void flagCurrent(CustomerUserRelaCustomerDto dto) {
        Validate.notNull(dto, "传入对象不能为空");
        Validate.notEmpty(dto.getCustomerCode(), "传入经销商编码不能为空");
        Validate.notEmpty(dto.getUserCode(), "传入用户编码不能为空");
        //1.把所有currentFlag更新成否
        customerUserRelaCustomerRepository.lambdaUpdate().eq(CustomerUserRelaCustomer::getUserCode, dto.getUserCode())
                .set(CustomerUserRelaCustomer::getCurrentFlag, false).update();
        //2.把当前currnetFla更新成是
        customerUserRelaCustomerRepository.lambdaUpdate().eq(CustomerUserRelaCustomer::getCustomerCode, dto.getCustomerCode())
                .eq(CustomerUserRelaCustomer::getUserCode, dto.getUserCode()).set(CustomerUserRelaCustomer::getCurrentFlag, true)
                .update();
    }
}
