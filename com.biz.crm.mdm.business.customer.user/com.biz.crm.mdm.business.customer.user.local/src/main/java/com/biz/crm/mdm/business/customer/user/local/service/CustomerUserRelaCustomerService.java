package com.biz.crm.mdm.business.customer.user.local.service;

import com.biz.crm.mdm.business.customer.user.local.entity.CustomerUserRelaCustomer;
import java.util.List;

/**
 * 客户用户与客户关联客户表(TerminalUserRTerminal)表服务接口
 *
 * <AUTHOR>
 * @since 2021-10-20 16:36:39
 */
public interface CustomerUserRelaCustomerService {

  /**
   * 根据用户编码集合获取集合
   *
   * @param userCodeList
   * @return
   */
  List<CustomerUserRelaCustomer> findByUserCodes(List<String> userCodeList);

  /**
   * 批量保存关联客户信息
   *
   * @param list
   * @param userCode
   */
  void saveBatch(List<CustomerUserRelaCustomer> list, String userCode);

  /**
   * 根据客户编码获取绑定的客户用户数量
   *
   * @param customerCodeList
   * @return
   */
  Integer countByCustomerCodes(List<String> customerCodeList);
}
