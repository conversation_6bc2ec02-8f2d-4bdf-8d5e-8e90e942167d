package com.biz.crm.mdm.business.customer.user.local.service.internal;

import com.biz.crm.mdm.business.customer.user.local.entity.CustomerUser;
import com.biz.crm.mdm.business.customer.user.local.entity.CustomerUserRelaCustomer;
import com.biz.crm.mdm.business.customer.user.local.repository.CustomerUserRelaCustomerRepository;
import com.biz.crm.mdm.business.customer.user.local.repository.CustomerUserRepository;
import com.biz.crm.mdm.business.customer.user.local.service.CustomerUserRelaCustomerService;

import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 客户用户与客户关联客户表服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-20 16:36:39
 */
@Service("customerUserRelaCustomerService")
public class CustomerUserRelaCustomerServiceImpl implements CustomerUserRelaCustomerService {

  @Autowired(required = false) 
  private CustomerUserRelaCustomerRepository customerUserRelaCustomerRepository;

  @Autowired(required = false) 
  private CustomerUserRepository customerUserRepository;

  @Override
  public List<CustomerUserRelaCustomer> findByUserCodes(List<String> userCodeList) {
    if (CollectionUtils.isEmpty(userCodeList)) {
      return Lists.newLinkedList();
    }
    return customerUserRelaCustomerRepository.findByUserCodes(userCodeList);
  }

  @Override
  @Transactional
  public void saveBatch(List<CustomerUserRelaCustomer> list, String userCode) {
    Validate.notBlank(userCode, "客户用户编码信息不能为空");
    customerUserRelaCustomerRepository.deleteByUserCodes(Lists.newArrayList(userCode));
    if (CollectionUtils.isEmpty(list)) {
      return;
    }
    Optional<CustomerUserRelaCustomer> first =
        list.stream().filter(a -> StringUtils.isBlank(a.getUserCode())
                        || StringUtils.isBlank(a.getCustomerCode())
                        || !userCode.equals(a.getUserCode())).findFirst();
    Validate.isTrue(!first.isPresent(), "用户编码或客户编码不能为空，且必须属于同一个客户用户");
    Map<String, List<CustomerUserRelaCustomer>> map =
        list.stream().collect(Collectors.groupingBy(CustomerUserRelaCustomer::getCustomerCode));
    map.entrySet().forEach(a -> Validate.isTrue(a.getValue().size() <= 1, "存在重复的客户编码"));
    for (CustomerUserRelaCustomer item : list) {
      item.setId(null);
      item.setTenantCode(TenantUtils.getTenantCode());
    }
    customerUserRelaCustomerRepository.saveBatch(list);
  }

  @Override
  public Integer countByCustomerCodes(List<String> customerCodeList) {
    if (CollectionUtils.isEmpty(customerCodeList)) {
      return 0;
    }
    List<CustomerUserRelaCustomer> byCustomerCodes = customerUserRelaCustomerRepository.findByCustomerCodes(customerCodeList);
    if(CollectionUtils.isEmpty(byCustomerCodes)) {
      return 0;
    }
    List<String> userCodes = byCustomerCodes.stream().map(CustomerUserRelaCustomer::getUserCode).filter(item -> StringUtils.isNotBlank(item)).distinct().collect(Collectors.toList());
    if(CollectionUtils.isEmpty(userCodes)) {
      return 0;
    }
    // 验证这些用户编号在客户用户信息中都是存在的
    List<CustomerUser> customerUserList = this.customerUserRepository.findByCustomerCodes(userCodes, TenantUtils.getTenantCode());
    if (CollectionUtils.isEmpty(customerUserList)) {
      return 0;
    }
    return customerUserList.size();
  }
}
