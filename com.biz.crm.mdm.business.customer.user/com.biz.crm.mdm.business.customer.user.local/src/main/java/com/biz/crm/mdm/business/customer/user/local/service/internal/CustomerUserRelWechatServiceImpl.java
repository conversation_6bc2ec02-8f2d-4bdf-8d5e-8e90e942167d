package com.biz.crm.mdm.business.customer.user.local.service.internal;

import com.biz.crm.mdm.business.customer.user.local.entity.CustomerUserRelWeChat;
import com.biz.crm.mdm.business.customer.user.local.repository.CustomerUserRelWechatRepository;
import com.biz.crm.mdm.business.customer.user.local.service.CustomerUserRelWeChatService;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 经销商用户与openid关联信息service类
 *
 * <AUTHOR>
 * @since 2021-10-20 16:36:39
 */
@Service("customerUserRelWechatServiceImpl")
public class CustomerUserRelWechatServiceImpl implements CustomerUserRelWeChatService {

    @Resource
    private CustomerUserRelWechatRepository customerUserRelWechatRepository;
    @Override
    public void unbindWx(List<String> ids) {
        Validate.notEmpty(ids,"传入ids不能为空");
        customerUserRelWechatRepository.removeByIds(ids);
    }

    @Override
    public void bindTerminalUserRelWechat(CustomerUserRelWeChat relWeChat) {
        Validate.notNull(relWeChat,"传入对象不能为空");
        Validate.notBlank(relWeChat.getOpenId(),"传入openid不能为空");
        Validate.notBlank(relWeChat.getUserName(),"传入username不能为空");
        //1.先删当前openid与用户的绑定关系
        customerUserRelWechatRepository.lambdaUpdate()
                .eq(CustomerUserRelWeChat::getOpenId, relWeChat.getOpenId())
                .remove();
        //2.保存信息
        customerUserRelWechatRepository.saveOrUpdate(relWeChat);

    }
}
