package com.biz.crm.mdm.business.customer.user.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.customer.user.local.entity.CustomerUser;
import com.biz.crm.mdm.business.customer.user.sdk.dto.CustomerUserPaginationDto;
import com.biz.crm.mdm.business.customer.user.sdk.vo.CustomerUserRelateCustomerVo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 客户用户(CustomerUser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-10-20 16:35:03
 */
public interface CustomerUserMapper extends BaseMapper<CustomerUser> {

  /**
   * 分页列表
   *
   * @param page 分页信息
   * @param dto 分页参数dto
   * @return 分页列表
   */
  Page<CustomerUser> findByConditions(
      Page<CustomerUser> page, @Param("dto") CustomerUserPaginationDto dto);

  /**
   * 根据角色编码获取对应的客户用户集合信息
   *
   * @param roleCodeList
   * @param tenantCode
   * @param delFlag
   * @return
   */
  List<CustomerUser> findByRoleCodesAndTenantCode(
      @Param("list") List<String> roleCodeList,
      @Param("tenantCode") String tenantCode,
      @Param("delFlag") String delFlag);

  /**
   * 获取用户于客户关联关系主要新
   *
   * @param customerCodeList
   * @param tenantCode
   * @param delFlag
   * @return
   */
  List<CustomerUserRelateCustomerVo> findRelateCustomerByCustomerCodes(
      @Param("list") List<String> customerCodeList,
      @Param("tenantCode") String tenantCode,
      @Param("delFlag") String delFlag);
}
