package com.biz.crm.mdm.business.customer.user.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Transient;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 客户用户(mdm_customer_user)实体类
 *
 * <AUTHOR>
 * @since 2021-10-20 16:35:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("mdm_customer_user")
@Table(
    name = "mdm_customer_user",
    indexes = {
      @Index(
          name = "mdm_customer_user_index1",
          columnList = "tenant_code,user_name",
          unique = true),
      @Index(name = "mdm_customer_user_index2", columnList = "user_code"),
    })
@org.hibernate.annotations.Table(appliesTo = "mdm_customer_user", comment = "客户用户")
@ApiModel(value = "CustomerUser", description = "客户用户")
public class CustomerUser extends TenantFlagOpEntity {
  
  private static final long serialVersionUID = -1207053189817697083L;

  /** 用户账号登录信息 */
  @ApiModelProperty("用户账号登录信息")
  @TableField(value = "user_name")
  @Column(name = "user_name", length = 64, columnDefinition = "varchar(64) COMMENT '用户账号登录信息'")
  private String userName;

  /** 用户编码 */
  @ApiModelProperty("用户编码")
  @TableField(value = "user_code")
  @Column(name = "user_code", length = 64, columnDefinition = "varchar(64) COMMENT '用户编码'")
  private String userCode;

  /** 密码（经过加密的） */
  @ApiModelProperty("密码（经过加密的）")
  @TableField(value = "user_password")
  @Column(
      name = "user_password",
      length = 128,
      columnDefinition = "varchar(128) COMMENT '密码（经过加密的）'")
  private String userPassword;

  /** 用户类型 */
  @ApiModelProperty("用户类型")
  @TableField(value = "user_type")
  @Column(name = "user_type", length = 32, columnDefinition = "varchar(32) COMMENT '用户类型'")
  private String userType;

  /** 性别 */
  @ApiModelProperty("性别")
  @TableField(value = "gender")
  @Column(name = "gender", length = 10, columnDefinition = "varchar(10) COMMENT '性别'")
  private String gender;

  /** 电话 */
  @ApiModelProperty("电话")
  @TableField(value = "user_phone")
  @Column(name = "user_phone", length = 32, columnDefinition = "varchar(32) COMMENT '电话'")
  private String userPhone;

  /** 人员姓名 */
  @ApiModelProperty("人员姓名")
  @TableField(value = "full_name")
  @Column(name = "full_name", length = 64, columnDefinition = "varchar(64) COMMENT '人员姓名'")
  private String fullName;

  /** 生效时间 */
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @ApiModelProperty("生效时间")
  @TableField(value = "start_time")
  @Column(name = "start_time", columnDefinition = "datetime COMMENT '生效时间'")
  private Date startTime;

  /** 失效时间 */
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @ApiModelProperty("失效时间")
  @TableField(value = "end_time")
  @Column(name = "end_time", columnDefinition = "datetime COMMENT '失效时间'")
  private Date endTime;

  /** 邮箱 */
  @ApiModelProperty("邮箱")
  @TableField(value = "email")
  @Column(name = "email", length = 128, columnDefinition = "varchar(128) COMMENT '邮箱'")
  private String email;

  /** 锁定状态 003/009 */
  @ApiModelProperty("锁定状态 003锁定 009正常")
  @TableField(value = "lock_state")
  @Column(
      name = "lock_state",
      length = 6,
      columnDefinition = "varchar(6) COMMENT '锁定状态 003锁定 009正常'")
  private String lockState;

  /** 最后一次登录时间 */
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @ApiModelProperty("最后一次登录时间")
  @TableField(value = "last_login_time")
  @Column(name = "last_login_time", columnDefinition = "datetime COMMENT '最后一次登录时间'")
  private Date lastLoginTime;

  /** 用户头像url */
  @ApiModelProperty("用户头像url")
  @TableField(value = "user_head_url")
  @Column(name = "user_head_url", length = 500, columnDefinition = "varchar(500) COMMENT '用户头像url'")
  private String userHeadUrl;

  /** 是否需要强制修改密码 true/false */
  @ApiModelProperty("是否需要强制修改密码")
  @TableField(value = "force_change_password")
  @Column(name = "force_change_password", columnDefinition = "int COMMENT '是否需要强制修改密码'")
  private Boolean forceChangePassword;

  /** 工号 */
  @ApiModelProperty("工号")
  @TableField(value = "job_code")
  @Column(name = "job_code", length = 32, columnDefinition = "varchar(32) COMMENT '工号'")
  private String jobCode;

  /** 员工类型 */
  @ApiModelProperty("员工类型")
  @TableField(value = "employee_type")
  @Column(name = "employee_type", length = 32, columnDefinition = "varchar(32) COMMENT '员工类型'")
  private String employeeType;

  /** 员工状态 */
  @ApiModelProperty("员工状态")
  @TableField(value = "employee_status")
  @Column(name = "employee_status", length = 32, columnDefinition = "varchar(32) COMMENT '员工状态'")
  private String employeeStatus;

  /** 身份证号码 */
  @ApiModelProperty("身份证号码")
  @TableField(value = "identity_card_number")
  @Column(
      name = "identity_card_number",
      length = 32,
      columnDefinition = "varchar(32) COMMENT '身份证号码'")
  private String identityCardNumber;

  /** 身份证地址 */
  @ApiModelProperty("身份证地址")
  @TableField(value = "identity_card_address")
  @Column(
      name = "identity_card_address",
      length = 255,
      columnDefinition = "varchar(255) COMMENT '身份证地址'")
  private String identityCardAddress;

  /** 民族 */
  @ApiModelProperty("民族")
  @TableField(value = "nationality")
  @Column(name = "nationality", length = 32, columnDefinition = "varchar(32) COMMENT '民族'")
  private String nationality;

  /** 现住址 */
  @ApiModelProperty("现住址")
  @TableField(value = "current_address")
  @Column(name = "current_address", length = 255, columnDefinition = "varchar(255) COMMENT '现住址'")
  private String currentAddress;

  /** 政治面貌 */
  @ApiModelProperty("政治面貌")
  @TableField(value = "political_affiliation")
  @Column(
      name = "political_affiliation",
      length = 128,
      columnDefinition = "varchar(128) COMMENT '政治面貌'")
  private String politicalAffiliation;

  /** 每隔三个月修改密码更新时间 */
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @ApiModelProperty("每隔三个月修改密码更新时间")
  @TableField(value = "update_password_date")
  @Column(name = "update_password_date", columnDefinition = "datetime COMMENT '每隔三个月修改密码更新时间'")
  private Date updatePasswordDate;

  /** 关联客户信息 */
  @TableField(exist = false)
  @Transient
  private List<CustomerUserRelaCustomer> customerList;

  /** 关联角色信息 */
  @TableField(exist = false)
  @Transient
  private List<CustomerUserRelaRole> roleList;
}
