package com.biz.crm.mdm.business.product.level.local.repository;
/**
 * Created by <PERSON><PERSON> on 2021-10-09 10:14.
 */

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.mdm.business.product.level.local.entity.ProductItem;
import com.biz.crm.mdm.business.product.level.local.entity.ProductLevelSap;
import com.biz.crm.mdm.business.product.level.local.mapper.ProductItemMapper;
import com.biz.crm.mdm.business.product.level.sdk.dto.ProductItemDto;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductItemVo;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductLevelSapVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * @program: crm
 * @description: 产品层级的数据库访问类
 * @author: Bao Hongbin
 * @create: 2021-10-09 10:14
 **/
@Component
public class ProductItemRepository extends ServiceImpl<ProductItemMapper, ProductItem> {

    @Autowired(required = false)
    @Qualifier("nebulaToolkitService")
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 分页条件查询
     *
     * @param pageable
     * @param dto
     * @return
     */
    public Page<ProductItemVo> findByConditions(Pageable pageable, ProductItemDto dto) {
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
        dto = Optional.ofNullable(dto).orElse(new ProductItemDto());
        Page<ProductItemVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        if (StringUtils.isEmpty(dto.getTenantCode())) {
            dto.setTenantCode(TenantUtils.getTenantCode());
        }
        return this.baseMapper.findByConditions(page, dto);
    }

    /**
     * 通过id查询详情
     *
     * @param id
     * @return
     */
    public ProductItem findDetailsById(String id) {
        if (StringUtil.isEmpty(id)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(ProductItem::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(ProductItem::getTenantCode, TenantUtils.getTenantCode())
                .eq(ProductItem::getId, id).one();
    }

    /**
     * 通过code查询详情
     *
     * @param code
     * @return
     */
    public ProductItem findDetailsByCode(String code) {
        if (StringUtil.isEmpty(code)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(ProductItem::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(ProductItem::getTenantCode, TenantUtils.getTenantCode())
                .eq(ProductItem::getProductItemCode, code).one();
    }

    /**
     * 通过code集合查询详情
     *
     * @param codeSet
     * @return
     */
    public List<ProductItem> findDetailsByCodes(Set<String> codeSet) {
        if (CollectionUtil.isEmpty(codeSet)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(ProductItem::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(ProductItem::getTenantCode, TenantUtils.getTenantCode())
                .in(ProductItem::getProductItemCode, codeSet).list();
    }


    public List<ProductItem> findListByCode(String code) {
        if (StringUtil.isEmpty(code)) {
            return Lists.newArrayList();
        }

        return this.lambdaQuery()
                .eq(ProductItem::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(ProductItem::getTenantCode, TenantUtils.getTenantCode())
                .eq(ProductItem::getProductItemCode, code).list();
    }

    /**
     * 通过code查询list
     *
     * @param code
     * @return
     */
    public List<ProductItemVo> findVoListByCode(String code) {
        if (StringUtil.isEmpty(code)) {
            return Lists.newArrayList();
        }

        List<ProductItem> productItemList = this.lambdaQuery()
                .eq(ProductItem::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(ProductItem::getTenantCode, TenantUtils.getTenantCode())
                .eq(ProductItem::getProductItemCode, code).list();

        return (List<ProductItemVo>) this.nebulaToolkitService.copyCollectionByWhiteList(productItemList,
                ProductItem.class, ProductItemVo.class, HashSet.class, ArrayList.class);


    }

    /**
     * 通过id集合查询产品层级数据
     *
     * @param ids
     * @return
     */
    public List<ProductItem> findListByIds(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(ProductItem::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(ProductItem::getTenantCode, TenantUtils.getTenantCode())
                .in(ProductItem::getId, ids).list();
    }

    /**
     * 通过code集合查询产品层级数据
     *
     * @param codes
     * @return
     */
    public List<ProductItem> findListByCodes(List<String> codes) {
        if (CollectionUtil.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(ProductItem::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(ProductItem::getTenantCode, TenantUtils.getTenantCode())
                .in(ProductItem::getProductItemCode, codes).list();
    }


    /**
     * 通过名称模糊查询产品层级list
     *
     * @param enableStatus
     * @param name
     * @return
     */
    public List<ProductItem> findListLikeName(String enableStatus, String name) {
        if (StringUtil.isEmpty(name)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(ProductItem::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(ProductItem::getTenantCode, TenantUtils.getTenantCode())
                .eq(StringUtils.isNotEmpty(enableStatus), ProductItem::getEnableStatus, enableStatus)
                .like(StringUtils.isNotEmpty(name), ProductItem::getProductItemName, name)
                .list();
    }


    /**
     * 根据产品层级类型获取产品层级信息
     *
     * @param businessTypeList 业态
     * @return 产品层级信息
     */
    public List<ProductItem> findByBusinessType(List<String> businessTypeList) {
        if (CollectionUtil.isEmpty(businessTypeList)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(ProductItem::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(ProductItem::getTenantCode, TenantUtils.getTenantCode())
                .in(ProductItem::getBusinessType, businessTypeList)
                .list();
    }


    /**
     * 重构修改方法
     *
     * @param productLevelList
     * @param tenantCode
     */
    public void updateBatchByIdAndTenantCode(List<ProductItem> productLevelList, String tenantCode) {
        productLevelList.stream().forEach(productLevel -> {
            LambdaUpdateWrapper<ProductItem> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
            lambdaUpdateWrapper.eq(ProductItem::getTenantCode, tenantCode);
            lambdaUpdateWrapper.in(ProductItem::getId, productLevel.getId());
            this.baseMapper.update(productLevel, lambdaUpdateWrapper);
        });
    }

    public String findMaxCode(String codeHead) {
        if (StringUtil.isEmpty(codeHead)){
            return "";
        }
       return this.baseMapper.findMaxCode(codeHead);
    }

    /**
     * 根据名称查询
     *
     * @param name
     * @return
     */
    public List<ProductItem> findByName(String name) {
        if (StringUtil.isEmpty(name)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(ProductItem::getProductItemName, name)
                .eq(ProductItem::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(ProductItem::getTenantCode, TenantUtils.getTenantCode())
                .list();
    }


    public List<ProductItemVo> selectByLevelNum(Integer levelNum) {
    	return baseMapper.selectByLevelNum(levelNum);
    }

    public List<ProductItemVo> selectByProductItemType(String productItemType) {
    	return baseMapper.selectByProductItemType(productItemType);
    }
}
