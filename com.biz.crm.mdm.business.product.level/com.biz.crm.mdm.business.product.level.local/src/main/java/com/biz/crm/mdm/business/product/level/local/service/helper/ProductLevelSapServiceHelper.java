package com.biz.crm.mdm.business.product.level.local.service.helper;
/**
 * Created by <PERSON><PERSON> on 2021-10-12 15:32.
 */

import cn.hutool.core.util.StrUtil;
import com.biz.crm.business.common.base.constant.BusinessConstant;
import com.biz.crm.mdm.business.product.level.local.entity.ProductLevelSap;
import com.biz.crm.mdm.business.product.level.local.repository.ProductLevelSapRepository;
import com.biz.crm.mdm.business.product.level.sdk.dto.ProductLevelSapDto;
import com.biz.crm.mdm.business.product.level.sdk.event.ProductLevelSapEventListener;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductLevelSapVo;
import com.bizunited.nebula.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * @program: crm
 * @description: 产品层级服务助手
 * @author: Bao Hongbin
 * @create: 2021-10-12 15:32
 **/
@Component
@Slf4j
public class ProductLevelSapServiceHelper {
  @Autowired(required = false)
  @Lazy
  private List<ProductLevelSapEventListener> productLevelSapEventListeners;
  
  @Autowired(required = false)
  private ProductLevelSapRepository productLevelSapRepository;

  /**
   * 验证创建请求参数
   *
   * @param dto
   */
  public void createValidation(ProductLevelSapDto dto) {
    Validate.notNull(dto, "进行当前操作时，信息对象必须传入!");
    log.info("新增产品层级请求dto：{}", JsonUtils.obj2JsonString(dto));
    if (StringUtils.isNotEmpty(dto.getProductLevelCode())) {
      List<ProductLevelSap> productLevels =
          productLevelSapRepository.findListByCode(dto.getProductLevelCode());
      Validate.isTrue(CollectionUtils.isEmpty(productLevels),
          "编码[" + dto.getProductLevelCode() + "]已存在");
    }
    if (StringUtils.isNotEmpty(dto.getParentCode())) {
      ProductLevelSap parent = productLevelSapRepository.findDetailsByCode(dto.getParentCode());
      Validate.notNull(parent, "上级编码无效");
    }
    Validate.notBlank(dto.getProductLevelName(), "新增信息时，产品层级名称不能为空！");
    Validate.notNull(dto.getProductLevelType(), "新增信息时，产品层级类型不能为空！");
    Validate.isTrue(dto.getProductLevelName().length() < 64,
        "产品层级名称信息，在进行新增时填入值超过了限定长度(64)，请检查!");

    String availableStatus = dto.getAvailableStatus();
    // 默认Y
    if (StrUtil.isBlank(availableStatus)) {
      dto.setAvailableStatus(BusinessConstant.BOOLEAN_YES);
    } else {
      Validate.isTrue(StrUtil.equals(BusinessConstant.BOOLEAN_YES, availableStatus) ||
              StrUtil.equals(BusinessConstant.BOOLEAN_NO, availableStatus), "是否生效字段有误");
    }
  }

  /**
   * 推送产品层级创建事件
   *
   * @param productLevelVos
   */
  public void sendCreateEvent(List<ProductLevelSapVo> productLevelVos) {
    if (Objects.nonNull(this.productLevelSapEventListeners)) {
      for (ProductLevelSapEventListener listener : this.productLevelSapEventListeners) {
        listener.onBatchCreate(productLevelVos);
      }
    }
  }

  /**
   * 验证更新请求参数
   *
   * @param dto
   */
  public void updateValidation(ProductLevelSapDto dto) {
    Validate.notNull(dto, "进行当前操作时，信息对象必须传入!");
    log.info("编辑产品层级请求dto：{}", JsonUtils.obj2JsonString(dto));
    Validate.notBlank(dto.getId(), "修改信息时，产品层级ID不能为空！");
    ProductLevelSap productLevel = productLevelSapRepository.findDetailsById(dto.getId());
    Validate.notNull(productLevel, "无效的业务技术编号信息");
    Validate.isTrue(
        productLevel.getProductLevelCode().equals(dto.getProductLevelCode()),
        "产品层级编码不能修改");
    if (StringUtils.isNotEmpty(dto.getParentCode())) {
      ProductLevelSap parent = productLevelSapRepository.findDetailsByCode(dto.getParentCode());
      Validate.notNull(parent, "上级产品层级不存在");
      Validate.isTrue(!parent.getId().equals(productLevel.getId()), "上级产品层级不能是自己");
      Validate.isTrue(!parent.getRuleCode().startsWith(productLevel.getRuleCode()),
          "上级产品层级不能是当前层级的下级");
    }
    Validate.notBlank(dto.getProductLevelName(), "修改信息时，产品层级名称不能为空！");
    Validate.notNull(dto.getProductLevelType(), "修改信息时，产品层级类型不能为空！");
    Validate.isTrue(dto.getProductLevelName().length() < 64,
        "产品层级名称信息，在进行修改时填入值超过了限定长度(64)，请检查!");

    String availableStatus = dto.getAvailableStatus();
    Validate.isTrue(StrUtil.equals(BusinessConstant.BOOLEAN_YES, availableStatus) ||
            StrUtil.equals(BusinessConstant.BOOLEAN_NO, availableStatus), "是否生效字段有误");

  }

  /**
   * 推送产品层级创建事件
   *
   * @param productLevelVos
   */
  public void sendUpdateEvent(List<ProductLevelSapVo> productLevelVos) {
    if (Objects.nonNull(this.productLevelSapEventListeners)) {
      for (ProductLevelSapEventListener listener : this.productLevelSapEventListeners) {
        listener.onBatchUpdate(productLevelVos);
      }
    }
  }

  /**
   * 推送产品层级删除事件
   *
   * @param productLevelVos
   */
  public void sendDeleteEvent(List<ProductLevelSapVo> productLevelVos) {
    if (Objects.nonNull(this.productLevelSapEventListeners)) {
      for (ProductLevelSapEventListener listener : this.productLevelSapEventListeners) {
        listener.onBatchDelete(productLevelVos);
      }
    }
  }

  /**
   * 推送产品层级启用事件
   *
   * @param productLevelVos
   */
  public void sendEnableEvent(List<ProductLevelSapVo> productLevelVos) {
    if (Objects.nonNull(this.productLevelSapEventListeners)) {
      for (ProductLevelSapEventListener listener : this.productLevelSapEventListeners) {
        listener.onBatchEnable(productLevelVos);
      }
    }
  }


  /**
   * 推送产品层级禁用事件
   *
   * @param productLevelVos
   */
  public void sendDisableEvent(List<ProductLevelSapVo> productLevelVos) {
    if (Objects.nonNull(this.productLevelSapEventListeners)) {
      for (ProductLevelSapEventListener listener : this.productLevelSapEventListeners) {
        listener.onBatchDisable(productLevelVos);
      }
    }
  }
}
