package com.biz.crm.mdm.business.product.level.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.product.level.sdk.dto.ProductLevelDto;
import com.biz.crm.mdm.business.product.level.sdk.dto.ProductLevelPaginationDto;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductLevelVo;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

/**
 * 产品层级接口
 *
 * <AUTHOR> hongbin
 * @date 2021-09-27 14:13:56
 */
public interface ProductLevelVoService {

  /**
   * 分页条件查询
   *
   * @param pageable
   * @param productLevelPaginationDto
   * @return
   */
  Page<ProductLevelVo> findByConditions(Pageable pageable, ProductLevelPaginationDto productLevelPaginationDto);

  /**
   * 通过id查询详情
   *
   * @param id
   * @return
   */
  ProductLevelVo findDetailsById(String id);

  /**
   * 相关的创建过程，http接口。请注意该创建过程除了可以创建模型中的基本信息以外，还可以对模型中属于OneToMany关联的明细信息一同进行创建注意：
   * 基于模型的创建操作传入的JSON对象，其主键信息不能有值，服务端将会自动为其赋予相关值。另外，创建操作成功后，系统将返回该对象的基本信息（不包括任何关联信息）
   *
   * @param dto
   * @return
   */
  ProductLevelVo create(ProductLevelDto dto);

  /**
   * 相关的更新过程，http接口。请注意该更新过程只会更新在模型层被标记为了updateable的属性，
   * 包括一般属性、ManyToOne和OneToOne性质的关联属性，而ManyToMany、OneToMany的关联属性，
   * 虽然也会传入，但需要开发人员自行在Service层完善其更新过程注意：基于模型（的修改操作传入的SON对象，
   * 其主键信息必须有值，服务端将验证这个主键值是否已经存在。另外，创建操作成功后，系统将返回该对象的基本信息（不包括任何关联信息）
   *
   * @param dto
   * @return
   */
  ProductLevelVo update(ProductLevelDto dto);

  /**
   * 批量启用产品层级
   *
   * @param singletonList
   */
  void enableBatch(List<String> singletonList);

  /**
   * 批量禁用产品层级
   *
   * @param singletonList
   */
  void disableBatch(List<String> singletonList);

  /**
   * 批量删除产品层级
   *
   * @param ids
   */
  void deleteBatch(List<String> ids);

  /**
   * 重置所有产品层级ruleCode
   */
  void updateRuleCode();

  /**
   * 根据产品层级编码集合查询该产品层级及全部下级层级的编码
   *
   * @param productLevelCodeSet
   * @return
   */
  List<String> findCurAndChildrenCodesByCodes(Set<String> productLevelCodeSet);

  /**
   * 获取所有的产品层级
   *
   * @return
   */
  List<ProductLevelVo> findAll();

  /**
   * 产品层级分页查询
   * @param pageable
   * @param dto
   * @return
   */
  Page<ProductLevelVo> findProductLevelByConditions(Pageable pageable, ProductLevelPaginationDto dto);

  /**
   * 产品层级编码
   * @param productLevelCode
   * @return
   */
  List<ProductLevelVo> findByProductLevelCode(String productLevelCode);
}

