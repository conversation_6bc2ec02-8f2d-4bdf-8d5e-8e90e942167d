package com.biz.crm.mdm.business.product.level.local.repository;
/**
 * Created by <PERSON><PERSON> on 2021-10-09 10:14.
 */

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.dto.TreeDto;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.vo.LazyTreeVo;
import com.biz.crm.mdm.business.product.level.local.entity.ProductItem;
import com.biz.crm.mdm.business.product.level.local.entity.ProductLevel;
import com.biz.crm.mdm.business.product.level.local.mapper.ProductLevelMapper;
import com.biz.crm.mdm.business.product.level.sdk.dto.ProductLevelPaginationDto;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductItemVo;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductLevelVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.util.CollectionUtils;

/**
 * @program: crm
 * @description: 产品层级的数据库访问类
 * @author: Bao Hongbin
 * @create: 2021-10-09 10:14
 **/
@Component
public class ProductLevelRepository extends ServiceImpl<ProductLevelMapper, ProductLevel> {

  @Autowired(required = false)
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 分页条件查询
   *
   * @param pageable
   * @param dto
   * @return
   */
  public Page<ProductLevelVo> findByConditions(Pageable pageable, ProductLevelPaginationDto dto) {
    Page<ProductLevelVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    if (StringUtils.isEmpty(dto.getTenantCode())){
      dto.setTenantCode(TenantUtils.getTenantCode());
    }
    if (StringUtils.isEmpty(dto.getDelFlag())){
      dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    }
    return this.baseMapper.findByConditions(page, dto);
  }

  /**
   * 通过id查询详情
   *
   * @param id
   * @return
   */
  public ProductLevel findDetailsById(String id) {
    return this.lambdaQuery()
        .eq(ProductLevel::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(ProductLevel::getTenantCode, TenantUtils.getTenantCode())
        .eq(ProductLevel::getId, id).one();
  }

  /**
   * 通过code查询详情
   *
   * @param code
   * @return
   */
  public ProductLevel findDetailsByCode(String code) {
    return this.lambdaQuery()
        .eq(ProductLevel::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(ProductLevel::getTenantCode, TenantUtils.getTenantCode())
        .eq(ProductLevel::getProductLevelCode, code).one();
  }

  /**
   * 通过code集合查询详情
   *
   * @param codeSet
   * @return
   */
  public List<ProductLevel> findDetailsByCodes(Set<String> codeSet) {
    return this.lambdaQuery()
        .eq(ProductLevel::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(ProductLevel::getTenantCode, TenantUtils.getTenantCode())
        .in(ProductLevel::getProductLevelCode, codeSet).list();
  }

  /**
   * 通过code查询list
   *
   * @param code
   * @return
   */
  public List<ProductLevel> findListByCode(String code) {
    return this.lambdaQuery()
        .eq(ProductLevel::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(ProductLevel::getTenantCode, TenantUtils.getTenantCode())
        .eq(ProductLevel::getProductLevelCode, code).list();
  }

  /**
   * 根据父级Code查询子集
   *
   * @param parentCode
   * @return
   */
  public List<ProductLevel> findChildrenListByParentCode(String parentCode) {
    return this.lambdaQuery()
        .eq(ProductLevel::getTenantCode, TenantUtils.getTenantCode())
        .isNull(!StringUtils.isNotEmpty(parentCode), ProductLevel::getParentCode)
        .eq(StringUtils.isNotEmpty(parentCode), ProductLevel::getParentCode, parentCode).list();
  }

  /**
   * 通过id集合查询产品层级数据
   *
   * @param ids
   * @return
   */
  public List<ProductLevel> findListByIds(List<String> ids) {
    return this.lambdaQuery()
        .eq(ProductLevel::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(ProductLevel::getTenantCode, TenantUtils.getTenantCode())
        .in(ProductLevel::getId, ids).list();
  }

  /**
   * 通过RuleCode集合查询产品层级数据
   *
   * @param ruleCodes
   * @return
   */
  public List<ProductLevel> findListByRuleCodes(Set<String> ruleCodes) {
    return this.lambdaQuery()
        .eq(ProductLevel::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(ProductLevel::getTenantCode, TenantUtils.getTenantCode())
        .in(ProductLevel::getRuleCode, ruleCodes).list();
  }

  /**
   * 通过启用状态和RuleCodes模糊查询自身及子集
   *
   * @param ruleCodes
   * @param enableStatus
   * @return
   */
  public List<ProductLevel> findCurAndChildrenByRuleCodeList(List<String> ruleCodes,
      String enableStatus) {
    return this.baseMapper.findCurAndChildrenByRuleCodeList(ruleCodes, enableStatus,
        TenantUtils.getTenantCode(), DelFlagStatusEnum.NORMAL);
  }

  /**
   * 根据父级Codes查询所有子集
   *
   * @param parentCodes
   * @return
   */
  public List<ProductLevel> findChildrenListByParentCodes(List<String> parentCodes) {
    return this.lambdaQuery()
        .eq(ProductLevel::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(ProductLevel::getTenantCode, TenantUtils.getTenantCode())
        .in(ProductLevel::getParentCode, parentCodes).list();
  }

  /**
   * 查找parentCode不为空但找不到对应上级的数据,设置parentCode为null
   *
   * @return
   */
  public void updateOrphanParentCodeNull() {
    this.baseMapper.updateOrphanParentCodeNull(TenantUtils.getTenantCode(), DelFlagStatusEnum.NORMAL);
  }

  /**
   * 查询所有没有父级编码的子集
   *
   * @return
   */
  public List<ProductLevel> findListWithoutParentCode() {
    return this.lambdaQuery()
        .eq(ProductLevel::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(ProductLevel::getTenantCode, TenantUtils.getTenantCode())
        .isNull(ProductLevel::getParentCode)
        .list();
  }

  /**
   * 通过code集合查询产品层级数据
   *
   * @param codes
   * @return
   */
  public List<ProductLevel> findListByCodes(List<String> codes) {
    return this.lambdaQuery()
        .eq(ProductLevel::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(ProductLevel::getTenantCode, TenantUtils.getTenantCode())
        .in(ProductLevel::getProductLevelCode, codes).list();
  }

  /**
   * 根据父级Code查询所有子集
   *
   * @param ruleCode
   * @return
   */
  public List<ProductLevel> findCurAndChildrenByRuleCode(String ruleCode) {
    return this.lambdaQuery()
        .eq(ProductLevel::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(ProductLevel::getTenantCode, TenantUtils.getTenantCode())
        .likeRight(ProductLevel::getRuleCode, ruleCode)
        .list();
  }

  /**
   * 查询所有产品层级
   *
   * @return
   */
  public List<ProductLevel> findAll() {
    return this.lambdaQuery()
        .eq(ProductLevel::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(ProductLevel::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
        .eq(ProductLevel::getTenantCode, TenantUtils.getTenantCode())
        .list();
  }

  /**
   * 通过名称模糊查询产品层级list
   *
   * @param enableStatus
   * @param name
   * @return
   */
  public List<ProductLevel> findListLikeName(String enableStatus, String name) {
    return this.lambdaQuery()
        .eq(ProductLevel::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(ProductLevel::getTenantCode, TenantUtils.getTenantCode())
        .eq(StringUtils.isNotEmpty(enableStatus), ProductLevel::getEnableStatus, enableStatus)
        .like(StringUtils.isNotEmpty(name), ProductLevel::getProductLevelName, name)
        .list();
  }

  public List<ProductLevel> findListLikeName(TreeDto dto) {
    dto = Optional.ofNullable(dto).orElse(new TreeDto());
    dto.setTenantCode(TenantUtils.getTenantCode());
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    return this.baseMapper.findListLikeName(dto);

  }

  /**
   * 查询产品层级懒加载数据
   *
   * @param enableStatus    启用状态
   * @param topOnly         传true只查第一层
   * @param parentCode      只查询该编码下一级
   * @param codeList        只查询这些编码
   * @param ruleCodeList    只查询这些降维编码
   * @param excludeRuleCode 排除这个降维编码的下级
   * @param includeRuleCode 这个降维编码的下级
   * @return
   */
  public List<LazyTreeVo> findLazyTreeList(String enableStatus, Boolean topOnly, String parentCode,
      List<String> codeList, List<String> ruleCodeList, String excludeRuleCode, String includeRuleCode) {
    return this.baseMapper.findLazyTreeList(enableStatus, topOnly, parentCode, codeList,
        ruleCodeList, excludeRuleCode, includeRuleCode, TenantUtils.getTenantCode(), DelFlagStatusEnum.NORMAL);
  }

  /**
   * 手动设置父级编码为空
   *
   * @param id
   */
  public void setParentCodeNull(String id) {
//    this.lambdaUpdate().set(ProductLevel::getParentCode, null)
//        .eq(ProductLevel::getId, id)
//        .update();
    this.baseMapper.setParentCodeNull(id, TenantUtils.getTenantCode());
  }

  /**
   * 手动设置父级编码，规则编码为空
   *
   * @param id
   */
  public void setParentCodeAndRuleCodeNull(String id) {
    this.lambdaUpdate()
        .set(ProductLevel::getParentCode, null)
        .set(ProductLevel::getRuleCode, "")
        .eq(ProductLevel::getId, id)
        .eq(ProductLevel::getTenantCode,TenantUtils.getTenantCode())   //新增租户编号
        .update();
  }

  /**
   * 根据产品层级类型获取产品层级信息
   *
   * @param productLevelType 产品层级类型
   * @param tenantCode 租户编码
   * @return 产品层级信息
   */
  public List<ProductLevel> findByProductLevelType(String productLevelType, String tenantCode) {
    return this.lambdaQuery()
        .eq(ProductLevel::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(ProductLevel::getTenantCode, tenantCode)
        .eq(ProductLevel::getProductLevelType, productLevelType)
        .list();
  }

  /**
   * 根据编码规则获取当前及子级的产品层级编码集合
   *
   * @param ruleCodeSet
   * @param tenantCode
   * @return
   */
  public List<String> findCodeByCurAndChildrenByRuleCodes(Set<String> ruleCodeSet, String tenantCode) {
    if(CollectionUtils.isEmpty(ruleCodeSet)) {
      return Lists.newLinkedList();
    }
    return this.baseMapper.findCodeByCurAndChildrenByRuleCodes(ruleCodeSet, tenantCode, DelFlagStatusEnum.NORMAL.getCode());
  }

  /**
   * 重构修改方法
   * @param productLevelList
   * @param tenantCode
   */
  public void updateBatchByIdAndTenantCode(List<ProductLevel> productLevelList, String tenantCode) {
    productLevelList.stream().forEach(productLevel -> {
      LambdaUpdateWrapper<ProductLevel> lambdaUpdateWrapper= Wrappers.lambdaUpdate();
      lambdaUpdateWrapper.eq(ProductLevel::getTenantCode,tenantCode);
      lambdaUpdateWrapper.in(ProductLevel::getId,productLevel.getId());
      this.baseMapper.update(productLevel,lambdaUpdateWrapper);
    });
  }

  /**
   * 重构修改方法
   * @param productLevel
   * @param tenantCode
   */
  public void updateByIdAndTenantCode(ProductLevel productLevel, String tenantCode) {
    LambdaUpdateWrapper<ProductLevel> lambdaUpdateWrapper= Wrappers.lambdaUpdate();
    lambdaUpdateWrapper.eq(ProductLevel::getTenantCode,tenantCode);
    lambdaUpdateWrapper.in(ProductLevel::getId,productLevel.getId());
    this.baseMapper.update(productLevel,lambdaUpdateWrapper);
  }

  /**
   * 根据产品层级编码获取ruleCode规则编码
   *
   * @param productLevelCode
   * @return ruleCode
   */
  public String findRuleCodeByProductLevelCode(String productLevelCode){
   ProductLevel productLevel= this.lambdaQuery()
          .eq(ProductLevel::getProductLevelCode,productLevelCode)
          .eq(ProductLevel::getTenantCode,TenantUtils.getTenantCode())
          .select(ProductLevel::getRuleCode)
          .one();
   if (ObjectUtils.isNull(productLevel)){
     return null;
   }
   return productLevel.getRuleCode();
  }

  /**
   * 分页查询产品层级
   *
   * @param pageable
   * @param dto
   * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.biz.crm.mdm.business.product.level.sdk.vo.ProductLevelVo>
   * @author: huxmld
   * @version: v1.0.0
   * @date: 2024/5/22 11:11
   */
  public Page<ProductLevelVo> findProductLevelByConditions(Pageable pageable, ProductLevelPaginationDto dto) {
    pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
    Page<ProductLevelVo> page = new Page<>(pageable.getPageNumber(), Math.min(pageable.getPageSize(), 2000));
    dto = Optional.ofNullable(dto).orElse(new ProductLevelPaginationDto());
    if (StringUtils.isEmpty(dto.getTenantCode())) {
      dto.setTenantCode(TenantUtils.getTenantCode());
    }
    if (StringUtils.isEmpty(dto.getDelFlag())) {
      dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    }
    return this.baseMapper.findProductLevelByConditions(page, dto);
  }

  public List<ProductLevelVo> findByProductLevelCode(String productLevelCode) {
    List<ProductLevel> productLevelList = this.lambdaQuery()
            .eq(ProductLevel::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(ProductLevel::getTenantCode, TenantUtils.getTenantCode())
            .eq(ProductLevel::getProductLevelCode, productLevelCode).list();
    return (List<ProductLevelVo>) this.nebulaToolkitService.copyCollectionByWhiteList(productLevelList,
            ProductLevel.class, ProductLevelVo.class, HashSet.class, ArrayList.class);

  }
}
