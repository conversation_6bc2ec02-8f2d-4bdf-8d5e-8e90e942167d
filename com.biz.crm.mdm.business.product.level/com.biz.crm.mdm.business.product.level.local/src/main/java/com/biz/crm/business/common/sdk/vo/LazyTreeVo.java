package com.biz.crm.business.common.sdk.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 懒加载树返回VO
 * <AUTHOR>
 */
@Getter
@Setter
public class LazyTreeVo {

  @ApiModelProperty("id")
  private String id;

  @ApiModelProperty("编码")
  private String code;

  @ApiModelProperty("名称")
  private String name;

  @ApiModelProperty("启用状态")
  private String enableStatus;

  @ApiModelProperty("上级code")
  private String parentCode;

  @ApiModelProperty("是否有下级")
  private Boolean hasChild;

  @ApiModelProperty("是否有下级，1有 0无")
  private Integer hasChildFlag;

  @ApiModelProperty("规则编码")
  private String ruleCode;

  @ApiModelProperty("下级")
  private List<LazyTreeVo> children;

}
