package com.biz.crm.mdm.business.product.level.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.product.level.local.entity.ProductItem;
import com.biz.crm.mdm.business.product.level.sdk.dto.ProductItemDto;
import com.biz.crm.mdm.business.product.level.sdk.dto.ProductLevelSapDto;
import com.biz.crm.mdm.business.product.level.sdk.dto.ProductLevelSapPaginationDto;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductItemVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 产品分类接口
 *
 * <AUTHOR> hongbin
 * @date 2021-09-27 14:13:56
 */
public interface ProductItemVoService {

    /**
     * 分页条件查询
     *
     * @param pageable
     * @param dto
     * @return
     */
    Page<ProductItemVo> findByConditions(Pageable pageable, ProductItemDto dto);

    /**
     * 通过id或编码查询详情
     *
     * @param id
     * @param code
     * @return
     */
    ProductItemVo findDetailsByIdOrCode(String id, String code);

    /**
     * 新增
     *
     * @param dto
     * @return
     */
    ProductItemVo create(ProductItemDto dto);

    /**
     * 导入新增
     * @param dto
     * @return
     */
    ProductItemVo importCreate(ProductItemDto dto);

    /**
     * 编辑
     *
     * @param dto
     * @return
     */
    ProductItemVo update(ProductItemDto dto);

    /**
     * 批量启用产品分类
     *
     * @param singletonList
     */
    void enableBatch(List<String> singletonList);

    /**
     * 批量禁用产品分类
     *
     * @param singletonList
     */
    void disableBatch(List<String> singletonList);

    /**
     * 批量删除产品分类
     *
     * @param ids
     */
    void deleteBatch(List<String> ids);

    /**
     * 根据层级查产品分类
     * @param levelNum
     * @return
     */
    List<ProductItemVo> selectByLevelNum(Integer levelNum);

    /**
     * 根据类型查询产品分类
     * @param productItemType
     * @return
     */
    List<ProductItemVo> selectByProductItemType(String productItemType);

    /**
     * 根据分类编码查询
     * @param productItemCode
     * @return
     */
    List<ProductItemVo> selectByProductItemCode(String productItemCode);

}

