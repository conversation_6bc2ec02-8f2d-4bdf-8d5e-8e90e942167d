package com.biz.crm.mdm.business.product.level.local.mapper;
/** Created by <PERSON><PERSON> on 2021-10-09 10:15. */
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.dto.TreeDto;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.vo.LazyTreeVo;
import com.biz.crm.mdm.business.product.level.local.entity.ProductLevel;
import com.biz.crm.mdm.business.product.level.sdk.dto.ProductLevelPaginationDto;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductLevelVo;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @program: crm
 * @description: 产品层级的mybatis-plus接口类
 * @author: <PERSON><PERSON>
 * @create: 2021-10-09 10:15
 */
public interface ProductLevelMapper extends BaseMapper<ProductLevel> {
  /**
   * 分页条件查询
   *
   * @param page
   * @param productLevelPaginationDto
   * @return
   */
  Page<ProductLevelVo> findByConditions(Page<ProductLevelVo> page,@Param("dto") ProductLevelPaginationDto productLevelPaginationDto);

  /**
   * 通过启用状态和RuleCodes模糊查询自身及子集
   *
   * @param ruleCodes
   * @param enableStatus
   * @param tenantCode
   * @return
   */
  List<ProductLevel> findCurAndChildrenByRuleCodeList(
      @Param("ruleCodes") List<String> ruleCodes,
      @Param("enableStatus") String enableStatus,
      @Param("tenantCode") String tenantCode,
      @Param("delFlag") DelFlagStatusEnum delFlag);

  /**
   * 查找parentCode不为空但找不到对应上级的数据,设置parentCode为null
   *
   * @param tenantCode
   * @return
   */
  void updateOrphanParentCodeNull(
      @Param("tenantCode") String tenantCode, @Param("delFlag") DelFlagStatusEnum delFlag);

  /**
   * 查询产品层级懒加载数据
   *
   * @param enableStatus 启用状态
   * @param topOnly 传true只查第一层
   * @param parentCode 只查询该编码下一级
   * @param codeList 只查询这些编码
   * @param ruleCodeList 只查询这些降维编码
   * @param excludeRuleCode 排除这个降维编码的下级
   * @param tenantCode
   * @return
   */
  List<LazyTreeVo> findLazyTreeList(
      @Param("enableStatus") String enableStatus,
      @Param("topOnly") Boolean topOnly,
      @Param("parentCode") String parentCode,
      @Param("codeList") List<String> codeList,
      @Param("ruleCodeList") List<String> ruleCodeList,
      @Param("excludeRuleCode") String excludeRuleCode,
      @Param("includeRuleCode") String includeRuleCode,
      @Param("tenantCode") String tenantCode,
      @Param("delFlag") DelFlagStatusEnum delFlag);

  /**
   * 根据编码规则获取当前及子级的产品层级编码集合
   *
   * @param ruleCodeSet
   * @param tenantCode
   * @param delFlag
   * @return
   */
  List<String> findCodeByCurAndChildrenByRuleCodes(
      @Param("list") Set<String> ruleCodeSet,
      @Param("tenantCode") String tenantCode,
      @Param("delFlag") String delFlag);

  void setParentCodeNull(@Param("id") String id, @Param("tenantCode") String tenantCode);

  /**
   * 查询
   * @param dto
   */
  List<ProductLevel> findListLikeName(  @Param("dto")TreeDto dto);

  /**
   * 分页查询产品层级
   *
   * @param page
   * @param dto
   * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.biz.crm.mdm.business.product.level.sdk.vo.ProductLevelVo>
   * @author: huxmld
   * @version: v1.0.0
   * @date: 2024/5/22 11:11
   */
  Page<ProductLevelVo> findProductLevelByConditions(Page<ProductLevelVo> page, @Param("dto") ProductLevelPaginationDto dto);
}
