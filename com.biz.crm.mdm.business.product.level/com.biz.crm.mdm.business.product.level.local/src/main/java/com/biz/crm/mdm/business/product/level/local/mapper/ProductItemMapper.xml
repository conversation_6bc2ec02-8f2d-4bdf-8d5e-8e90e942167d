<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.product.level.local.mapper.ProductItemMapper">

    <select id="findByConditions" resultType="com.biz.crm.mdm.business.product.level.sdk.vo.ProductItemVo">
        select mpi.*
        from mdm_product_item mpi
        where mpi.tenant_code = #{dto.tenantCode}
        AND mpi.del_flag = '${@<EMAIL>()}'
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and mpi.enable_status = #{dto.enableStatus}
        </if>
        <if test="dto.businessType != null and dto.businessType != ''">
            and mpi.business_type = #{dto.businessType}
        </if>
        <if test="dto.productItemType != null and dto.productItemType != ''">
            and mpi.product_item_type = #{dto.productItemType}
        </if>
        <if test="dto.productItemCode != null and dto.productItemCode != ''">
            <bind name="productItemCode" value="'%' + dto.productItemCode + '%'"/>
            and mpi.product_item_code like #{productItemCode}
        </if>
        <if test="dto.productItemName != null and dto.productItemName != ''">
            <bind name="productItemName" value="'%' + dto.productItemName + '%'"/>
            and mpi.product_item_name like #{productItemName}
        </if>
        <if test="dto.unionName != null and dto.unionName != ''">
            <bind name="unionName" value="'%' + dto.unionName + '%'"/>
            and ( mpi.product_item_code like #{unionName} or mpi.product_item_name like #{unionName} )
        </if>
        order by mpi.modify_time desc, mpi.id desc
    </select>
    <select id="findMaxCode" resultType="java.lang.String">
        select max(mpi.product_item_code)
        from mdm_product_item mpi
        where mpi.product_item_code like concat(#{codeHead}, '%')
    </select>
    <select id="selectByLevelNum" resultType="com.biz.crm.mdm.business.product.level.sdk.vo.ProductItemVo">
        select mpi.*
        from mdm_product_item mpi
        where mpi.level_num = #{levelNum}
    </select>

    <select id="selectByProductItemType" resultType="com.biz.crm.mdm.business.product.level.sdk.vo.ProductItemVo">
        select mpi.*
        from mdm_product_item mpi
        where mpi.product_item_type = #{productItemType}
    </select>
</mapper>
