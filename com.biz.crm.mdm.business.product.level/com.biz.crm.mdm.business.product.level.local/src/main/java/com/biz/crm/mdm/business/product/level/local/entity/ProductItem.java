package com.biz.crm.mdm.business.product.level.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * 产品分类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ProductItem", description = "产品分类")
@TableName("mdm_product_item")
@Entity
@Table(name = "mdm_product_item", indexes = {
        @Index(name = "mdm_product_item_up1", columnList = "product_item_code", unique = true),
        @Index(name = "mdm_product_item_index1", columnList = "business_type"),
        @Index(name = "mdm_product_item_index2", columnList = "modify_time,id"),
        @Index(name = "mdm_product_item_index3", columnList = "product_item_type"),
})
@org.hibernate.annotations.Table(appliesTo = "mdm_product_item", comment = "产品分类")
public class ProductItem extends TenantFlagOpEntity {

    private static final long serialVersionUID = -9040759794836081668L;

    @ApiModelProperty("分类编码")
    @Column(name = "product_item_code", columnDefinition = "VARCHAR(32) COMMENT '分类编码'")
    private String productItemCode;

    @ApiModelProperty("分类名称")
    @Column(name = "product_item_name", columnDefinition = "VARCHAR(128) COMMENT '分类名称'")
    private String productItemName;

    @ApiModelProperty("分类类型")
    @Column(name = "product_item_type", columnDefinition = "VARCHAR(32) COMMENT '分类类型'")
    private String productItemType;

    @ApiModelProperty("层级")
    @Column(name = "level_num", columnDefinition = "int(2) COMMENT '层级'")
    private Integer levelNum;

    @ApiModelProperty("业态")
    @Column(name = "business_type", columnDefinition = "VARCHAR(32) COMMENT '业态'")
    private String businessType;

}
