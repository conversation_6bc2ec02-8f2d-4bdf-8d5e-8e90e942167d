package com.biz.crm.mdm.business.product.level.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.product.level.local.service.ProductLevelVoService;
import com.biz.crm.mdm.business.product.level.sdk.dto.ProductLevelDto;
import com.biz.crm.mdm.business.product.level.sdk.dto.ProductLevelPaginationDto;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductLevelVo;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 迁移原/baseTreeController/productLevelTree接口
 * 产品层级控制器
 *
 * <AUTHOR> hongbin
 * @date 2021-09-27 14:13:56
 */

@Api(tags = "产品层级：ProductLevelVo：与产品层级相关的内容")
@Slf4j
@RestController
@RequestMapping("/v1/productLevel/productLevel")
public class ProductLevelVoController {

  @Autowired(required = false)
  private ProductLevelVoService productLevelVoService;

  /**
   * 多条件分页查询
   *
   * @return
   */
  @ApiOperation(value = "多条件分页查询(可适用于列表分页查询和公用分页弹框)", notes = "分页参数为page和size，page从0开始，size默认50;")
  @GetMapping("findByConditions")
  public Result<Page<ProductLevelVo>> findByConditions(@PageableDefault(50) Pageable pageable,
                                                       @ApiParam(name = "productLevelPaginationDto", value = "分页Dto") ProductLevelPaginationDto productLevelPaginationDto) {
    try {
      Page<ProductLevelVo> result = this.productLevelVoService.findByConditions(pageable, productLevelPaginationDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 按照ProductLevel实体中的（id）主键进行查询明细查询，查询的明细包括当前业务表单所有的关联属性。
   *
   * @param id 主键
   */
  @ApiOperation(value = "按照ProductLevel实体中的（id）主键进行查询明细查询，查询的明细包括当前业务表单所有的关联属性。")
  @GetMapping("/findDetailsById")
  public Result<ProductLevelVo> findDetailsById(@RequestParam("id") @ApiParam("主键") String id) {
    try {
      ProductLevelVo result = this.productLevelVoService.findDetailsById(id);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "新增")
  @PostMapping(value = "")
  public Result<ProductLevelVo> create(@RequestBody ProductLevelDto dto) {
    try {
      ProductLevelVo current = this.productLevelVoService.create(dto);
      return Result.ok(current);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("编辑")
  @PatchMapping(value = "")
  public Result<ProductLevelVo> update(@RequestBody ProductLevelDto dto) {
    try {
      ProductLevelVo current = this.productLevelVoService.update(dto);
      return Result.ok(current);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 启用
   */
  @ApiOperation(value = "启用")
  @PatchMapping("/enable")
  public Result<?> enable(@RequestBody List<String> ids) {
    try {
      this.productLevelVoService.enableBatch(ids);
      return Result.ok("启用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 禁用
   */
  @ApiOperation(value = "禁用")
  @PatchMapping("/disable")
  public Result<?> disable(@RequestBody List<String> ids) {
    try {
      this.productLevelVoService.disableBatch(ids);
      return Result.ok("禁用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 删除
   */
  @ApiOperation(value = "删除")
  @DeleteMapping("/delete")
  public Result<?> delete(@RequestParam("ids") List<String> ids) {
    try {
      this.productLevelVoService.deleteBatch(ids);
      return Result.ok("删除成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("重置降维编码")
  @PatchMapping("/updateRuleCode")
  public Result<?> updateRuleCode() {
    try {
      this.productLevelVoService.updateRuleCode();
      return Result.ok("重置降维编码成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据产品层级编码查询该产品层级及全部下级层级的编码
   *
   * @param productLevelCode 产品层级编码
   */
  @ApiOperation(value = "根据产品层级编码查询该产品层级及全部下级层级的编码")
  @GetMapping("/findCurAndChildrenCodesByCode")
  public Result<List<String>> findCurAndChildrenCodesByCode(
      @RequestParam("productLevelCode") @ApiParam("产品层级编码") String productLevelCode) {
    try {
      List<String> result = this.productLevelVoService.findCurAndChildrenCodesByCodes(Sets.newHashSet(productLevelCode));
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
  
  /**
   * 获取所有的产品层级
   */
  @ApiOperation(value = "获取所有的产品层级")
  @GetMapping("/findAll")
  public Result<List<ProductLevelVo>> findAll() {
    try {
      List<ProductLevelVo> result = this.productLevelVoService.findAll();
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

}
