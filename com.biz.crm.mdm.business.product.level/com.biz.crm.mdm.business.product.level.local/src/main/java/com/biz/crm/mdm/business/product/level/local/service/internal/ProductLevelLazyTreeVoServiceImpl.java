package com.biz.crm.mdm.business.product.level.local.service.internal;
/**
 * Created by <PERSON><PERSON> on 2021-10-28 18:06.
 */

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.dto.TreeDto;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.TreeRuleCodeStrategy;
import com.biz.crm.business.common.sdk.service.TreeRuleCodeStrategyHolder;
import com.biz.crm.business.common.sdk.utils.TreeUtil;
import com.biz.crm.business.common.sdk.vo.LazyTreeVo;
import com.biz.crm.mdm.business.product.level.local.entity.ProductLevel;
import com.biz.crm.mdm.business.product.level.local.repository.ProductLevelRepository;
import com.biz.crm.mdm.business.product.level.local.service.ProductLevelLazyTreeVoService;
import com.biz.crm.mdm.business.product.level.sdk.common.constant.ProductLevelConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: crm
 * @description: 产品层级懒加载树服务实现
 * @author: Bao Hongbin
 * @create: 2021-10-28 18:06
 **/
@Service
public class ProductLevelLazyTreeVoServiceImpl implements ProductLevelLazyTreeVoService {
  @Autowired(required = false)
  private ProductLevelRepository productLevelRepository;
  @Autowired(required = false)
  private TreeRuleCodeStrategyHolder treeRuleCodeStrategyHolder;

  @Override
  public List<LazyTreeVo> findLazyTree(TreeDto treeDto) {
    treeDto = Optional.ofNullable(treeDto).orElse(new TreeDto());
    List<LazyTreeVo> list = new ArrayList<>();
    //设置排除ruleCode
    String excludeRuleCode = "";
    if (StringUtils.isNotEmpty(treeDto.getExcludeCodeAndChildren())) {
      //如果排除编码不为空，说明存在查询时需要排除的数据及其所以子集，则设置需要排除的ruleCode，通过ruleCode向下排除
      ProductLevel one = productLevelRepository.findDetailsByCode(treeDto.getExcludeCodeAndChildren());
      if (one != null) {
        excludeRuleCode = one.getRuleCode();
      }
    }
    String includeRuleCode = "";
    if (StringUtils.isNotEmpty(treeDto.getIncludeCodeAndChildren())) {
      //如果排除编码不为空，说明存在查询时需要排除的数据及其所以子集，则设置需要排除的ruleCode，通过ruleCode向下排除
      ProductLevel one = productLevelRepository.findDetailsByCode(treeDto.getIncludeCodeAndChildren());
      if (one != null) {
        includeRuleCode = one.getRuleCode();
      }
    }
    if (StringUtils.isNotEmpty(treeDto.getParentCode())) {
      //如果父级编码不为空，说明本次只查询该编码的下一级数据构建成树形结构
      list.addAll(productLevelRepository.findLazyTreeList(treeDto.getEnableStatus(),
          null, treeDto.getParentCode(), null, null, excludeRuleCode,includeRuleCode));
    } else if (StringUtils.isNotEmpty(treeDto.getCode())) {
      //如果父级编码不为空，说明本次只查询该编码的下一级数据构建成树形结构
      list.addAll(productLevelRepository.findLazyTreeList(treeDto.getEnableStatus(),
          null, treeDto.getParentCode(), Collections.singletonList(treeDto.getCode()), null, excludeRuleCode,includeRuleCode));
    } else if (!StringUtils.isEmpty(treeDto.getName())) {
      //如果名称不为空，说明本次查询是通过名称模糊查询，则需要查询出所有符合模糊查询条件的数据，并查询出其上级构建成树形结构
      List<ProductLevel> likeList = productLevelRepository.findListLikeName(treeDto.getEnableStatus(), treeDto.getName());
      if (!CollectionUtils.isEmpty(likeList)) {
        TreeRuleCodeStrategy treeRuleCodeStrategy = this.treeRuleCodeStrategyHolder.getStrategy(null);
        Set<String> parentRuleCodes = treeRuleCodeStrategy.findParentRuleCodeByRuleCodes(ProductLevelConstant.RULE_CODE_LENGTH,
                likeList.stream().map(ProductLevel::getRuleCode).collect(Collectors.toList()));
        list.addAll(productLevelRepository.findLazyTreeList(treeDto.getEnableStatus(), null, null,
            null, new ArrayList<>(parentRuleCodes), excludeRuleCode,includeRuleCode));
      }
    } else {
      //否则，说明本次查询是只查询顶层数据
      list.addAll(productLevelRepository.findLazyTreeList(treeDto.getEnableStatus(), true, null,
          null, null, excludeRuleCode,includeRuleCode));
    }
    if (CollectionUtil.isNotEmpty(list)) {
      for (LazyTreeVo lazyTreeVo : list) {
        EnableStatusEnum enableStatusEnum = EnableStatusEnum.codeToEnum(lazyTreeVo.getEnableStatus());
        if (EnableStatusEnum.DISABLE.equals(enableStatusEnum)) {
          lazyTreeVo.setName(lazyTreeVo.getName() + "(" + enableStatusEnum.getDes() + ")");
        }
        lazyTreeVo.setHasChild(null != lazyTreeVo.getHasChildFlag() && lazyTreeVo.getHasChildFlag().compareTo(0) > 0);
      }
      return TreeUtil.generateLazyTreeByParentCode(list);
    }
    return new ArrayList<>();
  }
}
