package com.biz.crm.mdm.business.product.level.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TreeDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 产品层级树dto
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/23 15:09
 */
@Data
@ApiModel("产品层级树dto")
public class ProductLevelTreeDto extends TreeDto {

    @ApiModelProperty("产品层级编码")
    private String productLevelCode;

    @ApiModelProperty("产品层级名称")
    private String productLevelName;

    @ApiModelProperty("产品层级类型")
    private String productLevelType;

    @ApiModelProperty("产品层级分类")
    private String productLevelCategory;

}
