package com.biz.crm.mdm.business.product.level.sdk.deprecated.dto;

import com.biz.crm.business.common.sdk.deprecated.vo.CrmTreeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 请求vo
 *
 * <AUTHOR>
 * @date 2020-09-01 14:13:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MdmProductLevelDto")
@Deprecated
public class MdmProductLevelDto extends CrmTreeVo {

  @ApiModelProperty("ID集合")
  private List<String> ids;

  @ApiModelProperty("产品层级编码")
  private String productLevelCode;

  @ApiModelProperty("产品层级名称")
  private String productLevelName;

  @ApiModelProperty("产品层级类型")
  private String productLevelType;

  @ApiModelProperty("产品层级分类")
  private String productLevelCategory;

  @ApiModelProperty("上级编码")
  private String parentCode;

  @ApiModelProperty("产品层级编码，产品层级树编码字段")
  private String code;

}
