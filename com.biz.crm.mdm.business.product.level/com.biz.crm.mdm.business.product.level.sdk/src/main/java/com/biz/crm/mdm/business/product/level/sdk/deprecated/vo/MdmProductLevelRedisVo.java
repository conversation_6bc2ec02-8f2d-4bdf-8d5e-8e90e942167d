package com.biz.crm.mdm.business.product.level.sdk.deprecated.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 产品层级缓存vo
 *
 * <AUTHOR>
 * @date 2020-11-17 20:23:13
 */
@Data
@Deprecated
public class MdmProductLevelRedisVo implements Serializable {

  private static final long serialVersionUID = -8326185268334113278L;
  private String id;
  private String productLevelCode;
  private String productLevelName;
  private String productLevelType;
  @ApiModelProperty("产品层级分类")
  private String productLevelCategory;
  private String parentCode;
  private String parentName;
  private String enableStatus;
  private String ruleCode;
  private Integer levelNum;

  private String ext1 = StringUtils.EMPTY;
  private String ext2 = StringUtils.EMPTY;
  private String ext3 = StringUtils.EMPTY;
  private String ext4 = StringUtils.EMPTY;
  private String ext5 = StringUtils.EMPTY;
  private String ext6 = StringUtils.EMPTY;
  private String ext7 = StringUtils.EMPTY;
  private String ext8 = StringUtils.EMPTY;
  private String ext9 = StringUtils.EMPTY;
  private String ext10 = StringUtils.EMPTY;
  private String ext11 = StringUtils.EMPTY;
  private String ext12 = StringUtils.EMPTY;
  private String ext13 = StringUtils.EMPTY;
  private String ext14 = StringUtils.EMPTY;
  private String ext15 = StringUtils.EMPTY;
  private String ext16 = StringUtils.EMPTY;
  private String ext17 = StringUtils.EMPTY;
  private String ext18 = StringUtils.EMPTY;
  private String ext19 = StringUtils.EMPTY;
  private String ext20 = StringUtils.EMPTY;
  private String ext21 = StringUtils.EMPTY;
  private String ext22 = StringUtils.EMPTY;
  private String ext23 = StringUtils.EMPTY;
  private String ext24 = StringUtils.EMPTY;
  private String ext25 = StringUtils.EMPTY;
  private String ext26 = StringUtils.EMPTY;
  private String ext27 = StringUtils.EMPTY;
  private String ext28 = StringUtils.EMPTY;
  private String ext29 = StringUtils.EMPTY;
  private String ext30 = StringUtils.EMPTY;
  private String ext31 = StringUtils.EMPTY;
  private String ext32 = StringUtils.EMPTY;
  private String ext33 = StringUtils.EMPTY;
  private String ext34 = StringUtils.EMPTY;
  private String ext35 = StringUtils.EMPTY;
  private String ext36 = StringUtils.EMPTY;
  private String ext37 = StringUtils.EMPTY;
  private String ext38 = StringUtils.EMPTY;
  private String ext39 = StringUtils.EMPTY;
  private String ext40 = StringUtils.EMPTY;
  private String ext41 = StringUtils.EMPTY;
  private String ext42 = StringUtils.EMPTY;
  private String ext43 = StringUtils.EMPTY;
  private String ext44 = StringUtils.EMPTY;
  private String ext45 = StringUtils.EMPTY;
  private String ext46 = StringUtils.EMPTY;
  private String ext47 = StringUtils.EMPTY;
  private String ext48 = StringUtils.EMPTY;
  private String ext49 = StringUtils.EMPTY;
  private String ext50 = StringUtils.EMPTY;
}