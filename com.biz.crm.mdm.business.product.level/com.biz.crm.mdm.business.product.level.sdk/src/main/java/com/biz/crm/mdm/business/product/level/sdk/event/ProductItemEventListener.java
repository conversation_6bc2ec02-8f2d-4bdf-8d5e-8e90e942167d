package com.biz.crm.mdm.business.product.level.sdk.event;

import com.biz.crm.mdm.business.product.level.sdk.dto.ProductLevelEventDto;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductItemVo;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

import java.util.List;

/**
 * 产品分类事件监听
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/7/24 15:40
 */
public interface ProductItemEventListener extends NebulaEvent {
    /**
     * 当新增产品层级时通知监听
     *
     * @param productItemVos
     */
    void onBatchCreate(List<ProductItemVo> productItemVos);

    /**
     * 当更新产品层级时通知监听
     *
     * @param productItemVos
     */
    void onBatchUpdate(List<ProductItemVo> productItemVos);

    /**
     * 当删除产品层级时通知监听
     *
     * @param productItemVos
     */
    void onBatchDelete(List<ProductItemVo> productItemVos);

    /**
     * 当启用产品层级时通知监听
     *
     * @param productItemVos
     */
    void onBatchEnable(List<ProductItemVo> productItemVos);


    /**
     * 当禁用产品层级时通知监听
     *
     * @param eventDtos
     */
    void onBatchDisable(List<ProductItemVo> eventDtos);

    /**
     * 当更新产品层级时通知监听
     *
     * @param dto
     */
    default void onUpdate(ProductLevelEventDto dto) {
    }
}
