package com.biz.crm.mdm.business.product.level.sdk.deprecated.vo;

import com.biz.crm.business.common.sdk.deprecated.vo.CrmTreeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 返回vo
 *
 * <AUTHOR>
 * @date 2020-09-01 14:13:56
 */
@Data
@ApiModel(value = "MdmProductLevelSelectVo", description = "产品层级下拉框返回VO")
@Deprecated
public class MdmProductLevelSelectVo extends CrmTreeVo {

  @ApiModelProperty("产品层级编码")
  private String productLevelCode;

  @ApiModelProperty("产品层级名称")
  private String productLevelName;

  @ApiModelProperty("产品层级类型")
  private String productLevelType;

  @ApiModelProperty("产品层级分类")
  private String productLevelCategory;

  @ApiModelProperty("上级产品层级编码")
  private String parentCode;

}