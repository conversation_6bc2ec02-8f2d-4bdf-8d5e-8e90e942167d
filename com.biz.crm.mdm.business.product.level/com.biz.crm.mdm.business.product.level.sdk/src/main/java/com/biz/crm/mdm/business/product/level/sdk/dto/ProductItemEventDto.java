package com.biz.crm.mdm.business.product.level.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import com.bizunited.nebula.event.sdk.service.NebulaEventDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品分类事件dto
 *
 * <AUTHOR>
 * @date 2022/7/4
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "产品分类事件dto")
public class ProductItemEventDto extends TenantDto implements NebulaEventDto {

    @ApiModelProperty("分类编码")
    private String productItemCode;

    @ApiModelProperty("分类名称")
    private String productItemName;

    @ApiModelProperty("分类类型")
    private String productItemType;

    @ApiModelProperty("业态")
    private String businessType;

}
