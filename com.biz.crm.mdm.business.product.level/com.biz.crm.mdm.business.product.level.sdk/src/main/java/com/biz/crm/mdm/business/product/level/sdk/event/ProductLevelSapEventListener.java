package com.biz.crm.mdm.business.product.level.sdk.event;

import com.biz.crm.mdm.business.product.level.sdk.dto.ProductLevelEventDto;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductLevelSapVo;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

import java.util.List;

/**
 * 产品层级事件监听
 * Created by Bao <PERSON> on 2021-10-12 16:55.
 */
public interface ProductLevelSapEventListener extends NebulaEvent {
    /**
     * 当新增产品层级时通知监听
     *
     * @param productLevelVos
     */
    void onBatchCreate(List<ProductLevelSapVo> productLevelVos);

    /**
     * 当更新产品层级时通知监听
     *
     * @param productLevelVos
     */
    void onBatchUpdate(List<ProductLevelSapVo> productLevelVos);

    /**
     * 当删除产品层级时通知监听
     *
     * @param productLevelVos
     */
    void onBatchDelete(List<ProductLevelSapVo> productLevelVos);

    /**
     * 当启用产品层级时通知监听
     *
     * @param productLevelVos
     */
    void onBatchEnable(List<ProductLevelSapVo> productLevelVos);


    /**
     * 当禁用产品层级时通知监听
     *
     * @param eventDtos
     */
    void onBatchDisable(List<ProductLevelSapVo> eventDtos);

    /**
     * 当更新产品层级时通知监听
     *
     * @param dto
     */
    default void onUpdate(ProductLevelEventDto dto) {
    }
}
