package com.biz.crm.mdm.business.product.level.sdk.dto;


import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.biz.crm.business.common.sdk.vo.TenantTreeFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 产品分类
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/7/24 15:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ProductItemDto", description = "产品分类请求dto")
public class ProductItemDto extends TenantFlagOpDto {

    private static final long serialVersionUID = -5130713983852967265L;

    @ApiModelProperty("分类编码")
    private String productItemCode;

    @ApiModelProperty("编码/名称")
    private String unionName;

    @ApiModelProperty("分类名称")
    private String productItemName;

    @ApiModelProperty("分类类型")
    private String productItemType;

    @ApiModelProperty("业态")
    private String businessType;

    @ApiModelProperty("层级")
    private Integer levelNum;

    @ApiModelProperty("回显编码，字符串，一般用于单选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这条数据")
    private String selectedCode;

    @ApiModelProperty("回显编码集合，字符串数组，一般用于多选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据")
    private List<String> selectedCodeList;
}
