package com.biz.crm.mdm.business.product.level.sdk.dto;
/**
 * Created by <PERSON><PERSON> on 2021-10-08 16:31.
 */

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @program: crm
 * @description: 产品层级分页查询dto
 * @author: <PERSON><PERSON>
 * @create: 2021-10-08 16:31
 **/
@Data
@ApiModel("产品层级分页查询dto")
public class ProductLevelPaginationDto extends TenantFlagOpDto {

  @ApiModelProperty(name = "ruleCode", value = "规则code查询用")
  private String ruleCode;

  @ApiModelProperty(name = "levelNum", value = "层级等级查询用")
  private Integer levelNum;

  @ApiModelProperty("ID集合")
  private List<String> ids;

  @ApiModelProperty("产品层级编码")
  private String productLevelCode;

  @ApiModelProperty("产品层级名称")
  private String productLevelName;

  @ApiModelProperty("产品层级类型")
  private String productLevelType;

  @ApiModelProperty("产品层级分类")
  private String productLevelCategory;

  @ApiModelProperty("上级编码")
  private String parentCode;

  @ApiModelProperty("上级产品层级名称")
  private String parentName;

  @ApiModelProperty("产品层级名称，模糊查询编码或名称")
  private String productLevelCodeOrName;

  @ApiModelProperty("产品层级编码，只查询当前产品层级及下级")
  private String underProductLevelCode;

  @ApiModelProperty("产品层级编码，排除当前层级和下级")
  private String notUnderProductLevelCode;

  @ApiModelProperty("回显编码，字符串，一般用于单选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这条数据")
  private String selectedCode;

  @ApiModelProperty("回显编码集合，字符串数组，一般用于多选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据")
  private List<String> selectedCodeList;

  /**
   * 产品层级ruleCode，只查询当前产品层级及下级
   */
  private String underThisRuleCode;

  /**
   * 产品层级ruleCode，排除当前产品层级及下级
   */
  private String notUnderThisRuleCode;
}
