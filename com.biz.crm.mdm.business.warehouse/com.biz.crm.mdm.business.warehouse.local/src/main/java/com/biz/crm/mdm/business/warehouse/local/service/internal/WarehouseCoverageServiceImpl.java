package com.biz.crm.mdm.business.warehouse.local.service.internal;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.region.sdk.constant.RegionConstant;
import com.biz.crm.mdm.business.region.sdk.service.RegionVoService;
import com.biz.crm.mdm.business.region.sdk.vo.RegionVo;
import com.biz.crm.mdm.business.warehouse.local.entity.WarehouseCoverageEntity;
import com.biz.crm.mdm.business.warehouse.local.repository.WarehouseCoverageRepository;
import com.biz.crm.mdm.business.warehouse.local.service.WarehouseCoverageService;
import com.biz.crm.mdm.business.warehouse.sdk.dto.WarehouseCoverageDto;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 仓库覆盖区域表服务实现类
 *
 * <AUTHOR>
 * @date 2021-11-19 10:27:45
 */
@Slf4j
@Service("warehouseCoverageService")
public class WarehouseCoverageServiceImpl implements WarehouseCoverageService {

  @Autowired(required = false)
  private WarehouseCoverageRepository warehouseCoverageRepository;
  @Autowired(required = false)
  private RegionVoService regionVoService;
  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @Override
  @Transactional
  public void update(List<WarehouseCoverageDto> dtoList, String warehouseCode, Integer regionLevel) {
    Validate.notBlank(warehouseCode, "仓库编码不能为空");
    this.warehouseCoverageRepository.deleteByWarehouseCode(warehouseCode);
    if (CollectionUtils.isEmpty(dtoList)) {
      return;
    }
    List<WarehouseCoverageEntity> entityList = (List<WarehouseCoverageEntity>) this.nebulaToolkitService.copyCollectionByWhiteList(dtoList, WarehouseCoverageDto.class, WarehouseCoverageEntity.class, HashSet.class, ArrayList.class);
    for(WarehouseCoverageEntity item : entityList){
      item.setWarehouseCode(warehouseCode);
      item.setTenantCode(TenantUtils.getTenantCode());
      item.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
      item.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
      //兼容之前的
      if(RegionConstant.PROVINCE_LEVEL_NUM.equals(item.getRegionLevel())){
        item.setProvinceCode(item.getRegionCode());
        item.setProvinceName(item.getRegionName());
      }
      if(RegionConstant.CITY_LEVEL_NUM.equals(item.getRegionLevel())){
        item.setCityCode(item.getRegionCode());
        item.setCityName(item.getRegionName());
      }
      if(RegionConstant.DISTRICT_LEVEL_NUM.equals(item.getRegionLevel())){
        item.setDistrictCode(item.getRegionCode());
        item.setDistrictName(item.getRegionName());
      }
      //校验当前覆盖区域是否和已选着覆盖区域相同
      if (item.getIsChecked()) {
        List<WarehouseCoverageEntity> list = this.warehouseCoverageRepository.lambdaQuery()
            .eq(WarehouseCoverageEntity::getTenantCode, TenantUtils.getTenantCode())
            .eq(WarehouseCoverageEntity::getRegionCode, item.getRegionCode())
            .eq(WarehouseCoverageEntity::getRegionLevel, item.getRegionLevel())
            .ne(WarehouseCoverageEntity::getWarehouseCode, warehouseCode)
            .list();
        Validate.isTrue(ObjectUtils.isEmpty(list), "[" + item.getRegionName() + "]区域已被仓库覆盖!");
      } else {
        List<WarehouseCoverageEntity> list = this.warehouseCoverageRepository.lambdaQuery()
            .eq(WarehouseCoverageEntity::getTenantCode, TenantUtils.getTenantCode())
            .eq(WarehouseCoverageEntity::getRegionCode, item.getRegionCode())
            .eq(WarehouseCoverageEntity::getRegionLevel, item.getRegionLevel())
            .eq(WarehouseCoverageEntity::getIsChecked, true)
            .ne(WarehouseCoverageEntity::getWarehouseCode, warehouseCode)
            .list();
        Validate.isTrue(ObjectUtils.isEmpty(list), "[" + item.getRegionName() + "]区域已被仓库覆盖!");
      }
      //旧数据校验
      if(item.getRegionLevel().equals(RegionConstant.PROVINCE_LEVEL_NUM) && item.getIsChecked()){
        List<WarehouseCoverageEntity> provinceList = this.warehouseCoverageRepository.lambdaQuery()
            .eq(WarehouseCoverageEntity::getTenantCode, TenantUtils.getTenantCode())
            .eq(WarehouseCoverageEntity::getProvinceCode, item.getRegionCode())
            .ne(WarehouseCoverageEntity::getWarehouseCode, warehouseCode)
            .list();
        Validate.isTrue(ObjectUtils.isEmpty(provinceList), "["+item.getRegionName()+"]区域已被仓库覆盖!");
      }
      if(item.getRegionLevel().equals(RegionConstant.CITY_LEVEL_NUM) && item.getIsChecked()){
        List<WarehouseCoverageEntity> cityList = this.warehouseCoverageRepository.lambdaQuery()
            .eq(WarehouseCoverageEntity::getTenantCode, TenantUtils.getTenantCode())
            .eq(WarehouseCoverageEntity::getCityCode, item.getRegionCode())
            .ne(WarehouseCoverageEntity::getWarehouseCode, warehouseCode)
            .list();
        Validate.isTrue(ObjectUtils.isEmpty(cityList), "["+item.getRegionName()+"]区域已被仓库覆盖!");
      }
      if(item.getRegionLevel().equals(RegionConstant.DISTRICT_LEVEL_NUM)){
        List<WarehouseCoverageEntity> districtList = this.warehouseCoverageRepository.lambdaQuery()
            .eq(WarehouseCoverageEntity::getTenantCode, TenantUtils.getTenantCode())
            .eq(WarehouseCoverageEntity::getDistrictCode, item.getRegionCode())
            .ne(WarehouseCoverageEntity::getWarehouseCode, warehouseCode)
            .list();
        Validate.isTrue(ObjectUtils.isEmpty(districtList), "["+item.getRegionName()+"]区域已被仓库覆盖!");
      }
    }
    this.warehouseCoverageRepository.saveBatch(entityList);
  }




  @Override
  public List<WarehouseCoverageDto> findRegionTree(Integer regionLevel, List<WarehouseCoverageDto> warehouseCoverageDtoList) {
    Validate.isTrue(CollectionUtils.isNotEmpty(warehouseCoverageDtoList), "进行当前操作时，信息对象必须传入!");
    // 去重
    warehouseCoverageDtoList = warehouseCoverageDtoList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection( () -> new TreeSet<>(Comparator.comparing(WarehouseCoverageDto::getRegionCode))), ArrayList::new));

    // 查询信息
    List<String> regionCodeList = warehouseCoverageDtoList.stream().map(WarehouseCoverageDto::getRegionCode).collect(Collectors.toList());
    List<RegionVo> regionVoList = this.regionVoService.findByRegionCodeList(regionCodeList);

    // 判断最低层级，补全下级
    List<RegionVo> subList = Lists.newArrayList();
    List<RegionVo> needSubList = regionVoList;
    long notBottomCount;
    if (regionLevel != null){
      do {
      // 筛选出需要查子级的数据 needSubList
      List<String> codeList = needSubList.stream().filter(r -> r.getRegionLevel() < regionLevel).map(RegionVo::getRegionCode).collect(Collectors.toList());
      List<RegionVo> tempSubList = this.regionVoService.findByParentCodeList(codeList);
      subList.addAll(tempSubList);
      notBottomCount = tempSubList.stream().filter(r -> r.getRegionLevel() < regionLevel).count();
      needSubList = tempSubList;
      } while (notBottomCount > 0);
    }

    // 每个都有parentCode，遍历得到，统一查询上级
    List<RegionVo> parentList = Lists.newArrayList();
    List<RegionVo> needParentList = regionVoList;
    long notTopCount;
    do {
      // 筛选出需要查父级的数据，父级最高只查询到省级
      List<String> parentCodeList = needParentList.stream().filter(r -> r.getRegionLevel() > RegionConstant.PROVINCE_LEVEL_NUM).map(RegionVo::getParentCode).collect(Collectors.toList());
      List<RegionVo> tempParentList = this.regionVoService.findByRegionCodeList(parentCodeList);
      parentList.addAll(tempParentList);
      notTopCount = tempParentList.stream().filter(r -> r.getRegionLevel() > RegionConstant.PROVINCE_LEVEL_NUM).count();
      needParentList = tempParentList;
    } while (notTopCount > 0);

    // 当前传入数据及子级 添加勾选状态
    Set<String> regionCodeSet = Sets.newHashSet();
    List<WarehouseCoverageDto> regionDtoList = Lists.newArrayList();
    for (RegionVo regionVo : regionVoList) {
      WarehouseCoverageDto dto = new WarehouseCoverageDto();
      dto.setRegionCode(regionVo.getRegionCode());
      dto.setRegionName(regionVo.getRegionName());
      dto.setParentCode(regionVo.getParentCode());
      dto.setRegionLevel(regionVo.getRegionLevel());
      regionDtoList.add(dto);
      regionCodeSet.add(regionVo.getRegionCode());
    }
    List<WarehouseCoverageDto> subDtoList = Lists.newArrayList();
    for (RegionVo regionVo : subList) {
      // 如果当前数据已经含有此数据，放弃这里的下级数据。去重
      if (regionCodeSet.contains(regionVo.getRegionCode())) {
        continue;
      }
      WarehouseCoverageDto dto = new WarehouseCoverageDto();
      dto.setRegionCode(regionVo.getRegionCode());
      dto.setRegionName(regionVo.getRegionName());
      dto.setParentCode(regionVo.getParentCode());
      dto.setRegionLevel(regionVo.getRegionLevel());
      subDtoList.add(dto);
      regionCodeSet.add(regionVo.getRegionCode());
    }
    List<WarehouseCoverageDto> parentDtoList = Lists.newArrayList();
    for (RegionVo regionVo : parentList) {
      // 如果当前数据及下级已经含有此数据，放弃这里的父级数据。去重
      if (regionCodeSet.contains(regionVo.getRegionCode())) {
        continue;
      }
      WarehouseCoverageDto dto = new WarehouseCoverageDto();
      dto.setRegionCode(regionVo.getRegionCode());
      dto.setRegionName(regionVo.getRegionName());
      dto.setParentCode(regionVo.getParentCode());
      dto.setRegionLevel(regionVo.getRegionLevel());
      parentDtoList.add(dto);
    }
    // 三个集合数据没有重复
    List<WarehouseCoverageDto> all = Lists.newArrayList();
    all.addAll(regionDtoList);
    all.addAll(subDtoList);
    all.addAll(parentDtoList);

    // 只有最低级需要设置isChecked
    for (WarehouseCoverageDto dto : all) {
      // 存在一个元素父级是当前元素，当前元素就是父级
      boolean isParent = all.stream().anyMatch(r -> r.getParentCode().equals(dto.getRegionCode()));
      dto.setIsChecked(!isParent);
    }
    return all;
  }
}
