package com.biz.crm.mdm.business.warehouse.local.event;

import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.mdm.business.warehouse.sdk.event.WarehouseEventListener;
import com.biz.crm.mdm.business.warehouse.sdk.vo.WarehouseEventVo;
import com.biz.crm.mdm.business.warehouse.sdk.vo.WarehouseVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/27 17:21
 * @ClassName WarehouseEventLogListenerImpl
 * @Description TODO 仓库管理事件日志监听器实现
 */
@Component
public class WarehouseEventLogListenerImpl implements WarehouseEventListener {

    @Autowired(required = false)
    private CrmBusinessLogVoService crmBusinessLogVoService;

    @Override
    public void onCreate(WarehouseEventVo vo) {
        WarehouseVo newest = vo.getNewWarehouse();
        WarehouseVo original = vo.getOldWarehouse();
        String onlyKey = newest.getId();
        CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
        crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
        crmBusinessLogDto.setOnlyKey(onlyKey);
        crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setOldObject(original);
        crmBusinessLogDto.setNewObject(newest);
        crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    }

    @Override
    public void onUpdate(WarehouseEventVo vo) {
        WarehouseVo newest = vo.getNewWarehouse();
        WarehouseVo original = vo.getOldWarehouse();
        String onlyKey = newest.getId();
        CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
        crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
        crmBusinessLogDto.setOnlyKey(onlyKey);
        crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setOldObject(original);
        crmBusinessLogDto.setNewObject(newest);
        crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    }

    @Override
    public void onDisable(WarehouseEventVo vo) {
        List<WarehouseVo> warehouseVoList = vo.getWarehouseVoList();
        if (CollectionUtils.isEmpty(warehouseVoList)){
            return;
        }
        warehouseVoList.forEach(warehouseVo -> {
            String onlyKey = warehouseVo.getId();
            WarehouseVo oldObj = new WarehouseVo();
            oldObj.setId(onlyKey);
            oldObj.setEnableStatus(warehouseVo.getEnableStatus());
            WarehouseVo newObj = new WarehouseVo();
            newObj.setId(onlyKey);
            newObj.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
            WarehouseEventVo warehouseEventVo = new WarehouseEventVo();
            warehouseEventVo.setOldWarehouse(oldObj);
            warehouseEventVo.setNewWarehouse(newObj);
            this.onUpdate(warehouseEventVo);
        });
    }

    @Override
    public void onEnable(WarehouseEventVo vo) {
        List<WarehouseVo> warehouseVoList = vo.getWarehouseVoList();
        if (CollectionUtils.isEmpty(warehouseVoList)){
            return;
        }
        warehouseVoList.forEach(warehouseVo -> {
            String onlyKey = warehouseVo.getId();
            WarehouseVo oldObj = new WarehouseVo();
            oldObj.setId(onlyKey);
            oldObj.setEnableStatus(warehouseVo.getEnableStatus());
            WarehouseVo newObj = new WarehouseVo();
            newObj.setId(onlyKey);
            newObj.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            WarehouseEventVo warehouseEventVo = new WarehouseEventVo();
            warehouseEventVo.setOldWarehouse(oldObj);
            warehouseEventVo.setNewWarehouse(newObj);
            this.onUpdate(warehouseEventVo);
        });
    }

    @Override
    public void onDelete(WarehouseEventVo vo) {
        List<WarehouseVo> warehouseVoList = vo.getWarehouseVoList();
        if (CollectionUtils.isEmpty(warehouseVoList)){
            return;
        }
        warehouseVoList.forEach(warehouseVo -> {
            String onlyKey = warehouseVo.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.DELETE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
            crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
            crmBusinessLogDto.setOldObject(warehouseVo);
            crmBusinessLogDto.setNewObject(null);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }
}
