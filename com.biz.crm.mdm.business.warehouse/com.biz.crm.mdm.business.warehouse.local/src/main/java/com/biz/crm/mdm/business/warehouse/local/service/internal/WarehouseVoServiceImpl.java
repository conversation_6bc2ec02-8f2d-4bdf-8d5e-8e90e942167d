package com.biz.crm.mdm.business.warehouse.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.region.sdk.service.RegionVoService;
import com.biz.crm.mdm.business.region.sdk.vo.RegionVo;
import com.biz.crm.mdm.business.warehouse.local.entity.WarehouseCoverageEntity;
import com.biz.crm.mdm.business.warehouse.local.entity.WarehouseEntity;
import com.biz.crm.mdm.business.warehouse.local.repository.WarehouseCoverageRepository;
import com.biz.crm.mdm.business.warehouse.local.repository.WarehouseRepository;
import com.biz.crm.mdm.business.warehouse.local.service.WarehouseService;
import com.biz.crm.mdm.business.warehouse.sdk.dto.WarehouseDto;
import com.biz.crm.mdm.business.warehouse.sdk.dto.WarehousePageDto;
import com.biz.crm.mdm.business.warehouse.sdk.service.WarehouseVoService;
import com.biz.crm.mdm.business.warehouse.sdk.vo.WarehouseVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 仓库vo服务接口实现类
 *
 * <AUTHOR>
 * @date 2022/3/9
 */
@Service
public class WarehouseVoServiceImpl implements WarehouseVoService {
  @Autowired(required = false)
  private WarehouseRepository warehouseRepository;
  @Autowired(required = false)
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;
  @Autowired(required = false)
  private WarehouseService warehouseService;
  @Autowired(required = false)
  private RegionVoService regionVoService;
  @Autowired(required = false)
  private WarehouseCoverageRepository warehouseCoverageRepository;
  @Autowired(required = false)
  private DictDataVoService dictDataVoService;

  @Override
  public WarehouseVo findDetailsByCode(String warehouseCode) {
    WarehouseEntity entity = this.warehouseRepository.findDetailsByCode(warehouseCode);
    Validate.notNull(entity, "仓库信息不存在");
    return this.nebulaToolkitService.copyObjectByWhiteList(entity, WarehouseVo.class, HashSet.class, ArrayList.class);
  }

  /**
   * 通过覆盖区域市Code获取仓库信息
   * @param cityCode
   * @return
   */
  @Override
  public WarehouseVo findDetailsByCityCode(String cityCode) {
    if(StringUtils.isBlank(cityCode)){
      return null;
    }
    WarehouseEntity entity = this.warehouseRepository.findDetailsByCityCode(cityCode);
    if (ObjectUtils.isEmpty(entity)) {
      //往上寻找 省
      List<RegionVo> regionVos = regionVoService.findByRegionCodeList(Lists.newArrayList(cityCode));
      Validate.isTrue(ObjectUtils.isNotEmpty(regionVos), "当前市无省份，请检查主数据!");
      String regionCode = regionVos.get(0).getParentCode();
      int regionLevel = regionVos.get(0).getRegionLevel() - 1;
      List<WarehouseCoverageEntity> warehouseCoverageEntityList = this.warehouseCoverageRepository.lambdaQuery()
          .eq(WarehouseCoverageEntity::getTenantCode, TenantUtils.getTenantCode())
          .eq(WarehouseCoverageEntity::getProvinceCode, regionCode)
          .eq(WarehouseCoverageEntity::getIsChecked, true)
          .list();
      if (ObjectUtils.isEmpty(warehouseCoverageEntityList)) {
        return null;
      } else {
        entity = this.warehouseRepository.lambdaQuery()
            .eq(WarehouseEntity::getTenantCode, TenantUtils.getTenantCode())
            .eq(WarehouseEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(WarehouseEntity::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
            .eq(WarehouseEntity::getWarehouseCode, warehouseCoverageEntityList.get(0).getWarehouseCode())
            .one();
      }
    }
    if(ObjectUtils.isEmpty(entity)){
      return null;
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(entity, WarehouseVo.class, HashSet.class, ArrayList.class);
  }

  /**
   * 获取默认仓库
   * @param bool
   * @return
   */
  @Override
  public WarehouseVo findDetailsByWarehouseDefault(Boolean bool) {
    if(bool == null){
      return null;
    }
    WarehouseEntity entity = this.warehouseRepository.findDetailsByWarehouseDefault(bool);
    if(entity == null){
      return null;
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(entity, WarehouseVo.class, HashSet.class, ArrayList.class);
  }

  @Override
  public Page<WarehouseVo> findByConditions(Pageable pageable, WarehouseDto Param) {
    pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
    WarehouseDto dto = ObjectUtils.defaultIfNull(Param, new WarehouseDto());
    if (StringUtils.isNotBlank(dto.getCompanyCode())) {
      List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.MDM_SAP_FACTORY_TYPE);
      List<String> factoryCodes = dictDataVos.stream().filter(e -> StringUtils.equals(dto.getCompanyCode(), e.getDictCode())).map(DictDataVo::getDictValue).filter(StringUtils::isNotBlank).collect(Collectors.toList());
      if (CollectionUtils.isEmpty(factoryCodes)) {
        return new Page<>(pageable.getPageNumber(), pageable.getPageSize(), 0);
      }
      dto.setFactoryCodeList(factoryCodes);
    } else {
      return new Page<>(pageable.getPageNumber(), pageable.getPageSize(), 0);
    }
    Page<WarehouseEntity> entityPage = this.warehouseService.findByConditions(pageable, dto);
    Page<WarehouseVo> pageResult = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
    if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
      pageResult.setRecords(convertEntityToVo(entityPage.getRecords()));
    }
    return pageResult;
  }

  @Override
  public List<WarehouseVo> findByWarehouseErpCodes(Set<String> warehouseErpCodes) {
    List<WarehouseEntity> list = this.warehouseRepository.findByWarehouseErpCodes(warehouseErpCodes);
    return  (List<WarehouseVo>) this.nebulaToolkitService.copyCollectionByBlankList(list, WarehouseEntity.class,
            WarehouseVo.class, HashSet.class, ArrayList.class);
  }

  /**
   * 仓库实体列表转VO列表
   *
   * @param entities 仓库实体列表
   * @return 仓库VO列表
   */
  private List<WarehouseVo> convertEntityToVo(List<WarehouseEntity> entities) {
    List<WarehouseVo> list = Lists.newArrayList(this.nebulaToolkitService.copyCollectionByWhiteList(entities, WarehouseEntity.class
        , WarehouseVo.class, HashSet.class, ArrayList.class));
    List<String> regionCodes = Lists.newArrayList();
    list.forEach(warehouseVo -> {
      if (StringUtils.isNotBlank(warehouseVo.getCityCode())) {
        regionCodes.add(warehouseVo.getCityCode());
      }
      if (StringUtils.isNotBlank(warehouseVo.getProvinceCode())) {
        regionCodes.add(warehouseVo.getProvinceCode());
      }
      if (StringUtils.isNotBlank(warehouseVo.getDistrictCode())) {
        regionCodes.add(warehouseVo.getDistrictCode());
      }
    });
    if (CollectionUtils.isEmpty(regionCodes)) {
      return list;
    }
    Map<String, String> regionCodeNameMap = this.regionVoService.findRegionCodeNameMap(regionCodes);
    list.forEach(warehouseVo -> {
      warehouseVo.setProvinceName(regionCodeNameMap.get(warehouseVo.getProvinceCode()));
      warehouseVo.setCityName(regionCodeNameMap.get(warehouseVo.getCityCode()));
      warehouseVo.setDistrictName(regionCodeNameMap.get(warehouseVo.getDistrictCode()));
    });
    return list;
  }

}
