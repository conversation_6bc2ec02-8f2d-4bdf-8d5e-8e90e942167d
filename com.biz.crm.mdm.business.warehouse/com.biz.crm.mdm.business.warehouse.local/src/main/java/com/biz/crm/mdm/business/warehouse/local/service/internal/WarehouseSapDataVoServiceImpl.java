package com.biz.crm.mdm.business.warehouse.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.auth.sdk.service.UrlApiService;
import com.biz.crm.business.common.auth.sdk.vo.UrlAddressVo;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.base.constant.ryytn.RyConstant;
import com.biz.crm.business.common.base.eunm.ExternalSystemEnum;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.base.util.BusinessHttpUtil;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.base.util.ryytn.RySignHeaderUtil;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.biz.crm.business.common.log.sdk.constant.ExternalLogGlobalConstants;
import com.biz.crm.business.common.log.sdk.dto.ExternalLogDetailDto;
import com.biz.crm.business.common.log.sdk.service.ExternalLogVoService;
import com.biz.crm.business.common.log.sdk.util.ExternalLogUtil;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.warehouse.local.entity.WarehouseEntity;
import com.biz.crm.mdm.business.warehouse.local.repository.WarehouseRepository;
import com.biz.crm.mdm.business.warehouse.local.service.WarehouseSapDataVoService;
import com.biz.crm.mdm.business.warehouse.sdk.constant.WarehouseConstant;
import com.biz.crm.mdm.business.warehouse.sdk.vo.SapWarehouseVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 仓库SAP数据
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/28 14:30
 */
@Service
@Slf4j
public class WarehouseSapDataVoServiceImpl implements WarehouseSapDataVoService {

    @Autowired(required = false)
    private WarehouseRepository warehouseRepository;

    @Autowired(required = false)
    @Qualifier("nebulaToolkitService")
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private RedisLockService redisLockService;

    @Autowired(required = false)
    private UrlApiService urlApiService;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Autowired(required = false)
    private ExternalLogVoService externalLogVoService;

    /**
     * 拉取仓库信息
     *
     * @param
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/28 14:29
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NOT_SUPPORTED)
    public void pullWarehouse() {
        log.info("=====>   拉取仓库 start    <=====");
        UrlAddressVo urlAddressVo = urlApiService.getUrlAddressByAccount(RyConstant.STD_SAP_ACCOUNT);
        String lockKey = WarehouseConstant.PULL_WAREHOUSE + DateUtil.dateStrNowYYYYMMDD();
        redisLockService.lock(lockKey, TimeUnit.MINUTES, 30);
        String url = urlAddressVo.getUrl();
        String interfaceAddress = String.format(RyConstant.SAP_INTERFACE_ADDRESS, urlAddressVo.getBusinessKey(), urlAddressVo.getEnvironment());
        try {
            log.info("=====>   拉取仓库  start    <=====");
            String bodyJson = this.buildBodyJson();
            ExternalLogDetailDto logDetailDto = ExternalLogUtil.buildLogSaveInfo(bodyJson, urlAddressVo);
            Map<String, String> headMap = RySignHeaderUtil.getSignHeadMap(urlAddressVo.getAccessId(), urlAddressVo.getSecretKey(), interfaceAddress);
            logDetailDto.setReqHead(JSON.toJSONString(headMap));
            logDetailDto.setMethod(WarehouseConstant.WAREHOUSE_INTERFACE);
            logDetailDto.setRequestUri(interfaceAddress);
            logDetailDto.setMethodMsg("拉取SAP仓库");
            externalLogVoService.addOrUpdateLog(logDetailDto, true);
            Result<String> result = BusinessHttpUtil.post(url + interfaceAddress, bodyJson, headMap);
            ExternalLogUtil.buildLogResult(logDetailDto, result);
            logDetailDto.setStatus(ExternalLogGlobalConstants.E);
            externalLogVoService.addOrUpdateLog(logDetailDto, false);
            if (result.isSuccess()
                    && StringUtil.isNotEmpty(result.getResult())) {
                JSONObject jsonObject = JSONObject.parseObject(result.getResult());
                if (jsonObject.containsKey(RyConstant.SAP_HEAD_DATA)
                        && Objects.nonNull(jsonObject.getJSONObject(RyConstant.SAP_HEAD_DATA))) {
                    JSONObject head = jsonObject.getJSONObject(RyConstant.SAP_HEAD_DATA);
                    String success = head.getString(RyConstant.SAP_HEAD_KEY);
                    String msg = head.getString(RyConstant.SAP_HEAD_MSG);
                    logDetailDto.setTipMsg(msg);
                    if (!ExternalLogGlobalConstants.S.equals(success)) {
                        externalLogVoService.addOrUpdateLog(logDetailDto, false);
                        return;

                    }
                } else {
                    logDetailDto.setTipMsg("SAP返回的数据结构异常!");
                    externalLogVoService.addOrUpdateLog(logDetailDto, false);
                    return;
                }
                logDetailDto.setStatus(ExternalLogGlobalConstants.S);
                externalLogVoService.addOrUpdateLog(logDetailDto, false);
                if (jsonObject.containsKey(RyConstant.SAP_DETAIL_DATA)
                        && CollectionUtil.isNotEmpty(jsonObject.getJSONArray(RyConstant.SAP_DETAIL_DATA))) {
                    JSONArray jsonArray = jsonObject.getJSONArray(RyConstant.SAP_DETAIL_DATA);
                    logDetailDto.setRespJsonSize(jsonArray.size());
                    Map<String, WarehouseEntity> dataMap = this.sapDataToEntityList(jsonArray);
                    this.saveOrUpdateMap(dataMap);
                }
            } else {
                log.error("=====>   拉取仓库 失败    <=====");
                log.error("{}", result);
            }
            log.info("=====>   拉取仓库 end    <=====");
        } catch (Exception e) {
            log.error("=====>   拉取仓库失败    <=====");
            log.error(e.getMessage(), e);
            throw e;
        } finally {
            redisLockService.unlock(lockKey);
            log.info("=====>   拉取仓库 end    <=====");
        }
    }

    /**
     * 分离新增和更新的数据,并保存数据
     *
     * @param dataMap
     */
    private void saveOrUpdateMap(Map<String, WarehouseEntity> dataMap) {
        if (CollectionUtil.isEmpty(dataMap)) {
            return;
        }
        List<WarehouseEntity> oldList = warehouseRepository.findAllByCodes(Lists.newArrayList(dataMap.keySet()));
        if (CollectionUtil.isEmpty(oldList)) {
            warehouseRepository.saveBatchXml(new ArrayList<>(dataMap.values()));
            return;
        }
        Map<String, WarehouseEntity> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getWarehouseCode()))
                .collect(Collectors.toMap(WarehouseEntity::getWarehouseCode, v -> v, (n, o) -> n));
        List<WarehouseEntity> updateList = Lists.newArrayList();
        List<WarehouseEntity> saveList = Lists.newArrayList();
        dataMap.values().forEach(entity -> {
            WarehouseEntity oldEntity = oldMap.get(entity.getWarehouseCode());
            if (Objects.nonNull(oldEntity)) {
                this.buildUpdateEntity(oldEntity, entity);
                updateList.add(oldEntity);
            } else {
                saveList.add(entity);
            }
        });

        if (CollectionUtil.isNotEmpty(saveList)) {
            warehouseRepository.saveBatchXml(saveList);
        }
        if (CollectionUtil.isNotEmpty(updateList)) {
            warehouseRepository.updateBatchXml(updateList);
        }
    }

    /**
     * 构建更新实体信息
     *
     * @param oldEntity
     * @param entity
     */
    private void buildUpdateEntity(WarehouseEntity oldEntity, WarehouseEntity entity) {
        oldEntity.setWarehouseErpCode(entity.getWarehouseErpCode());
        oldEntity.setWarehouseName(entity.getWarehouseName());
        oldEntity.setWarehouseAddress(entity.getWarehouseAddress());
        oldEntity.setFactoryCode(entity.getFactoryCode());
        oldEntity.setFactoryName(entity.getFactoryName());
        oldEntity.setProvinceName(entity.getProvinceName());
        oldEntity.setCityName(entity.getCityName());
        oldEntity.setDistrictName(entity.getDistrictName());
        oldEntity.setDelFlag(entity.getDelFlag());
        oldEntity.setEnableStatus(entity.getEnableStatus());
        oldEntity.setModifyAccount(entity.getModifyAccount());
        oldEntity.setModifyName(entity.getModifyName());
        oldEntity.setModifyTime(entity.getModifyTime());
    }

    /**
     * SAP 的 JSON 数据转 实体
     *
     * @param jsonArray
     */
    private Map<String, WarehouseEntity> sapDataToEntityList(JSONArray jsonArray) {
        if (CollectionUtil.isEmpty(jsonArray)) {
            return Maps.newHashMap();
        }
        List<SapWarehouseVo> sapCostCenterVoList = jsonArray.toJavaList(SapWarehouseVo.class);
        Map<String, WarehouseEntity> centerMap = Maps.newConcurrentMap();
        String dataSource = ExternalSystemEnum.SAP.getCode();
        AbstractCrmUserIdentity crmUserIdentity = loginUserService.getAbstractLoginUser();
        Date dateNow = new Date();
        sapCostCenterVoList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getLGORT()))
                .filter(k -> StringUtil.isNotEmpty(k.getWERKS()))
                .forEach(vo -> {
                    WarehouseEntity entity = new WarehouseEntity();
                    this.setBaseInfo(entity, crmUserIdentity);
                    entity.setWarehouseCode(vo.getLGORT() + vo.getWERKS());
                    entity.setWarehouseErpCode(vo.getLGORT());
                    entity.setWarehouseName(vo.getLGOBE());
                    entity.setFactoryCode(vo.getWERKS());
                    entity.setFactoryName(vo.getNAME1());
                    entity.setProvinceName(vo.getBEZEI());
                    entity.setCityName(vo.getZSZS1());
                    entity.setDistrictName(vo.getZSZQ());
                    entity.setWarehouseAddress(vo.getZFHDZ());
                    entity.setSyncUpdateTime(dateNow);
                    entity.setDataSource(dataSource);

                    centerMap.put(entity.getWarehouseCode(), entity);
                });
        return centerMap;
    }


    /**
     * 构建body参数
     *
     * @return
     */
    private String buildBodyJson() {

        JSONObject ctrl = new JSONObject();
        ctrl.put("SYSID", CommonConstant.CURRENT_SYSTEM);
        ctrl.put("REVID", ExternalSystemEnum.SAP.getCode());
        ctrl.put("FUNID", WarehouseConstant.WAREHOUSE_INTERFACE);
        ctrl.put("INFID", UuidCrmUtil.general());
        ctrl.put("UNAME", CommonConstant.CURRENT_SYSTEM);
        ctrl.put("DATUM", DateUtil.dateStrNowYYYYMMDD());
        ctrl.put("UZEIT", DateUtil.dateStrNowHms());
        ctrl.put("KEYID", "");
        ctrl.put("TABIX", 0);
        ctrl.put("MSGTY", "MSGTY");
        ctrl.put("MSAGE", "");

        JSONArray jsonArray = new JSONArray();
        JSONObject data = new JSONObject();
        //工厂
        data.put("WERKS", "");
        //仓库号
        data.put("LGORT", "");
        jsonArray.add(data);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put(RyConstant.SAP_HEAD_DATA, ctrl);
        jsonObject.put(RyConstant.SAP_DETAIL_DATA, jsonArray);

        return jsonObject.toJSONString();
    }

    /**
     * 设置基础信息
     *
     * @param entity
     */
    private void setBaseInfo(TenantFlagOpEntity entity, AbstractCrmUserIdentity crmUserIdentity) {
        Date date = new Date();
        entity.setId(UuidCrmUtil.general());
        entity.setTenantCode(TenantUtils.getTenantCode());
        entity.setCreateAccount(crmUserIdentity.getUsername());
        entity.setCreateName(crmUserIdentity.getRealName());
        entity.setCreateTime(date);
        entity.setModifyAccount(crmUserIdentity.getUsername());
        entity.setModifyName(crmUserIdentity.getRealName());
        entity.setModifyTime(date);
        entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        EnableStatusEnum enableStatusEnum = EnableStatusEnum.ENABLE;
        entity.setEnableStatus(enableStatusEnum.getCode());
    }
}
