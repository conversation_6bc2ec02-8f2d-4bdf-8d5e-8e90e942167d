package com.biz.crm.mdm.business.warehouse.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 仓库覆盖区域Dto
 *
 * <AUTHOR>
 * @date 2021/11/19
 */
@Data
@ApiModel(value = "WarehouseCoverageDto", description = "仓库覆盖区域Dto")
public class WarehouseCoverageDto {

  /**
   * 省编码
   */
  @ApiModelProperty("编码")
  private String regionCode;

  /**
   * 省名称
   */
  @ApiModelProperty("名称")
  private String regionName;
  /**
   * 上层编码
   */
  @ApiModelProperty("上层编码")
  private String parentCode;
  /**
   * 是否选中
   */
  @ApiModelProperty("是否选中")
  private Boolean isChecked;
  /**
   * 行政区域层级
   */
  @ApiModelProperty("行政区域层级")
  private Integer regionLevel;

}
