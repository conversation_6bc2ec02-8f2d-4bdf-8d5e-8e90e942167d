package com.biz.crm.mdm.business.warehouse.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.warehouse.sdk.dto.WarehouseDto;
import com.biz.crm.mdm.business.warehouse.sdk.vo.WarehouseVo;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

/**
 * 仓库vo服务接口类
 *
 * <AUTHOR>
 * @date 2022/3/9
 */
public interface WarehouseVoService {
  /**
   * 根据仓库编码获取仓库详情
   *
   * @param warehouseCode 仓库编码
   * @return 仓库详情
   */
  WarehouseVo findDetailsByCode(String warehouseCode);

  /**
   * 通过覆盖区域市Code获取仓库信息
   * @param cityCode
   * @return
   */
  WarehouseVo findDetailsByCityCode(String cityCode);

  /**
   * 获取默认仓库
   * @param bool
   * @return
   */
  WarehouseVo findDetailsByWarehouseDefault(Boolean bool);

  /**
   * 仓库分页列表
   *
   * @param pageable 分页信息
   * @param dto      请求参数dto
   * @return Page<WarehouseVo> 仓库分页信息
   */
  default Page<WarehouseVo> findByConditions(Pageable pageable, WarehouseDto dto) {
    return null;
  }

  /**
   * 根据仓库Erp编码查询仓库
   *
   * @param warehouseErpCodes 仓库Erp编码
   * @return 仓库详情
   */
  List<WarehouseVo> findByWarehouseErpCodes(Set<String> warehouseErpCodes);
}
