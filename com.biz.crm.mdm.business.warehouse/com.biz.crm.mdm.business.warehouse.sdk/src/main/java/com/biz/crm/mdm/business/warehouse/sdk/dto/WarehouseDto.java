package com.biz.crm.mdm.business.warehouse.sdk.dto;

import com.biz.crm.business.common.sdk.dto.UuidDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 仓库Dto
 *
 * <AUTHOR>
 * @date 2021/11/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "WarehouseDto", description = "仓库Dto")
public class WarehouseDto extends UuidDto {

  @ApiModelProperty("仓库名称")
  private String warehouseName;

  @ApiModelProperty("仓库编码")
  private String warehouseCode;

  @ApiModelProperty("仓库ERP编码")
  private String warehouseErpCode;

  @ApiModelProperty("工厂编码")
  private String factoryTypeCode;

  @ApiModelProperty("工厂编码")
  private String factoryCode;
  private List<String> factoryCodeList;

  @ApiModelProperty("工厂名称")
  private String factoryName;

  @ApiModelProperty("仓库地址")
  private String warehouseAddress;

  @ApiModelProperty("经度")
  private BigDecimal longitude;

  @ApiModelProperty("纬度")
  private BigDecimal latitude;

  @ApiModelProperty("省编码")
  private String provinceCode;

  @ApiModelProperty("市编码")
  private String cityCode;

  @ApiModelProperty("区编码")
  private String districtCode;

  @ApiModelProperty("省名称")
  private String provinceName;

  @ApiModelProperty("市名称")
  private String cityName;

  @ApiModelProperty("区名称")
  private String districtName;

  @ApiModelProperty("仓库责任人")
  private String warehouseHead;

  @ApiModelProperty("联系人电话")
  private String contactPhone;

  @ApiModelProperty("默认仓库;数据字典[yesOrNo]")
  private String warehouseDefault;

  @ApiModelProperty("数据同步时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date syncUpdateTime;

  @ApiModelProperty("数据源[数据字典:mdm_data_source]")
  private String dataSource;

  @ApiModelProperty("区域最低层级")
  private Integer regionLevel;

  @ApiModelProperty("覆盖区域")
  private List<WarehouseCoverageDto> coverageList;

  /**查询字段**/
  @ApiModelProperty("公司编码")
  private String companyCode;
}
