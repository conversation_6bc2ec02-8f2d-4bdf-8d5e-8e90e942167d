package com.biz.crm.mdm.business.warehouse.sdk.dto;

import com.biz.crm.business.common.sdk.dto.UuidDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仓库分页查询Dto
 *
 * <AUTHOR>
 * @date 2021/11/16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "WarehousePageDto", description = "仓库分页查询Dto")
public class WarehousePageDto extends UuidDto {

  @ApiModelProperty("仓库责任人")
  private String warehouseHead;

  @ApiModelProperty("仓库编码")
  private String warehouseCode;

  @ApiModelProperty("仓库名称")
  private String warehouseName;

  @ApiModelProperty("仓库ERP编码")
  private String warehouseErpCode;

  @ApiModelProperty("工厂编码")
  private String factoryTypeCode;

  @ApiModelProperty("仓库地址")
  private String warehouseAddress;

  @ApiModelProperty("启用状态")
  private String enableStatus;

}
