package com.biz.crm.mdm.business.login.config.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-06-19 16:08
 * @description：
 */
@Data
@ApiModel(value = "IndexModuleRangeVo", description = "首页布局组件适配范围Vo")
public class IndexModuleRangeVo extends TenantFlagOpVo {

  @ApiModelProperty("首页组件编码")
  private String moduleCode;

  @ApiModelProperty("范围编码")
  private String rangeCode;

  @ApiModelProperty("范围名称")
  private String rangeName;

  @ApiModelProperty("范围上级编码")
  private String rangeParentCode;

  @ApiModelProperty("范围上级名称")
  private String rangeParentName;

  @ApiModelProperty("首页组件权限类别编码")
  private String moduleTypeCode;

}
