package com.biz.crm.mdm.business.login.config.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-06-19 16:11
 * @description：
 */
@Data
@ApiModel(value = "IndexLayoutDetailDto", description = "首页布局主题详情Dto")
public class IndexLayoutDetailDto extends TenantFlagOpDto {

  @ApiModelProperty("首页模板编码")
  private String layoutCode;

  @ApiModelProperty("首页组件编码")
  private String moduleCode;

  @ApiModelProperty("首页组件名称")
  private String moduleName;

  @ApiModelProperty("首页组件菜单标识")
  private String moduleMenuCode;

  @ApiModelProperty("标题")
  private String title;

  @ApiModelProperty("展示顺序")
  private Integer sort;

}
