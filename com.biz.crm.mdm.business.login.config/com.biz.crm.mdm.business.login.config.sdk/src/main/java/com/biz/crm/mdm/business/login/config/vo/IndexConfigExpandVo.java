package com.biz.crm.mdm.business.login.config.vo;

import com.biz.crm.business.common.sdk.vo.FileVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/29 11:07
 * @ClassName IndexConfigExpandVo
 * @Description TODO
 */
@Data
public class IndexConfigExpandVo extends FileVo {

    /**
     * 数据类型
     */
    @ApiModelProperty("数据类型")
    private String dataType;
    /**
     * 配置头表id
     */
    @ApiModelProperty("配置头标ID")
    private String configId;
    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String name;
}
