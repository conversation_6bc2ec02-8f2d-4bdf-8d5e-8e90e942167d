package com.biz.crm.mdm.business.login.config.deprecated.resp;

import com.biz.crm.business.common.sdk.deprecated.vo.CrmTreeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Package com.biz.crm.sys.index.entity
 * @Description: TODO
 * @date 2020/11/24 下午12:55
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ApiModel(value = "MdmIndexConfigVo", description = "首页配置VO")
@Deprecated
public class MdmIndexConfigVo extends CrmTreeVo {
  private static final long serialVersionUID = 4140070437763956796L;
  @ApiModelProperty("类型 1：后台，2小程序")
  private String dataType;
  @ApiModelProperty("模板名称")
  private String templateName;
  @ApiModelProperty("登录页标题")
  private String indexTitle;
  @ApiModelProperty("服务热线")
  private String servicePhone;
  @ApiModelProperty("客服电话")
  private String clientPhone;
  @ApiModelProperty("备案号")
  private String recordNo;
  @ApiModelProperty("公司名称")
  private String companyName;
  @ApiModelProperty("登录按钮颜色")
  private String buttonColour;
  @ApiModelProperty("底部文字")
  private String bottomText;
  @ApiModelProperty("首行企业logo图片")
  private String logoUrl;
  @ApiModelProperty("长中部长轮播时间")
  private String logoTime;
  @ApiModelProperty("扩展信息")
  private List<MdmIndexConfigExpandVo> items;
  @ApiModelProperty("类型描述")
  private String dataTypeName;
  @ApiModelProperty("首页url地址")
  private String pcFirstUrl;

  public String getDataTypeName() {
    if ("1".equals(dataType)) {
      return "后台配置";
    } else if ("2".equals(dataType)) {
      return "小程序配置";
    }
    return dataTypeName;
  }
}
