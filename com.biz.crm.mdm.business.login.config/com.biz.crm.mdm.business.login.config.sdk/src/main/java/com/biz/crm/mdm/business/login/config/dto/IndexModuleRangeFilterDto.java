package com.biz.crm.mdm.business.login.config.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-06-21 15:37
 * @description：
 */
@Data
@ApiModel(value = "IndexModuleRangeFilterDto", description = "首页布局组件适配范围过滤dto")
public class IndexModuleRangeFilterDto {

  @ApiModelProperty("组件编码")
  private List<String> moduleCodes;
  @ApiModelProperty("职位编码")
  private String positionCode;
  @ApiModelProperty("企业用户编码")
  private String userCode;
  @ApiModelProperty("组织编码")
  private String orgCode;
  @ApiModelProperty("角色编码")
  private List<String> roleCodes;
  @ApiModelProperty("租户")
  private String tenantCode;

}
