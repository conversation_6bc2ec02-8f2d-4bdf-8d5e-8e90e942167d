package com.biz.crm.mdm.business.login.config.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-06-19 16:06
 * @description：
 */
@Data
@ApiModel(value = "IndexModuleDto", description = "首页布局组件Dto")
public class IndexModuleDto extends TenantFlagOpDto {

  @ApiModelProperty("首页组件编码")
  private String moduleCode;

  @ApiModelProperty("首页组件名称")
  private String moduleName;

  @ApiModelProperty("是否默认首页组件 true:是, false:否")
  private Boolean defaultModule = false;

  @ApiModelProperty("首页组件权限类别名称(别名)")
  private String moduleTypeName;

  @ApiModelProperty("首页组件菜单标识")
  private String moduleMenuCode;

  @ApiModelProperty("适配范围")
  List<IndexModuleRangeDto> indexModuleRangeVoList;
}
