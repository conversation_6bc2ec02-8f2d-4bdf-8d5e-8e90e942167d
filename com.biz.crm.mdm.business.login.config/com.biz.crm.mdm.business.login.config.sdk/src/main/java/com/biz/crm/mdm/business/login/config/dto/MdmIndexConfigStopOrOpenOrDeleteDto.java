package com.biz.crm.mdm.business.login.config.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Package com.biz.crm.sys.index.vo
 * @Description: TODO
 * @date 2020/11/24 下午1:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "MdmIndexConfigStopOrOpenOrDeleteVo", description = "首页配置启用禁用入参")
public class MdmIndexConfigStopOrOpenOrDeleteDto {
  @ApiModelProperty("操作数据集合")
  private List<String> ids;
  @ApiModelProperty("003停用，009启用；删除不传该字段")
  private String enableStatus;
}
