package com.biz.crm.mdm.business.login.config.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 参数配置请求DTO
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ParameterManageDto", description = "参数配置")
public class ParameterManageDto extends TenantFlagOpDto {

    @ApiModelProperty("参数")
    private String parameterCode;

    @ApiModelProperty("参数名称")
    private String parameterName;

    @ApiModelProperty("默认值")
    private String defaultValue;

    @ApiModelProperty("参数值")
    private String parameterValue;

    @ApiModelProperty("参数说明")
    private String parameterExplain;

    @ApiModelProperty("所属模块")
    private String parameterModule;

}