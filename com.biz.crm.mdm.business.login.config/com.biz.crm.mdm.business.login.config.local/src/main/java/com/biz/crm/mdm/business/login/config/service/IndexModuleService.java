package com.biz.crm.mdm.business.login.config.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.login.config.dto.IndexModuleDto;
import com.biz.crm.mdm.business.login.config.vo.IndexModuleVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-06-19 16:20
 * @description：
 */
public interface IndexModuleService {

  /**
   * 首页布局组件分页查询
   * @param pageable
   * @param dto
   * @return
   */
  Page<IndexModuleVo> findByConditions(Pageable pageable, IndexModuleDto dto);

  /**
   * 新增组件
   * @param dto
   * @return
   */
  IndexModuleVo create(IndexModuleDto dto);

  /**
   * 修改组件
   * @param dto
   * @return
   */
  IndexModuleVo update(IndexModuleDto dto);

  /**
   * 根据组件id查询详情
   * @param id
   * @return
   */
  IndexModuleVo findDetailById(String id);

  /**
   * 按id集合启用
   *
   * @param ids
   */
  void enableBatch(List<String> ids);

  /**
   * 按id集合禁用
   *
   * @param ids
   */
  void disableBatch(List<String> ids);

  /**
   * 按id集合删除
   *
   * @param ids
   */
  void deleteBatch(List<String> ids);


}
