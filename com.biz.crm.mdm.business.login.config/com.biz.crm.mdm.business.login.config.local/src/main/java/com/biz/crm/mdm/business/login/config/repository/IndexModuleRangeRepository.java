
package com.biz.crm.mdm.business.login.config.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.mdm.business.login.config.entity.IndexModuleRangeEntity;
import com.biz.crm.mdm.business.login.config.mapper.IndexModuleRangeMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-06-19 16:20
 * @description：
 */
@Component
public class IndexModuleRangeRepository extends ServiceImpl<IndexModuleRangeMapper, IndexModuleRangeEntity> {

  /**
   * 根据组件code查询当前适配范围
   * @param code
   * @return
   */
  public List<IndexModuleRangeEntity> findListByCode(String code) {
    return this.lambdaQuery()
        .eq(IndexModuleRangeEntity::getTenantCode, TenantUtils.getTenantCode())
        .eq(IndexModuleRangeEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(IndexModuleRangeEntity::getModuleCode, code)
        .list();
  }

  /**
   * 根据组件code删除当前适配范围子表内容
   * @param code
   */
  public void deleteByCode(String code){
    this.lambdaUpdate()
        .eq(IndexModuleRangeEntity::getTenantCode, TenantUtils.getTenantCode())
        .eq(IndexModuleRangeEntity::getModuleCode, code)
        .remove();
  }

}
