<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.mdm.business.login.config.mapper.ParameterManageMapper">

  <sql id="manage">
    manage.id as id,
    manage.parameter_code as parameterCode ,
    manage.parameter_name as parameterName ,
    manage.default_value as defaultValue ,
    manage.parameter_value as parameterValue,
    manage.parameter_explain as parameterExplain,
    manage.parameter_module as parameterModule,
    manage.tenant_code as tenantCode,
    manage.del_flag as delFlag,
    manage.enable_status as enableStatus,
    manage.create_account as createAccount,
    manage.create_time as createTime,
    manage.modify_account as modifyAccount,
    manage.tenant_code as tenantCode,
    manage.modify_time as modifyTime
  </sql>


  <select id="findByConditions" resultType="com.biz.crm.mdm.business.login.config.entity.ParameterManage">
    SELECT
    <include refid="manage"/>
    FROM mdm_parameter_manage manage
    <where>
    	manage.tenant_code = #{dto.tenantCode}
      <if test="dto.parameterModule != null and dto.parameterModule != ''">
        and manage.parameter_module = #{dto.parameterModule}
      </if>
      <if test="dto.parameterCode != null and dto.parameterCode != ''">
        <bind name="likeparameterCode" value="'%' + dto.parameterCode + '%'"/>
        and manage.parameter_code like #{likeparameterCode}
      </if>
      <if test="dto.parameterName != null and dto.parameterName != ''">
        <bind name="likeparameterName" value="'%' + dto.parameterName + '%'"/>
        and manage.parameter_name like #{likeparameterName}
      </if>
      <if test="dto.parameterExplain != null and dto.parameterExplain != ''">
        <bind name="likeparameterExplain" value="'%' + dto.parameterExplain + '%'"/>
        and manage.parameter_explain like #{likeparameterExplain}
      </if>
      <if test="dto.parameterValue != null and dto.parameterValue != ''">
        <bind name="likeParameterValue" value="'%' + dto.parameterValue + '%'"/>
        and manage.parameter_value like #{likeParameterValue}
      </if>
    </where>
    order by manage.parameter_code asc
  </select>

</mapper>
