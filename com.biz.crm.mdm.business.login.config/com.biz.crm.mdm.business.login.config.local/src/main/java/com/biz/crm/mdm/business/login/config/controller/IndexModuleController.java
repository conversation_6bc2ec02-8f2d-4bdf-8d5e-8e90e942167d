package com.biz.crm.mdm.business.login.config.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.login.config.dto.IndexModuleDto;
import com.biz.crm.mdm.business.login.config.service.IndexModuleService;
import com.biz.crm.mdm.business.login.config.vo.IndexModuleVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-06-19 17:06
 * @description：
 */
@Slf4j
@RestController
@RequestMapping("/v1/indexConfig/indexModule")
@Api(tags = "首页布局组件 : IndexModuleEntity : 首页布局组件")
public class IndexModuleController {

  @Autowired(required = false)
  private IndexModuleService indexModuleService;

  @ApiOperation(value = "查询分页列表")
  @GetMapping(value = {"/findByConditions"})
  public Result<Page<IndexModuleVo>> findByConditions(@PageableDefault(50) Pageable pageable, IndexModuleDto dto) {
    try {
      Page<IndexModuleVo> result = this.indexModuleService.findByConditions(pageable, dto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "创建")
  @PostMapping(value = "")
  public Result<IndexModuleVo> create(@RequestBody IndexModuleDto dto) {
    try {
      IndexModuleVo vo = this.indexModuleService.create(dto);
      return Result.ok(vo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "更新")
  @PatchMapping(value = "")
  public Result<IndexModuleVo> update(@RequestBody IndexModuleDto dto) {
    try {
      IndexModuleVo vo = this.indexModuleService.update(dto);
      return Result.ok(vo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "详情")
  @GetMapping(value = {"/findDetailById"})
  public Result<IndexModuleVo> findDetailById(@RequestParam("id") String id) {
    try {
      IndexModuleVo vo = this.indexModuleService.findDetailById(id);
      return Result.ok(vo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "启用")
  @PostMapping("/enable")
  public Result<?> enable(@RequestBody List<String> ids) {
    try {
      this.indexModuleService.enableBatch(ids);
      return Result.ok("启用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "禁用")
  @PostMapping("/disable")
  public Result<?> disable(@RequestBody List<String> ids) {
    try {
      this.indexModuleService.disableBatch(ids);
      return Result.ok("禁用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "逻辑删除")
  @DeleteMapping("/delete")
  public Result<?> delete(@RequestParam("ids") List<String> ids) {
    try {
      this.indexModuleService.deleteBatch(ids);
      return Result.ok("删除成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


}
