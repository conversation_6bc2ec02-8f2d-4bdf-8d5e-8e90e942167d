package com.biz.crm.mdm.business.login.config.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;

/**
 * <AUTHOR>
 * @date 2023-06-19 10:11
 * @description：
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "IndexModuleEntity", description = "首页布局组件")
@Entity
@TableName("mdm_index_module")
@javax.persistence.Table(
    name = "mdm_index_module",
    indexes = {
        @Index(name = "index_module_index1", columnList = "module_code"),
    })
@org.hibernate.annotations.Table(appliesTo = "mdm_index_module", comment = "首页布局组件")
public class IndexModuleEntity extends TenantFlagOpEntity {

  private static final long serialVersionUID = 1L;

  @ApiModelProperty("首页组件编码")
  @TableField(value = "module_code")
  @Column(name = "module_code", length = 100, columnDefinition = "varchar(100) COMMENT '首页组件编码'")
  private String moduleCode;

  @ApiModelProperty("首页组件名称")
  @TableField(value = "module_name")
  @Column(name = "module_name", length = 100, columnDefinition = "varchar(100) COMMENT '首页组件名称'")
  private String moduleName;

  /**
   * 是 覆盖全部范围 否 则产生范围
   */
  @ApiModelProperty("是否默认首页组件 true:是, false:否")
  @TableField(value = "default_module")
  @Column(name = "default_module", columnDefinition = "int COMMENT '是否默认首页组件 true:是, false:否'")
  private Boolean defaultModule = false;

  @ApiModelProperty("首页组件权限类别名称(别名)")
  @TableField(value = "module_type_name")
  @Column(name = "module_type_name", length = 100, columnDefinition = "varchar(100) COMMENT '首页组件权限类别(别名)'")
  private String moduleTypeName;

  @ApiModelProperty("首页组件菜单标识")
  @TableField(value = "module_menu_code")
  @Column(name = "module_menu_code", length = 255, columnDefinition = "varchar(255) COMMENT '首页组件菜单标识'")
  private String moduleMenuCode;

}
