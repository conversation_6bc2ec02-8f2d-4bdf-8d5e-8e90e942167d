package com.biz.crm.mdm.business.login.config.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.login.config.constant.IndexModuleConstant;
import com.biz.crm.mdm.business.login.config.dto.IndexLayoutDetailDto;
import com.biz.crm.mdm.business.login.config.dto.IndexLayoutDto;
import com.biz.crm.mdm.business.login.config.dto.IndexModuleRangeFilterDto;
import com.biz.crm.mdm.business.login.config.entity.IndexLayoutDetailEntity;
import com.biz.crm.mdm.business.login.config.entity.IndexLayoutEntity;
import com.biz.crm.mdm.business.login.config.entity.IndexModuleEntity;
import com.biz.crm.mdm.business.login.config.enums.IndexLayoutMatchRangeEnum;
import com.biz.crm.mdm.business.login.config.mapper.IndexModuleRangeMapper;
import com.biz.crm.mdm.business.login.config.repository.IndexLayoutDetailRepository;
import com.biz.crm.mdm.business.login.config.repository.IndexLayoutRepository;
import com.biz.crm.mdm.business.login.config.repository.IndexModuleRepository;
import com.biz.crm.mdm.business.login.config.service.IndexLayoutService;
import com.biz.crm.mdm.business.login.config.vo.IndexLayoutDetailVo;
import com.biz.crm.mdm.business.login.config.vo.IndexLayoutVo;
import com.biz.crm.mdm.business.login.config.vo.IndexModuleVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgPositionVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-06-19 16:22
 * @description：
 */
@Service("indexLayoutService")
public class IndexLayoutServiceImpl implements IndexLayoutService {

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;
  @Autowired(required = false)
  private GenerateCodeService generateCodeService;
  @Autowired(required = false)
  private LoginUserService loginUserService;
  @Autowired(required = false)
  private IndexLayoutRepository indexLayoutRepository;
  @Autowired(required = false)
  private IndexLayoutDetailRepository indexLayoutDetailRepository;
  @Autowired(required = false)
  private IndexModuleRangeMapper indexModuleRangeMapper;
  @Autowired(required = false)
  private IndexModuleRepository indexModuleRepository;
  @Autowired(required = false)
  @Lazy
  private OrgPositionVoService orgPositionVoService;
  @Autowired(required = false)
  private UserVoService userVoService;


  /**
   * 根据当前租户查询默认布局
   *
   * @return
   */
  @Override
  public IndexLayoutVo findDefaultLayoutByTenantCode() {
    String tenantCode = TenantUtils.getTenantCode();
    IndexLayoutEntity entity = this.indexLayoutRepository.findDefaultLayoutByTenantCode(tenantCode);
    //若当前租户未配置首页默认布局
    if (ObjectUtils.isEmpty(entity)) {
      entity = this.createDefaultLayout();
    }
    //查询布局内容详情
    List<IndexLayoutDetailEntity> layoutDetailEntityList = indexLayoutDetailRepository.findDetailByCode(entity.getLayoutCode());
    //组装返回数据
    IndexLayoutVo resVo = this.nebulaToolkitService.copyObjectByWhiteList(entity, IndexLayoutVo.class, HashSet.class, ArrayList.class);
    List<IndexLayoutDetailVo> indexLayoutDetailVoList = new ArrayList<>();
    if (ObjectUtils.isNotEmpty(layoutDetailEntityList)) {
      indexLayoutDetailVoList = (List<IndexLayoutDetailVo>) this.nebulaToolkitService.copyCollectionByWhiteList(layoutDetailEntityList, IndexLayoutDetailEntity.class, IndexLayoutDetailVo.class, HashSet.class, ArrayList.class);
      //根据当前用户判断是否可见组件
      indexLayoutDetailVoList = this.validateLayoutDetail(indexLayoutDetailVoList);
    }
    resVo.setIndexLayoutDetailVoList(indexLayoutDetailVoList);
    return resVo;
  }

  /**
   * 根据当前租户+账号查询默认布局
   *
   * @return
   */
  @Override
  public IndexLayoutVo findLayoutByCurrentAccount() {
    String account = this.loginUserService.getLoginAccountName();
    IndexLayoutVo resVo = this.findLayoutByAccount(account);
    if (ObjectUtils.isEmpty(resVo)) {
      resVo = this.findDefaultLayoutByTenantCode();
    }
    return resVo;
  }


  /**
   * 根据用户自定义的的首页布局信息
   *
   * @return
   */
  private IndexLayoutVo findLayoutByAccount(String account) {
    String tenantCode = TenantUtils.getTenantCode();
    IndexLayoutEntity entity = this.indexLayoutRepository.findLayoutByCurrentAccount(tenantCode, account);
    //若当前租户未配置首页默认布局
    if (ObjectUtils.isEmpty(entity)) {
      return null;
    }
    //查询布局内容详情
    List<IndexLayoutDetailEntity> layoutDetailEntityList = indexLayoutDetailRepository.findDetailByCode(entity.getLayoutCode());
    //组装返回数据
    IndexLayoutVo resVo = this.nebulaToolkitService.copyObjectByWhiteList(entity, IndexLayoutVo.class, HashSet.class, ArrayList.class);
    List<IndexLayoutDetailVo> indexLayoutDetailVoList = new ArrayList<>();
    if (ObjectUtils.isNotEmpty(layoutDetailEntityList)) {
      indexLayoutDetailVoList = (List<IndexLayoutDetailVo>) this.nebulaToolkitService.copyCollectionByWhiteList(layoutDetailEntityList, IndexLayoutDetailEntity.class, IndexLayoutDetailVo.class, HashSet.class, ArrayList.class);
      //根据当前用户判断是否可见组件
      indexLayoutDetailVoList = this.validateLayoutDetail(indexLayoutDetailVoList);
    }
    resVo.setIndexLayoutDetailVoList(indexLayoutDetailVoList);
    return resVo;
  }

  /**
   * 新增默认布局
   *
   * @return
   */
  public IndexLayoutEntity createDefaultLayout() {
    String tenantCode = TenantUtils.getTenantCode();
    IndexLayoutEntity entity = new IndexLayoutEntity();
    entity.setDefaultLayout(true);
    entity.setTenantCode(tenantCode);
    entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    //布局风格编码 默认设置为0
    entity.setLayoutType("0");
    entity.setLayoutMatchRange(IndexLayoutMatchRangeEnum.MATCH_ALL_ACCOUNT.getKey());
    entity.setLayoutCode(this.createRuleCode());
    indexLayoutRepository.save(entity);
    return entity;
  }

  /**
   * 修改租户下默认主题布局
   *
   * @param dto
   * @return
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public IndexLayoutVo updateDefaultLayout(IndexLayoutDto dto) {
    //校验数据
    this.validateData(dto);
    String tenantCode = TenantUtils.getTenantCode();
    IndexLayoutEntity entity = this.nebulaToolkitService.copyObjectByWhiteList(dto, IndexLayoutEntity.class, HashSet.class, ArrayList.class);
    entity.setDefaultLayout(true);
//    entity.setLayoutAccount(this.loginUserService.getLoginAccountName());
    entity.setTenantCode(tenantCode);
    entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    /**
     *  判断当前主题覆盖的范围
     *  1、适配所有账号
     *    删除当前租户下除了默认布局外的数据
     *    删除子表组件数据
     *    更新数据
     *  2、仅对新账号生效
     *    仅更新数据 不删除其他用户布局数据
     */
    if (IndexLayoutMatchRangeEnum.MATCH_ALL_ACCOUNT.getKey().equals(entity.getLayoutMatchRange())) {
      //删除当前租户下其他用户的子表内容
      indexLayoutDetailRepository.deleteOtherLayout(tenantCode);
      //删除当前租户下其他用户的主表内容
      indexLayoutRepository.deleteOtherLayout(tenantCode);
    }
    //保存子表内容
    List<IndexLayoutDetailEntity> layoutDetailEntityList = (List<IndexLayoutDetailEntity>) this.nebulaToolkitService.copyCollectionByWhiteList(dto.getIndexLayoutDetailVoList(), IndexLayoutDetailDto.class, IndexLayoutDetailEntity.class, HashSet.class, ArrayList.class);
    layoutDetailEntityList.forEach(k -> {
      k.setLayoutCode(entity.getLayoutCode());
      k.setDefaultLayout(true);
      k.setTenantCode(tenantCode);
      k.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
      k.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    });
    //删除旧数据
    indexLayoutDetailRepository.lambdaUpdate()
        .eq(IndexLayoutDetailEntity::getTenantCode, tenantCode)
        .eq(IndexLayoutDetailEntity::getDefaultLayout, true)
        .eq(IndexLayoutDetailEntity::getLayoutCode, entity.getLayoutCode())
        .remove();
    indexLayoutRepository.saveOrUpdate(entity);
    indexLayoutDetailRepository.saveOrUpdateBatch(layoutDetailEntityList);

    IndexLayoutVo resVo = this.nebulaToolkitService.copyObjectByWhiteList(entity, IndexLayoutVo.class, HashSet.class, ArrayList.class);
    List<IndexLayoutDetailVo> indexLayoutDetailVoList = new ArrayList<>();
    if (ObjectUtils.isNotEmpty(layoutDetailEntityList)) {
      indexLayoutDetailVoList = (List<IndexLayoutDetailVo>) this.nebulaToolkitService.copyCollectionByWhiteList(layoutDetailEntityList, IndexLayoutDetailEntity.class, IndexLayoutDetailVo.class, HashSet.class, ArrayList.class);
      //根据当前用户判断是否可见组件
      indexLayoutDetailVoList = this.validateLayoutDetail(indexLayoutDetailVoList);
    }
    resVo.setIndexLayoutDetailVoList(indexLayoutDetailVoList);
    return resVo;
  }

  /**
   * 首页布局隐藏组件
   *
   * @param id
   * @return
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public IndexLayoutVo deleteModuleOnLayoutById(String id) {
    Validate.isTrue(StringUtils.isNotEmpty(id), "请选择要隐藏的组件!");
    String tenantCode = TenantUtils.getTenantCode();
    /**
     * 判断是不是默认组件
     *  是 则新增一套用户的
     *     删除指定的组件
     *  否 直接删除
     */
    IndexLayoutDetailEntity layoutDetailEntity = indexLayoutDetailRepository.getById(id);
    Validate.isTrue(ObjectUtils.isNotEmpty(layoutDetailEntity), "当前布局组件不存在!");
    //判断是否默认组件
    if (layoutDetailEntity.getDefaultLayout()) {
      //copy默认布局到自定义的布局
      IndexLayoutEntity layoutEntity = this.createByDefined();
      //查询到新布局的对应的id
      IndexLayoutDetailEntity tempDetailEntity = indexLayoutDetailRepository.lambdaQuery()
          .eq(IndexLayoutDetailEntity::getTenantCode, tenantCode)
          .eq(IndexLayoutDetailEntity::getDefaultLayout, false)
          .eq(IndexLayoutDetailEntity::getLayoutCode, layoutEntity.getLayoutCode())
          .eq(IndexLayoutDetailEntity::getModuleCode, layoutDetailEntity.getModuleCode())
          .eq(IndexLayoutDetailEntity::getSort, layoutDetailEntity.getSort())
          .one();
      id = tempDetailEntity.getId();
    }
    this.indexLayoutDetailRepository.deleteModuleOnLayoutById(tenantCode, id);
    return this.findLayoutByCurrentAccount();
  }

  /**
   * 修改租户下用户自定义主题布局
   *
   * @param dto
   * @return
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public IndexLayoutVo updateDefinedLayout(IndexLayoutDto dto) {
    String tenantCode = TenantUtils.getTenantCode();
    String currentAccount = this.loginUserService.getLoginAccountName();
    //当前租户下的默认布局
    IndexLayoutEntity entity = this.indexLayoutRepository.findDefaultLayoutByTenantCode(tenantCode);
    //查询当前布局数据
    IndexLayoutEntity oldLayout = this.indexLayoutRepository.findLayoutByCurrentAccount(tenantCode, currentAccount);
    //如果当前请求参数是默认布局 则新增一套编码，如果不是 则正常更新
    IndexLayoutEntity layoutEntity = new IndexLayoutEntity();
    layoutEntity.setId(dto.getId());
    String indexLayoutCode = dto.getLayoutCode();
    if (dto.getLayoutCode().equals(entity.getLayoutCode())) {
      indexLayoutCode = this.createRuleCode();
      layoutEntity.setId(null);
    }
    layoutEntity.setLayoutCode(indexLayoutCode);
    layoutEntity.setLayoutAccount(currentAccount);
    layoutEntity.setDefaultLayout(false);
    layoutEntity.setLayoutType(dto.getLayoutType());
    layoutEntity.setLayoutMatchRange(null);
    layoutEntity.setTenantCode(tenantCode);
    layoutEntity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    layoutEntity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    //当布局更换时
    if (ObjectUtils.isNotEmpty(oldLayout) && !oldLayout.getLayoutType().equals(dto.getLayoutType())) {
      indexLayoutDetailRepository.deleteLayoutDetailByCode(tenantCode, oldLayout.getLayoutCode());
    }
    //布局组件内容
    if (ObjectUtils.isNotEmpty(dto.getIndexLayoutDetailVoList())) {
      List<IndexLayoutDetailEntity> layoutDetailEntityList = (List<IndexLayoutDetailEntity>) this.nebulaToolkitService.copyCollectionByWhiteList(dto.getIndexLayoutDetailVoList(), IndexLayoutDetailDto.class, IndexLayoutDetailEntity.class, HashSet.class, ArrayList.class);
      layoutDetailEntityList.forEach(k -> {
        k.setLayoutCode(layoutEntity.getLayoutCode());
        k.setDefaultLayout(false);
        k.setTenantCode(tenantCode);
        k.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        k.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
      });
      //修改子表数据
      indexLayoutDetailRepository.saveOrUpdateBatch(layoutDetailEntityList);
    }
    indexLayoutRepository.saveOrUpdate(layoutEntity);
    return this.findLayoutByCurrentAccount();
  }


  /**
   * 首页布局编辑 当前用户可用的组件
   *
   * @return
   */
  @Override
  public List<IndexModuleVo> findModuleByInRange() {
    String tenantCode = TenantUtils.getTenantCode();
    String userCode = "";
    String positionCode = "";
    List<String> roleCodes = Lists.newArrayList();
    AbstractCrmUserIdentity abstractLoginUser = this.loginUserService.getAbstractLoginUser();
    if (Objects.nonNull(abstractLoginUser)) {
      //找到当前用户的企业人员
      UserVo userVo = this.userVoService.findByUserName(abstractLoginUser.getUsername());
      if (Objects.nonNull(userVo)) {
        userCode = userVo.getUserCode();
        //找到当前用户登陆使用的职位
      }
      positionCode = abstractLoginUser.getPostCode();
      roleCodes.addAll(Arrays.asList(abstractLoginUser.getRoleCodes()));
    }

    //找到当前用户的组织
    OrgVo orgVo = this.orgPositionVoService.findByPositionCode(positionCode);
    String orgCode = ObjectUtils.isNotEmpty(orgVo) ? orgVo.getOrgCode() : "";
    IndexModuleRangeFilterDto dto = new IndexModuleRangeFilterDto();
    dto.setTenantCode(TenantUtils.getTenantCode());
    dto.setPositionCode(positionCode);
    dto.setUserCode(userCode);
    dto.setRoleCodes(roleCodes);
    dto.setOrgCode(orgCode);

    List<IndexModuleEntity> resList = new ArrayList<>();
    //查询默认组件
    List<IndexModuleEntity> allRangeList = indexModuleRepository.lambdaQuery()
        .eq(IndexModuleEntity::getTenantCode, tenantCode)
        .eq(IndexModuleEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(IndexModuleEntity::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
        .eq(IndexModuleEntity::getDefaultModule, true)
        .list();
    if (ObjectUtils.isNotEmpty(allRangeList)) {
      resList.addAll(allRangeList);
    }
    //携带适配范围的组件
    List<IndexModuleEntity> inRangeList = indexModuleRangeMapper.findModuleByInRange(dto);
    if (ObjectUtils.isNotEmpty(inRangeList)) {
      resList.addAll(inRangeList);
    }
    return (List<IndexModuleVo>) this.nebulaToolkitService.copyCollectionByWhiteList(resList, IndexModuleEntity.class, IndexModuleVo.class, HashSet.class, ArrayList.class);
  }


  /**
   * 当前用户使用默认布局(不存在未自定义布局时) 当隐藏、编辑、调整布局时 首先把默认布局copy成自定义的
   */
  private IndexLayoutEntity createByDefined() {
    IndexLayoutVo layoutVo = this.findDefaultLayoutByTenantCode();
    //生成布局编码
    String indexLayoutCode = this.createRuleCode();
    //布局主表
    IndexLayoutEntity layoutEntity = this.nebulaToolkitService.copyObjectByWhiteList(layoutVo, IndexLayoutEntity.class, HashSet.class, ArrayList.class);
    layoutEntity.setLayoutCode(indexLayoutCode);
    layoutEntity.setDefaultLayout(false);
    layoutEntity.setId(null);
    layoutEntity.setLayoutAccount(this.loginUserService.getLoginAccountName());
    //布局组件内容
    if (ObjectUtils.isNotEmpty(layoutVo.getIndexLayoutDetailVoList())) {
      List<IndexLayoutDetailEntity> layoutDetailEntity = (List<IndexLayoutDetailEntity>) this.nebulaToolkitService.copyCollectionByWhiteList(layoutVo.getIndexLayoutDetailVoList(), IndexLayoutDetailVo.class, IndexLayoutDetailEntity.class, HashSet.class, ArrayList.class);
      layoutDetailEntity.forEach(k -> {
        k.setId(null);
        k.setLayoutCode(indexLayoutCode);
        k.setDefaultLayout(false);
      });
      //新增子表数据
      indexLayoutDetailRepository.saveBatch(layoutDetailEntity);
    }
    //保存主表数据
    indexLayoutRepository.save(layoutEntity);
    return layoutEntity;
  }

  private String createRuleCode() {
    String indexLayoutCode = this.generateCodeService.generateCode(IndexModuleConstant.INDEX_LAYOUT_CODE);
    return indexLayoutCode;
  }

  /**
   * 首页布局查询后 判断内部组件对当前用户是否可见
   *
   * @param list
   * @return
   */
  private List<IndexLayoutDetailVo> validateLayoutDetail(List<IndexLayoutDetailVo> list) {
    //获取当前组件编码
    List<String> moduleCodes = list.stream().map(IndexLayoutDetailVo::getModuleCode).collect(Collectors.toList());
    String userCode = "";
    String positionCode = "";
    List<String> roleCodes = Lists.newArrayList();
    AbstractCrmUserIdentity abstractLoginUser = this.loginUserService.getAbstractLoginUser();
    if (Objects.nonNull(abstractLoginUser)) {
      //找到当前用户的企业人员
      UserVo userVo = this.userVoService.findByUserName(abstractLoginUser.getUsername());
      if (Objects.nonNull(userVo)) {
        userCode = userVo.getUserCode();
        //找到当前用户登陆使用的职位
      }
      positionCode = abstractLoginUser.getPostCode();
      roleCodes.addAll(Arrays.asList(abstractLoginUser.getRoleCodes()));
    }
    //找到当前用户的组织
    OrgVo orgVo = this.orgPositionVoService.findByPositionCode(positionCode);
    String orgCode = ObjectUtils.isNotEmpty(orgVo) ? orgVo.getOrgCode() : "";
    IndexModuleRangeFilterDto dto = new IndexModuleRangeFilterDto();
    dto.setTenantCode(TenantUtils.getTenantCode());
    dto.setPositionCode(positionCode);
    dto.setUserCode(userCode);
    dto.setRoleCodes(roleCodes);
    dto.setOrgCode(orgCode);
    dto.setModuleCodes(moduleCodes);
    //获取过滤后可见的组件信息 转成Set
    Set<String> filterModuleSet = indexModuleRangeMapper.filterModuleByRange(dto).stream().collect(Collectors.toSet());
    filterModuleSet = Optional.ofNullable(filterModuleSet).orElseGet(HashSet::new);
    //将默认组件进行搜索
    List<IndexModuleEntity> defaultModuleList = this.indexModuleRepository.findDefaultModule();
    if (ObjectUtils.isNotEmpty(defaultModuleList)) {
      for (IndexModuleEntity k : defaultModuleList) {
        filterModuleSet.add(k.getModuleCode());
      }
    }
    for (IndexLayoutDetailVo item : list) {
      String moduleCode = item.getModuleCode();
      //根据组件编码查询组件详细信息
      IndexModuleEntity entity = indexModuleRepository.findModuleByCode(moduleCode);
      item.setShowFlag(false);
      if (ObjectUtils.isNotEmpty(entity)) {
        //补充完整组件信息
        item.setModuleName(entity.getModuleName());
        item.setModuleMenuCode(entity.getModuleMenuCode());
      }
      if (filterModuleSet.contains(moduleCode)) {
        item.setShowFlag(true);
      }
    }
    return list;
  }

  /**
   * 校验布局数据
   *
   * @param dto
   */
  private void validateData(IndexLayoutDto dto) {
    Validate.isTrue(StringUtils.isNotEmpty(dto.getLayoutType()), "请选择布局风格!");
    Validate.isTrue(StringUtils.isNotEmpty(dto.getLayoutMatchRange()), "请选择默认布局适配范围!");
    Validate.isTrue(StringUtils.isNotEmpty(dto.getId()), "请勿确认默认布局模板id");
    Validate.isTrue(StringUtils.isNotEmpty(dto.getLayoutCode()), "请勿确认默认布局模板编码");
    IndexLayoutVo layoutVo = this.findDefaultLayoutByTenantCode();
    //判断id 和布局模板编码是否被修改了
    Validate.isTrue(ObjectUtils.isNotEmpty(layoutVo), "当前租户系统下不存在首页默认布局信息");
    Validate.isTrue(dto.getId().equals(layoutVo.getId()), "请勿修改默认布局模板id");
    Validate.isTrue(dto.getLayoutCode().equals(layoutVo.getLayoutCode()), "请勿修改默认布局模板编码");
  }

}
