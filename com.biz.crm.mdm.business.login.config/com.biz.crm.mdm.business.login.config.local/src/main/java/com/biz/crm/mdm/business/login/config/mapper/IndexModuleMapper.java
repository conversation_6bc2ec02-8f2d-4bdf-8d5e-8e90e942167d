package com.biz.crm.mdm.business.login.config.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.login.config.dto.IndexConfigDto;
import com.biz.crm.mdm.business.login.config.dto.IndexModuleDto;
import com.biz.crm.mdm.business.login.config.entity.IndexConfig;
import com.biz.crm.mdm.business.login.config.entity.IndexModuleEntity;
import com.biz.crm.mdm.business.login.config.vo.IndexModuleVo;
import org.apache.ibatis.annotations.Param;
import org.elasticsearch.index.IndexModule;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023-06-19 16:20
 * @description：
 */
public interface IndexModuleMapper extends BaseMapper<IndexModuleEntity> {

  /**
   * 首页布局组件分页查询
   * @param page
   * @param dto
   * @return
   */
  Page<IndexModuleVo> findByConditions(Page<IndexModuleVo> page, @Param("dto") IndexModuleDto dto);


}
