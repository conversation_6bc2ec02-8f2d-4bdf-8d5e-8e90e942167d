package com.biz.crm.mdm.business.login.config.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.login.config.dto.IndexConfigDto;
import com.biz.crm.mdm.business.login.config.entity.IndexConfig;
import com.biz.crm.mdm.business.login.config.service.IndexConfigService;

import com.biz.crm.mdm.business.login.config.vo.IndexConfigVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 * 系统首页配置
 */
@Api(value = "系统首页配置", tags = "系统首页配置")
@RestController
@RequestMapping("/v1/indexConfig/indexConfig")
@Slf4j
public class IndexConfigController {
  @Autowired(required = false)
  private IndexConfigService indexConfigService;

  /**
   * 列表
   */
  @ApiOperation(value = "分页查询列表")
  @GetMapping("/findByConditions")
  public Result<Page<IndexConfig>> findByConditions(@PageableDefault(50) Pageable pageable,
                                                    IndexConfigDto dto) {
    return Result.ok(indexConfigService.findByConditions(pageable, dto));
  }

  /**
   * 批量删除
   */
  @ApiOperation(value = "批量删除")
  @DeleteMapping("/deleteBatch")
  public Result<?> delete(@RequestParam List<String> ids) {
    indexConfigService.deleteBatch(ids);
    return Result.ok("删除成功");
  }

  /**
   * 批量启用
   */

  @ApiOperation(value = "批量启用")
  @PatchMapping("/enableBatch")
  public Result<?> enable(@RequestBody List<String> ids) {
    indexConfigService.enableBatch(ids);
    return Result.ok("启用成功");
  }

  /**
   * 批量禁用
   */
  @ApiOperation(value = "批量禁用")
  @PatchMapping("/disableBatch")
  public Result<?> disable(@RequestBody List<String> ids) {
    indexConfigService.disableBatch(ids);
    return Result.ok("禁用成功");
  }


  @ApiOperation(value = "根据主键id查询")
  @GetMapping("/findById")
  public Result<IndexConfig> findById(@RequestParam String id) {
    return Result.ok(this.indexConfigService.findById(id));
  }

  /**
   * 新增
   */
  @ApiOperation(value = "新增")
  @PostMapping("")
  public Result<IndexConfig> create(@RequestBody IndexConfig indexConfig) {
    try {
      return Result.ok(this.indexConfigService.create(indexConfig));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 更新
   */
  @ApiOperation(value = "更新")
  @PatchMapping("")
  public Result<IndexConfig> update(@RequestBody IndexConfig indexConfig) {
    try {
      return Result.ok(this.indexConfigService.update(indexConfig));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据租户查询当前登录页配置
   *
   * @param dto
   * @return
   */
  @ApiOperation(value = "根据租户查询当前登录页配置")
  @PostMapping("/findThemeByTenantCode")
  public Result<IndexConfigVo> findThemeByTenantCode(@RequestBody IndexConfigDto dto) {
    try {
      return Result.ok(this.indexConfigService.findThemeByTenantCode(dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

}
