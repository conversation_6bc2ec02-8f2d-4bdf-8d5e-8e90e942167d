package com.biz.crm.mdm.business.login.config.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.login.config.dto.ParameterManageDto;
import com.biz.crm.mdm.business.login.config.entity.ParameterManage;
import com.biz.crm.mdm.business.login.config.service.ParameterManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 参数配置
 *
 * <AUTHOR>
 * @date 2020-11-24 10:47:10
 */
@RestController
@RequestMapping("/v1/parametermanage/parametermanage")
@Api(tags = "全局参数配置")
public class ParameterManageController {

  @Autowired(required = false)
  private ParameterManageService parameterManageService;

  /**
   * 列表
   */
  @ApiOperation(value = "分页查询列表")
  @GetMapping("/findByConditions")
  public Result<Page<ParameterManage>> list(@PageableDefault(50) Pageable pageable,
                                            ParameterManageDto dto) {
    return Result.ok(parameterManageService.findByConditions(pageable, dto));
  }

  @ApiOperation(value = "新增")
  @PostMapping("")
  public Result<ParameterManage> create(@RequestBody ParameterManage parameterManage) {
    return Result.ok(parameterManageService.create(parameterManage));
  }

  @ApiOperation(value = "更新")
  @PatchMapping("")
  public Result<ParameterManage> update(@RequestBody ParameterManage parameterManage) {
    return Result.ok(parameterManageService.update(parameterManage));
  }

  /**
   * 批量删除
   */
  @ApiOperation(value = "批量删除")
  @DeleteMapping("/deleteBatch")
  public Result<?> delete(@RequestParam List<String> ids) {
    parameterManageService.deleteBatch(ids);
    return Result.ok("删除成功");
  }

}
