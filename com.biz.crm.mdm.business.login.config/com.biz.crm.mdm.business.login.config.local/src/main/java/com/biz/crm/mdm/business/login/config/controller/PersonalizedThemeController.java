package com.biz.crm.mdm.business.login.config.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.login.config.dto.PersonalizedThemeDto;
import com.biz.crm.mdm.business.login.config.service.PersonalizedThemeService;
import com.biz.crm.mdm.business.login.config.vo.PersonalizedThemeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * personalized_theme 个性化主题表控制层
 *
 * <AUTHOR>
 * @date 2023/2/13 14:29
 * @description 个性化主题模块api
 */

@Slf4j
@RestController
@RequestMapping("/v1/indexConfig/personalizedTheme")
@Api(tags = "个性化主题 : PersonalizedTheme : 个性化主题")
public class PersonalizedThemeController {
  @Autowired(required = false)
  private PersonalizedThemeService personalizedThemeService;

  /**
   * 根据租户获取主体配置信息
   *
   * @param dto
   * @return
   */
  @ApiOperation(value = "根据用户名称获取主题配置信息")
  @GetMapping(value = {"/findThemeByTenantCode"})
  public Result<List<PersonalizedThemeVo>> findThemeByTenantCode(PersonalizedThemeDto dto) {
    try {
      List<PersonalizedThemeVo> result = this.personalizedThemeService.findThemeByTenantCode(dto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 创建
   *
   * @param dto
   * @return
   */
  @ApiOperation(value = "创建")
  @PostMapping(value = "")
  public Result<PersonalizedThemeVo> create(@RequestBody PersonalizedThemeDto dto) {
    try {
      PersonalizedThemeVo current = this.personalizedThemeService.create(dto);
      return Result.ok(current);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 更新
   *
   * @param dto
   * @return
   */
  @ApiOperation(value = "更新")
  @PatchMapping(value = "")
  public Result<PersonalizedThemeVo> update(@RequestBody PersonalizedThemeDto dto) {
    try {
      PersonalizedThemeVo current = this.personalizedThemeService.update(dto);
      return Result.ok(current);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 详情
   *
   * @param id
   * @return
   */
  @ApiOperation(value="详情")
  @GetMapping("/findDetailById")
  public Result<PersonalizedThemeVo> findDetailById(@RequestParam String id){
    try {
      PersonalizedThemeVo vo  = personalizedThemeService.findDetailById(id);
      return Result.ok(vo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 逻辑删除
   *
   * @param ids
   * @return
   */
  @ApiOperation(value = "逻辑删除")
  @DeleteMapping("/delete")
  public Result<?> delete(@RequestBody List<String> ids) {
    try {
      this.personalizedThemeService.updateDelFlagByIds(ids);
      return Result.ok("删除成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

}
