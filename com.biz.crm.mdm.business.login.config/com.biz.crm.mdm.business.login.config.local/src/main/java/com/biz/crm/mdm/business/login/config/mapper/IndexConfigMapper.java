package com.biz.crm.mdm.business.login.config.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.login.config.dto.IndexConfigDto;
import com.biz.crm.mdm.business.login.config.entity.IndexConfig;
import org.apache.ibatis.annotations.Param;

/**
 * 登录配置 的 mybatis-plus接口类 {@link IndexConfig}
 *
 * <AUTHOR>
 */
public interface IndexConfigMapper extends BaseMapper<IndexConfig> {

  /**
   * 分页列表
   *
   * @param page 分页信息
   * @param dto  分页参数dto
   * @return 分页列表
   */
  Page<IndexConfig> findByConditions(Page<IndexConfig> page, @Param("dto") IndexConfigDto dto);
  
}
