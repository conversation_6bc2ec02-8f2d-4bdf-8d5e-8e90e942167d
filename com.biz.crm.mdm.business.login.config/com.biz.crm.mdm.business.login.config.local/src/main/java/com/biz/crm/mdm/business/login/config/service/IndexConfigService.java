package com.biz.crm.mdm.business.login.config.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.login.config.dto.IndexConfigDto;
import com.biz.crm.mdm.business.login.config.entity.IndexConfig;
import com.biz.crm.mdm.business.login.config.vo.IndexConfigVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * 登录页配置service
 */
public interface IndexConfigService  {
    /**
     * 条件分页查询
     * @param pageable 分页参数
     * @param vo 分页查询条件
     * @return 分页数据
     */
    Page<IndexConfig> findByConditions(Pageable pageable, IndexConfigDto vo);

    /**
     * 批量启用
     * @param ids 主键列表
     */
    void enableBatch(List<String> ids);

    /**
     * 批量禁用
     * @param ids 主键列表
     */
    void disableBatch(List<String> ids);

    /**
     * 批量删除
     * @param ids 主键列表
     */
    void deleteBatch(List<String> ids);

    /**
     * 详情
     * @param id 主键id
     * @return 配置实体
     */
    IndexConfig findById(String id);

    /**
     * 新建配置
     * @param indexConfig 配置实体
     * @return 新建后的实体类
     */
    IndexConfig create(IndexConfig indexConfig);

    /**
     * 更新配置
     * @param indexConfig 配置实体
     * @return 更新后的实体类
     */
    IndexConfig update(IndexConfig indexConfig);

    /**
     * 根据租户获取当前配置项
     * @return
     */
    IndexConfigVo findThemeByTenantCode(IndexConfigDto dto);

    /**
     * 查询当前主题配置是否在使用
     * @param code
     * @return
     */
    int findIndexByThemeCode(String code);
}
