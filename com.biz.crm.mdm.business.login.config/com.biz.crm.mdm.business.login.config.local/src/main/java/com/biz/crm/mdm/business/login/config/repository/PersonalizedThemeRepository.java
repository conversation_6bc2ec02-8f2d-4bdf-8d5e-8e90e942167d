package com.biz.crm.mdm.business.login.config.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.login.config.dto.PersonalizedThemeDto;
import com.biz.crm.mdm.business.login.config.entity.PersonalizedTheme;
import com.biz.crm.mdm.business.login.config.mapper.PersonalizedThemeMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/13 14:55
 * @description 个性化主题表（repository）
 */
@Component
public class PersonalizedThemeRepository extends ServiceImpl<PersonalizedThemeMapper, PersonalizedTheme> {

  /**
   * 查询默认主题配色_所有租户共用
   * @param dto
   * @return
   */
  public List<PersonalizedTheme> findDefaultTheme(PersonalizedThemeDto dto) {
    List<PersonalizedTheme> list = this.lambdaQuery()
            .eq(PersonalizedTheme::getDelFlag, dto.getDelFlag())
            .eq(PersonalizedTheme::getEnableStatus, dto.getEnableStatus())
            .eq(PersonalizedTheme::getDefaultTheme, true)
            .list();
    return list;
  }

  /**
   * 查询当前用户配色
   * @param dto
   * @return
   */
  public List<PersonalizedTheme> findByTenantCode(PersonalizedThemeDto dto) {
    List<PersonalizedTheme> list = this.lambdaQuery()
            .eq(PersonalizedTheme::getDelFlag, dto.getDelFlag())
            .eq(PersonalizedTheme::getEnableStatus, dto.getEnableStatus())
            .eq(PersonalizedTheme::getTenantCode, dto.getTenantCode())
            .eq(PersonalizedTheme::getDefaultTheme, false)
            .list();
    return list;
  }

  /**
   * 根据主题编码计数
   * (同一租户下主题编码唯一)
   * @return
   * @param themeCode
   */
  public Integer CountByThemeCode(String themeCode) {
   return this.lambdaQuery()
        .eq(PersonalizedTheme::getThemeCode,themeCode)
        .eq(PersonalizedTheme::getTenantCode,TenantUtils.getTenantCode())
        .eq(PersonalizedTheme::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(PersonalizedTheme::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
        .count();
  }

  /**
   * 根据id和租户编号查询主题数据
   * @param id
   * @param tenantCode
   * @return
   */
  public PersonalizedTheme findById(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(PersonalizedTheme::getTenantCode,tenantCode)
        .in(PersonalizedTheme::getId,id)
        .one();
  }

  /**
   * 逻辑删除
   * @param ids
   */
  public void updateBatchByIds(List<String> ids) {
    this.lambdaUpdate()
        .eq(PersonalizedTheme::getTenantCode,TenantUtils.getTenantCode())
        .in(PersonalizedTheme::getId,ids)
        .set(PersonalizedTheme::getDelFlag, DelFlagStatusEnum.DELETE)
        .update();
  }

  /**
   * 根据code和租户编号查询主题数据
   * @param code
   * @param tenantCode
   * @return
   */
  public PersonalizedTheme findByCode(String code, String tenantCode) {
    //查询默认配置是否存在
    PersonalizedTheme entity = new PersonalizedTheme();
    entity = this.lambdaQuery()
            .eq(PersonalizedTheme::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(PersonalizedTheme::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
            .eq(PersonalizedTheme::getDefaultTheme, true)
            .eq(PersonalizedTheme::getThemeCode,code)
            .one();
    //默认配置不存在时 去找当前租户下的配置
    if(ObjectUtils.isEmpty(entity)){
      entity = this.lambdaQuery()
              .eq(PersonalizedTheme::getTenantCode, tenantCode)
              .eq(PersonalizedTheme::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
              .eq(PersonalizedTheme::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
              .eq(PersonalizedTheme::getDefaultTheme, false)
              .eq(PersonalizedTheme::getThemeCode,code)
              .one();
    }
    return entity;
  }

  /**
   * 根据id集合批量查询
   * @param ids
   */
  public List<PersonalizedTheme> findThemesByIds(List<String> ids) {
   return this.lambdaQuery()
        .eq(PersonalizedTheme::getTenantCode,TenantUtils.getTenantCode())
        .in(PersonalizedTheme::getId,ids)
        .list();
  }
}
