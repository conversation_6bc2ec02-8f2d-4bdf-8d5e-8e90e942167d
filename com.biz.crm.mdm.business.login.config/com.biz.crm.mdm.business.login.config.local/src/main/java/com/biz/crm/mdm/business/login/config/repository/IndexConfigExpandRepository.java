package com.biz.crm.mdm.business.login.config.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.login.config.entity.IndexConfigExpand;
import com.biz.crm.mdm.business.login.config.mapper.IndexConfigExpandMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 登录配置扩展类 的 数据库访问类 {@link IndexConfigExpand}
 *
 * <AUTHOR>
 */
@Component
public class IndexConfigExpandRepository extends ServiceImpl<IndexConfigExpandMapper, IndexConfigExpand> {

  /**
   * 根据config_id 删除扩展配置
   * @param configId 主配置id
   */
  public void deleteExpandByConfId(String configId) {
    QueryWrapper<IndexConfigExpand> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("config_id", configId);
    queryWrapper.eq("tenant_code", TenantUtils.getTenantCode());
    this.baseMapper.delete(queryWrapper);
  }

  public List<IndexConfigExpand> findDetailByIds(List<String> ids,String tenantCode) {
    return this.lambdaQuery()
        .in(IndexConfigExpand::getConfigId,ids)
        .eq(IndexConfigExpand::getTenantCode,tenantCode)
        .list();
  }
}
