package com.biz.crm.mdm.business.table.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 字段配置实体类
 *
 * <AUTHOR>
 * @date 2020-11-21 12:19:06
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MdmColumnConfigEntity", description = "字段配置实体类")
@Entity
@org.hibernate.annotations.Table(appliesTo = "`mdm_column_config`", comment = "字段配置实体类")
@TableName("mdm_column_config")
@Table(name = "`mdm_column_config`", indexes = @Index(name = "uk_parent_code_function_code_field", columnList = "parent_code,function_code,field", unique = true))
public class MdmColumnConfigEntity extends UuidFlagOpEntity {
  private static final long serialVersionUID = -9146852714551743935L;

  /**
   * 菜单编码
   */
  @ApiModelProperty("菜单编码")
  @Column(name = "parent_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 菜单编码 '")
  private String parentCode;

  /**
   * 是否有不可编辑部分，1是0否
   * - 给前端用的，比如：来自数据试图的字段，不能改名称
   */
  @ApiModelProperty("是否有不可编辑部分，1是0否")
  @Column(name = "is_limited", nullable = true, length = 1, columnDefinition = "char(1) COMMENT ' 是否有不可编辑部分，1是0否 '")
  private Boolean isLimited;

  /**
   * 是否导出，1是0否
   */
  @ApiModelProperty("是否导出，1是0否")
  @Column(name = "column_export", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 是否导出，1是0否 '")
  private String columnExport;

  /**
   * 功能编码
   */
  @ApiModelProperty("功能编码")
  @Column(name = "function_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 功能编码 '")
  private String functionCode;

  /**
   * 属性--英文属性
   */
  @ApiModelProperty("属性--英文属性")
  @Column(name = "field", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 属性--英文属性 '")
  private String field;

  /**
   * 标签--汉字标题
   */
  @ApiModelProperty("标签--汉字标题")
  @Column(name = "title", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 标签--汉字标题 '")
  private String title;

  /**
   * 显示模式：1显示，0隐藏
   */
  @ApiModelProperty("显示模式：1显示，0隐藏")
  @Column(name = "visible", nullable = true, length = 1, columnDefinition = "char(1) COMMENT ' 显示模式：1显示，0隐藏 '")
  private Boolean visible;

  /**
   * 新增页面时候编辑状态
   */
  @ApiModelProperty("新增页面时候编辑状态")
  @Column(name = "editable_in_create", nullable = true, length = 8, columnDefinition = "VARCHAR(8) COMMENT ' 新增页面时候编辑状态 '")
  private String editableInCreate;

  /**
   * 编辑页面编辑状态
   */
  @ApiModelProperty("编辑页面编辑状态")
  @Column(name = "editable_in_edit", nullable = true, length = 8, columnDefinition = "VARCHAR(8) COMMENT ' 编辑页面编辑状态 '")
  private String editableInEdit;

  /**
   * 编辑页面显隐
   */
  @ApiModelProperty("编辑页面显隐")
  @Column(name = "visible_in_edit", nullable = true, length = 8, columnDefinition = "VARCHAR(8) COMMENT ' 编辑页面显隐 '")
  private String visibleInEdit;

  /**
   * 查看页面显隐
   */
  @ApiModelProperty("查看页面显隐")
  @Column(name = "visible_in_look", nullable = true, length = 8, columnDefinition = "VARCHAR(8) COMMENT ' 查看页面显隐 '")
  private String visibleInLook;

  /**
   * 对齐方式
   */
  @ApiModelProperty("对齐方式")
  @Column(name = "align", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 对齐方式 '")
  private String align;

  /**
   * 类型
   */
  @ApiModelProperty("类型")
  @Column(name = "type", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 类型 '")
  private String type;

  /**
   * 宽度
   */
  @ApiModelProperty("宽度")
  @Column(name = "width", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 宽度 '")
  private String width;

  /**
   * 顺序
   */
  @ApiModelProperty("顺序")
  @Column(name = "formorder", columnDefinition = "int(6) COMMENT ' 顺序 '")
  private Integer formorder;

  /**
   * 位置
   */
  @ApiModelProperty("位置")
  @Column(name = "fixed", nullable = true, length = 8, columnDefinition = "VARCHAR(8) COMMENT ' 位置 '")
  private String fixed;

  /**
   * 是否展示省略号1展示，0不展示
   */
  @ApiModelProperty("是否展示省略号1展示，0不展示")
  @Column(name = "show_overflow", nullable = true, length = 1, columnDefinition = "char(1) COMMENT ' 是否展示省略号1展示，0不展示 '")
  private Boolean showOverflow;

  /**
   * 是否必填
   */
  @ApiModelProperty("是否必填")
  @Column(name = "required", nullable = true, length = 1, columnDefinition = "char(1) COMMENT ' 是否必填 '")
  private Boolean required;

  /**
   * 数据字典编码
   */
  @ApiModelProperty("数据字典编码")
  @Column(name = "dict_code", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 数据字典编码 '")
  private String dictCode;

  /**
   * 列样式
   */
  @ApiModelProperty("列样式")
  @Column(name = "col", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 列样式 '")
  private String col;

  /**
   * 事件
   */
  @ApiModelProperty("事件")
  @Column(name = "formon", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 事件 '")
  private String formon;

  /**
   * 操作
   */
  @ApiModelProperty("操作")
  @Column(name = "formoptions", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 操作 '")
  private String formoptions;

  /**
   * 属性
   */
  @ApiModelProperty("属性")
  @Column(name = "props", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 属性 '")
  private String props;

  /**
   * 刷新
   */
  @ApiModelProperty("刷新")
  @Column(name = "refresh", nullable = true, length = 1, columnDefinition = "char(1) COMMENT ' 刷新1是0否 '")
  private Boolean refresh;

  /**
   * 值
   */
  @ApiModelProperty("值")
  @Column(name = "formvalue", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 值 '")
  private String formvalue;

  /**
   * 控件
   */
  @ApiModelProperty("控件")
  @Column(name = "class_name", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 控件 '")
  private String className;

  /**
   * 字段是否搜索
   */
  @ApiModelProperty("字段是否搜索")
  @Column(name = "search", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 字段是否搜索 '")
  private String search;

  /**
   * 请求地址
   */
  @ApiModelProperty("请求地址")
  @Column(name = "request_url", nullable = true, columnDefinition = "longtext COMMENT ' 请求地址 '")
  private String requestUrl;

  /**
   * 下拉框是否搜索
   */
  @ApiModelProperty("下拉框是否搜索")
  @Column(name = "request_search", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 下拉框是否搜索 '")
  private String requestSearch;

  /**
   * 是否查看详情
   */
  @ApiModelProperty("是否查看详情")
  @Column(name = "edit_view", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 是否查看详情 '")
  private String editView;

  /**
   * 查询方式-1不作查询,0精确查询，1模糊查询，2范围查询，3左模糊查询，4右模糊查询，5小于，6小于等于，7大于，8大于等于
   */
  @ApiModelProperty("查询方式-1不作查询,0精确查询，1模糊查询，2范围查询，3左模糊查询，4右模糊查询，5小于，6小于等于，7大于，8大于等于")
  @Column(name = "search_type", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 查询方式-1不作查询,0精确查询，1模糊查询，2范围查询，3左模糊查询，4右模糊查询，5小于，6小于等于，7大于，8大于等于 '")
  private String searchType;

  /**
   * 实体字段名称
   */
  @ApiModelProperty("实体字段名称")
  @Column(name = "entity_field_name", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 实体字段名称 '")
  private String entityFieldName;

  /**
   * 列表控件类型
   */
  @ApiModelProperty("列表控件类型")
  @Column(name = "form_control", nullable = true, length = 64, columnDefinition = "VARCHAR(64) COMMENT ' 列表控件类型 '")
  private String formControl;

  /**
   * 多行分割符信息(前端自行定义多行分隔符字符串)
   */
  @ApiModelProperty("多行分割符信息(前端自行定义多行分隔符字符串)")
  @Column(name="multiline_separator_info" , nullable=true, columnDefinition = "varchar(2000) COMMENT '多行分割符信息(前端自行定义多行分隔符字符串)'")
  private String multilineSeparatorInfo;

  /**
   * 页面模板
   */
  @ApiModelProperty("页面模板")
  @TableField(exist = false)
  @Transient
  private MdmFunctionSubEntity functionSubEntity;
}
