package com.biz.crm.mdm.business.table.local.deprecated.controller;

import com.biz.crm.business.common.sdk.deprecated.model.PageResult;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.table.sdk.deprecated.dto.MdmFunctionReqVo;
import com.biz.crm.mdm.business.table.sdk.deprecated.dto.MdmFunctionSubButtonReqVo;
import com.biz.crm.mdm.business.table.sdk.deprecated.dto.MdmFunctionSubReqVo;
import com.biz.crm.mdm.business.table.sdk.deprecated.vo.MdmButtonConfigRespVo;
import com.biz.crm.mdm.business.table.sdk.deprecated.vo.MdmFunctionRespVo;
import com.biz.crm.mdm.business.table.sdk.deprecated.vo.MdmFunctionSubPermissionVo;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 下级菜单表
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/mdmfunctionsub")
@Api(tags = "功能列表")
public class MdmFunctionSubControllerFixme {

  /**
   * 列表
   * fixme
   * hefan：逗我吗？最后去调用mdmFunctionService.functionPage 查询 mdm_function 【菜单表】
   * hefan：如果真是菜单，这里暂时改不了
   */
  @ApiOperation(value = "查询可配置分页菜单列表")
  @PostMapping("/page")
  public Result<PageResult<MdmFunctionRespVo>> page(@RequestBody MdmFunctionReqVo mdmFunctionReqVo) {
    return Result.error("要查询菜单，找菜单模块去！");
  }

  /**
   * fixme
   * hefan:又是一个 mdm_function 的查询
   *
   * @param mdmFunctionReqVo
   * @return
   */
  @ApiOperation(value = "查询可配置的菜单树")
  @PostMapping("/configTree")
  public Result<List<MdmFunctionRespVo>> configTree(@RequestBody MdmFunctionReqVo mdmFunctionReqVo) {
    return Result.error("要查询菜单，找菜单模块去！");
  }

  /**
   * fixme: 这是一个报表：
   * from mdm_function_sub a
   * left join mdm_function_sub_button b on a.function_code=b.function_code and a.parent_code=b.parent_code
   * left join mdm_button c on c.button_code =b.button_code
   * left join mdm_function d on d.function_code=a.parent_code
   *
   * @param mdmFunctionSubButtonReqVo
   * @return
   */
  @ApiOperation("页面引擎动态导出专用查询")
  @PostMapping("/dynamicExportSearch")
  public Result<PageResult<JsonNode>> dynamicExportSearch(@RequestBody MdmFunctionSubButtonReqVo mdmFunctionSubButtonReqVo) {
    return Result.error("这是一个报表");
  }





  /**
   * fixme: 结合数据权限，获取上级菜单，已经授权的下级菜单列表
   * @param mdmFunctionSubReqVo
   * @return
   */
  @ApiOperation("根据上级菜单查询配置权限的功能列表(数据权限专用)")
  @PostMapping("/findAuthorizedSubList")
  public Result<List<MdmFunctionSubPermissionVo>> findAuthorizedSubList(@RequestBody MdmFunctionSubReqVo mdmFunctionSubReqVo) {
    return Result.error("都在页面引擎调用数据权限了，怎么能放过你");
  }



}
