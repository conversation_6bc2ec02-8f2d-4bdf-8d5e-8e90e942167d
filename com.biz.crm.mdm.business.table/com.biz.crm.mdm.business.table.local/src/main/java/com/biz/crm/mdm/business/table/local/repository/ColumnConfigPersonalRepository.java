package com.biz.crm.mdm.business.table.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.table.local.entity.MdmColumnConfigPersonalEntity;
import com.biz.crm.mdm.business.table.local.mapper.ColumnConfigPersonalMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class ColumnConfigPersonalRepository extends ServiceImpl<ColumnConfigPersonalMapper, MdmColumnConfigPersonalEntity> {

  /**
   * 根据 parentCode functionCode 删除 字段配置个性设置
   *
   * @param parentCode
   * @param functionCode
   */
  public void deleteByParentCodeAndFunctionCode(String parentCode, String functionCode) {
    this.lambdaUpdate()
        .eq(MdmColumnConfigPersonalEntity::getParentCode, parentCode)
        .eq(MdmColumnConfigPersonalEntity::getFunctionCode, functionCode)
        .remove();
  }

  /**
   * 根据 parentCode functionCode positionCode 删除 字段配置个性设置
   *
   * @param parentCode
   * @param functionCode
   * @param positionCode
   */
  public void deleteByParentCodeAndFunctionCodeAndPositionCode(String parentCode, String functionCode, String positionCode) {
    this.lambdaUpdate()
        .eq(MdmColumnConfigPersonalEntity::getPositionCode, positionCode)
        .eq(MdmColumnConfigPersonalEntity::getParentCode, parentCode)
        .eq(MdmColumnConfigPersonalEntity::getFunctionCode, functionCode)
        .remove();
  }

  /**
   * 删除由parent7 func7 posi7帐户
   *
   * @param parentCode    菜单
   * @param functionCode  页面模板
   * @param positionCode  岗位
   * @param createAccount 创建账户
   */
  public void deleteByParent7Func7Posi7Account(String parentCode, String functionCode, String positionCode, String createAccount) {
    this.lambdaUpdate()
        .eq(MdmColumnConfigPersonalEntity::getPositionCode, positionCode)
        .eq(MdmColumnConfigPersonalEntity::getParentCode, parentCode)
        .eq(MdmColumnConfigPersonalEntity::getFunctionCode, functionCode)
        .eq(MdmColumnConfigPersonalEntity::getCreateAccount, createAccount)
        .remove();
  }

  /**
   * 根据 岗位，上级菜单，页面模板 按 顺序 正序查询
   *
   * @param positionCode
   * @param parentCode
   * @param functionCode
   * @return
   */
  public List<MdmColumnConfigPersonalEntity> findByPositionAndParentAndFunctionOrderByFormOrderAsc(String positionCode, String parentCode, String functionCode) {
    return this.lambdaQuery()
        .eq(MdmColumnConfigPersonalEntity::getPositionCode, positionCode)
        .eq(MdmColumnConfigPersonalEntity::getParentCode, parentCode)
        .eq(MdmColumnConfigPersonalEntity::getFunctionCode, functionCode)
        .orderByAsc(MdmColumnConfigPersonalEntity::getFormOrder)
        .list();
  }

  /**
   * 发现由posi7 parent7 func7帐户
   *
   * @param positionCode  岗位
   * @param parentCode    菜单
   * @param functionCode  页面模板
   * @param createAccount 创建账户
   * @return {@link List}<{@link MdmColumnConfigPersonalEntity}>
   */
  public List<MdmColumnConfigPersonalEntity> findByPosi7Parent7Func7Account(String positionCode, String parentCode, String functionCode, String createAccount) {
    return this.lambdaQuery()
        .eq(MdmColumnConfigPersonalEntity::getPositionCode, positionCode)
        .eq(MdmColumnConfigPersonalEntity::getParentCode, parentCode)
        .eq(MdmColumnConfigPersonalEntity::getFunctionCode, functionCode)
        .eq(MdmColumnConfigPersonalEntity::getCreateAccount, createAccount)
        .orderByAsc(MdmColumnConfigPersonalEntity::getFormOrder)
        .list();
  }

  /**
   * 根据 parentCode functionCode positionCode 删除 个性设置
   *
   * @param positionCode
   * @param parentCode
   * @param functionCode
   */
  public void deleteByPositionCodeAndParentCodeAndFunctionCode(String positionCode, String parentCode, String functionCode) {
    this.lambdaUpdate()
        .eq(MdmColumnConfigPersonalEntity::getPositionCode, positionCode)
        .eq(MdmColumnConfigPersonalEntity::getParentCode, parentCode)
        .eq(MdmColumnConfigPersonalEntity::getFunctionCode, functionCode)
        .remove();
  }
}
