package com.biz.crm.mdm.business.table.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.table.sdk.dto.FunctionSubDto;
import com.biz.crm.mdm.business.table.sdk.dto.FunctionSubPaginationDto;
import com.biz.crm.mdm.business.table.sdk.service.FunctionSubVoService;
import com.biz.crm.mdm.business.table.sdk.vo.FunctionSubVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 页面模板VO
 *
 * <AUTHOR>
 */
@Api(tags = "页面引擎：FunctionSubVo: 页面模板VO")
@Slf4j
@RestController
@RequestMapping("/v1/table/functionsub")
public class FunctionSubVoController {

  @Autowired(required = false)
  private FunctionSubVoService functionSubVoService;

  /**
   * hefan：分页-动态查询function_sub
   * 列表
   */
  @ApiOperation(value = "多条件分页查询", notes = "分页参数为page和size，page从0开始，size默认50;" +
      "可传的参数：FunctionSubPaginationDto")
  @GetMapping("/findByCondition")
  public Result<Page<FunctionSubVo>> findByCondition(@PageableDefault(50) Pageable pageable,
                                                     @ApiParam(name = "FunctionSubPaginationDto", value = "分页Dto") FunctionSubPaginationDto functionSubPaginationDto) {
    try {
      Page<FunctionSubVo> result = this.functionSubVoService.findByCondition(pageable, functionSubPaginationDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * hefan：动态查询 function_sub
   *
   * @param functionSubDto
   * @return
   */
  @ApiOperation("动态查询 function_sub")
  @GetMapping("/findByFunctionSubDto")
  public Result<List<FunctionSubVo>> findByFunctionSubDto(@ApiParam(name = "functionSubDto", value = "functionSub动态查询DTO") FunctionSubDto functionSubDto) {
    try {
      List<FunctionSubVo> functionSubVos = this.functionSubVoService.findByFunctionSubDto(functionSubDto);
      return Result.ok(functionSubVos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * hefan: 根据 ParentCode FunctionCode 查询 一条functionSub数据
   *
   * @param parentCode
   * @param functionCode
   * @return
   */
  @ApiOperation("根据菜单编码与功能编码查询")
  @GetMapping("/findOneByParentCodeAndFunctionCode")
  public Result<FunctionSubVo> findOneByParentCodeAndFunctionCode(String parentCode, String functionCode) {
    try {
      FunctionSubVo functionSubVo = this.functionSubVoService.findOneByParentCodeAndFunctionCode(parentCode, functionCode);
      return Result.ok(functionSubVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * hefan: 根据ID查询一条
   *
   * @param id
   * @return
   */
  @ApiOperation(value = "根据ID查询一条")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "id", value = "id", required = true, paramType = "query")
  })
  @GetMapping("/findById")
  public Result<FunctionSubVo> findById(@RequestParam(value = "id") String id) {
    try {
      FunctionSubVo functionSubVo = this.functionSubVoService.findById(id);
      return Result.ok(functionSubVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * hefan: 根据ID，FunctionCode 查询一条信息
   *
   * @param id
   * @param functionCode
   * @return
   */
  @ApiOperation("根据ID，FunctionCode 查询一条信息")
  @GetMapping("/findByIdAndFunctionCode")
  public Result<FunctionSubVo> findByIdAndFunctionCode(String id, String functionCode) {
    try {
      FunctionSubVo respVo = this.functionSubVoService.findByIdAndFunctionCode(id, functionCode);
      return Result.ok(respVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }



}
