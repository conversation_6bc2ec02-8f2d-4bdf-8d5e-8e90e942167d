package com.biz.crm.mdm.business.table.local.service;

import com.biz.crm.mdm.business.table.local.entity.MdmFunctionSubEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FunctionSubService {

  /**
   * 新增 function_sub
   *
   * @param mdmFunctionSubEntity
   * @return
   */
  MdmFunctionSubEntity create(MdmFunctionSubEntity mdmFunctionSubEntity);

  /**
   * 修改数据，如果修改了functionCode，同时修改column_config、sub_button关联
   *
   * @param mdmFunctionSubEntity
   */
  void update(MdmFunctionSubEntity mdmFunctionSubEntity);

  /**
   * 这个方法内部本身就是写法错误的
   * *  删了functionSub
   * *  删了functionSubButton
   * *  删了columnConfig
   * *  清除了parentCode以下的缓存
   *
   * @param ids
   */
  void deleteByIdIn(List<String> ids);

  /**
   * 根据id集合 启用（修改字段）
   *
   * @param ids
   */
  void enableByIdIn(List<String> ids);

  /**
   * 根据id集合 禁用（修改字段）
   *
   * @param ids
   */
  void disableByIdIn(List<String> ids);
}
