package com.biz.crm.mdm.business.table.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.table.local.entity.MdmColumnConfigEntity;
import com.biz.crm.mdm.business.table.sdk.dto.ColumnConfigPaginationDto;
import com.biz.crm.mdm.business.table.sdk.vo.ColumnConfigVo;

/**
 * <AUTHOR>
 */
public interface ColumnConfigMapper extends BaseMapper<MdmColumnConfigEntity> {

  /**
   * 分页 动态 条件 查询
   * @param page
   * @param columnConfigPaginationDto
   * @return
   */
  Page<ColumnConfigVo> findByCondition(Page<ColumnConfigVo> page, ColumnConfigPaginationDto columnConfigPaginationDto);
}
