package com.biz.crm.mdm.business.table.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.table.local.entity.MdmFunctionSubEntity;
import com.biz.crm.mdm.business.table.local.service.FunctionSubService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 页面引擎模板
 *
 * <AUTHOR>
 */
@Api(tags = "页面引擎：FunctionSub: 页面引擎模板")
@Slf4j
@RestController
@RequestMapping("/v1/table/functionsub")
public class FunctionSubController {

  @Autowired(required = false)
  private FunctionSubService functionSubService;

  /**
   * hefan: function_sub 新增
   *
   * @param mdmFunctionSubEntity
   * @return
   */
  @ApiOperation(value = "function_sub 新增")
  @PostMapping("")
  public Result<?> create(@RequestBody MdmFunctionSubEntity mdmFunctionSubEntity) {
    try {
      MdmFunctionSubEntity entity = this.functionSubService.create(mdmFunctionSubEntity);
      return Result.ok(entity);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * hefan: 修改数据，如果修改了functionCode，同时修改column_config、sub_button关联
   */
  @ApiOperation(value = "修改数据，如果修改了functionCode，同时修改column_config、sub_button关联")
  @PatchMapping("")
  public Result<?> update(@RequestBody MdmFunctionSubEntity mdmFunctionSubEntity) {
    try {
      this.functionSubService.update(mdmFunctionSubEntity);
      return Result.ok("修改成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * hefan: 这个方法内部本身就是写法错误的
   * 删了functionSub
   * 删了functionSubButton
   * 删了columnConfig
   * 清除了parentCode以下的缓存
   */
  @ApiOperation(value = "删了functionSub、删了functionSubButton、删了columnConfig")
  @DeleteMapping("/deleteByIdIn")
  public Result<?> deleteByIdIn(@RequestParam List<String> ids) {
    try {
      this.functionSubService.deleteByIdIn(ids);
      return Result.ok("删除成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * hefan: 根据ID集合 修改 启用状态
   * 启用
   */
  @ApiOperation(value = "根据ID集合 修改 启用状态")
  @PatchMapping("/enableByIdIn")
  public Result<?> enableByIdIn(@RequestBody List<String> ids) {
    try {
      this.functionSubService.enableByIdIn(ids);
      return Result.ok("启用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * hefan: 根据ID集合 修改 禁用状态
   * 禁用
   */
  @ApiOperation(value = "根据ID集合 修改 禁用状态")
  @PostMapping("/disableByIdIn")
  public Result<?> disableByIdIn(@RequestBody List<String> ids) {
    try {
      this.functionSubService.disableByIdIn(ids);
      return Result.ok("禁用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
