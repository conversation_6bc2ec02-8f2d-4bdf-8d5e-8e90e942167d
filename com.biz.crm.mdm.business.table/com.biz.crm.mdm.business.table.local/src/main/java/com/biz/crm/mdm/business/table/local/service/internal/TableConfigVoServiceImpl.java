package com.biz.crm.mdm.business.table.local.service.internal;

import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.table.local.entity.MdmFunctionSubButtonEntity;
import com.biz.crm.mdm.business.table.local.entity.MdmFunctionSubEntity;
import com.biz.crm.mdm.business.table.local.repository.FunctionSubButtonRepository;
import com.biz.crm.mdm.business.table.local.repository.FunctionSubRepository;
import com.biz.crm.mdm.business.table.sdk.enums.PageTemplateTypeEnum;
import com.biz.crm.mdm.business.table.sdk.service.ColumnConfigPersonalVoService;
import com.biz.crm.mdm.business.table.sdk.service.ColumnConfigVoService;
import com.biz.crm.mdm.business.table.sdk.service.TableConfigVoService;
import com.biz.crm.mdm.business.table.sdk.vo.ButtonConfigVo;
import com.biz.crm.mdm.business.table.sdk.vo.ColumnConfigPersonalVo;
import com.biz.crm.mdm.business.table.sdk.vo.ColumnConfigVo;
import com.biz.crm.mdm.business.table.sdk.vo.TableConfigVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.competence.sdk.service.ButtonVoService;
import com.bizunited.nebula.competence.sdk.vo.ButtonVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class TableConfigVoServiceImpl implements TableConfigVoService {

  @Autowired(required = false)
  private FunctionSubRepository functionSubRepository;
  @Autowired(required = false)
  private FunctionSubButtonRepository functionSubButtonRepository;
  @Autowired(required = false)
  private LoginUserService loginUserService;
  @Autowired(required = false)
  private ButtonVoService buttonVoService;

  private static final String ROLE_ADMIN = "ADMIN";

  @Override
  public TableConfigVo findByParentCodeAndFunctionCode(String parentCode, String functionCode) {
    Validate.notBlank(parentCode, "菜单编码不能为空");
    Validate.notBlank(functionCode, "功能编码不能为空");
    // 根据 parentCode functionType functionCode 查询
    MdmFunctionSubEntity functionSubEntity = this.functionSubRepository.findOneByParentCodeAndFunctionCodeAndFunctionType(parentCode, functionCode, PageTemplateTypeEnum.LIST_CONFIG.getCode());
    Validate.notNull(functionSubEntity, "列表配置不存在");
    // 角色权限内的按钮信息 ； 页面引擎主表【页面模板】的字段信息 ；
    TableConfigVo resultRespVo = this.findListConfig(parentCode, functionCode);
    // apiUrl
    resultRespVo.setUrl(functionSubEntity.getApiUrl());
    //由于走接口需要实现页面拖动 现在固定为true 可拖动 后续优化为从数据库查询 TODO
    resultRespVo.setResizable(true);
    // fixme: 原逻辑：缓存TableConfigVo
    // fixme：依据个性化字段配置，对返回的结果的字段排序
    return resultRespVo;
  }

  @Autowired(required = false)
  private ColumnConfigVoService columnConfigVoService;
  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;
  @Autowired(required = false)
  private ColumnConfigPersonalVoService columnConfigPersonalVoService;

  /**
   * hefan:
   * 返回对象内包含：
   * 按钮信息(TODO:图标信息,角色权限内的按钮信息)
   * 页面引擎主表【页面模板】的字段信息
   *
   * @param parentCode
   * @param functionCode
   * @return
   */
  private TableConfigVo findListConfig(String parentCode, String functionCode) {
    // hefan：查询 functionSubButton关联表，按钮，图标
    String showMode = BooleanEnum.TRUE.getNumStr();
    List<MdmFunctionSubButtonEntity> functionSubButton = functionSubButtonRepository.findButtonByParentCodeAndFunctionCodeAndVisible(parentCode, functionCode, showMode);
    // fixme: 按钮的图标查询

    // 根据parentCode、functionCode 查询，并按照 formorder 排序

    // 按角色权限过滤按钮信息
    Set<String> roleCodes = this.findUserRoleCodeSet();
    if (CollectionUtils.isEmpty(roleCodes)) {
      functionSubButton = Lists.newLinkedList();
    } else if (!roleCodes.contains(ROLE_ADMIN)) {
      String[] competenceCodes = new String[] {StringUtils.joinWith("@", parentCode, functionCode)};
      String[] roleArr = roleCodes.toArray(new String[0]);
      final List<ButtonVo> roleButtons =
          buttonVoService.findByRoleCodesAndCompetenceCodes(roleArr, competenceCodes);
      if (CollectionUtils.isEmpty(roleButtons)) {
        functionSubButton = Lists.newLinkedList();
      } else {
        Set<String> buttonCodes =
            roleButtons.stream().map(ButtonVo::getCode).collect(Collectors.toSet());
        final List<MdmFunctionSubButtonEntity> cur =
            functionSubButton.stream()
                .filter(a -> buttonCodes.contains(a.getCode()))
                .collect(Collectors.toList());
        functionSubButton = cur;
        //设置值---添加buttonMethod字段
        Map<String, ButtonVo> buttonVoMap = roleButtons.stream()
            .collect(Collectors.toMap(ButtonVo::getCode, buttonVo -> buttonVo));
        functionSubButton.forEach(button -> {
          String buttonMethod = buttonVoMap.get(button.getCode()).getButtonMethod();
          buttonMethod = StringUtils.isEmpty(buttonMethod) ? "" : buttonMethod;
          button.setButtonMethod(buttonMethod);
        } );
      }
    }

    List<ColumnConfigVo> columnConfigVos = this.columnConfigVoService.findByParentCodeAndFunctionCodeOrderByFormorder(parentCode, functionCode);
    List<ButtonConfigVo> buttonConfigVos = this.copyFuctionSubButton2ButtonCofigVo(functionSubButton);
    List<ColumnConfigPersonalVo> columnConfigPersonalVos = this.columnConfigPersonalVoService.findByParentCodeAndFunctionCode(parentCode,functionCode);
    TableConfigVo tableConfigVo = new TableConfigVo();
    tableConfigVo.setColumn(columnConfigVos);
    tableConfigVo.setColumnPersonal(columnConfigPersonalVos);
    tableConfigVo.setButtonVos(buttonConfigVos);
    return tableConfigVo;
  }

  /**
   * 拷贝 FuctionSubButton 到 ButtonCofigVo
   * @param functionSubButton
   * @return
   */
  private List<ButtonConfigVo> copyFuctionSubButton2ButtonCofigVo(List<MdmFunctionSubButtonEntity> functionSubButton) {
    List<ButtonConfigVo> buttonConfigVos = Lists.newLinkedList();
    if (!CollectionUtils.isEmpty(functionSubButton)){
      for (MdmFunctionSubButtonEntity entity : functionSubButton) {
        ButtonConfigVo buttonConfigVo = this.nebulaToolkitService.copyObjectByBlankList(entity, ButtonConfigVo.class, HashSet.class, LinkedList.class);
        buttonConfigVos.add(buttonConfigVo);
      }
    }
    return buttonConfigVos;
  }

  /**
   * 获取操作人角色信息
   *
   * @return
   */
  private Set<String> findUserRoleCodeSet() {
    final Authentication authentication = this.loginUserService.getAuthentication();
    Validate.notNull(authentication, "未获取到用户认证信息");
    final Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
    if (CollectionUtils.isEmpty(authorities)) {
      return Sets.newHashSet();
    }
    Set<String> set = Sets.newHashSet();
    for (GrantedAuthority item : authorities) {
      final String role = item.getAuthority();
      if (StringUtils.isBlank(role)) {
        continue;
      }
      set.add(role);
    }
    return set;
  }
}
