package com.biz.crm.mdm.business.table.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.table.local.entity.MdmFunctionSubButtonEntity;
import com.biz.crm.mdm.business.table.local.repository.FunctionSubButtonRepository;
import com.biz.crm.mdm.business.table.sdk.constant.PageEngineConstant;
import com.biz.crm.mdm.business.table.sdk.service.ButtonConfigVoService;
import com.biz.crm.mdm.business.table.sdk.vo.ButtonConfigVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.competence.sdk.service.ButtonVoService;
import com.bizunited.nebula.competence.sdk.vo.ButtonVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ButtonConfigVoServiceImpl implements ButtonConfigVoService {

  @Autowired(required = false)
  private FunctionSubButtonRepository functionSubButtonRepository;
  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;
  @Autowired(required = false)
  private ButtonVoService buttonVoService;

  @Override
  public Page<ButtonConfigVo> findByParentCodeAndFunctionCodePage(Pageable pageable,String parentCode, String functionCode) {
    Validate.notBlank(parentCode, "菜单编码不能为空");
    Validate.notBlank(functionCode, "页面模板编码不能为空");
    //分页查询
    Page<MdmFunctionSubButtonEntity> buttonByParentCodeAndFunctionCode = this.functionSubButtonRepository.findButtonPageByParentCodeAndFunctionCode(pageable, parentCode, functionCode);
    List<MdmFunctionSubButtonEntity> functionSubButton = buttonByParentCodeAndFunctionCode.getRecords();
    List<ButtonConfigVo> buttonConfigVos = (List<ButtonConfigVo>) this.nebulaToolkitService.copyCollectionByBlankList(functionSubButton, MdmFunctionSubButtonEntity.class, ButtonConfigVo.class, LinkedHashSet.class, LinkedList.class);
    //  引擎按钮
    String code = StringUtils.join(parentCode, PageEngineConstant.JOINT_MARK, functionCode);
    List<ButtonVo> buttonVos = this.buttonVoService.findByCompetenceCode(code);
    Map<String, List<ButtonVo>> codeMapButtonVo = null;
    if (!CollectionUtils.isEmpty(buttonVos)) {
      codeMapButtonVo = buttonVos.stream().collect(Collectors.groupingBy(ButtonVo::getCode));
    }
    for (ButtonConfigVo buttonConfigVo : buttonConfigVos) {
      if (CollectionUtils.isEmpty(codeMapButtonVo)) {
        // 页面模板侧按钮，与 总控侧按钮 脱离管控。直接阻断
        continue;
      }
      List<ButtonVo> buttonVoList = codeMapButtonVo.get(buttonConfigVo.getCode());
      if (CollectionUtils.isEmpty(buttonVoList)) {
        continue;
      }
      ButtonVo buttonVo = buttonVoList.get(0);
      buttonConfigVo.setPageEngineButtonId(buttonConfigVo.getId());
      buttonConfigVo.setButtonId(buttonVo.getId());
      String buttonMethod = StringUtils.isEmpty(buttonVo.getButtonMethod()) ? "" : buttonVo.getButtonMethod();
      buttonConfigVo.setButtonMethod(buttonMethod);
    }
    //重新构建page对象
    Page<ButtonConfigVo> page = new Page<>();
    page.setRecords(buttonConfigVos);
    page.setTotal(buttonByParentCodeAndFunctionCode.getTotal());
    page.setCurrent(buttonByParentCodeAndFunctionCode.getCurrent());
    page.setPages(buttonByParentCodeAndFunctionCode.getPages());
    page.setSize(buttonByParentCodeAndFunctionCode.getSize());
    return page;
  }

  @Override
  public ButtonConfigVo findById(String id) {
    Validate.notBlank(id, "id不能为空");
    MdmFunctionSubButtonEntity entity = this.functionSubButtonRepository.findButtonById(id);
    ButtonConfigVo buttonConfigVo = this.nebulaToolkitService.copyObjectByBlankList(entity, ButtonConfigVo.class, LinkedHashSet.class, LinkedList.class);
    // 引擎按钮
    ButtonVo buttonVo = this.buttonVoService.findByCode(buttonConfigVo.getCode());
    if (Objects.nonNull(buttonVo)) {
      buttonConfigVo.setPageEngineButtonId(buttonConfigVo.getId());
      buttonConfigVo.setButtonId(buttonVo.getId());
    }
    return buttonConfigVo;
  }

  @Override
  public List<ButtonConfigVo> findByParentCodeAndFunctionCode(String parentCode, String functionCode) {
    Validate.notBlank(parentCode, "菜单编码不能为空");
    Validate.notBlank(functionCode, "页面模板编码不能为空");
    List<MdmFunctionSubButtonEntity> functionSubButton = this.functionSubButtonRepository.findButtonByParentCodeAndFunctionCode(parentCode, functionCode);
    List<ButtonConfigVo> buttonConfigVos = (List<ButtonConfigVo>) this.nebulaToolkitService.copyCollectionByBlankList(functionSubButton, MdmFunctionSubButtonEntity.class, ButtonConfigVo.class, LinkedHashSet.class, LinkedList.class);
    //  引擎按钮
    String code = StringUtils.join(parentCode, PageEngineConstant.JOINT_MARK, functionCode);
    List<ButtonVo> buttonVos = this.buttonVoService.findByCompetenceCode(code);
    Map<String, List<ButtonVo>> codeMapButtonVo = null;
    if (!CollectionUtils.isEmpty(buttonVos)) {
      codeMapButtonVo = buttonVos.stream().collect(Collectors.groupingBy(ButtonVo::getCode));
    }
    for (ButtonConfigVo buttonConfigVo : buttonConfigVos) {
      if (CollectionUtils.isEmpty(codeMapButtonVo)) {
        // 页面模板侧按钮，与 总控侧按钮 脱离管控。直接阻断
        continue;
      }
      List<ButtonVo> buttonVoList = codeMapButtonVo.get(buttonConfigVo.getCode());
      if (CollectionUtils.isEmpty(buttonVoList)) {
        continue;
      }
      ButtonVo buttonVo = buttonVoList.get(0);
      buttonConfigVo.setPageEngineButtonId(buttonConfigVo.getId());
      buttonConfigVo.setButtonId(buttonVo.getId());
    }
    return buttonConfigVos;
  }
}
