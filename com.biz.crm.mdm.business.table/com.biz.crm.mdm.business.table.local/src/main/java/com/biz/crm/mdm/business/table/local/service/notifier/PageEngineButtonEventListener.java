package com.biz.crm.mdm.business.table.local.service.notifier;

import com.biz.crm.mdm.business.table.local.entity.MdmFunctionSubButtonEntity;
import com.biz.crm.mdm.business.table.local.service.FunctionSubButtonService;
import com.biz.crm.mdm.business.table.sdk.vo.PageEngineButtonVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.competence.sdk.event.ButtonEventListener;
import com.bizunited.nebula.competence.sdk.vo.ButtonVo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.LinkedList;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class PageEngineButtonEventListener implements ButtonEventListener {

  @Autowired(required = false)
  private FunctionSubButtonService functionSubButtonService;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 页面引擎的新增按钮，同时也是 页面模板（即某种功能）与 按钮 的绑定过程
   * @param button
   */
  @Transactional
  @Override
  public void onCreated(ButtonVo button) {
    if(!(button instanceof PageEngineButtonVo)) {
      return;
    }
    // 当按钮基本创建时，需要创建默认的关联按钮信息
    PageEngineButtonVo defaultButtonVo = (PageEngineButtonVo)button;
    String code = defaultButtonVo.getCode();
    // 试图进行边界校验后报错，
    MdmFunctionSubButtonEntity exsit = functionSubButtonService.findByCode(code);
    Validate.isTrue(exsit == null , "添加功能时，发现页面引擎按钮中已经关联了对应的按钮基本信息业务编号，请检查!!");
    MdmFunctionSubButtonEntity entity = nebulaToolkitService.copyObjectByBlankList(defaultButtonVo, MdmFunctionSubButtonEntity.class, HashSet.class, LinkedList.class);
    entity.setButtonName(defaultButtonVo.getName());
    entity.setRemark(defaultButtonVo.getButtonDesc());
    entity.setIconCode(defaultButtonVo.getIcon());
    entity.setCode(defaultButtonVo.getCode());
    // 开始保存
    this.functionSubButtonService.create(entity);
  }

  @Override
  public void onUpdate(ButtonVo button) {
    if(!(button instanceof PageEngineButtonVo)) {
      return;
    }
    // 当按钮基本创建时，需要创建默认的关联按钮信息
    PageEngineButtonVo defaultButtonVo = (PageEngineButtonVo)button;
    String code = defaultButtonVo.getCode();
    // 试图进行边界校验后报错，
    MdmFunctionSubButtonEntity exsitById = this.functionSubButtonService.findById(defaultButtonVo.getPageEngineButtonId());
    Validate.notNull(exsitById, "根据PageEngineButtonId没有查询到数据");
    MdmFunctionSubButtonEntity exsitByCode = this.functionSubButtonService.findByCode(code);
    /**
     *  exsitByCode == null 可以继续修改，
     *  exsitByCode != null ，exsitByCode.getId() == PageEngineButtonId 可以继续修改
     *  exsitByCode != null ，exsitByCode.getId() ！= PageEngineButtonId 不可以继续修改
     */
    boolean isSame = Objects.nonNull(exsitByCode) && exsitByCode.getId().equals(exsitById.getId());
    Validate.isTrue(Objects.isNull(exsitByCode) || isSame, "编码已经被其它信息占用，请重新修改");
    MdmFunctionSubButtonEntity entity = nebulaToolkitService.copyObjectByBlankList(defaultButtonVo, MdmFunctionSubButtonEntity.class, HashSet.class, LinkedList.class);
    entity.setId(exsitById.getId());
    entity.setCreateAccount(exsitById.getCreateAccount());
    entity.setCreateTime(exsitById.getCreateTime());
    entity.setCreateName(exsitById.getCreateName());
    entity.setButtonName(defaultButtonVo.getName());
    entity.setRemark(defaultButtonVo.getButtonDesc());
    entity.setIconCode(defaultButtonVo.getIcon());
    entity.setCode(defaultButtonVo.getCode());
    // 开始保存
    this.functionSubButtonService.update(entity);
  }

  /**
   * 页面引擎的删除按钮，同时也是 页面模板（即某种功能）与 按钮 的解除绑定过程
   * nebula 按钮在事件通知之前已经做了解绑操作
   *
   * @param button
   */
  @Transactional
  @Override
  public void onDeleted(ButtonVo button) {
    if(!(button instanceof PageEngineButtonVo)) {
      return;
    }
    String code = button.getCode();
    // 下层的按钮总控 和 此处的页面引擎按钮 必须同生共死
    MdmFunctionSubButtonEntity exsit = functionSubButtonService.findByCode(code);
    Validate.notNull(exsit, "删除按钮时，发现默认按钮中没有关联对应的按钮基本信息业务编号，请检查!!");
    // 进行删除
    LinkedList<String> ids = Lists.newLinkedList();
    ids.add(exsit.getId());
    this.functionSubButtonService.deleteByIdIn(ids);
  }
}
