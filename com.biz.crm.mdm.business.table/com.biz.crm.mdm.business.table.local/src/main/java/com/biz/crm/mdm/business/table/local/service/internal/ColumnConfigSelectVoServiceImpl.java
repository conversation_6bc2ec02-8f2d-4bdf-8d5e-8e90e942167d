package com.biz.crm.mdm.business.table.local.service.internal;

import com.biz.crm.mdm.business.table.local.entity.MdmColumnConfigEntity;
import com.biz.crm.mdm.business.table.local.repository.ColumnConfigRepository;
import com.biz.crm.mdm.business.table.sdk.constant.PageEngineConstant;
import com.biz.crm.mdm.business.table.sdk.service.ColumnConfigSelectVoService;
import com.biz.crm.mdm.business.table.sdk.vo.ColumnConfigSelectVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
public class ColumnConfigSelectVoServiceImpl implements ColumnConfigSelectVoService {

  @Autowired(required = false)
  private ColumnConfigRepository columnConfigRepository;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public List<ColumnConfigSelectVo> findByParentCodeAndFunctionCodeAndColumnExportOpt(String parentCode, String functionCode, String columnExport) {
    Validate.notBlank(parentCode, "菜单编码不能为空");
    Validate.notBlank(functionCode, "功能编码不能为空");
    // fixme: 原crm为parentCode=="CRM20201123000000046"在此处打了个补丁
    // 根据 parentCode functionCode 【columnExport】查询 字段配置
    List<MdmColumnConfigEntity> list = this.columnConfigRepository.findByParentCodeAndFunctionCodeAndColumnExportOpt(parentCode, functionCode, columnExport);
    if (CollectionUtils.isEmpty(list)) {
      return Lists.newLinkedList();
    }
    list.sort(Comparator.comparing(x -> Optional.ofNullable(x.getFormorder()).map(Integer::valueOf).orElse(PageEngineConstant.DEFAULT_ORDER)));
    List<ColumnConfigSelectVo> result = (List<ColumnConfigSelectVo>) this.nebulaToolkitService.copyCollectionByBlankList(list, MdmColumnConfigEntity.class, ColumnConfigSelectVo.class, HashSet.class, LinkedList.class);
    return result;

  }
}
