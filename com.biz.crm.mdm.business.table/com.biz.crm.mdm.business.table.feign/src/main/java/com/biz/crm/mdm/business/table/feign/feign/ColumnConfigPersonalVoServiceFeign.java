package com.biz.crm.mdm.business.table.feign.feign;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.table.feign.feign.internal.ColumnConfigPersonalVoServiceFeignImpl;
import com.biz.crm.mdm.business.table.sdk.vo.ColumnConfigPersonalVo;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年12月27日 16:32:00
 */
@FeignClient(
        
    name = "${mdm.feign-client.name:crm-mdm}",
    path = "crm-mdm",
    fallbackFactory = ColumnConfigPersonalVoServiceFeignImpl.class)
public interface ColumnConfigPersonalVoServiceFeign {
  /**
   *
   * @param parentCode
   * @param functionCode
   * @param account
   * @param tenantCode
   * @return
   */
  @GetMapping("/v1/table/columnConfigPersonal/findByParentCodeAndFunctionCode")
  Result<List<ColumnConfigPersonalVo>> findByParentCodeAndFunctionCode(
      @RequestParam @ApiParam(name = "parentCode", value = "上级菜单", required = true) String parentCode,
      @RequestParam @ApiParam(name = "functionCode", value = "菜单", required = true) String functionCode);
}
