package com.biz.crm.mdm.business.table.feign.feign;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.table.feign.feign.internal.ColumnConfigVoServiceFeignImpl;
import com.biz.crm.mdm.business.table.sdk.vo.ColumnConfigVo;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年12月27日 16:57:00
 */
@FeignClient(
        
    name = "${mdm.feign-client.name:crm-mdm}",
    path = "crm-mdm",
    fallbackFactory = ColumnConfigVoServiceFeignImpl.class)
public interface ColumnConfigVoServiceFeign {

  @GetMapping("/v1/table/columnConfig/findByParentCodeAndFunctionCodeOrderByFormorder")
  Result<List<ColumnConfigVo>> findByParentCodeAndFunctionCodeOrderByFormorder(@RequestParam("parentCode")String parentCode,
                                                                               @RequestParam("functionCode")String functionCode);
}
