package com.biz.crm.mdm.business.table.sdk.service;

import com.biz.crm.mdm.business.table.sdk.dto.PermissionObjDto;
import com.biz.crm.mdm.business.table.sdk.vo.PermissionObjVo;

import java.util.List;

/**
 * 可能是权限对象，配置在字典里的
 *
 * <AUTHOR>
 */
public interface PermissionObjVoService {

  /**
   * 根据 上级菜单编码  下级菜单编码 查询
   *
   * @param parentCode
   * @param functionCode
   * @return
   */
  List<PermissionObjVo> findByParentCodeAndFunctionCode(String parentCode, String functionCode);

  /**
   * 权限对象范围列表保存
   * @param permissionObjDto
   */
  void create(PermissionObjDto permissionObjDto);

}
