package com.biz.crm.mdm.business.table.sdk.deprecated.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021-04-22 09:44
 **/
@Deprecated
@Data
@ApiModel("配置权限的列表")
public class MdmFunctionSubPermissionVo {

    @ApiModelProperty("列表编码")
    private String functionCode;

    @ApiModelProperty("上级菜单编码")
    private String parentCode;

    @ApiModelProperty("列表名称")
    private String functionName;

    @ApiModelProperty("当前菜单下是否已经配置权限,1是0否")
    private String functionAuthorized;
}
