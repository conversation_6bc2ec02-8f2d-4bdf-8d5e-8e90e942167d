package com.biz.crm.mdm.business.table.sdk.deprecated.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * hefan:
 * ColumnConfigSelectVo
 *
 * 导出vo
 *
 * <AUTHOR>
 * @date 2020-12-04 16:17
 **/
@Deprecated
@Data
public class MdmColumnExportRespVo {
    /**
     * 列表复选框字段
     */
    public static final String checkTypecheckbox = "checkTypecheckbox";
    @ApiModelProperty("属性")
    private String field;

    @ApiModelProperty("标签")
    private String title;

    @ApiModelProperty("数据字典编码")
    private String dictCode;

    @ApiModelProperty("是否导出，1是0否")
    private String columnExport;
}
