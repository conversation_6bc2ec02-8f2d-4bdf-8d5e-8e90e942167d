package com.biz.crm.mdm.business.table.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 下级菜单表DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "FunctionSubDto", description = "下级菜单表DTO")
public class FunctionSubDto extends TenantDto {

  @ApiModelProperty("菜单配置类型")
  private String functionType;

  @ApiModelProperty("下级菜单编码")
  private String functionCode;

  @ApiModelProperty("下级菜单名称")
  private String functionName;

  @ApiModelProperty("上级菜单编码")
  private String parentCode;

  @ApiModelProperty("上级菜单编码集合")
  private List<String> parentCodeList;

  @ApiModelProperty("1：表示查询包含按钮功能列表")
  private Integer attachedButton;

}
