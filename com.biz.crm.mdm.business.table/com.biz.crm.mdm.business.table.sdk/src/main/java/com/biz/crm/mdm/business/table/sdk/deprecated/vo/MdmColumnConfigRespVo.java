package com.biz.crm.mdm.business.table.sdk.deprecated.vo;

//import com.biz.crm.config.CrmColumnResolve;
//import com.bizunited.platform.saturn.engine.annotation.SaturnColumn;
//import com.bizunited.platform.saturn.engine.annotation.SaturnEntity;

import com.bizunited.nebula.saturn.engine.annotation.SaturnColumn;
import com.bizunited.nebula.saturn.engine.annotation.SaturnEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字段配置返回vo
 * hefan: ColumnConfigVo
 *
 * <AUTHOR>
 * @date 2020-11-21 12:19:06
 */
@Deprecated
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "字段配置")
@SaturnEntity(name = "MdmColumnConfigRespVo", description = "字段配置")
public class MdmColumnConfigRespVo {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("菜单编码")
    private String parentCode;

    @ApiModelProperty("是否导出，1是0否")
    private String columnExport;
    
    @ApiModelProperty("菜单编码")
    private String functionCode;

    @ApiModelProperty("属性")
    private String field;

    @ApiModelProperty("标签")
    private String title;

    @ApiModelProperty("显示模式：true显示，false隐藏")
    private Boolean visible;

    @ApiModelProperty("新增页面时候编辑状态")
    private String editableInCreate;

    @ApiModelProperty("编辑页面编辑状态")
    private String editableInEdit;

    @ApiModelProperty("编辑页面显隐")
    private String visibleInEdit;

    @ApiModelProperty("查看页面显隐")
    private String visibleInLook;

    @ApiModelProperty("对齐方式")
    private String align;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("宽度")
    private String width;

    @ApiModelProperty("顺序")
    private Integer formorder;

    @ApiModelProperty("位置")
    private String fixed;

    @ApiModelProperty("是否展示省略号true展示，false不展示")
    private Boolean showOverflow;

    @ApiModelProperty("是否必填")
    private Boolean required;

    @ApiModelProperty("数据字典编码")
    private String dictCode;

    @ApiModelProperty("列样式")
    @SaturnColumn(description = "列样式")
    private String col;

    @ApiModelProperty("校验")
    @SaturnColumn(description = "校验")
    private String validate;

    @ApiModelProperty("事件")
    @SaturnColumn(description = "事件")
    private String formon;

    @ApiModelProperty("操作")
    @SaturnColumn(description = "操作")
    private String formoptions;


    @ApiModelProperty("属性")
    @SaturnColumn(description = "属性")
    private String props;

    @ApiModelProperty("刷新")
    @SaturnColumn(description = "刷新")
    private Boolean refresh;

    @ApiModelProperty("值")
    @SaturnColumn(description = "值")
    private String formvalue;

    @ApiModelProperty("控件")
    @SaturnColumn(description = "控件")
    private String className;

    @ApiModelProperty("字段是否搜索")
    private String search;

    @ApiModelProperty("请求地址")
    private String requestUrl;

    @ApiModelProperty("下拉框是否搜索")
    private String requestSearch;

    @ApiModelProperty("是否查看详情")
    private String editView;

    @ApiModelProperty("查询方式-1不作查询,0精确查询，1模糊查询，2范围查询，3左模糊查询，4右模糊查询，5小于，6小于等于，7大于，8大于等于")
    private String searchType;

    @ApiModelProperty("实体字段名称")
    private String entityFieldName;

    @ApiModelProperty("列表控件类型")
    private String formControl;
}