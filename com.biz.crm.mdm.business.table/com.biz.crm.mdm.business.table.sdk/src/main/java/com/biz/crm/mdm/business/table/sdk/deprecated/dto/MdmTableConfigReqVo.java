package com.biz.crm.mdm.business.table.sdk.deprecated.dto;

//import com.biz.crm.nebular.mdm.CrmExtTenVo;

import com.biz.crm.business.common.sdk.deprecated.vo.CrmExtTenVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * hefan：
 * TableConfigDto
 *
 * 页面配置请求vo
 *
 * <AUTHOR>
 * @date 2020-11-21 14:25:32
 */
@Deprecated
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MdmTableConfigReqVo", description = "页面配置")
public class MdmTableConfigReqVo extends CrmExtTenVo {

    @ApiModelProperty("ID集合")
    private List<String> ids;

    @ApiModelProperty("菜单编码")
    private String parentCode;

    @ApiModelProperty("功能编码")
    private String functionCode;

    @ApiModelProperty("是否有边框")
    private Boolean border;

    @ApiModelProperty("是否允许拖动列宽是否允许拖动列宽")
    private Boolean resizable;

    @ApiModelProperty("标签名称")
    private String showHeader;

    @ApiModelProperty("默认显示")
    private String emptyText;

    @ApiModelProperty("宽度")
    private String width;

    @ApiModelProperty("对齐方式")
    private String align;

    @ApiModelProperty("选中行")
    private Boolean highlightHoveRow;

    @ApiModelProperty("是否展示省略号")
    private Boolean showOverflow;

    @ApiModelProperty("高度")
    private String height;

    @ApiModelProperty("是否保持原始值状态")
    private Boolean keepSource;

}