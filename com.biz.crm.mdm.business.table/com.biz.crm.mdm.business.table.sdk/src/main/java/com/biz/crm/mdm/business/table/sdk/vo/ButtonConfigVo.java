package com.biz.crm.mdm.business.table.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * hefan:
 *  含图标信息的按钮
 *
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ButtonConfigVo", description = "含图标信息的按钮")
public class ButtonConfigVo {

  /**
   * 页面引擎侧的ID
   */
  @ApiModelProperty("页面引擎侧的ID")
  private String id;

  /**
   * 页面引擎侧的ID
   */
  @ApiModelProperty("页面引擎侧的ID")
  private String pageEngineButtonId;

  /**
   * 总控侧的ID
   */
  @ApiModelProperty("总控侧的ID")
  private String buttonId;

  /**
   * 按钮编码
   */
  @ApiModelProperty("按钮编码")
  private String code;

  /**
   * 前端的按钮组件编码
   */
  @ApiModelProperty("前端的按钮组件编码")
  private String buttonCode;

  @ApiModelProperty("按扭名称")
  private String buttonName;

  @ApiModelProperty("按钮类型:0表头按钮，1行按钮")
  private String buttonType;

  @ApiModelProperty("按钮类型名称:0表头按钮，1行按钮(数据字典button_type)")
  private String buttonTypeName;

  @ApiModelProperty("图标编码")
  private String iconCode;

  @ApiModelProperty("询问语")
  private String ask;

  @ApiModelProperty("功能查询地址")
  private String queryUrl;

  @ApiModelProperty("url")
  private String apiUrl;

  @ApiModelProperty("按钮操作类型")
  private String buttonOperationType;

  @ApiModelProperty("上级菜单编码")
  private String parentCode;

  @ApiModelProperty("功能列表编码")
  private String functionCode;

  @ApiModelProperty("排序")
  private Integer buttonOrder;

  @ApiModelProperty("功能编码")
  private String doCode;

  @ApiModelProperty("显示模式：true显示，false隐藏")
  private Boolean visible;

  @ApiModelProperty("导入按钮编码")
  private String configCode;

  // 往下--图标信息

  @ApiModelProperty("图标路径")
  private String iconUrl;

  @ApiModelProperty("图标名称")
  private String iconName;

  @ApiModelProperty("图标样式")
  private String iconStyle;

  @ApiModelProperty("图标效果")
  private String iconEffect;

  @ApiModelProperty("按钮方法")
  private String buttonMethod;

  @ApiModelProperty("指定关联的功能列表编码")
  private String assignFunctionCode;
}
