package com.biz.crm.mdm.business.table.sdk.deprecated.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * hefan:
 * ColumnConfigPersonalBatchDto
 * 字段配置个性设置请求vo
 *
 * <AUTHOR>
 * @date 2021-02-20 15:30:42
 */
@Deprecated
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MdmColumnConfigPersonalBatchReqVo", description = "字段配置个性设置")
public class MdmColumnConfigPersonalBatchReqVo {

    @ApiModelProperty("上级菜单编码")
    private String parentCode;

    @ApiModelProperty("功能菜单编码")
    private String functionCode;

    @ApiModelProperty("字段")
    private List<MdmColumnConfigPersonalReqVo> columnList;

}