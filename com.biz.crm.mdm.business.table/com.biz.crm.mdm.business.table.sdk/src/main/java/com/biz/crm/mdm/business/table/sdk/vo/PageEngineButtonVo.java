package com.biz.crm.mdm.business.table.sdk.vo;

import com.bizunited.nebula.competence.sdk.vo.ButtonVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 页面引擎 与 nebula 按钮整合时，使用的VO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PageEngineButtonVo" ,description = "页面引擎 与 nebula 按钮整合时，使用的VO")
public class PageEngineButtonVo extends ButtonVo {

  /**
   * 页面引擎侧的ID
   */
  @ApiModelProperty("页面引擎侧的ID")
  private String pageEngineButtonId;

  /**
   * 前端的按钮组件编码
   */
  @ApiModelProperty("前端的按钮组件编码")
  private String buttonCode;

  /**
   * 按钮编码
   */
  @ApiModelProperty("按钮编码")
  private String code;

  /**
   * 按钮名称
   */
  @ApiModelProperty("按钮名称")
  private String buttonName;

  @ApiModelProperty("按钮类型:0表头按钮，1行按钮")
  private String buttonType;

  @ApiModelProperty("图标编码")
  private String iconCode;

  /**
   * 询问语
   */
  @ApiModelProperty("询问语")
  private String ask;

  /**
   * 功能查询地址
   */
  @ApiModelProperty("功能查询地址")
  private String queryUrl;

  /**
   * url
   */
  @ApiModelProperty("url")
  private String apiUrl;

  /**
   * api url 请求映射
   */
  @ApiModelProperty("apiUrlRequestMapping")
  private String apiUrlRequestMapping;

  /**
   * 按钮操作类型
   */
  @ApiModelProperty("按钮操作类型")
  private String buttonOperationType;

  /**
   * 上级菜单编码
   */
  @ApiModelProperty("上级菜单编码")
  private String parentCode;

  /**
   * 功能列表编码
   */
  @ApiModelProperty("功能列表编码")
  private String functionCode;

  /**
   * 排序
   */
  @ApiModelProperty("排序")
  private Integer buttonOrder;

  /**
   * 功能编码
   */
  @ApiModelProperty("功能编码")
  private String doCode;

  /**
   * 显示模式：true显示，false隐藏
   */
  @ApiModelProperty("显示模式：true显示，false隐藏")
  private Boolean visible;

  /**
   * 导入按钮编码
   */
  @ApiModelProperty("导入按钮编码")
  private String configCode;

  /**
   * 指定关联的功能列表编码
   */
  @ApiModelProperty("指定关联的功能列表编码")
  private String assignFunctionCode;
}
