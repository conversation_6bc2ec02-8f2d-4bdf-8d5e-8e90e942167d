package com.biz.crm.mdm.business.table.sdk.deprecated.vo;
//import com.biz.crm.nebular.mdm.CrmExtTenVo;
//import com.biz.crm.nebular.mdm.button.MdmButtonReqVo;
//import com.biz.crm.nebular.mdm.button.MdmButtonRespVo;

import com.biz.crm.business.common.sdk.deprecated.vo.CrmExtTenVo;
import com.bizunited.nebula.saturn.engine.annotation.SaturnColumn;
import com.bizunited.nebula.saturn.engine.annotation.SaturnEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

//import com.bizunited.platform.saturn.engine.annotation.SaturnColumn;
//import com.bizunited.platform.saturn.engine.annotation.SaturnEntity;

//import com.biz.crm.nebular.mdm.pageconfig.MdmButtonConfigRespVo;
//import com.biz.crm.nebular.mdm.pageconfig.MdmColumnConfigRespVo;

/**
 * hefan:
 * TableConfigVo
 *
 * 页面配置返回vo
 *
 * <AUTHOR>
 * @date 2020-11-21 14:25:32
 */
@Deprecated
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "页面配置")
@SaturnEntity(name = "MdmTableConfigRespVo", description = "页面配置")
public class MdmTableConfigRespVo extends CrmExtTenVo {

    @ApiModelProperty("菜单编码")
    @SaturnColumn(description = "菜单编码")
    private String functionCode;

    @ApiModelProperty("是否有边框1是0否")
    @SaturnColumn(description = "是否有边框1是0否")
    private Boolean border;

    @ApiModelProperty("是否允许拖动列宽 是否允许拖动列宽1是0否")
    @SaturnColumn(description = "是否允许拖动列宽 是否允许拖动列宽1是0否")
    private Boolean resizable;

    @ApiModelProperty("标签名称")
    @SaturnColumn(description = "标签名称")
    private String showHeader;

    @ApiModelProperty("默认显示")
    @SaturnColumn(description = "默认显示")
    private String emptyText;

    @ApiModelProperty("宽度")
    @SaturnColumn(description = "宽度")
    private String width;

    @ApiModelProperty("对齐方式")
    @SaturnColumn(description = "对齐方式")
    private String align;

    @ApiModelProperty("选中行")
    @SaturnColumn(description = "选中行")
    private Boolean highlightHoveRow;

    @ApiModelProperty(" 是否展示省略号")
    @SaturnColumn(description = " 是否展示省略号")
    private Boolean showOverflow;

    @ApiModelProperty("高度")
    @SaturnColumn(description = "高度")
    private String height;

    @ApiModelProperty("是否保持原始值状态")
    @SaturnColumn(description = "是否保持原始值状态")
    private Boolean keepSource;

    @ApiModelProperty("列配置集合")
    private List<MdmColumnConfigRespVo> column;

    @ApiModelProperty("编辑配置")
    private String editConfig;

    private String treeConfig;

    @ApiModelProperty("搜索框配置")
    private String formConfig;

    @ApiModelProperty("后台请求地址")
    private String url;

    @ApiModelProperty("按钮数组")
    private List<MdmButtonConfigRespVo>  buttonVos;

}