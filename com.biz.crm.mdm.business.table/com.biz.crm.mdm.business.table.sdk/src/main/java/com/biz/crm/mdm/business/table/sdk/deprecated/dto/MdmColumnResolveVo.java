package com.biz.crm.mdm.business.table.sdk.deprecated.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字段解析返回vo
 *
 * <AUTHOR>
 * @date 2020-11-23 14:20
 **/
@Deprecated
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "字段解析返回vo")
//@SaturnEntity(name = "MdmColumnResolveRespVo", description = "字段解析返回vo")
public class MdmColumnResolveVo {

    @ApiModelProperty("类名称")
    private String className;

    @ApiModelProperty("类注释")
    private String classNote;

    @ApiModelProperty("类路径")
    private String classPath;
}
