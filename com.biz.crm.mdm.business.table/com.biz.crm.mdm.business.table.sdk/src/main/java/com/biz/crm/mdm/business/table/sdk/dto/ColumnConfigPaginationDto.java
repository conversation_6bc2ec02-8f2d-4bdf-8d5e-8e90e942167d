package com.biz.crm.mdm.business.table.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字段配置请求vo
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ColumnConfigPaginationDto", description = "字段配置分页DTO")
public class ColumnConfigPaginationDto {

  @ApiModelProperty("下级菜单编码")
  private String functionCode;

  @ApiModelProperty("属性")
  private String field;



}
