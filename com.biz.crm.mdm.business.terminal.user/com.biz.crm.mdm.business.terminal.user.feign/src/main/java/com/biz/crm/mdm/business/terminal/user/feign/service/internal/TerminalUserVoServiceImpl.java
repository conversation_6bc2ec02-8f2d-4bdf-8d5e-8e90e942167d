package com.biz.crm.mdm.business.terminal.user.feign.service.internal;

import com.biz.crm.mdm.business.terminal.user.feign.feign.TerminalUserVoFeign;
import com.biz.crm.mdm.business.terminal.user.sdk.dto.TerminalUserDto;
import com.biz.crm.mdm.business.terminal.user.sdk.dto.TerminalUserRelWeChatDto;
import com.biz.crm.mdm.business.terminal.user.sdk.dto.TerminalUserRelaTerminalDto;
import com.biz.crm.mdm.business.terminal.user.sdk.service.TerminalUserVoService;
import com.biz.crm.mdm.business.terminal.user.sdk.vo.TerminalUserRelWechatVo;
import com.biz.crm.mdm.business.terminal.user.sdk.vo.TerminalUserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 终端用户Vo接口实现类
 *
 * <AUTHOR>
 * @date 2022/2/22
 */
@Service("FeignTerminalUserVoServiceImpl")
public class TerminalUserVoServiceImpl implements TerminalUserVoService {

  @Autowired(required = false)
  private TerminalUserVoFeign terminalUserVoFeign;

  @Override
  public List<TerminalUserVo> findDetailsByIdsOrUserCodes(List<String> ids, List<String> userCodes) {
    return this.terminalUserVoFeign.findDetailsByUserCodes(userCodes).checkFeignResult();
  }

  @Override
  public List<TerminalUserVo> findDetailsByIdsOrUserNames(List<String> ids, List<String> userNames) {
    return this.terminalUserVoFeign.findDetailsByUserNames(userNames).checkFeignResult();
  }

  @Override
  public TerminalUserVo create(TerminalUserDto dto) {
    throw new UnsupportedOperationException();
  }

  @Override
  public TerminalUserVo update(TerminalUserDto dto) {
    throw new UnsupportedOperationException();
  }

  @Override
  public TerminalUserVo findByUserPhone(String userPhone) {
    return this.terminalUserVoFeign.findByUserPhone(userPhone).checkFeignResult();
  }

  @Override
  public TerminalUserVo findByUserName(String userName) {
    return this.terminalUserVoFeign.findByUserName(userName).checkFeignResult();
  }

  @Override
  public TerminalUserVo findByOpenid(String openid) {
    return terminalUserVoFeign.findByUserOpenid(openid).checkFeignResult();
  }

  @Override
  public TerminalUserRelWechatVo bindTerminalUserWxInfo(TerminalUserRelWeChatDto weChatDto) {
    return terminalUserVoFeign.bindWx(weChatDto).checkFeignResult();
  }

  @Override
  public void flagCurrent(TerminalUserRelaTerminalDto dto) {
    terminalUserVoFeign.flagCurrent(dto);
  }

  @Override
  public TerminalUserVo findByUserName(String userName, Boolean shareBenefits) {
    return null;
  }

  @Override
  public void deleteBatch(List<String> ids) {

  }
}
