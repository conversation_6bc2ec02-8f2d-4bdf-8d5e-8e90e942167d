package com.biz.crm.mdm.business.terminal.user.local.service.internal;

import com.biz.crm.mdm.business.terminal.user.local.entity.TerminalUser;
import com.biz.crm.mdm.business.terminal.user.local.entity.TerminalUserRelaTerminal;
import com.biz.crm.mdm.business.terminal.user.local.repository.TerminalUserRelaTerminalRepository;
import com.biz.crm.mdm.business.terminal.user.local.service.TerminalUserRelaTerminalService;
import com.biz.crm.mdm.business.terminal.user.local.service.TerminalUserService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 终端用户与终端关联终端表(TerminalUserRTerminal)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-20 16:36:39
 */
@Service("terminalUserRelaTerminalService")
public class TerminalUserRelaTerminalServiceImpl implements TerminalUserRelaTerminalService {

  @Autowired(required = false) private TerminalUserRelaTerminalRepository terminalUserRelaTerminalRepository;

  @Autowired(required = false) private TerminalUserService terminalUserService;

  /**
   * 基于数据库执行的数据视图执行内容缓存（最多500毫秒）
   */
  private static volatile Cache<String, List<TerminalUserRelaTerminal>> cache = null;

  public TerminalUserRelaTerminalServiceImpl(){
    if(cache == null) {
      synchronized (TerminalUserRelaTerminalServiceImpl.class) {
        while(cache == null) {
          cache = CacheBuilder.newBuilder()
                  .initialCapacity(10000)
                  .expireAfterWrite(500, TimeUnit.MILLISECONDS)
                  .maximumSize(100000)
                  .build();
        }
      }
    }
  }

  @Override
  public List<TerminalUserRelaTerminal> findByUserCodes(List<String> userCodeList) {
    if (CollectionUtils.isEmpty(userCodeList)) {
      return Lists.newLinkedList();
    }
    String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), userCodeList);
    List<TerminalUserRelaTerminal> graph = cache.getIfPresent(cacheKey);
    if (graph == null) {
      graph = this.terminalUserRelaTerminalRepository.findByUserCodes(userCodeList);
      cache.put(cacheKey, graph);
    }
    return graph;
  }

  @Override
  @Transactional
  public void saveBatch(List<TerminalUserRelaTerminal> list, String userCode) {
    Validate.notBlank(userCode, "终端用户编码不能为空");
    terminalUserRelaTerminalRepository.deleteByUserCodes(Lists.newArrayList(userCode));
    if (CollectionUtils.isEmpty(list)) {
      return;
    }
    Optional<TerminalUserRelaTerminal> first =
        list.stream()
            .filter(
                a ->
                    StringUtils.isBlank(a.getTerminalCode())
                        || StringUtils.isBlank(a.getUserCode())
                        || !userCode.equals(a.getUserCode()))
            .findFirst();
    Validate.isTrue(!first.isPresent(), "终端编码、用户编码不能为空,且必须属于同一终端用户");
    Set<String> set = Sets.newHashSet();
    list.forEach(
        a -> Validate.isTrue(set.add(a.getTerminalCode()), "存在重复的记录信息"));

    for (TerminalUserRelaTerminal item : list) {
      item.setId(null);
      item.setTenantCode(TenantUtils.getTenantCode());
    }
    terminalUserRelaTerminalRepository.saveBatch(list);
  }

  @Override
  public Integer countByTerminalCodes(List<String> terminalCodeList) {
    if (CollectionUtils.isEmpty(terminalCodeList)) {
      return 0;
    }
    // 查询关联关系
    List<TerminalUserRelaTerminal> byTerminalCodes =
        terminalUserRelaTerminalRepository.findByTerminalCodes(terminalCodeList);
    if (CollectionUtils.isEmpty(byTerminalCodes)) {
      return 0;
    }
    // 从关联关系中获取用户编码
    List<String> userCodes =
        byTerminalCodes.stream()
            .map(TerminalUserRelaTerminal::getUserCode)
            .collect(Collectors.toList());
    // 查询用户
    List<TerminalUser> terminalUsers = this.terminalUserService.findByUserCodes(userCodes);
    if (CollectionUtils.isEmpty(terminalUsers)) {
      return 0;
    }
    return 1;
  }
}
