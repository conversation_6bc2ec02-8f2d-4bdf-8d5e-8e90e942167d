<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.terminal.user.local.mapper.TerminalUserMapper">

  <!--分页查询-->
  <select id="findByConditions"
    resultType="com.biz.crm.mdm.business.terminal.user.local.entity.TerminalUser">
    select *
    from mdm_terminal_user
    where 1=1
      and tenant_code=#{dto.tenantCode}
    <if test="dto.userCode != null and dto.userCode != ''">
      <bind name="likeUserCode" value="'%' + dto.userCode + '%'"/>
      and user_code like #{likeUserCode}
    </if>
    <if test="dto.userName != null and dto.userName != ''">
      <bind name="likeUserName" value="'%' + dto.userName + '%'"/>
      and user_name like #{likeUserName}
    </if>
    <if test="dto.fullName != null and dto.fullName != ''">
      <bind name="likeFullName" value="'%' + dto.fullName + '%'"/>
      and full_name like #{likeFullName}
    </if>
    <if test="dto.userPhone != null and dto.userPhone != ''">
      <bind name="likeUserPhone" value="'%' + dto.userPhone + '%'"/>
      and user_phone like #{likeUserPhone}
    </if>
    <if test="dto.enableStatus != null and dto.enableStatus != ''">
      and enable_status=#{dto.enableStatus}
    </if>
    <if test="dto.delFlag != null and dto.delFlag != ''">
      and del_flag=#{dto.delFlag}
    </if>
    order by create_time desc,user_code asc
  </select>

</mapper>
