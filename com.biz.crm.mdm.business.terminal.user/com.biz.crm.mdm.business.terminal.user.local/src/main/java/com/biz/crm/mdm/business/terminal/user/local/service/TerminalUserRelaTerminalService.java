package com.biz.crm.mdm.business.terminal.user.local.service;

import com.biz.crm.mdm.business.terminal.user.local.entity.TerminalUserRelaTerminal;
import java.util.List;

/**
 * 终端用户与终端关联终端表(TerminalUserRTerminal)表服务接口
 *
 * <AUTHOR>
 * @since 2021-10-20 16:36:39
 */
public interface TerminalUserRelaTerminalService {

  /**
   * 根据用户编码集合获取集合
   *
   * @param userCodeList
   * @return
   */
  List<TerminalUserRelaTerminal> findByUserCodes(List<String> userCodeList);

  /**
   * 批量保存关联终端信息
   *
   * @param list
   * @param userCode
   */
  void saveBatch(List<TerminalUserRelaTerminal> list, String userCode);

  /**
   * 根据终端编码获取绑定用户的数量信息
   *
   * @param terminalCodeList
   * @return
   */
  Integer countByTerminalCodes(List<String> terminalCodeList);
}
