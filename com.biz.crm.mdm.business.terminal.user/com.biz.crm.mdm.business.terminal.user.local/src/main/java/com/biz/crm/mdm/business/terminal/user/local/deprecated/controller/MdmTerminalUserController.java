package com.biz.crm.mdm.business.terminal.user.local.deprecated.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.deprecated.model.PageResult;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.terminal.user.local.entity.TerminalUser;
import com.biz.crm.mdm.business.terminal.user.local.service.TerminalUserService;
import com.biz.crm.mdm.business.terminal.user.sdk.deprecated.MdmTerminalUserPageReqVo;
import com.biz.crm.mdm.business.terminal.user.sdk.dto.TerminalUserDto;
import com.biz.crm.mdm.business.terminal.user.sdk.dto.TerminalUserPaginationDto;
import com.biz.crm.mdm.business.terminal.user.sdk.dto.TerminalUserResetPasswordDto;
import com.biz.crm.mdm.business.terminal.user.sdk.service.TerminalUserVoService;
import com.biz.crm.mdm.business.terminal.user.sdk.vo.TerminalUserVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/10/20
 */
@Slf4j
@RestController
@RequestMapping("/mdmTerminalUserController")
@Api(tags = "MDM-用户管理（终端用户）")
@Deprecated
public class MdmTerminalUserController {

  @Autowired(required = false)
  private TerminalUserService terminalUserService;

  @Autowired(required = false)
  private TerminalUserVoService terminalUserVoService;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;


  @ApiOperation(value = "查询分页列表")
  @PostMapping("/pageList")
  public Result<PageResult<TerminalUser>> pageList(
      @RequestBody MdmTerminalUserPageReqVo reqVo) {
    try {
      reqVo = Optional.ofNullable(reqVo).orElse(new MdmTerminalUserPageReqVo());
      TerminalUserPaginationDto dto = nebulaToolkitService
          .copyObjectByWhiteList(reqVo, TerminalUserPaginationDto.class, HashSet.class,
              ArrayList.class);
      Page<TerminalUser> page = terminalUserService
          .findByConditions(
              PageRequest.of(reqVo.getPageNum(), reqVo.getPageSize()), dto);
      PageResult<TerminalUser> pageResult = PageResult.<TerminalUser>builder()
          .data(page.getRecords())
          .count(page.getTotal())
          .build();
      return Result.ok(pageResult);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "新增")
  @PostMapping("/save")
  public Result<?> save(@RequestBody TerminalUserDto reqVo) {
    terminalUserVoService.create(reqVo);
    return Result.ok();
  }

  @ApiOperation(value = "终端用户详情-根据ID查询", httpMethod = "GET")
  @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = false, dataType = "String", paramType = "query")})
  @GetMapping("/detailById")
  public Result<TerminalUserVo> queryById(
      @RequestParam(value = "id", required = true) String id) {
    List<String> ids = Lists.newLinkedList();
    if (StringUtils.isNotBlank(id)) {
      ids.add(id);
    }
    TerminalUserVo vo = null;
    List<TerminalUserVo> list = terminalUserVoService.findDetailsByIdsOrUserCodes(ids, null);
    if (CollectionUtils.isNotEmpty(list)) {
      vo = list.stream().findFirst().orElse(null);
    }
    return Result.ok(vo);
  }

  @ApiOperation(value = "更新")
  @PostMapping("/update")
  public Result<?> update(@RequestBody TerminalUserDto reqVo) {
    terminalUserVoService.update(reqVo);
    return Result.ok("修改成功");
  }

  @ApiOperation(value = "删除", httpMethod = "POST")
  @ApiImplicitParams({@ApiImplicitParam(name = "ids", value = "id集合", required = true, paramType = "body")})
  @PostMapping("/delete")
  public Result<?> delete(@RequestBody List<String> ids) {
    terminalUserService.updateDelFlagByIds(ids);
    return Result.ok("删除成功");
  }


  @ApiOperation(value = "启用", httpMethod = "POST")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "ids", value = "id集合", required = true, paramType = "body")})
  @PostMapping("/enable")
  public Result<?> enable(@RequestBody List<String> ids) {
    terminalUserService.enableBatch(ids);
    return Result.ok("启用成功");
  }


  @ApiOperation(value = "禁用", httpMethod = "POST")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "ids", value = "id集合", required = true, paramType = "body")})
  @PostMapping("/disable")
  public Result<?> disable(@RequestBody List<String> ids) {
    terminalUserService.disableBatch(ids);
    return Result.ok("禁用成功");
  }

  @ApiOperation(value = "强制批量修改密码", httpMethod = "POST")
  @PostMapping("/forceChangePassword")
  public Result<?> forceChangePassword(@RequestBody TerminalUserResetPasswordDto reqVo) {
    terminalUserService.updatePassword(reqVo);
    return Result.ok("修改成功");
  }
}
