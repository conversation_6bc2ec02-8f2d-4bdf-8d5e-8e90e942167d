package com.biz.crm.mdm.business.terminal.user.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.mdm.business.terminal.user.local.entity.TerminalUser;
import com.biz.crm.mdm.business.terminal.user.local.repository.TerminalUserRepository;
import com.biz.crm.mdm.business.terminal.user.local.service.TerminalUserService;
import com.biz.crm.mdm.business.terminal.user.sdk.constant.TerminalUserConstant;
import com.biz.crm.mdm.business.terminal.user.sdk.dto.TerminalUserPaginationDto;
import com.biz.crm.mdm.business.terminal.user.sdk.dto.TerminalUserResetPasswordDto;
import com.biz.crm.mdm.business.terminal.user.sdk.event.TerminalUserEventListener;
import com.biz.crm.mdm.business.terminal.user.sdk.vo.TerminalUserVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.Aes128Utils;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

/**
 * 终端用户(TerminalUser)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-20 16:35:05
 */
@Service("terminalUserService")
public class TerminalUserServiceImpl implements TerminalUserService {

  @Autowired(required = false) private TerminalUserRepository terminalUserRepository;

  @Autowired(required = false)
  @Lazy
  private List<TerminalUserEventListener> eventListeners;

  @Autowired(required = false)
  private GenerateCodeService generateCodeService;

  @Autowired(required = false)
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 基于数据库执行的数据视图执行内容缓存（最多500毫秒）
   */
  private static volatile Cache<String, List<TerminalUser>> cache = null;

  public TerminalUserServiceImpl(){
    if(cache == null) {
      synchronized (TerminalUserServiceImpl.class) {
        while(cache == null) {
          cache = CacheBuilder.newBuilder()
                  .initialCapacity(10000)
                  .expireAfterWrite(500, TimeUnit.MILLISECONDS)
                  .maximumSize(100000)
                  .build();
        }
      }
    }
  }

  @Override
  public Page<TerminalUser> findByConditions(Pageable pageable, TerminalUserPaginationDto dto) {
    pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
    dto = Optional.ofNullable(dto).orElse(new TerminalUserPaginationDto());
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    Page<TerminalUser> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return terminalUserRepository.findByConditions(page, dto);
  }

  @Override
  public TerminalUser findDetailById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    return terminalUserRepository.findById(id);
  }

  @Override
  @Transactional
  public TerminalUser create(TerminalUser terminalUser) {
    Validate.notNull(terminalUser, "终端用户信息缺失");
    Validate.notBlank(terminalUser.getUserName(), "终端用户账户不能为空");
    // 如果userCode为空需要期初一个编码
    if (StringUtils.isEmpty(terminalUser.getUserCode())) {
      terminalUser.setUserCode(
          generateCodeService.generateCode(TerminalUserConstant.TERMINAL_USER_CODE));
    }
    this.createValidation(terminalUser);
    // 设置基础值
    terminalUser.setTenantCode(TenantUtils.getTenantCode());
    terminalUser.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    terminalUser.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    terminalUserRepository.saveOrUpdate(terminalUser);
    return terminalUser;
  }

  @Override
  @Transactional
  public TerminalUser update(TerminalUser terminalUser) {
    // 校验
    this.updateValidation(terminalUser);
    //新增租户编号
    terminalUser.setTenantCode(TenantUtils.getTenantCode());
    terminalUserRepository.saveOrUpdate(terminalUser);
    return terminalUser;
  }

  @Override
  @Transactional
  public void enableBatch(List<String> ids) {
    Validate.isTrue(CollectionUtils.isNotEmpty(ids), "id集合不能为空");
    terminalUserRepository.updateEnableStatusByIds(ids, EnableStatusEnum.ENABLE);
    onEnableOrDisable(ids, EnableStatusEnum.ENABLE);
  }

  @Override
  @Transactional
  public void disableBatch(List<String> ids) {
    Validate.isTrue(CollectionUtils.isNotEmpty(ids), "id集合不能为空");
    terminalUserRepository.updateEnableStatusByIds(ids, EnableStatusEnum.DISABLE);
    onEnableOrDisable(ids, EnableStatusEnum.DISABLE);
  }

  @Override
  @Transactional
  public void updateDelFlagByIds(List<String> ids) {
    Validate.isTrue(CollectionUtils.isNotEmpty(ids), "id集合不能为空");
    terminalUserRepository.updateDelFlagByIds(ids);
    if (CollectionUtils.isEmpty(eventListeners)) {
      return;
    }
    List<TerminalUserVo> voList = findVoListByIds(ids);
    if (CollectionUtils.isEmpty(voList)) {
      return;
    }
    for (TerminalUserEventListener eventListener : eventListeners) {
      eventListener.onDelete(voList);
    }
  }

  @Override
  public List<TerminalUser> findDetailsByIdsOrUserNames(List<String> ids, List<String> userNames) {
    if (CollectionUtils.isEmpty(ids) && CollectionUtils.isEmpty(userNames)) {
      return Lists.newLinkedList();
    }
    StringBuilder sb = new StringBuilder();
    if (CollectionUtils.isNotEmpty(ids)) {
      sb.append(StringUtils.join(ids));
    }
    if (CollectionUtils.isNotEmpty(userNames)) {
      sb.append(StringUtils.join(userNames));
    }
    String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), sb.toString());
    List<TerminalUser> graph = cache.getIfPresent(cacheKey);
    if (graph == null) {
      graph = this.terminalUserRepository.findDetailsByIdsOrUserNames(ids, userNames);
      cache.put(cacheKey, graph);
    }
    return graph;
  }

  @Override
  public List<TerminalUser> findDetailsByIdsOrUserCodes(List<String> ids, List<String> userCodes) {
    if (CollectionUtils.isEmpty(ids) && CollectionUtils.isEmpty(userCodes)) {
      return Lists.newLinkedList();
    }
    StringBuilder sb = new StringBuilder();
    if (CollectionUtils.isNotEmpty(ids)) {
      sb.append(StringUtils.join(ids));
    }
    if (CollectionUtils.isNotEmpty(userCodes)) {
      sb.append(StringUtils.join(userCodes));
    }
    String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), sb.toString());
    List<TerminalUser> graph = cache.getIfPresent(cacheKey);
    if (graph == null) {
      graph = this.terminalUserRepository.findDetailsByIdsOrUserCodes(ids, userCodes);
      cache.put(cacheKey, graph);
    }
    return graph;
  }

  @Override
  @Transactional
  public void updatePassword(TerminalUserResetPasswordDto dto) {
    Validate.notNull(dto, "参数不能为空");
    Validate.isTrue(CollectionUtils.isNotEmpty(dto.getIds()), "终端用户id集合不能为空");
    // 对密码进行 md5 加密
    String password =
        Aes128Utils.decrypt(
            dto.getPassword(),
            TerminalUserConstant.ENCRYPT_KEY,
            Aes128Utils.EncodeType.CBC,
            Aes128Utils.Padding.PKCS_7_PADDING);
    String md5Password = DigestUtils.md5DigestAsHex(password.getBytes(StandardCharsets.UTF_8));
    dto.setPassword(md5Password);
    terminalUserRepository.updatePassword(dto);
  }

  /**
   * 根据用户编码集合查询用户
   *
   * @param userCodes
   * @return
   */
  @Override
  public List<TerminalUser> findByUserCodes(List<String> userCodes) {
    if (CollectionUtils.isEmpty(userCodes)) {
      return Lists.newLinkedList();
    }
    String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), userCodes);
    List<TerminalUser> graph = cache.getIfPresent(cacheKey);
    if (graph == null) {
      graph = this.terminalUserRepository.findByUserCodes(userCodes);
      cache.put(cacheKey, graph);
    }
    return graph;
  }

  /**
   * 发送启用禁用变更通知
   *
   * @param ids
   * @param enableStatusEnum
   */
  private void onEnableOrDisable(List<String> ids, EnableStatusEnum enableStatusEnum) {
    if (CollectionUtils.isEmpty(eventListeners)) {
      return;
    }
    List<TerminalUserVo> voList = findVoListByIds(ids);
    if (CollectionUtils.isEmpty(voList)) {
      return;
    }
    for (TerminalUserEventListener event : eventListeners) {
      if (enableStatusEnum.equals(EnableStatusEnum.ENABLE)) {
        event.onEnable(voList);
      } else if (enableStatusEnum.equals(EnableStatusEnum.DISABLE)) {
        event.onDisable(voList);
      }
    }
  }

  /**
   * 只有主表信息，不包含扩展信息
   *
   * @param ids
   * @return
   */
  private List<TerminalUserVo> findVoListByIds(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newLinkedList();
    }
    List<TerminalUser> list = this.findDetailsByIdsOrUserCodes(ids, null);
    if (CollectionUtils.isEmpty(list)) {
      return Lists.newLinkedList();
    }
    return (List<TerminalUserVo>)
        this.nebulaToolkitService.copyCollectionByWhiteList(
            list, TerminalUser.class, TerminalUserVo.class, HashSet.class, ArrayList.class);
  }

  /**
   * 创建时校验
   *
   * @param terminalUser
   */
  private void createValidation(TerminalUser terminalUser) {
    // 校验用户账号或编码
    Integer count =
        this.terminalUserRepository.countByUserCodeOrUserNameCoverDel(
            terminalUser.getUserCode(), terminalUser.getUserName());
//    Validate.isTrue(null == count || 1 > count, "用户账号或编码已存在");
    Validate.isTrue(null == count || 1 >= count, "用户账号或编码已存在");
    //如果账号存在则用新注册终端用户信息更新被删除的终端用户，并修改删除状态
    if( count == 1 ){
      TerminalUser terminalUserOld = this.terminalUserRepository.findByUserCodeOrUserNameCoverDel(
              terminalUser.getUserCode(), terminalUser.getUserName()).get(0);
      // 这个终端用户必须时删除状态才触发覆盖逻辑。
      Validate.isTrue(DelFlagStatusEnum.DELETE.getCode().equals(terminalUserOld.getDelFlag()), "用户账号或编码已存在");
      terminalUser.setId(terminalUserOld.getId());
    }
    // 校验电话
    Validate.notBlank(terminalUser.getUserPhone(), "终端用户电话不能为空");
    TerminalUser terminalUserByPhone =
        this.terminalUserRepository.findUserByPhone(terminalUser.getUserPhone());
    Validate.isTrue(Objects.isNull(terminalUserByPhone) || terminalUserByPhone.getId().equals(terminalUser.getId()), "电话号码已经被占用");
    // 对密码进行 md5 加密
    String password =
        Aes128Utils.decrypt(
            terminalUser.getUserPassword(),
            TerminalUserConstant.ENCRYPT_KEY,
            Aes128Utils.EncodeType.CBC,
            Aes128Utils.Padding.PKCS_7_PADDING);
    String md5Password = DigestUtils.md5DigestAsHex(password.getBytes(StandardCharsets.UTF_8));
    terminalUser.setUserPassword(md5Password);
  }

  /**
   * 更新时校验
   *
   * @param terminalUser
   */
  private void updateValidation(TerminalUser terminalUser) {
    Validate.notNull(terminalUser, "终端用户信息缺失");
    Validate.notBlank(terminalUser.getUserName(), "终端用户账户不能为空");
    Validate.isTrue(StringUtils.isNotBlank(terminalUser.getId()), "id不能为空");
    Validate.notBlank(terminalUser.getUserPhone(), "终端用户电话不能为空");
    // 校验电话
    TerminalUser terminalUserByPhone =
        this.terminalUserRepository.findUserByPhone(terminalUser.getUserPhone());
    if (!Objects.isNull(terminalUserByPhone)
        && !(terminalUserByPhone.getId().equals(terminalUser.getId()))) {
      throw new IllegalArgumentException("电话号码已经被占用");
    }
    String currentId = terminalUser.getId();
    TerminalUser current = terminalUserRepository.findById(currentId);
    Validate.notNull(current, "修改信息不存在");
    Validate.isTrue(terminalUser.getUserCode().equals(current.getUserCode()), "编码不能修改");
    Validate.isTrue(terminalUser.getUserName().equals(current.getUserName()), "账号不能修改");

    if (StringUtils.isNotBlank(terminalUser.getUserPassword())) {
      // 对密码进行 md5 加密
      String password =
          Aes128Utils.decrypt(
              terminalUser.getUserPassword(),
              TerminalUserConstant.ENCRYPT_KEY,
              Aes128Utils.EncodeType.CBC,
              Aes128Utils.Padding.PKCS_7_PADDING);
      if (!password.equals(current.getUserPassword())) {
        // 如果不是原来的密码需要md5加密
        String md5Password = DigestUtils.md5DigestAsHex(password.getBytes(StandardCharsets.UTF_8));
        terminalUser.setUserPassword(md5Password);
      } else {
        // 否则还是原来的密码
        terminalUser.setUserPassword(current.getUserPassword());
      }
    } else {
      // 否则还是原来的密码
      terminalUser.setUserPassword(current.getUserPassword());
    }
  }
}
