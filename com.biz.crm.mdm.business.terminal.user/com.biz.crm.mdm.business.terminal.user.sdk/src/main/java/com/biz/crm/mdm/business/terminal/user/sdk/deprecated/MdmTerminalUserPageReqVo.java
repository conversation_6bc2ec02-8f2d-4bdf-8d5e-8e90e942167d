package com.biz.crm.mdm.business.terminal.user.sdk.deprecated;

import com.biz.crm.business.common.sdk.deprecated.vo.CrmExtTenVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 终端用户列表查询请求VO
 * <AUTHOR>
 * @date 2021/2/3
 * @time 20:22
 **/
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MdmTerminalUserPageReqVo", description = "终端用户列表查询请求VO")
@Deprecated
public class MdmTerminalUserPageReqVo extends CrmExtTenVo {
  private static final long serialVersionUID = 6762798472664432526L;

  @ApiModelProperty("用户账号登录信息")
  private String userName;

  @Deprecated
  @ApiModelProperty("用户编码")
  private String userCode;

  @ApiModelProperty("用户类型")
  private String userType;

  @ApiModelProperty("性别")
  private String gender;

  @ApiModelProperty("电话")
  private String userPhone;

  @ApiModelProperty("人员姓名")
  private String fullName;

  @ApiModelProperty("生效时间")
  private String startTime;

  @ApiModelProperty("失效时间")
  private String endTime;

  @ApiModelProperty("邮箱")
  private String email;

  @ApiModelProperty("锁定状态")
  private String lockState;

  @ApiModelProperty("最后一次登录时间")
  private String lastLoginTime;

  @ApiModelProperty("列表查询条件：终端编码")
  private String terminalCode;

  @ApiModelProperty("列表查询条件：终端名称")
  private String terminalName;

  @ApiModelProperty("列表查询条件：角色编码")
  private String roleCode;

  @ApiModelProperty("列表查询条件：角色名称")
  private String roleName;

  @ApiModelProperty("工号")
  private String jobCode;

  @ApiModelProperty("员工类型")
  private String employeeType;

  @ApiModelProperty("员工状态")
  private String employeeStatus;

  @ApiModelProperty("身份证号码")
  private String identityCardNumber;

  @ApiModelProperty("身份证地址")
  private String identityCardAddress;

  @ApiModelProperty("民族")
  private String nationality;

  @ApiModelProperty("现住址")
  private String currentAddress;

  @ApiModelProperty("政治面貌")
  private String politicalAffiliation;

}
