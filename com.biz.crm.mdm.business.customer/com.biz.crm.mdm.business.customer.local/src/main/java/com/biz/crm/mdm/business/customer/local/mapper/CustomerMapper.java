package com.biz.crm.mdm.business.customer.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.mapper.BusinessBaseMapper;
import com.biz.crm.mdm.business.customer.local.entity.CustomerEntity;
import com.biz.crm.mdm.business.customer.local.model.MultipleConditionModel;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerDto;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerQueryDto;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerSelectDto;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 客户的mybatis-plus接口类 {@link CustomerEntity}
 *
 * <AUTHOR>
 * @date 2021/9/28
 */
public interface CustomerMapper extends BusinessBaseMapper<CustomerEntity> {

    /**
     * 客户信息下拉框分页列表
     *
     * @param page 分页信息
     * @param dto  分页参数dto
     * @return 客户信息下拉框分页列表
     */
    Page<CustomerEntity> findByCustomerSelectDto(@Param("page") Page<CustomerEntity> page, @Param("dto") CustomerSelectDto dto);


    /**
     * 分页查询
     *
     * @param page 分页信息
     * @param dto  分页参数dto
     * @return 查询结果
     */
    Page<CustomerEntity> findByConditions(@Param("page") Page<CustomerEntity> page, @Param("dto") CustomerDto dto);


    /**
     * 通过ID或者编码获取客户信息详情(包含关联数据)
     *
     * @param id           客户ID
     * @param customerCode 客户编码
     * @param tenantCode   租户编码
     * @return 客户信息详情(包含关联数据)
     */
    CustomerEntity findDetailsByIdOrCode(@Param("id") String id, @Param("customerCode") String customerCode, @Param("tenantCode") String tenantCode);

    /**
     * 多条件查询客户列表
     *
     * @param model 条件model
     * @return 客户列表
     */
    List<CustomerEntity> findByMultipleConditionModel(@Param("model") MultipleConditionModel model);


    /**
     * 根据企业组织获取对应的客户信息
     *
     * @param orgCodeList
     * @param delFlag
     * @param tenantCode
     * @return
     */
    List<CustomerEntity> findByOrgCodes(@Param("list") List<String> orgCodeList, @Param("delFlag") String delFlag, @Param("tenantCode") String tenantCode);

    /**
     * 上级客户信息下拉框分页列表
     *
     * @param page
     * @param tenantCode
     * @return
     */
    Page<CustomerEntity> findByParentCustomerIsNull(Page<CustomerEntity> page, @Param("status") String status, @Param("tenantCode") String tenantCode);

    /**
     * 根据标签id查询
     *
     * @param page
     * @param tagId
     * @return
     */
    Page<CustomerEntity> findByTagId(Page<CustomerEntity> page, @Param("tagId") String tagId, @Param("tenantCode") String tenantCode);

    /**
     * 根据组织集合、渠道集合、标签集合查询客户编码集合
     * 参数非必填项所以不需要进行参数校验
     *
     * @param orgCodes     组织集合
     * @param channelCodes 渠道集合
     * @param tags         标签集合
     * @param tenantCode
     * @return 客户编码集合
     */
    Set<String> findCustomerCodesByOrgCodesAndChannelsAndTags(@Param("orgCodes") List<String> orgCodes, @Param("channelCodes") List<String> channelCodes, @Param("tags") List<String> tags, @Param("tenantCode") String tenantCode);

    /**
     * 下级客户信息分页列表
     *
     * @param page
     * @param tenantCode
     * @param customerCode
     * @return
     */
    Page<CustomerEntity> findChildrenByCustomerCode(Page<CustomerEntity> page, @Param("tenantCode") String tenantCode, @Param("customerCode") String customerCode);

    /**
     * 获取经销商编码集合
     *
     * @param dto
     * @param tenantCode
     * @return
     */
    Set<String> findByCustomerQueryDto(
            @Param("dto") CustomerQueryDto dto, @Param("tenantCode") String tenantCode);

    /**
     * 根据 客户编码 、组织包含、组织不包含 查询
     *
     * @param customerCode 客户代码
     * @param orgCodeIn    组织代码
     * @param orgCodeNotIn org代码不
     * @param delFlag      代码
     * @param tenantCode   租户代码
     * @return {@link List}<{@link CustomerEntity}>
     */
    List<CustomerEntity> findByCustomer7OrgIn7OrgNotIn(
            @Param("customerCode") String customerCode,
            @Param("orgCodeIn") List<String> orgCodeIn,
            @Param("orgCodeNotIn") List<String> orgCodeNotIn,
            @Param("delFlag") String delFlag,
            @Param("tenantCode") String tenantCode
    );

    /**
     * 查询当前用户当前职位关联的客户信息
     *
     * @param userName
     * @param postCode
     * @param tenantCode
     * @return
     */
    List<CustomerEntity> findByUserNameAndPostCode(@Param("userName") String userName, @Param("postCode") String postCode, @Param("tenantCode") String tenantCode);

    /**
     * 根据组织编码集及渠道等条件查询客户信息
     *
     * @param orgCodes     组织编码
     * @param channelCodes 渠道编码
     * @param tags         标签集合
     * @return
     */
    Set<CustomerEntity> findCustomersByOrgCodesAndChannelsAndTags(@Param("orgCodes") List<String> orgCodes, @Param("channelCodes") List<String> channelCodes, @Param("tags") List<String> tags, @Param("tenantCode") String tenantCode);

    /****
     * 更新经销商的分利状态
     * @param customerList
     * @return
     */
    int unFlagCustomerShareBenefits(@Param("customerLists") List<String> customerList);

    /**
     * 条件查询分利经销商编码集合
     *
     * @param dto
     * @return
     */
    Set<String> findByCustomerDto(@Param("dto") CustomerSelectDto dto);

    /**
     * 条件查询经销商关联信息
     *
     * @param dto
     * @return
     */
    List<CustomerVo> findCustomerDetailsByDto(@Param("dto") CustomerSelectDto dto);

    /**
     * 基于编码查询客户信息
     *
     * @param customerCodes
     * @param shareBenefits
     * @param tenantCode
     * @return
     */
    List<CustomerVo> findCustomerByCodes(
            @Param("customerCodes") Set<String> customerCodes,
            @Param("shareBenefits") Boolean shareBenefits,
            @Param("tenantCode") String tenantCode);

    List<CustomerVo> findCustomerByCustomerNameList(@Param("tenantCode") String tenantCode, @Param("customerNameList") List<String> customerNameList);

    /**
     * SAP推送的客户销售属性更新
     *
     * @param updateList
     * @return
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/29 18:41
     */
    void updateCustomerSalesBatchXml(@Param("dtoList") List<CustomerEntity> updateList);

    /**
     * 条件查询，带距离分页
     *
     * @param page
     * @param dto
     * @return
     */
    Page<CustomerVo> findCustomerByConditions(Page<CustomerVo> page, @Param("dto") CustomerQueryDto dto);

    List<CustomerVo> findCusRelateTerminal(@Param("customerCodes") List<String> customerCodes,@Param("terminalTag") String terminalTag);

    List<CustomerEntity> findAllowSaleCustomerByChannels(@Param("channelCodes") Set<String> channelCodes);

    void updateCustomerActiveState(@Param("dtoList") List<CustomerDto> dtoList);


    Set<String> findCustomerCodesByCondition(@Param("dto") CustomerSelectDto dto);

    List<CustomerVo> loadCacheCustomer(@Param("tenantCode") String tenantCode, @Param("enableStatus") String enableStatus);

    List<String> findCustomerByUserNames(@Param("userNames") List<String> userNames, @Param("tenantCode") String tenantCode);

    List<CustomerVo> findCustomerListByOrgCodes(@Param("orgCode")String orgCode);
}
