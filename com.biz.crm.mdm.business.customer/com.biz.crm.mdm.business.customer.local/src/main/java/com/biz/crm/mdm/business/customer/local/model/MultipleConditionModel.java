package com.biz.crm.mdm.business.customer.local.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 多条件model
 *
 * <AUTHOR>
 * @date 2021/11/11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MultipleConditionModel", description = "多条件model")
public class MultipleConditionModel {

  /**
   * 租户编号
   */
  @ApiModelProperty("租户编号")
  private String tenantCode;

  /***
   * 企业组织编码集合
   */
  @ApiModelProperty("企业组织编码集合")
  private List<String> orgCodes;

  /***
   * 客户组织编码集合
   */
  @ApiModelProperty("客户组织编码集合")
  private List<String> customerOrgCodes;

  /***
   * 对接人编码集合
   */
  @ApiModelProperty("对接人编码集合")
  private List<String> dockingPositionCodes;

  /**
   * 流程状态
   */
  @ApiModelProperty("流程状态")
  private String processStatus;


}
