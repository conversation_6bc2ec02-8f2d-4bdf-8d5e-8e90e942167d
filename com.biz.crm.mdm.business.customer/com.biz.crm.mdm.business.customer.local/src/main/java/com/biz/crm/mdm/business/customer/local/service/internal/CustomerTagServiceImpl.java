package com.biz.crm.mdm.business.customer.local.service.internal;

import cn.hutool.core.collection.CollUtil;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.customer.local.entity.CustomerEntity;
import com.biz.crm.mdm.business.customer.local.entity.CustomerTagEntity;
import com.biz.crm.mdm.business.customer.local.entity.CustomerTagMappingEntity;
import com.biz.crm.mdm.business.customer.local.entity.CustomerTagRuleEntity;
import com.biz.crm.mdm.business.customer.local.repository.CustomerTagMappingRepository;
import com.biz.crm.mdm.business.customer.local.repository.CustomerTagRepository;
import com.biz.crm.mdm.business.customer.local.service.CustomerService;
import com.biz.crm.mdm.business.customer.local.service.CustomerTagRuleService;
import com.biz.crm.mdm.business.customer.local.service.CustomerTagService;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerTagDto;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerTagRuleDto;
import com.biz.crm.mdm.business.customer.sdk.enums.TagAttributesConstant;
import com.biz.crm.mdm.business.customer.sdk.enums.TagExpressionConstant;
import com.biz.crm.mdm.business.customer.sdk.enums.TagUpdateTypeEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;

/**
 * 客户标签表服务实现类
 *
 * <AUTHOR>
 * @date 2021-10-26 16:27:42
 */
@Service("customerTagService")
public class CustomerTagServiceImpl implements CustomerTagService {

  @Autowired(required = false)
  private CustomerTagRepository customerTagRepository;
  @Autowired(required = false)
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;
  @Autowired(required = false)
  private CustomerTagRuleService customerTagRuleService;
  @Autowired(required = false)
  private LoginUserService loginUserService;
  @Autowired(required = false)
  private CustomerService customerService;
  @Autowired(required = false)
  private CustomerTagMappingRepository customerTagMappingRepository;

  @Override
  public List<CustomerTagEntity> findByCustomerCode(String customerCode) {
    if (StringUtils.isBlank(customerCode)) {
      return Lists.newArrayList();
    }
    return this.customerTagRepository.findByCustomerCode(customerCode);
  }

  @Override
  public List<CustomerTagEntity> findByCustomerCodes(List<String> customerCodes) {
    if (CollUtil.isEmpty(customerCodes)) {
      return Lists.newArrayList();
    }
    return this.customerTagRepository.findByCustomerCodes(customerCodes);
  }

  /**
   * 新增客户标签
   * @param dto 参数dto
   * @return
   */
  @Override
  @Transactional
  public CustomerTagEntity create(CustomerTagDto dto) {
    this.createValidation(dto);
    CustomerTagEntity entity = this.nebulaToolkitService.copyObjectByWhiteList(dto, CustomerTagEntity.class, HashSet.class, ArrayList.class);
    entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    entity.setEnableStatus(DelFlagStatusEnum.NORMAL.getCode());
    AbstractCrmUserIdentity loginUser = loginUserService.getAbstractLoginUser();
    String account = loginUser.getAccount();
    String username = loginUser.getUsername();
    
    Date date = new Date();
    entity.setCreateAccount(account);
    entity.setCreateName(username);
    entity.setCreateTime(date);
    entity.setModifyAccount(account);
    entity.setModifyName(username);
    entity.setModifyTime(date);
    //新增租户编号
    entity.setTenantCode(TenantUtils.getTenantCode());
    this.customerTagRepository.save(entity);

    // 标签与经销商更新
    CustomerTagMappingEntity mappingEntity = new CustomerTagMappingEntity();
    mappingEntity.setTagId(entity.getId());
    mappingEntity.setCustomerCode(dto.getCustomerCode());
    mappingEntity.setTenantCode(TenantUtils.getTenantCode());
    this.customerTagMappingRepository.save(mappingEntity);
    //规则
    if(CollectionUtils.isNotEmpty(dto.getRules())) {
      Collection<CustomerTagRuleEntity> customerTagRuleEntities = nebulaToolkitService.copyCollectionByWhiteList(dto.getRules(), CustomerTagRuleDto.class, CustomerTagRuleEntity.class, HashSet.class, ArrayList.class);
      customerTagRuleEntities.stream().forEach(item -> item.setCustomerTagId(entity.getId()));
      //新增租户编号
      customerTagRuleEntities.stream().forEach(item -> item.setTenantCode(TenantUtils.getTenantCode()));
      customerTagRuleService.create(customerTagRuleEntities);
    }
    return entity;
  }

  /**
   * 更新客户标签
   * @param dto
   * @return
   */
  @Override
  @Transactional
  public CustomerTagEntity update(CustomerTagDto dto) {
    this.updateValidation(dto);
    CustomerTagEntity entity = this.nebulaToolkitService.copyObjectByWhiteList(dto, CustomerTagEntity.class, HashSet.class, ArrayList.class);
    AbstractCrmUserIdentity loginUser = loginUserService.getAbstractLoginUser();
    entity.setModifyAccount(loginUser.getAccount());
    entity.setModifyName(loginUser.getUsername());
    entity.setModifyTime(new Date());
    //重构修改方法
    this.customerTagRepository.updateByIdAndTenantCode(entity,TenantUtils.getTenantCode());

    // 删除原标签关系
    this.customerTagMappingRepository.deleteByTagIds(Collections.singletonList(entity.getId()));
    // 标签与经销商更新
    CustomerTagMappingEntity mappingEntity = new CustomerTagMappingEntity();
    mappingEntity.setTagId(entity.getId());
    mappingEntity.setCustomerCode(dto.getCustomerCode());
    mappingEntity.setTenantCode(TenantUtils.getTenantCode());
    this.customerTagMappingRepository.save(mappingEntity);

    //规则
    if(CollectionUtils.isNotEmpty(dto.getRules())) {
      Collection<CustomerTagRuleEntity> customerTagRuleEntities = nebulaToolkitService.copyCollectionByWhiteList(dto.getRules(), CustomerTagRuleDto.class, CustomerTagRuleEntity.class, HashSet.class, ArrayList.class);
      customerTagRuleService.update(customerTagRuleEntities);
    }
    return entity;
  }

  /**
   * 批量起用
   * @param ids
   */
  @Override
  @Transactional
  public void enableBatch(List<String> ids) {
    Validate.notEmpty(ids, "缺失id");
    this.customerTagRepository.updateEnableStatusByIds(ids, EnableStatusEnum.ENABLE.getCode());
  }

  /**
   * 批量禁用
   * @param ids
   */
  @Override
  @Transactional
  public void disableBatch(List<String> ids) {
    Validate.notEmpty(ids, "缺失id");
    this.customerTagRepository.updateEnableStatusByIds(ids, EnableStatusEnum.DISABLE.getCode());
  }

  /**
   * 查看详情（包括关联信息）
   * @param id
   * @return
   */
  @Override
  public CustomerTagEntity findDetailById(String id) {
    if(StringUtils.isBlank(id)){
      return null;
    }
    return customerTagRepository.findDetailById(id);
  }

  /**
   * 调用经销商标签定时任务
   */
  @Override
  @Transactional
  public void handleCustomerTagTask() {
    List<CustomerEntity> customers = customerService.findByStatus(EnableStatusEnum.ENABLE.getCode());
    //TODO
    //1.统计每个客户的信息
    //2.根据统计结果，查找满足的标签
    //3.保存关系
    for(CustomerEntity customer : customers){
      //销售额
      BigDecimal sale = BigDecimal.ZERO;
      //销售目标达标统计
      BigDecimal target = BigDecimal.ZERO;
      //逾期次数
      Integer num = 0;

      LinkedList<String> tagList = this.handleTag(sale, target, num);
      List<CustomerTagMappingEntity> mappings = new ArrayList<>();
      for(String tag : tagList){
        CustomerTagMappingEntity mappingEntity = new CustomerTagMappingEntity();
        mappingEntity.setTagId(tag);
        mappingEntity.setCustomerCode(customer.getCustomerCode());
        mappingEntity.setTenantCode(customer.getTenantCode());
        mappings.add(mappingEntity);
      }
      customerTagMappingRepository.saveBatch(mappings);
    }
  }

  /**
   * 处理满足规则的标签
   * @param sale
   * @param target
   * @param num
   */
  private LinkedList<String> handleTag(BigDecimal sale, BigDecimal target, Integer num) {
    List<CustomerTagEntity> tags = customerTagRepository.findDetailByStatusAndUpdateType(EnableStatusEnum.ENABLE.getCode(), true);
    if(CollectionUtils.isEmpty(tags)){
      return Lists.newLinkedList();
    }
    LinkedList<String> tagList = new LinkedList<>();
    for(CustomerTagEntity tag : tags){
      List<CustomerTagRuleEntity> rules = tag.getRules();
      if(CollectionUtils.isEmpty(rules)){
        continue;
      }
      Boolean bool = this.handleRules(rules, sale, target, num);
      if(bool){
        tagList.add(tag.getId());
      }
    }
    return tagList;
  }

  /**
   * 处理规则
   * @param rules
   * @param sale
   * @param target
   * @param num
   */
  private Boolean handleRules(List<CustomerTagRuleEntity> rules, BigDecimal sale, BigDecimal target, Integer num) {
    Boolean flag = false;
    for(CustomerTagRuleEntity rule : rules){
      switch (rule.getTagAttributes()) {
        case TagAttributesConstant.SALE:
          flag = this.handleCompare(rule, sale);
          break;
        case TagAttributesConstant.CHANNEL:
          flag = this.handleCompare(rule, target);
          break;
        case TagAttributesConstant.CREDIT:
          flag = this.handleCompare(rule, new BigDecimal(num));
          break;
        default:
          throw new IllegalArgumentException("不支持该属性");
      }
    }
    return flag;
  }

  /**
   * 处理
   * @param rule
   * @param data
   * @return
   */
  private Boolean handleCompare(CustomerTagRuleEntity rule, BigDecimal data) {
    switch (rule.getTagExpression()) {
      case TagExpressionConstant.EQUAL:
        if(data.compareTo(rule.getExpressionAmount()) == 0){
          return true;
        }
        break;
      case TagExpressionConstant.MORE_THAN:
        if(data.compareTo(rule.getExpressionAmount()) == 1){
          return true;
        }
        break;
      case TagExpressionConstant.LESS_THAN:
        if(data.compareTo(rule.getExpressionAmount()) == -1){
          return true;
        }
        break;
      case TagExpressionConstant.INTERVAL:
        if(data.compareTo(rule.getExpressionAmount()) != -1 && data.compareTo(rule.getExpressionAmountMax()) != 1){
          return true;
        }
        break;
      default:
        throw new IllegalArgumentException("不支持该属性细分");
    }
    return false;
  }

  /**
   * 更新数据验证
   * @param dto
   */
  private void updateValidation(CustomerTagDto dto) {
    Validate.notNull(dto, "进行当前操作时，信息对象必须传入!");
    Validate.notBlank(dto.getId(), "更新时缺失id");
    Validate.notBlank(dto.getTagName(), "缺失标签名称");
    CustomerTagEntity entity = customerTagRepository.findByTagName(dto.getTagName());
    Validate.isTrue(entity != null && entity.getId().equals(dto.getId()), "标签名称重复，请检查");
    Validate.notNull(dto.getUpdateType(), "缺失更新方式");
    if(TagUpdateTypeEnum.TIMING.getDictCode().equals(String.valueOf(dto.getUpdateType()))) {
      Validate.notNull(dto.getUpdateCycle(), "缺失更新周期");
    }
  }

  @Override
  @Transactional
  public void deleteBatch(List<String> ids) {
    Validate.isTrue(CollectionUtils.isNotEmpty(ids), "缺失id");
    List<CustomerTagEntity> entities = this.customerTagRepository.listByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
    Validate.isTrue(CollectionUtils.isNotEmpty(entities) && entities.size() == ids.size(), "数据删除个数不匹配");
    this.customerTagRepository.deleteBatch(ids);

    // 删除原标签关系
    this.customerTagMappingRepository.deleteByTagIds(ids);
  }

  /**
   * 在创建customerTag模型对象之前，检查对象各属性的正确性，其主键属性必须没有值
   *
   * @param dto 检查对象
   */
  private void createValidation(CustomerTagDto dto) {
    Validate.notNull(dto, "进行当前操作时，信息对象必须传入!");
    dto.setTenantCode(TenantUtils.getTenantCode());
    dto.setId(null);
    Validate.notBlank(dto.getTagName(), "缺失标签名称");
    CustomerTagEntity entity = customerTagRepository.findByTagName(dto.getTagName());
    Validate.isTrue(entity==null, "标签名称重复，请检查");
    Validate.notNull(dto.getUpdateType(), "缺失更新方式");
    if(TagUpdateTypeEnum.TIMING.getDictCode().equals(String.valueOf(dto.getUpdateType()))) {
      Validate.notNull(dto.getUpdateCycle(), "缺失更新周期");
    }
  }
}
