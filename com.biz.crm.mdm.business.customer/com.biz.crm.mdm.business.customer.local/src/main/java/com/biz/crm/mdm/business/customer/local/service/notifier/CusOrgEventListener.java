package com.biz.crm.mdm.business.customer.local.service.notifier;

import com.biz.crm.mdm.business.customer.local.entity.CustomerEntity;
import com.biz.crm.mdm.business.customer.local.model.MultipleConditionModel;
import com.biz.crm.mdm.business.customer.local.service.CustomerService;
import com.biz.crm.mdm.business.org.sdk.dto.OrgEventBatchDto;
import com.biz.crm.mdm.business.org.sdk.dto.OrgEventDto;
import com.biz.crm.mdm.business.org.sdk.event.OrgEventListener;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 客户信息组织事件监听实现
 *
 * <AUTHOR>
 * @date 2021/10/28
 */
@Component
public class CusOrgEventListener implements OrgEventListener {

  @Autowired(required = false)
  private CustomerService customerService;

  /**
   * 当组织发生删除事件时，根据触发事件的组织编码,验证组织是否存在与客户的关联关系。
   * <p>
   * 1.存在与客户的关联关系,则抛出组织已关联客户的异常,阻断当前组织的删除操作
   * 2.不存在与客户的关联关系,则什么也不做.
   */
  @Override
  public void onDelete(List<String> orgCodes) {
    if (CollectionUtils.isEmpty(orgCodes)) {
      return;
    }
    MultipleConditionModel model = new MultipleConditionModel();
    model.setOrgCodes(orgCodes);
    List<CustomerEntity> list = customerService.findByMultipleConditionModel(model);
    Validate.isTrue(CollectionUtils.isEmpty(list), "存在与客户的绑定关系不能删除");
  }

  @Override
  public void onDeleteBatch(OrgEventBatchDto orgEventBatchDto) {

  }

  @Override
  public void onEnableBatch(OrgEventBatchDto orgEventBatchDto) {

  }

  @Override
  public void onDisableBatch(OrgEventBatchDto orgEventBatchDto) {

  }

  @Override
  public void onUpdate(OrgEventDto orgEventDto) {

  }
}
