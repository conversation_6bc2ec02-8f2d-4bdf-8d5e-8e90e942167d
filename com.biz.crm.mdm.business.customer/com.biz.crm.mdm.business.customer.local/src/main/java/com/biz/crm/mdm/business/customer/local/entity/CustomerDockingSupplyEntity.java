package com.biz.crm.mdm.business.customer.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * 客户信息对接人供货实体
 *
 * <AUTHOR>
 * @date 2021/10/26
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CustomerDockingSupplyEntity", description = "客户对接人供货信息实体")
@Entity
@TableName("mdm_customer_docking_supply")
@Table(name = "mdm_customer_docking_supply", indexes = {
    @Index(name = "customer_docking_supply_index1", columnList = "code"),
    @Index(name = "customer_docking_supply_index2", columnList = "docking_id"),
    @Index(name = "customer_docking_supply_index3", columnList = "tenant_code"),
})
@org.hibernate.annotations.Table(appliesTo = "mdm_customer_docking_supply", comment = "客户对接人供货信息表")
public class CustomerDockingSupplyEntity extends TenantOpEntity {

  private static final long serialVersionUID = 1967348844304069314L;

  @ApiModelProperty("类型1商品2产品层级")
  @Column(name = "data_type",  columnDefinition = "VARCHAR(10) COMMENT '类型1商品2产品层级'")
  private String dataType;

  @ApiModelProperty("编码")
  @Column(name = "code",  columnDefinition = "VARCHAR(64) COMMENT '编码'")
  private String code;

  @ApiModelProperty("描述")
  @Column(name = "name",  columnDefinition = "VARCHAR(128) COMMENT '描述'")
  private String name;

  @ApiModelProperty("对接人ID")
  @Column(name = "docking_id",  columnDefinition = "VARCHAR(32) COMMENT '对接人ID'")
  private String dockingId;

}
