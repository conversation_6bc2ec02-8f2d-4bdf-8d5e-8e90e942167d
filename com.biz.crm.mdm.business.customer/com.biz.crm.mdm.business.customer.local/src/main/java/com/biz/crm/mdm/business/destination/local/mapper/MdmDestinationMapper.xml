<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.destination.local.mapper.MdmDestinationMapper">

    <select id="findByConditions" resultType="com.biz.crm.mdm.business.destination.sdk.vo.MdmDestinationVo">
        select * from mdm_destination
        where tenant_code = #{dto.tenantCode}
        and del_flag = '${@<EMAIL>()}'
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and enable_status = #{dto.enableStatus}
        </if>
        <if test="dto.erpCode != null and dto.erpCode != ''">
            and erp_code = #{dto.erpCode}
        </if>
        <if test="dto.destinationCode != null and dto.destinationCode != ''">
            and destination_code = #{dto.destinationCode}
        </if>
        <if test="dto.destinationName != null and dto.destinationName != ''">
            <bind name="destinationNameLike" value="'%' + dto.destinationName + '%'"/>
          and destination_name like #{destinationNameLike}
        </if>
        <if test="dto.customerCode != null and dto.customerCode != ''">
            and customer_code = #{dto.customerCode}
        </if>
        <if test="dto.customerName != null and dto.customerName != ''">
            <bind name="customerNameLike" value="'%' + dto.customerName + '%'"/>
            and customer_name like #{customerNameLike}
        </if>
        <if test="dto.customerErpCode != null and dto.customerErpCode != ''">
            and customer_erp_code = #{dto.customerErpCode}
        </if>
        <if test="dto.companyCode != null and dto.companyCode != ''">
            and company_code = #{dto.companyCode}
        </if>
        <if test="dto.companyName != null and dto.companyName != ''">
            <bind name="companyNameLike" value="'%' + dto.companyName + '%'"/>
            and company_name like #{companyNameLike}
        </if>
        <if test="dto.destinationType != null and dto.destinationType != ''">
            and destination_type = #{dto.destinationType}
        </if>
        <if test="dto.dataSource != null and dto.dataSource != ''">
            and data_source = #{dto.dataSource}
        </if>
        order by create_time desc, id desc
    </select>
</mapper>