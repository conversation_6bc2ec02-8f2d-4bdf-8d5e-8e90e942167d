package com.biz.crm.mdm.business.customer.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * 客户信息送达方
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/7/5 14:38
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CustomerDestinationEntity", description = "客户信息送达方")
@Entity
@TableName("mdm_customer_destination")
@Table(name = "mdm_customer_destination", indexes = {
        @Index(name = "mdm_customer_destination_index1", columnList = "customer_code"),
        @Index(name = "mdm_customer_destination_index2", columnList = "destination_code"),
        @Index(name = "mdm_customer_destination_index3", columnList = "tenant_code,customer_code,destination_code", unique = true),
})
@org.hibernate.annotations.Table(appliesTo = "mdm_customer_destination", comment = "客户信息送达方")
public class CustomerDestinationEntity extends TenantEntity {

    private static final long serialVersionUID = 6854788874224568298L;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", length = 32, columnDefinition = "VARCHAR(64) COMMENT '客户编码'")
    private String customerCode;

    @ApiModelProperty("送达方编码")
    @Column(name = "destination_code", columnDefinition = "VARCHAR(32) COMMENT '送达方编码'")
    private String destinationCode;

    @ApiModelProperty("送达方名称")
    @Column(name = "destination_name", columnDefinition = "VARCHAR(128) COMMENT '送达方名称'")
    private String destinationName;

    @ApiModelProperty("送达方ERP编码")
    @Column(name = "destination_erp_code", columnDefinition = "VARCHAR(32) COMMENT '送达方ERP编码'")
    private String destinationErpCode;


}