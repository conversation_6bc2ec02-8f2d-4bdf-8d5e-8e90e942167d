package com.biz.crm.mdm.business.destination.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.mdm.business.destination.local.entity.MdmDestinationEntity;
import com.biz.crm.mdm.business.destination.local.repository.MdmDestinationRepository;
import com.biz.crm.mdm.business.destination.local.service.MdmDestinationSapDataSaveService;
import com.biz.crm.mdm.business.destination.local.service.MdmDestinationSapDataService;
import com.biz.crm.mdm.business.destination.sdk.dto.MdmDestinationDto;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 送达方  保存或更新SAP数据
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/22 18:47
 */
@Service
@Slf4j
public class MdmDestinationSapDataServiceImpl implements MdmDestinationSapDataService {

    @Autowired(required = false)
    @Qualifier("nebulaToolkitService")
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private MdmDestinationRepository mdmDestinationRepository;

    @Autowired(required = false)
    private MdmDestinationSapDataSaveService mdmDestinationSapDataSaveService;


    /**
     * 保存或更新SAP数据
     *
     * @param dtoList
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/24 20:53
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NOT_SUPPORTED)
    public void saveOrUpdateSapData(List<MdmDestinationDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, MdmDestinationDto> dtoMap = Maps.newHashMap();
        dtoList.stream().filter(Objects::nonNull).forEach(dto -> {
            if (StringUtil.isEmpty(dto.getDestinationCode())) {
                return;
            }
            dtoMap.put(dto.getDestinationCode(), dto);
        });
        if (CollectionUtil.isEmpty(dtoMap)) {
            return;
        }
        List<MdmDestinationEntity> saveList = Lists.newArrayList();
        List<MdmDestinationEntity> updateList = Lists.newArrayList();
        this.buildSaveListAndUpdateList(saveList, updateList, dtoMap);
        mdmDestinationSapDataSaveService.saveOrUpdateList(saveList, updateList);

    }

    /**
     * 区分新增还是更新
     *
     * @param saveList
     * @param updateList
     * @param dtoMap
     */
    private void buildSaveListAndUpdateList(List<MdmDestinationEntity> saveList, List<MdmDestinationEntity> updateList,
                                            Map<String, MdmDestinationDto> dtoMap) {
        if (CollectionUtil.isEmpty(dtoMap)) {
            return;
        }
        List<MdmDestinationEntity> oldList = this.mdmDestinationRepository.findDetailsByIdsOrCodes(null, Lists.newArrayList(dtoMap.keySet()));
        List<MdmDestinationEntity> entityList = Lists.newArrayList(this.nebulaToolkitService
                .copyCollectionByWhiteList(dtoMap.values(), MdmDestinationDto.class, MdmDestinationEntity.class, HashSet.class, ArrayList.class));
        if (CollectionUtil.isEmpty(oldList)) {
            saveList.addAll(entityList);
            return;
        }
        Map<String, MdmDestinationEntity> oldEntityMap = oldList.stream().filter(k -> StringUtil.isNotEmpty(k.getDestinationCode()))
                .collect(Collectors.toMap(MdmDestinationEntity::getDestinationCode, v -> v, (n, o) -> n));
        entityList.forEach(entity -> {
            MdmDestinationEntity oldEntity = oldEntityMap.get(entity.getDestinationCode());
            if (Objects.isNull(oldEntity)) {
                saveList.add(entity);
            } else {
                oldEntity.setErpCode(entity.getErpCode());
                oldEntity.setDestinationName(entity.getDestinationName());
                oldEntity.setCustomerErpCode(entity.getCustomerErpCode());
                oldEntity.setCustomerCode(entity.getCustomerCode());
                oldEntity.setCustomerName(entity.getCustomerName());
                oldEntity.setDestinationType(entity.getDestinationType());
                oldEntity.setCompanyCode(entity.getCompanyCode());
                oldEntity.setCompanyName(entity.getCompanyName());
                oldEntity.setProductGroupCode(entity.getProductGroupCode());
                oldEntity.setProductGroupName(entity.getProductGroupName());
                oldEntity.setChannelCode(entity.getChannelCode());
                oldEntity.setProvinceCode(entity.getProvinceCode());
                oldEntity.setProvinceName(entity.getProvinceName());
                oldEntity.setCityName(entity.getCityName());
                oldEntity.setDistrictName(entity.getDistrictName());
                oldEntity.setSyncUpdateTime(entity.getSyncUpdateTime());
                oldEntity.setDataSource(entity.getDataSource());
                oldEntity.setConsigneeAddress(entity.getConsigneeAddress());
                oldEntity.setConsigneeName(entity.getConsigneeName());
                oldEntity.setConsigneePhone(entity.getConsigneePhone());
                oldEntity.setEnableStatus(entity.getEnableStatus());
                oldEntity.setDelFlag(entity.getDelFlag());
                oldEntity.setModifyAccount(entity.getModifyAccount());
                oldEntity.setModifyName(entity.getModifyName());
                oldEntity.setModifyTime(entity.getModifyTime());
                updateList.add(oldEntity);
            }
        });
    }

    /**
     * 更新保存数据
     *
     * @param saveList
     * @param updateList
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveOrUpdateList(List<MdmDestinationEntity> saveList, List<MdmDestinationEntity> updateList) {
        if (CollectionUtil.isNotEmpty(saveList)) {
            mdmDestinationRepository.saveBatchXml(saveList);
        }
        if (CollectionUtil.isNotEmpty(updateList)) {
            mdmDestinationRepository.updateBatchXml(updateList);
        }
    }
}