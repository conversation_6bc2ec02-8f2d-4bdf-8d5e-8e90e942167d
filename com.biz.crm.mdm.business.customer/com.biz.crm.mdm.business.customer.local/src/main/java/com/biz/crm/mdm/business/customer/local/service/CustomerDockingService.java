package com.biz.crm.mdm.business.customer.local.service;

import com.biz.crm.mdm.business.customer.local.entity.CustomerDockingEntity;
import com.biz.crm.mdm.business.customer.sdk.dto.BindCustomerDockingDto;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerDockingDto;
import com.biz.crm.mdm.business.customer.sdk.dto.UnbindCustomerDockingDto;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerDockingVo;

import java.util.List;

/**
 * 客户对接人表服务接口
 *
 * <AUTHOR>
 * @date 2021-10-26 16:25:01
 */
public interface CustomerDockingService {

  /**
   * 客户对接人信息绑定客户编码
   *
   * @param dtoList      客户对接人信息
   * @param customerCode 客户编码
   */
  void rebindCustomerCode(List<CustomerDockingDto> dtoList, String customerCode);

  /**
   * 绑定客户对接人信息
   * @param dto 请求参数dto
   */
  void bind(BindCustomerDockingDto dto);

  /**
   * 解绑客户对接人信息
   * @param dto 请求参数dto
   */
  void unbind(UnbindCustomerDockingDto dto);

  /**
   * 根据客户编码查询关联业务员
   * @param customerCodes
   * @return
   */
  List<CustomerDockingVo> findListByCustomerCodes(List<String> customerCodes);
}
