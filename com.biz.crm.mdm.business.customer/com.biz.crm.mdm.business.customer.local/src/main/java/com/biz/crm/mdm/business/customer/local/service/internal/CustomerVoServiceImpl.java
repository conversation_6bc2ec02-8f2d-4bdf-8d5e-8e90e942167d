package com.biz.crm.mdm.business.customer.local.service.internal;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.mdm.business.customer.local.entity.*;
import com.biz.crm.mdm.business.customer.local.repository.CustomerDockingRepository;
import com.biz.crm.mdm.business.customer.local.repository.CustomerRepository;
import com.biz.crm.mdm.business.customer.local.service.*;
import com.biz.crm.mdm.business.customer.org.sdk.service.CustomerOrgVoSdkService;
import com.biz.crm.mdm.business.customer.org.sdk.vo.CustomerOrgVo;
import com.biz.crm.mdm.business.customer.sdk.constant.CustomerConstant;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerDto;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerQueryDto;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerSelectDto;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerAddressVoService;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerTagVoService;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.*;
import com.biz.crm.mdm.business.destination.local.entity.MdmDestinationEntity;
import com.biz.crm.mdm.business.destination.local.repository.MdmDestinationRepository;
import com.biz.crm.mdm.business.org.sdk.common.constant.OrgCodeConstant;
import com.biz.crm.mdm.business.org.sdk.dto.OrgQueryDto;
import com.biz.crm.mdm.business.org.sdk.dto.RelateOrgCodeQueryDto;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.AbstractRelationView;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.biz.crm.mdm.business.user.sdk.service.UserInfoVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserInfoVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.jsonwebtoken.lang.Assert;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 客户信息VO服务接口实现类
 *
 * <AUTHOR>
 * @date 2021/10/27
 */
@Service
@Slf4j
public class CustomerVoServiceImpl implements CustomerVoService {

    @Autowired(required = false)
    private CustomerService customerService;

    @Autowired(required = false)
    private MdmDestinationRepository mdmDestinationRepository;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private OrgVoService orgVoService;
    @Autowired(required = false)
    private CustomerOrgVoSdkService customerOrgVoSdkService;
    @Autowired(required = false)
    private UserInfoVoService userInfoVoService;
    @Autowired(required = false)
    private PositionVoService positionVoService;
    @Autowired(required = false)
    private CustomerRepository customerRepository;
    @Autowired(required = false)
    private CustomerROrgService customerROrgService;
    @Autowired(required = false)
    private CustomerDockingRepository customerDockingRepository;
    @Autowired(required = false)
    private CustomerContactService customerContactService;

    @Autowired(required = false)
    private CustomerTagService customerTagService;

    @Autowired(required = false)
    private CustomerTagVoService customerTagVoService;

    @Autowired(required = false)
    private CustomerAddressVoService customerAddressVoService;

    @Autowired(required = false)
    private CustomerWarehouseAddressService customerWarehouseAddressService;

    @Autowired(required = false)
    private CustomerDestinationService customerDestinationService;

    @Autowired(required = false)
    private CustomerMediaService customerMediaService;

    @Resource
    private RedisService redisService;


    /**
     * 基于数据库执行的数据视图执行内容缓存（最多500毫秒）
     */
    private static volatile Cache<String, CustomerVo> cache = null;

    public CustomerVoServiceImpl() {
        if (cache == null) {
            synchronized (CustomerVoServiceImpl.class) {
                while (cache == null) {
                    cache = CacheBuilder.newBuilder()
                            .initialCapacity(10000)
                            .expireAfterWrite(500, TimeUnit.MILLISECONDS)
                            .maximumSize(100000)
                            .build();
                }
            }
        }
    }

    @Override
    public CustomerVo findDetailsByIdOrCode(String id, String customerCode) {
        if (StringUtils.isAllBlank(id, customerCode)) {
            return null;
        }
        String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), id, customerCode);
        CustomerVo customerVo = cache.getIfPresent(cacheKey);
        if (customerVo == null) {
            CustomerEntity entity = customerService.findDetailsByIdOrCode(id, customerCode);
            if (Objects.isNull(entity)) {
                return null;
            }
            customerVo = this.covertEntityToVo(entity);
            //仓库地址
            this.buildCustomerWarehouseAddress(customerVo);
            //完善送达方信息
            this.buildCustomerDestination(customerVo);
            //完善收货地址
            this.buildCustomerAddress(customerVo);
            this.dealSaleAreaInfo(customerVo);
            this.perfectDockingInfo(customerVo);
            // 文件信息
            this.buildCustomerFile(Collections.singletonList(customerVo.getCustomerCode()), Collections.singletonList(customerVo));
            cache.put(cacheKey, customerVo);
        }

        return customerVo;
    }

    /**
     * 完善对接人信息
     * 1.获取对接人职位编码集合
     * 2.获取职位集合对应用户信息映射MAP和对应职位详细信息映射MAP
     * 3.遍历对接人信息,设置用户和职位组织信息
     *
     * @param vo
     */
    private void perfectDockingInfo(CustomerVo vo) {
        if (Objects.isNull(vo)) {
            return;
        }
        this.perfectDockingInfo(Collections.singletonList(vo));
    }

    /**
     * 完善对接人信息
     * 1.获取对接人职位编码集合
     * 2.获取职位集合对应用户信息映射MAP和对应职位详细信息映射MAP
     * 3.遍历对接人信息,设置用户和职位组织信息
     *
     * @param customerVoList
     */
    private void perfectDockingInfo(List<CustomerVo> customerVoList) {
        if (CollectionUtil.isEmpty(customerVoList)) {
            return;
        }
        List<CustomerDockingVo> dockingList = Lists.newArrayList();
        customerVoList.forEach(vo -> {
            if (CollectionUtil.isNotEmpty(vo.getDockingList())) {
                dockingList.addAll(vo.getDockingList());
            }
        });
        if (CollectionUtil.isEmpty(dockingList)) {
            return;
        }
        Set<String> positionCodes = dockingList.stream().map(CustomerDockingVo::getPositionCode).collect(Collectors.toSet());
        Map<String, UserInfoVo> userMap = Maps.newHashMap();
        Map<String, PositionVo> positionMap = Maps.newHashMap();
        List<UserInfoVo> userList = this.userInfoVoService.findByPositionCodes(positionCodes);
        if (CollectionUtil.isNotEmpty(userList)) {
            userMap.putAll(userList.stream().collect(Collectors.toMap(UserInfoVo::getPositionCode, t -> t, (n, o) -> n)));
        }
        List<PositionVo> positionList = positionVoService.findDetailsByIdsOrCodes(null, Lists.newArrayList(positionCodes));
        if (CollectionUtil.isNotEmpty(positionList)) {
            positionMap.putAll(positionList.stream().collect(Collectors.toMap(PositionVo::getPositionCode, t -> t, (n, o) -> n)));
        }
        customerVoList.forEach(vo -> {
            if (CollectionUtil.isNotEmpty(vo.getDockingList())) {
                //完善对接人用户和组织信息
                this.perfectDockingPositionUserInfo(vo.getDockingList(), userMap, positionMap);
                //完善对接人组织信息
                this.perfectDockingOrgInfo(vo.getDockingList(), positionMap);
            }
        });
    }

    /**
     * 完善对接人职位用户信息
     *
     * @param dockingList 对接口信息列表
     * @param userMap     用户信息映射
     * @param positionMap 职位信息映射
     */
    private void perfectDockingPositionUserInfo(List<CustomerDockingVo> dockingList, Map<String, UserInfoVo> userMap, Map<String, PositionVo> positionMap) {
        for (CustomerDockingVo customerDockingVo : dockingList) {
            UserInfoVo userInfoVo = userMap.get(customerDockingVo.getPositionCode());
            PositionVo positionVo = positionMap.get(customerDockingVo.getPositionCode());
            if (Objects.nonNull(userInfoVo)) {
                customerDockingVo.setFullName(userInfoVo.getFullName());
                customerDockingVo.setContact(userInfoVo.getUserPhone());
                customerDockingVo.setUserName(userInfoVo.getUserName());
            }
            if (Objects.nonNull(positionVo)) {
                customerDockingVo.setPositionName(positionVo.getPositionName());
            }
        }
    }

    /**
     * 完善对接人组织信息
     *
     * @param dockingList 对接口信息列表
     */
    private void perfectDockingOrgInfo(List<CustomerDockingVo> dockingList, Map<String, PositionVo> positionMap) {
        Set<String> orgCodeSet = Sets.newHashSet();
        if (CollectionUtil.isEmpty(dockingList)) {
            return;
        }
        dockingList.forEach(customerDockingVo -> {
            PositionVo positionVo = positionMap.get(customerDockingVo.getPositionCode());
            if (Objects.isNull(positionVo) || CollectionUtil.isEmpty(positionVo.getRelationData())) {
                return;
            }
            positionVo.getRelationData().stream().filter(prv -> OrgCodeConstant.KEY.equals(prv.getRelationKey())
                            && !CollectionUtil.isEmpty(prv.getRelationData()))
                    .collect(Collectors.toList()).forEach(positionRelationVo -> {
                        List<String> orgCodes = positionRelationVo.getRelationData()
                                .stream().map(AbstractRelationView::getCode).collect(Collectors.toList());
                        customerDockingVo.setOrgCode(String.join(",", orgCodes));
                        orgCodeSet.addAll(orgCodes);
                    });
        });
        if (CollectionUtil.isEmpty(orgCodeSet)) {
            return;
        }
        List<OrgVo> orgVoList = this.orgVoService.findByOrgCodes(Lists.newArrayList(orgCodeSet));
        if (CollectionUtil.isEmpty(orgVoList)) {
            return;
        }
        Map<String, OrgVo> orgMap = orgVoList.stream()
                .collect(Collectors.toMap(OrgVo::getOrgCode, t -> t, (key1, key2) -> key2));
        dockingList.forEach(customerDockingVo -> {
            String orgCode = customerDockingVo.getOrgCode();
            if (StringUtils.isBlank(orgCode)) {
                return;
            }
            List<String> orgNames = Arrays.stream(orgCode.split(",")).map(s -> {
                OrgVo orgVo = orgMap.get(s);
                return Objects.isNull(orgVo) ? null : orgVo.getOrgName();
            }).filter(Objects::nonNull).collect(Collectors.toList());
            if (!CollectionUtil.isEmpty(orgNames)) {
                customerDockingVo.setOrgName(String.join(",", orgNames));
            }
        });
    }

    @Override
    public Page<CustomerVo> findByCustomerSelectDto(Pageable pageable, CustomerSelectDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
        dto = ObjectUtils.defaultIfNull(dto, new CustomerSelectDto());
        dto.setTenantCode(TenantUtils.getTenantCode());
        Set<String> orgCodes = new HashSet<>();
        if (ObjectUtils.isNotEmpty(dto.getOrgCode())) {
            orgCodes.add(dto.getOrgCode());
        }
        if (!CollectionUtil.isEmpty(dto.getOrgCodes())) {
            orgCodes.addAll(dto.getOrgCodes());
        }
        if (ObjectUtils.isNotEmpty(dto.getParentOrgCode())) {
            List<OrgVo> children = orgVoService.findAllChildrenByOrgCode(dto.getParentOrgCode());
            if (CollectionUtils.isNotEmpty(children)) {
                orgCodes.addAll(children.stream().map(e -> e.getOrgCode()).collect(Collectors.toList()));
            }
        }
        dto.setOrgCodes(orgCodes);
        Page<CustomerEntity> entityPage = customerService.findByCustomerSelectDto(pageable, dto);
        if (CollectionUtil.isEmpty(entityPage.getRecords())) {
            return new Page<>();
        }
        Page<CustomerVo> pageResult = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        List<CustomerVo> customerVos = buildCustomerVoList(entityPage.getRecords(), null);
        customerVos.forEach(item -> {
            item.setUnionName(String.format("%s/%s", ObjectUtils.defaultIfNull(item.getCustomerCode(), "")
                    , ObjectUtils.defaultIfNull(item.getCustomerName(), "")));
        });
        pageResult.setRecords(customerVos);
        return pageResult;
    }

    /**
     * 分页查询
     *
     * @param pageable 分页信息
     * @param dto      分页参数dto
     * @return 查询结果
     */
    @Override
    public Page<CustomerVo> findByConditions(Pageable pageable, CustomerDto dto) {
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
        dto = Optional.ofNullable(dto).orElse(new CustomerDto());
        Page<CustomerEntity> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        dto.setTenantCode(TenantUtils.getTenantCode());

        if (StringUtils.isNotBlank(dto.getCustomerType())) {
            if (dto.getCustomerType().contains(",")) {
                dto.setCustomerTypeList(Arrays.asList(dto.getCustomerType().split(",")));
                dto.setCustomerType(null);
            }
        }
        Page<CustomerEntity> entityPage = this.customerRepository.findByConditions(page, dto);
        Page<CustomerVo> pageResult =
                new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        if (CollUtil.isEmpty(entityPage.getRecords())) {
            return pageResult;
        }
        List<String> list = entityPage.getRecords().stream().map(CustomerEntity::getCustomerCode).collect(Collectors.toList());
        List<CustomerTagEntity> tagEntityList = customerTagService.findByCustomerCodes(list);
        List<CustomerVo> customerVoList = this.buildCustomerVoList(entityPage.getRecords(), tagEntityList);
        //完善仓库地址
        this.buildCustomerWarehouseAddress(customerVoList);
        pageResult.setRecords(customerVoList);
        return pageResult;
    }

    /**
     * 封装数据
     *
     * @param customerEntities 经销商数据
     * @param tagEntityList    标签数据
     * @return
     */
    private List<CustomerVo> buildCustomerVoList(List<CustomerEntity> customerEntities, List<CustomerTagEntity> tagEntityList) {
        List<CustomerVo> customerVos = new ArrayList<>();
        if (CollectionUtil.isEmpty(customerEntities)) {
            return customerVos;
        }
        customerVos = (List<CustomerVo>) this.nebulaToolkitService.copyCollectionByWhiteList(customerEntities,
                CustomerEntity.class, CustomerVo.class, HashSet.class, ArrayList.class);
        Map<String, List<CustomerTagVo>> tagVos = Maps.newHashMap();
        // 标签
        if (CollectionUtil.isNotEmpty(tagEntityList)) {
            List<CustomerTagVo> list = (List<CustomerTagVo>) this.nebulaToolkitService.copyCollectionByWhiteList(tagEntityList,
                    CustomerTagEntity.class, CustomerTagVo.class, HashSet.class, ArrayList.class);
            tagVos = list.stream().collect(Collectors.groupingBy(CustomerTagVo::getCustomerCode));
        }
        //组织
        Map<String, List<OrgVo>> orgMap = this.buildOrgMap(customerEntities);
        Map<String, CustomerOrgVo> customerOrgMap = this.buildCustomerOrgMap(customerEntities);
        for (CustomerVo customerVo : customerVos) {
            customerVo.setTagVos(tagVos.get(customerVo.getCustomerCode()));
            List<OrgVo> orgList = orgMap.get(customerVo.getCustomerCode());
            if (CollectionUtil.isNotEmpty(orgList)) {
                List<String> orgNameList = Lists.newArrayList();
                List<String> orgCodeList = Lists.newArrayList();
                orgList.forEach(a -> {
                    orgNameList.add(a.getOrgName());
                    orgCodeList.add(a.getOrgCode());
                });
                customerVo.setOrgName(String.join(",", orgNameList));
                customerVo.setOrgCode(String.join(",", orgCodeList));
            }
            CustomerOrgVo customerOrgVo = customerOrgMap.get(customerVo.getCustomerOrgCode());
            if (Objects.nonNull(customerOrgVo)) {
                customerVo.setCustomerOrgName(customerOrgVo.getCustomerOrgName());
            }
        }
        return customerVos;
    }

    /**
     * 批量设置是否分利
     *
     * @param customerCodeList 编码集合
     * @param bool             设置值
     */
    @Override
    @Transactional
    public void modifyShareBenefits(List<String> customerCodeList, Boolean bool) {
        if (CollUtil.isNotEmpty(customerCodeList)) {
            //如果是解绑操作 再次验证一下经销商对应中段的关系
            if (!bool && !CollectionUtil.isEmpty(customerCodeList)) {
                customerRepository.unFlagCustomerShareBenefits(customerCodeList);
            } else {
                customerRepository.modifyShareBenefits(customerCodeList, bool);
            }
        }
    }

    @Override
    public List<CustomerVo> findByOrgCodes(List<String> orgCodeList) {
        if (CollectionUtil.isEmpty(orgCodeList)) {
            return Lists.newLinkedList();
        }
        List<CustomerEntity> list = this.customerService.findByOrgCodes(orgCodeList);
        if (CollectionUtil.isEmpty(list)) {
            return Lists.newLinkedList();
        }
        List<CustomerVo> customerVos = (List<CustomerVo>) this.nebulaToolkitService.copyCollectionByBlankList(list,
                CustomerEntity.class, CustomerVo.class, HashSet.class, ArrayList.class);
        //完善仓库地址
        this.buildCustomerWarehouseAddress(customerVos);
        return customerVos;
    }

    @Override
    public List<CustomerVo> findByCustomerCodes(List<String> customerCodeList) {
        if (CollectionUtil.isEmpty(customerCodeList)) {
            return Lists.newLinkedList();
        }
        List<CustomerEntity> list = this.customerService.findByCustomerCodes(customerCodeList);
        if (CollectionUtil.isEmpty(list)) {
            return Lists.newLinkedList();
        }
        List<CustomerVo> customerVos =
                (List<CustomerVo>) this.nebulaToolkitService.copyCollectionByBlankList(list, CustomerEntity.class, CustomerVo.class, HashSet.class, ArrayList.class);
        List<String> customerCodes = customerVos.stream()
                .filter(a -> StringUtils.isNotBlank(a.getCustomerCode()))
                .map(CustomerVo::getCustomerCode)
                .collect(Collectors.toList());
        //封装企业组织信息
        this.buildOrgInfo(customerCodes, customerVos);
        //封装客户组织信息
        this.buildCustomerOrgInfo(customerVos);
        //封装客户对接人信息
        this.buildDockingInfo(customerCodes, customerVos);
        //封装客户联系人信息
        this.buildContactInfo(customerCodes, customerVos);
        // 文件信息
        this.buildCustomerFile(customerCodes, customerVos);
        //完善仓库地址
        this.buildCustomerWarehouseAddress(customerVos);
        //完善送达方信息
        this.buildCustomerDestination(customerVos);
        return customerVos;
    }


    /**
     * 通过用户名查询关联的客户信息
     *
     * @param userNameList
     * @return
     */
    @Override
    public List<CustomerVo> findCustomerByUserNames(List<String> userNameList) {
        List<String> customerCodes = customerRepository.findCustomerByUserNames(userNameList);
        if (CollectionUtils.isEmpty(customerCodes)) {
            return Lists.newArrayList();
        }
        List<CustomerVo> customerVos = findByCustomerCodes(customerCodes);
        List<CustomerVo> removeCusList = customerVos.stream().filter(x -> ObjectUtils.isEmpty(x.getWorkLatitude()) || ObjectUtils.isEmpty(x.getWorkLongitude()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(removeCusList)) {
            customerVos.removeAll(removeCusList);
        }
        return customerVos;
    }


    @Override
    public List<CustomerVo> findCustomerByUserName(String userName) {
        List<String> customerCodes = customerRepository.findCustomerByUserNames(Lists.newArrayList(userName));
        if (CollectionUtils.isEmpty(customerCodes)) {
            return Lists.newArrayList();
        }
        List<CustomerVo> customerVos = findByCustomerCodes(customerCodes);
        return customerVos;
    }

    /**
     * 根据客户ERP编码集合获取对应的客户信息
     *
     * @param erpCodeList 客户编码集合
     * @return 客户信息集合
     */
    @Override
    public List<CustomerVo> findByErpCodes(List<String> erpCodeList) {
        if (CollectionUtil.isEmpty(erpCodeList)) {
            return Lists.newLinkedList();
        }
        List<CustomerEntity> list = this.customerService.findByErpCodes(erpCodeList);
        if (CollectionUtil.isEmpty(list)) {
            return Lists.newLinkedList();
        }
        List<CustomerVo> customerVos =
                (List<CustomerVo>) this.nebulaToolkitService.copyCollectionByBlankList(list, CustomerEntity.class, CustomerVo.class, HashSet.class, ArrayList.class);
        List<String> customerCodes = customerVos.stream()
                .filter(a -> StringUtils.isNotBlank(a.getCustomerCode()))
                .map(CustomerVo::getCustomerCode)
                .collect(Collectors.toList());
        //封装企业组织信息
        this.buildOrgInfo(customerCodes, customerVos);
        //封装客户组织信息
        this.buildCustomerOrgInfo(customerVos);
        //封装客户对接人信息
        this.buildDockingInfo(customerCodes, customerVos);
        //封装客户联系人信息
        this.buildContactInfo(customerCodes, customerVos);
        // 文件信息
        this.buildCustomerFile(customerCodes, customerVos);
        //完善仓库地址
        this.buildCustomerWarehouseAddress(customerVos);
        //完善送达方信息
        this.buildCustomerDestination(customerVos);
        return customerVos;
    }

    private void buildCustomerFile(List<String> customerCodes, List<CustomerVo> customerVos) {
        List<CustomerMediaVo> fileVos = this.customerMediaService.findByCustomerCodes(customerCodes);
        if (CollectionUtil.isEmpty(fileVos)) {
            return;
        }
        // k-customerCode,v-fileList
        Map<String, List<CustomerMediaVo>> customerContactVoMap = fileVos.stream().collect(Collectors.groupingBy(CustomerMediaVo::getCustomerCode));
        customerVos.forEach(customerVo -> customerVo.setFileList(customerContactVoMap.get(customerVo.getCustomerCode())));
    }

    @Override
    public List<CustomerVo> findCustomerAndContactByCustomerCodes(List<String> customerCodeList) {
        if (CollectionUtil.isEmpty(customerCodeList)) {
            return Lists.newLinkedList();
        }
        List<CustomerEntity> customerEntities = this.customerService.findByCustomerCodes(customerCodeList);
        if (CollectionUtil.isEmpty(customerEntities)) {
            return Lists.newLinkedList();
        }
        List<CustomerVo> customerVos = (List<CustomerVo>) this.nebulaToolkitService.copyCollectionByBlankList(
                customerEntities, CustomerEntity.class, CustomerVo.class, HashSet.class, ArrayList.class);
        List<String> customerCodes = customerVos.stream()
                .map(CustomerVo::getCustomerCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        this.buildContactInfo(customerCodes, customerVos);
        //完善仓库地址
        this.buildCustomerWarehouseAddress(customerVos);
        return customerVos;
    }

    /**
     * 封装客户组织信息
     *
     * @param customerVos
     */
    private void buildCustomerOrgInfo(List<CustomerVo> customerVos) {
        if (CollectionUtil.isEmpty(customerVos)) {
            return;
        }
        Set<String> customerOrgCodeSet =
                customerVos.stream()
                        .filter(a -> StringUtils.isNotBlank(a.getCustomerOrgCode()))
                        .map(CustomerVo::getCustomerOrgCode)
                        .collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(customerOrgCodeSet)) {
            return;
        }
        List<CustomerOrgVo> orgVoList =
                this.customerOrgVoSdkService.findListByCodes(Lists.newArrayList(customerOrgCodeSet));
        if (CollectionUtil.isEmpty(orgVoList)) {
            return;
        }
        Map<String, String> map =
                orgVoList.stream()
                        .filter(a -> StringUtils.isNoneBlank(a.getCustomerOrgCode(), a.getCustomerOrgName()))
                        .collect(
                                Collectors.toMap(
                                        CustomerOrgVo::getCustomerOrgCode,
                                        CustomerOrgVo::getCustomerOrgName,
                                        (a, b) -> a));
        for (CustomerVo item : customerVos) {
            if (StringUtils.isBlank(item.getCustomerOrgCode())) {
                continue;
            }
            item.setCustomerOrgName(map.get(item.getCustomerOrgCode()));
        }
    }

    /**
     * 封装客户联系人信息
     *
     * @param customerCodes 客户编码集合
     * @param customerVos   待封装客户信息
     */
    private void buildContactInfo(List<String> customerCodes, List<CustomerVo> customerVos) {
        List<CustomerContactVo> contactVos = this.customerContactService.findByCustomerCodes(customerCodes);
        if (CollectionUtil.isEmpty(contactVos)) {
            return;
        }
        // k-customerCode,v-contactList
        Map<String, List<CustomerContactVo>> customerContactVoMap = contactVos.stream().collect(Collectors.groupingBy(CustomerContactVo::getCustomerCode));
        customerVos.forEach(customerVo -> customerVo.setContactList(customerContactVoMap.get(customerVo.getCustomerCode())));
    }

    /**
     * 封装客户对接人信息
     *
     * @param customerCodes 客户编码集合
     * @param customerVos   待封装客户信息
     */
    private void buildDockingInfo(List<String> customerCodes, List<CustomerVo> customerVos) {
        List<CustomerDockingEntity> dockingEntities =
                this.customerDockingRepository.findByCustomerCodes(customerCodes, TenantUtils.getTenantCode());
        if (CollectionUtil.isEmpty(dockingEntities)) {
            return;
        }
        // k-customerCode,v-dockingList
        Map<String, List<CustomerDockingEntity>> dockingMap = Maps.newHashMap();
        dockingEntities.forEach(customerDockingEntity -> {
            List<CustomerDockingEntity> list = dockingMap.getOrDefault(customerDockingEntity.getCustomerCode(), Lists.newArrayList());
            list.add(customerDockingEntity);
            dockingMap.put(customerDockingEntity.getCustomerCode(), list);
        });
        List<CustomerDockingVo> allDockingList = Lists.newArrayList();
        customerVos.forEach(customerVo -> {
            List<CustomerDockingEntity> entities = dockingMap.get(customerVo.getCustomerCode());
            if (CollectionUtil.isEmpty(entities)) {
                return;
            }
            customerVo.setDockingList((List<CustomerDockingVo>) this.nebulaToolkitService
                    .copyCollectionByBlankList(entities, CustomerDockingEntity.class, CustomerDockingVo.class, HashSet.class, ArrayList.class));
            allDockingList.addAll(customerVo.getDockingList());
        });
        //完善对接人信息
        this.perfectDockingInfo(customerVos);
    }

    /**
     * 封装企业组织信息
     *
     * @param customerCodes 客户编码集合
     * @param customerVos   待封装客户信息
     */
    private void buildOrgInfo(List<String> customerCodes, List<CustomerVo> customerVos) {
        List<CustomerROrgEntity> customerROrgEntityList = Lists.newLinkedList();
        if (CollectionUtil.isNotEmpty(customerCodes)) {
            customerROrgEntityList = this.customerROrgService.findByCustomerCodes(new HashSet<>(customerCodes));
            if (CollectionUtil.isEmpty(customerROrgEntityList)) {
                return;
            }
        }
        // k-customerCode,v-orgCodeList
        Map<String, List<String>> map = customerROrgEntityList.stream()
                .filter(a -> StringUtils.isNoneBlank(a.getCustomerCode(), a.getOrgCode()))
                .collect(Collectors.groupingBy(
                        CustomerROrgEntity::getCustomerCode,
                        Collectors.mapping(CustomerROrgEntity::getOrgCode, Collectors.toList())));

        Set<String> orgCodeSet = customerROrgEntityList.stream()
                .filter(a -> StringUtils.isNotBlank(a.getOrgCode()))
                .map(CustomerROrgEntity::getOrgCode)
                .collect(Collectors.toSet());
        List<OrgVo> orgVoList = this.orgVoService.findByOrgCodes(Lists.newArrayList(orgCodeSet));
        Map<String, String> orgMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(orgVoList)) {
            orgMap.putAll(orgVoList.stream()
                    .filter(a -> StringUtils.isNoneBlank(a.getOrgCode(), a.getOrgName()))
                    .collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getOrgName, (a, b) -> a)));
        }

        for (CustomerVo item : customerVos) {
            List<String> curOrgCodeList = map.get(item.getCustomerCode());
            if (CollectionUtil.isEmpty(curOrgCodeList)) {
                continue;
            }
            List<CustomerRelateOrgVo> curOrgList = Lists.newLinkedList();
            for (String orgCode : curOrgCodeList) {
                if (!orgMap.containsKey(orgCode)) {
                    continue;
                }
                CustomerRelateOrgVo cur = new CustomerRelateOrgVo();
                cur.setCustomerCode(item.getCustomerCode());
                cur.setOrgCode(orgCode);
                cur.setOrgName(orgMap.get(orgCode));
                curOrgList.add(cur);
            }
            item.setOrgList(curOrgList);
        }
    }

    @Override
    public List<CustomerVo> findByChannels(List<String> channelList) {
        if (CollectionUtil.isEmpty(channelList)) {
            return Lists.newLinkedList();
        }
        List<CustomerEntity> list = this.customerRepository.findByChannels(channelList, TenantUtils.getTenantCode());
        if (CollectionUtil.isEmpty(list)) {
            return Lists.newLinkedList();
        }
        List<CustomerVo> customerVos = (List<CustomerVo>) this.nebulaToolkitService.copyCollectionByBlankList(list,
                CustomerEntity.class, CustomerVo.class, HashSet.class, ArrayList.class);
        //完善仓库地址
        this.buildCustomerWarehouseAddress(customerVos);
        return customerVos;
    }

    @Override
    public List<CustomerVo> findByTypes(List<String> typeList) {
        if (CollectionUtil.isEmpty(typeList)) {
            return Lists.newLinkedList();
        }
        List<CustomerEntity> list = this.customerRepository.findByTypes(typeList, TenantUtils.getTenantCode());
        if (CollectionUtil.isEmpty(list)) {
            return Lists.newLinkedList();
        }
        List<CustomerVo> customerVos = (List<CustomerVo>) this.nebulaToolkitService
                .copyCollectionByBlankList(list, CustomerEntity.class, CustomerVo.class, HashSet.class,
                        ArrayList.class);
        //完善仓库地址
        this.buildCustomerWarehouseAddress(customerVos);
        return customerVos;
    }

    @Override
    public Set<String> findCustomerCodesByOrgCodesAndChannelsAndTags(List<String> orgCodes, List<String> channelCodes, List<String> tags) {
        return this.customerRepository.findCustomerCodesByOrgCodesAndChannelsAndTags(orgCodes, channelCodes, tags);
    }

    @Override
    public Set<CustomerVo> findCustomersByOrgCodesAndChannelsAndTags(List<String> orgCodes,
                                                                     List<String> channelCodes, List<String> tags) {
        Set<CustomerEntity> customerEntities = this.customerRepository.findCustomersByOrgCodesAndChannelsAndTags(
                orgCodes, channelCodes, tags);
        if (CollectionUtil.isEmpty(customerEntities)) {
            return new HashSet<>();
        }
        Set<CustomerVo> customerVos = (Set<CustomerVo>) this.nebulaToolkitService.copyCollectionByWhiteList(
                customerEntities, CustomerEntity.class, CustomerVo.class, HashSet.class, ArrayList.class);
        return customerVos;
    }

    @Override
    public List<CustomerVo> findForPriceByCustomerCodes(Set<String> customerCodeSet) {
        if (CollectionUtil.isEmpty(customerCodeSet)) {
            return Lists.newLinkedList();
        }
        List<CustomerEntity> list = this.customerService.findByCustomerCodes(Lists.newArrayList(customerCodeSet));
        if (CollectionUtil.isEmpty(list)) {
            return Lists.newLinkedList();
        }
        List<CustomerVo> customerVos =
                (List<CustomerVo>) this.nebulaToolkitService.copyCollectionByBlankList(list, CustomerEntity.class, CustomerVo.class, HashSet.class, ArrayList.class);
        List<String> customerCodes = customerVos.stream()
                .filter(a -> StringUtils.isNotBlank(a.getCustomerCode()))
                .map(CustomerVo::getCustomerCode)
                .collect(Collectors.toList());
        //封装企业组织信息
        this.buildOrgInfo(customerCodes, customerVos);
        //完善仓库地址
        this.buildCustomerWarehouseAddress(customerVos);
        return customerVos;
    }

    @Override
    public List<CustomerVo> findByAmapIds(Set<String> amapIds) {
        if (CollectionUtil.isEmpty(amapIds)) {
            return Lists.newArrayList();
        }
        List<CustomerEntity> list = this.customerRepository.findByAmapIds(amapIds);
        if (CollectionUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<CustomerVo> customerVos = (List<CustomerVo>) this.nebulaToolkitService.copyCollectionByWhiteList(list,
                CustomerEntity.class, CustomerVo.class, HashSet.class, ArrayList.class);

        //完善仓库地址
        this.buildCustomerWarehouseAddress(customerVos);
        return customerVos;
    }

    @Override
    public Set<String> findByCustomerQueryDto(CustomerQueryDto dto) {
        if (Objects.isNull(dto)
                || (StringUtils.isAllBlank(
                dto.getCustomerCode(),
                dto.getCustomerName(),
                dto.getChannelCode(),
                dto.getOrgName(),
                dto.getDelFlag(),
                dto.getProcessStatus()) && CollectionUtils.isEmpty(dto.getCustomerCodeSet()))) {
            return Sets.newHashSet();
        }

        if (StringUtils.isNotBlank(dto.getOrgName())) {
            final OrgQueryDto queryDto = new OrgQueryDto();
            queryDto.setOrgName(dto.getOrgName());
            final Set<String> orgCodeSet = this.orgVoService.findByOrgQueryDto(queryDto);
            if (CollectionUtil.isEmpty(orgCodeSet)) {
                return Sets.newHashSet();
            }
            dto.setOrgCodeSet(orgCodeSet);
        }

        return this.customerRepository.findByCustomerQueryDto(dto);
    }

    @Override
    public Map<String, Set<String>> findAllowSaleCustomerByOrgCodes(Set<String> orgCodes) {
        if (CollectionUtil.isEmpty(orgCodes)) {
            return Maps.newHashMap();
        }
        final RelateOrgCodeQueryDto queryDto = new RelateOrgCodeQueryDto();
        queryDto.setOrgCodeSet(orgCodes);
        queryDto.setSearchType(-1);
        final Map<String, String> orgRuleMap = this.orgVoService.findByRelateOrgCodeQueryDto(queryDto);
        if (CollectionUtil.isEmpty(orgRuleMap)) {
            return Maps.newHashMap();
        }
        List<CustomerROrgEntity> list =
                this.customerROrgService.findAllowSaleCustomerByOrgCodes(orgRuleMap.keySet());
        if (CollectionUtil.isEmpty(list)) {
            return Maps.newHashMap();
        }
        Map<String, Set<String>> map = list.stream().filter(a ->
                        StringUtils.isNoneBlank(a.getCustomerCode(), a.getOrgCode())
                                && orgRuleMap.containsKey(a.getOrgCode()))
                .collect(Collectors.groupingBy(CustomerROrgEntity::getCustomerCode,
                        Collectors.mapping(CustomerROrgEntity::getOrgCode, Collectors.toSet())));
        Map<String, Set<String>> re = Maps.newHashMap();
        for (Entry<String, Set<String>> item : map.entrySet()) {
            Set<String> rule = Sets.newHashSet();
            for (String orgCode : item.getValue()) {
                final String s = orgRuleMap.get(orgCode);
                if (StringUtils.isBlank(s)) {
                    continue;
                }
                rule.add(s);
            }
            if (CollectionUtil.isEmpty(rule)) {
                continue;
            }
            re.put(item.getKey(), rule);
        }
        return re;
    }

    @Override
    public Map<String, Set<String>> findAllowSaleCustomerByChannels(Set<String> channelCodes) {
        if (CollectionUtil.isEmpty(channelCodes)) {
            return Maps.newHashMap();
        }

        List<CustomerEntity> list = this.customerRepository.findAllowSaleCustomerByChannels(channelCodes);
        if (CollectionUtil.isEmpty(list)) {
            return Maps.newHashMap();
        }
        return list.stream().collect(Collectors.groupingBy(CustomerEntity::getSapCustomerChannelName, Collectors.mapping(CustomerEntity::getCustomerCode, Collectors.toSet())));
    }

    @Override
    public List<CustomerVo> findCountByCreateAccountAndFromTypeAndCreateTimeScope(String createAccount, String fromType, String startDate, String endDate) {
        if (StringUtils.isAnyBlank(createAccount, fromType, startDate, endDate)) {
            return new ArrayList<>(0);
        }
        List<CustomerEntity> customerEntities = this.customerRepository.findCountByCreateAccountAndFromTypeAndCreateTimeScope(createAccount, fromType, startDate, endDate);
        List<CustomerVo> customerVos = (List<CustomerVo>) this.nebulaToolkitService.copyCollectionByWhiteList(customerEntities, CustomerEntity.class, CustomerVo.class, HashSet.class, ArrayList.class);
        //完善仓库地址
        this.buildCustomerWarehouseAddress(customerVos);
        return customerVos;
    }

    @Override
    public Boolean existByCustomerCodeAndChannels(List<String> channelList, String customerCode) {
        CustomerEntity entity = customerRepository.findByCustomerCodeAndChannels(customerCode, channelList);
        return ObjectUtils.isNotEmpty(entity);
    }

    @Override
    public Boolean existByCustomerCode(String customerCode) {
        CustomerEntity entity = customerRepository.findByCustomerCode(customerCode);
        return ObjectUtils.isNotEmpty(entity);
    }

    @Override
    public Boolean existByCustomer7OrgIn7OrgNotIn(String customerCode, List<String> orgCodeIn, List<String> orgCodeNotIn) {
        if (CollectionUtil.isEmpty(orgCodeIn)) {
            return false;
        }
        List<CustomerEntity> entities = customerRepository.findByCustomer7OrgIn7OrgNotIn(customerCode, orgCodeIn, orgCodeNotIn);
        return !CollectionUtil.isEmpty(entities);
    }

    @Override
    public CustomerVo findOrgByCode(String customerCode) {
        List<CustomerROrgEntity> list = customerROrgService.findByCustomerCodes(Sets.newHashSet(customerCode));
        if (!CollectionUtil.isEmpty(list)) {
            return this.nebulaToolkitService.copyObjectByWhiteList(list.get(0), CustomerVo.class, HashSet.class, ArrayList.class);
        }
        return null;
    }

    @Override
    public List<CustomerVo> findCustomerByUserNameAndPostCode(String userName, String postCode) {
        Validate.notNull(userName, "查询用户关联的客户时，用户名不能为空！");
        Validate.notNull(postCode, "查询用户关联的客户时，职位编码不能为空！");
        String tenantCode = TenantUtils.getTenantCode();
        List<CustomerEntity> customerEntities = this.customerRepository.findByUserNameAndPostCode(
                userName, postCode, tenantCode);
        if (CollectionUtil.isEmpty(customerEntities)) {
            return new ArrayList<>();
        }
        List<CustomerVo> customerVos = (List<CustomerVo>) this.nebulaToolkitService.copyCollectionByWhiteList(
                customerEntities, CustomerEntity.class, CustomerVo.class, HashSet.class, ArrayList.class);
        //完善仓库地址
        this.buildCustomerWarehouseAddress(customerVos);
        return customerVos;

    }

    /**
     * 客户实体类转客户信息VO
     *
     * @param entity 客户实体类
     * @return 客户信息VO
     */
    private CustomerVo covertEntityToVo(CustomerEntity entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        List<CustomerVo> list = this.covertEntityToVo(Lists.newArrayList(entity));
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 客户实体类转客户信息VO(集合)
     *
     * @param entities 客户实体类列表
     * @return 客户信息VO列表
     */
    private List<CustomerVo> covertEntityToVo(List<CustomerEntity> entities) {
        if (CollectionUtil.isEmpty(entities)) {
            return Lists.newArrayList();
        }
        List<CustomerVo> list = Lists.newArrayList(this.nebulaToolkitService.copyCollectionByBlankList(entities,
                CustomerEntity.class, CustomerVo.class, HashSet.class, ArrayList.class));
        List<String> codes = Lists.newLinkedList();
        for (CustomerVo customerVo : list) {
            if (StringUtil.isNotEmpty(customerVo.getProvinceCode())) {
                codes.add(customerVo.getProvinceCode());
            }
            if (StringUtil.isNotEmpty(customerVo.getCityCode())) {
                codes.add(customerVo.getCityCode());
            }
            if (StringUtil.isNotEmpty(customerVo.getDistrictCode())) {
                codes.add(customerVo.getDistrictCode());
            }
        }
        //Map<String, String> regionCodeNameMap = regionVoService.findRegionCodeNameMap(codes);
        Map<String, List<OrgVo>> orgMap = this.buildOrgMap(entities);
        Map<String, CustomerOrgVo> customerOrgMap = this.buildCustomerOrgMap(entities);
        for (CustomerVo customerVo : list) {
            List<OrgVo> orgList = orgMap.get(customerVo.getCustomerCode());
            if (!CollectionUtil.isEmpty(orgList)) {
                List<String> orgNameList = Lists.newArrayList();
                List<String> orgCodeList = Lists.newArrayList();
                orgList.forEach(a -> {
                    orgNameList.add(a.getOrgName());
                    orgCodeList.add(a.getOrgCode());
                });
                customerVo.setOrgName(String.join(",", orgNameList));
                customerVo.setOrgCode(String.join(",", orgCodeList));
            }
            CustomerOrgVo customerOrgVo = customerOrgMap.get(customerVo.getCustomerOrgCode());
            if (Objects.nonNull(customerOrgVo)) {
                customerVo.setCustomerOrgName(customerOrgVo.getCustomerOrgName());
            }
            customerVo.setUnionName(String.format("%s/%s", ObjectUtils.defaultIfNull(customerVo.getCustomerCode(), "")
                    , ObjectUtils.defaultIfNull(customerVo.getCustomerName(), "")));
            //customerVo.setCityName(regionCodeNameMap.get(customerVo.getCityCode()));
            //customerVo.setDistrictName(regionCodeNameMap.get(customerVo.getDistrictCode()));
            //customerVo.setProvinceName(regionCodeNameMap.get(customerVo.getProvinceCode()));
        }
        return list;
    }

    /**
     * 封装客户信息关联企业组织映射(key:客户编码,value:客户关联企业组织集合)
     *
     * @param entities 客户信息列表
     * @return 客户信息关联企业组织映射
     */
    private Map<String, List<OrgVo>> buildOrgMap(List<CustomerEntity> entities) {
        Map<String, List<OrgVo>> resultMap = Maps.newHashMap();
        if (CollectionUtil.isEmpty(entities)) {
            return resultMap;
        }
        Set<String> cusCodeSet = entities.stream().map(CustomerEntity::getCustomerCode).collect(Collectors.toSet());
        List<CustomerROrgEntity> customerROrgEntities = customerROrgService.findByCustomerCodes(cusCodeSet);
        if (CollectionUtil.isEmpty(customerROrgEntities)) {
            return resultMap;
        }
        Map<String, List<CustomerROrgEntity>> customerROrgMap = customerROrgEntities.stream().collect(Collectors.groupingBy(CustomerROrgEntity::getCustomerCode));
        for (CustomerEntity entity : entities) {
            entity.setOrgList(customerROrgMap.getOrDefault(entity.getCustomerCode(), Lists.newArrayList()));
        }
        Set<String> orgCodeList = Sets.newHashSet();
        entities.forEach(customerEntity -> {
            if (!CollectionUtil.isEmpty(customerEntity.getOrgList())) {
                orgCodeList.addAll(customerEntity.getOrgList().stream().map(CustomerROrgEntity::getOrgCode)
                        .collect(Collectors.toList()));
            }
        });
        if (CollectionUtil.isEmpty(orgCodeList)) {
            return resultMap;
        }
        List<OrgVo> orgVoList = this.orgVoService.findByOrgCodes(Lists.newArrayList(orgCodeList));
        if (CollectionUtil.isEmpty(orgVoList)) {
            return resultMap;
        }
        Map<String, OrgVo> orgMap = orgVoList.stream()
                .collect(Collectors.toMap(OrgVo::getOrgCode, t -> t, (key1, key2) -> key2));
        //封装客户信息关联企业组织映射(key:客户编码,value:客户关联企业组织)
        entities.forEach(customerEntity -> {
            if (CollectionUtil.isEmpty(customerEntity.getOrgList())) {
                return;
            }
            List<OrgVo> list = Lists.newArrayList();
            for (CustomerROrgEntity customerROrgEntity : customerEntity.getOrgList()) {
                OrgVo orgVo = orgMap.get(customerROrgEntity.getOrgCode());
                if (Objects.nonNull(orgVo)) {
                    list.add(orgVo);
                }
            }
            resultMap.put(customerEntity.getCustomerCode(), list);
        });
        return resultMap;
    }

    /**
     * 封装客户信息关联客户组织映射(key:客户编码,value:客户关联客户组织)
     *
     * @param entities 客户信息列表
     * @return 客户信息关联客户组织映射
     */
    private Map<String, CustomerOrgVo> buildCustomerOrgMap(List<CustomerEntity> entities) {
        Map<String, CustomerOrgVo> resultMap = Maps.newHashMap();
        if (CollectionUtil.isEmpty(entities)) {
            return resultMap;
        }
        List<String> orgCodeList = entities.stream().map(CustomerEntity::getCustomerOrgCode)
                .collect(Collectors.toList());
        orgCodeList = orgCodeList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(orgCodeList)) {
            return resultMap;
        }
        List<CustomerOrgVo> orgVoList = this.customerOrgVoSdkService.findListByCodes(orgCodeList);
        if (CollectionUtil.isEmpty(orgVoList)) {
            return resultMap;

        }
        Map<String, CustomerOrgVo> orgMap = orgVoList.stream()
                .collect(Collectors.toMap(CustomerOrgVo::getCustomerOrgCode, t -> t, (key1, key2) -> key2));
        //封装客户信息关联客户组织映射(key:客户编码,value:客户关联客户组织)
        entities.forEach(customerEntity -> {
            CustomerOrgVo customerOrgVo = orgMap.get(customerEntity.getCustomerOrgCode());
            if (Objects.nonNull(customerOrgVo)) {
                resultMap.put(customerEntity.getCustomerOrgCode(), customerOrgVo);
            }
        });
        return resultMap;
    }


    /**
     * 处理销售区域变成树形结构
     */
    private void dealSaleAreaInfo(CustomerVo customerVo) {
        if (Objects.isNull(customerVo)
                || CollectionUtil.isEmpty(customerVo.getSaleAreaList())) {
            return;
        }
        List<CustomerSaleAreaVo> customerSaleAreaVos = customerVo.getSaleAreaList();
        customerSaleAreaVos = customerSaleAreaVos.stream().filter(e -> StringUtils.isNotBlank(e.getRegionCode())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(customerSaleAreaVos)) {
            customerVo.setSaleAreaList(null);
            return;
        }
        Map<String, List<CustomerSaleAreaVo>> cusSaleVoMap = customerSaleAreaVos.stream().collect(Collectors.groupingBy(CustomerSaleAreaVo::getParentCode));
        List<String> existsParentIdList = customerSaleAreaVos.stream()
                .map(CustomerSaleAreaVo::getParentCode).filter(cusSaleVoMap::containsKey)
                .collect(Collectors.toList());
        Iterator<CustomerSaleAreaVo> iterator = customerSaleAreaVos.iterator();
        while (iterator.hasNext()) {
            CustomerSaleAreaVo next = iterator.next();
            List<CustomerSaleAreaVo> children = cusSaleVoMap.get(next.getRegionCode());
            next.setChildren(children);
            if (!("00".equals(next.getParentCode()) || !existsParentIdList.contains(
                    next.getParentCode()))) {
                iterator.remove();
            }
        }
        log.info("customerSaleAreaVos:{}", customerSaleAreaVos);
        customerVo.setSaleAreaList(customerSaleAreaVos);
    }

    @Override
    public Set<String> findByCustomerDto(CustomerSelectDto dto) {
        if (ObjectUtils.isEmpty(dto)) {
            return new HashSet<>(0);
        }
        return this.customerRepository.findByCustomerDto(dto);
    }

    @Override
    public CustomerRelationInfoVo findCustomerDetailsByDto(CustomerSelectDto dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        List<CustomerVo> customerVos = this.customerRepository.findCustomerDetailsByDto(dto);
        if (CollectionUtil.isEmpty(customerVos)) {
            return null;
        }
        CustomerRelationInfoVo customerRelationInfoVo = new CustomerRelationInfoVo();
        Set<String> orgCodes = customerVos.stream()
                .filter(customerVo -> StringUtils.isNotBlank(customerVo.getOrgCode()))
                .map(CustomerVo::getOrgCode).collect(
                        Collectors.toSet());
        customerRelationInfoVo.setOrgCodes(orgCodes);

        Set<String> channelCodes = customerVos.stream()
                .filter(customerVo -> StringUtils.isNotBlank(customerVo.getChannelCode()))
                .map(CustomerVo::getChannelCode).collect(
                        Collectors.toSet());
        customerRelationInfoVo.setChannelCodes(channelCodes);

        Set<String> customerTypes = customerVos.stream()
                .filter(customerVo -> StringUtils.isNotBlank(customerVo.getCustomerType()))
                .map(CustomerVo::getCustomerType).collect(
                        Collectors.toSet());
        customerRelationInfoVo.setCustomerTypes(customerTypes);

        return customerRelationInfoVo;
    }

    @Override
    public List<CustomerVo> findCustomerByCodes(Set<String> customerCodes, Boolean shareBenefits) {
        if (CollectionUtil.isEmpty(customerCodes) | Objects.isNull(shareBenefits)) {
            return null;
        }
        List<CustomerVo> customerVoList = this.customerRepository.findCustomerByCodes(customerCodes, shareBenefits);
        this.buildCustomerWarehouseAddress(customerVoList);
        return customerVoList;
    }

    /**
     * 通过客户名称查询客户信息
     *
     * @param customerNameList
     * @return
     */
    @Override
    public List<CustomerVo> findCustomerByCustomerNameList(List<String> customerNameList) {
        List<CustomerVo> customerVoList = Lists.newArrayList();
        List<List<String>> partitionList = Lists.partition(customerNameList, 800);
        for (List<String> strings : partitionList) {
            List<CustomerVo> customerVos = customerRepository.findCustomerByCustomerNameList(strings);
            if (!CollectionUtil.isEmpty(customerVos)) {
                customerVoList.addAll(customerVos);
            }
        }
        this.buildCustomerWarehouseAddress(customerVoList);
        return customerVoList;
    }


    private static final String bw_terminal_tag = "C001";

    @Override
    public Page<CustomerVo> findCustomerByConditions(Pageable pageable, CustomerQueryDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
        dto = ObjectUtils.defaultIfNull(dto, new CustomerQueryDto());
        Assert.notNull(dto.getLongitude(), "经度不能为空");
        Assert.notNull(dto.getLatitude(), "纬度不能为空");
        Page<CustomerVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());

        //获取当前登录人信息
//        AbstractCrmUserIdentity loginDetails = this.loginUserService.getAbstractLoginUser();
//        String postCode = loginDetails.getPostCode();
//        List<PositionVo> positionVoList = positionVoService.findAllChildrenByCode(postCode);
//        Assert.notEmpty(positionVoList, "当前登录人职位下级获取失败");
//        Set<String> positionCodeSet = positionVoList.stream().map(PositionVo::getPositionCode).collect(Collectors.toSet());
//        Assert.notEmpty(positionCodeSet, "当前登录人职位下级获取失败");
//        dto.setPositionCodeSet(Sets.newHashSet(positionCodeSet));

        if (StringUtil.isNotEmpty(dto.getOrgCode())) {
            List<OrgVo> orgVoList = orgVoService.findAllChildrenByOrgCode(dto.getOrgCode());
            if (CollectionUtil.isNotEmpty(orgVoList)) {
                dto.setOrgCode("");
                dto.setOrgCodeList(orgVoList.stream().filter(k -> StringUtil.isNotEmpty(k.getOrgCode()))
                        .map(OrgVo::getOrgCode).distinct().collect(Collectors.toList()));
            }
            if (CollectionUtil.isEmpty(dto.getOrgCodeList())) {
                //空组织
                return page;
            }
        }
        page = this.customerRepository.findCustomerByConditions(page, dto);
        List<CustomerVo> voList = page.getRecords();
        if (CollectionUtils.isEmpty(voList)) {
            return page;
        }
        this.buildTagInfo(voList);
        //查询终端类型
        List<String> customerCodes = voList.stream().map(x -> x.getCustomerCode()).collect(Collectors.toList());
        List<CustomerVo> terminalRelateCusList = customerRepository.findCusRelateTerminal(customerCodes, null);
        Map<String, Integer> terminalRelateCusMap = terminalRelateCusList.stream().collect(Collectors.toMap(x -> x.getCustomerCode(), l -> l.getTerminalNum()));

        List<CustomerVo> bwTerminalRelateCusList = customerRepository.findCusRelateTerminal(customerCodes, bw_terminal_tag);
        Map<String, Integer> bwTerminalRelateCusMap = bwTerminalRelateCusList.stream().collect(Collectors.toMap(x -> x.getCustomerCode(), l -> l.getTerminalNum()));
        for (CustomerVo vo : voList) {
            vo.setTerminalNum(terminalRelateCusMap.getOrDefault(vo.getCustomerCode(), 0));
            vo.setBwTerminalNum(bwTerminalRelateCusMap.getOrDefault(vo.getCustomerCode(), 0));
        }
        page.setRecords(voList);
        return page;
    }

    public void buildTagInfo(List<CustomerVo> voList) {
        if (CollectionUtil.isEmpty(voList)) {
            return;
        }
        List<String> customerCodes = voList.stream().map(CustomerVo::getCustomerCode).collect(Collectors.toList());
        List<CustomerTagVo> byCustomerCodes = this.customerTagVoService.findByCustomerCodes(customerCodes);
        if (CollectionUtil.isEmpty(byCustomerCodes)) {
            return;
        }
        Map<String, List<CustomerTagVo>> map = byCustomerCodes.stream().collect(Collectors.groupingBy(CustomerTagVo::getCustomerCode));
        voList.forEach(record -> {
            record.setTagVos(map.getOrDefault(record.getCustomerCode(), Lists.newArrayList()));
        });
    }

    /**
     * 根据终端编码，维护经纬度为空的客户经纬度
     *
     * @param customer
     * @return
     */
    @Override
    public Result<?> updateLonAndLatByCustomerCode(CustomerDto customer) {
        this.customerRepository.updateLonAndLatByCustomerCode(customer);
        return Result.ok();
    }


    /**
     * 通过erp+公司编码查询
     *
     * @param erpCode
     * @param companyCode
     * @return
     */
    @Override
    public CustomerVo findByErpCodeAndCompanyCode(String erpCode, String companyCode) {
        CustomerEntity entity = customerRepository.findByErpCodeAndCompanyCode(erpCode, companyCode);
        if (ObjectUtils.isNotEmpty(entity)) {
            return nebulaToolkitService.copyObjectByWhiteList(entity, CustomerVo.class, HashSet.class, ArrayList.class);
        }
        return null;
    }

    @Override
    public List<CustomerVo> findByPayerCodes(Set<String> payerCodes) {
        if (CollectionUtils.isEmpty(payerCodes)) {
            return Lists.newArrayList();
        }
        List<CustomerEntity> entities = this.customerRepository.findByPayerCodes(payerCodes);
        if (CollectionUtils.isEmpty(entities)) {
            return Lists.newArrayList();
        }
        return (List<CustomerVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entities, CustomerEntity.class,
                CustomerVo.class, HashSet.class, ArrayList.class);
    }

    /**
     * 客户信息补充收货地址
     *
     * @param vo
     */
    private void buildCustomerAddress(CustomerVo vo) {
        if (Objects.isNull(vo)) {
            return;
        }
        List<CustomerAddressVo> customerAddressVos = this.customerAddressVoService.findAddressByCustomerCode(vo.getCustomerCode());
        if (CollectionUtil.isEmpty(customerAddressVos)) {
            return;
        }
        vo.setCustomerAddressVos(customerAddressVos);
    }

    /**
     * 客户信息补充仓库地址
     *
     * @param vo
     */
    private void buildCustomerWarehouseAddress(CustomerVo vo) {
        if (Objects.isNull(vo)) {
            return;
        }
        this.buildCustomerWarehouseAddress(Collections.singletonList(vo));
    }

    /**
     * 客户信息补充仓库地址
     *
     * @param voList
     */
    private void buildCustomerWarehouseAddress(List<CustomerVo> voList) {
        if (CollectionUtil.isEmpty(voList)) {
            return;
        }
        List<String> customerCodeList = voList.stream()
                .filter(k -> StringUtils.isNotEmpty(k.getCustomerCode()))
                .map(CustomerVo::getCustomerCode).distinct().collect(Collectors.toList());
        List<CustomerWarehouseAddressVo> warehouseAddressVoList = this.customerWarehouseAddressService.findWarehouseAddressByCustomerCodes(customerCodeList);
        if (CollectionUtil.isEmpty(warehouseAddressVoList)) {
            return;
        }
        Map<String, List<CustomerWarehouseAddressVo>> warehouseVoMap = warehouseAddressVoList.stream()
                .filter(k -> StringUtils.isNotEmpty(k.getCustomerCode()))
                .collect(Collectors.groupingBy(CustomerWarehouseAddressVo::getCustomerCode));
        voList.forEach(item -> {
            item.setWarehouseAddressList(warehouseVoMap.getOrDefault(item.getCustomerCode(), Lists.newArrayList()));
        });
    }

    /**
     * 客户信息补充送达方
     *
     * @param vo
     */
    private void buildCustomerDestination(CustomerVo vo) {
        if (Objects.isNull(vo)) {
            return;
        }
        this.buildCustomerDestination(Collections.singletonList(vo));
    }


    /**
     * 客户信息补充送达方
     *
     * @param voList
     */
    private void buildCustomerDestination(List<CustomerVo> voList) {
        if (CollectionUtil.isEmpty(voList)) {
            return;
        }
        List<String> customerCodeList = voList.stream()
                .filter(k -> StringUtils.isNotEmpty(k.getCustomerCode()))
                .map(CustomerVo::getCustomerCode).distinct().collect(Collectors.toList());
        List<CustomerDestinationVo> destinationVoList = this.customerDestinationService.findByCustomerCodes(customerCodeList);
        if (CollectionUtil.isEmpty(destinationVoList)) {
            return;
        }

        Map<String, String> customerCodeNameMap = voList.stream()
                .filter(k -> StringUtils.isNotEmpty(k.getCustomerCode()))
                .collect(Collectors.toMap(CustomerVo::getCustomerCode, CustomerVo::getCustomerName, (n, o) -> n));

        List<String> destinationCodeList = destinationVoList.stream().filter(k -> StringUtil.isNotEmpty(k.getDestinationCode()))
                .map(CustomerDestinationVo::getDestinationCode).distinct().collect(Collectors.toList());
        List<MdmDestinationEntity> destinationEntityList = mdmDestinationRepository.findDetailsByIdsOrCodes(null, destinationCodeList);
        Map<String, String> codeNameMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(destinationEntityList)) {
            codeNameMap.putAll(destinationEntityList.stream().filter(k -> StringUtil.isNotEmpty(k.getDestinationCode()))
                    .collect(Collectors.toMap(MdmDestinationEntity::getDestinationCode, MdmDestinationEntity::getDestinationName, (n, o) -> n)));
        }
        destinationVoList.forEach(item -> {
            item.setDestinationName(codeNameMap.getOrDefault(item.getDestinationCode(), ""));
            if (StringUtil.isEmpty(item.getDestinationName())) {
                item.setDestinationName(customerCodeNameMap.getOrDefault(item.getDestinationCode(), ""));
            }
        });
        Map<String, List<CustomerDestinationVo>> warehouseVoMap = destinationVoList.stream()
                .filter(k -> StringUtils.isNotEmpty(k.getCustomerCode()))
                .collect(Collectors.groupingBy(CustomerDestinationVo::getCustomerCode));
        voList.forEach(item -> {
            item.setDestinationList(warehouseVoMap.getOrDefault(item.getCustomerCode(), Lists.newArrayList()));
        });
    }

    /**
     * 根据客户编码更新客户活跃状态熔断
     *
     * @param dtoList
     * @return com.biz.crm.business.common.sdk.model.Result<?>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/23 21:10
     */
    @Override
    public Result<?> updateCustomerActiveState(List<CustomerDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Result.ok();
        }
        customerRepository.updateCustomerActiveState(dtoList);
        return Result.ok();
    }

    /**
     * 根据条件查询客户编码
     *
     * @param dto
     * @return
     */
    @Override
    public Set<String> findCustomerCodesByCondition(CustomerSelectDto dto) {
        return customerRepository.findCustomerCodesByCondition(dto);
    }


    @Override
    public List<CustomerVo> loadCacheCustomer() {
        String key = CustomerConstant.CUSTOMER_LOAD_REDIS_KEY;
        if (redisService.hasKey(key)) {
            return (List<CustomerVo>) redisService.get(key);
        }
        List<CustomerVo> customerVos = customerRepository.loadCacheCustomer();
        redisService.set(key, customerVos, 18000);
        return customerVos;
    }

    @Override
    public List<String> findCustomerCodeByTags(List<String> tagCodes) {
        List<CustomerEntity> customerEntities = customerRepository.findCustomerCodeByTags(tagCodes);
        return customerEntities.stream().map(CustomerEntity::getCustomerCode).collect(Collectors.toList());
    }


    @Override
    public Map<String, List<CustomerVo>> findCustomerListByOrgCodes(List<String> orgCodes) {
        Map<String, List<CustomerVo>> map = Maps.newHashMap();
        for (String orgCode : orgCodes) {
            List<CustomerVo> customerVos = customerRepository.findCustomerListByOrgCodes(orgCode);
            if (CollectionUtil.isNotEmpty(customerVos)) {
                map.put(orgCode, customerVos);
            }
        }
        return map;
    }


    @Override
    public List<CustomerVo> findCustomerListByChannelDepartmentCodeList(List<String> channelDepartmentCodeList) {
        List<CustomerEntity> customerEntities = customerRepository.findCustomerListByChannelDepartmentCodeList(channelDepartmentCodeList);
        if (CollectionUtil.isEmpty(customerEntities)) {
            return Lists.newArrayList();
        }
        return (List<CustomerVo>) nebulaToolkitService.copyCollectionByWhiteList(customerEntities, CustomerEntity.class, CustomerVo.class, HashSet.class, ArrayList.class);
    }
}
