package com.biz.crm.mdm.business.customer.local.authority;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.mdm.business.customer.local.entity.CustomerROrgEntity;
import com.biz.crm.mdm.business.customer.local.service.CustomerROrgService;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.bizunited.nebula.mars.sdk.register.SelectAuthorityModeRegister;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: heguanyun
 * @Date: 2022/4/7 17:25
 * description:
 */
@Component
public class CustomerOrgAuthorityModeRegister implements SelectAuthorityModeRegister {

    @Autowired(required = false)
    private CustomerROrgService customerROrgService;

    @Autowired(required = false)
    private PositionVoService positionVoService;

    @Override
    public String modeKey() {
        return "customerOrgAuthorityModeRegister";
    }

    @Override
    public String modeName() {
        return "按照当前登录者组织关联的客户维度的值确认";
    }

    @Override
    public String controlKey() {
        return "customerOrgAuthorityModeRegister";
    }

    @Override
    public int sort() {
        return 1;
    }

    @Override
    public String groupCode() {
        return "customer_position_scope";
    }

    @Override
    public boolean isArrayValue() {
        return true;
    }

    @Override
    public boolean isStaticValue() {
        return false;
    }

    @Override
    public Class<?> modeValueClass() {
        return String.class;
    }

    @Override
    public Object staticValue(String[] staticValues) {
        return CommonConstant.NOT_AUTHORITY_ARR;
    }

    @Override
    public Object dynamicValue(UserIdentity loginDetails, String modeGroupCode) {
        String identityType = loginDetails.getIdentityType();
        //如果不是后台管理用户，就不按职位字段进行权限控制
        if (!StringUtils.equals(identityType, "u")) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        AbstractCrmUserIdentity loginUserDetails = (AbstractCrmUserIdentity) loginDetails;
        Object object = loginUserDetails.invokeFieldValue("postCode");
        if (Objects.isNull(object)) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        PositionVo positionVo = positionVoService.findByPositionCode(object.toString());
        if (Objects.isNull(positionVo)
                || StringUtil.isEmpty(positionVo.getOrgCode())) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        List<CustomerROrgEntity> customerOrgEntities =
                customerROrgService.findAllowSaleCustomerByOrgCodes(Collections.singleton(object.toString()));
        if (CollectionUtil.isEmpty(customerOrgEntities)) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        List<String> customerCodes = customerOrgEntities.stream().map(CustomerROrgEntity::getCustomerCode)
                .filter(StringUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(customerCodes)) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        return customerCodes.toArray(new String[0]);
    }

    @Override
    public String converterKey() {
        return "chartArrayMarsAuthorityAstConverter";
    }
}
