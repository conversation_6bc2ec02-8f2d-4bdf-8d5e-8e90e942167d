package com.biz.crm.mdm.business.supplier.local.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.mdm.business.supplier.local.entity.MdmSupplierEntity;
import com.biz.crm.mdm.business.supplier.local.mapper.MdmSupplierMapper;
import com.biz.crm.mdm.business.supplier.sdk.dto.MdmSupplierDto;
import com.biz.crm.mdm.business.supplier.sdk.vo.MdmSupplierVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 供应商的数据库访问类
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/22 18:54
 */
@Component
public class MdmSupplierRepository extends ServiceImpl<MdmSupplierMapper, MdmSupplierEntity> {

    /**
     * 通过ID或者编码获取供应商信息详情(包含关联数据)
     *
     * @param id           供应商ID
     * @param supplierCode 供应商编码
     * @return 供应商信息详情(包含关联数据)
     */
    public MdmSupplierEntity findDetailsByIdOrCode(String id, String supplierCode) {
        if (StringUtils.isAllBlank(id, supplierCode)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(StringUtils.isNotEmpty(id), MdmSupplierEntity::getId, id)
                .eq(StringUtils.isNotEmpty(supplierCode), MdmSupplierEntity::getSupplierCode, supplierCode)
                .eq(MdmSupplierEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(MdmSupplierEntity::getTenantCode, TenantUtils.getTenantCode())
                .one();
    }


    /**
     * 根据ID或编码集合获取供应商详情
     *
     * @param ids
     * @param codeList
     * @return
     */
    public List<MdmSupplierEntity> findDetailsByIdsOrCodes(List<String> ids, List<String> codeList) {
        if (CollectionUtil.isEmpty(ids)
                && CollectionUtil.isEmpty(codeList)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(CollectionUtil.isNotEmpty(ids), MdmSupplierEntity::getId, ids)
                .in(CollectionUtil.isNotEmpty(codeList), MdmSupplierEntity::getSupplierCode, codeList)
                .list();
    }


    /**
     * 批量保存
     *
     * @param saveList
     */
    public void saveBatchXml(List<MdmSupplierEntity> saveList) {
        if (CollectionUtil.isEmpty(saveList)) {
            return;
        }
        Lists.partition(saveList, CommonConstant.MAX_PAGE_SIZE).forEach(list -> {
            this.baseMapper.insertBatchSomeColumn(list);
        });
    }


    public void updateBatchXml(List<MdmSupplierEntity> updateList) {
        if (CollectionUtil.isEmpty(updateList)) {
            return;
        }
        Lists.partition(updateList, CommonConstant.MAX_PAGE_SIZE).forEach(this::updateBatchById);
    }

    public Page<MdmSupplierVo> findSupplierPage(Pageable pageable, MdmSupplierDto dto) {
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
        dto = Optional.ofNullable(dto).orElse(new MdmSupplierDto());
        if (StringUtil.isEmpty(dto.getTenantCode())) {
            dto.setTenantCode(TenantUtils.getTenantCode());
        }
        Page<MdmSupplierVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.baseMapper.findSupplierPage(page, dto);
    }


    public MdmSupplierEntity findDetailBySapCodeAndCompanyCode(String supplierSapCode, String companyCode) {
        return this.lambdaQuery()
                .eq(MdmSupplierEntity::getTenantCode, TenantUtils.getTenantCode())
                .eq(MdmSupplierEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(MdmSupplierEntity::getSupplierSapCode, supplierSapCode)
                .eq(MdmSupplierEntity::getCompanyCode, companyCode).one();
    }
}
