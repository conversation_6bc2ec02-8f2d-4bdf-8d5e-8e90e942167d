package com.biz.crm.mdm.business.customer.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * 客户联系人信息实体
 *
 * <AUTHOR>
 * @date 2021/10/26
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CustomerContactEntity", description = "客户联系人信息实体")
@Entity
@TableName("mdm_customer_contact")
@Table(name = "mdm_customer_contact", indexes = {
    @Index(name = "mdm_customer_contact_index1", columnList = "customer_code"),
    @Index(name = "mdm_customer_contact_index2", columnList = "tenant_code")
})
@org.hibernate.annotations.Table(appliesTo = "mdm_customer_contact", comment = "客户联系人信息表")
public class CustomerContactEntity extends TenantOpEntity {

  private static final long serialVersionUID = -255420824181340972L;

  @ApiModelProperty("客户编码")
  @Column(name = "customer_code", columnDefinition = "VARCHAR(32) COMMENT '客户编码'")
  private String customerCode;

  @ApiModelProperty("联系人账号")
  @Column(name = "contact_account", columnDefinition = "VARCHAR(32) COMMENT '联系人账号'")
  private String contactAccount;

  @ApiModelProperty("联系人姓名")
  @Column(name = "contact_name", columnDefinition = "VARCHAR(128) COMMENT '联系人姓名'")
  private String contactName;

  @ApiModelProperty("联系人电话")
  @Column(name = "contact_phone", columnDefinition = "VARCHAR(16) COMMENT '联系人电话'")
  private String contactPhone;

  @ApiModelProperty("主联系人(true:是,false:否)")
  @Column(name = "contact_main", columnDefinition = "int(1) COMMENT '主联系人,1是0否'")
  private Boolean contactMain;

}
