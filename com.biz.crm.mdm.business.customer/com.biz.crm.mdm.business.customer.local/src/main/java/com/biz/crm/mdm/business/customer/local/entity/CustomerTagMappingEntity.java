package com.biz.crm.mdm.business.customer.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * 标签与经销商关系表
 * <AUTHOR>
 * @date 2021/11/10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CustomerTagMappingEntity", description = "标签与经销商关系表")
@Entity
@TableName("mdm_customer_tag_mapping")
@Table(name = "mdm_customer_tag_mapping", indexes = {
    @Index(name = "mdm_customer_tag_mapping_index1", columnList = "tag_id"),
    @Index(name = "mdm_customer_tag_mapping_index2", columnList = "customer_code")
})
@org.hibernate.annotations.Table(appliesTo = "mdm_customer_tag_mapping", comment = "标签与经销商关系表")
public class CustomerTagMappingEntity extends TenantEntity {
  private static final long serialVersionUID = 7359534430675820270L;

  /**
   * 标签id
   */
  @ApiModelProperty("标签id")
  @Column(name = "tag_id", length = 64, columnDefinition = "VARCHAR(64) COMMENT '标签id'")
  private String tagId;

  /**
   * 经销商编码
   */
  @ApiModelProperty("经销商编码")
  @Column(name = "customer_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '经销商编码'")
  private String customerCode;

}
