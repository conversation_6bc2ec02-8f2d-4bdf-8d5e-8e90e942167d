package com.biz.crm.mdm.business.customer.local.companyseal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.customer.sdk.dto.CompanySealDto;
import com.biz.crm.mdm.business.customer.sdk.vo.CompanySealVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/11 11:40
 **/
public interface CompanySealService {


    Page<CompanySealVo> findList(Pageable pageable, CompanySealDto dto);


    void createOrUpdate(CompanySealDto dto);


    void deleteBatch(List<String> ids);

    CompanySealVo findByCompanyCode(String companyCode);
}
