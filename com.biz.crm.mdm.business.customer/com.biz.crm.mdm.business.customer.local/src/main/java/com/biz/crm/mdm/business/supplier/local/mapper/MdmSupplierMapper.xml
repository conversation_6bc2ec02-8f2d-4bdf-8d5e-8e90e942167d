<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.supplier.local.mapper.MdmSupplierMapper">


    <select id="findSupplierPage" resultType="com.biz.crm.mdm.business.supplier.sdk.vo.MdmSupplierVo">
        select ms.*
        from mdm_supplier ms
        where ms.del_flag = '${@<EMAIL>()}'
          AND ms.tenant_code = #{dto.tenantCode}
        <if test="dto.supplierCode != null and dto.supplierCode != ''">
            <bind name="supplierCode" value="'%' + dto.supplierCode + '%'"/>
            and ms.supplier_code like #{supplierCode}
        </if>
        <if test="dto.supplierSapCode != null and dto.supplierSapCode != ''">
            <bind name="supplierSapCode" value="'%' + dto.supplierSapCode + '%'"/>
            and ms.supplier_sap_code like #{supplierSapCode}
        </if>
        <if test="dto.supplierName != null and dto.supplierName != ''">
            <bind name="supplierName" value="'%' + dto.supplierName + '%'"/>
            and ms.supplier_name like #{supplierName}
        </if>
        <if test="dto.supplierType != null and dto.supplierType != ''">
            and ms.supplier_type = #{dto.supplierType}
        </if>
        <if test="dto.dataSource != null and dto.dataSource != ''">
            and ms.data_source = #{dto.dataSource}
        </if>
        <if test="dto.address != null and dto.address != ''">
            <bind name="address" value="'%' + dto.address + '%'"/>
            and ms.address like #{address}
        </if>
        <if test="dto.companyCode != null and dto.companyCode != ''">
            and ms.company_code = #{dto.companyCode}
        </if>
        order by ms.modify_time desc, ms.id desc
    </select>
</mapper>
