package com.biz.crm.mdm.business.supplier.local.service.internal;

import com.biz.crm.mdm.business.supplier.local.entity.MdmSupplierBankEntity;
import com.biz.crm.mdm.business.supplier.local.repository.MdmSupplierBankRepository;
import com.biz.crm.mdm.business.supplier.local.service.MdmSupplierBankService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户服务接口实现类
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/22 18:47
 */
@Service
@Slf4j
public class MdmSupplierBankServiceImpl implements MdmSupplierBankService {

    @Autowired(required = false)
    @Qualifier("nebulaToolkitService")
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private MdmSupplierBankRepository mdmSupplierBankRepository;

    @Override
    public List<MdmSupplierBankEntity> findBySupplierCode(String supplierCode) {
        return mdmSupplierBankRepository.findBySupplierCode(supplierCode);
    }

    @Override
    public List<MdmSupplierBankEntity> findBySupplierCodes(List<String> supplierCodeList) {
        return mdmSupplierBankRepository.findBySupplierCodes(supplierCodeList);
    }
}