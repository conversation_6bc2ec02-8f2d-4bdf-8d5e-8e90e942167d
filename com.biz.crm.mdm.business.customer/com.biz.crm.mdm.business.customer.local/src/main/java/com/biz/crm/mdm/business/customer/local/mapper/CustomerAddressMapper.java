package com.biz.crm.mdm.business.customer.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.mapper.BusinessBaseMapper;
import com.biz.crm.mdm.business.customer.local.entity.CustomerAddressEntity;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerAddressPageDto;
import org.apache.ibatis.annotations.Param;

/**
 * 客户收货地址表的mybatis-plus接口类 {@link CustomerAddressEntity}
 *
 * <AUTHOR>
 * @date 2021-10-28 17:48:58
 */
public interface CustomerAddressMapper extends BusinessBaseMapper<CustomerAddressEntity> {

  /**
   * 客户收货地址分页列表
   *
   * @param page 分页信息
   * @param dto  分页参数dto
   * @return 分页列表
   */
  Page<CustomerAddressEntity> findByConditions(Page<CustomerAddressEntity> page, @Param("dto") CustomerAddressPageDto dto);

}

