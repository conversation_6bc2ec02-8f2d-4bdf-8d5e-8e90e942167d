package com.biz.crm.mdm.business.supplier.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.supplier.local.entity.MdmSupplierBankEntity;
import com.biz.crm.mdm.business.supplier.local.entity.MdmSupplierEntity;
import com.biz.crm.mdm.business.supplier.local.repository.MdmSupplierRepository;
import com.biz.crm.mdm.business.supplier.local.service.MdmSupplierBankService;
import com.biz.crm.mdm.business.supplier.local.service.MdmSupplierService;
import com.biz.crm.mdm.business.supplier.sdk.dto.MdmSupplierDto;
import com.biz.crm.mdm.business.supplier.sdk.service.MdmSupplierVoService;
import com.biz.crm.mdm.business.supplier.sdk.vo.MdmSupplierBankVo;
import com.biz.crm.mdm.business.supplier.sdk.vo.MdmSupplierVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户信息VO服务接口实现类
 *
 * <AUTHOR>
 * @date 2021/10/27
 */
@Service
@Slf4j
public class MdmSupplierVoServiceImpl implements MdmSupplierVoService {

    @Autowired(required = false)
    private MdmSupplierService mdmSupplierService;

    @Autowired(required = false)
    private MdmSupplierBankService mdmSupplierBankService;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private MdmSupplierRepository mdmSupplierRepository;

    /**
     * 供应商分页查询
     *
     * @param pageable
     * @param dto
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.biz.crm.mdm.business.supplier.sdk.vo.MdmSupplierVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/7 16:12
     */
    @Override
    public Page<MdmSupplierVo> findSupplierPage(Pageable pageable, MdmSupplierDto dto) {
        return mdmSupplierRepository.findSupplierPage(pageable, dto);
    }

    /**
     * 根据ID或者编码获取详情
     *
     * @param id           ID
     * @param supplierCode 编码
     * @return com.biz.crm.mdm.business.supplier.sdk.vo.MdmSupplierVo
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/22 18:51
     */
    @Override
    public MdmSupplierVo findDetailsByIdOrCode(String id, String supplierCode) {
        if (StringUtils.isAllBlank(id, supplierCode)) {
            return null;
        }
        MdmSupplierEntity entity = mdmSupplierService.findDetailsByIdOrCode(id, supplierCode);
        if (Objects.isNull(entity)) {
            return null;
        }
        MdmSupplierVo vo = this.nebulaToolkitService.copyObjectByWhiteList(entity, MdmSupplierVo.class, HashSet.class, ArrayList.class);
        List<MdmSupplierBankEntity> bankEntityList = mdmSupplierBankService.findBySupplierCode(entity.getSupplierCode());
        if (CollectionUtil.isNotEmpty(bankEntityList)) {
            vo.setBankList((List<MdmSupplierBankVo>) this.nebulaToolkitService.copyCollectionByWhiteList(bankEntityList, MdmSupplierBankEntity.class,
                    MdmSupplierBankVo.class, HashSet.class, ArrayList.class));
        }
        return vo;
    }

    /**
     * 根据ID集合获取供应商详情
     *
     * @param ids
     * @return java.util.List<com.biz.crm.mdm.business.supplier.sdk.vo.MdmSupplierVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/25 11:04
     */
    @Override
    public List<MdmSupplierVo> findDetailsByIds(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<MdmSupplierEntity> entityList = mdmSupplierService.findDetailsByIdsOrCodes(ids, null);
        return this.buildVo(entityList);
    }

    /**
     * 根据编码集合获取供应商详情
     *
     * @param supplierCodeList
     * @return java.util.List<com.biz.crm.mdm.business.supplier.sdk.vo.MdmSupplierVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/25 11:04
     */
    @Override
    public List<MdmSupplierVo> findDetailsByCodes(List<String> supplierCodeList) {
        if (CollectionUtil.isEmpty(supplierCodeList)) {
            return Lists.newArrayList();
        }
        List<MdmSupplierEntity> entityList = mdmSupplierService.findDetailsByIdsOrCodes(null, supplierCodeList);
        return this.buildVo(entityList);
    }


    @Override
    public MdmSupplierVo findDetailBySapCodeAndCompanyCode(String supplierSapCode, String companyCode) {
        MdmSupplierEntity supplierEntity = mdmSupplierRepository.findDetailBySapCodeAndCompanyCode(supplierSapCode, companyCode);
        if (ObjectUtils.isEmpty(supplierEntity)) {
            return null;
        }
        return nebulaToolkitService.copyObjectByBlankList(supplierEntity, MdmSupplierVo.class, HashSet.class, ArrayList.class);
    }

    /**
     * 根据实体构建VO
     *
     * @param entityList
     * @return
     */
    private List<MdmSupplierVo> buildVo(List<MdmSupplierEntity> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return Lists.newArrayList();
        }
        List<MdmSupplierVo> supplierVoList = ((List<MdmSupplierVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entityList, MdmSupplierEntity.class,
                MdmSupplierVo.class, HashSet.class, ArrayList.class));
        List<String> supplierCodeList = entityList.stream().filter(k -> StringUtil.isNotEmpty(k.getSupplierCode()))
                .map(MdmSupplierEntity::getSupplierCode).distinct().collect(Collectors.toList());
        List<MdmSupplierBankEntity> bankEntityList = mdmSupplierBankService.findBySupplierCodes(supplierCodeList);
        if (CollectionUtil.isNotEmpty(bankEntityList)) {
            List<MdmSupplierBankVo> bankVoList = ((List<MdmSupplierBankVo>) this.nebulaToolkitService.copyCollectionByWhiteList(bankEntityList, MdmSupplierBankEntity.class,
                    MdmSupplierBankVo.class, HashSet.class, ArrayList.class));
            if (CollectionUtil.isNotEmpty(bankVoList)) {
                Map<String, List<MdmSupplierBankVo>> bankMap = bankVoList.stream().filter(k -> StringUtil.isNotEmpty(k.getSupplierCode()))
                        .collect(Collectors.groupingBy(MdmSupplierBankVo::getSupplierCode));
                supplierVoList.forEach(vo -> {
                    vo.setBankList(bankMap.getOrDefault(vo.getSupplierCode(), Lists.newArrayList()));
                });
            }
        }
        return supplierVoList;
    }
}
