package com.biz.crm.mdm.business.customer.local.repository;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.mdm.business.customer.local.entity.CustomerAddressEntity;
import com.biz.crm.mdm.business.customer.local.entity.CustomerTagEntity;
import com.biz.crm.mdm.business.customer.local.mapper.CustomerTagMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 客户标签表的数据库访问类 {@link CustomerTagEntity}
 *
 * <AUTHOR>
 * @date 2021-10-26 16:27:41
 */
@Component
public class CustomerTagRepository extends ServiceImpl<CustomerTagMapper, CustomerTagEntity> {

  /**
   * 通过客户编码查询客户标签列表
   *
   * @param customerCode 客户编码
   * @return 客户标签列表
   */
  public List<CustomerTagEntity> findByCustomerCode(String customerCode) {
    return this.baseMapper.findByCustomerCode(customerCode, TenantUtils.getTenantCode());
  }

  /**
   * 通过客户编码查询客户标签列表
   *
   * @param customerCodes 客户编码
   * @return 客户标签列表
   */
  public List<CustomerTagEntity> findByCustomerCodes(List<String> customerCodes) {
    return this.baseMapper.findByCustomerCodes(customerCodes, TenantUtils.getTenantCode());
  }

  /**
   * 批量删除
   *
   * @param ids ID集合
   */
  public void deleteBatch(List<String> ids) {
    this.lambdaUpdate()
        .in(CustomerTagEntity::getId, ids)
        .eq(CustomerTagEntity::getTenantCode,TenantUtils.getTenantCode())    //新增租户编号判断条件
        .set(CustomerTagEntity::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
        .update();
  }

  /**
   * 根据标签名称查询
   * @param tagName
   * @return
   */
  public CustomerTagEntity findByTagName(String tagName) {
    return this.lambdaQuery().
            eq(CustomerTagEntity::getTagName, tagName)
           .eq(CustomerTagEntity::getTenantCode,TenantUtils.getTenantCode())    //新增租户编号判断条件
            .one();
  }

  /**
   * 修改启禁用
   * @param ids
   * @param code
   */
  public void updateEnableStatusByIds(List<String> ids, String code) {
    this.lambdaUpdate()
            .in(CustomerTagEntity::getId, ids)
            .eq(CustomerTagEntity::getTenantCode,TenantUtils.getTenantCode())    //新增租户编号判断条件
            .set(CustomerTagEntity::getEnableStatus, code)
            .update();
  }

  /**
   * 查看详情（包括关联信息）
   * @param id
   * @return
   */
  public CustomerTagEntity findDetailById(String id) {
    //新增租户编号判断条件
    return this.baseMapper.findDetailById(id,TenantUtils.getTenantCode());
  }

  /**
   * 根据状态查询
   * @param status
   * @return
   */
  public List<CustomerTagEntity> findDetailByStatusAndUpdateType(String status, Boolean type) {
    return this.baseMapper.findDetailByStatus(status, type,TenantUtils.getTenantCode());    //新增租户编号判断条件
  }

  /**
   * 重构修改方法
   * @param entity
   * @param tenantCode
   */
  public void updateByIdAndTenantCode(CustomerTagEntity entity, String tenantCode) {
    LambdaUpdateWrapper<CustomerTagEntity>lambdaUpdateWrapper = Wrappers.lambdaUpdate();
    lambdaUpdateWrapper.eq(CustomerTagEntity::getTenantCode,tenantCode);
    lambdaUpdateWrapper.in(CustomerTagEntity::getId,entity.getId());
    this.baseMapper.update(entity,lambdaUpdateWrapper);
  }

  public List<CustomerTagEntity> listByIdsAndTenantCode(List<String> ids, String tenantCode) {
    return this.lambdaQuery()
        .eq(CustomerTagEntity::getTenantCode,tenantCode)
        .in(CustomerTagEntity::getId,ids)
        .list();
  }
}
