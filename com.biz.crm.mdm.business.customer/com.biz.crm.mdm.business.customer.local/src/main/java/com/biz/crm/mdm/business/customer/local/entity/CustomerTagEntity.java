package com.biz.crm.mdm.business.customer.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 客户标签实体
 *
 * <AUTHOR>
 * @date 2021/10/26
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CustomerTagEntity", description = "客户标签实体")
@Entity
@TableName("mdm_customer_tag")
@Table(name = "mdm_customer_tag", indexes = {
    @Index(name = "mdm_customer_tag_index1", columnList = "tenant_code")
})
@org.hibernate.annotations.Table(appliesTo = "mdm_customer_tag", comment = "客户标签表")
public class CustomerTagEntity extends TenantFlagOpEntity {

  private static final long serialVersionUID = 432064718021397427L;

  /**
   * 标签名称
   */
  @ApiModelProperty("标签名称")
  @Column(name = "tag_name", length = 255, columnDefinition = "VARCHAR(255) COMMENT '标签名称'")
  private String tagName;

  @ApiModelProperty("标签类型")
  @Column(name = "tag_type", length = 255, columnDefinition = "VARCHAR(255) COMMENT '标签类型'")
  private String tagType;

  /**
   * 标签说明
   */
  @ApiModelProperty("标签说明")
  @Column(name = "tag_description", length = 255, columnDefinition = "VARCHAR(255) COMMENT '标签说明'")
  private String tagDescription;

  /**
   * 更新方式（0：实时更新；1：定时更新）
   */
  @ApiModelProperty("更新方式（0：实时更新；1：定时更新）")
  @Column(name = "update_type", length = 1, columnDefinition = "bit(1) COMMENT '更新方式（0：实时更新；1：定时更新）'")
  private Boolean updateType ;

  /**
   * 更新周期（day：天；week：周；month：月；year：年）
   */
  @ApiModelProperty("更新周期（day：天；week：周；month：月；year：年）")
  @Column(name = "update_cycle", length = 64, columnDefinition = "VARCHAR(64) COMMENT '更新周期（day：天；week：周；month：月；year：年）'")
  private String updateCycle ;


  @ApiModelProperty("经销商编码")
  @TableField(exist = false)
  @Transient
  private String customerCode;
  /**
   * 规则信息
   */
  @TableField(exist = false)
  @Transient
  private List<CustomerTagRuleEntity> rules;
}
