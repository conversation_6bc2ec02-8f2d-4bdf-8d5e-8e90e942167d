package com.biz.crm.mdm.business.customer.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 客户收货地址实体类
 *
 * <AUTHOR>
 * @date 2021/10/28
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CustomerAddressEntity", description = "客户收货地址实体类")
@Entity
@TableName("mdm_customer_address")
@Table(name = "mdm_customer_address", indexes = {
    @Index(name = "mdm_customer_address_index1", columnList = "customer_code"),
    @Index(name = "mdm_customer_address_index2", columnList = "tenant_code")
})
@org.hibernate.annotations.Table(appliesTo = "mdm_customer_address", comment = "客户收货地址表")
public class CustomerAddressEntity extends TenantFlagOpEntity {

  private static final long serialVersionUID = 8572125054461710670L;

  @ApiModelProperty("市编码")
  @Column(name = "city_code", columnDefinition = "VARCHAR(32) COMMENT '市编码'")
  private String cityCode;

  @ApiModelProperty("区编码")
  @Column(name = "district_code", columnDefinition = "VARCHAR(32) COMMENT '区编码'")
  private String districtCode;

  @ApiModelProperty("省编码")
  @Column(name = "province_code", columnDefinition = "VARCHAR(32) COMMENT '省编码'")
  private String provinceCode;

  @ApiModelProperty("送达方编码")
  @Column(name = "destination_code", columnDefinition = "VARCHAR(128) COMMENT '送达方编码'")
  private String destinationCode;

  @ApiModelProperty("送达方名称")
  @Column(name = "destination_name", columnDefinition = "VARCHAR(128) COMMENT '送达方名称'")
  private String destinationName;

  @ApiModelProperty("详细地址")
  @Column(name = "detailed_address", columnDefinition = "VARCHAR(128) COMMENT '详细地址'")
  private String detailedAddress;

  @ApiModelProperty("收货人")
  @Column(name = "consignee_name", columnDefinition = "VARCHAR(64) COMMENT '收货人'")
  private String consigneeName;

  @ApiModelProperty("联系人")
  @Column(name = "contact_name", columnDefinition = "VARCHAR(64) COMMENT '联系人'")
  private String contactName;

  @ApiModelProperty("联系电话")
  @Column(name = "contact_phone", columnDefinition = "VARCHAR(64) COMMENT '联系电话'")
  private String contactPhone;

  @ApiModelProperty("是否默认地址，1：是，0：否")
  @Column(name = "default_address", columnDefinition = "int(1) COMMENT '是否默认地址，1：是，0：否'")
  private Boolean defaultAddress;

  @ApiModelProperty("客户编码")
  @Column(name = "customer_code", columnDefinition = "VARCHAR(64) COMMENT '客户编码'")
  private String customerCode;

  @ApiModelProperty("有效开始时间")
  @Column(name = "start_time", columnDefinition = "VARCHAR(64) COMMENT '有效开始时间'")
  private String startTime;

  @ApiModelProperty("有效结束时间")
  @Column(name = "end_time", columnDefinition = "VARCHAR(64) COMMENT '有效结束时间'")
  private String endTime;

  @ApiModelProperty("经度")
  @Column(name = "longitude", columnDefinition = "decimal(12,8) COMMENT '经度'")
  private BigDecimal longitude;

  @ApiModelProperty("纬度")
  @Column(name = "latitude", columnDefinition = "decimal(12,8) COMMENT '纬度'")
  private BigDecimal latitude;

  @ApiModelProperty("数据源[数据字典:mdm_data_source]")
  @Column(name = "data_source", columnDefinition = "VARCHAR(8) DEFAULT 'STD' COMMENT '数据源[数据字典:mdm_data_source]'")
  private String dataSource;

}
