package com.biz.crm.mdm.business.customer.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerSelectDto;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerWarehouseAddressDto;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerWarehouseAddressVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 客户仓库地址表服务接口
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/31 17:54
 */
public interface CustomerWarehouseAddressService {


    /**
     * 保存仓库地址
     *
     * @param warehouseAddressList
     * @param customerCode
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/1 15:30
     */
    void saveList(List<CustomerWarehouseAddressDto> warehouseAddressList, String customerCode);

    /**
     * 根据客户编码获取客户仓库地址
     *
     * @param customerCodeList
     * @return java.util.List<com.biz.crm.mdm.business.customer.sdk.vo.CustomerWarehouseAddressVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/1 15:51
     */
    List<CustomerWarehouseAddressVo> findWarehouseAddressByCustomerCodes(List<String> customerCodeList);

    /**
     * 客户仓库下拉分页查询接口
     * @param pageable
     * @param dto
     * @return
     */
    Page<CustomerWarehouseAddressVo> findWarehouseAddressByConditions(Pageable pageable, CustomerSelectDto dto);
}
