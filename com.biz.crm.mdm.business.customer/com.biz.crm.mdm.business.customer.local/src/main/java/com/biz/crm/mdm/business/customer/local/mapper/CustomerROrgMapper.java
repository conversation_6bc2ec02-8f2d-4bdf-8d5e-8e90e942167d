package com.biz.crm.mdm.business.customer.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.business.common.base.mapper.BusinessBaseMapper;
import com.biz.crm.mdm.business.customer.local.entity.CustomerROrgEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 客户与企业组织关联表的mybatis-plus接口类 {@link CustomerROrgEntity}
 *
 * <AUTHOR>
 * @date 2021-11-10 10:19:10
 */
public interface CustomerROrgMapper extends BusinessBaseMapper<CustomerROrgEntity> {

  /**
   * 获取审批通过且未删除的经销商对应的组织关系明细
   *
   * @param list
   * @param processStatus
   * @param delFlag
   * @param tenantCode
   * @return
   */
  List<CustomerROrgEntity> findAllowSaleCustomerByOrgCodes(
      @Param("list") List<String> list,
      @Param("processStatus") String processStatus,
      @Param("delFlag") String delFlag,
      @Param("tenantCode") String tenantCode);
}
