package com.biz.crm.mdm.business.customer.local.observer;

import com.biz.crm.mdm.business.customer.local.entity.CustomerEntity;
import com.biz.crm.mdm.business.customer.local.entity.CustomerROrgEntity;
import com.biz.crm.mdm.business.customer.local.repository.CustomerROrgRepository;
import com.biz.crm.mdm.business.customer.local.repository.CustomerRepository;
import com.biz.crm.mdm.business.customer.local.service.CustomerService;
import com.biz.crm.mdm.business.customer.sdk.constant.CustomerConstant;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerEventDto;
import com.biz.crm.mdm.business.customer.sdk.event.CustomerEventListener;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerRelateOrgVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.workflow.sdk.constant.enums.ActApproveStatusEnum;
import com.biz.crm.workflow.sdk.dto.CallBackDto;
import com.biz.crm.workflow.sdk.listener.CallBackListener;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * 客户审批回调类
 *
 * 弃用（现用：ProcessCompleteListenerOfCustomer）
 *
 * <AUTHOR>
 */
@Deprecated
@Component
public class CustomerCallbackListener implements CallBackListener {

  @Autowired(required = false)
  private CustomerService customerService;

  @Autowired(required = false)
  private CustomerRepository customerRepository;

  @Autowired(required = false) private NebulaNetEventClient nebulaNetEventClient;

  @Autowired(required = false) private NebulaToolkitService nebulaToolkitService;

  @Autowired(required = false) private CustomerROrgRepository customerROrgRepository;

  @Override
  public void onCallBack(CallBackDto dto) {
    if (!dto.getFormType().equals(CustomerConstant.CUSTOMER_PROCESS_NAME)) {
      return;
    }
    //校验单号(查询方法缺失)
    CustomerEntity customerEntity = this.customerRepository.findByProcessNumber(dto.getProcessNo());
    if (ObjectUtils.isEmpty(customerEntity)) {
      return;
    }
    //校验审批状态
    Validate.isTrue(customerEntity.getProcessStatus().equals(ActApproveStatusEnum.APPROVING.getCode()), "此订单非审批中状态，无法进行操作！");
    //根据回调类别处理对应业务
    if (StringUtils.equals(String.valueOf(dto.getProcessState()), ActApproveStatusEnum.APPROVED.getCode())) {
      //审批通过业务处理
      customerEntity.setProcessStatus(ActApproveStatusEnum.APPROVED.getCode());
      this.customerService.onProcessSuccess(customerEntity);
    } else if (StringUtils.equals(String.valueOf(dto.getProcessState()), ActApproveStatusEnum.REJECTED.getCode())) {
      //审批驳回业务处理
      customerEntity.setProcessStatus(ActApproveStatusEnum.REJECTED.getCode());
    } else if (StringUtils.equals(String.valueOf(dto.getProcessState()), ActApproveStatusEnum.INTERRUPT.getCode())) {
      //流程追回业务处理
      customerEntity.setProcessStatus(ActApproveStatusEnum.INTERRUPT.getCode());
    }
    this.customerRepository.updateById(customerEntity);

    if (customerEntity.getProcessStatus().equals(ActApproveStatusEnum.APPROVED.getCode())) {
      final List<CustomerROrgEntity> orgList =
          this.customerROrgRepository.findByCustomerCodes(
              Lists.newArrayList(customerEntity.getCustomerCode()), customerEntity.getTenantCode());

      CustomerVo vo =
          this.nebulaToolkitService.copyObjectByBlankList(
              customerEntity, CustomerVo.class, HashSet.class, ArrayList.class);
      if (CollectionUtils.isNotEmpty(orgList)) {
        List<CustomerRelateOrgVo> orgVoList =
            (List<CustomerRelateOrgVo>)
                this.nebulaToolkitService.copyCollectionByBlankList(
                    orgList,
                    CustomerROrgEntity.class,
                    CustomerRelateOrgVo.class,
                    HashSet.class,
                    ArrayList.class);
        vo.setOrgList(orgVoList);
      }
      CustomerEventDto eventDto = new CustomerEventDto();
      eventDto.setNewest(vo);
      // 终端审批通过创建事件
      SerializableBiConsumer<CustomerEventListener, CustomerEventDto> onApproved =
          CustomerEventListener::onApproved;
      this.nebulaNetEventClient.publish(eventDto, CustomerEventListener.class, onApproved);
    }
    //---------------  end -------------------
  }
}
