package com.biz.crm.mdm.business.supplier.local.service;

import com.biz.crm.mdm.business.supplier.local.entity.MdmSupplierBankEntity;
import com.biz.crm.mdm.business.supplier.local.entity.MdmSupplierEntity;

import java.util.List;

/**
 * 供应商
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/22 18:45
 */
public interface MdmSupplierBankService {


    /**
     * 根据编码获取里列表
     *
     * @param supplierCode 编码
     * @return com.biz.crm.mdm.business.supplier.sdk.vo.MdmSupplierVo
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/22 18:51
     */
    List<MdmSupplierBankEntity> findBySupplierCode(String supplierCode);



    /**
     * 根据编码获取里列表
     *
     * @param supplierCodeList 编码
     * @return com.biz.crm.mdm.business.supplier.sdk.vo.MdmSupplierVo
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/22 18:51
     */
    List<MdmSupplierBankEntity> findBySupplierCodes(List<String> supplierCodeList);
}
