package com.biz.crm.mdm.business.supplier.local.service;

import com.biz.crm.mdm.business.supplier.local.entity.MdmSupplierEntity;

import java.util.List;

/**
 * 供应商
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/22 18:45
 */
public interface MdmSupplierService {


    /**
     * 根据ID或者编码获取详情
     *
     * @param id           ID
     * @param supplierCode 编码
     * @return com.biz.crm.mdm.business.supplier.sdk.vo.MdmSupplierVo
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/22 18:51
     */
    MdmSupplierEntity findDetailsByIdOrCode(String id, String supplierCode);

    /**
     * 根据ID或编码集合获取供应商详情
     *
     * @param ids
     * @param supplierCodeList
     * @return java.util.List<com.biz.crm.mdm.business.supplier.local.entity.MdmSupplierEntity>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/25 11:04
     */
    List<MdmSupplierEntity> findDetailsByIdsOrCodes(List<String> ids, List<String> supplierCodeList);
}
