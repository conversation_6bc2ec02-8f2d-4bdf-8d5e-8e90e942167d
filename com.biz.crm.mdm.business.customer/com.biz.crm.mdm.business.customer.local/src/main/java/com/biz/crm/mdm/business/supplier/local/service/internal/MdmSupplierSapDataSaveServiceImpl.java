package com.biz.crm.mdm.business.supplier.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.mdm.business.supplier.local.entity.MdmSupplierBankEntity;
import com.biz.crm.mdm.business.supplier.local.entity.MdmSupplierEntity;
import com.biz.crm.mdm.business.supplier.local.repository.MdmSupplierBankRepository;
import com.biz.crm.mdm.business.supplier.local.repository.MdmSupplierRepository;
import com.biz.crm.mdm.business.supplier.local.service.MdmSupplierSapDataSaveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 供应商 保存或更新SAP数据
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/22 18:47
 */
@Service
@Slf4j
public class MdmSupplierSapDataSaveServiceImpl implements MdmSupplierSapDataSaveService {

    @Autowired(required = false)
    private MdmSupplierRepository mdmSupplierRepository;

    @Autowired(required = false)
    private MdmSupplierBankRepository mdmSupplierBankRepository;

    /**
     * 保存或更新SAP数据
     *
     * @param saveList
     * @param updateList
     * @param supplierCodeList
     * @param bankEntityList
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/15 11:44
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveOrUpdateList(List<MdmSupplierEntity> saveList, List<MdmSupplierEntity> updateList,
                                 List<String> supplierCodeList, List<MdmSupplierBankEntity> bankEntityList) {
        if (CollectionUtil.isNotEmpty(saveList)) {
            mdmSupplierRepository.saveBatchXml(saveList);
        }
        if (CollectionUtil.isNotEmpty(updateList)) {
            mdmSupplierRepository.updateBatchXml(updateList);
        }
        if (CollectionUtil.isNotEmpty(supplierCodeList)) {
            mdmSupplierBankRepository.delBySupplierCodeList(supplierCodeList);
        }
        if (CollectionUtil.isNotEmpty(bankEntityList)) {
            mdmSupplierBankRepository.saveBatchXml(bankEntityList);
        }

    }
}