package com.biz.crm.mdm.business.customer.local.mapper;

import com.biz.crm.business.common.base.mapper.BusinessBaseMapper;
import com.biz.crm.mdm.business.customer.local.entity.CustomerDockingEntity;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerDockingVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户对接人表的mybatis-plus接口类 {@link CustomerDockingEntity}
 *
 * <AUTHOR>
 * @date 2021-10-26 16:25:01
 */
public interface CustomerDockingMapper extends BusinessBaseMapper<CustomerDockingEntity> {

    List<CustomerDockingVo> findListByCustomerCodes(@Param("customerCodes") List<String> customerCodes);


    Integer deleteByCustomerCodesAndFixedPositionLevelCode(@Param("customerCodes")List<String> customerCodes);
}

