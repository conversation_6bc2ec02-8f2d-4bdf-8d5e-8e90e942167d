package com.biz.crm.mdm.business.customer.local.service;

import com.biz.crm.mdm.business.customer.local.entity.CustomerMediaEntity;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerUploadDto;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerMediaVo;

import java.util.List;

/**
 * 客户媒体文件表服务接口
 *
 * <AUTHOR>
 * @date 2021-10-26 16:26:37
 */
public interface CustomerMediaService {

  /**
   * 客户媒体文件绑定客户编码
   *
   * @param dtoList      客户媒体文件列表
   * @param customerCode 客户编码
   */
  void rebindCustomerCode(List<CustomerUploadDto> dtoList, String customerCode);

  List<CustomerMediaVo> findByCustomerCodes(List<String> customerCodes);
}
