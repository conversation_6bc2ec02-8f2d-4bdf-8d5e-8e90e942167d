package com.biz.crm.mdm.business.destination.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerDto;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.destination.sdk.dto.MdmDestinationDto;
import com.biz.crm.mdm.business.destination.sdk.service.MdmDestinationVoService;
import com.biz.crm.mdm.business.destination.sdk.vo.MdmDestinationVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 送达方信息管理
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/22 18:58
 */
@Slf4j
@RestController
@RequestMapping("/v1/destination/destination")
@Api(tags = "送达方信息: MdmDestinationVo: 送达方管理")
public class MdmDestinationVoController {

    @Autowired(required = false)
    private MdmDestinationVoService mdmDestinationVoService;

    @ApiOperation(value = "通过送达方ID或者送达方编码查询送达方信息详情(包含送达方信息所有关联数据)")
    @GetMapping("/findDetailsByIdOrCode")
    public Result<MdmDestinationVo> findDetailsByIdOrCode(
            @RequestParam(value = "id", required = false) @ApiParam(name = "id", value = "主键ID") String id,
            @RequestParam(value = "destinationCode", required = false) @ApiParam(name = "destinationCode", value = "送达方编码") String destinationCode) {
        try {
            return Result.ok(mdmDestinationVoService.findDetailsByIdOrCode(id, destinationCode));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "通过客户编码定位对应的送达方")
    @GetMapping("/findDetailsByCustomerCode")
    public Result<MdmDestinationVo> findDetailsByCustomerCode(
            @RequestParam(value = "customerCode") @ApiParam(name = "customerCode", value = "客户编码") String customerCode) {
        try {
            return Result.ok(mdmDestinationVoService.findDetailsByCustomerCode(customerCode));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "通过客户编码定位对应的送达方集合")
    @GetMapping("/findDetailListByCustomerCode")
    public Result<List<MdmDestinationVo>> findDetailListByCustomerCode(
        @RequestParam(value = "customerCode", required = false) @ApiParam(name = "customerCode", value = "客户编码") String customerCode,
        @RequestParam(value = "enableStatus", required = false) @ApiParam(name = "enableStatus", value = "启禁用") String enableStatus,
        @RequestParam(value = "keyword", required = false) @ApiParam(name = "keyword", value = "关键词查询") String keyword) {
        try {
            return Result.ok(mdmDestinationVoService.findDetailListByCustomerCode(customerCode, enableStatus, keyword));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "通过客户编码定位对应的送达方")
    @GetMapping("/findDetailsByCustomerCodes")
    public Result<List<MdmDestinationVo>> findDetailsByCustomerCodes(
            @RequestParam(value = "customerCodes") @ApiParam(name = "customerCodes", value = "客户编码") List<String> customerCodes) {
        try {
            return Result.ok(mdmDestinationVoService.findDetailsByCustomerCodes(customerCodes));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "通过送达方编码查询送达方数据")
    @PostMapping("/findByDestinationCodes")
    public Result<List<MdmDestinationVo>> findByDestinationCodes(@RequestBody List<String> destinationCodes) {
        return Result.ok(mdmDestinationVoService.findByDestinationCodes(destinationCodes));
    }



    @ApiOperation(value = "分页查询")
    @GetMapping("/findByConditions")
    public Result<Page<MdmDestinationVo>> findByConditions(@PageableDefault(50) Pageable pageable,
                                                      @ApiParam(name = "dto", value = "查询参数") MdmDestinationDto dto) {
        try {
            return Result.ok(mdmDestinationVoService.findByConditions(pageable, dto));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "根据客户编码查询关联送达方")
    @GetMapping("/findListByCustomerCodes")
    public Result<List<MdmDestinationVo>> findListByCustomerCodes(@RequestParam(value = "customerCodes")
                                                              @ApiParam(name = "customerCodes", value = "客户编码") List<String> customerCodes) {
        try {
            return Result.ok(mdmDestinationVoService.findListByCustomerCodes(customerCodes));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
