package com.biz.crm.mdm.business.customer.local.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.customer.local.entity.CustomerAddressEntity;
import com.biz.crm.mdm.business.customer.local.entity.CustomerEntity;
import com.biz.crm.mdm.business.customer.local.mapper.CustomerAddressMapper;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerAddressPageDto;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerAddressQueryDto;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 客户收货地址表的数据库访问类 {@link CustomerAddressEntity}
 *
 * <AUTHOR>
 * @date 2021-10-28 17:48:58
 */
@Component
public class CustomerAddressRepository extends ServiceImpl<CustomerAddressMapper, CustomerAddressEntity> {

  /**
   * 通过客户编码解绑默认地址
   *
   * @param customerCode 客户编码
   * @param tenantCode   租户编码
   */
  public void unbindDefaultAddressByCustomerCode(String customerCode, String tenantCode) {
    this.lambdaUpdate()
        .eq(CustomerAddressEntity::getTenantCode, tenantCode)
        .eq(CustomerAddressEntity::getCustomerCode, customerCode)
        .eq(CustomerAddressEntity::getDefaultAddress, Boolean.TRUE)
        .set(CustomerAddressEntity::getDefaultAddress, Boolean.FALSE)
        .update();
  }

  /**
   * 批量删除
   *
   * @param ids ID集合
   */
  public void updateDelFlagByIds(List<String> ids) {
    this.lambdaUpdate()
        .eq(CustomerAddressEntity::getTenantCode, TenantUtils.getTenantCode())    //新增租户编号判断条件
        .in(CustomerAddressEntity::getId, ids)
        .set(CustomerAddressEntity::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
        .update();
  }

  /**
   * 客户收货地址分页列表
   *
   * @param dto      请求参数dto
   * @param pageable 分页信息
   * @return Page<CustomerAddressEntity> 客户收货地址分页信息
   */
  public Page<CustomerAddressEntity> findByConditions(Pageable pageable, CustomerAddressPageDto dto) {
    Page<CustomerAddressEntity> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return this.baseMapper.findByConditions(page, dto);
  }

  /**
   * 通过ID获取客户收货地址信息
   *
   * @param id 收货地址ID
   * @return 客户收货地址信息
   */
  public CustomerAddressEntity findById(String id) {
    return this.lambdaQuery()
        .eq(CustomerAddressEntity::getId, id)
        .eq(CustomerAddressEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        //新增租户编号
        .eq(CustomerAddressEntity::getTenantCode,TenantUtils.getTenantCode())
        .one();
  }

  /**
   * 查询客户收货地址列表
   *
   * @param dto 查询dto
   * @return 客户收货地址信息列表
   */
  public List<CustomerAddressEntity> findByCustomerAddressQueryDto(CustomerAddressQueryDto dto) {
    return this.lambdaQuery()
        .eq(CustomerAddressEntity::getTenantCode, dto.getTenantCode())
        .eq(CustomerAddressEntity::getCustomerCode, dto.getCustomerCode())
        .eq(CustomerAddressEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(Objects.nonNull(dto.getDefaultAddress()), CustomerAddressEntity::getDefaultAddress, dto.getDefaultAddress())
        .list();
  }

  /**
   * 重构修改方法  通过id和租户编号修改
   * @param updateEntity
   * @param tenantCode
   */
  public void updateByIdAndTenantCode(CustomerAddressEntity updateEntity, String tenantCode) {
    LambdaUpdateWrapper<CustomerAddressEntity>lambdaUpdateWrapper= Wrappers.lambdaUpdate();
    lambdaUpdateWrapper.eq(CustomerAddressEntity::getTenantCode,tenantCode);
    lambdaUpdateWrapper.in(CustomerAddressEntity::getId,updateEntity.getId());
    this.baseMapper.update(updateEntity,lambdaUpdateWrapper);
  }

  /**
   *
   * @param id
   * @param tenantCode
   * @return
   */
  public CustomerAddressEntity findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(CustomerAddressEntity::getTenantCode,tenantCode)
        .in(CustomerAddressEntity::getId,id)
        .one();
  }

  public List<CustomerAddressEntity> listByIdsAndTenantCode(List<String> ids, String tenantCode) {
    return this.lambdaQuery()
        .eq(CustomerAddressEntity::getTenantCode,tenantCode)
        .in(CustomerAddressEntity::getId,ids)
        .list();
  }

  /**
   * 查询当前客户所关联的收货地址
   * @param customerCode
   * @return
   */
  public List<CustomerAddressEntity> findDefaultAddressByCustomerCode(String customerCode) {
    return  this.lambdaQuery()
        .eq(CustomerAddressEntity::getCustomerCode,customerCode)
        .eq(CustomerAddressEntity::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
        .eq(CustomerAddressEntity::getDelFlag,DelFlagStatusEnum.NORMAL.getCode())
        .list();
  }

  /**
   * 根据客户信息查询关联的收货地址
   *
   * @param customerCode
   * @return
   */
  public List<CustomerAddressEntity> findAddressByCustomerCode(String customerCode) {
    return this.lambdaQuery()
        .eq(CustomerAddressEntity::getTenantCode, TenantUtils.getTenantCode())
        .eq(CustomerAddressEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(CustomerAddressEntity::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
        .eq(CustomerAddressEntity::getCustomerCode, customerCode)
        .list();
  }

  public void deleteByCustomerCodesAndDataSource(List<String> codeList, String dataSource) {
    if (CollectionUtil.isEmpty(codeList)
            || StringUtil.isEmpty(dataSource)) {
      return;
    }
    this.lambdaUpdate()
            .in(CustomerAddressEntity::getCustomerCode, codeList)
            .eq(CustomerAddressEntity::getDataSource, dataSource)
            .remove();
  }

  public void saveBatchXml(List<CustomerAddressEntity> saveList) {
    if (CollectionUtil.isEmpty(saveList)) {
      return;
    }
    Lists.partition(saveList, CommonConstant.MAX_PAGE_SIZE).forEach(list -> {
      this.baseMapper.insertBatchSomeColumn(list);
    });
  }
}
