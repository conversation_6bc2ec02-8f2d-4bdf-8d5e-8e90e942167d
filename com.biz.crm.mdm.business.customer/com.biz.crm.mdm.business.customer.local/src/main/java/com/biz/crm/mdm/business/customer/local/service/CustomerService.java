package com.biz.crm.mdm.business.customer.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.customer.local.entity.CustomerEntity;
import com.biz.crm.mdm.business.customer.local.model.MultipleConditionModel;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerDto;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerRelateOrgDto;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerSelectDto;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 客户服务接口类
 *
 * <AUTHOR>
 * @date 2021/10/25
 */
public interface CustomerService {

    /**
     * 创建客户
     *
     * @param dto 参数dto
     * @return 创建的客户
     */
    CustomerEntity create(CustomerDto dto);

    /**
     * 导入创建
     *
     * @param dto
     * @return
     */
    CustomerEntity importCreate(CustomerDto dto);

    /**
     * 更新客户
     *
     * @param dto 参数dto
     * @return 更新的客户
     */
    CustomerEntity update(CustomerDto dto);

    /**
     * 批量删除客户
     *
     * @param ids 客户id集合
     */
    void deleteBatch(List<String> ids);

    /**
     * 批量启用客户
     *
     * @param ids 客户id集合
     */
    void enableBatch(List<String> ids);

    /**
     * 批量禁用客户
     *
     * @param ids 客户id集合
     */
    void disableBatch(List<String> ids);

    /**
     * 批量冻结客户
     *
     * @param ids 客户id集合
     */
    void freezeBatch(List<String> ids);

    /**
     * 批量解冻客户
     *
     * @param ids 客户id集合
     */
    void unfreezeBatch(List<String> ids);

    /**
     * 客户信息下拉框分页列表
     *
     * @param dto      请求参数dto
     * @param pageable 分页信息
     * @return Page<CustomerEntity> 客户信息下拉框分页列表
     */
    Page<CustomerEntity> findByCustomerSelectDto(Pageable pageable, CustomerSelectDto dto);

    /**
     * 获取客户信息详情(包含关联数据)
     *
     * @param id           客户ID
     * @param customerCode 客户编码
     * @return 客户信息详情
     */
    CustomerEntity findDetailsByIdOrCode(String id, String customerCode);

    /**
     * 客户换绑企业组织
     *
     * @param dto 请求参数dto
     */
    void rebindOrg(CustomerRelateOrgDto dto);

    /**
     * 客户换绑客户组织
     *
     * @param dto 请求参数dto
     */
    void rebindCustomerOrg(CustomerRelateOrgDto dto);

    /**
     * 多条件查询客户列表
     *
     * @param model 条件model
     * @return 客户列表
     */
    List<CustomerEntity> findByMultipleConditionModel(MultipleConditionModel model);

    /**
     * 根据企业组织获取对应的客户信息
     *
     * @param orgCodeList
     * @return
     */
    List<CustomerEntity> findByOrgCodes(List<String> orgCodeList);

    /**
     * 根据客户编码集合获取对应的客户信息
     *
     * @param customerCodeList
     * @return
     */
    List<CustomerEntity> findByCustomerCodes(List<String> customerCodeList);

    /**
     * 根据客户ERP编码集合获取对应的客户信息
     *
     * @param customerCodeList
     * @return
     */
    List<CustomerEntity> findByErpCodes(List<String> customerCodeList);

    /**
     * 上级客户信息下拉框分页列表
     *
     * @param pageable
     * @return
     */
    Page<CustomerEntity> findByParentCustomerIsNull(Pageable pageable);

    /**
     * 根据客户编码查询下级客户
     *
     * @param pageable
     * @param customerCode
     * @return
     */
    Page<CustomerEntity> findChildrenByCustomerCode(Pageable pageable, String customerCode);

    /**
     * 根据标签id查询
     *
     * @param pageable
     * @param tagId
     * @return
     */
    Page<CustomerEntity> findByTagId(Pageable pageable, String tagId);

    /**
     * 根据客户编码或客户名称查询
     *
     * @param
     * @return
     */
    List<CustomerEntity> findByCustomerCodeLikeOrCustomerNameLike(String customerCodeLikeOrNameLike);

    /**
     * 根据状态查询
     *
     * @param status
     * @return
     */
    List<CustomerEntity> findByStatus(String status);

    /**
     * 流程审批成功处理业务
     *
     * @param customer 客户实体
     */
    void onProcessSuccess(CustomerEntity customer);

    List<CustomerVo> findByErpCodesAndCompanyCodes(List<String> companyCodes, List<String> customerSapCodes);
}
