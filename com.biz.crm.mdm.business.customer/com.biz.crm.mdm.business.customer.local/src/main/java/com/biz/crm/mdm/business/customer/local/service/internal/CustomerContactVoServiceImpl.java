package com.biz.crm.mdm.business.customer.local.service.internal;

import com.biz.crm.mdm.business.customer.local.service.CustomerContactService;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerContactVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerContactVo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户联系人信息表服务实现类
 *
 * <AUTHOR>
 * @date 2021-10-26 16:23:48
 */
@Service
public class CustomerContactVoServiceImpl implements CustomerContactVoService {

    @Autowired(required = false)
    private CustomerContactService customerContactService;

    /**
     * 客户联系人
     *
     * @param customerCodes
     * @return java.util.List<com.biz.crm.mdm.business.customer.sdk.vo.CustomerContactVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/15 18:28
     */
    @Override
    public List<CustomerContactVo> findByCustomerCodes(List<String> customerCodes) {
        if (CollectionUtils.isEmpty(customerCodes)) {
            return Lists.newArrayList();
        }
        return customerContactService.findByCustomerCodes(customerCodes);
    }

}
