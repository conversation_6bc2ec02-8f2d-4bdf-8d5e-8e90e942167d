package com.biz.crm.mdm.business.destination.local.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.mdm.business.destination.local.entity.MdmDestinationEntity;
import com.biz.crm.mdm.business.destination.local.mapper.MdmDestinationMapper;
import com.biz.crm.mdm.business.destination.sdk.dto.MdmDestinationDto;
import com.biz.crm.mdm.business.destination.sdk.vo.MdmDestinationVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 送达方的数据库访问类
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/22 18:54
 */
@Component
public class MdmDestinationRepository extends ServiceImpl<MdmDestinationMapper, MdmDestinationEntity> {

    /**
     * 通过ID或者编码获取送达方信息详情(包含关联数据)
     *
     * @param id              送达方ID
     * @param destinationCode 送达方编码
     * @return 送达方信息详情(包含关联数据)
     */
    public MdmDestinationEntity findDetailsByIdOrCode(String id, String destinationCode) {
        if (StringUtils.isAllBlank(id, destinationCode)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(StringUtils.isNotEmpty(id), MdmDestinationEntity::getId, id)
                .eq(StringUtils.isNotEmpty(destinationCode), MdmDestinationEntity::getDestinationCode, destinationCode)
                .eq(MdmDestinationEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(MdmDestinationEntity::getTenantCode, TenantUtils.getTenantCode())
                .one();
    }

    public List<MdmDestinationEntity> findDetailsByIdsOrCodes(List<String> ids, List<String> codeList) {
        if (CollectionUtil.isEmpty(ids)
                && CollectionUtil.isEmpty(codeList)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(CollectionUtil.isNotEmpty(ids), MdmDestinationEntity::getId, ids)
                .in(CollectionUtil.isNotEmpty(codeList), MdmDestinationEntity::getDestinationCode, codeList)
                .list();
    }

    public void saveBatchXml(List<MdmDestinationEntity> saveList) {
        if (CollectionUtil.isEmpty(saveList)) {
            return;
        }
        Lists.partition(saveList, CommonConstant.MAX_PAGE_SIZE).forEach(list -> {
            this.baseMapper.insertBatchSomeColumn(list);
        });
    }

    public void updateBatchXml(List<MdmDestinationEntity> updateList) {
        if (CollectionUtil.isEmpty(updateList)) {
            return;
        }
        Lists.partition(updateList, CommonConstant.MAX_PAGE_SIZE).forEach(this::updateBatchById);
    }

    /**
     * 根据客户编码查询送达方信息
     *
     * @param customerCode
     * @return
     */
    public MdmDestinationEntity findDetailsByCustomerCode(String customerCode) {
        if (StringUtil.isEmpty(customerCode)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(MdmDestinationEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(MdmDestinationEntity::getTenantCode, TenantUtils.getTenantCode())
                .eq(MdmDestinationEntity::getCustomerCode, customerCode)
                .one();
    }

    /**
     * 根据客户编码查询送达方信息
     *
     * @param customerCodes
     * @return
     */
    public List<MdmDestinationEntity> findDetailsByCustomerCodes(List<String> customerCodes) {
        if (CollectionUtil.isEmpty(customerCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(MdmDestinationEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(MdmDestinationEntity::getTenantCode, TenantUtils.getTenantCode())
                .in(MdmDestinationEntity::getCustomerCode, customerCodes)
                .list();
    }

    public List<MdmDestinationEntity> findByDestinationCodes(List<String> destinationCodes) {

        if (CollectionUtil.isEmpty(destinationCodes)) {
            return new ArrayList<>(0);
        }
        return this.lambdaQuery()
                .eq(MdmDestinationEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(MdmDestinationEntity::getTenantCode, TenantUtils.getTenantCode())
                .in(MdmDestinationEntity::getDestinationCode, destinationCodes)
                .list();
    }

    public Page<MdmDestinationVo> findByConditions(Pageable pageable, MdmDestinationDto dto) {
        return this.baseMapper.findByConditions(new Page<MdmDestinationVo>(pageable.getPageNumber(), pageable.getPageSize()), dto);
    }
}
