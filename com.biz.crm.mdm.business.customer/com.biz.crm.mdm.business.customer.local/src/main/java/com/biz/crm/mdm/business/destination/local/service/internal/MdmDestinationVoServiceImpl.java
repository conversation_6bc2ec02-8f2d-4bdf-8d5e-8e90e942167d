package com.biz.crm.mdm.business.destination.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Opt;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.customer.local.entity.CustomerDestinationEntity;
import com.biz.crm.mdm.business.customer.local.repository.CustomerDestinationRepository;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerContactVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.destination.local.entity.MdmDestinationEntity;
import com.biz.crm.mdm.business.destination.local.repository.MdmDestinationRepository;
import com.biz.crm.mdm.business.destination.local.service.MdmDestinationService;
import com.biz.crm.mdm.business.destination.sdk.dto.MdmDestinationDto;
import com.biz.crm.mdm.business.destination.sdk.service.MdmDestinationVoService;
import com.biz.crm.mdm.business.destination.sdk.vo.MdmDestinationVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 送达方信息VO服务接口实现类
 *
 * <AUTHOR>
 * @date 2021/10/27
 */
@Service
@Slf4j
public class MdmDestinationVoServiceImpl implements MdmDestinationVoService {

    @Autowired(required = false)
    private MdmDestinationService mdmDestinationService;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private MdmDestinationRepository mdmDestinationRepository;

    @Autowired(required = false)
    private CustomerDestinationRepository customerDestinationRepository;

    @Autowired(required = false)
    private CustomerVoService customerVoService;


    /**
     * 根据ID或者编码获取详情
     *
     * @param id              ID
     * @param destinationCode 编码
     * @return com.biz.crm.mdm.business.destination.sdk.vo.MdmDestinationVo
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/22 18:51
     */
    @Override
    public MdmDestinationVo findDetailsByIdOrCode(String id, String destinationCode) {
        if (StringUtils.isAllBlank(id, destinationCode)) {
            return null;
        }
        MdmDestinationEntity entity = mdmDestinationService.findDetailsByIdOrCode(id, destinationCode);
        if (Objects.isNull(entity)) {
            return null;
        }
        return this.nebulaToolkitService.copyObjectByWhiteList(entity, MdmDestinationVo.class, HashSet.class, ArrayList.class);
    }

    /**
     * 处理送达方查询，根据客户编码查询对应的送达方数据
     *
     * @param customerCode
     * @return
     */
    @Override
    public MdmDestinationVo findDetailsByCustomerCode(String customerCode) {
        if (StringUtil.isEmpty(customerCode)) {
            return null;
        }
        // 执行查询
        MdmDestinationEntity destinations = this.mdmDestinationService.findDetailsByCustomerCode(customerCode);
        if (Objects.isNull(destinations)) {
            return null;
        }
        return nebulaToolkitService.copyObjectByWhiteList(destinations, MdmDestinationVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public List<MdmDestinationVo> findDetailListByCustomerCode(String customerCode, String enableStatus, String keyword) {
        if (StringUtil.isEmpty(customerCode)) {
            return Lists.newArrayList();
        }
        CustomerVo customerVo = customerVoService.findDetailsByIdOrCode("", customerCode);
        if (Objects.isNull(customerVo)) {
            return Lists.newArrayList();
        }
        List<MdmDestinationEntity> destinations = Lists.newArrayList();
        MdmDestinationEntity entity = this.customerToDestination(customerVo);
        if (Objects.nonNull(entity)) {
            destinations.add(entity);
        }
        //1044487 【DMS后台】送达方需要拉取为其本身的数据  客户自己也是送达方
        List<CustomerDestinationEntity> customerDestinationEntities = customerDestinationRepository.findByCustomerCode(customerCode, TenantUtils.getTenantCode());
        List<String> destinationCodes = customerDestinationEntities.stream().map(CustomerDestinationEntity::getDestinationCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(destinationCodes)) {
            List<MdmDestinationEntity> destinationList = this.mdmDestinationRepository.findDetailsByIdsOrCodes(null, destinationCodes);
            if (CollectionUtil.isNotEmpty(destinationList)) {
                if (StringUtils.isNotEmpty(enableStatus)) {
                    destinationList = destinationList.stream()
                            .filter(e -> enableStatus.equalsIgnoreCase(e.getEnableStatus()))
                            .collect(Collectors.toList());
                }
                if (CollectionUtil.isNotEmpty(destinationList)) {
                    destinations.addAll(destinationList);
                }
            }
        }
        // 执行查询
        if (CollectionUtil.isEmpty(destinations)) {
            return Lists.newArrayList();
        }
        List<MdmDestinationVo> destinationVos = (List<MdmDestinationVo>) nebulaToolkitService.copyCollectionByWhiteList(destinations, MdmDestinationEntity.class, MdmDestinationVo.class, HashSet.class, ArrayList.class);
        for (MdmDestinationVo destinationVo : destinationVos) {
            String consigneeUnionAddress = String.join("/", StringUtils.stripToEmpty(destinationVo.getDestinationName()),
                    StringUtils.stripToEmpty(destinationVo.getConsigneeAddress()),
                    StringUtils.stripToEmpty(destinationVo.getErpCode()));
            destinationVo.setConsigneeUnionAddress(consigneeUnionAddress);
        }
        if (StringUtils.isNotBlank(keyword)) {
            destinationVos = destinationVos.stream().filter(e -> StringUtils.isNotBlank(e.getConsigneeAddress()) && e.getConsigneeAddress().contains(keyword)).collect(Collectors.toList());
        }
        return destinationVos;
    }

    /**
     * 客户转送达方
     *
     * @param customerVo
     * @return
     */
    private MdmDestinationEntity customerToDestination(CustomerVo customerVo) {
        if (Objects.isNull(customerVo)) {
            return null;
        }
        MdmDestinationEntity entity = nebulaToolkitService.copyObjectByWhiteList(customerVo, MdmDestinationEntity.class,
                HashSet.class, ArrayList.class);
        entity.setId(null);
        entity.setErpCode(customerVo.getErpCode());
        entity.setDestinationCode(customerVo.getCustomerCode());
        entity.setDestinationName(customerVo.getCustomerName());

        entity.setCustomerErpCode(customerVo.getErpCode());
        entity.setCustomerCode(customerVo.getCustomerCode());
        entity.setCustomerName(customerVo.getCustomerName());
        List<CustomerContactVo> contactList = customerVo.getContactList();
        if (CollectionUtil.isNotEmpty(contactList)) {
            Optional<CustomerContactVo> main = contactList.stream().filter(k -> Objects.nonNull(k.getContactMain()))
                    .filter(CustomerContactVo::getContactMain).findFirst();
            CustomerContactVo contactVo = main.orElseGet(() -> contactList.get(0));
            entity.setConsigneePhone(contactVo.getContactPhone());
            entity.setConsigneeName(contactVo.getContactName());
        }
        entity.setConsigneeAddress(customerVo.getRegisteredAddress());

        return entity;
    }

    /**
     * 处理送达方查询，根据客户编码查询对应的送达方数据
     *
     * @param customerCodes
     * @return
     */
    @Override
    public List<MdmDestinationVo> findDetailsByCustomerCodes(List<String> customerCodes) {
        if (CollectionUtil.isNotEmpty(customerCodes)) {
            return Lists.newArrayList();
        }
        // 执行查询
        List<MdmDestinationEntity> destinations = this.mdmDestinationService.findDetailsByCustomerCodes(customerCodes);
        if (CollectionUtil.isNotEmpty(destinations)) {
            return Lists.newArrayList();
        }

        return (List<MdmDestinationVo>) nebulaToolkitService.copyCollectionByWhiteList(destinations, MdmDestinationEntity.class,
                MdmDestinationVo.class, HashSet.class, ArrayList.class);
    }

    public List<MdmDestinationVo> findListByCustomerCodes(List<String> customerCodes) {
        if (CollectionUtil.isEmpty(customerCodes)) {
            return Lists.newArrayList();
        }
        // 根据客户编码 查询关联表
        List<CustomerDestinationEntity> customerDestinationEntities =
                customerDestinationRepository.findByCustomerCodes(customerCodes);
        if (CollectionUtil.isEmpty(customerDestinationEntities)) {
            return Lists.newArrayList();
        }
        // 根据送达方编码查询
        Map<String, List<String>> relateCusMap = customerDestinationEntities.stream().collect(
                Collectors.groupingBy(CustomerDestinationEntity::getDestinationCode,
                        Collectors.mapping(
                                CustomerDestinationEntity::getCustomerCode,
                                Collectors.toList())
                        ));
        List<MdmDestinationEntity> destinations = mdmDestinationRepository.findByDestinationCodes(
                new ArrayList<>(relateCusMap.keySet()));
        if (CollectionUtil.isEmpty(destinations)) {
            return Lists.newArrayList();
        }

        List<MdmDestinationVo> result = (List<MdmDestinationVo>) nebulaToolkitService.copyCollectionByWhiteList(destinations, MdmDestinationEntity.class,
                MdmDestinationVo.class, HashSet.class, ArrayList.class);
        for (MdmDestinationVo destinationVo : result) {
            destinationVo.setRelaCustomerCodes(relateCusMap.get(destinationVo.getDestinationCode()));
        }
        return result;
    }

    @Override
    public List<MdmDestinationVo> findByDestinationCodes(List<String> destinationCodes) {
        if (CollectionUtil.isEmpty(destinationCodes)) {
            return new ArrayList<>(0);
        }
        List<MdmDestinationEntity> entityList = this.mdmDestinationRepository.findByDestinationCodes(destinationCodes);
        if (CollectionUtil.isEmpty(entityList)) {
            return new ArrayList<>(0);
        }
        return (List<MdmDestinationVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entityList, MdmDestinationEntity.class,
                MdmDestinationVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public Page<MdmDestinationVo> findByConditions(Pageable pageable, MdmDestinationDto dto) {
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
        dto = Optional.ofNullable(dto).orElse(new MdmDestinationDto());
        dto.setTenantCode(TenantUtils.getTenantCode());
        return this.mdmDestinationRepository.findByConditions(pageable, dto);
    }
}
