package com.biz.crm.mdm.business.customer.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 客户对接人实体
 *
 * <AUTHOR>
 * @date 2021/10/26
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CustomerDockingEntity", description = "客户对接人实体")
@Entity
@TableName("mdm_customer_docking")
@Table(name = "mdm_customer_docking", indexes = {
    @Index(name = "mdm_customer_docking_index1", columnList = "position_code"),
    @Index(name = "mdm_customer_docking_index2", columnList = "customer_code"),
    @Index(name = "mdm_customer_docking_index3", columnList = "tenant_code,customer_code,position_code", unique = true),
})
@org.hibernate.annotations.Table(appliesTo = "mdm_customer_docking", comment = "客户对接人表")
public class CustomerDockingEntity extends TenantOpEntity {

  private static final long serialVersionUID = -7360595186438385542L;

  @ApiModelProperty("账号")
  @Transient
  @TableField(exist = false)
  private String userName;

  @ApiModelProperty("职位编码")
  @Column(name = "position_code", columnDefinition = "VARCHAR(32) COMMENT '职位编码'")
  private String positionCode;

  @ApiModelProperty("客户编码")
  @Column(name = "customer_code", columnDefinition = "VARCHAR(32) COMMENT '客户编码'")
  private String customerCode;

  @ApiModelProperty("渠道")
  @Column(name = "channel_code", columnDefinition = "VARCHAR(32) COMMENT '渠道'")
  private String channelCode;

  @ApiModelProperty("产品组编码")
  @Column(name = "product_group_code", columnDefinition = "VARCHAR(32) COMMENT '产品组编码'")
  private String productGroupCode;

  @ApiModelProperty("销售公司编码")
  @Column(name = "sale_company", columnDefinition = "VARCHAR(32) COMMENT '销售公司编码'")
  private String saleCompany;

  @ApiModelProperty("关联供货信息")
  @Transient
  @TableField(exist = false)
  private List<CustomerDockingSupplyEntity> supplyList;
}
