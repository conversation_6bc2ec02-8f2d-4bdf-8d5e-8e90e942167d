<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.customer.local.mapper.CustomerAddressMapper">

  <select id="findByConditions"
          resultType="com.biz.crm.mdm.business.customer.local.entity.CustomerAddressEntity">
    SELECT
      *
    FROM
      mdm_customer_address
    WHERE
      del_flag = '${@<EMAIL>()}'
      AND tenant_code = #{dto.tenantCode}
      AND customer_code = #{dto.customerCode}
      <if test="dto.contactName != null and dto.contactName != ''">
        <bind name="likeContactName" value="'%' + dto.contactName + '%'"/>
        AND contact_name like #{likeContactName}
      </if>
      <if test="dto.defaultAddress != null">
        AND default_address = #{dto.defaultAddress}
      </if>
  </select>
</mapper>
