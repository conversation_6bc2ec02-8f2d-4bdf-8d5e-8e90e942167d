package com.biz.crm.mdm.business.customer.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.customer.local.entity.CustomerTagMappingEntity;
import com.biz.crm.mdm.business.customer.local.mapper.CustomerTagMappingMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 经销商标签关联关系持久化
 * @Author: zengxingwang
 * @Date: 2021/12/23 19:21
 */
@Component
public class CustomerTagMappingRepository extends ServiceImpl<CustomerTagMappingMapper, CustomerTagMappingEntity> {

  /**
   * 根据标签ID集合删除
   *
   * @param tagIds 标签ID集合
   */
  public void deleteByTagIds(List<String> tagIds) {
    this.lambdaUpdate()
        .eq(CustomerTagMappingEntity::getTenantCode, TenantUtils.getTenantCode())    //新增租户编号判断条件
        .in(CustomerTagMappingEntity::getTagId, tagIds)
        .remove();
  }
}
