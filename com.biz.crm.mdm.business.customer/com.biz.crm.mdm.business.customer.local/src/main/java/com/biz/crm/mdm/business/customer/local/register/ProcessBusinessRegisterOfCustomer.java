package com.biz.crm.mdm.business.customer.local.register;

import com.biz.crm.mdm.business.customer.sdk.constant.CustomerConstant;
import com.biz.crm.workflow.sdk.register.ProcessBusinessRegister;
import org.springframework.stereotype.Component;

/**
 * 客户审批功能注册信息
 *
 * <AUTHOR>
 * @date 2022/09/05
 */
@Component
public class ProcessBusinessRegisterOfCustomer implements ProcessBusinessRegister {

  @Override
  public String getBusinessCode() {
    return CustomerConstant.CUSTOMER_PROCESS_NAME;
  }

  @Override
  public String getBusinessName() {
    return "客户创建时审批";
  }

}
