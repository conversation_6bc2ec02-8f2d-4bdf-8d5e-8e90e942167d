package com.biz.crm.mdm.business.customer.local.authority;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.mdm.business.customer.local.entity.CustomerROrgEntity;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerDockingVoService;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.bizunited.nebula.mars.sdk.register.SelectAuthorityModeRegister;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @descreption 按照数据操作者所属职位进行组织维度的值确认
 */
@Component
public class CustomerPositionAllChildrenAuthorityModeRegister implements SelectAuthorityModeRegister {

    @Autowired(required = false)
    private PositionVoService positionVoService;

    @Autowired(required = false)
    private CustomerDockingVoService customerDockingVoService;

    @Override
    public String modeKey() {
        return "customerPositionAllChildrenAuthorityModeRegister";
    }

    @Override
    public String modeName() {
        return "按照当前登录者职位及其所有下级职位关联的客户维度的值确认";
    }

    @Override
    public String controlKey() {
        return "customerPositionAllChildrenAuthorityModeRegister";
    }

    @Override
    public int sort() {
        return 2;
    }

    @Override
    public String groupCode() {
        return "customer_position_scope";
    }

    @Override
    public boolean isArrayValue() {
        return true;
    }

    @Override
    public boolean isStaticValue() {
        return false;
    }

    @Override
    public Class<?> modeValueClass() {
        return String.class;
    }

    @Override
    public Object staticValue(String[] staticValues) {
        return CommonConstant.NOT_AUTHORITY_ARR;
    }

    @Override
    public Object dynamicValue(UserIdentity loginDetails, String modeGroupCode) {
        String identityType = loginDetails.getIdentityType();
        //如果不是后台管理用户，就不按职位字段进行权限控制
        if (!StringUtils.equals(identityType, "u")) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        AbstractCrmUserIdentity loginUserDetails = (AbstractCrmUserIdentity) loginDetails;
        List<PositionVo> positionVoList = positionVoService.findAllChildrenByCode(loginUserDetails.getPostCode());
        if (CollectionUtil.isEmpty(positionVoList)) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        List<String> positionCodeList = positionVoList.stream().map(PositionVo::getPositionCode)
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(positionCodeList)) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        List<String> customerCodes = customerDockingVoService.getCustomerCodesByPositionCodes(positionCodeList);
        if (CollectionUtil.isEmpty(customerCodes)) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        customerCodes = customerCodes.stream().filter(StringUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(customerCodes)) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        return customerCodes.toArray(new String[0]);
    }

    @Override
    public String converterKey() {
        return "chartArrayMarsAuthorityAstConverter";
    }
}
