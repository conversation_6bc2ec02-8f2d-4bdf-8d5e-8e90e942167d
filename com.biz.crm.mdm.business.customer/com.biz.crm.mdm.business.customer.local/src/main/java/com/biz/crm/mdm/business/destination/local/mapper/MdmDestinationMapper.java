package com.biz.crm.mdm.business.destination.local.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.mapper.BusinessBaseMapper;
import com.biz.crm.mdm.business.destination.local.entity.MdmDestinationEntity;
import com.biz.crm.mdm.business.destination.sdk.dto.MdmDestinationDto;
import com.biz.crm.mdm.business.destination.sdk.vo.MdmDestinationVo;
import org.apache.ibatis.annotations.Param;

/**
 * 送达方的mybatis-plus接口类 {@link MdmDestinationEntity}
 *
 * <AUTHOR>
 * @date 2021/9/28
 */
public interface MdmDestinationMapper extends BusinessBaseMapper<MdmDestinationEntity> {

    Page<MdmDestinationVo> findByConditions(@Param("page") Page<MdmDestinationVo> page,
                                            @Param("dto") MdmDestinationDto dto);
}
