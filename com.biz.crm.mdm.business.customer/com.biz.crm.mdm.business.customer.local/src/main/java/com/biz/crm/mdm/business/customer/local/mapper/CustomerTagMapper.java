package com.biz.crm.mdm.business.customer.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.mdm.business.customer.local.entity.CustomerTagEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户标签表的mybatis-plus接口类 {@link CustomerTagEntity}
 *
 * <AUTHOR>
 * @date 2021-10-26 16:27:41
 */
public interface CustomerTagMapper extends BaseMapper<CustomerTagEntity> {

  /**
   * 查看详情（包括关联信息）
   * @param id
   * @return
   */
  CustomerTagEntity findDetailById(@Param("id") String id,@Param("tenantCode") String tenantCode);

  /**
   * 通过客户编码查询客户标签列表
   * @param customerCode
   * @return
   */
  List<CustomerTagEntity> findByCustomerCode(@Param("customerCode") String customerCode, @Param("tenantCode") String tenantCode);

  /**
   * 通过客户编码集合查询客户标签列表
   *
   * @param customerCodes 客户编码集合
   * @return 查询结果
   */
  List<CustomerTagEntity> findByCustomerCodes(@Param("customerCodes") List<String> customerCodes, @Param("tenantCode") String tenantCode);

  /**
   * 根据状态查询
   * @param status
   * @param tenantCode
   * @return
   */
  List<CustomerTagEntity> findDetailByStatus(@Param("status") String status, @Param("type") Boolean type,@Param("tenantCode") String tenantCode);
}

