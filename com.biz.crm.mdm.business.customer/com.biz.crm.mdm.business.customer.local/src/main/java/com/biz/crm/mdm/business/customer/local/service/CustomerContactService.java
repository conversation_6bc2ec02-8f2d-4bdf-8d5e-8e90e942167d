package com.biz.crm.mdm.business.customer.local.service;

import com.biz.crm.mdm.business.customer.sdk.dto.CustomerContactDto;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerContactVo;

import java.util.List;

/**
 * 客户联系人信息表服务接口
 *
 * <AUTHOR>
 * @date 2021-10-26 16:23:48
 */
public interface CustomerContactService {

  /**
   * 客户联系人信息绑定客户编码
   *
   * @param dtoList      客户联系人信息
   * @param customerCode 客户编码
   */
  void rebindCustomerCode(List<CustomerContactDto> dtoList, String customerCode);

  /**
   * 根据客户编码集合查询联系人信息
   *
   * @param customerCodes 客户编码集合
   */
  List<CustomerContactVo> findByCustomerCodes(List<String> customerCodes);
}
