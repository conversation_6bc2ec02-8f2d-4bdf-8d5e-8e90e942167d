package com.biz.crm.mdm.business.customer.local.service;

import com.biz.crm.mdm.business.customer.local.entity.CustomerTagRuleEntity;

import java.util.Collection;

/**
 * 经销商标签规则接口定义
 * @Author: zengxingwang
 * @Date: 2021/12/21 16:12
 */
public interface CustomerTagRuleService {

  /**
   * 创建规则
   * @param customerTagRuleEntities
   */
  void create(Collection<CustomerTagRuleEntity> customerTagRuleEntities);

  /**
   * 更新规则
   * @param customerTagRuleEntities
   */
  void update(Collection<CustomerTagRuleEntity> customerTagRuleEntities);
}
