package com.biz.crm.mdm.business.destination.local.service;

import com.biz.crm.mdm.business.destination.sdk.dto.MdmDestinationDto;

import java.util.List;

/**
 * 送达方 保存或更新SAP数据
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/22 18:45
 */
public interface MdmDestinationSapDataService {


    /**
     * 保存或更新SAP数据
     *
     * @param destinationDtoList
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/24 20:53
     */
    void saveOrUpdateSapData(List<MdmDestinationDto> destinationDtoList);
}
