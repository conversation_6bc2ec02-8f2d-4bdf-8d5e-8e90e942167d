package com.biz.crm.mdm.business.customer.feign.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.customer.feign.feign.internal.CustomerVoServiceFeignImpl;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerDto;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerQueryDto;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerSelectDto;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerRelationInfoVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.sun.xml.bind.v2.runtime.unmarshaller.UnmarshallingContext;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 客户feign
 *
 * <AUTHOR>
 * @date 2021/11/15
 */
@FeignClient(

        name = "${mdm.feign-client.name:crm-mdm}",
        path = "crm-mdm",
        fallbackFactory = CustomerVoServiceFeignImpl.class)
public interface CustomerVoServiceFeign {

    /**
     * 通过客户ID或者客户编码查询客户信息详情(包含客户信息所有关联数据)
     *
     * @param id
     * @param customerCode
     */
    @GetMapping("/v1/customer/customer/findDetailsByIdOrCode")
    Result<CustomerVo> findDetailsByIdOrCode(
            @RequestParam(value = "id", required = false) @ApiParam(name = "id", value = "主键ID")
            String id,
            @RequestParam(value = "customerCode", required = false)
            @ApiParam(name = "customerCode", value = "客户编码")
            String customerCode);

    /**
     * 根据企业组织编码获取匹配的客户信息
     *
     * @param orgCodeList
     * @return
     */
    @GetMapping("/v1/customer/customer/findByOrgCodes")
    Result<List<CustomerVo>> findByOrgCodes(@RequestParam("orgCodeList") List<String> orgCodeList);

    /**
     * 分页查询
     *
     * @param page
     * @param size
     * @param dto
     * @return
     */
    @GetMapping("/v1/customer/customer/findByCustomerDto")
    Result<Page<CustomerVo>> findByCustomerDto(@RequestParam("page") Integer page,
                                               @RequestParam("size") Integer size,
                                               @SpringQueryMap CustomerDto dto);

    /**
     * 修改经销商分利状态
     *
     * @param dto
     */
    @PostMapping("/v1/customer/customer/modifyShareBenefits")
    Result<?> modifyShareBenefits(@RequestBody CustomerDto dto);

    /**
     * 根据客户编码集合获取对应的客户信息
     *
     * @param customerCodeList
     * @return
     */
    @PostMapping("/v1/customer/customer/findByCustomerCodes")
    Result<List<CustomerVo>> findByCustomerCodes(@RequestBody List<String> customerCodeList);


    @ApiOperation(value = "通过用户名查询关联客户信息")
    @PostMapping("/v1/customer/customer/findCustomerByUserNames")
    Result<List<CustomerVo>> findCustomerByUserNames(@RequestBody List<String> userNameList);

    @GetMapping("/v1/customer/customer/findCustomerByUserName")
    Result<List<CustomerVo>> findCustomerByUserName(@RequestParam(value = "userName") String userName);

    /**
     * 根据客户ERP编码集合获取对应的客户信息
     *
     * @param erpCodeList
     * @return
     */
    @PostMapping("/v1/customer/customer/findByErpCodes")
    Result<List<CustomerVo>> findByErpCodes(@RequestBody List<String> erpCodeList);

    /**
     * 根据客户编码集合获取对应的客户信息及联系人
     *
     * @param customerCodeList 客户编码集合
     * @return 客户信息集合
     */
    @ApiOperation(value = "根据客户编码集合获取对应的客户信息及联系人")
    @PostMapping("/v1/customer/customer/findCustomerAndContactByCustomerCodes")
    Result<List<CustomerVo>> findCustomerAndContactByCustomerCodes(@RequestBody List<String> customerCodeList);

    /**
     * 根据客户编码集合获取对应的客户信息-包含主信息+组织信息
     *
     * @param customerCodeSet 客户编码集合
     * @return 客户信息集合
     */
    @PostMapping("/v1/customer/customer/findForPriceByCustomerCodes")
    Result<List<CustomerVo>> findForPriceByCustomerCodes(@RequestBody Set<String> customerCodeSet);

    /**
     * 根据渠道编码集合获取对应的客户信息
     *
     * @param channelList 渠道集合
     * @return 客户信息集合
     */
    @GetMapping("/v1/customer/customer/findByChannels")
    Result<List<CustomerVo>> findByChannels(@RequestParam("channelList") List<String> channelList);

    /**
     * 根据客户类型集合获取对应的客户信息
     *
     * @param typeList 客户类型集合
     * @return 客户信息集合
     */
    @GetMapping("/v1/customer/customer/findByTypes")
    Result<List<CustomerVo>> findByTypes(@RequestParam("typeList") List<String> typeList);

    /**
     * 根据CustomerQueryDto获取对应的客户编码集合
     *
     * @param dto
     * @return
     */
    @PostMapping("/v1/customer/customer/findByCustomerQueryDto")
    Result<Set<String>> findByCustomerQueryDto(@RequestBody CustomerQueryDto dto);

    /**
     * 根据组织获取组织及下级所有的审核通过且未删除的经销商信息
     *
     * @param orgCodes
     * @return
     */
    @GetMapping("/v1/customer/customer/findAllowSaleCustomerByOrgCodes")
    Result<Map<String, Set<String>>> findAllowSaleCustomerByOrgCodes(
            @RequestParam("orgCodes") Set<String> orgCodes);

    /**
     * 根据渠道查所有的审核通过且未删除的经销商信息
     *
     * @param channelCodes
     * @return
     */
    @GetMapping("/v1/customer/customer/findAllowSaleCustomerByChannels")
    Result<Map<String, Set<String>>> findAllowSaleCustomerByChannels(
            @RequestParam("channelCodes") Set<String> channelCodes);

    /**
     * 根据高德id集合获取对应的客户信息
     *
     * @param amapIds
     * @return 客户信息集合
     */
    @GetMapping("/v1/customer/customer/findByAmapIds")
    Result<List<CustomerVo>> findByAmapIds(@RequestParam("amapIds") Set<String> amapIds);

    /**
     * 存在由客户代码和通道
     *
     * @param channelList  频道列表
     * @param customerCode 客户代码
     * @return {@link UnmarshallingContext}
     */
    @GetMapping("/v1/customer/customer/existByCustomerCodeAndChannels")
    Result<Boolean> existByCustomerCodeAndChannels(@RequestParam("channelList") List<String> channelList, @RequestParam("customerCode") String customerCode);

    /**
     * 存在客户代码
     *
     * @param customerCode 客户代码
     * @return {@link Result}<{@link Boolean}>
     */
    @GetMapping("/v1/customer/customer/existByCustomerCode")
    Result<Boolean> existByCustomerCode(@RequestParam("customerCode") String customerCode);

    /**
     * 判断客户户是否在组织内
     *
     * @param customerCode 客户代码
     * @param orgCodeIn    组织代码
     * @param orgCodeNotIn org代码不
     * @return {@link Result}<{@link Boolean}>
     */
    @GetMapping("/v1/customer/customer/existByCustomer7OrgIn7OrgNotIn")
    Result<Boolean> existByCustomer7OrgIn7OrgNotIn(@RequestParam("customerCode") String customerCode, @RequestParam("orgCodeIn") List<String> orgCodeIn, @RequestParam("orgCodeNotIn") List<String> orgCodeNotIn);

    /**
     * 通过客户编码查询客户组织
     *
     * @param customerCode 客户代码
     * @return 客户关联的组织信息
     */
    @GetMapping("/v1/customer/customer/findOrgByCode")
    Result<CustomerVo> findOrgByCode(@RequestParam("customerCode") String customerCode);

    /**
     * 通过用户名查询当前用户所关联的客户
     *
     * @param userName
     * @return
     */
    @GetMapping("/v1/customer/customer/findCustomerByUserNameAndPostCode")
    Result<List<CustomerVo>> findCustomerByUserNameAndPostCode(@RequestParam("userName") String userName, @RequestParam("postCode") String postCode);

    /**
     * 条件查询分利经销商编码集合
     *
     * @param dto
     * @return
     */
    @GetMapping("/v1/customer/customer/findByCustomersDto")
    Result<Set<String>> findByCustomerDto(@SpringQueryMap CustomerSelectDto dto);

    /**
     * 根据条件查询经销商关联信息
     *
     * @param dto
     * @return
     */
    @GetMapping("/v1/customer/customer/findCustomerDetailsByDto")
    Result<CustomerRelationInfoVo> findCustomerDetailsByDto(@SpringQueryMap CustomerSelectDto dto);

    /**
     * 基于客户编码查询客户信息
     *
     * @param customerCodes
     * @param shareBenefits
     * @return
     */
    @GetMapping("/v1/customer/customer/findCustomerByCodes")
    Result<List<CustomerVo>> findCustomerByCodes(@RequestParam("customerCodes") Set<String> customerCodes,
                                                 @RequestParam("shareBenefits") Boolean shareBenefits);

    @PostMapping("/v1/customer/customer/findCustomerByCustomerNameList")
    Result<List<CustomerVo>> findCustomerByCustomerNameList(@RequestBody List<String> customerNameList);

    @ApiOperation(value = "根据客户编码，维护经纬度为空的客户经纬度")
    @PostMapping("/v1/customer/customer/updateLonAndLatByCustomerCode")
    Result<?> updateLonAndLatByCustomerCode(@RequestBody CustomerDto customer);

    @ApiOperation(value = "通过ERP+公司编码查询客户信息")
    @GetMapping("/v1/customer/customer/findByErpCodeAndCompanyCode")
    Result<CustomerVo> findByErpCodeAndCompanyCode(@RequestParam(value = "erpCode") String erpCode,
                                                   @RequestParam(value = "companyCode") String companyCode);

    @ApiOperation(value = "根据付款方编码查询客户")
    @PostMapping("/v1/customer/customer/findByPayerCodes")
    Result<List<CustomerVo>> findByPayerCodes(@RequestBody Set<String> payerCodes);

    /**
     * 根据客户编码更新客户活跃状态熔断
     *
     * @param dtoList
     * @return com.biz.crm.business.common.sdk.model.Result<?>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/23 21:10
     */
    @ApiOperation(value = "根据客户编码更新客户活跃状态熔断")
    @PostMapping("/v1/customer/customer/updateCustomerActiveState")
    Result<?> updateCustomerActiveState(@RequestBody List<CustomerDto> dtoList);

    /**
     * 客户信息下拉框分页列表
     *
     * @param page
     * @param size
     * @param dto
     * @return com.biz.crm.business.common.sdk.model.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page < com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo>>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/23 22:27
     */
    @ApiOperation(value = "客户信息下拉框分页列表")
    @GetMapping("/v1/customer/customer/findByCustomerSelectDto")
    Result<Page<CustomerVo>> findByCustomerSelectDto(@RequestParam("page") Integer page, @RequestParam("size") Integer size,
                                                     @SpringQueryMap CustomerSelectDto dto);

    @ApiOperation(value = "查询客户编码")
    @PostMapping("/v1/customer/customer/findCustomerCodesByCondition")
    Result<Set<String>> findCustomerCodesByCondition(@RequestBody CustomerSelectDto dto);

    @ApiOperation(value = "客户信息载入缓存")
    @GetMapping("/v1/customer/customer/loadCacheCustomer")
    Result<List<CustomerVo>> loadCacheCustomer();

    @PostMapping("/v1/customer/customer/findCustomerCodeByTags")
    @ApiOperation("根据标签编码查询客户编码")
    Result<List<String>> findCustomerCodeByTags(@RequestBody List<String> tagCodes);

    @PostMapping("/v1/customer/customer/findCustomerListByOrgCodes")
    @ApiOperation(value = "通过组织查询下级所有客户信息列表")
    Result<Map<String, List<CustomerVo>>> findCustomerListByOrgCodes(@RequestBody List<String> orgCodes);

    @ApiOperation(value = "通过渠道归属部门查询客户列表")
    @PostMapping("/v1/customer/customer/findCustomerListByChannelDepartmentCodeList")
    Result<List<CustomerVo>> findCustomerListByChannelDepartmentCodeList(@RequestBody List<String> channelDepartmentCodeList);
}
