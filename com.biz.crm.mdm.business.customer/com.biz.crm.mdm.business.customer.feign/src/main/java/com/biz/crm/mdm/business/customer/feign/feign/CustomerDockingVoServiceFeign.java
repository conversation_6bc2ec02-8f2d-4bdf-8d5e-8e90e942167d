package com.biz.crm.mdm.business.customer.feign.feign;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.customer.feign.feign.internal.CustomerDockingVoFeignImpl;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerDockingVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 客户feign
 *
 * <AUTHOR>
 * @date 2021/11/15
 */
@FeignClient(name = "${mdm.feign-client.name:crm-mdm}", path = "crm-mdm", fallbackFactory = CustomerDockingVoFeignImpl.class)
public interface CustomerDockingVoServiceFeign {

    /**
     * 根据客户编码集合获取对应的客户对接人信息
     *
     * @param customerCodes 客户编码集合
     * @return 客户对接人信息集合
     */

    @ApiOperation(value = "根据客户编码集合获取对应的客户对接人信息")
    @GetMapping("/v1/customerDocking/customerDocking/findByCustomerCodes")
    Result<List<CustomerDockingVo>> findByCustomerCodes(@RequestParam("customerCodes") List<String> customerCodes);

    /**
     * 根据职位编码集合获取对应的客户对接人信息
     *
     * @param positionCodes 职位编码集合
     * @return 客户对接人信息集合
     */

    @ApiOperation(value = "根据职位编码集合获取对应的客户信息")
    @GetMapping("/v1/customerDocking/customerDocking/findByPositionCodes")
    Result<List<CustomerDockingVo>> findByPositionCodes(@RequestParam("positionCodes") List<String> positionCodes);

    /**
     * 根据职位编码集合获取对应的客户对接人信息
     *
     * @param positionCodes 职位编码集合
     * @return 客户对接人信息集合
     */

    @ApiOperation(value = "根据职位编码集合获取对应的客户信息")
    @GetMapping("/v1/customerDocking/customerDocking/getCustomerCodesByPositionCodes")
    Result<List<String>> getCustomerCodesByPositionCodes(@RequestParam("positionCodes") List<String> positionCodes);

    @ApiOperation(value = "通过客户编码查询客户对接人信息")
    @PostMapping("/v1/customerDocking/customerDocking/findListByCustomerCodeList")
    Result<List<CustomerDockingVo>> findListByCustomerCodeList(@RequestBody List<String> customerCodes);
}
