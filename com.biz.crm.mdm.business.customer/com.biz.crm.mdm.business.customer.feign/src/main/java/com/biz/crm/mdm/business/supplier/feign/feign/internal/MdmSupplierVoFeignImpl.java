package com.biz.crm.mdm.business.supplier.feign.feign.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.supplier.feign.feign.MdmSupplierVoFeign;
import com.biz.crm.mdm.business.supplier.sdk.dto.MdmSupplierDto;
import com.biz.crm.mdm.business.supplier.sdk.vo.MdmSupplierVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 供应商feign接口类
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/24 16:10
 */
@Component
public class MdmSupplierVoFeignImpl implements FallbackFactory<MdmSupplierVoFeign> {
    @Override
    public MdmSupplierVoFeign create(Throwable throwable) {
        return new MdmSupplierVoFeign() {
            @Override
            public Result<Page<MdmSupplierVo>> findSupplierPage(Integer page, Integer size, MdmSupplierDto dto) {
                throw new UnsupportedOperationException("供应商分页查询熔断");
            }

            @Override
            public Result<MdmSupplierVo> findDetailsByIdOrCode(String id, String supplierCode) {
                throw new UnsupportedOperationException("根据ID或编码获取供应商信息熔断");
            }

            @Override
            public Result<List<MdmSupplierVo>> findDetailsByIds(List<String> ids) {
                throw new UnsupportedOperationException("根据ID集合获取供应商信息熔断");
            }

            @Override
            public Result<List<MdmSupplierVo>> findDetailsByCodes(List<String> supplierCodeList) {
                throw new UnsupportedOperationException("根据编码集合获取供应商信息熔断");
            }

            @Override
            public Result<MdmSupplierVo> findDetailBySapCodeAndCompanyCode(String supplierSapCode, String companyCode) {
                throw new UnsupportedOperationException("通过公司编码+sap编码进入熔断!");
            }
        };
    }
}
