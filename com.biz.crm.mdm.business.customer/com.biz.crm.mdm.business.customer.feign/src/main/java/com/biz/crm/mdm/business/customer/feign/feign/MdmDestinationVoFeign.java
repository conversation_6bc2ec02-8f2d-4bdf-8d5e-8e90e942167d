package com.biz.crm.mdm.business.customer.feign.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.customer.feign.feign.internal.CustomerVoFeignImpl;
import com.biz.crm.mdm.business.customer.feign.feign.internal.MdmDestinationVoFeignImpl;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerDto;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.destination.sdk.dto.MdmDestinationDto;
import com.biz.crm.mdm.business.destination.sdk.vo.MdmDestinationVo;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

/**
 * 客户feign接口类
 *
 * <AUTHOR>
 * @date 2022/2/18
 */
@FeignClient(
        
    name = "${mdm.feign-client.name:crm-mdm}",
    path = "crm-mdm",
    fallbackFactory = MdmDestinationVoFeignImpl.class)
public interface MdmDestinationVoFeign {

    @ApiOperation(value = "根据客户编码集合获取对应的客户标签信息")
    @GetMapping("/v1/destination/destination/findDetailsByIdOrCode")
    Result<MdmDestinationVo> findDetailsByIdOrCode(@RequestParam(value = "id", required = false) String id,
                                                   @RequestParam(value = "destinationCode", required = false) String destinationCode);

    @ApiOperation(value = "根据送达方编码集合查询送达方")
    @PostMapping("/v1/destination/destination/findByDestinationCodes")
    Result<List<MdmDestinationVo>> findByDestinationCodes(@RequestBody List<String> destinationCodes);

    @ApiOperation(value = "分页查询")
    @GetMapping("/v1/destination/destination/findByConditions")
    Result<Page<MdmDestinationVo>> findByConditions(@RequestParam("page") Integer page,
                                                    @RequestParam("size") Integer size,
                                                    @SpringQueryMap MdmDestinationDto dto);


    @ApiOperation(value = "通过客户编码定位对应的送达方")
    @GetMapping("/v1/destination/destination/findDetailsByCustomerCodes")
    Result<List<MdmDestinationVo>> findDetailsByCustomerCodes(
            @RequestParam(value = "customerCodes") List<String> customerCodes);


    @ApiOperation(value = "通过客户编码定位对应的送达方")
    @GetMapping("/v1/destination/destination/findListByCustomerCodes")
    Result<List<MdmDestinationVo>> findListByCustomerCodes(
            @RequestParam(value = "customerCodes") List<String> customerCodes);
}
