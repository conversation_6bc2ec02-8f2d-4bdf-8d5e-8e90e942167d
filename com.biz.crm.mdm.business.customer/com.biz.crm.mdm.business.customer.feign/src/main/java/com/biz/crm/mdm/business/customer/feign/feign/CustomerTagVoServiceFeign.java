package com.biz.crm.mdm.business.customer.feign.feign;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.customer.feign.feign.internal.CustomerTagVoServiceFeignImpl;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerTagVo;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 客户feign
 *
 * <AUTHOR>
 * @date 2021/11/15
 */
@FeignClient(    name = "${mdm.feign-client.name:crm-mdm}", path = "crm-mdm", fallbackFactory = CustomerTagVoServiceFeignImpl.class)
public interface CustomerTagVoServiceFeign {

  /**
   * 根据客户编码集合获取对应的客户标签信息
   *
   * @param customerCodeList 客户编码集合
   * @return 客户标签信息集合
   */

  @ApiOperation(value = "根据客户编码集合获取对应的客户标签信息")
  @GetMapping("/v1/customerTag/customerTag/findByCustomerCodes")
  Result<List<CustomerTagVo>> findByCustomerCodes(@RequestParam("customerCodeList") List<String> customerCodeList);
}
