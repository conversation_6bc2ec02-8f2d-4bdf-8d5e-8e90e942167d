package com.biz.crm.mdm.business.customer.feign.feign.internal;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.customer.feign.feign.CustomerVoFeign;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import org.springframework.cloud.openfeign.FallbackFactory;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 客户feign接口熔断类
 *
 * <AUTHOR>
 * @date 2022/2/18
 */
@Component
public class CustomerVoFeignImpl implements FallbackFactory<CustomerVoFeign> {
  @Override
  public CustomerVoFeign create(Throwable throwable) {
    return new CustomerVoFeign() {
      @Override
      public Result<List<CustomerVo>> findByCustomerCodes(List<String> customerCodeList) {
        throw new UnsupportedOperationException("根据客户编码集合获取对应的客户信息熔断");
      }

      @Override
      public Result<Set<String>> findCustomerCodesByOrgCodesAndChannelsAndTags(List<String> orgCodes, List<String> channelCodes, List<String> tags) {
        throw new UnsupportedOperationException("根据组织集合、渠道集合、标签集合查询客户编码集合熔断");
      }

      @Override
      public Result<Set<CustomerVo>> findCustomersByOrgCodesAndChannelsAndTags(
          List<String> orgCodes, List<String> channelCodes, List<String> tags) {
        throw  new UnsupportedOperationException("根据组织集合、渠道集合、标签集合查询客户信息集合熔断");
      }
    };
  }
}
