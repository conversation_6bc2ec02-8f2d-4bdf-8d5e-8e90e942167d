package com.biz.crm.mdm.business.customer.feign.feign.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.customer.feign.feign.MdmDestinationVoFeign;
import com.biz.crm.mdm.business.destination.sdk.dto.MdmDestinationDto;
import com.biz.crm.mdm.business.destination.sdk.vo.MdmDestinationVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 客户feign接口熔断类
 *
 * <AUTHOR>
 * @date 2022/2/18
 */
@Component
public class MdmDestinationVoFeignImpl implements FallbackFactory<MdmDestinationVoFeign> {
    @Override
    public MdmDestinationVoFeign create(Throwable throwable) {
        return new MdmDestinationVoFeign() {
            @Override
            public Result<MdmDestinationVo> findDetailsByIdOrCode(String id, String destinationCode) {
                throw new UnsupportedOperationException("根据送达方编码查询送达方熔断");
            }

            @Override
            public Result<List<MdmDestinationVo>> findByDestinationCodes(List<String> destinationCodes) {
                throw new UnsupportedOperationException("根据送达方编码集合查询送达方熔断");
            }

            @Override
            public Result<Page<MdmDestinationVo>> findByConditions(Integer page, Integer size, MdmDestinationDto dto) {
                throw new UnsupportedOperationException("分页查询送达方熔断");
            }

            @Override
            public Result<List<MdmDestinationVo>> findDetailsByCustomerCodes(List<String> customerCodes) {
                throw new UnsupportedOperationException("根据客户编码集合查询送达方熔断");
            }

            @Override
            public Result<List<MdmDestinationVo>> findListByCustomerCodes(List<String> customerCodes) {
                throw new UnsupportedOperationException("根据客户编码集合查询送达方熔断");
            }
        };
    }
}
