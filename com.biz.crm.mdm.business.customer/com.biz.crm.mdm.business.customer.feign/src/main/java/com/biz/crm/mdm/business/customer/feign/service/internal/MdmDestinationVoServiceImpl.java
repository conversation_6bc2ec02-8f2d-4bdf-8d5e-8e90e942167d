package com.biz.crm.mdm.business.customer.feign.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.customer.feign.feign.MdmDestinationVoFeign;
import com.biz.crm.mdm.business.destination.sdk.dto.MdmDestinationDto;
import com.biz.crm.mdm.business.destination.sdk.service.MdmDestinationVoService;
import com.biz.crm.mdm.business.destination.sdk.vo.MdmDestinationVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class MdmDestinationVoServiceImpl implements MdmDestinationVoService {

    @Autowired(required = false)
    private MdmDestinationVoFeign mdmDestinationVoFeign;

    @Override
    public MdmDestinationVo findDetailsByIdOrCode(String id, String destinationCode) {
        return mdmDestinationVoFeign.findDetailsByIdOrCode(id, destinationCode).checkFeignResult();
    }

    @Override
    public MdmDestinationVo findDetailsByCustomerCode(String customerCode) {
        return null;
    }

    @Override
    public List<MdmDestinationVo> findDetailListByCustomerCode(String customerCode, String enableStatus, String keyword) {
        return null;
    }

    @Override
    public List<MdmDestinationVo> findDetailsByCustomerCodes(List<String> customerCodes) {
        return mdmDestinationVoFeign.findDetailsByCustomerCodes(customerCodes).checkFeignResult();
    }

    @Override
    public List<MdmDestinationVo> findByDestinationCodes(List<String> destinationCodes) {
        if (CollectionUtil.isEmpty(destinationCodes)) {
            return new ArrayList<>(0);
        }
        return mdmDestinationVoFeign.findByDestinationCodes(destinationCodes).checkFeignResult();
    }

    @Override
    public Page<MdmDestinationVo> findByConditions(Pageable pageable, MdmDestinationDto dto) {
        return mdmDestinationVoFeign.findByConditions(pageable.getPageNumber(), pageable.getPageSize(), dto).checkFeignResult();
    }

    @Override
    public List<MdmDestinationVo> findListByCustomerCodes(List<String> customerCodes) {
        return mdmDestinationVoFeign.findListByCustomerCodes(customerCodes).checkFeignResult();
    }
}
