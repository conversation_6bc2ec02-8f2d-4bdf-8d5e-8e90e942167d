package com.biz.crm.mdm.business.customer.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerClientDto;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerClientVo;
import org.springframework.data.domain.Pageable;

/**
 * 经销商客户信息voService
 *
 * <AUTHOR>
 */
public interface CustomerClientVoService {

  /**
   * 查询当前用户及其下属所关联的客户信息分页列表
   * <p>
   * 用户账号和租户必传
   *
   * @param dto      查询对象
   * @param pageable 分页信息
   * @return Page<CustomerClientVo> 查询当前用户及其下属所关联的客户信息分页列表
   */
  default Page<CustomerClientVo> findChildrenPageByCustomerClientDto(Pageable pageable, CustomerClientDto dto) {
    return new Page<>();
  }

}
