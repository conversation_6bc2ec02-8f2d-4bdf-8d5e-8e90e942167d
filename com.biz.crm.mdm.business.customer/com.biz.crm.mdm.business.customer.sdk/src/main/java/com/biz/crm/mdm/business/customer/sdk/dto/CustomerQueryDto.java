package com.biz.crm.mdm.business.customer.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 根据CustomerQueryDto获取匹配的客户编码
 *
 * <AUTHOR>
 * @date 2022/5/27
 */
@Data
public class CustomerQueryDto extends TenantFlagOpDto {

    @ApiModelProperty("客户编码 模糊查询")
    private String customerCode;

    @ApiModelProperty("客户名称 模糊查询")
    private String customerName;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("组织名称模糊查询")
    private String orgName;

    @ApiModelProperty("审核状态")
    private String processStatus;

    @ApiModelProperty("职位编码")
    private String positionCode;

    @ApiModelProperty("客户编码集合")
    private Set<String> customerCodeSet;

    @ApiModelProperty("组织编码集合")
    private Set<String> orgCodeSet;

    @ApiModelProperty("职位编码集合")
    private Set<String> positionCodeSet;

    @ApiModelProperty("客户标签")
    private String tag;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("组织编码集合")
    private List<String> orgCodeList;

    @ApiModelProperty("组织编码-用于查询当前及下级的所以组织")
    private String orgCode;

    @ApiModelProperty("客户关键字")
    private String customerKeyWord;
}
