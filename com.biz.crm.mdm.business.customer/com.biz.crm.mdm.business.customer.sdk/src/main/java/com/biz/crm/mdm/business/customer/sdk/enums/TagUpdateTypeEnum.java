package com.biz.crm.mdm.business.customer.sdk.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * 经销商等级枚举
 * @Author: zengxingwang
 * @Date: 2021/12/21 16:33
 */
public enum TagUpdateTypeEnum {

  REAL_TIME("real_time", "0","实时更新", "1"),
  TIMING("timing", "1","定时更新", "2");

  /**
   * 系统key
   */
  private String key;
  /**
   * 字典编码
   */
  private String dictCode;
  /**
   * 字典值
   */
  private String value;
  /**
   * 字典排序
   */
  private String order;

  TagUpdateTypeEnum(String key, String dictCode, String value, String order) {
    this.key = key;
    this.dictCode = dictCode;
    this.order = order;
    this.value = value;
  }

  public String getKey() {
    return key;
  }

  public String getValue() {
    return value;
  }

  public String getOrder() {
    return order;
  }

  public String getDictCode() {
    return dictCode;
  }

  /**
   * 通过key获取 CustomerLevelEnum
   * @param key
   * @return
   */
  public static TagUpdateTypeEnum getByKey(String key) {
    return Arrays.stream(TagUpdateTypeEnum.values()).filter(item -> Objects.equals(item.getKey(), key))
            .findFirst().orElse(null);
  }

  /**
   * 通过dictCode获取 CustomerLevelEnum
   * @param dictCode
   * @return
   */
  public static TagUpdateTypeEnum getByDictCode(String dictCode) {
    return Arrays.stream(TagUpdateTypeEnum.values()).filter(item -> Objects.equals(item.getDictCode(), dictCode))
            .findFirst().orElse(null);
  }

  /**
   * 通过value获取 CustomerLevelEnum
   * @param value
   * @return
   */
  public static TagUpdateTypeEnum getByValue(String value) {
    return Arrays.stream(TagUpdateTypeEnum.values()).filter(item -> Objects.equals(item.getValue(), value))
            .findFirst().orElse(null);
  }
}
