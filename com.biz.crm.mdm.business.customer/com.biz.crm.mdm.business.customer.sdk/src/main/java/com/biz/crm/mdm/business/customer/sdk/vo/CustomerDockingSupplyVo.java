package com.biz.crm.mdm.business.customer.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 客户对接人-供货关系Dto
 *
 * <AUTHOR>
 * @date 2021/10/25
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CustomerDockingSupplyVo", description = "客户对接人-供货关系详情Vo")
public class CustomerDockingSupplyVo extends TenantVo {

  private static final long serialVersionUID = -6729792714668670049L;

  /**
   * 类型1商品2产品层级
   */
  @ApiModelProperty("类型1商品2产品层级")
  private String dataType;

  /**
   * 编码
   */
  @ApiModelProperty("编码")
  private String code;

  /**
   * 描述
   */
  @ApiModelProperty("描述")
  private String name;
}