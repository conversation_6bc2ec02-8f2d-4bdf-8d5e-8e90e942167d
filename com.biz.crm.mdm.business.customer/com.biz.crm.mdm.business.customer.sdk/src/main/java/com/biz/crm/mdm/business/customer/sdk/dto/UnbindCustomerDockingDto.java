package com.biz.crm.mdm.business.customer.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 解绑客户对接人Dto
 *
 * <AUTHOR>
 * @date 2021/11/11
 */
@Data
@ApiModel(value = "BindCustomerDockingDto", description = "解绑客户对接人Dto")
public class UnbindCustomerDockingDto {

  /**
   * 职位编码
   */
  @ApiModelProperty("职位编码")
  private String positionCode;
  /**
   * 客户编码集合
   */
  @ApiModelProperty("客户编码集合")
  private List<String> customerCodeList;

}