package com.biz.crm.mdm.business.destination.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 送达方VO
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/22 18:30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MdmDestinationVo", description = "送达方VO")
public class MdmDestinationVo extends TenantFlagOpVo {

    private static final long serialVersionUID = 2925453818632642994L;

    @ApiModelProperty("送达方编码")
    private String destinationCode;

    @ApiModelProperty("送达方名称")
    private String destinationName;

    @ApiModelProperty("送达方ERP编码")
    private String erpCode;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户ERP编码")
    private String customerErpCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("分销渠道编码")
    private String channelCode;

    @ApiModelProperty("产品组编码")
    private String productGroupCode;

    @ApiModelProperty("产品组名称")
    private String productGroupName;

    @ApiModelProperty("送达方类型[数据字典:mdm_destination_type]")
    private String destinationType;

    @ApiModelProperty("收货人电话")
    private String consigneePhone;

    @ApiModelProperty("收货人名称")
    private String consigneeName;

    @ApiModelProperty("送达方地址")
    private String consigneeAddress;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("区编码")
    private String districtCode;

    @ApiModelProperty("区名称")
    private String districtName;

    @ApiModelProperty("数据同步时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date syncUpdateTime;

    @ApiModelProperty("数据源[数据字典:mdm_data_source]")
    private String dataSource;

    @ApiModelProperty("送达方地址:送达方名称/详细地址/SAP送达方编码")
    private String consigneeUnionAddress;

    @ApiModelProperty("关联客户编码")
    private String relaCustomerCode;

    private List<String> relaCustomerCodes;
}
