package com.biz.crm.mdm.business.customer.sdk.vo;

import com.biz.crm.business.common.sdk.vo.UuidVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 经销商客户信息Vo
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ApiModel(value = "CustomerClientVo", description = "经销商客户信息Vo")
public class CustomerClientVo extends UuidVo {

  private static final long serialVersionUID = 5323221664981825624L;

  /**
   * 客户编码
   */
  @ApiModelProperty("客户编码")
  private String customerCode;
  /**
   * 客户名称
   */
  @ApiModelProperty("客户名称")
  private String customerName;
  /**
   * 客户类型
   */
  @ApiModelProperty("客户类型")
  private String customerType;

  /**
   * 市级编码
   */
  @ApiModelProperty("市级编码")
  private String cityCode;
  /**
   * 区级编码
   */
  @ApiModelProperty("区级编码")
  private String districtCode;
  /**
   * 省级编码
   */
  @ApiModelProperty("省级编码")
  private String provinceCode;
  /**
   * 市名称
   */
  @ApiModelProperty("市名称")
  private String cityName;
  /**
   * 区名称
   */
  @ApiModelProperty("区名称")
  private String districtName;
  /**
   * 省名称
   */
  @ApiModelProperty("省名称")
  private String provinceName;
  /**
   * 审批状态
   */
  @ApiModelProperty("审批状态 ")
  private String processStatus;

  /**
   * 注册地址
   */
  @ApiModelProperty("注册地址")
  private String registeredAddress;

  /**
   * 经度
   */
  @ApiModelProperty("经度")
  private BigDecimal longitude;

  /**
   * 纬度
   */
  @ApiModelProperty("纬度")
  private BigDecimal latitude;

  /**
   * 联系人列表
   */
  @ApiModelProperty("联系人列表")
  private List<CustomerContactVo> contactList;
}