package com.biz.crm.mdm.business.customer.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerTagDto;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerTagVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 经销商标签接口定义
 * @Author: zengxingwang
 * @Date: 2021/12/21 19:28
 */
public interface CustomerTagVoService {

  /**
   * 多条件分页查询
   * @param pageable
   * @param customerTagDto
   * @return
   */
  Page<CustomerTagVo> findByConditions(Pageable pageable, CustomerTagDto customerTagDto);

  /**
   * 通过客户编码集合获取客户标签
   *
   * @param customerCodes 客户编码集合
   * @return 客户标签
   */
  List<CustomerTagVo> findByCustomerCodes(List<String> customerCodes);
}
