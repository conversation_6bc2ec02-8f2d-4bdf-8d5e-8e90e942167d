package com.biz.crm.mdm.business.destination.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.destination.sdk.dto.MdmDestinationDto;
import com.biz.crm.mdm.business.destination.sdk.vo.MdmDestinationVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 送达方信息VO服务接口类
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/22 18:40
 */
public interface MdmDestinationVoService {

    /**
     * 获取送达方信息详情(包含关联数据)
     *
     * @param id              送达方ID
     * @param destinationCode 送达方编码
     * @return 送达方信息详情
     */
    MdmDestinationVo findDetailsByIdOrCode(String id, String destinationCode);

    /**
     * 通过客户编码查询送达方数据
     *
     * @param customerCode
     * @return
     */
    MdmDestinationVo findDetailsByCustomerCode(String customerCode);

    /**
     * 通过客户编码查询送达方数据
     *
     * @param customerCode
     * @param enableStatus
     * @return
     */
    List<MdmDestinationVo> findDetailListByCustomerCode(String customerCode, String enableStatus, String keyword);

    /**
     * 通过客户编码查询送达方数据
     *
     * @param customerCodes
     * @return
     */
    List<MdmDestinationVo> findDetailsByCustomerCodes(List<String> customerCodes);

    /**
     * 通过送达方编码查询送达方数据
     *
     * @param destinationCodes
     * @return
     */
    List<MdmDestinationVo> findByDestinationCodes(List<String> destinationCodes);

    Page<MdmDestinationVo> findByConditions(Pageable pageable, MdmDestinationDto dto);


    List<MdmDestinationVo> findListByCustomerCodes(List<String> customerCodes);
}
