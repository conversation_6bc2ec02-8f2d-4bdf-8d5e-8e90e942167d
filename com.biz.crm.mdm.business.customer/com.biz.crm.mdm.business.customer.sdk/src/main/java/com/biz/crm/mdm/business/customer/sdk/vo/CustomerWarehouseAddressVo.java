package com.biz.crm.mdm.business.customer.sdk.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import com.biz.crm.business.common.sdk.vo.TenantVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 客户仓库地址信息Vo
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/31 17:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "CustomerWarehouseAddressVo", description = "客户仓库地址信息Vo")
public class CustomerWarehouseAddressVo extends TenantVo {

    private static final long serialVersionUID = -6276151814093374230L;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("区编码")
    private String districtCode;

    @ApiModelProperty("区名称")
    private String districtName;

    @ApiModelProperty("是否默认地址;数据字典[yesOrNo]")
    private String defaultAddress;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("仓库地址")
    private String warehouseAddress;

}
