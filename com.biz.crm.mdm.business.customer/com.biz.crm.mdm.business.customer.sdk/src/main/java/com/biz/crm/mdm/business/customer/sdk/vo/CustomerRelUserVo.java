package com.biz.crm.mdm.business.customer.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户关联客户用户Vo
 *
 * <AUTHOR>
 * @date 2021/11/26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CustomerRelUserVo", description = "客户关联客户用户Vo")
public class CustomerRelUserVo {

    /**
     * 用户账号登录信息
     */
    @ApiModelProperty("用户账号登录信息")
    private String userName;

    /**
     * 用户编码
     */
    @ApiModelProperty("用户编码")
    private String userCode;

    /**
     * 用户类型
     */
    @ApiModelProperty("用户类型")
    private String userType;

    /**
     * 人员姓名
     */
    @ApiModelProperty("人员姓名")
    private String fullName;

}