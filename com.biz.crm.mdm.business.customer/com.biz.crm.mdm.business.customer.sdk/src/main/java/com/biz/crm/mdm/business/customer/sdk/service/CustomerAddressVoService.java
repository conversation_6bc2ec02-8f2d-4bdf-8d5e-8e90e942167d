package com.biz.crm.mdm.business.customer.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerAddressPageDto;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerAddressQueryDto;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerAddressVo;
import com.google.common.collect.Lists;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 客户收货地址信息VO服务接口类
 *
 * <AUTHOR>
 * @date 2021/10/28
 */
public interface CustomerAddressVoService {

  /**
   * 客户收货地址分页列表
   *
   * @param dto      请求参数dto
   * @param pageable 分页信息
   * @return Page<CustomerAddressVo> 客户收货地址分页信息
   */
  Page<CustomerAddressVo> findByConditions(Pageable pageable, CustomerAddressPageDto dto);

  /**
   * 通过ID获取客户收货地址详细信息
   * @param id 收货地址ID
   * @return 客户收货地址信息
   */
  CustomerAddressVo findDetailsById(String id);

  /**
   * 商城-客户收货地址分页查询
   * @param pageable
   * @param dto
   * @return {@link Page< CustomerAddressVo>}
   */
  default Page<CustomerAddressVo> findCustomerAddressByCurrentCustomer(Pageable pageable, CustomerAddressPageDto dto){
    return null;
  }

  /**
   * 查询客户收货地址列表
   *
   * @param dto 查询dto
   * @return 客户收货地址信息列表
   */
  default List<CustomerAddressVo> findByCustomerAddressQueryDto(CustomerAddressQueryDto dto) {
    return Lists.newLinkedList();
  }

  /**
   * 通过客户编码查询客户收货地址
   * @param customerCode
   * @return
   */
  CustomerAddressVo findCustomerAddressByCustomerCode(String customerCode);


  /**
   * 根据客户信息编码查询关联的收货地址
   *
   * @param customerCode
   * @return
   */
  List<CustomerAddressVo> findAddressByCustomerCode(String customerCode);
}
