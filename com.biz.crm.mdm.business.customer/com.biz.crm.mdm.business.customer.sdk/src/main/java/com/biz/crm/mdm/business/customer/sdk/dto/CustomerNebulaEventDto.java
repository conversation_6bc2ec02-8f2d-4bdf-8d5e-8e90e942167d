package com.biz.crm.mdm.business.customer.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import com.bizunited.nebula.event.sdk.service.NebulaEventDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 客户事件引擎事件通知Dto
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
public class CustomerNebulaEventDto extends TenantDto implements NebulaEventDto {

  /**
   * 渠道
   */
  @ApiModelProperty("渠道")
  private String channelCode;

  @ApiModelProperty("产品组编码")
  private String productGroupCode;

  /**
   * 渠道名称
   */
  @ApiModelProperty("渠道名称")
  private String channelName;
  /**
   * 客户编码
   */
  @ApiModelProperty("客户编码")
  private String customerCode;
  /**
   * 客户名称
   */
  @ApiModelProperty("客户名称")
  private String customerName;
  /**
   * 所属客户组织编码
   */
  @ApiModelProperty("所属客户组织编码")
  private String customerOrgCode;
  /**
   * 客户类型
   */
  @ApiModelProperty("客户类型")
  private String customerType;
  /**
   * 操作类型
   */
  @ApiModelProperty("操作类型")
  private String operateType;
  /**
   * 所属企业组织编码
   */
  @ApiModelProperty("所属企业组织编码")
  private String orgCode;
  /**
   * 市级编码
   */
  @ApiModelProperty("市级编码")
  private String cityCode;
  /**
   * 区级编码
   */
  @ApiModelProperty("区级编码")
  private String districtCode;
  /**
   * 省级编码
   */
  @ApiModelProperty("省级编码")
  private String provinceCode;
  /**
   * 市名称
   */
  @ApiModelProperty("市名称")
  private String cityName;
  /**
   * 区名称
   */
  @ApiModelProperty("区名称")
  private String districtName;
  /**
   * 省名称
   */
  @ApiModelProperty("省名称")
  private String provinceName;
  /**
   * 工程名
   */
  @ApiModelProperty("工程名")
  private String projectName;
  /**
   * 注册地址
   */
  @ApiModelProperty("注册地址")
  private String registeredAddress;
  /**
   * 经度
   */
  @ApiModelProperty("经度")
  private BigDecimal longitude;
  /**
   * 纬度
   */
  @ApiModelProperty("纬度")
  private BigDecimal latitude;
  /**
   * 客户联系方式
   */
  @ApiModelProperty("客户联系方式")
  private String customerContact;
  /**
   * 客户法人代表
   */
  @ApiModelProperty("客户法人代表")
  private String legalRepresentative;
  /**
   * erp编码
   */
  @ApiModelProperty("erp编码")
  private String erpCode;
  /**
   * 审批状态
   */
  @ApiModelProperty("审批状态")
  private String actApproveStatus;
  /**
   * 价格组
   */
  @ApiModelProperty("价格组")
  private String priceGroup;

  /**
   * 合作状态
   */
  @ApiModelProperty("合作状态")
  private String cooperateStatus;
  /**
   * 审批类型
   */
  @ApiModelProperty("审批类型")
  private String approvalType;
  /**
   * 流程编号
   */
  @ApiModelProperty("流程编号")
  private String processCode;
  /**
   * 业务来源系统
   */
  @ApiModelProperty("业务来源系统")
  private String fromType;

  /**
   * 数据来源
   */
  @ApiModelProperty("数据来源")
  private String sourceType;
}
