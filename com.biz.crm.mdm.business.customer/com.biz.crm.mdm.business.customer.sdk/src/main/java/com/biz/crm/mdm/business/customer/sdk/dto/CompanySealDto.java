package com.biz.crm.mdm.business.customer.sdk.dto;

import com.biz.crm.business.common.local.entity.TenantEntity;
import com.biz.crm.business.common.sdk.dto.FileDto;
import com.biz.crm.business.common.sdk.dto.TenantDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/11 11:43
 **/
@Data
@ApiModel("公司印章")
public class CompanySealDto extends FileDto {


    @ApiModelProperty("公司编码")
    private String companyCode;
}
