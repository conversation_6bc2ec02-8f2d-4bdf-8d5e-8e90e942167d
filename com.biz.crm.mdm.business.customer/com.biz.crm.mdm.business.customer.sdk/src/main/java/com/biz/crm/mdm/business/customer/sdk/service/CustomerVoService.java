package com.biz.crm.mdm.business.customer.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerDto;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerQueryDto;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerSelectDto;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerRelationInfoVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 客户信息VO服务接口类
 *
 * <AUTHOR>
 * @date 2021/10/27
 */
public interface CustomerVoService {

    /**
     * 获取客户信息详情(包含关联数据)
     *
     * @param id           客户ID
     * @param customerCode 客户编码
     * @return 客户信息详情
     */
    CustomerVo findDetailsByIdOrCode(String id, String customerCode);

    /**
     * 客户信息下拉框分页列表
     *
     * @param dto      请求参数dto
     * @param pageable 分页信息
     * @return Page<CustomerVo> 客户信息下拉框分页列表
     */
    Page<CustomerVo> findByCustomerSelectDto(Pageable pageable, CustomerSelectDto dto);

    /**
     * 分页查询
     *
     * @param pageable 分页信息
     * @param dto      分页参数dto
     * @return 查询结果
     */
    default Page<CustomerVo> findByConditions(Pageable pageable, CustomerDto dto) {
        return new Page<>();
    }

    /**
     * 批量设置是否分利
     *
     * @param customerCodeList 编码集合
     * @param bool             设置值
     */
    default void modifyShareBenefits(List<String> customerCodeList, Boolean bool) {
    }

    /**
     * 根据企业组织编码获取匹配的客户信息
     *
     * @param orgCodeList 组织编码集合
     * @return 客户信息集合
     */
    List<CustomerVo> findByOrgCodes(List<String> orgCodeList);

    /**
     * 根据客户编码集合获取对应的客户信息
     *
     * @param customerCodeList 客户编码集合
     * @return 客户信息集合
     */
    List<CustomerVo> findByCustomerCodes(List<String> customerCodeList);

    /**
     * 通过用户名查询关联客户信息
     *
     * @param userNameList
     * @return
     */
    List<CustomerVo> findCustomerByUserNames(List<String> userNameList);

    List<CustomerVo> findCustomerByUserName(String userName);

    /**
     * 根据客户ERP编码集合获取对应的客户信息
     *
     * @param erpCodeList 客户编码集合
     * @return 客户信息集合
     */
    List<CustomerVo> findByErpCodes(List<String> erpCodeList);

    /**
     * 根据客户编码集合获取对应的客户信息及联系人
     *
     * @param customerCodeList 客户编码集合
     * @return 客户信息集合
     */
    List<CustomerVo> findCustomerAndContactByCustomerCodes(List<String> customerCodeList);

    /**
     * 根据渠道编码集合获取对应的客户信息
     *
     * @param channelList 渠道集合
     * @return 客户信息集合
     */
    List<CustomerVo> findByChannels(List<String> channelList);

    /**
     * 根据客户类型集合获取对应的客户信息
     *
     * @param typeList 客户类型集合
     * @return 客户信息集合
     */
    List<CustomerVo> findByTypes(List<String> typeList);

    /**
     * 根据组织集合、渠道集合、标签集合查询客户编码集合
     * 参数非必填项所以不需要进行参数校验
     *
     * @param orgCodes     组织集合
     * @param channelCodes 渠道集合
     * @param tags         标签集合
     * @return 客户编码集合
     */
    default Set<String> findCustomerCodesByOrgCodesAndChannelsAndTags(List<String> orgCodes, List<String> channelCodes, List<String> tags) {
        return new HashSet<>();
    }

    /**
     * 根据客户编码集合获取对应的客户信息-包含主信息+组织信息
     *
     * @param customerCodeSet 客户编码集合
     * @return 客户信息集合
     */
    default List<CustomerVo> findForPriceByCustomerCodes(Set<String> customerCodeSet) {
        return Lists.newLinkedList();
    }

    /**
     * 根据高德id集合查询数据集合
     *
     * @param amapIds
     * @return
     */
    default List<CustomerVo> findByAmapIds(Set<String> amapIds) {
        return Lists.newArrayList();
    }

    /**
     * 根据CustomerQueryDto获取匹配的客户编码集合
     *
     * @param dto
     * @return
     */
    default Set<String> findByCustomerQueryDto(CustomerQueryDto dto) {
        return Sets.newHashSet();
    }

    /**
     * 根据组织获取组织及下级所有的审核通过且未删除的经销商信息
     *
     * @param orgCodes
     * @return k-经销商编码，v-关联组织对应的降维码集合
     */
    default Map<String, Set<String>> findAllowSaleCustomerByOrgCodes(Set<String> orgCodes) {
        return Maps.newHashMap();
    }

    /**
     * 根据渠道查所有的审核通过且未删除的经销商信息
     *
     * @param channelCodes
     * @return
     */
    default Map<String, Set<String>> findAllowSaleCustomerByChannels(Set<String> channelCodes) {
        return Maps.newHashMap();
    }

    /**
     * 根据创建人账号，年月日期，业务系统类型查询数据
     *
     * @param createAccount
     * @param fromType
     * @return
     */
    default List<CustomerVo> findCountByCreateAccountAndFromTypeAndCreateTimeScope(String createAccount, String fromType, String startDate, String endDate) {
        return new ArrayList<>(0);
    }


    /**
     * 找到客户代码和渠道
     *
     * @param channelList  频道列表
     * @param customerCode 客户代码
     * @return {@link String}
     */
    Boolean existByCustomerCodeAndChannels(List<String> channelList, String customerCode);

    /**
     * 存在客户代码
     *
     * @param customerCode 客户代码
     * @return {@link String}
     */
    Boolean existByCustomerCode(String customerCode);

    /**
     * 判断客户户是否在组织内
     *
     * @param customerCode 客户代码
     * @param orgCodeIn    组织代码
     * @param orgCodeNotIn org代码不
     * @return {@link Boolean}
     */
    Boolean existByCustomer7OrgIn7OrgNotIn(String customerCode, List<String> orgCodeIn, List<String> orgCodeNotIn);

    /**
     * 通过客户编码查询客户组织
     *
     * @param customerCode 客户编码
     * @return 客户信息详情
     */
    CustomerVo findOrgByCode(String customerCode);

    /**
     * 通过用户名和对应职位查询关联当前用户的客户
     *
     * @param userName
     * @return
     */
    List<CustomerVo> findCustomerByUserNameAndPostCode(String userName, String postCode);

    /**
     * 根据组织编码集及渠道等条件查询客户信息
     *
     * @param orgCodes     组织编码
     * @param channelCodes 渠道编码
     * @param tags         标签集合
     * @return
     */
    default Set<CustomerVo> findCustomersByOrgCodesAndChannelsAndTags(List<String> orgCodes, List<String> channelCodes, List<String> tags) {
        return new HashSet<>();
    }

    /**
     * 条件查询分利终端编码集合
     *
     * @param dto
     * @return
     */
    Set<String> findByCustomerDto(CustomerSelectDto dto);

    /**
     * 条件查询经销商关联信息
     *
     * @param dto
     * @return
     */
    CustomerRelationInfoVo findCustomerDetailsByDto(CustomerSelectDto dto);


    /**
     * 基于编码查询客户信息
     *
     * @param customerCodes
     * @return
     */
    List<CustomerVo> findCustomerByCodes(Set<String> customerCodes, Boolean shareBenefits);

    /**
     * 通过客户名称查询客户信息
     *
     * @param customerNameList
     * @return
     */
    List<CustomerVo> findCustomerByCustomerNameList(List<String> customerNameList);

    Page<CustomerVo> findCustomerByConditions(Pageable pageable, CustomerQueryDto dto);

    /**
     * 根据终端编码，维护经纬度为空的客户经纬度
     *
     * @param customer
     * @return
     */
    Result<?> updateLonAndLatByCustomerCode(CustomerDto customer);

    CustomerVo findByErpCodeAndCompanyCode(String erpCode, String companyCode);

    /**
     * 根据付款方编码查询客户
     *
     * @param payerCodes
     * @return
     */
    List<CustomerVo> findByPayerCodes(Set<String> payerCodes);

    /**
     * 根据客户编码更新客户活跃状态熔断
     *
     * @param dtoList
     * @return com.biz.crm.business.common.sdk.model.Result<?>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/23 21:10
     */
    Result<?> updateCustomerActiveState(List<CustomerDto> dtoList);

    /**
     * 根据条件查询客户编码
     *
     * @param dto
     * @return
     */
    Set<String> findCustomerCodesByCondition(CustomerSelectDto dto);


    List<CustomerVo> loadCacheCustomer();

    List<String> findCustomerCodeByTags(List<String> tagCodes);

    Map<String, List<CustomerVo>> findCustomerListByOrgCodes(List<String> orgCodes);

    List<CustomerVo> findCustomerListByChannelDepartmentCodeList(List<String> channelDepartmentCodeList);
}
