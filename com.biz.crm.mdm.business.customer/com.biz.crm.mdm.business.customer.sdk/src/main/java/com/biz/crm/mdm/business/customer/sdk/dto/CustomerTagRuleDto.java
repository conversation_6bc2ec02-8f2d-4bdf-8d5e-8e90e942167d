package com.biz.crm.mdm.business.customer.sdk.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户标签规则dto
 * @Author: zengxingwang
 * @Date: 2021/12/21 15:19
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CustomerTagRuleDto", description = "客户标签规则dto" )
public class CustomerTagRuleDto {

  /**
   * 标签属性
   */
  @ApiModelProperty("标签属性")
  private String tagAttributes;

  /**
   * 标签细分
   */
  @ApiModelProperty("标签细分")
  private String tagSubdivision;

  /**
   * 标签表达式
   */
  @ApiModelProperty("标签表达式")
  private String tagExpression;

  /**
   * 表达式结果（0：否；1：是；）
   */
  @ApiModelProperty("表达式结果（0：否；1：是；）")
  private Integer expressionResult;

  /**
   * 表达式数量
   */
  @ApiModelProperty("表达式数量")
  private BigDecimal expressionAmount;

  /**
   * 表达式数量
   */
  @ApiModelProperty("表达式数量")
  private BigDecimal expressionAmountMax;

  /**
   * 统计时间开始
   */
  @ApiModelProperty("统计时间开始")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;

  /**
   * 统计时间结束
   */
  @ApiModelProperty("统计时间结束")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;
}
