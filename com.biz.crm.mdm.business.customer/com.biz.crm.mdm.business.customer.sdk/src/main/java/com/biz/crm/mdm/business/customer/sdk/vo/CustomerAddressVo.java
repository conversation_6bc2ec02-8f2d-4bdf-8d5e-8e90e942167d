package com.biz.crm.mdm.business.customer.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 客户收货地址Vo
 *
 * <AUTHOR>
 * @date 2021/10/28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CustomerAddressVo", description = "客户收货地址Vo")
public class CustomerAddressVo extends TenantFlagOpVo {

  private static final long serialVersionUID = 7228210817672447201L;

  @ApiModelProperty("市级编码")
  private String cityCode;

  @ApiModelProperty("区级编码")
  private String districtCode;

  @ApiModelProperty("省级编码")
  private String provinceCode;

  @ApiModelProperty("市名称")
  private String cityName;

  @ApiModelProperty("区名称")
  private String districtName;

  @ApiModelProperty("省名称")
  private String provinceName;

  @ApiModelProperty("详细地址")
  private String detailedAddress;

  @ApiModelProperty("送达方编码")
  private String destinationCode;

  @ApiModelProperty("送达方名称")
  private String destinationName;

  @ApiModelProperty("收货人")
  private String consigneeName;

  @ApiModelProperty("联系人")
  private String contactName;

  @ApiModelProperty("联系电话")
  private String contactPhone;

  @ApiModelProperty("是否默认地址(true：是，false：否)")
  private Boolean defaultAddress;

  @ApiModelProperty("客户编码")
  private String customerCode;

  @ApiModelProperty("有效开始时间")
  private String startTime;

  @ApiModelProperty("有效结束时间")
  private String endTime;

  @ApiModelProperty("经度")
  private BigDecimal longitude;

  @ApiModelProperty("纬度")
  private BigDecimal latitude;

  @ApiModelProperty("数据源[数据字典:mdm_data_source]")
  private String dataSource;

}