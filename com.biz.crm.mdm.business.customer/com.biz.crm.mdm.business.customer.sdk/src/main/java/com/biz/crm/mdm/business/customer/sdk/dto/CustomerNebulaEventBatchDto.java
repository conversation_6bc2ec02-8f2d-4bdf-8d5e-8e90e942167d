package com.biz.crm.mdm.business.customer.sdk.dto;

import com.bizunited.nebula.event.sdk.service.NebulaEventDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 客户事件引擎事件通知Dto
 *
 * <AUTHOR>
 */
@Data
public class CustomerNebulaEventBatchDto implements NebulaEventDto {

  /**
   * 客户信息集合
   */
  @ApiModelProperty("客户信息集合")
  private List<CustomerNebulaEventDto> customerNebulaEventDtoList;
}
