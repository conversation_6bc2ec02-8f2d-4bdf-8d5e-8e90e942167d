package com.biz.crm.mdm.business.customer.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.biz.crm.workflow.sdk.dto.ProcessBusinessDto;
import com.biz.crm.workflow.sdk.vo.AttachmentVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 客户信息Dto
 *
 * <AUTHOR>
 * @date 2021/10/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "CustomerDto", description = "客户信息Dto")
public class CustomerDto extends TenantFlagOpDto {

  @ApiModelProperty("工作流参数")
  private ProcessBusinessDto processBusiness;

  @ApiModelProperty("渠道编码/分销渠道")
  private String channelCode;

  @ApiModelProperty("渠道名称")
  private String channelName;

  @ApiModelProperty("渠道部门")
  private String channelDepartmentCode;

  @ApiModelProperty("渠道类型")
  private String channelType;

  @ApiModelProperty("SAP客户渠道编码")
  private String sapCustomerChannelCode;

  @ApiModelProperty("SAP客户渠道名称")
  private String sapCustomerChannelName;

  @ApiModelProperty("搜索项")
  private String searchName;

  @ApiModelProperty("产品组编码")
  private String productGroupCode;

  @ApiModelProperty("产品组名称")
  private String productGroupName;

  @ApiModelProperty("客户编码")
  private String customerCode;

  @ApiModelProperty("客户名称")
  private String customerName;

  @ApiModelProperty("客户编码/客户名称")
  private String keyword;

  @ApiModelProperty("客户编码/客户名称")
  private String unionName;

  @ApiModelProperty("所属客户组织编码")
  private String customerOrgCode;

  @ApiModelProperty("客户类型")
  private String customerType;

  @ApiModelProperty("操作类型")
  private String operateType;

  @ApiModelProperty("所属企业组织编码(多个逗号分隔)")
  private String orgCode;

  @ApiModelProperty("省级编码")
  private String provinceCode;

  @ApiModelProperty("省名称")
  private String provinceName;

  @ApiModelProperty("市名称")
  private String cityName;

  @ApiModelProperty("市级编码")
  private String cityCode;

  @ApiModelProperty("区级编码")
  private String districtCode;

  @ApiModelProperty("区名称")
  private String districtName;

  @ApiModelProperty("公司编码")
  private String companyCode;
  @ApiModelProperty("公司编码(排除)")
  private List<String> excludeCompanyCodeList;

  @ApiModelProperty("公司名称")
  private String companyName;

  @ApiModelProperty("成本中心编码")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  private String costCenterName;

  @ApiModelProperty("SAP销售属性;数据字典[mdm_sap_sales_type]")
  private String sapSalesType;

  @ApiModelProperty("合同客户;数据字典[yesOrNo]")
  private String contractCustomer;

  @ApiModelProperty("发货要求")
  private String deliveryRequest;

  @ApiModelProperty("效期要求")
  private String expirationDateRequest;

  @ApiModelProperty("工程名")
  private String projectName;

  @ApiModelProperty("注册地址")
  private String registeredAddress;

  @ApiModelProperty("经度")
  private BigDecimal longitude;

  @ApiModelProperty("纬度")
  private BigDecimal latitude;

  @ApiModelProperty("客户联系方式")
  private String customerContact;

  @ApiModelProperty("客户法人代表")
  private String legalRepresentative;

  @ApiModelProperty("ERP编码")
  private String erpCode;

  @ApiModelProperty("审批状态")
  private String actApproveStatus;

  @ApiModelProperty("价格组")
  private String priceGroup;

  @ApiModelProperty("价格组名称")
  private String priceGroupName;

  @ApiModelProperty("是否分利")
  private Boolean shareBenefits;

  @ApiModelProperty("合作状态")
  private String cooperateStatus;

  @ApiModelProperty("合作类型")
  private String cooperateType;

  @ApiModelProperty("合作类型")
  private String hzlx;

  @ApiModelProperty("合同到期日")
  private String contractDate;

  @ApiModelProperty("审批类型")
  private String approvalType;

  @ApiModelProperty("流程编号")
  private String processCode;

  @ApiModelProperty("审批状态 ")
  private String processStatus;

  @ApiModelProperty("业务来源系统")
  private String fromType;

  @ApiModelProperty("责任业务员编码")
  private String dutySalespersonCode;

  @ApiModelProperty("责任业务员工号")
  private String dutySalespersonUserName;

  @ApiModelProperty("责任业务员名称")
  private String dutySalespersonName;

  @ApiModelProperty("数据来源")
  private String sourceType;

  @ApiModelProperty("客户等级")
  private String customerLevel;

  @ApiModelProperty("上级经销商编码")
  private String parentCustomerCode;

  @ApiModelProperty("是否提交审批流程(true:是,false:否)")
  private Boolean submitProcess;

  @ApiModelProperty("寄售客户;数据字典[yesOrNo]")
  private String consignSale;

  @ApiModelProperty("冻结/解冻原因;数据字典[mdm_freeze_reason]")
  private String freezeReason;

  @ApiModelProperty("客户活跃状态;数据字典[mdm_customer_active_state]")
  private String activeState;

  @ApiModelProperty("客户标签;数据字典[mdm_customer_tag]")
  private String customerTag;

  @ApiModelProperty("办公省编码")
  private String workProvinceCode;

  @ApiModelProperty("办公省名称")
  private String workProvinceName;

  @ApiModelProperty("办公市编码")
  private String workCityCode;

  @ApiModelProperty("办公市名称")
  private String workCityName;

  @ApiModelProperty("办公区编码")
  private String workDistrictCode;

  @ApiModelProperty("办公区名称")
  private String workDistrictName;

  @ApiModelProperty("办公地点")
  private String workAddress;

  @ApiModelProperty("办公地点经度")
  private BigDecimal workLongitude;

  @ApiModelProperty("办公地点纬度")
  private BigDecimal workLatitude;

  @ApiModelProperty("风险类")
  private String riskClass;

  @ApiModelProperty("付款方ERP编码")
  private String payerErpCode;

  @ApiModelProperty("付款方编码")
  private String payerCode;

  @ApiModelProperty("SAP状态")
  private String sapStatus;

  @ApiModelProperty(value = "组织信息")
  private  List<CustomerRelateOrgDto> orgList;

  @ApiModelProperty(value = "流程附件信息")
  private List<AttachmentVo> attachmentVos;

  @ApiModelProperty("文件类型(1:企业证书照片,2:法人身份证照片,3:经销商办公地照片)")
  private List<CustomerUploadDto> fileList;

  @ApiModelProperty("经销商编码集合")
  private List<String> customerCodeList;

  @ApiModelProperty("对接人列表")
  private List<CustomerDestinationDto> destinationList;

  @ApiModelProperty("对接人列表")
  private List<CustomerDockingDto> dockingList;

  @ApiModelProperty("联系人列表")
  private List<CustomerContactDto> contactList;

  @ApiModelProperty("销售区域列表")
  private List<CustomerSaleAreaDto> saleAreaList;

  @ApiModelProperty("客户开票信息")
  private List<CustomerBillDto> billList;

  @ApiModelProperty("收货地址")
  private List<CustomerAddressDto> customerAddressVos;

  @ApiModelProperty("仓库地址")
  private List<CustomerWarehouseAddressDto> warehouseAddressList;

  @ApiModelProperty("订单用筛选可选订单范围客户标记")
  private String orderCustomerFlag;
  
  @ApiModelProperty("公司代码列表，用户客户资金查询过滤")
  private List<String> companyCodeList;

  @ApiModelProperty("客户类型")
  private List<String> customerTypeList;
}