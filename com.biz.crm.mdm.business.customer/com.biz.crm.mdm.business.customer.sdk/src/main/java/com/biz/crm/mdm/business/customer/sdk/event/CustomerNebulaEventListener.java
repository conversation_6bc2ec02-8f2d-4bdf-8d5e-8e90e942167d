package com.biz.crm.mdm.business.customer.sdk.event;

import com.biz.crm.mdm.business.customer.sdk.dto.CustomerNebulaEventBatchDto;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerNebulaEventDto;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerNebulaEventUpdateDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * 客户事件引擎批量事件通知Dto
 *
 * <AUTHOR>
 */
public interface CustomerNebulaEventListener extends NebulaEvent {

  /**
   * 当客户信息创建时触发
   *
   * @param customerNebulaEventDto 创建时的vo
   */
  void onCreate(CustomerNebulaEventDto customerNebulaEventDto);

  /**
   * 当客户信息修改时触发
   *
   * @param customerNebulaEventUpdateDto
   */
  void onUpdate(CustomerNebulaEventUpdateDto customerNebulaEventUpdateDto);

  /**
   * 当客户信息禁用时触发
   *
   * @param customerNebulaEventBatchDto 禁用vo信息
   */
  void onDisable(CustomerNebulaEventBatchDto customerNebulaEventBatchDto);

  /**
   * 当客户信息启用时触发
   *
   * @param customerNebulaEventBatchDto 禁用vo信息
   */
  void onEnable(CustomerNebulaEventBatchDto customerNebulaEventBatchDto);

  /**
   * 当客户信息删除时触发
   *
   * @param customerNebulaEventBatchDto 删除vo信息
   */
  void onDelete(CustomerNebulaEventBatchDto customerNebulaEventBatchDto);

  /**
   * 当客户信息冻结时触发
   * @param customerNebulaEventBatchDto 冻结vo信息
   */
  void onFreeze(CustomerNebulaEventBatchDto customerNebulaEventBatchDto);

  /**
   * 当客户信息解冻时触发
   * @param customerNebulaEventBatchDto 解冻vo信息
   */
  void onUnfreeze(CustomerNebulaEventBatchDto customerNebulaEventBatchDto);
}
