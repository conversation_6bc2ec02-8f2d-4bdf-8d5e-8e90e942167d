package com.biz.crm.mdm.business.sale.territory.local.service.notify;

import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.mdm.business.sale.territory.sdk.dto.event.SaleTerritoryEventBatchDto;
import com.biz.crm.mdm.business.sale.territory.sdk.dto.event.SaleTerritoryEventDto;
import com.biz.crm.mdm.business.sale.territory.sdk.dto.event.SaleTerritoryEventUpdateDto;
import com.biz.crm.mdm.business.sale.territory.sdk.event.SaleTerritoryNebulaEventListener;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @title SaleTerritoryLogEventListenerImpl
 * @date 2023/3/2 15:56
 * @description 销售区域日志事件监听接口实现类
 */
@Component
public class SaleTerritoryLogEventListenerImpl implements SaleTerritoryNebulaEventListener {

  @Autowired(required = false)
  private CrmBusinessLogVoService crmBusinessLogVoService;

  @Override
  public void onEnable(SaleTerritoryEventDto saleTerritoryEventDto) {}

  @Override
  public void onDisable(SaleTerritoryEventBatchDto saleTerritoryEventBatchDto) {}

  @Override
  public void onDelete(SaleTerritoryEventBatchDto saleTerritoryEventBatchDto) {}

  /**
   * 新增销售区域时触发事件 存储日志
   *
   * @param saleTerritoryEventDto
   */
  @Override
  public void onCreate(SaleTerritoryEventDto saleTerritoryEventDto) {
    SaleTerritoryEventDto newest = saleTerritoryEventDto;
    String onlyKey = newest.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(null);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }

  /**
   * 修改销售区域时触发事件 存储日志
   *
   * @param saleTerritoryEventUpdateDto
   */
  @Override
  public void onUpdate(SaleTerritoryEventUpdateDto saleTerritoryEventUpdateDto) {
    SaleTerritoryEventDto newest = saleTerritoryEventUpdateDto.getNewSaleTerritoryEventDto();
    SaleTerritoryEventDto original = saleTerritoryEventUpdateDto.getOldSaleTerritoryEventDto();
    String onlyKey = newest.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }
}
