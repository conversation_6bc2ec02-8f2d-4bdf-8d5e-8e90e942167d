package com.biz.crm.mdm.business.sale.territory.sdk.event;

import com.biz.crm.mdm.business.sale.territory.sdk.vo.SaleTerritoryVo;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;
import java.util.List;

/**
 * <AUTHOR>
 * @title SaleTerritoryEventListener
 * @date 2023/2/25 16:56
 * @description 事件通知接口
 */
public interface SaleTerritoryEventListener extends NebulaEvent {
  /**
   * 当新增销售区域时通知监听
   *
   * @param SaleTerritoryVos
   */
  void onBatchCreate(List<SaleTerritoryVo> SaleTerritoryVos);

  /**
   * 当更新销售区域时通知监听
   *
   * @param SaleTerritoryVos
   */
  void onBatchUpdate(List<SaleTerritoryVo> SaleTerritoryVos);

  /**
   * 当删除销售区域时通知监听
   *
   * @param SaleTerritoryVos
   */
  void onBatchDelete(List<SaleTerritoryVo> SaleTerritoryVos);

  /**
   * 当启用销售区域时通知监听
   *
   * @param SaleTerritoryVos
   */
  void onBatchEnable(List<SaleTerritoryVo> SaleTerritoryVos);

  /**
   * 当禁用销售区域时通知监听
   *
   * @param eventDtos
   */
  void onBatchDisable(List<SaleTerritoryVo> eventDtos);
}
