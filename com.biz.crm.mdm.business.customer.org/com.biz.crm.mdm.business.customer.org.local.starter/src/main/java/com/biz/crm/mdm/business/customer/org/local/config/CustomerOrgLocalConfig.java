package com.biz.crm.mdm.business.customer.org.local.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 客户组织模块配置
 */
@Configuration
@EntityScan(basePackages = "com.biz.crm.mdm.business.customer.org.local.entity")
@ComponentScan(basePackages = {"com.biz.crm.mdm.business.customer.org"})
public class CustomerOrgLocalConfig {

}