package com.biz.crm.mdm.business.customer.org.local.controller;
/**
 * Created by <PERSON><PERSON> on 2021-10-28 17:43.
 */

import com.biz.crm.business.common.sdk.dto.TreeDto;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.vo.TreeVo;
import com.biz.crm.mdm.business.customer.org.local.service.CustomerOrgTreeVoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 迁移原/baseTreeController/customerOrgTree接口
 * @program: crm
 * @description: 客户组织：TreeVo：客户组织树
 * @author: <PERSON><PERSON>
 * @create: 2021-10-28 17:43
 **/

@Api(tags = "客户组织：TreeVo：客户组织树")
@Slf4j
@RestController
@RequestMapping("/v1/customerOrg/tree")
public class CustomerOrgTreeVoController {
  @Autowired(required = false)
  private CustomerOrgTreeVoService customerOrgTreeVoService;

  @ApiOperation(value = "客户组织树")
  @GetMapping
  public Result<List<TreeVo>> findTree(@ApiParam(name = "treeDto", value = "查询Dto") TreeDto treeDto) {
    try {
      List<TreeVo> result = this.customerOrgTreeVoService.findTree(treeDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
