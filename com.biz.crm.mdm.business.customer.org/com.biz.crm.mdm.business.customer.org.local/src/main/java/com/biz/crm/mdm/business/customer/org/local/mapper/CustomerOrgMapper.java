package com.biz.crm.mdm.business.customer.org.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.vo.LazyTreeVo;
import com.biz.crm.mdm.business.customer.org.local.entity.CustomerOrg;
import com.biz.crm.mdm.business.customer.org.sdk.dto.CustomerOrgPaginationDto;
import com.biz.crm.mdm.business.customer.org.sdk.vo.CustomerOrgVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by <PERSON><PERSON> on 2021-10-27 18:02.
 */
public interface CustomerOrgMapper extends BaseMapper<CustomerOrg> {
  /**
   * 通过启用状态和RuleCodes模糊查询自身及子集
   *
   * @param ruleCodes
   * @param enableStatus
   * @param tenantCode
   * @return
   */
  List<CustomerOrg> findCurAndChildrenByRuleCodeList(@Param("ruleCodes") List<String> ruleCodes,
                                                     @Param("enableStatus") String enableStatus,
                                                     @Param("tenantCode") String tenantCode,
                                                     @Param("delFlag") DelFlagStatusEnum delFlag);

  /**
   * 查找parentCode不为空但找不到对应上级的数据,设置parentCode为null
   *
   * @param tenantCode
   * @return
   */
  void updateOrphanParentCodeNull(@Param("tenantCode") String tenantCode, @Param("delFlag") DelFlagStatusEnum delFlag);

  /**
   * 分页条件查询
   *
   * @param page
   * @param customerOrgPaginationDto
   * @return
   */
  Page<CustomerOrgVo> findByConditions(Page<CustomerOrgVo> page,
                                       @Param("dto") CustomerOrgPaginationDto customerOrgPaginationDto,
                                       @Param("delFlag") DelFlagStatusEnum delFlag);

  /**
   * 查询产品层级懒加载数据
   *
   * @param enableStatus    启用状态
   * @param topOnly         传true只查第一层
   * @param parentCode      只查询该编码下一级
   * @param codeList        只查询这些编码
   * @param ruleCodeList    只查询这些降维编码
   * @param excludeRuleCode 排除这个降维编码的下级
   * @param tenantCode
   * @return
   */
  List<LazyTreeVo> findLazyTreeList(@Param("enableStatus") String enableStatus,
                                    @Param("topOnly") Boolean topOnly,
                                    @Param("parentCode") String parentCode,
                                    @Param("codeList") List<String> codeList,
                                    @Param("ruleCodeList") List<String> ruleCodeList,
                                    @Param("excludeRuleCode") String excludeRuleCode,
                                    @Param("tenantCode") String tenantCode,
                                    @Param("delFlag") DelFlagStatusEnum delFlag);


  /**
   * 通过组织机构集合查询当前组织及其以下组织的信息
   *
   * @param orgCodes
   * @param enableStatus
   * @param tenantCode
   * @return
   */
  List<CustomerOrg> findCurAndChildrenByOrgCodeList(@Param("orgCodes") List<String> orgCodes,
                                                     @Param("enableStatus") String enableStatus,
                                                     @Param("tenantCode") String tenantCode);

}
