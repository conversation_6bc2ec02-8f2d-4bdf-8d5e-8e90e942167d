package com.biz.crm.mdm.business.customer.org.sdk.deprecated.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 职位与客户组织关联
 *
 * <AUTHOR>
 * @date 2020-11-03 17:31:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Deprecated
@ApiModel(value = "职位与客户组织关联")
public class MdmPositionCustomerOrgRespVo {

  @ApiModelProperty("id")
  private String id;

  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty("职位名称")
  private String positionName;

  @Deprecated
  @ApiModelProperty("用户编码")
  private String userCode;

  @ApiModelProperty("用户名称")
  private String userName;

  @ApiModelProperty("用户全名")
  private String fullName;

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("组织名称")
  private String orgName;

  @ApiModelProperty("客户组织编码")
  private String customerOrgCode;

}