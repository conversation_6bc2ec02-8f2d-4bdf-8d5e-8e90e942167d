<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.visitor.local.mapper.VisitorPhoneMapper">
  <resultMap id="visitorPhoneEntity" type="com.biz.crm.mdm.business.visitor.local.entity.VisitorPhoneEntity"/>
  <resultMap id="visitorPhoneVo" type="com.biz.crm.mdm.business.visitor.sdk.vo.VisitorPhoneVo"/>

  <!--分页查询-->
  <select id="findByConditions" resultMap="visitorPhoneVo">
    select *
    from mdm_visitor_phone
    <where>
      <if test="dto.tenantCode != null and dto.tenantCode != '' ">
        AND tenant_code = #{dto.tenantCode}
      </if>
      <if test="dto.delFlag != null and dto.delFlag != '' ">
        AND del_flag = #{dto.delFlag}
      </if>
      <if test="dto.enableStatus != null and dto.enableStatus != '' ">
        AND enable_status = #{dto.enableStatus}
      </if>
      <if test="dto.account != null and dto.account != '' ">
        <bind name="likeAccount" value="'%'+dto.account+'%'"/>
        AND account like #{likeAccount}
      </if>
      <if test="dto.phone != null and dto.phone != '' ">
        <bind name="likePhone" value="'%'+dto.phone+'%'"/>
        AND phone like #{likePhone}
      </if>
    </where>
    ORDER BY create_time DESC
  </select>

</mapper>
