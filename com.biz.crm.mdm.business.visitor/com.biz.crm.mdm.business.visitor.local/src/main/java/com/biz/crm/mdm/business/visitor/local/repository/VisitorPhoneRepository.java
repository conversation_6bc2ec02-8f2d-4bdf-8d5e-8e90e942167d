package com.biz.crm.mdm.business.visitor.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.visitor.local.entity.VisitorAccountEntity;
import com.biz.crm.mdm.business.visitor.local.entity.VisitorPhoneEntity;
import com.biz.crm.mdm.business.visitor.local.mapper.VisitorPhoneMapper;
import com.biz.crm.mdm.business.visitor.sdk.dto.VisitorPhonePageDto;
import com.biz.crm.mdm.business.visitor.sdk.vo.VisitorPhoneVo;

import java.util.List;

import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Component;

/**
 * 游客手机表(repository)
 *
 * <AUTHOR>
 * @since 2022-08-30 17:23:01
 */
@Component
public class VisitorPhoneRepository extends ServiceImpl<VisitorPhoneMapper, VisitorPhoneEntity> {

  /**
   * 分页
   *
   * @param page
   * @param dto
   * @return
   */
  public Page<VisitorPhoneVo> findByConditions(Page<VisitorPhonePageDto> page, VisitorPhonePageDto dto) {
    return baseMapper.findByConditions(page, dto);
  }

  /**
   * 根据ID获取详情
   *
   * @param id
   * @return
   */
  public VisitorPhoneEntity findById(String id) {
    return this.lambdaQuery()
        .eq(VisitorPhoneEntity::getTenantCode,TenantUtils.getTenantCode())
        .in(VisitorPhoneEntity::getId,id)
        .one();
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids
   * @return
   */
  public List<VisitorPhoneEntity> findByIds(List<String> ids) {
    return this.lambdaQuery()
        .eq(VisitorPhoneEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(VisitorPhoneEntity::getTenantCode,TenantUtils.getTenantCode())
        .in(VisitorPhoneEntity::getId, ids)
        .list();
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param tenantCode
   * @param account
   * @return
   */
  public List<VisitorPhoneEntity> findByAccount(String tenantCode, String account) {
    return this.lambdaQuery()
        .eq(VisitorPhoneEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(VisitorPhoneEntity::getTenantCode, tenantCode)
        .eq(VisitorPhoneEntity::getAccount, account)
        .list();
  }

  /**
   * 根据id集合 更新组织启用/禁用状态
   *
   * @param ids
   * @param enable
   */
  public void updateEnableStatusByIds(List<String> ids, EnableStatusEnum enable) {
    this.lambdaUpdate().in(VisitorPhoneEntity::getId, ids)
        .eq(VisitorPhoneEntity::getTenantCode, TenantUtils.getTenantCode())    //新增租户编号判断条件
        .set(VisitorPhoneEntity::getEnableStatus, enable.getCode())
        .update();
  }

  /**
   * 逻辑删除
   *
   * @param ids
   */
  public void updateDelFlagByIds(List<String> ids) {
    this.lambdaUpdate().in(VisitorPhoneEntity::getId, ids)
        .eq(VisitorPhoneEntity::getTenantCode,TenantUtils.getTenantCode())    //新增租户编号判断条件
        .set(VisitorPhoneEntity::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
        .update();
  }

  /**
   * 根据手机号更新登录次数
   *
   * @param phone
   */
  public boolean updateLoginTimesByPhone(String tenantCode, String phone) {
    return this.lambdaUpdate()
        .eq(VisitorPhoneEntity::getTenantCode, tenantCode)
        .eq(VisitorPhoneEntity::getPhone, phone)
        .setSql("login_times = login_times + 1")
        .update();
  }

  /**
   * 重构修改方法
   * @param entity
   * @param tenantCode
   */
  public void updateByIdAndTenantCode(VisitorPhoneEntity entity, String tenantCode) {
    this.lambdaUpdate()
        .eq(VisitorPhoneEntity::getTenantCode,tenantCode)
        .in(VisitorPhoneEntity::getId,entity.getId())
        .setEntity(entity)
        .update();
  }
}
