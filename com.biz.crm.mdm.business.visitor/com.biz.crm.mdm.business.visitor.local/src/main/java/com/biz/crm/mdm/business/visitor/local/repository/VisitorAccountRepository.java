package com.biz.crm.mdm.business.visitor.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.mdm.business.visitor.local.entity.VisitorAccountEntity;
import com.biz.crm.mdm.business.visitor.local.mapper.VisitorAccountMapper;
import com.biz.crm.mdm.business.visitor.sdk.dto.VisitorAccountPageDto;
import com.biz.crm.mdm.business.visitor.sdk.vo.VisitorAccountVo;

import java.util.List;

import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Component;

/**
 * 游客账号表(repository)
 *
 * <AUTHOR>
 * @since 2022-08-30 17:22:28
 */
@Component
public class VisitorAccountRepository extends ServiceImpl<VisitorAccountMapper, VisitorAccountEntity> {

  /**
   * 分页
   *
   * @param page
   * @param dto
   * @return
   */
  public Page<VisitorAccountVo> findByConditions(Page<VisitorAccountPageDto> page, VisitorAccountPageDto dto) {
    return baseMapper.findByConditions(page, dto);
  }

  /**
   * 根据ID获取详情
   *
   * @param id
   * @return
   */
  public VisitorAccountEntity findById(String id) {
    return this.lambdaQuery()
        .eq(VisitorAccountEntity::getTenantCode,TenantUtils.getTenantCode())
        .in(VisitorAccountEntity::getId,id)
        .one();
  }

  /**
   * 根据id集合获取详情集合
   *
   * @param ids
   * @return
   */
  public List<VisitorAccountEntity> findByIds(List<String> ids) {
    return this.lambdaQuery()
        .eq(VisitorAccountEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(VisitorAccountEntity::getTenantCode,TenantUtils.getTenantCode())
        .in(VisitorAccountEntity::getId, ids)
        .list();
  }

  /**
   * 根据游客账号获取
   *
   * @param tenantCode 租户
   * @param account 游客账号
   * @return
   */
  public VisitorAccountEntity findByAccount(String tenantCode, String account) {
    return this.lambdaQuery()
        .eq(VisitorAccountEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(VisitorAccountEntity::getTenantCode, tenantCode)
        .eq(VisitorAccountEntity::getAccount, account)
        .one();
  }

  /**
   * 获取默认账号
   *
   * @param tenantCode
   * @return
   */
  public VisitorAccountEntity findDefaultAccount(String tenantCode) {
    return this.lambdaQuery()
        .eq(VisitorAccountEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .eq(VisitorAccountEntity::getIsDefault, BooleanEnum.TRUE.getCapital())
        .eq(VisitorAccountEntity::getTenantCode, tenantCode)
        .one();
  }

  /**
   * 根据id集合 更新组织启用/禁用状态
   *
   * @param ids
   * @param enable
   */
  public void updateEnableStatusByIds(List<String> ids, EnableStatusEnum enable) {
    this.lambdaUpdate().in(VisitorAccountEntity::getId, ids)
        .eq(VisitorAccountEntity::getTenantCode, TenantUtils.getTenantCode())    //新增租户编号判断条件
        .set(VisitorAccountEntity::getEnableStatus, enable.getCode())
        .update();
  }

  /**
   * 逻辑删除
   *
   * @param ids
   */
  public void updateDelFlagByIds(List<String> ids) {
    this.lambdaUpdate().in(VisitorAccountEntity::getId, ids)
        .eq(VisitorAccountEntity::getTenantCode,TenantUtils.getTenantCode())    //新增租户编号判断条件
        .set(VisitorAccountEntity::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
        .update();
  }

  /**
   * 重构修改方法
   * @param entity
   * @param tenantCode
   */
  public void updateByIdAndTenantCode(VisitorAccountEntity entity, String tenantCode) {
    this.lambdaUpdate()
        .eq(VisitorAccountEntity::getTenantCode,tenantCode)
        .in(VisitorAccountEntity::getId,entity.getId())
        .setEntity(entity)
        .update();

  }
}
