package com.biz.crm.mdm.business.visitor.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.mdm.business.visitor.local.entity.VisitorAccountEntity;
import com.biz.crm.mdm.business.visitor.local.entity.VisitorPhoneEntity;
import com.biz.crm.mdm.business.visitor.local.repository.VisitorAccountRepository;
import com.biz.crm.mdm.business.visitor.local.repository.VisitorPhoneRepository;
import com.biz.crm.mdm.business.visitor.sdk.constant.VisitorConstant;
import com.biz.crm.mdm.business.visitor.sdk.service.VisitorAccountVoService;
import com.biz.crm.mdm.business.visitor.sdk.dto.VisitorAccountPageDto;
import com.biz.crm.mdm.business.visitor.sdk.dto.VisitorAccountDto;
import com.biz.crm.mdm.business.visitor.sdk.vo.VisitorAccountVo;
import com.biz.crm.mdm.business.visitor.sdk.event.VisitorAccountEventListener;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;

/**
 * 游客账号表(VisitorAccount)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-30 17:22:29
 */
@Slf4j
@Service
public class VisitorAccountVoServiceImpl implements VisitorAccountVoService {

  @Autowired
  private VisitorAccountRepository visitorAccountRepository;

  @Autowired
  private VisitorPhoneRepository visitorPhoneRepository;

  @Autowired(required = false)
  private List<VisitorAccountEventListener> visitorAccountEventListeners;

  @Autowired(required = false)
  private GenerateCodeService generateCodeService;

  @Autowired
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public Page<VisitorAccountVo> findByConditions(Pageable pageable, VisitorAccountPageDto dto) {
    pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
    dto = Optional.ofNullable(dto).orElse(new VisitorAccountPageDto());
    dto.setTenantCode(TenantUtils.getTenantCode());
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    Page<VisitorAccountPageDto> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return this.visitorAccountRepository.findByConditions(page, dto);
  }

  @Override
  public VisitorAccountVo findDetailById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    VisitorAccountEntity entity = this.visitorAccountRepository.findById(id);
    if (entity == null) {
      return null;
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(entity, VisitorAccountVo.class, HashSet.class, ArrayList.class);
  }

  @Override
  public VisitorAccountVo findByAccount(String account) {
    if (StringUtils.isBlank(account)) {
      return null;
    }
    VisitorAccountEntity entity = this.visitorAccountRepository.findByAccount(TenantUtils.getTenantCode(), account);
    if (entity == null) {
      return null;
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(entity, VisitorAccountVo.class, HashSet.class, ArrayList.class);
  }

  @Override
  public VisitorAccountVo findDefaultAccount() {
    VisitorAccountEntity entity = this.visitorAccountRepository.findDefaultAccount(TenantUtils.getTenantCode());
    if (entity == null) {
      return null;
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(entity, VisitorAccountVo.class, HashSet.class, ArrayList.class);
  }

  @Override
  @Transactional
  public void create(VisitorAccountDto dto) {
    this.createValidation(dto);
    dto.setTenantCode(TenantUtils.getTenantCode());
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    //默认填入创建时间
    dto.setEffectiveTime(ObjectUtils.isEmpty(dto.getEffectiveTime())?new Date():dto.getEffectiveTime());
    dto.setExpireTime(ObjectUtils.isEmpty(dto.getExpireTime())?new Date(9999, 11, 30):dto.getExpireTime());
    if (BooleanEnum.TRUE.getCapital().equals(dto.getIsDefault())) {
      VisitorAccountEntity entity = this.visitorAccountRepository.findDefaultAccount(TenantUtils.getTenantCode());
      Validate.isTrue(entity==null, "默认账号已经存在，请勿重复添加！");
    } else {
      dto.setIsDefault(BooleanEnum.FALSE.getCapital());
    }
    // redis生成发货单编码，编码规则为FHD+年月日+5位顺序数。每天都从00001开始编
    String code = this.generateCodeService.generateCode(VisitorConstant.VISITOR_CODE);
    Validate.notEmpty(code, "创建游客账号时，编码生成失败！");
    dto.setAccount(code);
    VisitorAccountEntity entity = this.nebulaToolkitService.copyObjectByWhiteList(dto, VisitorAccountEntity.class, HashSet.class, ArrayList.class);
    //新增租户编号
    entity.setTenantCode(TenantUtils.getTenantCode());
    this.visitorAccountRepository.save(entity);
    // 发送通知
    if (CollectionUtils.isNotEmpty(visitorAccountEventListeners)) {
      VisitorAccountVo vo = this.nebulaToolkitService.copyObjectByWhiteList(entity, VisitorAccountVo.class, HashSet.class, ArrayList.class);
      this.visitorAccountEventListeners.forEach(event -> event.onCreate(vo));
    }
  }

  @Override
  @Transactional
  public void update(VisitorAccountDto dto) {
    this.updateValidation(dto);
    VisitorAccountEntity oldEntity = this.visitorAccountRepository.findById(dto.getId());
    Validate.notNull(oldEntity, "修改信息不存在！");
    Validate.isTrue(oldEntity.getIsDefault().equals(dto.getIsDefault()), "不能修改是否默认！");
    // 账号属性，第一种情况：原来账号属性为Y 可被多用户使用，现在改为N 只可被一个用户使用；第二种情况：即第一种情况反过来，没有任何限制
    if (BooleanEnum.TRUE.getCapital().equals(oldEntity.getAttribute()) && BooleanEnum.FALSE.getCapital().equals(dto.getAttribute())) {
      List<VisitorPhoneEntity> visitorPhoneEntityList = this.visitorPhoneRepository.findByAccount(TenantUtils.getTenantCode(), oldEntity.getAccount());
      if (CollectionUtils.isNotEmpty(visitorPhoneEntityList)) {
        Validate.isTrue(visitorPhoneEntityList.size()<=1, "当前游客账号已经被多个用户手机绑定，不能修改账户属性！");
      }
    }
    VisitorAccountEntity entity = this.nebulaToolkitService.copyObjectByWhiteList(dto, VisitorAccountEntity.class, HashSet.class, ArrayList.class);
    //重构修改方法
    this.visitorAccountRepository.updateByIdAndTenantCode(entity,TenantUtils.getTenantCode());
    // 发送修改通知
    if (CollectionUtils.isNotEmpty(visitorAccountEventListeners)) {
      VisitorAccountVo oldVo = this.nebulaToolkitService.copyObjectByWhiteList(oldEntity, VisitorAccountVo.class, HashSet.class, ArrayList.class);
      VisitorAccountVo newVo = this.nebulaToolkitService.copyObjectByWhiteList(entity, VisitorAccountVo.class, HashSet.class, ArrayList.class);
      this.visitorAccountEventListeners.forEach(event -> event.onUpdate(oldVo, newVo));
    }
  }

  @Override
  @Transactional
  public void enableBatch(List<String> ids) {
    Validate.isTrue(CollectionUtils.isNotEmpty(ids), "id集合不能为空");
    List<VisitorAccountEntity> entities = this.visitorAccountRepository.findByIds(ids);
    Validate.isTrue(CollectionUtils.isNotEmpty(entities), "不存在或已删除！");
    this.visitorAccountRepository.updateEnableStatusByIds(ids, EnableStatusEnum.ENABLE);
    // 发送启用通知
    if (CollectionUtils.isNotEmpty(visitorAccountEventListeners)) {
      List<VisitorAccountVo> voList = (List<VisitorAccountVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entities, VisitorAccountEntity.class, VisitorAccountVo.class, HashSet.class, ArrayList.class);
      this.visitorAccountEventListeners.forEach(event -> event.onEnable(voList));
    }
  }

  @Override
  @Transactional
  public void disableBatch(List<String> ids) {
    Validate.isTrue(CollectionUtils.isNotEmpty(ids), "id集合不能为空");
    List<VisitorAccountEntity> entities = this.visitorAccountRepository.findByIds(ids);
    Validate.isTrue(CollectionUtils.isNotEmpty(entities), "不存在或已删除！");
    this.visitorAccountRepository.updateEnableStatusByIds(ids, EnableStatusEnum.DISABLE);
    // 发送禁用通知
    if (CollectionUtils.isNotEmpty(visitorAccountEventListeners)) {
      List<VisitorAccountVo> voList = (List<VisitorAccountVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entities, VisitorAccountEntity.class, VisitorAccountVo.class, HashSet.class, ArrayList.class);
      this.visitorAccountEventListeners.forEach(event -> event.onDisable(voList));
    }
  }

  @Override
  @Transactional
  public void updateDelFlagByIds(List<String> ids) {
    Validate.isTrue(CollectionUtils.isNotEmpty(ids), "id集合不能为空");
    List<VisitorAccountEntity> entities = this.visitorAccountRepository.findByIds(ids);
    Validate.isTrue(CollectionUtils.isNotEmpty(entities), "不存在或已删除！");
    this.visitorAccountRepository.updateDelFlagByIds(ids);
    // 发送删除通知
    if (CollectionUtils.isNotEmpty(visitorAccountEventListeners)) {
      List<VisitorAccountVo> voList = (List<VisitorAccountVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entities, VisitorAccountEntity.class, VisitorAccountVo.class, HashSet.class, ArrayList.class);
      this.visitorAccountEventListeners.forEach(event -> event.onDelete(voList));
    }
  }

  private void createValidation(VisitorAccountDto dto) {
    // TODO 具体实现
    this.validation(dto);
  }

  private void updateValidation(VisitorAccountDto dto) {
    // TODO 具体实现
    this.validation(dto);
    Validate.notBlank(dto.getId(), "ID不能为空");
    Validate.notBlank(dto.getAccount(), "账号不能为空");
  }

  private void validation(VisitorAccountDto dto) {
    // TODO 具体实现
    Validate.notNull(dto, "游客账号对象不能为空");
    Validate.notNull(dto.getAttribute(), "账号属性不能为空");
    Validate.notNull(dto.getRoleCode(), "关联的角色编码不能为空");
    Validate.notNull(dto.getRoleName(), "关联的角色名称不能为空");
  }
}
