package com.biz.crm.mdm.business.visitor.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 游客账号表(VisitorAccount)实体类
 *
 * <AUTHOR>
 * @since 2022-08-30 17:23:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "mdm_visitor_account")
@TableName(value = "mdm_visitor_account")
@ApiModel(value = "VisitorAccount", description = "游客账号表")
@org.hibernate.annotations.Table(appliesTo = "mdm_visitor_account", comment = "游客账号表")
public class VisitorAccountEntity extends TenantFlagOpEntity {

  @ApiModelProperty("账号")
  @TableField(value = "account")
  @Column(name = "account", length = 64, columnDefinition = "varchar(64) COMMENT '账号'")
  private String account;

  @ApiModelProperty("账号属性：Y 可被多用户使用，N 只可被一个用户使用")
  @TableField(value = "attribute")
  @Column(name = "attribute", length = 10, columnDefinition = "varchar(10) COMMENT '账号属性：Y 可被多用户使用，N 只可被一个用户使用'")
  private String attribute;

  @ApiModelProperty("关联的角色编码")
  @TableField(value = "role_code")
  @Column(name = "role_code", length = 64, columnDefinition = "varchar(64) COMMENT '关联的角色编码'")
  private String roleCode;

  @ApiModelProperty("关联的角色名称")
  @TableField(value = "role_name")
  @Column(name = "role_name", length = 64, columnDefinition = "varchar(64) COMMENT '关联的角色名称'")
  private String roleName;

  @ApiModelProperty("是否默认")
  @TableField(value = "is_default")
  @Column(name = "is_default", length = 10, columnDefinition = "varchar(10) COMMENT '是否默认'")
  private String isDefault;

  /**
   * 生效时间
   */
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @TableField(value = "effective_time")
  @Column(name = "effective_time" , length = 20, columnDefinition = "datetime COMMENT '生效时间 '")
  private Date effectiveTime;

  /**
   * 过期时间
   */
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @TableField(value = "expire_time")
  @Column(name = "expire_time" , length = 20, columnDefinition = "datetime COMMENT '过期时间 '")
  private Date expireTime;
}
