package com.biz.crm.mdm.business.visitor.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.visitor.sdk.service.VisitorPhoneVoService;
import com.biz.crm.mdm.business.visitor.sdk.dto.VisitorPhonePageDto;
import com.biz.crm.mdm.business.visitor.sdk.dto.VisitorPhoneDto;
import com.biz.crm.mdm.business.visitor.sdk.vo.VisitorPhoneVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * 游客手机表(VisitorPhone)表控制层
 *
 * <AUTHOR>
 * @since 2022-08-30 17:23:01
 */
@Slf4j
@Api(tags = "游客手机表：VisitorPhoneVo")
@RestController
@RequestMapping(value = {"/v1/visitorPhone/visitorPhoneVo"})
public class VisitorPhoneVoController {

  @Autowired
  private VisitorPhoneVoService visitorPhoneVoService;

  @ApiOperation(value = "查询分页列表")
  @GetMapping(value = {"/findByConditions"})
  public Result<Page<VisitorPhoneVo>> findByConditions(@PageableDefault(50) Pageable pageable, VisitorPhonePageDto dto) {
    try {
      Page<VisitorPhoneVo> result = this.visitorPhoneVoService.findByConditions(pageable, dto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "详情")
  @GetMapping(value = {"/findDetailById"})
  public Result<VisitorPhoneVo> findDetailById(@RequestParam("id") String id) {
    try {
      VisitorPhoneVo vo = this.visitorPhoneVoService.findDetailById(id);
      return Result.ok(vo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据游客账号获取游客手机")
  @GetMapping(value = {"/findByAccount"})
  public Result<List<VisitorPhoneVo>> findByAccount(@RequestParam("account") String account) {
    try {
      List<VisitorPhoneVo> voList = this.visitorPhoneVoService.findByAccount(account);
      return Result.ok(voList);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据默认游客账号获取游客手机")
  @GetMapping(value = {"/findByDefaultAccount"})
  public Result<List<VisitorPhoneVo>> findByDefaultAccount() {
    try {
      List<VisitorPhoneVo> voList = this.visitorPhoneVoService.findByDefaultAccount();
      return Result.ok(voList);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "创建（根据自定义选中游客账号创建）")
  @PostMapping(value = "")
  public Result create(@RequestBody VisitorPhoneDto dto) {
    try {
      this.visitorPhoneVoService.create(dto);
      return Result.ok("创建成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "创建（根据默认游客账号创建）")
  @PostMapping(value = "createByDefaultAccount")
  public Result createByDefaultAccount(@RequestBody VisitorPhoneDto dto) {
    try {
      this.visitorPhoneVoService.createByDefaultAccount(dto);
      return Result.ok("创建成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "更新")
  @PatchMapping(value = "")
  public Result update(@RequestBody VisitorPhoneDto dto) {
    try {
      this.visitorPhoneVoService.update(dto);
      return Result.ok("更新成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "启用")
  @PatchMapping("/enable")
  public Result enable(@RequestBody List<String> ids) {
    try {
      this.visitorPhoneVoService.enableBatch(ids);
      return Result.ok("启用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "禁用")
  @PatchMapping("/disable")
  public Result disable(@RequestBody List<String> ids) {
    try {
      this.visitorPhoneVoService.disableBatch(ids);
      return Result.ok("禁用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "逻辑删除")
  @DeleteMapping("/delete")
  public Result delete(@RequestParam("ids") List<String> ids) {
    try {
      this.visitorPhoneVoService.updateDelFlagByIds(ids);
      return Result.ok("删除成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据手机号更新登录次数")
  @PostMapping(value = "updateLoginTimesByPhone")
  public Result updateLoginTimesByPhone(@RequestParam("phone") String phone) {
    try {
      this.visitorPhoneVoService.updateLoginTimesByPhone(phone);
      return Result.ok("更新成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
