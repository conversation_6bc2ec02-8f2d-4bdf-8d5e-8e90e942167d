package com.biz.crm.mdm.business.visitor.feign.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.visitor.feign.feign.VisitorPhoneVoFeign;
import com.biz.crm.mdm.business.visitor.sdk.dto.VisitorPhoneDto;
import com.biz.crm.mdm.business.visitor.sdk.dto.VisitorPhonePageDto;
import com.biz.crm.mdm.business.visitor.sdk.service.VisitorPhoneVoService;
import com.biz.crm.mdm.business.visitor.sdk.vo.VisitorPhoneVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户信息feign接口实现类
 *
 * <AUTHOR>
 * @since 2022-08-30 17:22:27
 */
@Service("VisitorPhoneFeignVoServiceImpl")
public class VisitorPhoneFeignVoServiceImpl implements VisitorPhoneVoService {

  @Autowired(required = false)
  private VisitorPhoneVoFeign visitorPhoneVoFeign;

  @Override
  public Page<VisitorPhoneVo> findByConditions(Pageable pageable, VisitorPhonePageDto dto) {
    return null;
  }

  @Override
  public VisitorPhoneVo findDetailById(String id) {
    return this.visitorPhoneVoFeign.findDetailById(id).checkFeignResult();
  }

  @Override
  public List<VisitorPhoneVo> findByAccount(String account) {
    return this.visitorPhoneVoFeign.findByAccount(account).checkFeignResult();
  }

  @Override
  public List<VisitorPhoneVo> findByDefaultAccount() {
    return this.visitorPhoneVoFeign.findByDefaultAccount().checkFeignResult();
  }

  @Override
  public void create(VisitorPhoneDto dto) {
    this.visitorPhoneVoFeign.create(dto);
  }

  @Override
  public void createByDefaultAccount(VisitorPhoneDto dto) {
    this.visitorPhoneVoFeign.createByDefaultAccount(dto);
  }

  @Override
  public void update(VisitorPhoneDto dto) {

  }

  @Override
  public void enableBatch(List<String> ids) {

  }

  @Override
  public void disableBatch(List<String> ids) {

  }

  @Override
  public void updateDelFlagByIds(List<String> ids) {

  }

  @Override
  public void updateLoginTimesByPhone(String phone) {
    this.visitorPhoneVoFeign.updateLoginTimesByPhone(phone);
  }
}
