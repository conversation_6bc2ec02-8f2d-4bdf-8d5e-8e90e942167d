package com.biz.crm.mdm.business.visitor.feign.feign.internal;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.visitor.feign.feign.VisitorAccountVoFeign;
import com.biz.crm.mdm.business.visitor.sdk.vo.VisitorAccountVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用户信息feign接口熔断类
 *
 * <AUTHOR>
 * @since 2022-08-30 17:22:27
 */
@Slf4j
@Component
public class VisitorAccountVoFeignFallbackImpl implements FallbackFactory<VisitorAccountVoFeign> {
  @Override
  public VisitorAccountVoFeign create(Throwable throwable) {
    return new VisitorAccountVoFeign() {

      @Override
      public Result<VisitorAccountVo> findByAccount(String account) {
        throw new UnsupportedOperationException("根据游客账号获取游客信息熔断");
      }

      @Override
      public Result<VisitorAccountVo> findByDefaultAccount() {
        throw new UnsupportedOperationException("获取默认游客账号熔断");
      }
    };
  }
}
