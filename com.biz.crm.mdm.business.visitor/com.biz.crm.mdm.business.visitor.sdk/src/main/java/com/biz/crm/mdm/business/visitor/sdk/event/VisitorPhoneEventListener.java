package com.biz.crm.mdm.business.visitor.sdk.event;

import com.biz.crm.mdm.business.visitor.sdk.vo.VisitorPhoneVo;

import java.util.List;

/**
 * 游客手机表变更事件通知接口
 *
 * <AUTHOR>
 * @since 2022-08-30 17:16:09
 */
public interface VisitorPhoneEventListener {

  /**
   * 创建时触发
   *
   * @param vo
   */
  default void onCreate(VisitorPhoneVo vo) {
  }

  /**
   * 编辑时触发
   *
   * @param oldVo
   * @param newVo
   */
  default void onUpdate(VisitorPhoneVo oldVo, VisitorPhoneVo newVo) {

  }

  /**
   * 启用时触发
   *
   * @param list
   */
  default void onEnable(List<VisitorPhoneVo> list) {

  }

  /**
   * 禁用时触发
   *
   * @param list
   */
  default void onDisable(List<VisitorPhoneVo> list) {
  }

  /**
   * 删除时触发
   *
   * @param list
   */
  default void onDelete(List<VisitorPhoneVo> list) {

  }
}

