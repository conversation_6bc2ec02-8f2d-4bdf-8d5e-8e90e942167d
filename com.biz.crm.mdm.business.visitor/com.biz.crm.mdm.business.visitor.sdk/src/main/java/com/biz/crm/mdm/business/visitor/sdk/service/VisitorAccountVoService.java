package com.biz.crm.mdm.business.visitor.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.visitor.sdk.dto.VisitorAccountPageDto;
import com.biz.crm.mdm.business.visitor.sdk.dto.VisitorAccountDto;
import com.biz.crm.mdm.business.visitor.sdk.vo.VisitorAccountVo;

import java.util.List;

import org.springframework.data.domain.Pageable;

/**
 * 游客账号表(VisitorAccount)表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-30 17:02:52
 */
public interface VisitorAccountVoService {

  /**
   * 分页条件查询
   *
   * @param pageable
   * @param dto
   * @return
   */
  Page<VisitorAccountVo> findByConditions(Pageable pageable, VisitorAccountPageDto dto);

  /**
   * 按id查询详情
   *
   * @param id
   * @return
   */
  VisitorAccountVo findDetailById(String id);

  /**
   * 按游客账号查询详情
   *
   * @param account
   * @return
   */
  VisitorAccountVo findByAccount(String account);

  /**
   * 查询默认游客账号
   *
   * @return
   */
  VisitorAccountVo findDefaultAccount();

  /**
   * 创建
   *
   * @param dto
   * @return
   */
  void create(VisitorAccountDto dto);

  /**
   * 更新
   *
   * @param dto
   * @return
   */
  void update(VisitorAccountDto dto);

  /**
   * 按id集合启用
   *
   * @param ids
   */
  void enableBatch(List<String> ids);

  /**
   * 按id集合禁用
   *
   * @param ids
   */
  void disableBatch(List<String> ids);

  /**
   * 逻辑删除
   *
   * @param ids
   */
  void updateDelFlagByIds(List<String> ids);
}
