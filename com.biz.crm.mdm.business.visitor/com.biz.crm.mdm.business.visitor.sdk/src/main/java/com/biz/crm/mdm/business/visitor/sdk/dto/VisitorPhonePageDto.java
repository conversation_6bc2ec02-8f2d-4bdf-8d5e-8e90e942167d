package com.biz.crm.mdm.business.visitor.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 游客手机表分页查询dto
 *
 * <AUTHOR>
 * @since 2022-08-30 17:16:07
 */
@Data
@ApiModel(value = "VisitorPhonePageDto", description = "游客手机表分页查询dto")
public class VisitorPhonePageDto extends TenantFlagOpDto {

  /**
   * 账号
   */
  @ApiModelProperty("关联账号")
  private String account;

  /**
   * 用户唯一识别号
   */
  @ApiModelProperty("用户唯一识别号")
  private String phone;

  /**
   * 登录次数
   */
  @ApiModelProperty("登录次数")
  private Integer loginTimes;
}

