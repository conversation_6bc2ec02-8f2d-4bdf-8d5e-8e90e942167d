package com.biz.crm.mdm.business.visitor.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 游客账号表vo
 *
 * <AUTHOR>
 * @since 2022-08-30 17:02:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "游客账号表Vo")
public class VisitorAccountVo extends TenantFlagOpVo {

  /**
   * 账号
   */
  @ApiModelProperty("账号")
  private String account;

  /**
   * 账号属性：Y 可被多用户使用，N 只可被一个用户使用
   */
  @ApiModelProperty("账号属性：Y 可被多用户使用，N 只可被一个用户使用")
  private String attribute;

  /**
   * 关联的角色编码
   */
  @ApiModelProperty("关联的角色编码")
  private String roleCode;

  /**
   * 关联的角色名称
   */
  @ApiModelProperty("关联的角色名称")
  private String roleName;

  /**
   * 是否默认
   */
  @ApiModelProperty("是否默认")
  private String isDefault;

  /**
   * 生效时间
   */
  @ApiModelProperty(name = "effectiveTime", value = "生效时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date effectiveTime;

  /**
   * 过期时间
   */
  @ApiModelProperty(name = "expireTime", value = "过期时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date expireTime;
}

