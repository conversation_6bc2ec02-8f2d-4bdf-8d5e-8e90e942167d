package com.biz.crm.mdm.business.customer.channel.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.mdm.business.customer.channel.local.entity.CustomerChannel;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 客户渠道主表(CustomerChannel)表mybatis访问层
 *
 * <AUTHOR>
 * @since 2022-10-31 17:04:11
 */
public interface CustomerChannelMapper extends BaseMapper<CustomerChannel> {

  /**
   * 分页查询所有数据
   *
   * @param page 分页对象
   * @param customerChannel 查询实体
   * @return 所有数据
  */
  public Page<CustomerChannel> findByConditions(@Param("page") Page<CustomerChannel> page, @Param("customerChannel") CustomerChannel customerChannel);
}

