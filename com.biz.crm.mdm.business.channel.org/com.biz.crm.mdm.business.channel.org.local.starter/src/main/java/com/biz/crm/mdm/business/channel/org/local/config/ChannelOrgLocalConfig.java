package com.biz.crm.mdm.business.channel.org.local.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 渠道组织模块配置
 */
@Configuration
@EntityScan(basePackages = "com.biz.crm.mdm.business.channel.org.local.entity")
@ComponentScan(basePackages = {"com.biz.crm.mdm.business.channel.org"})
public class ChannelOrgLocalConfig {

}