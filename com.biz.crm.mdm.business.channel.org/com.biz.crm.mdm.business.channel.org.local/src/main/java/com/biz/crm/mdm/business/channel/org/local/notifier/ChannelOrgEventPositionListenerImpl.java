package com.biz.crm.mdm.business.channel.org.local.notifier;
/**
 * Created by <PERSON><PERSON> on 2021-10-12 17:15.
 */

import com.biz.crm.mdm.business.channel.org.local.entity.ChannelOrgPosition;
import com.biz.crm.mdm.business.channel.org.local.service.ChannelOrgPositionVoService;
import com.biz.crm.mdm.business.channel.org.sdk.event.ChannelOrgEventListener;
import com.biz.crm.mdm.business.channel.org.sdk.vo.ChannelOrgVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: crm
 * @description: 渠道组织关联职位监听渠道组织事件
 * @author: <PERSON><PERSON>
 **/
@Component
@Slf4j
public class ChannelOrgEventPositionListenerImpl implements ChannelOrgEventListener {
  @Autowired(required = false)
  private ChannelOrgPositionVoService channelOrgPositionVoService;

  @Override
  public void onBatchCreate(List<ChannelOrgVo> channelOrgVos) {

  }

  @Override
  public void onBatchUpdate(List<ChannelOrgVo> channelOrgVos) {

  }

  /**
   * 删除渠道组织时判断是否存在与职位的关联
   *
   * @param channelOrgVos
   */
  @Override
  public void onBatchDelete(List<ChannelOrgVo> channelOrgVos) {
    if (CollectionUtils.isEmpty(channelOrgVos)) {
      return;
    }
    List<String> customerOrgCodes =
        channelOrgVos.stream().map(ChannelOrgVo::getChannelOrgCode).collect(Collectors.toList());
    List<ChannelOrgPosition> channelOrgPositions =
        channelOrgPositionVoService.findByCustomerOrgCodes(customerOrgCodes);
    Validate.isTrue(CollectionUtils.isEmpty(channelOrgPositions), "存在渠道组织和职位的关联，无法删除！");
  }

  @Override
  public void onBatchEnable(List<ChannelOrgVo> channelOrgVos) {

  }

  @Override
  public void onBatchDisable(List<ChannelOrgVo> eventDtos) {

  }
}
