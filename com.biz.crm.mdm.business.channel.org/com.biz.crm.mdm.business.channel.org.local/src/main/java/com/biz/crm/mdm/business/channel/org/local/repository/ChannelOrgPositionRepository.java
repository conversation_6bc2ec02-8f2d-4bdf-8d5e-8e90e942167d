package com.biz.crm.mdm.business.channel.org.local.repository;
/**
 * Created by <PERSON><PERSON> on 2021-11-01 16:48.
 */

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.channel.org.local.entity.ChannelOrgPosition;
import com.biz.crm.mdm.business.channel.org.local.mapper.ChannelOrgPositionMapper;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @program: crm
 * @description: 渠道组织和职位关联数据库访问类
 * @author: <PERSON><PERSON>
 * @create: 2021-11-01 16:48
 **/
@Component
public class ChannelOrgPositionRepository extends ServiceImpl<ChannelOrgPositionMapper, ChannelOrgPosition> {

  /**
   * 更加渠道组织编码和职位集合删除关联关系
   *
   * @param customerOrgCode
   * @param positionCodeList
   */
  public void removeRelationByCustomerOrgCodeAndPositionCodes(String customerOrgCode, List<String> positionCodeList, String tenantCode) {
    Validate.notEmpty(customerOrgCode, "渠道组织编码必须传入！");
    Validate.isTrue(!CollectionUtils.isEmpty(positionCodeList),
        "需要关联的职位编码不能为空！");
    this.lambdaUpdate()
        .eq(ChannelOrgPosition::getTenantCode, tenantCode)
        .eq(ChannelOrgPosition::getChannelOrgCode, customerOrgCode)
        .in(ChannelOrgPosition::getPositionCode, positionCodeList)
        .remove();
  }

  /**
   * 通过职位编码查询与渠道组织的关联关系
   *
   * @param positionCode
   * @param tenantCode
   * @return
   */
  public List<ChannelOrgPosition> findListByPositionCode(List<String> positionCode, String tenantCode) {
    return this.lambdaQuery()
        .eq(ChannelOrgPosition::getTenantCode, tenantCode)
        .in(ChannelOrgPosition::getPositionCode, positionCode).list();
  }

  /**
   * 通过渠道组织编码集合查找与职位的关联关系
   *
   * @param customerOrgCodes
   * @param tenantCode
   * @return
   */
  public List<ChannelOrgPosition> findListByCustomerOrgCodes(List<String> customerOrgCodes, String tenantCode) {
    return this.lambdaQuery()
        .eq(ChannelOrgPosition::getTenantCode, tenantCode)
        .in(ChannelOrgPosition::getChannelOrgCode, customerOrgCodes).list();
  }

  /**
   * 重构删除方法  通过ids和租户编号删除
   * @param ids
   */
  public void deleteByIdsAndTenantCode(List<String> ids,String tenantCode) {
    this.lambdaUpdate()
        .eq(ChannelOrgPosition::getTenantCode,tenantCode)
        .in(ChannelOrgPosition::getId, ids)
        .remove();
  }
}
