<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.channel.org.local.mapper.ChannelOrgMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.biz.crm.mdm.business.channel.org.local.entity.ChannelOrg">
    <id column="id" property="id"/>
    <result column="tenant_code" property="tenantCode"/>
    <result column="create_account" property="createAccount"/>
    <result column="create_name" property="createName"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_account" property="modifyAccount"/>
    <result column="modify_name" property="modifyName"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="channel_org_code" property="channelOrgCode"/>
    <result column="channel_org_name" property="channelOrgName"/>
    <result column="channel_org_level" property="channelOrgLevel"/>
    <result column="channel_org_type" property="channelOrgType"/>
    <result column="channel_org_desc" property="channelOrgDesc"/>
    <result column="parent_code" property="parentCode"/>
    <result column="level_num" property="levelNum"/>
    <result column="rule_code" property="ruleCode"/>
    <result column="del_flag" property="delFlag"/>
    <result column="enable_status" property="enableStatus"/>
    <result column="remark" property="remark"/>
  </resultMap>
  <update id="updateOrphanParentCodeNull">
    UPDATE mdm_channel_org t
    SET t.parent_code = NULL
    WHERE
      t.id IN (
      SELECT
        c1.id
      FROM(
        SELECT a.id
        FROM mdm_channel_org a
        LEFT JOIN mdm_channel_org b ON a.parent_code = b.channel_org_code AND b.tenant_code = #{tenantCode} AND b.del_flag = #{delFlag.code}
        WHERE (a.tenant_code = #{tenantCode}
          AND a.del_flag = #{delFlag.code}
          AND a.parent_code IS NOT NULL
          AND a.parent_code != ''
          AND b.channel_org_code IS NULL) or
           (
           a.tenant_code = #{tenantCode}
          AND a.del_flag != #{delFlag.code}
           )) c1
      )
  </update>
  <select id="findCurAndChildrenByRuleCodeList"
          resultType="com.biz.crm.mdm.business.channel.org.local.entity.ChannelOrg">
    select a.*,b.channel_org_name as parent_name
    from mdm_channel_org a
    left join mdm_channel_org b on a.parent_code=b.channel_org_code AND b.tenant_code = #{tenantCode} AND b.del_flag = #{delFlag.code}
    WHERE 1=1
    AND a.del_flag = #{delFlag.code}
    AND a.tenant_code = #{tenantCode}
    <if test="enableStatus != null and enableStatus != ''">
      and a.enable_status=#{enableStatus}
    </if>
    <if test="ruleCodes != null and ruleCodes.size > 0">
      and
      <foreach collection="ruleCodes" item="item" index="index" open="(" separator="or" close=")">
        a.rule_code LIKE CONCAT(#{item},'%')
      </foreach>
    </if>
    order by a.create_time desc
  </select>
  <select id="findByConditions" resultType="com.biz.crm.mdm.business.channel.org.sdk.vo.ChannelOrgVo">
    select a.*,b.channel_org_name as parent_name
    from mdm_channel_org a
    left join mdm_channel_org b on a.parent_code=b.channel_org_code AND b.tenant_code = #{dto.tenantCode} AND b.del_flag = #{delFlag.code}
    WHERE 1=1
    AND a.del_flag = #{delFlag.code}
    AND a.tenant_code = #{dto.tenantCode}
    <if test="dto.ruleCode != null and dto.ruleCode != ''">
      <bind name="rightLikeRuleCode" value="dto.ruleCode + '%'"/>
      and a.rule_code like #{rightLikeRuleCode}
    </if>
    <if test="dto.channelOrgCode != null and dto.channelOrgCode != ''">
      <bind name="likeChannelOrgCode" value="'%' + dto.channelOrgCode + '%'"/>
      and a.channel_org_code like #{likeChannelOrgCode}
    </if>
    <if test="dto.channelOrgName != null and dto.channelOrgName != ''">
      <bind name="likeChannelOrgName" value="'%' + dto.channelOrgName + '%'"/>
      and a.channel_org_name like #{likeChannelOrgName}
    </if>
    <if test="dto.channelOrgLevel != null and dto.channelOrgLevel != ''">
      and a.channel_org_level=#{dto.channelOrgLevel}
    </if>
    <if test="dto.channelOrgType != null and dto.channelOrgType != ''">
      and a.channel_org_type=#{dto.channelOrgType}
    </if>
    <if test="dto.enableStatus != null and dto.enableStatus != ''">
      and a.enable_status=#{dto.enableStatus}
    </if>
    <if test="dto.createName != null and dto.createName != ''">
      <bind name="likecreateName" value="'%' + dto.createName + '%'"/>
      and a.create_name like #{likecreateName}
    </if>
    <if test="dto.modifyName != null and dto.modifyName != ''">
      <bind name="likeupdateName" value="'%' + dto.modifyName + '%'"/>
      and a.modify_name like #{likeupdateName}
    </if>
    <if test="dto.createTime != null">
      and a.create_time &gt;= #{dto.createTime}
    </if>
    <if test="dto.parentCode != null and dto.parentCode != ''">
      and a.parent_code = #{dto.parentCode}
    </if>
    <if test="dto.channelOrgCodeOrName != null and dto.channelOrgCodeOrName != ''">
      <bind name="likeChannelOrgCodeOrName" value="'%' + dto.channelOrgCodeOrName + '%'"/>
      and (a.channel_org_code like #{likeChannelOrgCodeOrName} or a.channel_org_name like
      #{likeChannelOrgCodeOrName})
    </if>
    <if test="dto.underThisRuleCode != null and dto.underThisRuleCode != ''">
      <bind name="likeRightunderThisRuleCode" value="dto.underThisRuleCode + '%'"/>
      and a.rule_code like #{likeRightunderThisRuleCode}
    </if>
    <if test="dto.notUnderThisRuleCode != null and dto.notUnderThisRuleCode != ''">
      <bind name="likeRightnotUnderThisRuleCode" value="dto.notUnderThisRuleCode + '%'"/>
      and a.rule_code not like #{likeRightnotUnderThisRuleCode}
    </if>
    order by
    <if test="dto.selectedCodeList != null and dto.selectedCodeList.size > 0">
      CASE
      <foreach collection="dto.selectedCodeList" item="item" index="index">
        WHEN a.channel_org_code = #{item} THEN ${index}
      </foreach>
      ELSE 99 END asc,
    </if>
    a.create_time desc,a.channel_org_code asc
  </select>
  <select id="findLazyTreeList" resultType="com.biz.crm.business.common.sdk.vo.LazyTreeVo">
    select a1.id, a1.channel_org_code as code, a1.channel_org_name as name, a1.parent_code,
    case when a2.parent_code is null then 0
    else 1 end as hasChildFlag
    from (select id, channel_org_code, channel_org_name, parent_code from mdm_channel_org
    where 1=1
    AND del_flag = #{delFlag.code}
    AND tenant_code = #{tenantCode}
    <if test="parentCode != null and parentCode != '' ">
      and parent_code = #{parentCode}
    </if>
    <if test="enableStatus != null and enableStatus != '' ">
      and enable_status = #{enableStatus}
    </if>
    <if test="topOnly != null and topOnly == true">
      and (parent_code is null or parent_code = '')
    </if>
    <if test="excludeRuleCode != null and excludeRuleCode != '' ">
      <bind name="likeRightexcludeRuleCode" value="excludeRuleCode + '%'"/>
      and rule_code not like #{likeRightexcludeRuleCode}
    </if>
    <if test="codeList != null and codeList.size > 0">
      and channel_org_code in
      <foreach collection="codeList" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </if>
    <if test="ruleCodeList != null and ruleCodeList.size > 0">
      and rule_code in
      <foreach collection="ruleCodeList" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </if>
    ) a1
    left join (select distinct parent_code from mdm_channel_org where 1=1
    AND del_flag = #{delFlag.code}
    AND tenant_code = #{tenantCode}
    <if test="enableStatus != null and enableStatus != '' ">
      and enable_status = #{enableStatus}
    </if>
    <if test="excludeRuleCode != null and excludeRuleCode != '' ">
      <bind name="likeRightexcludeRuleCode" value="excludeRuleCode + '%'"/>
      and rule_code not like #{likeRightexcludeRuleCode}
    </if>
    ) a2 on a1.channel_org_code = a2.parent_code
    order by a1.channel_org_code asc
  </select>

  <select id="findCurAndChildrenByOrgCodeList"
          resultType="com.biz.crm.mdm.business.channel.org.local.entity.ChannelOrg">
    select a.*,b.channel_org_name as parent_name
    from mdm_channel_org a
    left join mdm_channel_org b on a.parent_code=b.channel_org_code AND b.tenant_code = #{tenantCode} AND b.del_flag = '${@<EMAIL>()}'
    WHERE 1=1
    AND a.del_flag = '${@<EMAIL>()}'
    AND a.tenant_code = #{tenantCode}
    <if test="enableStatus != null and enableStatus != ''">
      and a.enable_status=#{enableStatus}
    </if>
    <if test="orgCodes != null and orgCodes.size > 0">
      and
      <foreach collection="orgCodes" item="item" index="index" open="(" separator="or" close=")">
        <bind name="likeOrgCode" value="item + '%'"/>
        a.channel_org_code LIKE #{likeOrgCode}
      </foreach>
    </if>
    order by a.create_time desc
  </select>


</mapper>
