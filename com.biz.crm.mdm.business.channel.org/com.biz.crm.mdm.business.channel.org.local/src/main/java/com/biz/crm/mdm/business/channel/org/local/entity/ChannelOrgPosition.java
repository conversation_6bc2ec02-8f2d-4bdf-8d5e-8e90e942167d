package com.biz.crm.mdm.business.channel.org.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * 渠道组织职位关联实体类
 */
@Data
@ApiModel(value = "channelOrgPosition", description = "渠道组织职位关联实体类")
@TableName("mdm_channel_org_position")
@Entity
@Table(name = "mdm_channel_org_position", indexes = {
    @Index(name = "COP_INDEX1", columnList = "channel_org_code"),
    @Index(name = "COP_INDEX2", columnList = "position_code"),
})
@org.hibernate.annotations.Table(appliesTo = "mdm_channel_org_position", comment = "渠道组织职位关联表")
public class ChannelOrgPosition extends TenantEntity {
  
  private static final long serialVersionUID = -905545375934629666L;
  /**
   * 渠道组织编码
   */
  @ApiModelProperty("渠道组织编码")
  @TableField(value = "channel_org_code")
  @Column(name = "channel_org_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '渠道组织编码'")
  private String channelOrgCode;

  /**
   * 职位编码
   */
  @ApiModelProperty("职位编码")
  @TableField(value = "position_code")
  @Column(name = "position_code", nullable = false, length = 64, columnDefinition = "VARCHAR(64) COMMENT '职位编码'")
  private String positionCode;

}
