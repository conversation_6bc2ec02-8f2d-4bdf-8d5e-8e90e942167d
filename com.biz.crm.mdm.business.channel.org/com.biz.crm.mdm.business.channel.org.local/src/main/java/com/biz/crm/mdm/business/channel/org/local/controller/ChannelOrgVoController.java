package com.biz.crm.mdm.business.channel.org.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.channel.org.sdk.service.ChannelOrgVoService;
import com.biz.crm.mdm.business.channel.org.sdk.dto.ChannelOrgCreateDto;
import com.biz.crm.mdm.business.channel.org.sdk.dto.ChannelOrgPaginationDto;
import com.biz.crm.mdm.business.channel.org.sdk.dto.ChannelOrgUpdateDto;
import com.biz.crm.mdm.business.channel.org.sdk.vo.ChannelOrgVo;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 渠道组织控制器
 */
@Api(tags = "渠道组织：ChannelOrgVo：与渠道组织相关的内容")
@Slf4j
@RestController
@RequestMapping("/v1/channelOrg/channelOrg")
public class ChannelOrgVoController {
  @Autowired(required = false)
  private ChannelOrgVoService channelOrgVoService;

  /**
   * 多条件分页查询
   *
   * @return
   */
  @ApiOperation(value = "多条件分页查询(可适用于列表分页查询和公用分页弹框)", notes = "分页参数为page和size，page从0开始，size默认50;")
  @GetMapping("findByConditions")
  public Result<Page<ChannelOrgVo>> findByConditions(@PageableDefault(50) Pageable pageable,
                                                     @ApiParam(name = "channelOrgPaginationDto", value = "分页Dto") ChannelOrgPaginationDto channelOrgPaginationDto) {
    try {
      Page<ChannelOrgVo> result = this.channelOrgVoService.findByConditions(pageable, channelOrgPaginationDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 按照实体中的（id）主键进行查询明细查询，查询的明细包括当前业务表单所有的关联属性。
   *
   * @param id 主键
   */
  @ApiOperation(value = "按照实体中的（id）主键进行查询明细查询，查询的明细包括当前业务表单所有的关联属性。")
  @GetMapping("/findDetailsById")
  public Result<ChannelOrgVo> findDetailsById(@RequestParam("id") @ApiParam("主键") String id) {
    try {
      ChannelOrgVo result = this.channelOrgVoService.findDetailsById(id);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 相关的创建过程，http接口。请注意该创建过程除了可以创建模型中的基本信息以外，还可以对模型中属于OneToMany关联的明细信息一同进行创建注意：
   * 基于模型的创建操作传入的JSON对象，其主键信息不能有值，服务端将会自动为其赋予相关值。另外，创建操作成功后，系统将返回该对象的基本信息（不包括任何关联信息）
   */
  @ApiOperation(value = "相关的创建过程，http接口。请注意该创建过程除了可以创建模型中的基本信息以外，" +
      "还可以对模型中属于OneToMany关联的明细信息一同进行创建注意：基于模型的创建操作传入的JSON对象，" +
      "其主键信息不能有值，服务端将会自动为其赋予相关值。另外，创建操作成功后，系统将返回该对象的基本信息（不包括任何关联信息）")
  @PostMapping(value = "")
  public Result<ChannelOrgVo> create(
      @RequestBody @ApiParam(name = "channelOrgCreateDto",
          value = "相关的创建过程，http接口。请注意该创建过程除了可以创建模型中的基本信息以外，" +
              "还可以对模型中属于OneToMany关联的明细信息一同进行创建注意：基于模型的创建操作传入的JSON对象，" +
              "其主键信息不能有值，服务端将会自动为其赋予相关值。" +
              "另外，创建操作成功后，系统将返回该对象的基本信息（不包括任何关联信息）") ChannelOrgCreateDto channelOrgCreateDto) {
    try {
      ChannelOrgVo current = this.channelOrgVoService.create(channelOrgCreateDto);
      return Result.ok(current);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 相关的更新过程，http接口。请注意该更新过程只会更新在模型层被标记为了updateable的属性，
   * 包括一般属性、ManyToOne和OneToOne性质的关联属性，而ManyToMany、OneToMany的关联属性，
   * 虽然也会传入，但需要开发人员自行在Service层完善其更新过程注意：基于模型（的修改操作传入的SON对象，
   * 其主键信息必须有值，服务端将验证这个主键值是否已经存在。另外，创建操作成功后，系统将返回该对象的基本信息（不包括任何关联信息）
   */
  @ApiOperation(value = "相关的更新过程，http接口。请注意该更新过程只会更新在模型层被标记为了updateable的属性，" +
      "包括一般属性、ManyToOne和OneToOne性质的关联属性，而ManyToMany、OneToMany的关联属性，虽然也会传入，" +
      "但需要开发人员自行在Service层完善其更新过程注意：基于模型的修改操作传入的JSON对象，其主键信息必须有值，" +
      "服务端将验证这个主键值是否已经存在。另外，创建操作成功后，系统将返回该对象的基本信息（不包括任何关联信息）")
  @PatchMapping(value = "")
  public Result<ChannelOrgVo> update(
      @RequestBody @ApiParam(name = "channelOrgUpdateDto",
          value = "相关的更新过程，http接口。请注意该更新过程只会更新在模型层被标记为了updateable的属性，" +
              "包括一般属性、ManyToOne和OneToOne性质的关联属性，而ManyToMany、OneToMany的关联属性，" +
              "虽然也会传入，但需要开发人员自行在Service层完善其更新过程注意：基于模型的修改操作传入的JSON对象，" +
              "其主键信息必须有值，服务端将验证这个主键值是否已经存在。另外，创建操作成功后，" +
              "系统将返回该对象的基本信息（不包括任何关联信息）") ChannelOrgUpdateDto channelOrgUpdateDto) {
    try {
      ChannelOrgVo current = this.channelOrgVoService.update(channelOrgUpdateDto);
      return Result.ok(current);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 启用
   */
  @ApiOperation(value = "启用")
  @PatchMapping("/enable")
  public Result<?> enable(@RequestBody List<String> ids) {
    try {
      this.channelOrgVoService.enableBatch(ids);
      return Result.ok("启用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 禁用
   */
  @ApiOperation(value = "禁用")
  @PatchMapping("/disable")
  public Result<?> disable(@RequestBody List<String> ids) {
    try {
      this.channelOrgVoService.disableBatch(ids);
      return Result.ok("禁用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 删除
   */
  @ApiOperation(value = "删除")
  @DeleteMapping("/delete")
  public Result<?> delete(@RequestParam("ids") List<String> ids) {
    try {
      this.channelOrgVoService.deleteBatch(ids);
      return Result.ok("删除成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("重置降维编码")
  @PatchMapping("/updateRuleCode")
  public Result<?> updateRuleCode() {
    try {
      this.channelOrgVoService.updateRuleCode();
      return Result.ok("重置降维编码成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 获取所有的渠道组织
   */
  @ApiOperation(value = "获取所有的渠道组织")
  @GetMapping("/findAll")
  public Result<List<ChannelOrgVo>> findAll() {
    try {
      List<ChannelOrgVo> result = this.channelOrgVoService.findAll();
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  /**
   *
   * 组织编码查询自身及一下渠道组织
   * @param orgCodes
   * <AUTHOR>
   * @date
   */
  @ApiOperation(value = "通过组织编码查询自身及下级渠道组织", httpMethod = "POST")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "orgCodes", value = "组织编码集合", required = true, paramType = "body")
  })
  @PostMapping("/findCurAndChildrenByOrgCodeList")
  public Result<List<ChannelOrgVo>> findCurAndChildrenByOrgCodeList(@RequestBody List<String> orgCodes) {
    try {
      List<ChannelOrgVo> result = this.channelOrgVoService.findCurAndChildrenByOrgCodeList(orgCodes);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
