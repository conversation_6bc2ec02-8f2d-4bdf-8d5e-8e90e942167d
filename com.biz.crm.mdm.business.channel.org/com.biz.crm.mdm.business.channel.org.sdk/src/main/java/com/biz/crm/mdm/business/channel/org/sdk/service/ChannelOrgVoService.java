package com.biz.crm.mdm.business.channel.org.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.channel.org.sdk.dto.ChannelOrgCreateDto;
import com.biz.crm.mdm.business.channel.org.sdk.dto.ChannelOrgPaginationDto;
import com.biz.crm.mdm.business.channel.org.sdk.dto.ChannelOrgUpdateDto;
import com.biz.crm.mdm.business.channel.org.sdk.vo.ChannelOrgVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Created by Bao <PERSON>bin on 2021-10-27 17:51.
 */
public interface ChannelOrgVoService {
  /**
   * 多条件分页查询(可适用于列表分页查询和公用分页弹框)
   *
   * @param pageable
   * @param channelOrgPaginationDto
   * @return
   */
  Page<ChannelOrgVo> findByConditions(Pageable pageable, ChannelOrgPaginationDto channelOrgPaginationDto);

  /**
   * 按照实体中的（id）主键进行查询明细查询，查询的明细包括当前业务表单所有的关联属性。
   *
   * @param id
   * @return
   */
  ChannelOrgVo findDetailsById(String id);

  /**
   * 相关的创建过程，http接口。请注意该创建过程除了可以创建模型中的基本信息以外，还可以对模型中属于OneToMany关联的明细信息一同进行创建注意：
   * 基于模型的创建操作传入的JSON对象，其主键信息不能有值，服务端将会自动为其赋予相关值。另外，创建操作成功后，系统将返回该对象的基本信息（不包括任何关联信息）
   *
   * @param channelOrgCreateDto
   * @return
   */
  ChannelOrgVo create(ChannelOrgCreateDto channelOrgCreateDto);

  /**
   * 相关的更新过程，http接口。请注意该更新过程只会更新在模型层被标记为了updateable的属性，
   * 包括一般属性、ManyToOne和OneToOne性质的关联属性，而ManyToMany、OneToMany的关联属性，
   * 虽然也会传入，但需要开发人员自行在Service层完善其更新过程注意：基于模型（的修改操作传入的SON对象，
   * 其主键信息必须有值，服务端将验证这个主键值是否已经存在。另外，创建操作成功后，系统将返回该对象的基本信息（不包括任何关联信息）
   *
   * @param channelOrgUpdateDto
   * @return
   */
  ChannelOrgVo update(ChannelOrgUpdateDto channelOrgUpdateDto);

  /**
   * 启用
   *
   * @param ids
   */
  void enableBatch(List<String> ids);

  /**
   * 禁用
   *
   * @param ids
   */
  void disableBatch(List<String> ids);

  /**
   * 删除
   *
   * @param ids
   */
  void deleteBatch(List<String> ids);

  /**
   * 重置降维编码
   */
  void updateRuleCode();

  /**
   * 获取所有的产品层级
   *
   * @return
   */
  List<ChannelOrgVo> findAll();


  /**
   *
   * 组织编码查询自身及一下渠道组织
   * @param orgCodes
   * <AUTHOR>
   * @date
   */
  List<ChannelOrgVo> findCurAndChildrenByOrgCodeList(List<String> orgCodes);

  /**
   * 根据渠道组织编码查询
   * @param channelOrgCodes
   * @return
   */
  List<ChannelOrgVo> findByChannelOrgCodes(List<String> channelOrgCodes);
}
