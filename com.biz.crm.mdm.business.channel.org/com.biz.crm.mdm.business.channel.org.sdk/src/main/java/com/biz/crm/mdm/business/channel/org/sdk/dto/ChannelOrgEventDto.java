package com.biz.crm.mdm.business.channel.org.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import com.bizunited.nebula.event.sdk.service.NebulaEventDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 渠道组织事件dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ChannelOrgEventDto", description = "渠道组织事件dto")
public class ChannelOrgEventDto extends TenantDto implements NebulaEventDto {

  @ApiModelProperty(value = "渠道组织编码")
  private String channelOrgCode;

  @ApiModelProperty(value = "渠道组织名称")
  private String channelOrgName;

  @ApiModelProperty(value = "渠道组织层级")
  private String channelOrgLevel;

  @ApiModelProperty(value = "渠道组织类型")
  private String channelOrgType;

  @ApiModelProperty(value = "渠道组织描述")
  private String channelOrgDesc;

  @ApiModelProperty("上级编码")
  private String parentCode;

  @ApiModelProperty(value = "上级组织名称")
  private String parentName;

  @ApiModelProperty(value = "启用状态")
  private String enableStatus;

}
