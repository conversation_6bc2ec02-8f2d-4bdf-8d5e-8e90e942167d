package com.biz.crm.mdm.business.channel.org.sdk.event;

import com.biz.crm.mdm.business.channel.org.sdk.vo.ChannelOrgVo;

import java.util.List;

/**
 * 渠道组织事件监听
 */
public interface ChannelOrgEventListener {
  /**
   * 当新增渠道组织时通知监听
   *
   * @param channelOrgVos
   */
  void onBatchCreate(List<ChannelOrgVo> channelOrgVos);

  /**
   * 当更新渠道组织时通知监听
   *
   * @param channelOrgVos
   */
  void onBatchUpdate(List<ChannelOrgVo> channelOrgVos);

  /**
   * 当删除渠道组织时通知监听
   *
   * @param channelOrgVos
   */
  void onBatchDelete(List<ChannelOrgVo> channelOrgVos);

  /**
   * 当启用渠道组织时通知监听
   *
   * @param channelOrgVos
   */
  void onBatchEnable(List<ChannelOrgVo> channelOrgVos);


  /**
   * 当禁用渠道组织时通知监听
   *
   * @param eventDtos
   */
  void onBatchDisable(List<ChannelOrgVo> eventDtos);

}
