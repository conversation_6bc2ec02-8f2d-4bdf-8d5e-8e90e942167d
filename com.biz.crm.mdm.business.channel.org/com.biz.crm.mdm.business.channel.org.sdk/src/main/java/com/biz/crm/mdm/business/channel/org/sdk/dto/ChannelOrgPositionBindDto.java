package com.biz.crm.mdm.business.channel.org.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 渠道组织和职位关联dto
 */
@Data
@ApiModel("渠道组织和职位关联dto")
public class ChannelOrgPositionBindDto {

  @ApiModelProperty("职位编码集合")
  private List<String> positionCodeList;

  @ApiModelProperty("渠道组织编码")
  private String channelOrgCode;

  @ApiModelProperty("旧渠道组织编码")
  private String oldChannelOrgCode;
}
