package com.biz.crm.mdm.business.channel.org.sdk.event;

import com.biz.crm.mdm.business.channel.org.sdk.dto.ChannelOrgEventDto;
import com.biz.crm.mdm.business.channel.org.sdk.dto.ChannelOrgEventBatchDto;
import com.biz.crm.mdm.business.channel.org.sdk.dto.ChannelOrgEventUpdateDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * 渠道组织关系事件监听器
 */
public interface ChannelOrgNebulaEventListener extends NebulaEvent {

  /**
   * 当新增渠道与组织关系时触发更新事件
   *
   * @param channelOrgEventDto
   */
  void onCreate(ChannelOrgEventDto channelOrgEventDto);

  /**
   * 当删除一批组织时触发删除事件
   *
   * @param channelOrgEventBatchDto
   */
  void onDeleteBatch(ChannelOrgEventBatchDto channelOrgEventBatchDto);

  /**
   * 当启用一批组织时触发批量启用事件
   *
   * @param channelOrgEventBatchDto
   */
  void onEnableBatch(ChannelOrgEventBatchDto channelOrgEventBatchDto);

  /**
   * 当禁用一批组织时触发批量禁用事件
   *
   * @param channelOrgEventBatchDto
   */
  void onDisableBatch(ChannelOrgEventBatchDto channelOrgEventBatchDto);

  /**
   * 当修改组织时触发更新事件
   *
   * @param channelOrgEventUpdateDto
   */
  void onUpdate(ChannelOrgEventUpdateDto channelOrgEventUpdateDto);
}
