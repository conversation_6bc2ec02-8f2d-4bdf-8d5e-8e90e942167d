package com.biz.crm.mdm.business.channel.org.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 渠道组织更新dto
 */
@Data
@ApiModel("渠道组织更新dto")
public class ChannelOrgUpdateDto {
  @ApiModelProperty(name = "id", value = "id")
  private String id;

  @ApiModelProperty(name = "enableStatus", value = "数据业务状态（启用状态）")
  private String enableStatus;

  @ApiModelProperty(value = "渠道组织编码")
  private String channelOrgCode;

  @ApiModelProperty(value = "渠道组织名称")
  private String channelOrgName;

  @ApiModelProperty(value = "渠道组织层级")
  private String channelOrgLevel;

  @ApiModelProperty(value = "渠道组织类型")
  private String channelOrgType;

  @ApiModelProperty(value = "渠道组织描述")
  private String channelOrgDesc;

  @ApiModelProperty("上级编码")
  private String parentCode;

}
