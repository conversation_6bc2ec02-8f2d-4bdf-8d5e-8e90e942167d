package com.biz.crm.mdm.business.channel.org.sdk.dto;

import com.bizunited.nebula.event.sdk.service.NebulaEventDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 渠道组织修改事件Dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ChannelOrgEventUpdateDto", description = "渠道组织批量事件Dto")
public class ChannelOrgEventUpdateDto implements NebulaEventDto {

  @ApiModelProperty(value = "（旧）渠道组织")
  private ChannelOrgEventDto oldChannelOrgEventDto;

  @ApiModelProperty(value = "（新）渠道组织")
  private ChannelOrgEventDto newChannelOrgEventDto;
}
