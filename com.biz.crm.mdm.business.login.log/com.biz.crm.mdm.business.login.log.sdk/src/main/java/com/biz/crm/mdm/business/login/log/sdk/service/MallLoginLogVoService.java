package com.biz.crm.mdm.business.login.log.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.login.log.sdk.dto.LoginLogDto;
import com.biz.crm.mdm.business.login.log.sdk.vo.MallLoginLogVo;
import org.springframework.data.domain.Pageable;

/**
 * 登录日志返回vo服务
 * <AUTHOR>
 */
public interface MallLoginLogVoService {

  /**
   * 分页条件查询
   *
   * @param pageable
   * @param loginLogDto
   * @return
   */
  Page<MallLoginLogVo> findByConditions(
          Pageable pageable, MallLoginLogVo mallLoginLogVo);


  void mallLoginRecord(LoginLogDto loginLog);
}
