package com.biz.crm.mdm.business.login.log.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.login.log.local.entity.LoginLog;
import com.biz.crm.mdm.business.login.log.local.mapper.LoginLogMapper;
import com.biz.crm.mdm.business.login.log.sdk.dto.LoginLogDto;
import com.biz.crm.mdm.business.login.log.sdk.vo.LoginLogVo;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

/**
 * 登录日志数据库访问类
 * <AUTHOR>
 **/
@Component
public class LoginLogRepository extends ServiceImpl<LoginLogMapper, LoginLog> {
  /**
   * 分页条件查询
   *
   * @param pageable
   * @param loginLogDto
   * @return
   */
  public Page<LoginLogVo> findByConditions(Pageable pageable, LoginLogDto loginLogDto) {
    Page<LoginLogVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return this.baseMapper.findByConditions(page, loginLogDto);
  }

  /**
   *
   * @param id
   * @return
   */
  public LoginLog findByIdAndTenantCode(String id,String tenantCode) {
    return this.lambdaQuery()
        .eq(LoginLog::getTenantCode,tenantCode)
        .in(LoginLog::getId,id)
        .one();
  }
  public LoginLog findByAccount(String account,String tenantCode) {
    return this.baseMapper.findByAccount(account,tenantCode);
  }
}
