package com.biz.crm.mdm.business.login.log.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.login.log.sdk.service.MallLoginLogVoService;
import com.biz.crm.mdm.business.login.log.sdk.vo.MallLoginLogVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: yangrui
 * @Date: 2025-01-16 22:08
 */
@Api(tags = "商城登录日志：MallLoginLogVo")
@Slf4j
@RestController
@RequestMapping("/v1/mallLoginLog/mallLoginLog")
public class MallLoginVoController {

    @Autowired(required = false)
    private MallLoginLogVoService mallLoginLogVoService;

    @ApiOperation(value = "多条件分页查询", notes = "分页参数为page和size，page从0开始，size默认50;")
    @GetMapping("findByConditions")
    public Result<Page<MallLoginLogVo>> findByConditions(@PageableDefault(50) Pageable pageable,
                                                         @ApiParam(name = "mallLoginLogVo", value = "分页Dto") MallLoginLogVo mallLoginLogVo) {
        try {
            Page<MallLoginLogVo> result = this.mallLoginLogVoService.findByConditions(pageable, mallLoginLogVo);
            return Result.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
