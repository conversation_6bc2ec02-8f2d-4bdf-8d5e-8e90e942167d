package com.biz.crm.mdm.business.login.log.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.util.Date;

/**
 * 登录日志实体类
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "LoginLog", description = "登录日志实体类")
@TableName("mdm_login_log")
@Entity
@Table(name = "mdm_login_log", indexes = {
    @Index(name = "idx_app_type", columnList = "app_type"),
    @Index(name = "idx_login_type", columnList = "login_type"),
    @Index(name = "idx_usertype", columnList = "usertype"),
    @Index(name = "idx_account", columnList = "account"),
})
@org.hibernate.annotations.Table(appliesTo = "mdm_login_log", comment = "登录日志表")
public class LoginLog extends TenantEntity {
  private static final long serialVersionUID = 7692944988932128598L;

  /**
   * 登录时间
   */
  @ApiModelProperty("登录时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "login_time", nullable = false, length = 20, columnDefinition = "datetime NOT NULL COMMENT '登录时间'")
  @TableField(value = "login_time")
  private Date loginTime;

  /**
   * 业务系统类型
   */
  @ApiModelProperty("业务系统类型")
  @TableField(value = "app_type")
  @Column(name = "app_type", nullable = false, length = 11, columnDefinition = "int(11) NOT NULL COMMENT '业务系统类型'")
  private Integer appType;

  /**
   * 登录方式
   */
  @ApiModelProperty("登录方式")
  @TableField(value = "login_type")
  @Column(name = "login_type", nullable = false, length = 11, columnDefinition = "int(11) NOT NULL COMMENT '登录方式'")
  private Integer loginType;

  /**
   * 用户类别(u-企业用户，c-客户用户，terminal-终端用户，customer - 经销商用户)
   */
  @ApiModelProperty("用户类别(u-企业用户，c-客户用户，terminal-终端用户，customer - 经销商用户)")
  @TableField(value = "usertype")
  @Column(name = "usertype", nullable = false, length = 32, columnDefinition = "VARCHAR(32) NOT NULL COMMENT '用户类别(u-企业用户，c-客户用户，terminal-终端用户，customer - 经销商用户)'")
  private String usertype;

  /**
   * 登录账号
   */
  @ApiModelProperty("登录账号")
  @TableField(value = "account")
  @Column(name = "account", nullable = false, length = 64, columnDefinition = "VARCHAR(64) NOT NULL COMMENT '登录账号'")
  private String account;

  /**
   * 用户名称
   */
  @ApiModelProperty("用户名称")
  @TableField(value = "full_name")
  @Column(name = "full_name", nullable = false, length = 64, columnDefinition = "VARCHAR(64) NOT NULL COMMENT '用户名称'")
  private String fullName;
}
