package com.biz.crm.mdm.business.login.log.local.service.internal;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.LoginFromTypeEnum;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerDockingVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.customer.user.sdk.service.CustomerUserVoService;
import com.biz.crm.mdm.business.customer.user.sdk.vo.CustomerUserRelaCustomerVo;
import com.biz.crm.mdm.business.customer.user.sdk.vo.CustomerUserVo;
import com.biz.crm.mdm.business.login.log.local.entity.MallLoginLog;
import com.biz.crm.mdm.business.login.log.local.repository.MallLoginLogRepository;
import com.biz.crm.mdm.business.login.log.sdk.dto.LoginLogDto;
import com.biz.crm.mdm.business.login.log.sdk.service.MallLoginLogVoService;
import com.biz.crm.mdm.business.login.log.sdk.vo.MallLoginLogVo;
import com.biz.crm.mdm.business.org.sdk.common.enums.OrgTypeEnum;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 登录日志服务实现
 * <AUTHOR>
 **/
@Service
@Slf4j
public class MallLoginLogVoServiceImpl implements MallLoginLogVoService {

  @Autowired(required = false)
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;

  @Autowired(required = false)
  private MallLoginLogRepository mallLoginLogRepository;

  @Autowired(required = false)
  private CustomerUserVoService customerUserVoService;

  @Autowired(required = false)
  private OrgVoService orgVoService;

  @Autowired(required = false)
  private CustomerVoService customerVoService;

  public void mallLoginRecord(LoginLogDto loginLog) {
    Integer fromType = loginLog.getAppType();
    if (Objects.isNull(fromType)) {
      return;
    }
    try {
      // 只记录dms-web商城和dms小程序登录
      boolean mallLogin = Objects.equals(LoginFromTypeEnum.DMS_WEB.getAppType(), fromType)
              || Objects.equals(LoginFromTypeEnum.DMS_MINI.getAppType(), fromType);
      if (!mallLogin) {
        return;
      }
      MallLoginLog mallLoginLog = new MallLoginLog();
      mallLoginLog.setAccount(loginLog.getAccount());
      mallLoginLog.setLoginTime(new Date());
      mallLoginLog.setLoginFromType(fromType.toString());
      mallLoginLog.setTenantCode(TenantUtils.getTenantCode());

      // 通过account找到客户用户
      CustomerUserVo customerUserVo = customerUserVoService.findByUserName(mallLoginLog.getAccount());
      mallLoginLog.setFullName(customerUserVo.getFullName());
      String userCode = customerUserVo.getUserCode();
      // 关联的客户
      List<CustomerUserRelaCustomerVo> customerVoList = customerUserVo.getCustomerInfoList();
      if (CollectionUtil.isNotEmpty(customerVoList)) {
        CustomerUserRelaCustomerVo customerUserRelaCustomerVo = customerVoList.get(0);
        String customerCode = customerUserRelaCustomerVo.getCustomerCode();
        CustomerVo customerVo = customerVoService.findDetailsByIdOrCode(null, customerCode);
        mallLoginLog.setCustomerCode(customerCode);
        mallLoginLog.setCustomerName(customerVo.getCustomerName());
        // 客户关联的组织
        String orgCode = customerVo.getOrgCode();
        String orgName = null;
        // 找到一级部门
        OrgVo org = orgVoService.findByOrgCode(orgCode);
        String salesDepartmentOrgCode = null;
        String salesDepartmentOrgName = null;
        if (Objects.nonNull(org)) {
          orgName = org.getOrgName();
          if (StrUtil.equals(OrgTypeEnum.DIVISION.getKey(), org.getOrgType())) {
            salesDepartmentOrgCode  = org.getOrgCode();
            salesDepartmentOrgName = org.getOrgName();
          }
        }
        if (StrUtil.isBlank(salesDepartmentOrgCode)) {
          List<OrgVo> orgVos = orgVoService.findAllParentByOrgCode(orgCode);
          // 找到大区那一层
          OrgVo division = CollUtil.isNotEmpty(orgVos) ?
                  orgVos.stream().filter(o ->
                          StrUtil.equals(OrgTypeEnum.DIVISION.getKey(), o.getOrgType()))
                          .findAny().orElse(null)
                  : null;
          if (Objects.nonNull(division)) {
            salesDepartmentOrgCode = division.getOrgCode();
            salesDepartmentOrgName = division.getOrgName();
          }
        }
        mallLoginLog.setOrgCode(orgCode);
        mallLoginLog.setOrgName(orgName);
        mallLoginLog.setSalesDepartmentCode(salesDepartmentOrgCode);
        mallLoginLog.setSalesDepartmentName(salesDepartmentOrgName);

        // 关联的业务员
        if (CollectionUtil.isNotEmpty(customerVo.getDockingList())) {
          CustomerDockingVo customerDockingVo =  customerVo.getDockingList().get(0);
          mallLoginLog.setBusinessUserName(customerDockingVo.getUserName());
          mallLoginLog.setBusinessUserFullName(customerDockingVo.getFullName());
        }
      }
      mallLoginLog.setCustomerUserCode(userCode);
      mallLoginLogRepository.save(mallLoginLog);
    } catch (Exception e) {
      log.error("LoginLogVoServiceImpl.mallLoginRecord error {}",
              e.getMessage(), e);
    }
  }

  public Page<MallLoginLogVo> findByConditions(Pageable pageable, MallLoginLogVo param) {
    Page<MallLoginLog> page = mallLoginLogRepository.findByConditions(pageable, param);
    if (Objects.isNull(page) || CollUtil.isEmpty(page.getRecords())) {
      return new Page<>();
    }
    List<MallLoginLogVo> voList = (List<MallLoginLogVo>) nebulaToolkitService.copyCollectionByWhiteList(
            page.getRecords(), MallLoginLog.class, MallLoginLogVo.class, HashSet.class, ArrayList.class);
    Page<MallLoginLogVo> result = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
    result.setRecords(voList);
    return result;
  }


}
