package com.biz.crm.mdm.business.login.log.feign.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.login.log.feign.feign.LoginLogVoServiceFeign;
import com.biz.crm.mdm.business.login.log.sdk.dto.LoginLogDto;
import com.biz.crm.mdm.business.login.log.sdk.service.LoginLogVoService;
import com.biz.crm.mdm.business.login.log.sdk.vo.LoginLogVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

/**
 * 登录日志sdk实现
 *
 * <AUTHOR>
 */
@Service
public class LoginLogVoServiceImpl implements LoginLogVoService {

  @Autowired(required = false) private LoginLogVoServiceFeign loginLogVoServiceFeign;

  @Override
  public Page<LoginLogVo> findByConditions(Pageable pageable, LoginLogDto loginLogDto) {
    throw new UnsupportedOperationException();
  }

  @Override
  public LoginLogVo findById(String id) {
    return this.loginLogVoServiceFeign.findById(id).checkFeignResult();
  }

  @Override
  public void create(LoginLogDto loginLogDto) {
    this.loginLogVoServiceFeign.create(loginLogDto);
  }

  @Override
  public LoginLogVo findByAccount(String account) {
    return this.loginLogVoServiceFeign.findByAccount(account).checkFeignResult();
  }
}
