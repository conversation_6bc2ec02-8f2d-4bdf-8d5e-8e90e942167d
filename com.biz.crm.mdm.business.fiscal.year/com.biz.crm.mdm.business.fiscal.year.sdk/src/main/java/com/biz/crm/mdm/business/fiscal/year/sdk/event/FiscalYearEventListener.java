package com.biz.crm.mdm.business.fiscal.year.sdk.event;


import com.biz.crm.mdm.business.fiscal.year.sdk.vo.FiscalYearEventVo;

import java.util.List;

/**
 * 财年变更事件通知接口
 *
 * <AUTHOR>
 * @date 2021/10/27
 */
public interface FiscalYearEventListener {

  /**
   * 当财年创建时触发
   *
   * @param vo 创建时的vo
   */
  void onCreate(FiscalYearEventVo vo);

  /**
   * 当财年修改时触发
   *
   * @param oldVo 修改之前的vo
   * @param newVo 修改之后的vo
   */
  void onUpdate(FiscalYearEventVo oldVo, FiscalYearEventVo newVo);

  /**
   * 当财年禁用时触发
   *
   * @param voList 禁用vo信息
   */
  void onDisable(List<FiscalYearEventVo> voList);

  /**
   * 当财年启用时触发
   *
   * @param voList 禁用vo信息
   */
  void onEnable(List<FiscalYearEventVo> voList);

  /**
   * 当财年删除时触发
   *
   * @param voList 删除vo信息
   */
  void onDelete(List<FiscalYearEventVo> voList);

}
