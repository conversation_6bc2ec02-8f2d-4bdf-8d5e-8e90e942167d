package com.biz.crm.mdm.business.fiscal.year.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 财年分页查询Dto
 *
 * <AUTHOR>
 * @date 2021/11/16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "FiscalYearPageDto", description = "财年分页查询Dto")
public class FiscalYearPageDto extends TenantDto {

  /**
   * 预算年(字典编码)
   */
  @ApiModelProperty("预算年(字典编码)")
  private String year;
  /**
   * 预算季度(字典编码)
   */
  @ApiModelProperty("预算季度(字典编码)")
  private String quarter;
  /**
   * 预算月度(字典编码)
   */
  @ApiModelProperty("预算月度(字典编码)")
  private String month;
  /**
   * 启用状态
   */
  @ApiModelProperty("启用状态")
  private String enableStatus;
  /**
   * 开始时间
   */
  @ApiModelProperty("开始时间(yyyy-MM-dd HH:mm:ss),如果只选日期后面的时分秒传(00:00:00)")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;
  /**
   * 结束时间
   */
  @ApiModelProperty("结束时间(yyyy-MM-dd HH:mm:ss),如果只选日期后面的时分秒传(23:59:59)")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;
  /**
   * 删除状态
   */
  @ApiModelProperty("删除状态")
  private String delFlag;
}
