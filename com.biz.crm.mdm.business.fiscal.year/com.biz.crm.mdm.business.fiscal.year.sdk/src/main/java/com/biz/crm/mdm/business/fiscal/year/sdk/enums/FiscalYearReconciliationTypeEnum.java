package com.biz.crm.mdm.business.fiscal.year.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * @Description 对账规则财年枚举
 * <AUTHOR>
 * @Date Created in 2022/4/15 下午3:30
 */
@Getter
@AllArgsConstructor
public enum FiscalYearReconciliationTypeEnum {
  /**
   * 年度
   */
  YEARLY("FISCAL_YEAR", "FISCAL_YEAR", "年度", "1"),

  /**
   * 季度
   */
  QUARTER("FISCAL_QUARTER", "FISCAL_QUARTER", "季度", "2"),

  /**
   * 月度
   */
  MONTHLY("FISCAL_MONTH", "FISCAL_MONTH", "月度", "3"),;

  private String key;
  /**
   * 字典编码
   */
  private String dictCode;
  /**
   * 字典值
   */
  private String value;
  /**
   * 字典排序
   */
  private String order;

  /**
   * 通过key获取 枚举值
   *
   * @param dictCode 字典值
   * @return 枚举值
   */
  public static FiscalYearReconciliationTypeEnum getByDictCode(String dictCode) {
    return Arrays.stream(FiscalYearReconciliationTypeEnum.values()).filter(item -> Objects.equals(item.getDictCode(), dictCode)).findFirst().orElse(null);
  }
}
