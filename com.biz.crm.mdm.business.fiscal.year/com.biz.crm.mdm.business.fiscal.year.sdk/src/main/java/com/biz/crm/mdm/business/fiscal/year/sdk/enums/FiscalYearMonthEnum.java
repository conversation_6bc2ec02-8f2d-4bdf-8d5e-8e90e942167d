package com.biz.crm.mdm.business.fiscal.year.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * @Description 财年月份
 * <AUTHOR>
 * @Date Created in 2022/3/1 上午9:50
 */
@Getter
@AllArgsConstructor
public enum FiscalYearMonthEnum {
  /**
   * 财年月份
   */
  JAN("JAN","1","一月","1"),
  FEB("FEB","2","二月","2"),
  MAR("MAR","3","三月","3"),
  APR("APR","4","四月","4"),
  MAY("MAY","5","五月","5"),
  JUN("JUN","6","六月","6"),
  JUL("JUL","7","七月","7"),
  AUG("AUG","8","八月","8"),
  SEPT("SEPT","9","九月","9"),
  OCT("OCT","10","十月","10"),
  NOV("NOV","11","十一月","11"),
  DEC("DEC","12","十二月","12"),
  ;

  private String key;
  /**
   * 字典编码
   */
  private String dictCode;
  /**
   * 字典值
   */
  private String value;
  /**
   * 字典排序
   */
  private String order;

  /**
   * 通过key获取 FiscalYearMonthEnum
   * @param key
   * @return
   */
  public static FiscalYearMonthEnum getByKey(String key) {
    return Arrays.stream(FiscalYearMonthEnum.values()).filter(item -> Objects.equals(item.getKey(), key))
        .findFirst().orElse(null);
  }

  /**
   * 通过dictCode获取 FiscalYearMonthEnum
   * @param key
   * @return
   */
  public static FiscalYearMonthEnum getByDictCode(String dictCode) {
    return Arrays.stream(FiscalYearMonthEnum.values()).filter(item -> Objects.equals(item.getDictCode(), dictCode))
        .findFirst().orElse(null);
  }
}
