package com.biz.crm.mdm.business.fiscal.year.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * @Project crm
 * @PackageName com.biz.crm.mdm.business.fiscal.year.sdk.enums
 * @ClassName FiscalYearQuarterEnum
 * <AUTHOR>
 * @Date 2022/3/1 上午9:42
 * @Description 财年季度枚举
 */
@Getter
@AllArgsConstructor
public enum FiscalYearQuarterEnum {
  /**
   * 财年季度
   */
  Q1TH("Q1TH","1","第一季度","1"),
  Q2ND("Q2ND","2","第二季度","2"),
  Q3RD("Q3RD","3","第三季度","3"),
  Q4TH("Q4TH","4","第四季度","4"),
  ;

  private String key;
  /**
   * 字典编码
   */
  private String dictCode;
  /**
   * 字典值
   */
  private String value;
  /**
   * 字典排序
   */
  private String order;

  /**
   * 通过key获取 FiscalYearQuarterEnum
   * @param key
   * @return
   */
  public static FiscalYearQuarterEnum getByKey(String key) {
    return Arrays.stream(FiscalYearQuarterEnum.values()).filter(item -> Objects.equals(item.getKey(), key))
        .findFirst().orElse(null);
  }

  /**
   * 通过dictCode获取 FiscalYearQuarterEnum
   * @param key
   * @return
   */
  public static FiscalYearQuarterEnum getByDictCode(String dictCode) {
    return Arrays.stream(FiscalYearQuarterEnum.values()).filter(item -> Objects.equals(item.getDictCode(), dictCode))
        .findFirst().orElse(null);
  }
}