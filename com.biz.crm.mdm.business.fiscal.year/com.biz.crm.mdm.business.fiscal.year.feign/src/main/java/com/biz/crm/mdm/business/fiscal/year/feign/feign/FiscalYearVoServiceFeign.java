package com.biz.crm.mdm.business.fiscal.year.feign.feign;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.fiscal.year.feign.feign.internal.FiscalYearVoServiceFeignImpl;
import com.biz.crm.mdm.business.fiscal.year.sdk.vo.FiscalYearRebateVo;
import com.biz.crm.mdm.business.fiscal.year.sdk.vo.FiscalYearVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;

/**
 * @description: 企业财年
 * @author: rentao
 * @date: 2022/4/11 21:19
 */
@FeignClient(  name = "${mdm.feign-client.name:crm-mdm}", path = "crm-mdm", fallbackFactory = FiscalYearVoServiceFeignImpl.class)
public interface FiscalYearVoServiceFeign {

  /**
   * 根据时间查询财年信息
   *
   * @param beginTime
   * @param endTime
   * @return 财年信息
   */

  @GetMapping("/v1/fiscalYear/fiscalYear/findByBeginTimeAndEndTime")
  Result<List<FiscalYearVo>> findByBeginTimeAndEndTime(@RequestParam("beginTime") Date beginTime, @RequestParam("endTime") Date endTime);

  /**
   * 查询当前租户下配置的财年信息
   * @return
   */
  @GetMapping("/v1/fiscalYear/fiscalYear/findFiscalYearRebate")
  Result<List<FiscalYearRebateVo>> findFiscalYearRebate();

}
