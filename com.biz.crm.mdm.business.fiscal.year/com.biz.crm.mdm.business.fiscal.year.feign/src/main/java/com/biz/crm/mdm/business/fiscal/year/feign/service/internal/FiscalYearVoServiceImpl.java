package com.biz.crm.mdm.business.fiscal.year.feign.service.internal;


import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.fiscal.year.feign.feign.FiscalYearVoServiceFeign;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearLinkageDto;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearReconciliationDto;
import com.biz.crm.mdm.business.fiscal.year.sdk.service.FiscalYearVoService;
import com.biz.crm.mdm.business.fiscal.year.sdk.vo.FiscalYearRebateVo;
import com.biz.crm.mdm.business.fiscal.year.sdk.vo.FiscalYearVo;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @description: 企业财年相关
 * @author: rentao
 * @date: 2022/4/11 15:30
 */
@Service
public class FiscalYearVoServiceImpl implements FiscalYearVoService {

  @Autowired(required = false)
  private FiscalYearVoServiceFeign fiscalYearVoServiceFeign;

  @Override
  public List<FiscalYearVo> findByFiscalYearLinkageDto(FiscalYearLinkageDto dto) {
    throw new UnsupportedOperationException();
  }

  @Override
  public List<FiscalYearVo> findByFiscalYearReconciliationDto(FiscalYearReconciliationDto dto) {
    return Lists.newArrayList();
  }

  @Override
  public List<FiscalYearVo> findByBeginTimeAndEndTime(Date beginTime, Date endTime) {
    Result<List<FiscalYearVo>> fiscalYearVoListByDate = this.fiscalYearVoServiceFeign
        .findByBeginTimeAndEndTime(beginTime,endTime);
    return fiscalYearVoListByDate.checkFeignResult();
  }

  /**
   * 查询当前租户下配置的财年信息
   *
   * @return
   */
  @Override
  public List<FiscalYearRebateVo> findFiscalYearRebate() {
    Result<List<FiscalYearRebateVo>> fiscalYearRebate = this.fiscalYearVoServiceFeign.findFiscalYearRebate();
    return fiscalYearRebate.getResult();
  }
}
