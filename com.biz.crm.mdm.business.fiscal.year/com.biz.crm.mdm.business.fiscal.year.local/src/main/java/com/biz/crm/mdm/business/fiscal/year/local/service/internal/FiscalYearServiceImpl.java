package com.biz.crm.mdm.business.fiscal.year.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.fiscal.year.local.entity.FiscalYearDetailEntity;
import com.biz.crm.mdm.business.fiscal.year.local.entity.FiscalYearEntity;
import com.biz.crm.mdm.business.fiscal.year.local.repository.FiscalYearDetailRepository;
import com.biz.crm.mdm.business.fiscal.year.local.repository.FiscalYearRepository;
import com.biz.crm.mdm.business.fiscal.year.local.service.FiscalYearDetailService;
import com.biz.crm.mdm.business.fiscal.year.local.service.FiscalYearService;
import com.biz.crm.mdm.business.fiscal.year.sdk.constant.FiscalYearConstant;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearDetailDto;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearDto;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearLinkageDto;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearPageDto;
import com.biz.crm.mdm.business.fiscal.year.sdk.enums.FiscalHalfYearEnum;
import com.biz.crm.mdm.business.fiscal.year.sdk.enums.FiscalYearMonthEnum;
import com.biz.crm.mdm.business.fiscal.year.sdk.enums.FiscalYearQuarterEnum;
import com.biz.crm.mdm.business.fiscal.year.sdk.enums.FiscalYearTypeEnum;
import com.biz.crm.mdm.business.fiscal.year.sdk.event.FiscalYearEventListener;
import com.biz.crm.mdm.business.fiscal.year.sdk.vo.FiscalYearEventVo;
import com.biz.crm.mdm.business.fiscal.year.sdk.vo.FiscalYearVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 财年表服务实现类
 *
 * <AUTHOR>
 * @date 2021-11-16 14:08:29
 */
@Slf4j
@Service("fiscalYearService")
public class FiscalYearServiceImpl implements FiscalYearService {

  @Autowired(required = false)
  private FiscalYearRepository fiscalYearRepository;

  @Autowired(required = false)
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;

  @Autowired(required = false)
  private List<FiscalYearEventListener> listeners;

  @Autowired(required = false)
  private FiscalYearDetailService fiscalYearDetailService;

  @Autowired(required = false)
  private FiscalYearDetailRepository fiscalYearDetailRepository;

  @Override
  @Transactional
  public FiscalYearEntity create(FiscalYearDto dto) {
    this.createValidation(dto);
    FiscalYearEntity entity = this.nebulaToolkitService.copyObjectByWhiteList(dto, FiscalYearEntity.class, HashSet.class, ArrayList.class);
    entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    //新增租户编号
    entity.setTenantCode(TenantUtils.getTenantCode());
    this.fiscalYearRepository.save(entity);
    //保存财年明细
    this.fiscalYearDetailService.batchCreate(dto.getFiscalYearDetailDtos(), dto.getYear(), dto.getTenantCode());
    //新增财年事件通知
    if (!CollectionUtils.isEmpty(listeners)) {
      FiscalYearEventVo vo = this.nebulaToolkitService.copyObjectByWhiteList(entity, FiscalYearEventVo.class, HashSet.class, ArrayList.class);
      listeners.forEach(listener -> {
        listener.onCreate(vo);
      });
    }
    return entity;
  }

  @Override
  @Transactional
  public FiscalYearEntity update(FiscalYearDto dto) {
    this.updateValidation(dto);
    //重构查询方法
    FiscalYearEntity entity = this.fiscalYearRepository.findByIdAndTenantCode(dto.getId(),TenantUtils.getTenantCode());
    Validate.notNull(entity, "财年不存在");
    FiscalYearEntity updateEntity = this.nebulaToolkitService.copyObjectByWhiteList(dto, FiscalYearEntity.class, HashSet.class, ArrayList.class);
    this.fiscalYearRepository.updateById(updateEntity);
    //更新财年明细
    this.fiscalYearDetailService.batchUpdate(dto.getFiscalYearDetailDtos(), dto.getYear(), dto.getTenantCode());
    //更新财年事件通知
    if (!CollectionUtils.isEmpty(listeners)) {
      FiscalYearEventVo oldVo = this.nebulaToolkitService.copyObjectByWhiteList(entity, FiscalYearEventVo.class, HashSet.class, ArrayList.class);
      FiscalYearEventVo newVo = this.nebulaToolkitService.copyObjectByWhiteList(dto, FiscalYearEventVo.class, HashSet.class, ArrayList.class);
      listeners.forEach(listener -> {
        listener.onUpdate(oldVo, newVo);
      });
    }
    return updateEntity;
  }

  @Override
  @Transactional
  public void deleteBatch(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "缺失id");
    List<FiscalYearEntity> entities = this.fiscalYearRepository.listByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
    Validate.isTrue(!CollectionUtils.isEmpty(entities) && entities.size() == ids.size(), "数据删除个数不匹配");
    this.fiscalYearRepository.updateDelFlagByIds(ids);
    List<String> yearList = entities.stream().map(FiscalYearEntity::getYear).collect(Collectors.toList());
    this.fiscalYearDetailRepository.deleteByYear(yearList,TenantUtils.getTenantCode());
    //删除财年事件通知
    if (!CollectionUtils.isEmpty(listeners)) {
      List<FiscalYearEventVo> voList = Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(entities, FiscalYearEntity.class
          , FiscalYearEventVo.class, HashSet.class, ArrayList.class));
      listeners.forEach(listener -> {
        listener.onDelete(voList);
      });
    }
  }

  @Override
  @Transactional
  public void enableBatch(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "缺失id");
    List<FiscalYearEntity> entities = this.fiscalYearRepository.listByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
    Validate.isTrue(!CollectionUtils.isEmpty(entities) && entities.size() == ids.size(), "数据启用个数不匹配");
    this.fiscalYearRepository.updateEnableStatusByIds(ids, EnableStatusEnum.ENABLE);
    //启用财年事件通知
    if (!CollectionUtils.isEmpty(listeners)) {
      List<FiscalYearEventVo> voList = Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(entities, FiscalYearEntity.class
          , FiscalYearEventVo.class, HashSet.class, ArrayList.class));
      listeners.forEach(listener -> {
        listener.onEnable(voList);
      });
    }
  }

  @Override
  @Transactional
  public void disableBatch(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "缺失id");
    List<FiscalYearEntity> entities = this.fiscalYearRepository.listByIdsAndTenantCode(ids,TenantUtils.getTenantCode());
    Validate.isTrue(!CollectionUtils.isEmpty(entities) && entities.size() == ids.size(), "数据禁用个数不匹配");
    this.fiscalYearRepository.updateEnableStatusByIds(ids, EnableStatusEnum.DISABLE);
    //禁用财年事件通知
    if (!CollectionUtils.isEmpty(listeners)) {
      List<FiscalYearEventVo> voList = Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(entities, FiscalYearEntity.class
          , FiscalYearEventVo.class, HashSet.class, ArrayList.class));
      listeners.forEach(listener -> {
        listener.onDisable(voList);
      });
    }
  }

  @Override
  public Page<FiscalYearEntity> findByConditions(Pageable pageable, FiscalYearPageDto dto) {
    pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
    dto = ObjectUtils.defaultIfNull(dto, new FiscalYearPageDto());
    dto.setTenantCode(TenantUtils.getTenantCode());//设置租户编号信息
    return this.fiscalYearRepository.findByConditions(pageable, dto);
  }

  @Override
  public FiscalYearEntity findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    return this.fiscalYearRepository.findById(id,TenantUtils.getTenantCode());
  }

  /**
   * 在创建fiscalYear模型对象之前，检查对象各属性的正确性，其主键属性必须没有值
   *
   * @param dto 检查对象
   */
  private void createValidation(FiscalYearDto dto) {
    Validate.notNull(dto, "进行当前操作时，信息对象必须传入!");
    dto.setTenantCode(TenantUtils.getTenantCode());
    dto.setId(null);
    //财年有效性校验
    this.validityCheck(dto);
    //财年明细校验
    this.detailCheck(dto);
  }

  /**
   * 在修改fiscalYear模型对象之前，检查对象各属性的正确性，其主键属性必须没有值
   *
   * @param dto 检查对象
   */
  private void updateValidation(FiscalYearDto dto) {
    Validate.notNull(dto, "进行当前操作时，信息对象必须传入!");
    dto.setTenantCode(TenantUtils.getTenantCode());
    Validate.notBlank(dto.getId(), "修改信息时，id不能为空！");
    //财年有效性校验
    this.validityCheck(dto);
    //财年明细校验
    this.detailCheck(dto);
  }

  /**
   * 财年有效性校验
   *
   * @param dto
   */
  private void validityCheck(FiscalYearDto dto) {
    String year = dto.getYear();
    Date beginTime = dto.getBeginTime();
    Date endTime = dto.getEndTime();
    Validate.notBlank(dto.getTenantCode(), "租户编码不能为空");
    Validate.notBlank(year, "预算财年不能为空");
    Validate.notNull(beginTime, "财年开始日期不能为空");
    Validate.notNull(endTime, "财年结束日期不能为空");
    //唯一校验
    List<FiscalYearEntity> existList = this.fiscalYearRepository.findByFiscalYearDto(dto);
    Map<String, FiscalYearEntity> existMap = existList.stream()
        .filter(o -> !o.getId().equals(dto.getId()))
        .collect(Collectors.toMap(FiscalYearEntity::getYear, Function.identity()));
    FiscalYearEntity fiscalYearEntity = existMap.get(dto.getYear());
    Validate.isTrue(ObjectUtils.isEmpty(fiscalYearEntity), "预算财年已存在，请重新填写");
    //财年时间交叉校验
    existList.stream().filter(o -> !o.getId().equals(dto.getId())).forEach(o -> {
      Date existBeginTime = o.getBeginTime();
      Date existEndTime = o.getEndTime();
      boolean timeFlag = true;
      if ((existBeginTime.compareTo(beginTime) <= 0 && existEndTime.compareTo(beginTime) >= 0)
          || (existBeginTime.compareTo(endTime) <= 0 && existEndTime.compareTo(endTime) >= 0)
          || (existBeginTime.compareTo(beginTime) <= 0 && existEndTime.compareTo(endTime) >= 0)
          || (existBeginTime.compareTo(beginTime) >= 0) && existEndTime.compareTo(endTime) <= 0) {
        timeFlag = false;
      }
      Validate.isTrue(timeFlag, "与历史财年[" + o.getYear() + "]存在时间交叉");
    });
  }

  /**
   * 财年明细校验
   *
   * @param dto
   */
  private void detailCheck(FiscalYearDto dto) {
    //财年时间
    Date yearBeginTime = dto.getBeginTime();
    Date yearEndTime = dto.getEndTime();
    //校验月份数量
    List<FiscalYearDetailDto> fiscalYearDetailDtos = dto.getFiscalYearDetailDtos();
    Validate.isTrue(!CollectionUtils.isEmpty(fiscalYearDetailDtos), "财年明细不能为空");
    Validate.isTrue((fiscalYearDetailDtos.size() == FiscalYearConstant.FISCAL_YEAR_MONTH_NUM), "财年明细月份数量错误");
    //季度分组
    Map<String, Map<String, FiscalYearDetailDto>> quarterMap = Maps.newHashMap();
    this.quarterMonthCheck(fiscalYearDetailDtos, quarterMap);
    //一季度一月开始日期
    Map<String, FiscalYearDetailDto> oneQuarterMap = quarterMap.get(FiscalYearQuarterEnum.Q1TH.getDictCode());
    FiscalYearDetailDto janMonth = oneQuarterMap.get(FiscalYearMonthEnum.JAN.getDictCode());
    Date janBeginTime = janMonth.getBeginTime();
    //四季度十二月结束日期
    Map<String, FiscalYearDetailDto> fourQuarterMap = quarterMap.get(FiscalYearQuarterEnum.Q4TH.getDictCode());
    FiscalYearDetailDto decMonth = fourQuarterMap.get(FiscalYearMonthEnum.DEC.getDictCode());
    Date decEndTime = decMonth.getEndTime();
    Validate.isTrue((yearBeginTime.compareTo(janBeginTime) == 0), "财年一月开始日期必须等于财年开始日期");
    Validate.isTrue((yearEndTime.compareTo(decEndTime) == 0), "财年十二月结束日期必须等于财年结束日期");
    //月份分组
    Map<String, FiscalYearDetailDto> monthMap = fiscalYearDetailDtos.stream()
        .collect(Collectors.toMap(FiscalYearDetailDto::getMonth, Function.identity()));
    //循环校验明细
    fiscalYearDetailDtos.stream()
        .sorted(Comparator.comparing(o -> Integer.valueOf(o.getMonth())))
        .forEach(o -> {
          //当前月份
          Integer month = Integer.valueOf(o.getMonth());
          //根据月份来给半年度属性赋值（Month>=7 为下半年 否则为上半年）
          if (Integer.valueOf(o.getMonth()) >= FiscalYearConstant.FISCAL_HALF_YEAR_MONTH) {
            o.setHalfYear(FiscalHalfYearEnum.DOWN_YEAR.getCode());
          } else {
            o.setHalfYear(FiscalHalfYearEnum.UP_YEAR.getCode());
          }
          Validate.isTrue(o.getBeginTime().compareTo(o.getEndTime()) < 0, "月份开始时间必须小于结束时间");
          //获取上一月结束时间
          int lastMonth = month - 1;
          FiscalYearDetailDto lastMonthDto = monthMap.get(String.valueOf(lastMonth));
          if (ObjectUtils.isNotEmpty(lastMonthDto)) {
            Date lastMonthEndTime = lastMonthDto.getEndTime();
            Validate.isTrue((lastMonthEndTime.compareTo(o.getBeginTime()) < 0), month + "月份开始日期必须大于上一个月结束日期");
            //设置结束日期时间为00:00:00
            Calendar cl = Calendar.getInstance();
            cl.setTime(lastMonthEndTime);
            cl.set(Calendar.SECOND, 0);
            cl.set(Calendar.MINUTE, 0);
            cl.set(Calendar.HOUR_OF_DAY, 0);
            cl.add(Calendar.DAY_OF_MONTH, 1);
            Date beginTime = cl.getTime();
            Validate.isTrue((beginTime.compareTo(o.getBeginTime()) == 0), month + "月份开始日期与上一个月结束日期存在遗漏");
          }
        });
  }

  /**
   * 季度月份校验
   *
   * @param fiscalYearDetailDtos
   * @param quarterMap
   */
  private void quarterMonthCheck(List<FiscalYearDetailDto> fiscalYearDetailDtos, Map<String, Map<String, FiscalYearDetailDto>> quarterMap) {
    //校验季度是否正确
    fiscalYearDetailDtos.forEach(o -> {
      String quarter = o.getQuarter();
      String month = o.getMonth();
      Validate.notBlank(o.getMonth(), "月份不能为空");
      Validate.notBlank(o.getQuarter(), "季度不能为空");
      Validate.notNull(o.getBeginTime(), "财年月份开始日期不能为空");
      Validate.notNull(o.getEndTime(), "财年月份结束日期不能为空");
      FiscalYearQuarterEnum quarterEnum = FiscalYearQuarterEnum.getByDictCode(quarter);
      Validate.isTrue(ObjectUtils.isNotEmpty(quarterEnum), "季度[" + quarter + "]数值错误");
      Map<String, FiscalYearDetailDto> monthMap = quarterMap.get(quarter);
      if (CollectionUtils.isEmpty(monthMap)) {
        monthMap = Maps.newHashMap();
      }
      //月份
      FiscalYearMonthEnum monthEnum = FiscalYearMonthEnum.getByDictCode(month);
      Validate.isTrue(ObjectUtils.isNotEmpty(monthEnum), "月度[" + month + "]数值错误");
      switch (quarterEnum) {
        case Q1TH:
          //一季度
          switch (monthEnum) {
            //一二三月
            case JAN:
            case FEB:
            case MAR:
              if (ObjectUtils.isNotEmpty(monthMap.get(month))) {
                throw new IllegalArgumentException(quarter + "季度存在多个" + month + "月份");
              }
              monthMap.put(month, o);
              quarterMap.put(quarter, monthMap);
              break;
            default:
              throw new IllegalArgumentException(quarter + "季度" + month + "月份错误");
          }
          break;
        case Q2ND:
          //二季度
          switch (monthEnum) {
            //四五六月
            case APR:
            case MAY:
            case JUN:
              if (ObjectUtils.isNotEmpty(monthMap.get(month))) {
                throw new IllegalArgumentException(quarter + "季度存在多个" + month + "月份");
              }
              monthMap.put(month, o);
              quarterMap.put(quarter, monthMap);
              break;
            default:
              throw new IllegalArgumentException(quarter + "季度" + month + "月份错误");
          }
          break;
        case Q3RD:
          //三季度
          switch (monthEnum) {
            //七八九月
            case JUL:
            case AUG:
            case SEPT:
              if (ObjectUtils.isNotEmpty(monthMap.get(month))) {
                throw new IllegalArgumentException(quarter + "季度存在多个" + month + "月份");
              }
              monthMap.put(month, o);
              quarterMap.put(quarter, monthMap);
              break;
            default:
              throw new IllegalArgumentException(quarter + "季度" + month + "月份错误");
          }
          break;
        case Q4TH:
          //四季度
          switch (monthEnum) {
            //十十一十二月
            case OCT:
            case NOV:
            case DEC:
              if (ObjectUtils.isNotEmpty(monthMap.get(month))) {
                throw new IllegalArgumentException(quarter + "季度存在多个" + month + "月份");
              }
              monthMap.put(month, o);
              quarterMap.put(quarter, monthMap);
              break;
            default:
              throw new IllegalArgumentException(quarter + "季度" + month + "月份错误");
          }
          break;
        default:
          break;
      }
    });
  }
}
