package com.biz.crm.mdm.business.fiscal.year.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.fiscal.year.local.entity.FiscalYearEntity;
import com.biz.crm.mdm.business.fiscal.year.local.mapper.FiscalYearMapper;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearDto;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearLinkageDto;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearPageDto;
import com.biz.crm.mdm.business.fiscal.year.sdk.enums.FiscalYearTypeEnum;
import com.biz.crm.mdm.business.fiscal.year.sdk.vo.FiscalYearVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;

/**
 * 财年表的数据库访问类 {@link FiscalYearEntity}
 *
 * <AUTHOR>
 * @date 2021-11-16 14:08:28
 */
@Component
public class FiscalYearRepository extends ServiceImpl<FiscalYearMapper, FiscalYearEntity> {

  /**
   * 通过FiscalYearDto查询出已经存在的财年数据(如果ID存在,需要排除当前ID的数据)
   *
   * @param dto 参数Dto
   * @return 已经存在的财年数据
   */
  public List<FiscalYearEntity> findByFiscalYearDto(FiscalYearDto dto) {
    return this.lambdaQuery()
        .eq(FiscalYearEntity::getTenantCode, dto.getTenantCode())
        .eq(FiscalYearEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .list();
  }

  /**
   * 通过开始时间和结束时间查询在存在时间交集的数据
   *
   * @param startTime  开始时间
   * @param endTime    结束时间
   * @param tenantCode 租户编码
   * @return 存在时间交集的数据
   */
  public List<FiscalYearEntity> findByStartTimeAndEndTime(Date startTime, Date endTime, String tenantCode) {
    //此处sql逻辑相当于(tenant_code = ? and del_flag =  '" + DelFlagStatusEnum.NORMAL.getCode() + "' and (( begin_time between ? and ?) or ( end_time between ? and ?))
    return this.lambdaQuery()
        .eq(FiscalYearEntity::getTenantCode, tenantCode)
        .eq(FiscalYearEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .and(queryWrapper -> {
          queryWrapper.or(qw -> qw.between(FiscalYearEntity::getBeginTime, startTime, endTime));
          queryWrapper.or(qw -> qw.between(FiscalYearEntity::getEndTime, startTime, endTime));
        })
        .list();
  }

  /**
   * 批量删除
   *
   * @param ids ID集合
   */
  public void updateDelFlagByIds(List<String> ids) {
    this.lambdaUpdate()
        .in(FiscalYearEntity::getId, ids)
        .eq(FiscalYearEntity::getTenantCode, TenantUtils.getTenantCode())//设置租户编号信息
        .set(FiscalYearEntity::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
        .update();
  }

  /**
   * 批量更新启用禁用状态
   *
   * @param ids          ID集合
   * @param enableStatus 启用禁用状态
   */
  public void updateEnableStatusByIds(List<String> ids, EnableStatusEnum enableStatus) {
    this.lambdaUpdate()
        .in(FiscalYearEntity::getId, ids)
        .eq(FiscalYearEntity::getTenantCode, TenantUtils.getTenantCode())//设置租户编号信息
        .set(FiscalYearEntity::getEnableStatus, enableStatus.getCode())
        .update();
  }

  /**
   * 财年分页列表
   *
   * @param dto      请求参数dto
   * @param pageable 分页信息
   * @return Page<FiscalYearEntity> 财年分页信息
   */
  public Page<FiscalYearEntity> findByConditions(Pageable pageable, FiscalYearPageDto dto) {
    Page<FiscalYearEntity> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return this.baseMapper.findByConditions(page, dto);
  }

  /**
   * 通过ID获取财年信息
   *
   * @param id 财年ID
   * @param tenantCode
   * @return 财年信息
   */
  public FiscalYearEntity findById(String id, String tenantCode) {
    return this.baseMapper.findById(id,tenantCode);
  }


  /**
   * 财年联动查询
   *
   * @param dto 请求dto
   * @return 财年信息
   */
  public List<FiscalYearEntity> findByFiscalYearLinkageDto(FiscalYearLinkageDto dto) {
    List<FiscalYearEntity> entities = Lists.newLinkedList();
    if (FiscalYearTypeEnum.YEARLY.getDictCode().equals(dto.getFiscalYearType())) {
      entities = this.baseMapper.findByFiscalYearLinkageYearDto(dto);
    } else {
      FiscalYearEntity byFiscalYearLinkageDto = this.baseMapper.findByFiscalYearLinkageDto(dto);
      if (!ObjectUtils.isEmpty(byFiscalYearLinkageDto)) {
        entities.add(byFiscalYearLinkageDto);
      }
    }
    return entities;
  }

  /**
   * 根据时间查询财年信息
   *
   * @param beginTime
   * @param endTime
   * @return
   */
  public List<FiscalYearVo> findByBeginTimeAndEndTime(Date beginTime, Date endTime) {
    return this.baseMapper.findByBeginTimeAndEndTime(beginTime, endTime, TenantUtils.getTenantCode());
  }

  /**
   * 重构查询方法
   * @param id
   * @param tenantCode
   * @return
   */
  public FiscalYearEntity findByIdAndTenantCode(String id, String tenantCode) {
    return this.lambdaQuery()
        .eq(FiscalYearEntity::getTenantCode,tenantCode)
        .in(FiscalYearEntity::getId,id)
        .one();

  }

  public List<FiscalYearEntity> listByIdsAndTenantCode(List<String> ids, String tenantCode) {
    return this.lambdaQuery()
        .eq(FiscalYearEntity::getTenantCode,tenantCode)
        .in(FiscalYearEntity::getId,ids)
        .list();
  }
}
