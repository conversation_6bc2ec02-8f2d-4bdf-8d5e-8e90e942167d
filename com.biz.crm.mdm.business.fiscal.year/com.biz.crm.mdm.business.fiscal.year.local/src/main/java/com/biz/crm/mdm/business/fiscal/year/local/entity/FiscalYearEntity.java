package com.biz.crm.mdm.business.fiscal.year.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.util.Date;
import java.util.List;


/**
 * 财年实体类
 *
 * <AUTHOR>
 * @date 2021/11/16
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "FiscalYearEntity", description = "财年实体类")
@Entity
@TableName("mdm_fiscal_year")
@Table(name = "mdm_fiscal_year", indexes = {
    @Index(name = "mdm_fiscal_year_index1", columnList = "tenant_code")
})
@org.hibernate.annotations.Table(appliesTo = "mdm_fiscal_year", comment = "财年表")
public class FiscalYearEntity extends TenantFlagOpEntity {

  private static final long serialVersionUID = 6341424867963604988L;

  /**
   * 预算年(字典编码)
   */
  @ApiModelProperty("预算年(字典编码)")
  @Column(name = "year", length = 32, columnDefinition = "VARCHAR(32) COMMENT '预算年(字典编码)'")
  private String year;

  /**
   * 开始时间
   */
  @ApiModelProperty("开始时间")
  @Column(name = "begin_time", length = 32, columnDefinition = "datetime COMMENT '开始时间'")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;

  /**
   * 结束时间
   */
  @ApiModelProperty("结束时间")
  @Column(name = "end_time", length = 32, columnDefinition = "datetime COMMENT '结束时间'")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  /**
   * 财年明细信息
   */
  @ApiModelProperty("财年明细信息")
  @Transient
  @TableField(exist = false)
  private List<FiscalYearDetailEntity> fiscalYearDetails;
}
