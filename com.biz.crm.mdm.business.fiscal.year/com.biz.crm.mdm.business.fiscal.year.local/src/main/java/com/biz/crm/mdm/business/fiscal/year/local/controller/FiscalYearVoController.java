package com.biz.crm.mdm.business.fiscal.year.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.fiscal.year.local.entity.FiscalYearEntity;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearLinkageDto;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearPageDto;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearReconciliationDto;
import com.biz.crm.mdm.business.fiscal.year.sdk.service.FiscalYearVoService;
import com.biz.crm.mdm.business.fiscal.year.sdk.vo.FiscalYearRebateVo;
import com.biz.crm.mdm.business.fiscal.year.sdk.vo.FiscalYearVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


import java.util.Date;
import java.util.List;

/**
 * @Project crm
 * @PackageName com.biz.crm.mdm.business.fiscal.year.local.controller
 * @ClassName FiscalYearVoController
 * <AUTHOR>
 * @Date 2022/3/2 下午5:32
 * @Description 财年设置其他模块调用接口
 */
@Slf4j
@RestController
@RequestMapping("/v1/fiscalYear/fiscalYear")
@Api(tags = "财年信息: FiscalYearVo: 财年设置")
public class FiscalYearVoController {

  @Autowired(required = false)
  private FiscalYearVoService fiscalYearVoService;

  /**
   * 合同/销量目录财年联动查询
   *
   * @param dto 请求dto
   * @return 财年信息
   */
  @ApiOperation(value = "合同/销量目录财年联动查询")
  @GetMapping("/findByFiscalYearLinkageDto")
  public Result<List<FiscalYearVo>> findByFiscalYearLinkageDto(@ApiParam(name = "FiscalYearLinkageDto", value = "查询Dto") FiscalYearLinkageDto dto) {
    try {
      return Result.ok(this.fiscalYearVoService.findByFiscalYearLinkageDto(dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 对账规则根据时间段查询
   * @param dto
   * @return
   */
  @ApiOperation(value = "对账规则根据时间段查询")
  @GetMapping("/findByFiscalYearReconciliationDto")
  public Result<List<FiscalYearVo>> findByDateAndFiscalYearType(@ApiParam(name = "FiscalYearReconciliationDto", value = "对账规则根据时间段查询Dto") FiscalYearReconciliationDto dto) {
    try {
      return Result.ok(this.fiscalYearVoService.findByFiscalYearReconciliationDto(dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据时间查询财年信息
   *
   * @param beginTime
   * @param endTime
   * @return 财年信息
   */
  @ApiOperation(value = "根据时间查询财年信息")
  @GetMapping("/findByBeginTimeAndEndTime")
  public Result<List<FiscalYearVo>> findByBeginTimeAndEndTime(@RequestParam("beginTime") Date beginTime
      , @RequestParam("endTime") Date endTime) {
    try {
      return Result.ok(this.fiscalYearVoService.findByBeginTimeAndEndTime(beginTime,endTime));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 查询当前租户下配置的财年信息
   * @return
   */
  @ApiOperation(value = "查询当前租户下配置的财年信息")
  @GetMapping("/findFiscalYearRebate")
  public Result<List<FiscalYearRebateVo>> findFiscalYearRebate() {
    try {
      return Result.ok(this.fiscalYearVoService.findFiscalYearRebate());
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


}