package com.biz.crm.mdm.business.fiscal.year.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import com.biz.crm.business.common.local.entity.UuidEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Project crm
 * @PackageName com.biz.crm.mdm.business.fiscal.year.local.entity
 * @ClassName FiscalYearDetailEntity
 * <AUTHOR>
 * @Date 2022/2/28 下午3:58
 * @Description 财年明细表
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "FiscalYearDetailEntity", description = "财年明细实体类")
@Entity
@TableName("mdm_fiscal_year_detail")
@Table(name = "mdm_fiscal_year_detail", indexes = {
    @Index(name = "mdm_fiscal_year_detail_index1", columnList = "tenant_code"),
    @Index(name = "mdm_fiscal_year_detail_index2", columnList = "year")
})
@org.hibernate.annotations.Table(appliesTo = "mdm_fiscal_year_detail", comment = "财年明细表")
public class FiscalYearDetailEntity extends TenantEntity {

  private static final long serialVersionUID = -1525534931791054446L;

  /**
   * 预算年(字典编码)
   */
  @ApiModelProperty("预算年(字典编码)")
  @Column(name = "year", length = 32, columnDefinition = "VARCHAR(32) COMMENT '预算年(字典编码)'")
  private String year;

  /**
   * 预算季度(字典编码)
   */
  @ApiModelProperty("预算季度(字典编码)")
  @Column(name = "quarter", length = 32, columnDefinition = "VARCHAR(32) COMMENT '预算季度(字典编码)'")
  private String quarter;

  /**
   * 预算月度(字典编码)
   */
  @ApiModelProperty("预算月度(字典编码)")
  @Column(name = "month", length = 32, columnDefinition = "VARCHAR(32) COMMENT '预算月度(字典编码)'")
  private String month;

  /**
   * 半年度（字典编码）
   */
  @ApiModelProperty("半年度（字典编码）")
  @Column(name = "half_year", length = 32, columnDefinition = "VARCHAR(32) COMMENT '半年度（字典编码）'")
  private String halfYear;

  /**
   * 开始时间
   */
  @ApiModelProperty("开始时间")
  @Column(name = "begin_time", length = 32, columnDefinition = "datetime COMMENT '开始时间'")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;

  /**
   * 结束时间
   */
  @ApiModelProperty("结束时间")
  @Column(name = "end_time", length = 32, columnDefinition = "datetime COMMENT '结束时间'")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;
}