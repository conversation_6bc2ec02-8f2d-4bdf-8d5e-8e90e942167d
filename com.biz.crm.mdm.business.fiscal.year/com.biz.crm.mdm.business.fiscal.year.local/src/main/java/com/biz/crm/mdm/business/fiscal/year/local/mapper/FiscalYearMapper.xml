<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.fiscal.year.local.mapper.FiscalYearMapper">

  <resultMap id="fiscalYearMap" type="com.biz.crm.mdm.business.fiscal.year.local.entity.FiscalYearEntity">
    <id column="id" property="id"/>
    <result column="del_flag" property="delFlag"/>
    <result column="enable_status" property="enableStatus"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="year" property="year"/>
    <result column="begin_time" property="beginTime"/>
    <result column="end_time" property="endTime"/>
    <collection property="fiscalYearDetails" resultMap="detailMap" column="detail_id" columnPrefix="detail_"/>
  </resultMap>

  <resultMap id="detailMap" type="com.biz.crm.mdm.business.fiscal.year.local.entity.FiscalYearDetailEntity">
    <id column="id" property="id"/>
    <result column="year" property="year"/>
    <result column="quarter" property="quarter"/>
    <result column="month" property="month"/>
    <result column="half_year" property="halfYear"/>
    <result column="begin_time" property="beginTime"/>
    <result column="end_time" property="endTime"/>
  </resultMap>

  <select id="findByConditions"
          resultType="com.biz.crm.mdm.business.fiscal.year.local.entity.FiscalYearEntity">
    SELECT
      *
    FROM
      mdm_fiscal_year
    WHERE
      del_flag = '${@<EMAIL>()}'
      AND tenant_code = #{dto.tenantCode}
      <if test="dto.year != null and dto.year != ''">
        AND year = #{dto.year}
      </if>
      <if test="dto.beginTime != null">
        AND begin_time <![CDATA[ >= ]]> #{dto.beginTime}
      </if>
      <if test="dto.endTime != null">
        AND end_time <![CDATA[ <= ]]> #{dto.endTime}
      </if>
      <if test="dto.enableStatus != null and dto.enableStatus != ''">
        and enable_status = #{dto.enableStatus}
      </if>
        ORDER BY create_time desc
  </select>

  <select id="findByFiscalYearLinkageDto" resultMap="fiscalYearMap">
    SELECT
      a.*,
      b.id as detail_id,
      b.year as detail_year,
      b.quarter as detail_quarter,
      b.month as detail_month,
      b.half_year as detail_half_year,
      b.begin_time as detail_begin_time,
      b.end_time as detail_end_time
    FROM
      mdm_fiscal_year a
    LEFT JOIN mdm_fiscal_year_detail b ON (a.year = b.year and a.tenant_code = b.tenant_code)
    WHERE
      a.del_flag = '${@<EMAIL>()}'
      AND a.tenant_code = #{dto.tenantCode}
      <if test="dto.year != null and dto.year != ''">
        AND a.year = #{dto.year}
      </if>
      <if test="dto.enableStatus != null and dto.enableStatus != ''">
        and a.enable_status = #{dto.enableStatus}
      </if>
  </select>

  <!--通过ID查询财年信息-->
  <select id="findById" resultMap="fiscalYearMap">
    SELECT
      a.*,
      b.id as detail_id,
      b.year as detail_year,
      b.quarter as detail_quarter,
      b.month as detail_month,
      b.half_year as detail_half_year,
      b.begin_time as detail_begin_time,
      b.end_time as detail_end_time
    FROM
      mdm_fiscal_year a
      LEFT JOIN mdm_fiscal_year_detail b ON (a.year = b.year and a.tenant_code = b.tenant_code)
    WHERE
      a.del_flag = '${@<EMAIL>()}'
      AND a.id = #{id} and a.tenant_code = #{tenantCode}
  </select>

  <!--财年联动查询 财年集合-->
  <select id="findByFiscalYearLinkageYearDto"
          resultType="com.biz.crm.mdm.business.fiscal.year.local.entity.FiscalYearEntity">
    SELECT
    a.*
    FROM
    mdm_fiscal_year a
    WHERE
    a.del_flag = '${@<EMAIL>()}'
    AND a.tenant_code = #{dto.tenantCode}
    <if test="dto.enableStatus != null and dto.enableStatus != ''">
      and a.enable_status = #{dto.enableStatus}
    </if>
  </select>

  <!--根据时间查询财年信息-->
  <select id="findByBeginTimeAndEndTime"
          resultType="com.biz.crm.mdm.business.fiscal.year.sdk.vo.FiscalYearVo">
    SELECT
    a.id,a.year,b.quarter,b.month,b.begin_time,b.end_time
    FROM
    mdm_fiscal_year a
    LEFT JOIN mdm_fiscal_year_detail b ON (a.year = b.year and a.tenant_code = b.tenant_code)
    WHERE
    a.del_flag = '${@<EMAIL>()}'
    AND a.tenant_code = #{tenantCode}
    and a.enable_status = '${@<EMAIL>()}'
    AND b.end_time <![CDATA[ > ]]> #{beginTime}
    AND b.begin_time <![CDATA[ < ]]> #{endTime}
    order by b.begin_time asc
  </select>

</mapper>
