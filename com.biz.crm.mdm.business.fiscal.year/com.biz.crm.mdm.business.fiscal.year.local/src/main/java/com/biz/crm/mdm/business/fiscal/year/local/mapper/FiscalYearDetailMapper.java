package com.biz.crm.mdm.business.fiscal.year.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.fiscal.year.local.entity.FiscalYearDetailEntity;
import com.biz.crm.mdm.business.fiscal.year.local.entity.FiscalYearEntity;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearLinkageDto;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearPageDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 财年明细表的mybatis-plus接口类 {@link FiscalYearDetailEntity}
 *
 * <AUTHOR>
 * @Date 2022/2/28 下午3:58
 */
public interface FiscalYearDetailMapper extends BaseMapper<FiscalYearDetailEntity> {

}

