package com.biz.crm.mdm.business.fiscal.year.local.service.internal;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.fiscal.year.local.entity.FiscalYearDetailEntity;
import com.biz.crm.mdm.business.fiscal.year.local.entity.FiscalYearEntity;
import com.biz.crm.mdm.business.fiscal.year.local.repository.FiscalYearDetailRepository;
import com.biz.crm.mdm.business.fiscal.year.local.repository.FiscalYearRepository;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.*;
import com.biz.crm.mdm.business.fiscal.year.sdk.enums.FiscalYearReconciliationTypeEnum;
import com.biz.crm.mdm.business.fiscal.year.sdk.enums.FiscalYearTypeEnum;
import com.biz.crm.mdm.business.fiscal.year.sdk.service.FiscalYearVoService;
import com.biz.crm.mdm.business.fiscal.year.sdk.vo.FiscalYearRebateVo;
import com.biz.crm.mdm.business.fiscal.year.sdk.vo.FiscalYearVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.netty.util.internal.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Year;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Project crm
 * @PackageName com.biz.crm.mdm.business.fiscal.year.local.service.internal
 * @ClassName FiscalYearVoServiceImpl
 * <AUTHOR>
 * @Date 2022/3/2 下午5:37
 * @Description 财年设置VO服务接口实现
 */
@Slf4j
@Service("fiscalYearVoService")
public class FiscalYearVoServiceImpl implements FiscalYearVoService {

  @Autowired(required = false)
  private FiscalYearRepository fiscalYearRepository;
  @Autowired(required = false)
  private FiscalYearDetailRepository fiscalYearDetailRepository;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public List<FiscalYearVo> findByFiscalYearLinkageDto(FiscalYearLinkageDto dto) {
    if (Objects.isNull(dto) || StringUtils.isBlank(dto.getFiscalYearType())) {
      return Lists.newLinkedList();
    }else if(FiscalYearTypeEnum.MONTHLY.getDictCode().equals(dto.getFiscalYearType())
        || FiscalYearTypeEnum.QUARTER.getDictCode().equals(dto.getFiscalYearType())){
      Validate.notBlank(dto.getYear(),"年份不能为空!");
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    List<FiscalYearEntity> fiscalYearEntities = this.fiscalYearRepository.findByFiscalYearLinkageDto(dto);
    if (ObjectUtils.isEmpty(fiscalYearEntities)) {
      return Lists.newLinkedList();
    }
    Map<String, FiscalYearVo> entityMap;
    List<FiscalYearVo> list = Lists.newArrayList();
    switch (FiscalYearTypeEnum.getByDictCode(dto.getFiscalYearType())) {
      //1.获取年信息
      case YEARLY:
        entityMap = fiscalYearEntities.stream().collect(Collectors.toMap(FiscalYearEntity::getYear, o -> {
          FiscalYearVo linkageVo = new FiscalYearVo();
          linkageVo.setYear(o.getYear());
          return linkageVo;
        }, (a, b) -> b));
        list = Lists.newArrayList(entityMap.values()).stream().sorted(Comparator.comparing(FiscalYearVo::getYear)).collect(Collectors.toList());
        break;
      //2.获取季度信息
      case QUARTER:
        entityMap = fiscalYearEntities.get(0).getFiscalYearDetails().stream().collect(Collectors.toMap(o -> String.format("%s-%s",o.getYear(),o.getQuarter()), o -> {
          FiscalYearVo linkageVo = new FiscalYearVo();
          linkageVo.setYear(o.getYear());
          linkageVo.setQuarter(o.getQuarter());
          return linkageVo;
        }, (a, b) -> b));
        list = Lists.newArrayList(entityMap.values()).stream().sorted(Comparator.comparing(o -> Integer.valueOf(o.getQuarter()))).collect(Collectors.toList());
        break;
      //3.获取月份信息
      case MONTHLY:
        entityMap = fiscalYearEntities.get(0).getFiscalYearDetails().stream().collect(Collectors.toMap(o -> String.format("%s-%s-%s",o.getYear(),o.getQuarter(),o.getMonth()), o -> {
          FiscalYearVo linkageVo = new FiscalYearVo();
          linkageVo.setYear(o.getYear());
          linkageVo.setQuarter(o.getQuarter());
          linkageVo.setMonth(o.getMonth());
          return linkageVo;
        }, (a, b) -> b));
        list = Lists.newArrayList(entityMap.values()).stream().sorted(Comparator.comparing(o -> Integer.valueOf(o.getMonth()))).collect(Collectors.toList());
        break;
      default:
        break;
    }
    return list;
  }

  /**
   * 根据时间和财年类型查询
   *
   * @param dto
   * @return
   */
  @Override
  public List<FiscalYearVo> findByFiscalYearReconciliationDto(FiscalYearReconciliationDto dto) {
    if (StringUtils.isBlank(dto.getFiscalYearType())) {
      Validate.notBlank(dto.getFiscalYearType(),"财年类型不能为空");
    }
    Validate.isTrue(!org.springframework.util.ObjectUtils
        .isEmpty(FiscalYearReconciliationTypeEnum.getByDictCode(dto.getFiscalYearType())),"财年类型错误");
    Validate.isTrue(!org.springframework.util.ObjectUtils.isEmpty(dto.getBeginTime()),"开始时间不能为空");
    Validate.isTrue(!org.springframework.util.ObjectUtils.isEmpty(dto.getEndTime()),"结束时间不能为空");
    dto.setTenantCode(TenantUtils.getTenantCode());
    List<FiscalYearVo> fiscalYearVos = this.fiscalYearRepository.findByBeginTimeAndEndTime(dto.getBeginTime(),dto.getEndTime());
    List<FiscalYearVo> fiscalYearRespList = buildByFiscalYearType(dto.getFiscalYearType(), fiscalYearVos);
    return fiscalYearRespList.stream().sorted(Comparator.comparing(FiscalYearVo::getBeginTime))
        .collect(Collectors.toList());
  }

  /**
   * 根据财年类型构建数据结构
   * @param fiscalYearType
   * @param fiscalYearVos
   * @return
   */
  private List<FiscalYearVo> buildByFiscalYearType(String fiscalYearType, List<FiscalYearVo> fiscalYearVos) {
    List<FiscalYearVo> list = Lists.newArrayList();
    if(CollectionUtils.isEmpty(fiscalYearVos)){
      return list;
    }
    Map<String, List<FiscalYearVo>> fiscalYearMap = Maps.newHashMap();
    switch (FiscalYearReconciliationTypeEnum.getByDictCode(fiscalYearType)) {
      //1.获取年信息
      case YEARLY:
        fiscalYearMap = fiscalYearVos.stream().collect(Collectors.groupingBy(FiscalYearVo::getYear));
        buildFiscalYearVo(list,fiscalYearMap);
        break;
      //2.获取季度信息
      case QUARTER:
        fiscalYearMap = fiscalYearVos.stream().collect(Collectors.groupingBy(o -> String.format("%s-%s",o.getYear(),o.getQuarter())));
        buildFiscalYearVo(list,fiscalYearMap);
        break;
      //3.获取月份信息
      case MONTHLY:
        fiscalYearMap = fiscalYearVos.stream().collect(Collectors.groupingBy(o -> String.format("%s-%s-%s",o.getYear(),o.getQuarter(),o.getMonth())));
        buildFiscalYearVo(list,fiscalYearMap);
        break;
      default:
        break;
    }
    return list;
  }

  private void buildFiscalYearVo(List<FiscalYearVo> list, Map<String, List<FiscalYearVo>> fiscalYearMap) {
    fiscalYearMap.forEach((k,v) -> {
      FiscalYearVo begin = v.get(0);
      FiscalYearVo end = v.get(v.size() - 1);
      FiscalYearVo vo = new FiscalYearVo();
      vo.setYear(begin.getYear());
      vo.setQuarter(begin.getQuarter());
      vo.setMonth(begin.getMonth());
      vo.setId(begin.getId());
      vo.setBeginTime(begin.getBeginTime());
      vo.setEndTime(end.getEndTime());
      list.add(vo);
    });
  }

  /**
   * 根据时间查询财年信息
   * @param dto
   * @return
   */
  @Override
  public List<FiscalYearVo> findByBeginTimeAndEndTime(Date beginTime, Date endTime) {
    Validate.isTrue(!org.springframework.util.ObjectUtils.isEmpty(beginTime),"开始时间不能为空");
    Validate.isTrue(!org.springframework.util.ObjectUtils.isEmpty(endTime),"结束时间不能为空");
    return this.fiscalYearRepository.findByBeginTimeAndEndTime(beginTime,endTime);
  }

  /**
   * 查询当前租户下配置的财年信息
   *
   * @return
   */
  @Override
  public List<FiscalYearRebateVo> findFiscalYearRebate() {
    //查询当前租户下存在的财年设置
    List<FiscalYearEntity> list = this.fiscalYearRepository.lambdaQuery()
        .eq(FiscalYearEntity::getTenantCode, TenantUtils.getTenantCode())
        .eq(FiscalYearEntity::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
        .eq(FiscalYearEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
        .list();
    if(ObjectUtils.isEmpty(list)){
      return Lists.newArrayList();
    }
    //封装成VO提供给dms
    List<FiscalYearRebateVo> resVo = (List<FiscalYearRebateVo>) this.nebulaToolkitService.copyCollectionByWhiteList(list, FiscalYearEntity.class, FiscalYearRebateVo.class, HashSet.class, ArrayList.class);
    for(FiscalYearRebateVo item : resVo){
      List<FiscalYearDetailEntity> detailEntityList = this.fiscalYearDetailRepository.lambdaQuery()
          .eq(FiscalYearDetailEntity::getTenantCode, TenantUtils.getTenantCode())
          .eq(FiscalYearDetailEntity::getYear, item.getYear())
          .orderByAsc(FiscalYearDetailEntity::getMonth)
          .list();
      if(ObjectUtils.isNotEmpty(detailEntityList)){
        List<FiscalYearDetailDto> resDetailVo = (List<FiscalYearDetailDto>) this.nebulaToolkitService.copyCollectionByWhiteList(detailEntityList, FiscalYearDetailEntity.class, FiscalYearDetailDto.class, HashSet.class, ArrayList.class);
        item.setFiscalYearDetailDtoList(resDetailVo);
      }
    }
    return resVo;
  }
}