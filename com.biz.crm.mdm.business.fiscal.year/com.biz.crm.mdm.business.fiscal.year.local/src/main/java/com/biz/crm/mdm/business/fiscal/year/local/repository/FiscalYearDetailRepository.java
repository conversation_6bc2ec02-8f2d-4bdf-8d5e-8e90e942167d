package com.biz.crm.mdm.business.fiscal.year.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.fiscal.year.local.entity.FiscalYearDetailEntity;
import com.biz.crm.mdm.business.fiscal.year.local.entity.FiscalYearEntity;
import com.biz.crm.mdm.business.fiscal.year.local.mapper.FiscalYearDetailMapper;
import com.biz.crm.mdm.business.fiscal.year.local.mapper.FiscalYearMapper;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearDto;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearLinkageDto;
import com.biz.crm.mdm.business.fiscal.year.sdk.dto.FiscalYearPageDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 财年表的数据库访问类 {@link FiscalYearDetailEntity}
 *
 * <AUTHOR>
 * @Date 2022/2/28 下午3:58
 */
@Component
public class FiscalYearDetailRepository extends ServiceImpl<FiscalYearDetailMapper, FiscalYearDetailEntity> {

  /**
   * 根据财年年份和租户编码删除财年明细
   * @param year 财年年份
   * @param tenantCode 租户编码
   */
  public void deleteByYear(String year, String tenantCode) {
    this.lambdaUpdate()
        .eq(FiscalYearDetailEntity::getYear, year)
        .eq(FiscalYearDetailEntity::getTenantCode,tenantCode)
        .remove();
  }

  /**
   * 根据财年年份和租户编码删除财年明细
   * @param yearList
   * @param tenantCode
   */
  public void deleteByYear(List<String> yearList, String tenantCode) {
    this.lambdaUpdate()
        .in(FiscalYearDetailEntity::getYear, yearList)
        .eq(FiscalYearDetailEntity::getTenantCode,tenantCode)
        .remove();
  }
}
