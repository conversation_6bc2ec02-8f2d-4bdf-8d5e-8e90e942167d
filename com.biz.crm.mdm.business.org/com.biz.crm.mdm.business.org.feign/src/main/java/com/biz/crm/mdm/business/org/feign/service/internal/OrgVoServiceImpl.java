package com.biz.crm.mdm.business.org.feign.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.org.feign.feign.OrgVoFeign;
import com.biz.crm.mdm.business.org.sdk.dto.OrgPaginationDto;
import com.biz.crm.mdm.business.org.sdk.dto.OrgPositionUserQueryDto;
import com.biz.crm.mdm.business.org.sdk.dto.OrgQueryDto;
import com.biz.crm.mdm.business.org.sdk.dto.RelateOrgCodeQueryDto;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgPositionUserVo;
import com.biz.crm.mdm.business.org.sdk.vo.OrgTreeByTypeVo;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.org.sdk.vo.SfaOrgCountVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 组织基本信息sdk实现
 */
@Service
public class OrgVoServiceImpl implements OrgVoService {

    @Autowired(required = false)
    private OrgVoFeign orgVoFeign;

    @Override
    public Page<OrgVo> findByConditions(Pageable pageable, OrgPaginationDto paginationDto) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Page<SfaOrgCountVo> findSfaOrgCount(Pageable pageable, SfaOrgCountVo dto) {
        return orgVoFeign.findSfaOrgCount(pageable, dto).checkFeignResult();
    }

    @Override
    public OrgVo findDetailsById(String id) {
        throw new UnsupportedOperationException();
    }

    @Override
    public OrgVo findByOrgCode(String orgCode) {
        throw new UnsupportedOperationException();
    }

    @Override
    public List<OrgVo> findDetailsByIds(List<String> ids) {
        throw new UnsupportedOperationException();
    }

    @Override
    public List<OrgVo> findByOrgCodes(List<String> orgCodes) {
        return this.orgVoFeign.findByOrgCodes(orgCodes).checkFeignResult();
    }

    @Override
    public List<OrgVo> findAllChildrenById(String id) {
        throw new UnsupportedOperationException();
    }

    @Override
    public List<OrgVo> findAllChildrenByOrgCode(String orgCode) {
        if (!StringUtils.isNotBlank(orgCode)) {
            return null;
        }
        return orgVoFeign.findAllChildrenByOrgCode(orgCode).checkFeignResult();
    }

    @Override
    public List<OrgVo> findAllChildrenByOrgCodes(List<String> orgCodes) {
        if (CollectionUtil.isEmpty(orgCodes)) {
            return Lists.newArrayList();
        }
        return orgVoFeign.findAllChildrenByOrgCodes(orgCodes).checkFeignResult();
    }

    @Override
    public Map<String, List<OrgVo>> findAllChildrenByOrgCodesMap(List<String> orgCodes) {
        if (CollectionUtil.isEmpty(orgCodes)) {
            return Maps.newHashMap();
        }
        return orgVoFeign.findAllChildrenByOrgCodesMap(orgCodes).checkFeignResult();
    }

    @Override
    public List<OrgVo> findAllChildrenByOrgTypes(List<String> orgTypes) {
        throw new UnsupportedOperationException();
    }

    /**
     * 通过企业组织编码查询其所有上级及其本身
     *
     * @param orgCode 企业组织编码
     * @return List<OrgVo> 其所有上级及其本身集合
     */
    @Override
    public List<OrgVo> findAllParentByOrgCode(String orgCode) {
        if (!StringUtils.isNotBlank(orgCode)) {
            return null;
        }
        return orgVoFeign.findAllParentByOrgCode(orgCode).checkFeignResult();
    }

    /**
     * 根据 组织编码集合查询 全部上级（含当前）组织列表
     *
     * @param orgCodes
     * @return
     */
    @Override
    public List<OrgVo> findAllParentByOrgCodes(List<String> orgCodes) {
        if (CollectionUtils.isEmpty(orgCodes)) {
            return null;
        }
        return orgVoFeign.findAllParentByOrgCodes(orgCodes).checkFeignResult();
    }

    /**
     * 根据 组织编码集合查询 全部上级（含当前）组织列表
     *
     * @param orgCodes
     * @return
     */
    @Override
    public Map<String, List<OrgVo>> findAllParentByOrgCodesMap(List<String> orgCodes) {
        if (CollectionUtil.isEmpty(orgCodes)) {
            return Maps.newHashMap();
        }
        return orgVoFeign.findAllParentByOrgCodesMap(orgCodes).checkFeignResult();
    }

    @Override
    public Map<String, OrgTreeByTypeVo> findAllParentTreeByOrgCodes(List<String> orgCodes) {
        return orgVoFeign.findAllParentTreeByOrgCodes(orgCodes).checkFeignResult();
    }

    @Override
    public OrgTreeByTypeVo findAllParentTreeByOrgCode(String orgCode) {
        return orgVoFeign.findAllParentTreeByOrgCode(orgCode).checkFeignResult();
    }

    @Override
    public Set<String> findByOrgQueryDto(OrgQueryDto dto) {
        return orgVoFeign.findByOrgQueryDto(dto).checkFeignResult();
    }

    @Override
    public Map<String, String> findByRelateOrgCodeQueryDto(RelateOrgCodeQueryDto dto) {
        return orgVoFeign.findByRelateOrgCodeQueryDto(dto).checkFeignResult();
    }

    @Override
    public Page<OrgVo> findOrgByConditions(Pageable pageable, OrgPaginationDto dto) {
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
        return orgVoFeign.findOrgByConditions(pageable.getPageNumber(), pageable.getPageSize(), dto).checkFeignResult();
    }

    @Override
    public List<OrgVo> findOrgByOrgNameList(List<String> orgNameList) {
        if (CollectionUtils.isEmpty(orgNameList)) {
            return Lists.newArrayList();
        }
        return orgVoFeign.findOrgByOrgNameList(orgNameList).checkFeignResult();
    }

    @Override
    public List<OrgPositionUserVo> findOrgPositionUserByConditions(OrgPositionUserQueryDto dto) {
        return orgVoFeign.findOrgPositionUserByConditions(dto).checkFeignResult();
    }

    @Override
    public List<OrgVo> findByOrgType(String orgType) {
        return orgVoFeign.findByOrgType(orgType).checkFeignResult();
    }

    @Override
    public Map<String, OrgVo> findOrgVoByOrgCodes(List<String> orgCodeList) {
        if (CollectionUtils.isEmpty(orgCodeList)) {
            return Maps.newHashMap();
        }
        return orgVoFeign.findOrgVoByOrgCodes(orgCodeList).checkFeignResult();
    }

    @Override
    public List<OrgVo> loadOrgListToCache() {
        return orgVoFeign.loadOrgListToCache().checkFeignResult();
    }

    @Override
    public List<OrgVo> findAll() {
        return orgVoFeign.findAllOrg().checkFeignResult();
    }

    @Override
    public List<OrgVo> findByRuleCodeLike(String ruleCode) {
        return orgVoFeign.findByRuleCodeLike(ruleCode).checkFeignResult();
    }

    @Override
    public List<OrgVo> findAllParentTreeByUserName(String userName) {
        return orgVoFeign.findAllParentTreeByUserName(userName).checkFeignResult();
    }
}
