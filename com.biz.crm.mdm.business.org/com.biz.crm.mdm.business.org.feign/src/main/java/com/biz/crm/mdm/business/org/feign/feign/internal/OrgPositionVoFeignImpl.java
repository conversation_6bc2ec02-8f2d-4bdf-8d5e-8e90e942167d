package com.biz.crm.mdm.business.org.feign.feign.internal;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.org.feign.feign.OrgPositionVoFeign;
import com.biz.crm.mdm.business.org.sdk.vo.OrgPositionVo;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 组织行基本信息feign熔断实现
 * <AUTHOR>
 */
@Component
public class OrgPositionVoFeignImpl implements FallbackFactory<OrgPositionVoFeign> {

  @Override
  public OrgPositionVoFeign create(Throwable throwable) {
    return new OrgPositionVoFeign() {

      @Override
      public Result<OrgVo> findByPositionCode(String positionCode) {
        throw new UnsupportedOperationException("根据职位查询组织信息熔断");
      }

      @Override
      public Result<List<OrgPositionVo>> findByOrgCodes(List<String> orgCodes) {
        throw new UnsupportedOperationException("根据组织编码查询组织职位集合熔断");
      }

      @Override
      public Result<List<OrgPositionVo>> findByPositionCodes(List<String> positionCodes) {
        throw new UnsupportedOperationException("根据职位集合查询组织编码集合熔断");
      }
    };
  }
}
