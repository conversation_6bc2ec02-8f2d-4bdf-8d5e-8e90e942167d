package com.biz.crm.mdm.business.org.feign.feign.internal;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.org.feign.feign.OrgRegionVoFeign;
import com.biz.crm.mdm.business.org.sdk.vo.OrgRegionVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 组织行政区域feign熔断实现
 *
 * <AUTHOR>
 * @date 2021/11/16
 */
@Component
public class OrgRegionVoFeignImpl implements FallbackFactory<OrgRegionVoFeign> {

  @Override
  public OrgRegionVoFeign create(Throwable throwable) {
    return new OrgRegionVoFeign() {
      @Override
      public Result<List<OrgRegionVo>> findByOrgCode(String orgCode) {
        throw new UnsupportedOperationException("查询组织关联的行政区域列表熔断");
      }
    };
  }
}
