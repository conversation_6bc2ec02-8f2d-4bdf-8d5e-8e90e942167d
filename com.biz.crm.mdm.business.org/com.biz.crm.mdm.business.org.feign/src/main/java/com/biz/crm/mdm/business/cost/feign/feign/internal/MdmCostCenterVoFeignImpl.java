package com.biz.crm.mdm.business.cost.feign.feign.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.cost.feign.feign.MdmCostCenterVoFeign;
import com.biz.crm.mdm.business.cost.sdk.dto.MdmCostCenterDto;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterOrgVo;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: 行基本信息feign熔断实现
 * @description:
 * @author: Bao Hongbin
 * @create: 2021-11-24 11:22
 */
@Component
public class MdmCostCenterVoFeignImpl implements FallbackFactory<MdmCostCenterVoFeign> {

    @Override
    public MdmCostCenterVoFeign create(Throwable throwable) {
        return new MdmCostCenterVoFeign() {
            @Override
            public Result<Page<MdmCostCenterVo>> findByConditions(Integer page, Integer size, MdmCostCenterDto dto) {
                throw new UnsupportedOperationException("分页查询熔断");
            }

            @Override
            public Result<MdmCostCenterVo> findById(String id) {
                throw new UnsupportedOperationException("根据ID查询信息熔断");
            }

            @Override
            public Result<MdmCostCenterVo> findByCode(String costCode) {
                throw new UnsupportedOperationException("根据编码查询信息熔断");
            }

            @Override
            public Result<List<MdmCostCenterVo>> findByIds(List<String> ids) {
                throw new UnsupportedOperationException("根据ID集合查询信息熔断");
            }

            @Override
            public Result<List<MdmCostCenterVo>> findByCodes(List<String> costCodes) {
                throw new UnsupportedOperationException("根据编码集合查询信息熔断");
            }

            @Override
            public Result<List<MdmCostCenterVo>> findByNames(List<String> names) {
                throw new UnsupportedOperationException("根据名称查询信息进入熔断！");
            }

            @Override
            public Result<List<String>> findOrgCodesByCostCenterCodes(List<String> costCenterCodes) {
                throw new UnsupportedOperationException("根据成本中心编码集合查询组织编码集合熔断");
            }

            @Override
            public Result<List<MdmCostCenterVo>> loadCacheCostCenter() {
                throw new UnsupportedOperationException("成本中心载入缓存进入熔断");
            }

            @Override
            public Result<List<MdmCostCenterVo>> findByOrgCodes(List<String> orgCodes) {
                throw new UnsupportedOperationException("根据组织编码集合查询成本中心进入熔断");
            }

            @Override
            public Result<List<MdmCostCenterOrgVo>> findCostOrgByCodes(List<String> costCodes) {
                throw new UnsupportedOperationException("根据成本中心编码集合查询成本中心和组织关系进入熔断");
            }
        };
    }
}
