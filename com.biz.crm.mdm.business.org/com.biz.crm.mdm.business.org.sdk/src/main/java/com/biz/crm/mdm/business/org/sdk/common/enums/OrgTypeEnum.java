package com.biz.crm.mdm.business.org.sdk.common.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.Getter;

/**
 * @Description 组织类型枚举
 * <AUTHOR>
 * @Date Created in 2021/5/20 下午3:57
 */
@Getter
public enum OrgTypeEnum {
  /**
   * 集团
   */
  GROUP("group", "group", "集团", "1"),
  /**
   * 中心
   */
  COMPANY("company", "company", "中心", "2"),
  /**
   * 大区运营部
   */
  DIVISION("division", "division", "大区运营部", "3"),
  /**
   * 业务部
   */
  REGION("region", "region", "业务部", "4"),
  /**
   * 三级部门
   */
  AREA("area", "area", "三级部门", "5"),
  /**
   * 部门
   */
  DEPARTMENT("department", "department", "部门", "6"),
  ;


  private String key;
  /**
   * 字典编码
   */
  private String dictCode;
  /**
   * 字典值
   */
  private String value;
  /**
   * 字典排序
   */
  private String order;


  OrgTypeEnum(String key, String dictCode, String value, String order) {
    this.key = key;
    this.dictCode = dictCode;
    this.order = order;
    this.value = value;
  }

  public String getKey() {
    return key;
  }

  public String getDictCode() {
    return dictCode;
  }

  public String getValue() {
    return value;
  }

  public String getOrder() {
    return order;
  }

  /**
   * 通过key获取 ProductTypeEnum
   *
   * @param key
   * @return
   */
  public static OrgTypeEnum getByKey(String key) {
    return Arrays.stream(OrgTypeEnum.values()).filter(item -> Objects.equals(item.getKey(), key))
            .findFirst().orElse(null);
  }


  public static Map<String, String> GETMAP = new HashMap<>();

  static {
    for (OrgTypeEnum type : OrgTypeEnum.values()) {
      GETMAP.put(type.getDictCode(), type.getValue());
    }
  }
}
