package com.biz.crm.mdm.business.org.sdk.service;

import com.biz.crm.mdm.business.org.sdk.dto.OrgPaginationDto;
import com.biz.crm.mdm.business.org.sdk.vo.OrgTreeVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 组织树形vo service
 * @date 2021/9/29 上午11:25
 */
public interface OrgTreeVoService {

  /**
   * 查询整棵树
   *
   * @return
   */
  List<OrgTreeVo> findAllStruTree();

  /**
   * 按组织name查询整棵树
   *
   * @param orgName
   * @param enableStatus
   * @return
   */
  List<OrgTreeVo> findByOrgNameStruTree(String orgName, String enableStatus);

  /**
   * 查询整课树  带条件
   *
   * @return
   */
  List<OrgTreeVo> findAllStruTreeByCondition(OrgPaginationDto orgPaginationDto);
}
