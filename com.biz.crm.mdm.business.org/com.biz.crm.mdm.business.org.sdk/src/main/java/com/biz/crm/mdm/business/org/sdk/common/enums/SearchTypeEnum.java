package com.biz.crm.mdm.business.org.sdk.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> rentao
 * @date : 2023/3/1 16:00
 */
@Getter
public enum SearchTypeEnum {

  /**
   * 点击搜索
   */
  CLICK("click", "click", "点击搜索", "1"),

  /**
   * 搜索框搜索
   */
  SEARCH("search", "search", "搜索框搜索", "2"),
  ;


  private String key;
  /**
   * 字典编码
   */
  private String dictCode;
  /**
   * 字典值
   */
  private String value;
  /**
   * 字典排序
   */
  private String order;


  SearchTypeEnum(String key, String dictCode, String value, String order) {
    this.key = key;
    this.dictCode = dictCode;
    this.order = order;
    this.value = value;
  }

  public String getKey() {
    return key;
  }

  public String getDictCode() {
    return dictCode;
  }

  public String getValue() {
    return value;
  }

  public String getOrder() {
    return order;
  }

  /**
   * 通过key获取 ProductTypeEnum
   *
   * @param key
   * @return
   */
  public static SearchTypeEnum getByKey(String key) {
    return Arrays.stream(SearchTypeEnum.values()).filter(item -> Objects.equals(item.getKey(), key))
            .findFirst().orElse(null);
  }


  public static Map<String, String> GETMAP = new HashMap<>();

  static {
    for (SearchTypeEnum type : SearchTypeEnum.values()) {
      GETMAP.put(type.getDictCode(), type.getValue());
    }
  }
}
