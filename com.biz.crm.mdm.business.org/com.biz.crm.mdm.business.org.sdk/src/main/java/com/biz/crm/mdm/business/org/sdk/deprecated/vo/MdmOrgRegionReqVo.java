package com.biz.crm.mdm.business.org.sdk.deprecated.vo;

import com.biz.crm.business.common.sdk.deprecated.vo.UuidVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 组织和行政区域关联表请求vo
 *
 * <AUTHOR>
 * @date 2020-11-17 10:42:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MdmOrgRegionReqVo", description = "组织和行政区域关联表")
@Deprecated
public class MdmOrgRegionReqVo extends UuidVo {

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("国家编码")
  private String countryCode;

  @ApiModelProperty("省份编码")
  private String provinceCode;

  @ApiModelProperty("城市编码")
  private String cityCode;

  @ApiModelProperty("区县编码")
  private String districtCode;

  @ApiModelProperty("乡镇编码")
  private String townshipCode;

  @ApiModelProperty("区域集合")
  private List<MdmOrgRegionReqVo> regionList;

}
