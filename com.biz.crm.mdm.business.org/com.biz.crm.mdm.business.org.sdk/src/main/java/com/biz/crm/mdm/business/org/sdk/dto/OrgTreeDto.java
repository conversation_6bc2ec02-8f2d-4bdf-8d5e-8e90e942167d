package com.biz.crm.mdm.business.org.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TreeDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> rentao
 * @date : 2023/3/1 15:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "组织区域dto")
public class OrgTreeDto extends TreeDto {

  @ApiModelProperty("搜索类型 com.biz.crm.mdm.business.org.sdk.common.enums.SearchTypeEnum")
  private String searchType = "click";
}
