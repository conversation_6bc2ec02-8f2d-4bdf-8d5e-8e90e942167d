package com.biz.crm.mdm.business.org.sdk.service;

import com.biz.crm.mdm.business.org.sdk.vo.OrgOaOrgVo;

import java.util.List;
import java.util.Map;

/**
 * 组织和OA组织关系
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/7/24 10:26
 */
public interface OrgOaOrgVoService {


    /**
     * 按组织code查询有效的OA组织
     *
     * @param orgCode
     * @return
     */
    List<OrgOaOrgVo> findByOrgCode(String orgCode);

    /**
     * 按组织code集合查询有效的OA组织熔断
     *
     * @param orgCodes
     * @return
     */
    Map<String, List<OrgOaOrgVo>> findByOrgCodes(List<String> orgCodes);

}
