package com.biz.crm.mdm.business.org.sdk.deprecated.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织表返回vo
 *
 * <AUTHOR>
 * @date 2020-12-07 09:55:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "组织树形结构返回VO")
@Deprecated
public class MdmOrgTreeRespVo {

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("组织名称")
  private String orgName;

  @ApiModelProperty("组织类型（字典mdm_org_type）")
  private String orgType;

  @ApiModelProperty("组织描述")
  private String orgDesc;

  @ApiModelProperty("上级组织编码")
  private String parentCode;

  private String ruleCode;

  private Integer levelNum;

  @ApiModelProperty("直接下级组织")
  private List<MdmOrgTreeRespVo> children;

}
