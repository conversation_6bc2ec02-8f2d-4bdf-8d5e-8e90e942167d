package com.biz.crm.mdm.business.org.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @className: com.biz.crm.mdm.business.org.sdk.vo.SfaOrgCountVo
 * @description: 促销员，组织汇总查询Vo
 * @author: xiaopeng.zhang
 * @create: 2024-08-19 9:25
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SFA组织汇总查询Vo")
public class SfaOrgCountVo extends TenantFlagOpVo {

    // 组织基础信息
    /**
     * 组织编码
     */
    @ApiModelProperty("组织编码")
    private String orgCode;
    /**
     * 组织名称
     */
    @ApiModelProperty("组织名称")
    private String orgName;
    /**
     * 组织类型
     */
    @ApiModelProperty("组织类型（字典mdm_org_type）")
    private String orgType;
    /**
     * 组织描述
     */
    @ApiModelProperty("组织描述")
    private String orgDesc;
    /**
     * 上级组织编码
     */
    @ApiModelProperty("上级组织编码")
    private String parentCode;

    /**
     * 上级组织名称
     */
    @ApiModelProperty("上级组织名称")
    private String parentName;

    /**
     * 规则code查询用
     */
    @ApiModelProperty("对应SAP组织编码成本中心编码")
    private String ruleCode;

    /**
     * 层级等级查询用
     */
    @ApiModelProperty("层级等级查询用")
    private Integer levelNum;

    /**
     * 关联下级组织编码
     */
    @ApiModelProperty("关联下级组织编码")
    private List<String> childOrgCodeList;

    @ApiModelProperty(name = "startTime", value = "开始时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(name = "endTime", value = "结束时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    //报表字段

    /**
     * 执行场次
     */
    @ApiModelProperty("执行场次")
    private BigDecimal executeCount;

    /**
     * 执行门店数数量
     */
    @ApiModelProperty("执行门店数数量")
    private BigDecimal executeTerminalNum;

    /**
     * 销售数量
     */
    @ApiModelProperty("销售数量")
    private BigDecimal salesAmount;

    /**
     * 45提以上场次数量（提报数量大于45以上的场次数）
     */
    @ApiModelProperty("45提以上场次数量")
    private BigDecimal upExecuteCount;

    /**
     * 场均达标率
     */
    @ApiModelProperty("场均达标率")
    private BigDecimal attainmentRate;

    /**
     * 场均达标率
     */
    @ApiModelProperty("场均达标率")
    private String attainmentRateStr;

    /**
     * 场均销量
     */
    @ApiModelProperty("场均销量")
    private BigDecimal averageSales;

    /**
     * 高端品销量
     */
    @ApiModelProperty("高端品销量")
    private BigDecimal heightProductSales;

    /**
     * 高端品占比
     */
    @ApiModelProperty("高端品占比")
    private BigDecimal heightProductRate;

    /**
     * 高端品占比
     */
    @ApiModelProperty("高端品占比")
    private String heightProductRateStr;

    /**
     * 试饮场次
     */
    @ApiModelProperty("试饮场次")
    private BigDecimal drinkCount;

    /**
     * 试饮率
     */
    @ApiModelProperty("试饮率")
    private BigDecimal drinkRate;

    /**
     * 试饮率
     */
    @ApiModelProperty("试饮率")
    private String drinkRateStr;

    /**
     * 角色编码
     */
    @ApiModelProperty("角色编码")
    private String roleCode;


}
