package com.biz.crm.mdm.business.org.sdk.deprecated.vo;

import com.biz.crm.business.common.sdk.deprecated.vo.CrmTreeTenVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 职位返回vo
 *
 * <AUTHOR>
 * @date 2020-11-17 20:23:13
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "以职位维度的职位及关联用户、组织、上级信息查询请求vo")
public class MdmPositionUserOrgPageReqVo extends CrmTreeTenVo {

    @ApiModelProperty("职位编码（模糊查询）")
    private String positionCode;

    @ApiModelProperty("组织编码（模糊查询）")
    private String orgCode;

    @ApiModelProperty("职位级别编码（模糊查询）")
    private String positionLevelCode;

    @ApiModelProperty("用户登录名（模糊查询）")
    private String userName;

    @Deprecated
    @ApiModelProperty("用户编码（模糊查询）")
    private String userCode;

    @ApiModelProperty("用户姓名（模糊查询用户名）")
    private String fullName;

    @ApiModelProperty("职位名称（模糊查询职位名称）")
    private String positionName;

    @ApiModelProperty("组织名称（模糊查询组织名称）")
    private String orgName;

    @ApiModelProperty("模糊查询用户姓名、职位名称、组织名称")
    private String unionNameFuzzyQuery;

    @ApiModelProperty("模糊查询用户登录名和用户姓名")
    private String userNameOrFullName;

    @ApiModelProperty("是否主职位 0否 1是 （精确查询）")
    private String primaryFlag;

    @ApiModelProperty("上级职位编码（精确查询这个职位编码直接关联的职位）")
    private String parentCode;

    @ApiModelProperty("关联组织编码（查询这个组织直接关联的职位）")
    private String thisOrgCode;

    @ApiModelProperty("职位编码（精确查询这个职位）")
    private String thisPositionCode;

    @ApiModelProperty("职位级别编码（查询这个职位级别的职位）")
    private String thisPositionLevelCode;

    @ApiModelProperty("用户登录名（精确查询这个登录名关联的全部职位）")
    private String thisUserName;

    @ApiModelProperty("组织编码（查这个组织及全部下级组织的职位）")
    private String allUnderThisOrgCode;

    @ApiModelProperty("组织编码（查这个组织（不含该组织）的全部下级组织的职位）")
    private String allUnderThisOrgCodeExcludeSelf;

    @ApiModelProperty("职位编码（查这个职位的全部下级职位）")
    private String allUnderThisPositionCode;

    @ApiModelProperty("职位编码（查这个职位（不含该职位）的全部下级职位）")
    private String allUnderThisPositionCodeExcludeSelf;

    @ApiModelProperty("是否返回未关联用户的职位（默认true） true：要返回；false:不返回")
    private Boolean includeEmptyUserPosition;

    @ApiModelProperty("筛选用户的启用状态")
    private String userEnableStatus;

    @ApiModelProperty("组织编码集合（批量查询这些组织直接关联的职位）")
    private List<String> orgCodeList;

    @ApiModelProperty("职位编码（精确查询这些职位）")
    private List<String> positionCodeList;

    @ApiModelProperty("排除这些用户登录名")
    private List<String> notInUserNameList;
}
