package com.biz.crm.mdm.business.cost.sdk.enent;

import com.biz.crm.mdm.business.cost.sdk.dto.MdmCostCenterEventBatchDto;
import com.biz.crm.mdm.business.cost.sdk.dto.MdmCostCenterEventDto;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterVo;
import com.biz.crm.mdm.business.org.sdk.dto.OrgLogEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

import java.util.List;

/**
 * 成本中心
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/20 14:40
 */
public interface MdmCostCenterLogEventListener extends NebulaEvent {


    /**
     * 新增成本中心
     *
     * @param dto
     */
    default void onCreate(MdmCostCenterEventDto dto) {
    }

    /**
     * 删除成本中心
     *
     * @param dto
     */
    default void onDelete(MdmCostCenterEventBatchDto dto) {
    }

    /**
     * 更新成本中心
     *
     * @param dto
     */
    default void onUpdate(MdmCostCenterEventDto dto) {
    }


}
