package com.biz.crm.mdm.business.org.sdk.deprecated.vo;

import com.biz.crm.business.common.sdk.deprecated.vo.CrmTreeTenVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织表返回vo
 *
 * <AUTHOR>
 * @date 2020-11-17 20:23:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "组织表")
@Deprecated
public class MdmOrgRespVo extends CrmTreeTenVo {

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("组织名称")
  private String orgName;

  @ApiModelProperty("组织类型（字典mdm_org_type）")
  private String orgType;

  @ApiModelProperty("组织描述")
  private String orgDesc;

  @ApiModelProperty("上级组织编码")
  private String parentCode;

  @ApiModelProperty("对应SAP组织编码")
  private String sapOrgCode;

  @ApiModelProperty("对应SAP组织编码利润中心编码")
  private String sapProfitOrgCode;

  @ApiModelProperty("对应SAP组织编码成本中心编码")
  private String sapCostOrgCode;

  @ApiModelProperty("数据源[数据字典:mdm_org_data_source]")
  private String dataSource;

  @ApiModelProperty("上级组织名称")
  private String parentName;

  private List<MdmOrgRespVo> respVos;
}
