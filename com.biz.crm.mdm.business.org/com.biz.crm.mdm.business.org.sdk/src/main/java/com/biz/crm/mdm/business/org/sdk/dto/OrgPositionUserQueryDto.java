package com.biz.crm.mdm.business.org.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 用户岗位组织信息
 *
 * <AUTHOR>
 * @date 2024-5-28 20:23:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "组织分页查询dto")
public class OrgPositionUserQueryDto extends TenantFlagOpDto {

  /**
   * 用户编码集合
   */
  @ApiModelProperty("用户编码集合")
  private List<String> userNames;

}
