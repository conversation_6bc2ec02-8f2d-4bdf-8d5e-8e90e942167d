package com.biz.crm.mdm.business.org.sdk.vo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.biz.crm.mdm.business.org.sdk.common.enums.OrgTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: yangrui
 * @Date: 2025-04-24 13:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrgTreeByTypeVo {

    private String relaOrgCode;
    private OrgVo relaOrg;
    private OrgVo zeroOrg;
    private OrgVo divisionOrg;
    private OrgVo regionOrg;
    private OrgVo areaOrg;
    private List<OrgVo> orgVoList;

    public String getZeroOrgCode() {
        return Optional.ofNullable(zeroOrg).map(OrgVo::getOrgCode).orElse(null);
    }

    public String getZeroOrgName() {
        return Optional.ofNullable(zeroOrg).map(OrgVo::getOrgName).orElse(null);
    }

    public String getDivisionOrgCode() {
        return Optional.ofNullable(divisionOrg).map(OrgVo::getOrgCode).orElse(null);
    }

    public String getDivisionOrgName() {
        return Optional.ofNullable(divisionOrg).map(OrgVo::getOrgName).orElse(null);
    }

    public String getRegionOrgCode() {
        return Optional.ofNullable(regionOrg).map(OrgVo::getOrgCode).orElse(null);
    }

    public String getRegionOrgName() {
        return Optional.ofNullable(regionOrg).map(OrgVo::getOrgName).orElse(null);
    }

    public String getAreaOrgCode() {
        return Optional.ofNullable(areaOrg).map(OrgVo::getOrgCode).orElse(null);
    }

    public String getAreaOrgName() {
        return Optional.ofNullable(areaOrg).map(OrgVo::getOrgName).orElse(null);
    }

    public String getRelaOrgName() {
        return Optional.ofNullable(relaOrg).map(OrgVo::getOrgName).orElse(null);
    }

    public void init() {
        if (CollUtil.isEmpty(orgVoList)) {
            return;
        }
        // 按照组织类型分组
        Map<String, OrgVo> orgVoMapByType = orgVoList.stream().collect(
                Collectors.toMap(OrgVo::getOrgType, o -> o, (a, b) -> a));
        OrgVo zero = orgVoMapByType.get(OrgTypeEnum.COMPANY.getKey());
        OrgVo division = orgVoMapByType.get(OrgTypeEnum.DIVISION.getKey());
        OrgVo region = orgVoMapByType.get(OrgTypeEnum.REGION.getKey());
        OrgVo area = orgVoMapByType.get(OrgTypeEnum.AREA.getKey());

        this.setZeroOrg(zero);
        this.setDivisionOrg(division);
        this.setRegionOrg(region);
        this.setAreaOrg(area);

        relaOrg = orgVoList.stream()
                .filter(o -> StrUtil.equals(o.getOrgCode(), relaOrgCode))
                .findAny().orElse(null);
    }

    public OrgTreeByTypeVo(List<OrgVo> orgVoList, String relaOrgCode) {
        this.orgVoList = orgVoList;
        this.relaOrgCode = relaOrgCode;
    }


}
