package com.biz.crm.mdm.business.org.sdk.service;

import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 组织缓存 service
 * @date 2021/9/29 上午11:25
 */
public interface OrgCacheService {

  /**
   * 按组织orgCodes 刷新缓存
   *
   * @param orgCodes
   */
  void refreshByOrgCodes(List<String> orgCodes);

  /**
   * 刷新缓存
   */
  void refreshAll();

  /**
   * 清空缓存
   */
  void removeAll();

  /**
   * 根据组织编码获取组织详情,如果缓存中不存在，那么从数据库中获取并缓存。
   *
   * @param orgCode 组织编码
   * @return
   */
  OrgVo findByOrgCode(String orgCode);

  /**
   * 根据组织编码获取组织的子节点
   *
   * @param orgCode
   * @return
   */
  List<OrgVo> findChildrenByOrgCode(String orgCode);

  /**
   * 根据组织编码获取该组织所在组织树的根节点
   *
   * @param orgCode 组织编码
   * @return
   */
  OrgVo findTopParentByOrgCode(String orgCode);

  /**
   * 根据组织编码获取组织及组织全部上级
   *
   * @param orgCode 组织编码
   * @return
   */
  List<OrgVo> findAllParentIncludeSelf(String orgCode);

  /**
   * 根据组织编码集合获取组织及组织全部上级
   *
   * @param orgCodes 组织编码集合
   * @return
   */
  List<OrgVo> findAllParentIncludeSelf(List<String> orgCodes);

  /**
   * 根据组织编码获取组织（不含自己）全部上级
   *
   * @param orgCode 组织编码
   * @return
   */
  List<OrgVo> findAllParentExcludeSelf(String orgCode);

  /**
   * 根据组织编码集合获取组织详情
   *
   * @param orgCodes 组织编码集合
   * @return
   */
  List<OrgVo> findByOrgCodes(List<String> orgCodes);

  /**
   * 根据组织编码获取组织及组织全部下级
   *
   * @param orgCode 组织编码
   * @return
   */
  List<OrgVo> findAllChildrenIncludeSelf(String orgCode);

  /**
   * 根据组织编码集合获取组织及组织全部下级
   *
   * @param orgCodes 组织编码集合
   * @return
   */
  List<OrgVo> findAllChildrenIncludeSelf(List<String> orgCodes);

  /**
   * 根据组织编码获取组织（不含自己）全部下级
   *
   * @param orgCode 组织编码
   * @return
   */
  List<OrgVo> findAllChildrenExcludeSelf(String orgCode);

  /**
   * 根据组织编码获取组织（不含自己）全部下级
   *
   * @param orgCodes 组织编码
   * @return
   */
  List<OrgVo> findAllChildrenExcludeSelf(List<String> orgCodes);

  /**
   * 根据组织编码获取组织及组织全部上级（只查启用）
   *
   * @param orgCode 组织编码
   * @return
   */
  List<OrgVo> findAllEnableParentIncludeSelf(String orgCode);

  /**
   * 根据组织编码集合获取组织及组织全部上级（只查启用）
   *
   * @param orgCodes 组织编码集合
   * @return
   */
  List<OrgVo> findAllEnableParentIncludeSelf(List<String> orgCodes);

  /**
   * 根据组织编码获取组织及组织全部下级（只查启用）
   *
   * @param orgCode 组织编码
   * @return
   */
  List<OrgVo> findAllEnableChildrenIncludeSelf(String orgCode);

  /**
   * 根据组织编码集合获取组织及组织全部下级（只查启用）
   *
   * @param orgCodes 组织编码集合
   * @return
   */
  List<OrgVo> findAllEnableChildrenIncludeSelf(List<String> orgCodes);

  /**
   * 根据组织编码获取组织（不含自己）全部下级（只查启用）
   *
   * @param orgCode 组织编码
   * @return
   */
  List<OrgVo> findAllEnableChildrenExcludeSelf(String orgCode);

  /**
   * 根据组织编码获取组织及组织全部上级组织编码
   *
   * @param orgCode 组织编码
   * @return
   */
  List<String> findAllParentOrgCodeIncludeSelf(String orgCode);

  /**
   * 根据组织编码集合获取组织及组织全部上级组织编码
   *
   * @param orgCodes 组织编码集合
   * @return
   */
  List<String> findAllParentOrgCodeIncludeSelf(List<String> orgCodes);

  /**
   * 根据组织编码获取组织（不含自己）全部上级组织编码
   *
   * @param orgCode 组织编码
   * @return
   */
  List<String> findAllParentOrgCodeExcludeSelf(String orgCode);

  /**
   * 根据组织编码获取组织（不含自己）全部上级组织编码
   *
   * @param orgCodes 组织编码
   * @return
   */
  List<String> findAllParentOrgCodeExcludeSelf(List<String> orgCodes);

  /**
   * 根据组织编码集合获取组织（不含参数中任何一个）全部下级
   *
   * @param orgCodes 组织编码集合
   * @return
   */
  List<OrgVo> findAllChildrenExcludeAnySelf(List<String> orgCodes);

  /**
   * 根据组织编码集合获取组织（不含参数中任何一个）全部下级组织编码
   *
   * @param orgCodes 组织编码集合
   * @return
   */
  List<String> findAllChildrenOrgCodeExcludeAnySelf(List<String> orgCodes);

  /**
   * 根据组织编码获取组织及组织全部上级组织编码（只查启用）
   *
   * @param orgCode 组织编码
   * @return
   */
  List<String> findAllEnableParentOrgCodeIncludeSelf(String orgCode);

  /**
   * 根据组织编码集合获取组织及组织全部上级组织编码（只查启用）
   *
   * @param orgCodes 组织编码集合
   * @return
   */
  List<String> findAllEnableParentOrgCodeIncludeSelf(List<String> orgCodes);

  /**
   * 根据组织编码获取组织（不含自己）全部上级（只查启用）
   *
   * @param orgCode 组织编码
   * @return
   */
  List<OrgVo> findAllEnableParentExcludeSelf(String orgCode);

  /**
   * 根据组织编码获取组织（不含自己）全部上级组织编码（只查启用）
   *
   * @param orgCode 组织编码
   * @return
   */
  List<String> findAllEnableParentOrgCodeExcludeSelf(String orgCode);

  /**
   * 根据组织编码获取组织及组织全部下级组织编码（只查启用）
   *
   * @param orgCode 组织编码
   * @return
   */
  List<String> findAllEnableChildrenOrgCodeIncludeSelf(String orgCode);

  /**
   * 根据组织编码集合获取组织及组织全部下级组织编码（只查启用）
   *
   * @param orgCodes 组织编码集合
   * @return
   */
  List<String> findAllEnableChildrenOrgCodeIncludeSelf(List<String> orgCodes);

  /**
   * 根据组织编码获取组织（不含自己）全部下级组织编码（只查启用）
   *
   * @param orgCode 组织编码
   * @return
   */
  List<String> findAllEnableChildrenOrgCodeExcludeSelf(String orgCode);
}
