package com.biz.crm.mdm.business.org.sdk.dto;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/6/30
 */
@Data
public class OrgQueryDto {
  /** 组织名称模糊查询 */
  private String orgName;
  /** 删除标记 */
  private String delFlag = DelFlagStatusEnum.NORMAL.getCode();

  /**
   * 启用标记
   */
  private String enableStatus;


  /**
   * 组织类型
   */
  private String orgType;

}
