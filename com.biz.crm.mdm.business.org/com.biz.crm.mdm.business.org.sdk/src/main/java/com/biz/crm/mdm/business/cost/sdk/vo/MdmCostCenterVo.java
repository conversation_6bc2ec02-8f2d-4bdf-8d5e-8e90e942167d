package com.biz.crm.mdm.business.cost.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * 成本中心表实体类
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/20 11:00
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "组织表")
public class MdmCostCenterVo extends TenantFlagOpVo {

    private static final long serialVersionUID = -8648715650478891283L;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("生效时间")
    @Column(name = "start_time", columnDefinition = "datetime COMMENT '生效时间'")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty("失效时间")
    @Column(name = "end_time", columnDefinition = "datetime COMMENT '失效时间'")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("数据同步时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date syncUpdateTime;

    @ApiModelProperty("数据源[数据字典:mdm_data_source]")
    private String dataSource;

    @ApiModelProperty("组织信息")
    private List<MdmCostCenterOrgVo> orgList;

    /**
     * 仅用于前端展示
     */
    @ApiModelProperty("成本中心名称-公司编码")
    private String costCenterNameAndCompanyCode;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

}
