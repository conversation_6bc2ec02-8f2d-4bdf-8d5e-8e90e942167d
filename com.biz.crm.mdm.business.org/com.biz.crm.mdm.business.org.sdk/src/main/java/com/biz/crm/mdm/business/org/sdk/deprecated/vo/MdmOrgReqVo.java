package com.biz.crm.mdm.business.org.sdk.deprecated.vo;

import com.biz.crm.business.common.sdk.deprecated.vo.CrmTreeTenVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Collection;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 组织表请求vo
 *
 * <AUTHOR>
 * @date 2020-11-17 20:23:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MdmOrgReqVo", description = "组织表")
@Deprecated
public class MdmOrgReqVo extends CrmTreeTenVo {

  @ApiModelProperty("ID集合")
  private List<String> ids;

  @ApiModelProperty("企业组织编码集合")
  private Collection<String> orgCodeList;

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("组织名称")
  private String orgName;

  @ApiModelProperty("组织类型")
  private String orgType;

  @ApiModelProperty("组织类型集合")
  private List<String> orgTypeList;

  @ApiModelProperty("组织描述")
  private String orgDesc;

  @ApiModelProperty("上级组织编码")
  private String parentCode;

  @ApiModelProperty("对应SAP组织编码")
  private String sapOrgCode;

  @ApiModelProperty("对应SAP组织编码利润中心编码")
  private String sapProfitOrgCode;

  @ApiModelProperty("对应SAP组织编码成本中心编码")
  private String sapCostOrgCode;

  @ApiModelProperty("数据源[数据字典:mdm_org_data_source]")
  private String dataSource;

  @ApiModelProperty("票扣兑付方式")
  private String ticketCashType;

  @ApiModelProperty("公共树编码字段")
  private String code;

}
