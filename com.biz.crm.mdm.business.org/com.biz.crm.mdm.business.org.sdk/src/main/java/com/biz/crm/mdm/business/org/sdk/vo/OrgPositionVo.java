package com.biz.crm.mdm.business.org.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 组织职位关系实体类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel("组织职位关系实体类")
public class OrgPositionVo extends TenantVo {
  private static final long serialVersionUID = 3026459516324389289L;

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("组织名称")
  private String orgName;

  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty("职位名称")
  private String positionName;

}
