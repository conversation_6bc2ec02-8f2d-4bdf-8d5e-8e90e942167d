package com.biz.crm.mdm.business.cost.local.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@EntityScan(basePackages = " com.biz.crm.mdm.business.cost.local.entity")
@ComponentScan(basePackages = {"com.biz.crm.mdm.business.cost"})
public class MdmCostCenterLocalConfig {
}
