<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
  namespace="com.biz.crm.mdm.business.org.local.mapper.OrgPositionMapper">

  <resultMap id="OrgMap" type="com.biz.crm.mdm.business.org.local.entity.OrgPosition">
    <id column="id" property="id"/>
    <result column="orgCode" property="orgCode"/>
    <result column="positonCode" property="positionCode"/>
  </resultMap>

  <sql id="OrgPosition">
        op.id as id ,
        op.org_code as orgCode ,
        op.position_code as positionCode
  </sql>

  <delete id="deleteByPositionCode">
    DELETE FROM mdm_org_position WHERE position_code = #{positionCode} and tenant_code = #{tenantCode}
  </delete>
    <select id="findByPositionCodeList" resultType="com.biz.crm.mdm.business.org.sdk.vo.OrgPositionVo">
        select mop.id,
        mp.position_code,
        mp.position_name,
        mo.org_code,
        mo.org_name
        from mdm_org_position mop
        left join mdm_position mp on mp.position_code = mop.position_code and mp.tenant_code = mop.tenant_code
        left join mdm_org mo on mo.org_code = mop.org_code and mo.tenant_code = mop.tenant_code
        where mp.tenant_code = #{tenantCode}
        and mp.del_flag = '${@<EMAIL>()}'
        and mp.position_code in
        <foreach item="item" index="index" collection="positionCodes" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
