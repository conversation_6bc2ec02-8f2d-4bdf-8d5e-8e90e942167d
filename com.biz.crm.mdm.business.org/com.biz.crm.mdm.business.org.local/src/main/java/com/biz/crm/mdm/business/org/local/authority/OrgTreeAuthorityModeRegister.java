package com.biz.crm.mdm.business.org.local.authority;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.mdm.business.org.local.entity.Org;
import com.biz.crm.mdm.business.org.local.service.OrgService;
import com.biz.crm.mdm.business.position.sdk.service.PositionOrgVoService;
import com.bizunited.nebula.mars.sdk.register.SelectAuthorityModeRegister;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: heguanyun
 * @Date: 2022/4/7 15:28 description:匹配当前登录者/操作者所属组织及其下级组织
 */
@Component
@Lazy
public class OrgTreeAuthorityModeRegister implements SelectAuthorityModeRegister {

  @Autowired(required = false)
  private ApplicationContext applicationContext;

  @Autowired(required = false)
  private PositionOrgVoService positionOrgVoService;


  @Override
  public String modeKey() {
    return "orgTreeAuthorityModeRegister";
  }

  @Override
  public String modeName() {
    return "按照当前登录者/操作者所属组织及其下级组织进行组织维度的值确认";
  }

  @Override
  public String controlKey() {
    return "orgTreeAuthorityModeRegister";
  }

  @Override
  public String groupCode() {
    return "org_group";
  }


  @Override
  public int sort() {
    return 4;
  }

  @Override
  public boolean isArrayValue() {
    return true;
  }

  @Override
  public boolean isStaticValue() {
    return false;
  }

  @Override
  public Class<?> modeValueClass() {
    return String.class;
  }

  @Override
  public Object staticValue(String[] staticValues) {
    return CommonConstant.NOT_AUTHORITY_ARR;
  }

  @Override
  public Object dynamicValue(UserIdentity loginDetails, String modeGroupCode) {
    String identityType = loginDetails.getIdentityType();
    // 如果不是后台管理用户，就不按组织字段进行权限控制
    if (!StringUtils.equals(identityType, "u")) {
      return CommonConstant.NOT_AUTHORITY_ARR;
    }
    OrgService orgService = applicationContext.getBean(OrgService.class);
    AbstractCrmUserIdentity loginUserDetails = (AbstractCrmUserIdentity) loginDetails;
    Object orgCodeObject = loginUserDetails.invokeFieldValue("orgCode");
    if (orgCodeObject == null) {
      return CommonConstant.NOT_AUTHORITY_ARR;
    }

    List<String> orgCodes = Lists.newArrayList(orgCodeObject.toString());
    Set<String> orgsByPositionCode = positionOrgVoService.findOrgsByPositionCode(loginUserDetails.getPostCode());
    if(CollectionUtil.isNotEmpty(orgsByPositionCode)){
      orgCodes.addAll(orgsByPositionCode);
    }
    List<Org> children = orgService.findAllChildrenByOrgCodes(orgCodes);
    if (CollectionUtil.isNotEmpty(children)) {
      orgCodes.addAll(children.stream().map(Org::getOrgCode).distinct().collect(Collectors.toList()));
    }
    orgCodes = orgCodes.stream().filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
    if (CollectionUtil.isEmpty(orgCodes)) {
      return CommonConstant.NOT_AUTHORITY_ARR;
    }
    return orgCodes.toArray(new String[0]);
  }

  @Override
  public String converterKey() {
    return "chartArrayMarsAuthorityAstConverter";
  }
}
