package com.biz.crm.mdm.business.org.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.mdm.business.org.local.entity.OrgPosition;
import com.biz.crm.mdm.business.org.sdk.vo.OrgPositionVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 组织职位关系实体类 mybatis-plus接口类
 *
 * <AUTHOR>
 * @date 2021/09/26
 */
public interface OrgPositionMapper extends BaseMapper<OrgPosition> {

  /**
   * 按职位code删除
   *
   * @param positionCode
   * @param tenantCode
   */
   void deleteByPositionCode(@Param("positionCode") String positionCode, @Param("tenantCode") String tenantCode);

  /**
   * 根据职位编码获取组织职位信息
   * @param positionCodes
   * @param tenantCode
   * @return
   */
   List<OrgPositionVo> findByPositionCodeList( @Param("positionCodes") List<String> positionCodes, @Param("tenantCode")  String tenantCode);
}
