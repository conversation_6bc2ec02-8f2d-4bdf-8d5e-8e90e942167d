package com.biz.crm.mdm.business.org.local.controller;

import com.biz.crm.business.common.sdk.model.Result;

import com.biz.crm.mdm.business.org.sdk.service.OrgRegionVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgRegionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 组织和行政区域关联表
 *
 * <AUTHOR>
 * @date 2021-10-15 10:42:46
 */
@Slf4j
@RestController
@RequestMapping("/v1/org/orgRegion")
@Api(tags = "组织管理：OrgRegionVo：关联行政区域")
public class OrgRegionVoController {

  @Autowired(required = false)
  private OrgRegionVoService orgRegionVoService;

  @ApiOperation(value = "查询组织关联的行政区域列表（不分页）")
  @GetMapping("/findByOrgCode")
  public Result<List<OrgRegionVo>> findByOrgCode(@RequestParam String orgCode) {
    try {
      List<OrgRegionVo> list = this.orgRegionVoService.findByOrgCode(orgCode);
      return Result.ok(list);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
