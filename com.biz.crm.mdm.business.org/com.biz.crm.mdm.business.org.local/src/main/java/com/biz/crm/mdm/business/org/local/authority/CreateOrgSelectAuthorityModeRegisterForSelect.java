package com.biz.crm.mdm.business.org.local.authority;

import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.mdm.business.org.local.service.OrgService;
import com.bizunited.nebula.mars.sdk.register.SelectAuthorityModeRegister;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 按照固定组织
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/8/2 14:12
 */
@Component
public class CreateOrgSelectAuthorityModeRegisterForSelect implements SelectAuthorityModeRegister {


    @Autowired(required = false)
    private OrgService orgService;

    @Override
    public String modeKey() {
        return "createOrgSelectAuthorityModeRegisterForSelect";
    }

    @Override
    public String modeName() {
        return "按照固定组织";
    }

    @Override
    public String controlKey() {
        return "createOrgSelectAuthorityModeRegisterForSelect_selectOrgTree";
    }

    @Override
    public int sort() {
        return 3;
    }

    @Override
    public String groupCode() {
        return "create_org_group";
    }

    @Override
    public String converterKey() {
        return "chartArrayMarsAuthorityAstConverter";
    }

    @Override
    public boolean isArrayValue() {
        return true;
    }

    @Override
    public boolean isStaticValue() {
        return true;
    }

    @Override
    public Class<?> modeValueClass() {
        return String.class;
    }

    @Override
    public Object staticValue(String[] staticValues) {
        if (null == staticValues || staticValues.length == 0) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        return staticValues;
    }

    @Override
    public Object dynamicValue(UserIdentity loginDetails, String modeGroupCode) {
        return CommonConstant.NOT_AUTHORITY_ARR;
    }
}
