package com.biz.crm.mdm.business.org.local.service;

import com.biz.crm.mdm.business.org.local.entity.OrgPosition;
import com.biz.crm.mdm.business.org.sdk.dto.OrgPositionBindDto;

import java.util.List;

/**
 * 组织职位接口
 *
 * <AUTHOR>
 * @date 2021/09/27
 */
public interface OrgPositionService {

  /**
   * 创建组织职位关系
   * <pre>
   * 如果创建时职位已存在组织关系那么
   * 如果不存在，重新创建
   * </pre>
   *
   * @param orgPositions
   * @return
   */
  List<OrgPosition> create(List<OrgPosition> orgPositions);

  /**
   * 通过职位code集合查询 职位组织关系
   *
   * @param positionCodes
   * @return
   */
  List<OrgPosition> findByPositionCodes(List<String> positionCodes);

  /**
   * 按组织code 集合查询
   *
   * @param orgCodes
   * @return
   */
  List<OrgPosition> findByOrgCodes(List<String> orgCodes);

  /**
   * 按职位code删除 关系
   *
   * @param positionCodes
   */
  void deleteByPositionCodes(List<String> positionCodes);


  /**
   * 企业组织绑定职位
   * @param orgPositionBindDto
   */
  void bindPosition(OrgPositionBindDto orgPositionBindDto);

  /**
   * 企业组织解除与职位绑定
   * @param ids
   */
  void unbindPosition(List<String> ids);
}
