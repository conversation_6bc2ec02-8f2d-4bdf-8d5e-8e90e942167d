package com.biz.crm.mdm.business.org.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.auth.sdk.service.UrlApiService;
import com.biz.crm.business.common.auth.sdk.vo.UrlAddressVo;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.base.constant.ryytn.RyConstant;
import com.biz.crm.business.common.base.util.BusinessHttpUtil;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.base.util.ryytn.RySignHeaderUtil;
import com.biz.crm.business.common.log.sdk.constant.ExternalLogGlobalConstants;
import com.biz.crm.business.common.log.sdk.dto.ExternalLogDetailDto;
import com.biz.crm.business.common.log.sdk.service.ExternalLogVoService;
import com.biz.crm.business.common.log.sdk.util.ExternalLogUtil;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.org.local.entity.OrgOaOrg;
import com.biz.crm.mdm.business.org.local.repository.OrgOaOrgRepository;
import com.biz.crm.mdm.business.org.local.service.OrgOaOrgPullDataService;
import com.biz.crm.mdm.business.org.local.service.OrgOaOrgService;
import com.biz.crm.mdm.business.org.sdk.vo.OaOrgOaOrgHeadVo;
import com.biz.crm.mdm.business.org.sdk.vo.OaOrgOaOrgVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.jsonwebtoken.lang.Assert;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @description: 组织接口 实现
 * @date 2021/9/27 上午10:33
 */
@Service
@Slf4j
public class OrgOaOrgPullDataServiceImpl implements OrgOaOrgPullDataService {


    @Autowired(required = false)
    private OrgOaOrgRepository orgOaOrgRepository;

    @Autowired(required = false)
    private GenerateCodeService generateCodeService;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Autowired(required = false)
    @Qualifier("nebulaToolkitService")
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private UrlApiService urlApiService;

    @Autowired(required = false)
    private ExternalLogVoService externalLogVoService;

    @Autowired(required = false)
    private OrgOaOrgService orgOaOrgService;

    /**
     * 拉取OA行政组织
     *
     * @param isAll
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/18 22:31
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NOT_SUPPORTED)
    public void pullOaOrg(String isAll) {
        UrlAddressVo urlAddressVo = urlApiService.getUrlAddressByAccount(RyConstant.STD_OA_ACCOUNT);
        //获取登陆信息
        String url = urlAddressVo.getUrl();
        String interfaceAddress = String.format(RyConstant.OA_PULL_ORG, urlAddressVo.getBusinessKey());
        int pageSize = CommonConstant.MAX_PAGE_SIZE;
        Pageable pageable = PageRequest.of(1, pageSize);
        String tenantCode = TenantUtils.getTenantCode();
        AbstractCrmUserIdentity userIdentity = loginUserService.getAbstractLoginUser();
        boolean nextPage;
        do {
            nextPage = false;
            JSONObject dataObject = this.buildReqJson(urlAddressVo, pageable);
            pageable = pageable.next();
            //请求参数
            Map<String, Object> reqMap = Maps.newHashMap();
            reqMap.put("datajson", JSONObject.toJSONString(dataObject));
            Map<String, String> headMap = RySignHeaderUtil.getSignHeadMap(urlAddressVo.getAccessId(), urlAddressVo.getSecretKey(), interfaceAddress);
            //组装请求参数放到日志
            ExternalLogDetailDto logDetailDto = ExternalLogUtil.buildLogSaveInfo(JSONObject.toJSONString(reqMap), urlAddressVo);
            logDetailDto.setReqHead(JSONObject.toJSONString(headMap));
            logDetailDto.setReqJson(JSONObject.toJSONString(reqMap));
            logDetailDto.setMethod(interfaceAddress.substring(interfaceAddress.lastIndexOf("/") + 1));
            logDetailDto.setRequestUri(interfaceAddress);
            logDetailDto.setMethodMsg("OA行政架构营销架构映射");
            externalLogVoService.addOrUpdateLog(logDetailDto, true);
            Result<String> result = BusinessHttpUtil.post(url + interfaceAddress, null, headMap, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
                    5, 120, reqMap);
            ExternalLogUtil.buildLogResult(logDetailDto, result);
            String data = null;
            try {
                data = checkResult(result);
                if (StringUtil.isEmpty(data)) {
                    return;
                }
                List<OaOrgOaOrgHeadVo> headVoList = JSONArray.parseArray(data, OaOrgOaOrgHeadVo.class);
                if (CollectionUtil.isEmpty(headVoList)) {
                    return;
                }
                nextPage = headVoList.size() == pageSize;
                this.buildOrgOaOrgData(headVoList, tenantCode, userIdentity);
                logDetailDto.setStatus(ExternalLogGlobalConstants.S);
                logDetailDto.setTipMsg("拉取成功");
                externalLogVoService.addOrUpdateLog(logDetailDto, false);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                logDetailDto.setTipMsg(e.getMessage());
                logDetailDto.setExceptionStack(e.getMessage());
                logDetailDto.setStatus(ExternalLogGlobalConstants.E);
                externalLogVoService.addOrUpdateLog(logDetailDto, false);
                throw e;
            }
        } while (nextPage);

    }

    /**
     * 构建请求参数
     *
     * @return
     */
    private JSONObject buildReqJson(UrlAddressVo urlAddressVo, Pageable pageable) {
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 200));

        String systemId = urlAddressVo.getEnvironment();
        String password = urlAddressVo.getRefreshKey();
        String currentDateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String md5 = getMD5Str(systemId + password + currentDateTime).toLowerCase();

        JSONObject headObject = new JSONObject();
        headObject.put("systemid", systemId);
        headObject.put("currentDateTime", currentDateTime);
        headObject.put("Md5", md5);
        //组装分页参数
        JSONObject pageInfo = new JSONObject();
        pageInfo.put("pageNo", String.valueOf(pageable.getPageNumber()));
        pageInfo.put("pageSize", String.valueOf(pageable.getPageSize()));
        LocalDateTime now = LocalDateTime.now();
        //封装operationinfo参数
        JSONObject operationinfo = new JSONObject();
        //OA的说固定传1 2024年7月19日17:05:28
        operationinfo.put("operator", "1");
        operationinfo.put("operationDate", now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        operationinfo.put("operationTime", now.format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        JSONObject dataObject = new JSONObject();
        dataObject.put("mainTable", new JSONObject());
        dataObject.put("header", headObject);
        dataObject.put("operationinfo", operationinfo);
        dataObject.put("pageInfo", pageInfo);
        return dataObject;
    }

    /**
     * 解析OA 数据
     *
     * @param oaOrgOaOrgVoList
     */
    private void buildOrgOaOrgData(List<OaOrgOaOrgHeadVo> oaOrgOaOrgVoList, String tenantCode, AbstractCrmUserIdentity userIdentity) {
        if (CollectionUtil.isEmpty(oaOrgOaOrgVoList)) {
            return;
        }
        List<OrgOaOrg> oaOrgList = Lists.newArrayList();
        Date nowDate = new Date();
        String username = userIdentity.getUsername();
        String realName = userIdentity.getRealName();
        oaOrgOaOrgVoList.forEach(headVo -> {
            if (Objects.isNull(headVo)) {
                return;
            }
            OaOrgOaOrgVo vo = headVo.getMainTable();
            if (Objects.isNull(vo)) {
                return;
            }
            OrgOaOrg orgOaOrg = new OrgOaOrg();
            orgOaOrg.setId(UuidCrmUtil.general());
            orgOaOrg.setOrgCode(vo.getYxzzbm());
            orgOaOrg.setOrgName(vo.getYxzzmc());
            orgOaOrg.setOaOrgCode(vo.getXzzzbm());
            orgOaOrg.setOaOrgName(vo.getXzzzmc());
            //OA 0启用  1禁用   ->  默认禁用   0 启用
            EnableStatusEnum enableStatusEnum = EnableStatusEnum.DISABLE;
            if ("0".equals(vo.getZt())) {
                enableStatusEnum = EnableStatusEnum.ENABLE;
            }
            orgOaOrg.setEnableStatus(enableStatusEnum.getCode());

            orgOaOrg.setCreateAccount(username);
            orgOaOrg.setCreateName(realName);
            orgOaOrg.setCreateTime(nowDate);
            orgOaOrg.setModifyAccount(username);
            orgOaOrg.setModifyName(realName);
            orgOaOrg.setModifyTime(nowDate);
            orgOaOrg.setTenantCode(tenantCode);
            orgOaOrg.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            oaOrgList.add(orgOaOrg);
        });
        orgOaOrgService.saveOrUpdateBatchXml(oaOrgList);
    }

    private String getMD5Str(String plainText) {
        //定义一个字节数组
        byte[] secretBytes = null;
        try {
            // 生成一个MD5加密计算摘要
            MessageDigest md = MessageDigest.getInstance("MD5");
            //对字符串进行加密
            md.update(plainText.getBytes());
            //获得加密后的数据
            secretBytes = md.digest();
        } catch (NoSuchAlgorithmException e) {
            //throw new RuntimeException("没有md5这个算法！");
            throw new RuntimeException("没有MD5");
        }
        //将加密后的数据转换为16进制数字
        String md5code = new BigInteger(1, secretBytes).toString(16);
        // 如果生成数字未满32位，需要前面补0
        // 不能把变量放到循环条件，值改变之后会导致条件变化。如果生成30位 只能生成31位md5
        int tempIndex = 32 - md5code.length();
        for (int i = 0; i < tempIndex; i++) {
            md5code = "0" + md5code;
        }
        return md5code;
    }

    /**
     * 校验OA返回结果
     *
     * @param result
     */
    private String checkResult(Result<String> result) {
        Assert.isTrue(result.isSuccess(), "请求OA异常:" + result.getMessage());
        Assert.hasLength(result.getResult(), "OA返回信息为空:" + result.getMessage());
        JSONObject jsonObject = JSONObject.parseObject(result.getResult());
        if (jsonObject.containsKey("status")) {
            Validate.isTrue(jsonObject.get("status").equals(RyConstant.OS_SUCCESS_CODE), "OA返回信息:" + jsonObject.get("info"));
        }
        Validate.isTrue(jsonObject.containsKey("result"), "OA返回信息无[result]字段");
        Validate.notNull(jsonObject.get("result"), "OA返回信息[result]对象为空!");
        return jsonObject.getString("result");
    }
}
