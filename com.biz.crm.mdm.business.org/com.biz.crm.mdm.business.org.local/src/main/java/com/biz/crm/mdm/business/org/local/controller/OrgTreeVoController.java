package com.biz.crm.mdm.business.org.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.org.sdk.dto.OrgPaginationDto;
import com.biz.crm.mdm.business.org.sdk.service.OrgTreeVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgTreeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 组织树状结构信息 http 接口
 * @date 2021/9/27 上午10:36
 */
@Api(tags = "组织树状结构信息")
@Slf4j
@RestController
@RequestMapping(value = {"/v1/org/orgTree"})
public class OrgTreeVoController {

  @Autowired(required = false)
  private OrgTreeVoService orgTreeVoService;

  @ApiOperation("查询所有组织树形结构（给后台使用），全部返回（包含停用）")
  @GetMapping("/findAllStruTree")
  public Result<List<OrgTreeVo>> findAllStruTree(OrgPaginationDto orgPaginationDto) {

    try {
      List<OrgTreeVo> trees = this.orgTreeVoService.findAllStruTreeByCondition(orgPaginationDto);
      return Result.ok(trees);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("查询所有组织树形结构（给后台使用），无筛选条件，全部返回（包含停用）")
  @GetMapping("/findByOrgNameStruTree")
  public Result<List<OrgTreeVo>> findByOrgNameStruTree(String orgName, String enableStatus) {
    try {
      List<OrgTreeVo> trees = this.orgTreeVoService.findByOrgNameStruTree(orgName, enableStatus);
      return Result.ok(trees);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
