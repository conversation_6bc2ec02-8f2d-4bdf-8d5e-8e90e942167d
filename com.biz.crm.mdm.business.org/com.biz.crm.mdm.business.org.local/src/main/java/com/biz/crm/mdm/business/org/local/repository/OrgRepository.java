package com.biz.crm.mdm.business.org.local.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.vo.LazyTreeVo;
import com.biz.crm.mdm.business.org.local.entity.Org;
import com.biz.crm.mdm.business.org.local.mapper.OrgMapper;
import com.biz.crm.mdm.business.org.sdk.dto.OrgPaginationDto;
import com.biz.crm.mdm.business.org.sdk.dto.OrgPositionUserQueryDto;
import com.biz.crm.mdm.business.org.sdk.dto.OrgQueryDto;
import com.biz.crm.mdm.business.org.sdk.vo.OrgPositionUserVo;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.org.sdk.vo.SfaOrgCountVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 条件字段分类 数据层
 * @date 2021/9/26 下午4:32
 */
@Repository
public class OrgRepository extends ServiceImpl<OrgMapper, Org> {

    private static final Integer SIZE = 1000;

    /**
     * 根据 Org 主键查询详细信息（包括关联表）
     *
     * @param id
     * @return
     */
    public Org findDetailsById(String id) {
        return this.baseMapper.findDetailsById(id, TenantUtils.getTenantCode(), DelFlagStatusEnum.NORMAL);
    }

    /**
     * 根据 orgCode 条件字段分类编码 查询（包括关联表）
     *
     * @param orgCode
     * @param tenantCode
     * @return
     */
    public Org findByOrgCode(String orgCode, String tenantCode) {
        return this.baseMapper.findByOrgCode(orgCode, tenantCode, DelFlagStatusEnum.NORMAL);
    }

    /**
     * 分页查询
     *
     * @param pageable
     * @param dto
     * @return
     */
    public Page<Org> findByConditions(Pageable pageable, OrgPaginationDto dto) {
        Page<Org> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        if (StringUtils.isEmpty(dto.getTenantCode())) {
            dto.setTenantCode(TenantUtils.getTenantCode());
        }
        if (StringUtils.isEmpty(dto.getDelFlag())) {
            dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return this.baseMapper.findByConditions(page, dto);
    }

    public Page<SfaOrgCountVo> findSfaOrgCount(Pageable pageable, SfaOrgCountVo dto){
        Page<SfaOrgCountVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.baseMapper.findSfaOrgCount(page,dto);
    }

    /**
     * 根据 Org 主键集合查询详细信息（包括关联表）
     *
     * @param ids
     * @return
     */
    public List<Org> findDetailsByIds(List<String> ids) {
        return this.baseMapper.findDetailsByIds(ids, DelFlagStatusEnum.NORMAL, TenantUtils.getTenantCode());
    }

    /**
     * 根据客户编码或客户名称查询
     *
     * @param
     * @return
     */
    public List<Org> findByOrgCodeLikeOrOrgNameLike(String orgCodeLikeorNameLike) {
        if (StringUtils.isEmpty(orgCodeLikeorNameLike)) {
            return lambdaQuery().eq(Org::getTenantCode, TenantUtils.getTenantCode()).list();
        }
        return this.lambdaQuery()
                .eq(Org::getTenantCode, TenantUtils.getTenantCode())
                .and(
                        i ->
                                i.like(
                                                StringUtils.isNotEmpty(orgCodeLikeorNameLike),
                                                Org::getOrgCode,
                                                orgCodeLikeorNameLike)
                                        .or()
                                        .like(
                                                StringUtils.isNotEmpty(orgCodeLikeorNameLike),
                                                Org::getOrgName,
                                                orgCodeLikeorNameLike))
                .list();
    }

    /**
     * 根据 orgCode集合查询详细信息（包括关联表）
     *
     * @param orgCodes
     * @param tenantCode
     * @return
     */
    public List<Org> findByOrgCodes(List<String> orgCodes, String tenantCode) {
        return this.baseMapper.findByOrgCodes(orgCodes, tenantCode, DelFlagStatusEnum.NORMAL);
    }

    /**
     * 按父节点code集合查询
     *
     * @param parentCodes
     * @param tenantCode
     * @return
     */
    public List<Org> findByParentCodes(List<String> parentCodes, String tenantCode) {
        return this.baseMapper.findByParentCodes(parentCodes, tenantCode, DelFlagStatusEnum.NORMAL);
    }

    /**
     * 按ruleCode 集合查询所有子集合的组织code
     *
     * @param ruleCodes
     * @param tenantCode
     * @return
     */
    public List<String> findChildrenOrgCodeByRuleCodes(List<String> ruleCodes, String tenantCode) {
        return this.baseMapper.findChildrenOrgCodeByRuleCodes(ruleCodes, tenantCode, DelFlagStatusEnum.NORMAL);
    }

    /**
     * 按ruleCode 集合查询所有子集合的组织code
     *
     * @param ruleCodes
     * @param tenantCode
     * @return
     */
    public List<Org> findChildrenOrgByRuleCodes(List<String> ruleCodes, String tenantCode) {
        return this.baseMapper.findChildrenOrgByRuleCodes(ruleCodes, tenantCode, DelFlagStatusEnum.NORMAL);
    }

    /**
     * 按id集合查询组织ruleCode
     *
     * @param ids
     * @return
     */
    public List<String> findRuleCodeByIds(List<String> ids) {
        List<Org> orgs = this.lambdaQuery()
                .eq(Org::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .in(CollectionUtils.isNotEmpty(ids), Org::getId, ids)
                .select(Org::getRuleCode)
                .list();
        if (CollectionUtils.isEmpty(orgs)) {
            return Lists.newArrayList();
        }
        return orgs.stream().map(Org::getRuleCode).collect(Collectors.toList());
    }

    /**
     * 按orgCode查询组织ruleCode
     *
     * @param orgCode
     * @param tenantCode
     * @return
     */
    public String findRuleCodeByOrgCode(String orgCode, String tenantCode) {
        Org org = this.lambdaQuery()
                .eq(Org::getOrgCode, orgCode)
                .eq(Org::getTenantCode, tenantCode)
                .eq(Org::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .select(Org::getRuleCode)
                .one();
        if (Objects.isNull(org)) {
            return null;
        }
        return org.getRuleCode();
    }

    /**
     * 按orgCodes集合查询组织ruleCode
     *
     * @param orgCodes
     * @return
     */
    public List<String> findRuleCodeByOrgCodes(List<String> orgCodes, String tenantCode) {
        List<String> list = Lists.newLinkedList();
        for (List<String> item : Lists.partition(orgCodes, SIZE)) {
            List<Org> cur = this.lambdaQuery()
                    .in(Org::getOrgCode, item)
                    .eq(Org::getTenantCode, tenantCode)
                    .eq(Org::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                    .select(Org::getRuleCode)
                    .list();
            if (CollectionUtils.isEmpty(cur)) {
                continue;
            }
            list.addAll(cur.stream().map(Org::getRuleCode).collect(Collectors.toSet()));
        }
        return list;
    }

    /**
     * 按orgTypes集合查询组织ruleCode
     *
     * @param orgTypes
     * @return
     */
    public List<String> findRuleCodeByOrgTypes(List<String> orgTypes, String tenantCode) {
        List<Org> orgs = this.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(orgTypes), Org::getOrgType, orgTypes)
                .eq(Org::getTenantCode, tenantCode)
                .eq(Org::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .select(Org::getRuleCode)
                .list();
        if (CollectionUtils.isEmpty(orgs)) {
            return Lists.newArrayList();
        }
        return orgs.stream().map(Org::getRuleCode).collect(Collectors.toList());
    }

    /**
     * 根据id集合 更新组织启用/禁用状态
     *
     * @param ids
     * @param enable
     */
    public void updateEnableStatusByIds(List<String> ids, EnableStatusEnum enable, Date endTime) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        UpdateWrapper<Org> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("enable_status", enable.getCode());
        updateWrapper.set("end_time", endTime);
        updateWrapper.in("id", ids);
        updateWrapper.eq("tenant_code", TenantUtils.getTenantCode());
        this.update(updateWrapper);
    }

    /**
     * 通过父节点code查询
     *
     * @param parentCode
     * @param tenantCode
     * @return
     */
    public List<Org> findByParentCode(String parentCode, String tenantCode) {
        return this.lambdaQuery()
                .eq(Org::getParentCode, parentCode)
                .eq(Org::getTenantCode, tenantCode)
                .eq(Org::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    /**
     * 查询父节点是null的节点（包含已删除已禁用）
     *
     * @param tenantCode
     * @return
     */
    public List<Org> findByParentCodeIsNull(String tenantCode) {
        return this.lambdaQuery()
                .eq(Org::getParentCode, StringUtils.EMPTY)
                .eq(Org::getTenantCode, tenantCode)
                .list();
    }

    /**
     * 通过组织编码更新ruleCode和levelNum
     *
     * @param orgCode
     * @param ruleCode
     * @param levelNum
     */
    public void updateRuleCodeAndLevelNumByOrgCode(
            String orgCode, String ruleCode, int levelNum, String tenantCode) {
        this.lambdaUpdate()
                .set(Org::getRuleCode, ruleCode)
                .set(Org::getLevelNum, levelNum)
                .eq(Org::getOrgCode, orgCode)
                .eq(Org::getTenantCode, tenantCode)
                .update();
    }

    /**
     * 按降维码集合，和状态查询
     *
     * @param ruleCodes
     * @param enable
     * @param tenantCode
     * @return
     */
    public List<Org> findByRuleCodesAndEnableStatus(List<String> ruleCodes, EnableStatusEnum enable, String tenantCode) {
        if (CollectionUtil.isEmpty(ruleCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(Objects.nonNull(enable), Org::getEnableStatus, Objects.nonNull(enable) ? enable.getCode() : null)
                .in(Org::getRuleCode, ruleCodes)
                .eq(Org::getTenantCode, tenantCode)
                .eq(Org::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .orderByDesc(Org::getRuleCode)
                .list();
    }

    /**
     * 按ruleCode查询所有子节点
     *
     * @param ruleCode
     * @param tenantCode
     * @return
     */
    public List<Org> findChildrenByRuleCode(String ruleCode, String tenantCode) {
        if (StringUtil.isEmpty(ruleCode)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .likeRight(Org::getRuleCode, ruleCode)
                .eq(Org::getTenantCode, tenantCode)
                .eq(Org::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    /**
     * 按ruleCodes查询所有子节点
     *
     * @param ruleCodes
     * @param tenantCode
     * @return
     */
    public List<Org> findChildrenByRuleCode(List<String> ruleCodes, String tenantCode) {
        List<Org> result = Lists.newArrayList();
        if (CollectionUtil.isEmpty(ruleCodes)) {
            return result;
        }
        Set<String> ruleCodeSet = ruleCodes.stream().filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(ruleCodeSet)) {
            return result;
        }
        ruleCodeSet.forEach(ruleCode -> {
            List<Org> ruleList = this.findChildrenByRuleCode(ruleCode, tenantCode);
            if (CollectionUtil.isNotEmpty(ruleList)) {
                result.addAll(ruleList);
            }
        });
        return result;
    }

    /**
     * 分页通过ruleCodes 查询子节点信息
     *
     * @param pageable
     * @param ruleCodes
     * @param tenantCode
     * @return
     */
    public Page<Org> findChildrenByRuleCodes(
            Pageable pageable, List<String> ruleCodes, String tenantCode) {
        Page<Org> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.baseMapper.findChildrenByRuleCodes(
                page, ruleCodes, tenantCode, DelFlagStatusEnum.NORMAL);
    }

    /**
     * 按条件查询 启用状态、组织类型、上级编码精确查询，组织编码、名称模糊查询
     *
     * @param dto
     * @return
     */
    public List<Org> findByConditions(OrgPaginationDto dto) {
        return this.lambdaQuery()
                .eq(StringUtils.isNotBlank(dto.getEnableStatus()), Org::getEnableStatus, dto.getEnableStatus())
                .in(CollectionUtil.isNotEmpty(dto.getLevelNumList()), Org::getLevelNum, dto.getLevelNumList())
                .eq(Objects.nonNull(dto.getLevelNum()), Org::getLevelNum, dto.getLevelNum())
                .in(CollectionUtil.isNotEmpty(dto.getOrgTypeList()), Org::getOrgType, dto.getOrgTypeList())
                .eq(StringUtils.isNotBlank(dto.getOrgType()), Org::getOrgType, dto.getOrgType())
                .eq(StringUtils.isNotBlank(dto.getParentCode()), Org::getParentCode, dto.getParentCode())
                .like(StringUtils.isNotBlank(dto.getOrgCode()), Org::getOrgCode, dto.getOrgCode())
                .like(StringUtils.isNotBlank(dto.getOrgName()), Org::getOrgName, dto.getOrgName())
                .eq(Org::getTenantCode, dto.getTenantCode())
                .eq(Org::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    /**
     * 根据 【EnableStatus】 orgName模糊 查询orgCode ruleCode
     *
     * @param enableStatus
     * @param name
     * @param tenantCode
     * @return
     */
    public List<Org> findOrgCodeAndRuleCodeByEnableStatusOptAndOrgNameLike(
            String enableStatus, String name, String tenantCode) {
        List<Org> likeList =
                this.lambdaQuery()
                        .eq(StringUtils.isNotEmpty(enableStatus), Org::getEnableStatus, enableStatus)
                        .like(Org::getOrgName, name)
                        .eq(Org::getTenantCode, tenantCode)
                        .select(Org::getOrgCode, Org::getRuleCode)
                        .eq(Org::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                        .list();
        return likeList;
    }

    /**
     * 查询企业组织懒加载数据
     *
     * @param enableStatus    启用状态
     * @param topOnly         传true只查第一层
     * @param parentCode      只查询该编码下一级
     * @param codeList        只查询这些编码
     * @param ruleCodeList    只查询这些降维编码
     * @param excludeRuleCode 排除这个降维编码的下级
     * @param tenantCode
     * @return
     */
    public List<LazyTreeVo> findOrgLazyTreeList(
            String enableStatus,
            Boolean topOnly,
            String parentCode,
            List<String> codeList,
            List<String> ruleCodeList,
            String excludeRuleCode,
            String tenantCode) {
        return this.baseMapper.findOrgLazyTreeList(
                enableStatus,
                topOnly,
                parentCode,
                codeList,
                ruleCodeList,
                excludeRuleCode,
                tenantCode,
                DelFlagStatusEnum.NORMAL);
    }

    /**
     * 查找parentCode不为空但找不到对应上级的数据,设置parentCode为null
     *
     * @return
     */
    public void updateOrphanParentCodeNull(String tenantCode) {
        this.baseMapper.updateOrphanParentCodeNull(tenantCode, DelFlagStatusEnum.NORMAL);
    }

    /**
     * 查询所有没有父级编码的子集
     *
     * @param tenantCode
     * @return
     */
    public List<Org> findListWithoutParentCode(String tenantCode) {
        return this.lambdaQuery()
                .eq(Org::getTenantCode, tenantCode)
                .eq(Org::getParentCode, StringUtils.EMPTY)
                .eq(Org::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    /**
     * 根据组织编码查询详情
     *
     * @param orgCode
     * @param tenantCode
     * @return
     */
    public Org findDetailsByCode(String orgCode, String tenantCode) {
        Validate.notBlank(orgCode, "组织编码不能为空");
        return this.lambdaQuery()
                .eq(Org::getTenantCode, tenantCode)
                .eq(Org::getOrgCode, orgCode)
                .eq(Org::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .one();
    }

    /**
     * 通过父级编码查询所有子级
     *
     * @param parentCode
     * @param tenantCode
     * @return
     */
    public List<Org> findChildrenListByParentCode(String parentCode, String tenantCode) {
        parentCode = Optional.ofNullable(parentCode).orElse(StringUtils.EMPTY);
        return this.lambdaQuery()
                .eq(Org::getTenantCode, tenantCode)
                .eq(Org::getParentCode, parentCode)
                .list();
    }

    /**
     * 手动设置父级编码为空
     *
     * @param id
     */
    public void setParentCodeNull(String id) {
        this.lambdaUpdate().set(Org::getParentCode, null).eq(Org::getId, id).eq(Org::getTenantCode, TenantUtils.getTenantCode()).update();//新增租户编码条件
    }

    /**
     * 手动设置父级编码，规则编码为空
     *
     * @param id
     */
    public void setParentCodeAndRuleCodeNull(String id) {
        this.lambdaUpdate()
                .set(Org::getParentCode, null)
                .set(Org::getRuleCode, "")
                .eq(Org::getId, id)
                .eq(Org::getTenantCode, TenantUtils.getTenantCode())//新增租户编码条件
                .update();
    }

    /**
     * 通过编码查找组织（包括逻辑删除）
     *
     * @param org
     */
    public Org findAllByOrgCode(Org org) {
        return this.lambdaQuery().eq(Org::getOrgCode, org.getOrgCode()).eq(Org::getTenantCode, TenantUtils.getTenantCode()).one();    //处理租户编号赋值的bug
    }

    /**
     * 获取匹配的组织编码集合
     *
     * @param dto
     * @return
     */
    public Set<String> findByOrgQueryDto(OrgQueryDto dto) {
        final List<Org> list =
                this.lambdaQuery()
                        .eq(Org::getTenantCode, TenantUtils.getTenantCode())
                        .eq(Org::getDelFlag, dto.getDelFlag())
                        .like(StringUtils.isNotBlank(dto.getOrgName()), Org::getOrgName, dto.getOrgName())
                        .eq(StringUtils.isNotBlank(dto.getOrgType()), Org::getOrgType, dto.getOrgType())
                        .eq(StringUtils.isNotBlank(dto.getEnableStatus()), Org::getEnableStatus, dto.getEnableStatus())
                        .select(Org::getOrgCode)
                        .list();
        if (CollectionUtils.isEmpty(list)) {
            return Sets.newHashSet();
        }
        return list.stream()
                .filter(a -> StringUtils.isNotBlank(a.getOrgCode()))
                .map(Org::getOrgCode)
                .collect(Collectors.toSet());
    }

    /**
     * 重构修改方法
     *
     * @param orgs
     * @param tenantCode
     */
    public void updateBatchByIdAndTenantCode(List<Org> orgs, String tenantCode) {
        if (CollectionUtils.isEmpty(orgs)) {
            return;
        }
        orgs.stream().forEach(org -> {
            LambdaUpdateWrapper<Org> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
            lambdaUpdateWrapper.eq(Org::getTenantCode, tenantCode);
            lambdaUpdateWrapper.in(Org::getId, org.getId());
            this.baseMapper.update(org, lambdaUpdateWrapper);
        });

    }

    /**
     * 重构修改方法
     *
     * @param org
     * @param tenantCode
     */
    public void updateByIdAndTenantCode(Org org, String tenantCode) {
        LambdaUpdateWrapper<Org> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(Org::getTenantCode, tenantCode);
        lambdaUpdateWrapper.in(Org::getId, org.getId());
        this.baseMapper.update(org, lambdaUpdateWrapper);
    }

    /**
     * 重构查询方法
     *
     * @param currentId
     * @param tenantCode
     * @return
     */
    public Org findByIdAndTenantCode(String currentId, String tenantCode) {
        return this.lambdaQuery()
                .eq(Org::getTenantCode, tenantCode)
                .in(Org::getId, currentId)
                .one();
    }

    /**
     * findOrgLazyTreeList方法的分页接口
     *
     * @param pageable
     * @param enableStatus
     * @param topOnly
     * @param parentCode
     * @param codeList
     * @param ruleCodeList
     * @param excludeRuleCode
     * @param tenantCode
     * @return
     */
    public Page<LazyTreeVo> findOrgLazyTreeListPage(
            Pageable pageable,
            String enableStatus,
            Boolean topOnly,
            String parentCode,
            List<String> codeList,
            List<String> ruleCodeList,
            String excludeRuleCode,
            String tenantCode) {
        Page<LazyTreeVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.baseMapper.findOrgLazyTreeListPage(
                page,
                enableStatus,
                topOnly,
                parentCode,
                codeList,
                ruleCodeList,
                excludeRuleCode,
                tenantCode,
                DelFlagStatusEnum.NORMAL);
    }

    /**
     * 查询 parentCode 为 "" 或 null
     * findListWithoutParentCode() 方法只筛选了parentCode为 "" 并没有判断parentCode为 null 的，findListNoParentCode() 该方法对 findListWithoutParentCode()方法进行了改进
     *
     * @param tenantCode
     * @return
     */
    public List<Org> findListNoParentCode(String tenantCode) {
        return this.baseMapper.findListNoParentCode(tenantCode, DelFlagStatusEnum.NORMAL.getCode());
    }

    /**
     * 根据客户名称查询
     *
     * @param
     * @return
     */
    public List<Org> findByOrgNameLike(String orgName) {
        return this.lambdaQuery()
                .eq(Org::getTenantCode, TenantUtils.getTenantCode())
                .eq(Org::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .like(Org::getOrgName, orgName)
                .list();
    }

    public Page<OrgVo> findOrgByConditions(Pageable pageable, OrgPaginationDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
        Page<OrgVo> page = new Page<>(pageable.getPageNumber(), Math.min(pageable.getPageSize(), 2000));
        dto = ObjectUtils.defaultIfNull(dto, new OrgPaginationDto());
        if (StringUtils.isEmpty(dto.getTenantCode())) {
            dto.setTenantCode(TenantUtils.getTenantCode());
        }
        if (StringUtils.isEmpty(dto.getDelFlag())) {
            dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        List<String> selectedCodes = Lists.newArrayList();
        if (StringUtil.isNotEmpty(dto.getSelectedCode())) {
            selectedCodes.add(dto.getSelectedCode());
        }
        if (CollectionUtil.isNotEmpty(dto.getSelectedCodes())) {
            selectedCodes.addAll(dto.getSelectedCodes());
        }
        dto.setSelectedCodes(selectedCodes);
        return this.baseMapper.findOrgByConditions(page, dto);
    }

    public List<Org> findOrgByOrgNameList(List<String> orgNameList) {
        return this.lambdaQuery()
                .eq(Org::getTenantCode, TenantUtils.getTenantCode())
                .eq(Org::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(Org::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .in(Org::getOrgName, orgNameList)
                .select(Org::getOrgCode, Org::getOrgName)
                .list();
    }

    public List<OrgPositionUserVo> findOrgPositionUserByConditions(OrgPositionUserQueryDto dto) {
        return this.baseMapper.findOrgPositionUserByConditions(dto);
    }

    public List<Org> findByOrgType(String orgType) {
        return this.lambdaQuery()
                .eq(Org::getTenantCode, TenantUtils.getTenantCode())
                .eq(Org::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(Org::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .eq(Org::getOrgType, orgType)
                .list();
    }

    public Page<OrgVo> findOrgListByOrgCodeAndRegionSubordinate(Page<OrgVo> page, OrgPaginationDto dto) {
        dto.setTenantCode(TenantUtils.getTenantCode());
        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        return this.baseMapper.findOrgListByOrgCodeAndRegionSubordinate(page, dto);
    }

    public List<Org> findAll() {
        return this.lambdaQuery()
                .eq(Org::getDelFlag,DelFlagStatusEnum.NORMAL.getCode())
                .eq(Org::getEnableStatus,EnableStatusEnum.ENABLE.getCode())
                .list();
    }

    public List<Org> findByRuleCodeLike(String ruleCode) {
        return this.lambdaQuery()
                .eq(Org::getDelFlag,DelFlagStatusEnum.NORMAL.getCode())
                .eq(Org::getEnableStatus,EnableStatusEnum.ENABLE.getCode())
                .likeRight(Org::getRuleCode,ruleCode)
                .list();
    }

    public List<Org> findAllParentTreeByUserName(String userName) {

       return   this.getBaseMapper().findAllParentTreeByUserName(userName);
    }
}
