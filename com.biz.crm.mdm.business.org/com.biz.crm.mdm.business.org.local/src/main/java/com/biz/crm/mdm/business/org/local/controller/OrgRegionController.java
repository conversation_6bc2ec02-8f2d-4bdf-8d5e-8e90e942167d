package com.biz.crm.mdm.business.org.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.org.local.entity.OrgRegion;
import com.biz.crm.mdm.business.org.local.service.OrgRegionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 组织和行政区域关联表
 *
 * <AUTHOR>
 * @date 2021-10-15 10:42:46
 */
@Slf4j
@RestController
@RequestMapping("/v1/org/orgRegion")
@Api(tags = "组织管理-关联行政区域")
public class OrgRegionController {

  @Autowired(required = false)
  private OrgRegionService orgRegionService;
  @ApiOperation(value = "新增组织和行政区域关联关系")
  @PostMapping("/create")
  public Result<?> create(@RequestBody List<OrgRegion> orgRegions) {
    try {
      this.orgRegionService.createBatch(orgRegions);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
