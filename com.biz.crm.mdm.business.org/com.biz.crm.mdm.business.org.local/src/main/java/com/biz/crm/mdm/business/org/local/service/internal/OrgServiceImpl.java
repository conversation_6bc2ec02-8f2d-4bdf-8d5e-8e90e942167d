package com.biz.crm.mdm.business.org.local.service.internal;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.constant.BusinessConstant;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.dto.TreeDto;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.business.common.sdk.service.TreeRuleCodeStrategy;
import com.biz.crm.business.common.sdk.service.TreeRuleCodeStrategyHolder;
import com.biz.crm.common.sequese.sdk.generator.service.CrmSequeseFactoryService;
import com.biz.crm.mdm.business.org.local.entity.Org;
import com.biz.crm.mdm.business.org.local.repository.OrgRepository;
import com.biz.crm.mdm.business.org.local.service.OrgService;
import com.biz.crm.mdm.business.org.sdk.common.enums.OrgTypeEnum;
import com.biz.crm.mdm.business.org.sdk.dto.OrgBySnowFlakeDto;
import com.biz.crm.mdm.business.org.sdk.dto.OrgEventBatchDto;
import com.biz.crm.mdm.business.org.sdk.dto.OrgEventDto;
import com.biz.crm.mdm.business.org.sdk.dto.OrgLogEventDto;
import com.biz.crm.mdm.business.org.sdk.dto.OrgPaginationDto;
import com.biz.crm.mdm.business.org.sdk.event.OrgEventListener;
import com.biz.crm.mdm.business.org.sdk.event.OrgLogEventListener;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 组织接口 实现
 * @date 2021/9/27 上午10:33
 */
@Service("OrgServiceImpl")
@Slf4j
public class OrgServiceImpl implements OrgService {

    private final int ONE = 1;

    @Autowired(required = false)
    private OrgRepository orgRepository;

    @Autowired(required = false)
    @Lazy
    private List<OrgEventListener> orgEventListeners;

    @Autowired(required = false)
    private GenerateCodeService generateCodeService;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Autowired(required = false)
    private TreeRuleCodeStrategyHolder treeRuleCodeStrategyHolder;

    @Autowired(required = false)
    @Qualifier("nebulaToolkitService")
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;

    @Value("${crm.business.rule-code-length.org:}")
    private Integer ruleCodeLength;
    @Value("${nacos._serviceName:}")
    private String testCode;
    @Autowired(required = false)
    private CrmSequeseFactoryService crmSequeseFactoryService;

    @Autowired(required = false)
    @Lazy
    private List<OrgLogEventListener> orgLogEventListeners;

    /**
     * 基于数据库执行的数据视图执行内容缓存（最多500毫秒）
     */
    private static volatile Cache<String, List<Org>> cache = null;

    public OrgServiceImpl() {
        if (cache == null) {
            synchronized (OrgServiceImpl.class) {
                while (cache == null) {
                    cache = CacheBuilder.newBuilder()
                            .initialCapacity(10000)
                            .expireAfterWrite(5, TimeUnit.SECONDS)
                            .maximumSize(100000)
                            .build();
                }
            }
        }
    }

    @Override
    public Page<Org> findByConditions(Pageable pageable, OrgPaginationDto paginationDto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
        paginationDto = ObjectUtils.defaultIfNull(paginationDto, new OrgPaginationDto());
        paginationDto.setTenantCode(TenantUtils.getTenantCode());
        // 排除当前组织（及全部下级组织）
        String excludeAllChildrenRuleCode = null;
        if (StringUtils.isNotBlank(paginationDto.getExcludeAllChildrenOrgCode())) {
            excludeAllChildrenRuleCode =
                    this.orgRepository.findRuleCodeByOrgCode(
                            paginationDto.getExcludeAllChildrenOrgCode(), TenantUtils.getTenantCode());
            paginationDto.setExcludeAllChildrenRuleCode(excludeAllChildrenRuleCode);
        }
        // 查询当前组织（及全部下级组织）包含自己
        String includeAllChildrenRuleCode = null;
        if (StringUtils.isNotBlank(paginationDto.getIncludeAllChildrenOrgCode())) {
            includeAllChildrenRuleCode =
                    this.orgRepository.findRuleCodeByOrgCode(
                            paginationDto.getIncludeAllChildrenOrgCode(), TenantUtils.getTenantCode());
            paginationDto.setIncludeAllChildrenRuleCode(includeAllChildrenRuleCode);
        }
        if(CollectionUtils.isNotEmpty(paginationDto.getChildrenOrgCodes())){
            List<Org> orgList = this.findAllChildrenByOrgCodes(Lists.newArrayList(paginationDto.getChildrenOrgCodes()));
            List<String> orgCodes = orgList.stream().map(Org::getOrgCode).collect(Collectors.toList());
            paginationDto.setChildrenOrgCodes(Sets.newHashSet(orgCodes));
        }
        // 查询当前组织（及全部下级组织）排除自己
        String includeAllChildrenOrgCodeExcludeSelf = paginationDto.getIncludeAllChildrenOrgCodeExcludeSelf();
        if (StringUtils.isNotBlank(includeAllChildrenOrgCodeExcludeSelf)) {
            List<Org> children = this.orgRepository.findByParentCode(includeAllChildrenOrgCodeExcludeSelf, TenantUtils.getTenantCode());
            if (CollectionUtils.isNotEmpty(children)) {
                List<String> targetRuleCodes =
                        children.stream()
                                .filter(
                                        item -> {
                                            return !Objects.equals(includeAllChildrenOrgCodeExcludeSelf, item.getOrgCode());
                                        })
                                .map(Org::getRuleCode)
                                .collect(Collectors.toList());
                List<String> targetOrgCodes = this.orgRepository.findChildrenOrgCodeByRuleCodes(targetRuleCodes, TenantUtils.getTenantCode());
                paginationDto.setOrgCodes(targetOrgCodes);
            }
        }
        // 查询集合中的所有（及全部下级组织）包含自己
        if (CollectionUtils.isNotEmpty(paginationDto.getAllChildrenOrgCodesIncludeSelf())) {
            // TODO  后续改成从缓存中获取
            List<Org> orgs = this.orgRepository.findByOrgCodes(paginationDto.getAllChildrenOrgCodesIncludeSelf(), TenantUtils.getTenantCode());
            if (CollectionUtils.isNotEmpty(orgs)) {
                List<String> targetRuleCodes = orgs.stream().map(Org::getRuleCode).collect(Collectors.toList());
                List<String> targetOrgCodes = this.orgRepository.findChildrenOrgCodeByRuleCodes(targetRuleCodes, TenantUtils.getTenantCode());
                paginationDto.setOrgCodes(targetOrgCodes);
            }
        }
        // 查询集合中的所有（及全部下级组织）排除自己，如果自己在其他子节点中那么保留
        List<String> allChildrenOrgCodesExcludeSelf = paginationDto.getAllChildrenOrgCodesExcludeSelf();
        if (CollectionUtils.isNotEmpty(paginationDto.getAllChildrenOrgCodesExcludeSelf())) {
            // TODO  后续改成从缓存中获取
            List<Org> orgs =
                    this.orgRepository.findByOrgCodes(
                            paginationDto.getAllChildrenOrgCodesExcludeSelf(), TenantUtils.getTenantCode());
            if (CollectionUtils.isNotEmpty(orgs)) {
                List<String> targetRuleCodes =
                        orgs.stream()
                                .filter(
                                        item -> {
                                            return !allChildrenOrgCodesExcludeSelf.contains(item.getOrgCode());
                                        })
                                .map(Org::getRuleCode)
                                .collect(Collectors.toList());
                List<String> targetOrgCodes =
                        this.orgRepository.findChildrenOrgCodeByRuleCodes(
                                targetRuleCodes, TenantUtils.getTenantCode());
                paginationDto.setOrgCodes(targetOrgCodes);
            }
        }
        // 查询集合中的所有（及全部下级组织）任何情况下都排除自己
        List<String> allChildrenOrgCodesExcludeAnySelf =
                paginationDto.getAllChildrenOrgCodesExcludeAnySelf();
        if (CollectionUtils.isNotEmpty(allChildrenOrgCodesExcludeAnySelf)) {
            // TODO  后续改成从缓存中获取
            List<Org> orgs =
                    this.orgRepository.findByOrgCodes(
                            paginationDto.getAllChildrenOrgCodesExcludeAnySelf(), TenantUtils.getTenantCode());
            if (CollectionUtils.isNotEmpty(orgs)) {
                List<String> targetRuleCodes =
                        orgs.stream().map(Org::getRuleCode).collect(Collectors.toList());
                List<String> targetOrgCodes =
                        this.orgRepository.findChildrenOrgCodeByRuleCodes(
                                targetRuleCodes, TenantUtils.getTenantCode());
                targetOrgCodes =
                        targetOrgCodes.stream()
                                .filter(
                                        item -> {
                                            return !allChildrenOrgCodesExcludeAnySelf.contains(item);
                                        })
                                .collect(Collectors.toList());
                paginationDto.setOrgCodes(targetOrgCodes);
            }
        }

        Set<String> codeList = Sets.newHashSet();
        if (StringUtils.isNotBlank(paginationDto.getSelectedCode())) {
            codeList.add(paginationDto.getSelectedCode());
        }
        if (CollectionUtils.isNotEmpty(paginationDto.getSelectedCodes())) {
            codeList.addAll(paginationDto.getSelectedCodes());
        }
        if (CollectionUtils.isNotEmpty(codeList)) {
            paginationDto.setSelectedCodes(Lists.newArrayList(codeList));
        }
        return this.orgRepository.findByConditions(pageable, paginationDto);
    }

    @Override
    public Org findDetailsById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        return this.orgRepository.findDetailsById(id);
    }

    @Override
    public Org findByOrgCode(String orgCode) {
        if (StringUtils.isBlank(orgCode)) {
            return null;
        }
        return this.orgRepository.findByOrgCode(orgCode, TenantUtils.getTenantCode());
    }

    @Override
    public List<Org> findDetailsByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        String cacheKey = StringUtils.join(ids);
        List<Org> graph = cache.getIfPresent(cacheKey);
        if (graph == null) {
            graph = this.orgRepository.findDetailsByIds(ids);
            cache.put(cacheKey, graph);
        }
        return graph;
    }

    @Override
    public List<Org> findByOrgCodeLikeOrOrgNameLike(String orgCodeLikeorNameLike) {
        return this.orgRepository.findByOrgCodeLikeOrOrgNameLike(orgCodeLikeorNameLike);
    }

    @Override
    public List<Org> findByOrgCodes(List<String> orgCodes) {
        if (CollectionUtils.isEmpty(orgCodes)) {
            return Lists.newArrayList();
        }
        String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), orgCodes);
        List<Org> graph = cache.getIfPresent(cacheKey);
        if (graph == null) {
            graph = this.orgRepository.findByOrgCodes(orgCodes, TenantUtils.getTenantCode());
            cache.put(cacheKey, graph);
        }
        return graph;
    }

    @Override
    @Transactional
    public void deleteByIds(List<String> ids) {
        Validate.notEmpty(ids, "id集合不能为空");
        List<Org> orgs = this.findDetailsByIds(ids);
        Validate.notEmpty(orgs, "无效的参数");
        List<String> orgCodes = orgs.stream().map(Org::getOrgCode).collect(Collectors.toList());
        List<Org> children =
                this.orgRepository.findByParentCodes(orgCodes, TenantUtils.getTenantCode());
        Validate.isTrue(CollectionUtils.isEmpty(children), "存在下级组织，不能删除");
        // 删除事件 TODO /终端/客户/行政区域 需要实现组织删除事件，在组织删除时校验是否存在关联
        if (Objects.nonNull(this.orgEventListeners)) {
            for (OrgEventListener listener : this.orgEventListeners) {
                listener.onDelete(orgCodes);
            }
        }
        // 逻辑删除
        for (Org org : orgs) {
            org.setDelFlag(DelFlagStatusEnum.DELETE.getCode());
            org.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
        }
        //重构修改方法
        this.orgRepository.updateBatchByIdAndTenantCode(orgs, TenantUtils.getTenantCode());
        //如果ParentCode为空，则手动设置为空
        for (Org org : orgs) {
            orgRepository.setParentCodeAndRuleCodeNull(org.getId());
        }
        // 发布引擎事件通知
        List<OrgEventDto> orgEventDtoList = Lists.newArrayList(this.nebulaToolkitService.copyCollectionByWhiteList(children, Org.class, OrgEventDto.class, HashSet.class, ArrayList.class));
        OrgEventBatchDto orgEventBatchDto = new OrgEventBatchDto();
        orgEventBatchDto.setOrgEventDtoList(orgEventDtoList);
        SerializableBiConsumer<OrgEventListener, OrgEventBatchDto> sf = OrgEventListener::onDeleteBatch;
        this.nebulaNetEventClient.publish(orgEventBatchDto, OrgEventListener.class, sf);

        // TODO 清除组织缓存
        // TODO 日志处理
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableBatch(List<String> ids) {
        Validate.notEmpty(ids, "id集合不能为空");
        // 通过ruleCode 取出上级ruleCode 用于判断父级节点是否被禁用
        //日志原始对象
        List<Org> orgs = this.orgRepository.findDetailsByIds(ids);
        List<String> ruleCodes = this.orgRepository.findRuleCodeByIds(ids);
        if (CollectionUtils.isNotEmpty(ruleCodes)) {
            Validate.notNull(this.treeRuleCodeStrategyHolder, "系统未配置降维码策略控制器");
            TreeRuleCodeStrategy treeRuleCodeStrategy = this.treeRuleCodeStrategyHolder.getStrategy(null);
            Validate.notNull(treeRuleCodeStrategy, "系统未配置降维码策略");
            Set<String> parentRuleCodes =
                    treeRuleCodeStrategy.findParentRuleCodeByRuleCodesExcludeAnySelf(ruleCodeLength, ruleCodes);
            if (CollectionUtils.isNotEmpty(parentRuleCodes)) {
                List<Org> parentDisableList = this.findByRuleCodesAndEnableStatus(
                                new ArrayList<>(parentRuleCodes), EnableStatusEnum.DISABLE)
                        .stream()
                        .filter(org -> !ids.contains(org.getId()))
                        .collect(Collectors.toList());
                Validate.isTrue(CollectionUtils.isEmpty(parentDisableList), "存在未启用的上级组织,不能启用当前组织");
            }
        }
        Date endTime = DateUtil.parse(DateUtil.MAX_END_TIME, DateUtil.DEFAULT_DATE_ALL_PATTERN);
        this.orgRepository.updateEnableStatusByIds(ids, EnableStatusEnum.ENABLE, endTime);
        // 发布引擎事件通知
        List<Org> orgList = this.orgRepository.getBaseMapper().selectBatchIds(ids);
        List<OrgEventDto> orgEventDtoList = Lists.newArrayList(this.nebulaToolkitService.copyCollectionByWhiteList(orgList, Org.class, OrgEventDto.class, HashSet.class, ArrayList.class));
        OrgEventBatchDto orgEventBatchDto = new OrgEventBatchDto();
        orgEventBatchDto.setOrgEventDtoList(orgEventDtoList);
        SerializableBiConsumer<OrgEventListener, OrgEventBatchDto> sf = OrgEventListener::onEnableBatch;
        this.nebulaNetEventClient.publish(orgEventBatchDto, OrgEventListener.class, sf);
        for (Org org : orgs) {
            OrgLogEventDto orgLogEventDto = new OrgLogEventDto();
            OrgVo oldObj = new OrgVo();
            OrgVo newObj = new OrgVo();
            oldObj.setId(org.getId());
            newObj.setId(org.getId());
            oldObj.setEnableStatus(org.getEnableStatus());
            oldObj.setEndTime(org.getEndTime());
            newObj.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            newObj.setEndTime(endTime);
            orgLogEventDto.setNewest(newObj);
            orgLogEventDto.setOriginal(oldObj);
            SerializableBiConsumer<OrgLogEventListener, OrgLogEventDto> onUpdate = OrgLogEventListener::onUpdate;
            this.nebulaNetEventClient.publish(orgLogEventDto, OrgLogEventListener.class, onUpdate);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableBatch(List<String> ids) {
        Validate.notEmpty(ids, "id集合不能为空");
        //日志原始对象
        List<Org> orgList = this.orgRepository.findDetailsByIds(ids);
        List<Org> entityList = this.findChildrenOrgByIds(ids);
        Date endTime = new Date();
        if (CollectionUtils.isNotEmpty(entityList)) {
            Set<String> orgCodeSet = orgList.stream()
                    .filter(k -> StringUtil.isNotEmpty(k.getOrgCode()))
                    .map(Org::getOrgCode).collect(Collectors.toSet());
            entityList.forEach(item -> {
                if (!orgCodeSet.contains(item.getOrgCode())) {
                    Validate.isTrue(!EnableStatusEnum.ENABLE.getCode().equals(item.getEnableStatus()),
                            "下级组织:" + item.getOrgName() + "[" + item.getOrgCode() + "]启用，请将下级组织禁用后重试");
                }
            });

        }
        this.orgRepository.updateEnableStatusByIds(ids, EnableStatusEnum.DISABLE, endTime);
        // 日志处理
        // 发布引擎事件通知
        List<OrgEventDto> orgEventDtoList = Lists.newArrayList(this.nebulaToolkitService.copyCollectionByWhiteList(orgList, Org.class, OrgEventDto.class, HashSet.class, ArrayList.class));
        OrgEventBatchDto orgEventBatchDto = new OrgEventBatchDto();
        orgEventBatchDto.setOrgEventDtoList(orgEventDtoList);
        SerializableBiConsumer<OrgEventListener, OrgEventBatchDto> sf = OrgEventListener::onDisableBatch;
        this.nebulaNetEventClient.publish(orgEventBatchDto, OrgEventListener.class, sf);
        for (Org org : orgList) {
            OrgLogEventDto orgLogEventDto = new OrgLogEventDto();
            OrgVo oldObj = new OrgVo();
            OrgVo newObj = new OrgVo();
            oldObj.setId(org.getId());
            newObj.setId(org.getId());
            oldObj.setEnableStatus(org.getEnableStatus());
            oldObj.setEndTime(org.getEndTime());
            newObj.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
            newObj.setEndTime(endTime);
            orgLogEventDto.setNewest(newObj);
            orgLogEventDto.setOriginal(oldObj);
            SerializableBiConsumer<OrgLogEventListener, OrgLogEventDto> onUpdate = OrgLogEventListener::onUpdate;
            this.nebulaNetEventClient.publish(orgLogEventDto, OrgLogEventListener.class, onUpdate);
        }
    }

    @Override
    @Transactional
    public Org create(Org org) {
        Org current = this.createForm(org);
        Org oldOrg = this.orgRepository.findAllByOrgCode(org);
        if (!Objects.isNull(oldOrg)) {
            throw new IllegalArgumentException("组织编码已存在（已逻辑删除）");
        }
        //新增租户编号
        current.setTenantCode(TenantUtils.getTenantCode());
        this.orgRepository.save(current);
        // TODO 清除组织缓存
        //日志处理
        OrgLogEventDto orgLogEventDto = new OrgLogEventDto();
        orgLogEventDto.setNewest(this.nebulaToolkitService.copyObjectByWhiteList(current, OrgVo.class, HashSet.class, ArrayList.class));
        SerializableBiConsumer<OrgLogEventListener, OrgLogEventDto> onCreate =
                OrgLogEventListener::onCreate;
        this.nebulaNetEventClient.publish(orgLogEventDto, OrgLogEventListener.class, onCreate);
        return current;
    }

    @Override
    @Transactional
    public Org update(Org org) {
        Org current = this.updateForm(org);
        // TODO 清除组织缓存
        return current;
    }

    @Override
    public List<Org> findAllChildrenById(String id) {
        Org parent = this.findDetailsById(id);
        if (Objects.isNull(parent)) {
            return Lists.newArrayList();
        }
        String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), parent.getRuleCode());
        List<Org> graph = cache.getIfPresent(cacheKey);
        if (graph == null) {
            graph = this.orgRepository.findChildrenByRuleCode(
                    parent.getRuleCode(), TenantUtils.getTenantCode());
            cache.put(cacheKey, graph);
        }
        return graph;
    }

    @Override
    public List<Org> findAllChildrenByOrgCode(String orgCode) {
        Org parent = this.findByOrgCode(orgCode);
        if (Objects.isNull(parent)) {
            return Lists.newArrayList();
        }
        String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), parent.getRuleCode());
        List<Org> graph = cache.getIfPresent(cacheKey);
        if (graph == null) {
            graph = this.orgRepository.findChildrenByRuleCode(
                    parent.getRuleCode(), TenantUtils.getTenantCode());
            cache.put(cacheKey, graph);
        }
        return graph;
    }

    @Override
    public List<Org> findAllChildrenByOrgCodes(List<String> orgCodes) {
        if (CollectionUtils.isEmpty(orgCodes)) {
            return Lists.newArrayList();
        }
        List<String> ruleCodes = this.orgRepository.findRuleCodeByOrgCodes(orgCodes, TenantUtils.getTenantCode());
        if (CollectionUtils.isEmpty(ruleCodes)) {
            return Lists.newArrayList();
        }
        return this.findChildrenByRuleCode(ruleCodes);
    }

    @Override
    public List<Org> findChildrenById(String id) {
        Org parent = this.findDetailsById(id);
        if (Objects.isNull(parent)) {
            return Lists.newArrayList();
        }
        return this.orgRepository.findByParentCode(parent.getOrgCode(), TenantUtils.getTenantCode());
    }

    @Override
    public List<Org> findChildrenByOrgCode(String orgCode) {
        if (StringUtils.isBlank(orgCode)) {
            return Lists.newArrayList();
        }
        String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), orgCode);
        List<Org> graph = cache.getIfPresent(cacheKey);
        if (graph == null) {
            graph = this.orgRepository.findByParentCode(orgCode, TenantUtils.getTenantCode());
            cache.put(cacheKey, graph);
        }
        return graph;
    }

    @Override
    public List<Org> findAllParentById(String id) {
        Org current = this.findDetailsById(id);
        if (Objects.isNull(current)) {
            return Lists.newArrayList();
        }
        return this.findAllParentByRuleCodes(Lists.newArrayList(current.getRuleCode()));
    }

    @Override
    public List<Org> findAllParentByOrgCode(String orgCode) {
        Org current = this.findByOrgCode(orgCode);
        if (Objects.isNull(current)) {
            return Lists.newArrayList();
        }
        return this.findAllParentByRuleCodes(Lists.newArrayList(current.getRuleCode()));
    }

    @Override
    public List<Org> findAllParentByOrgCodes(List<String> orgCodes) {
        if (CollectionUtils.isEmpty(orgCodes)) {
            return Lists.newArrayList();
        }
        List<Org> orgs = this.findByOrgCodes(orgCodes);
        if (CollectionUtils.isEmpty(orgs)) {
            return Lists.newArrayList();
        }
        List<String> ruleCodes =
                orgs.stream()
                        .filter(item -> StringUtils.isNotBlank(item.getRuleCode()))
                        .map(Org::getRuleCode)
                        .collect(Collectors.toList());
        return this.findAllParentByRuleCodes(ruleCodes);
    }

    @Override
    public List<Org> findAllParentByRuleCodes(List<String> ruleCodes) {
        if (CollectionUtils.isEmpty(ruleCodes)) {
            return Lists.newArrayList();
        }
        Validate.notNull(this.treeRuleCodeStrategyHolder, "系统未配置降维码策略控制器");
        TreeRuleCodeStrategy treeRuleCodeStrategy = this.treeRuleCodeStrategyHolder.getStrategy(null);
        Validate.notNull(treeRuleCodeStrategy, "系统未配置降维码策略");
        Set<String> ruleCodeAll = treeRuleCodeStrategy.findParentRuleCodeByRuleCodes(ruleCodeLength, ruleCodes);
        if (CollectionUtils.isEmpty(ruleCodeAll)) {
            return Lists.newArrayList();
        }
        return this.findByRuleCodesAndEnableStatus(new ArrayList<>(ruleCodeAll), EnableStatusEnum.ENABLE);
    }

    @Override
    public List<Org> findAllParentByOrgCodesExcludeSelf(List<String> orgCodes) {
        List<Org> orgs = this.findByOrgCodes(orgCodes);
        if (CollectionUtils.isEmpty(orgs)) {
            return Lists.newArrayList();
        }
        List<String> ruleCodes = orgs.stream().map(Org::getRuleCode).collect(Collectors.toList());
        TreeRuleCodeStrategy treeRuleCodeStrategy = this.treeRuleCodeStrategyHolder.getStrategy(null);
        Set<String> ruleCodeAll =
                treeRuleCodeStrategy.findParentRuleCodesByRuleCodesExcludeSelf(
                        //OrgCodeConstant.RULE_CODE_LENGTH, ruleCodes);
                        ruleCodeLength, ruleCodes);
        if (CollectionUtils.isEmpty(ruleCodeAll)) {
            return Lists.newArrayList();
        }
        return this.findByRuleCodesAndEnableStatus(new ArrayList<>(ruleCodeAll), null);
    }

    @Override
    public Org findParentById(String id) {
        Org current = this.findDetailsById(id);
        if (Objects.isNull(current)) {
            return null;
        }
        if (StringUtils.isBlank(current.getParentCode())) {
            return null;
        }
        return this.findByOrgCode(current.getParentCode());
    }

    @Override
    public Org findParentByOrgCode(String orgCode) {
        Org current = this.findByOrgCode(orgCode);
        if (Objects.isNull(current)) {
            return null;
        }
        if (StringUtils.isBlank(current.getParentCode())) {
            return null;
        }
        return this.findByOrgCode(current.getParentCode());
    }

    @Override
    public List<Org> findAllChildrenByOrgCodes(Pageable pageable, List<String> orgCodes) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
        if (CollectionUtils.isEmpty(orgCodes)) {
            return Lists.newArrayList();
        }
        List<String> ruleCodes = this.orgRepository.findRuleCodeByOrgCodes(orgCodes, TenantUtils.getTenantCode());
        if (CollectionUtils.isEmpty(ruleCodes)) {
            return Lists.newArrayList();
        }
        String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), ruleCodes, pageable.getPageNumber(), pageable.getPageSize());
        List<Org> graph = cache.getIfPresent(cacheKey);
        if (graph == null) {
            Page<Org> page = this.orgRepository.findChildrenByRuleCodes(pageable, ruleCodes, TenantUtils.getTenantCode());
            if (null == page) {
                return Lists.newArrayList();
            }
            graph = page.getRecords();
            cache.put(cacheKey, graph);
        }
        return graph;
    }

    @Override
    public List<Org> findByConditions(OrgPaginationDto dto) {
        if (dto == null) {
            dto = new OrgPaginationDto();
        }
        dto.setTenantCode(TenantUtils.getTenantCode());
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(dto.getTenantCode())) {
            sb.append(dto.getTenantCode());
        }
        if (StringUtils.isNotBlank(dto.getEnableStatus())) {
            sb.append(dto.getEnableStatus());
        }
        if (StringUtils.isNotBlank(dto.getParentCode())) {
            sb.append(dto.getParentCode());
        }
        if (StringUtils.isNotBlank(dto.getOrgType())) {
            sb.append(dto.getOrgType());
        }
        if (Objects.nonNull(dto.getLevelNum())) {
            sb.append(dto.getLevelNum());
        }
        if (StringUtils.isNotBlank(dto.getOrgCode())) {
            sb.append(dto.getOrgCode());
        }
        if (StringUtils.isNotBlank(dto.getOrgName())) {
            sb.append(dto.getOrgName());
        }
        String cacheKey = sb.toString();
        List<Org> graph = cache.getIfPresent(cacheKey);
        if (graph == null) {
            graph = this.orgRepository.findByConditions(dto);
            cache.put(cacheKey, graph);
        }
        return graph;
    }

    @Override
    public List<Org> findByParentCode(String orgCode) {
        return this.findChildrenByOrgCode(orgCode);
    }

    @Override
    @Transactional
    public void updateRuleCode() {
        // 1.查找parentCode不为空但找不到对应上级的数据，将其parentCode设为空
        orgRepository.updateOrphanParentCodeNull(TenantUtils.getTenantCode());
        // 2.查找所有parentCode为空的数据（相当于第一层数据）
        //List<Org> topList = orgRepository.(TenantUtils.getTenantCode());

        //--findListWithoutParentCode()方法只筛选了parentCode为 "" 并没有判断parentCode为 null 的，findListNoParentCode() 该方法对 findListWithoutParentCode()方法进行了改进--
        List<Org> topList = orgRepository.findListNoParentCode(TenantUtils.getTenantCode());
        // 3.递归设置其ruleCode和其所有子级的ruleCode
        TreeRuleCodeStrategy treeRuleCodeStrategy = this.treeRuleCodeStrategyHolder.getStrategy(null);
        for (int i = 0; i < topList.size(); i++) {
            // 递归调用findListWithoutParentCode
            this.updateCurAndChildrenRuleCode(
                    topList.get(i).getOrgCode(),
                    //treeRuleCodeStrategy.generateByNum(OrgCodeConstant.RULE_CODE_LENGTH, i + ONE),
                    treeRuleCodeStrategy.generateByNum(ruleCodeLength, i + ONE),
                    ONE);
        }
    }

    @Override
    public List<Org> findAllChildrenByOrgTypes(List<String> orgTypes) {
        if (CollectionUtils.isEmpty(orgTypes)) {
            return Lists.newArrayList();
        }
        List<String> ruleCodes =
                this.orgRepository.findRuleCodeByOrgTypes(orgTypes, TenantUtils.getTenantCode());
        if (CollectionUtils.isEmpty(ruleCodes)) {
            return Lists.newArrayList();
        }
        return this.findChildrenByRuleCode(ruleCodes);
    }

    @Override
    public List<Org> findChildrenByRuleCode(List<String> ruleCodes) {
        if (CollectionUtils.isEmpty(ruleCodes)) {
            return Lists.newArrayList();
        }
        List<Org> result = Lists.newArrayList();
        ruleCodes.forEach(ruleCode -> {
            String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), ruleCode);
            List<Org> graph = cache.getIfPresent(cacheKey);
            if (graph == null) {
                graph = this.orgRepository.findChildrenByRuleCode(ruleCode, TenantUtils.getTenantCode());
                cache.put(cacheKey, graph);
            }
            result.addAll(graph);
        });
        return result;
    }

    @Override
    public List<Org> findByRuleCodesAndEnableStatus(List<String> ruleCodes, EnableStatusEnum enable) {
        if (CollectionUtils.isEmpty(ruleCodes)) {
            return Lists.newArrayList();
        }
        String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), ruleCodes, enable != null ? enable.getCode() : null);
        List<Org> graph = cache.getIfPresent(cacheKey);
        if (graph == null) {
            graph = this.orgRepository.findByRuleCodesAndEnableStatus(ruleCodes, enable, TenantUtils.getTenantCode());
            cache.put(cacheKey, graph);
        }
        return graph;
    }

    @Override
    public List<Org> findByOrgType(String orgType) {
        if (StringUtils.isBlank(orgType)) {
            return Lists.newArrayList();
        }
        return this.orgRepository.findByOrgType(orgType);
    }


    /**
     * 通过组织查询大区及其下级组织
     *
     * @param pageable
     * @param dto
     * @return
     */
    @Override
    public Page<OrgVo> findOrgListByOrgCodeAndRegionSubordinate(Pageable pageable, OrgPaginationDto dto) {
        Validate.notNull(dto.getOrgCode(), "组织编码不能为空");
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        List<String> orgTypeList = Lists.newArrayList(OrgTypeEnum.REGION.getDictCode(), OrgTypeEnum.DIVISION.getDictCode(), OrgTypeEnum.AREA.getDictCode());
        dto.setOrgTypeList(orgTypeList);
        Org org = orgRepository.findByOrgCode(dto.getOrgCode(), TenantUtils.getTenantCode());
        String ruleCode = org.getRuleCode();
        dto.setRuleCode(ruleCode);
        Page<OrgVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return orgRepository.findOrgListByOrgCodeAndRegionSubordinate(page, dto);
    }

    @Override
    public Page<OrgVo> findBearOrgList(Pageable pageable, OrgPaginationDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(0, 50));
        List<String> orgTypeList = Lists.newArrayList(OrgTypeEnum.REGION.getDictCode(), OrgTypeEnum.DIVISION.getDictCode(), OrgTypeEnum.AREA.getDictCode());
        dto.setOrgTypeList(orgTypeList);
        Page<OrgVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return orgRepository.findOrgListByOrgCodeAndRegionSubordinate(page, dto);
    }

    @Override
    public Page<String> findNameByLevel(Pageable pageable, String queryType) {
        //OrgTypeEnum.getByKey()
        return null;
    }

    /**
     * 更新自己及子集的降维编码
     *
     * @param orgCode
     * @param ruleCode
     * @param levelNum
     */
    private void updateCurAndChildrenRuleCode(String orgCode, String ruleCode, int levelNum) {
        // 更新当前
        Org org = orgRepository.findDetailsByCode(orgCode, TenantUtils.getTenantCode());
        if (org == null) {
            return;
        }
        org.setRuleCode(ruleCode);
        org.setLevelNum(levelNum);
        org.setLevelNumStr(String.valueOf(levelNum));
        //重构修改方法
        orgRepository.updateByIdAndTenantCode(org, TenantUtils.getTenantCode());
        // 查询下一层
        List<Org> childrenList =
                orgRepository.findChildrenListByParentCode(orgCode, TenantUtils.getTenantCode());
        if (CollectionUtils.isEmpty(childrenList)) {
            return;
        }
        // 遍历下级
        TreeRuleCodeStrategy treeRuleCodeStrategy = this.treeRuleCodeStrategyHolder.getStrategy(null);
        for (int i = 0; i < childrenList.size(); i++) {
            // 递归调用
            this.updateCurAndChildrenRuleCode(
                    childrenList.get(i).getOrgCode(),
                    //ruleCode + treeRuleCodeStrategy.generateByNum(OrgCodeConstant.RULE_CODE_LENGTH, i + ONE),
                    ruleCode + treeRuleCodeStrategy.generateByNum(ruleCodeLength, i + ONE),
                    levelNum + ONE);
        }
    }

    /**
     * 更新
     *
     * @param org
     * @return
     */
    private Org updateForm(Org org) {
        this.updateValidation(org);
        // 这里可根据id或者code更新
        String currentId = org.getId();
        Org current = this.orgRepository.findByIdAndTenantCode(currentId, TenantUtils.getTenantCode());
        //日志旧对象
        //original 对象用于业务日志原始对象
        Org oldOrg = this.nebulaToolkitService.copyObjectByWhiteList(current, Org.class, HashSet.class, ArrayList.class);
        Validate.notNull(current, "未发现指定的原始模型对象信");
        Validate.isTrue(Objects.equals(org.getOrgCode(), current.getOrgCode()), "组织编码不能修改");
        int levelNum = ONE;
        String parentCode = org.getParentCode();
        String parentRuleCode = null;
        List<Org> children = Lists.newArrayList();
        List<TreeDto> childrenDto = Lists.newArrayList();
        if (StringUtils.isNotEmpty(parentCode)) {
            Org parent = this.findByOrgCode(parentCode);
            Validate.notNull(parent, "上级组织不存在");
            Validate.isTrue(!parent.getRuleCode().startsWith(current.getRuleCode()), "上级组织不能是当前组织的下级组织");
            Validate.isTrue(!parentCode.equals(org.getOrgCode()), "上级组织不能是自己!");
            levelNum = parent.getLevelNum() + ONE;
            parentRuleCode = parent.getRuleCode();
            children = this.orgRepository.findByParentCode(parentCode, TenantUtils.getTenantCode());
        } else {
            children = this.orgRepository.findByParentCodeIsNull(TenantUtils.getTenantCode());
        }
        if (CollectionUtils.isNotEmpty(children)) {
            childrenDto = (List<TreeDto>) this.nebulaToolkitService.copyCollectionByWhiteList(children, Org.class, TreeDto.class, HashSet.class, ArrayList.class);
        }
        String oldParentCode = current.getParentCode();
        BeanUtils.copyProperties(
                org, current, "id", "modifyTime", "createAccount", "createTime", "tenantCode", "ruleCode");
        // 开始赋值——更新时间与更新人
        Date now = new Date();
        String account = this.loginUserService.getLoginAccountName();
        current.setLevelNum(levelNum);
        current.setLevelNumStr(String.valueOf(levelNum));
        current.setModifyAccount(account);
        current.setModifyTime(now);
        //新增租户编号
        current.setTenantCode(TenantUtils.getTenantCode());
        if (Objects.isNull(current.getStartTime())) {
            current.setStartTime(new Date());
        }
        this.orgRepository.saveOrUpdate(current);
        if (StringUtils.isBlank(org.getParentCode())) {
            //如果ParentCode为空，则手动设置为空
            orgRepository.setParentCodeNull(org.getId());
        }
        // 判断启用状态是否发生变化
        if (!Objects.equals(org.getEnableStatus(), current.getEnableStatus())) {
            if (Objects.equals(org.getEnableStatus(), EnableStatusEnum.DISABLE.getCode())) {
                this.disableBatch(Lists.newArrayList(org.getId()));
            } else if (Objects.equals(org.getEnableStatus(), EnableStatusEnum.ENABLE.getCode())) {
                this.enableBatch(Lists.newArrayList(org.getId()));
            }
        }
        // 是否重置降维编码 取决于上级有无变化,更新所有下级节点 ruleCode
        if (!Objects.equals(parentCode, oldParentCode)) {
            Validate.notNull(this.treeRuleCodeStrategyHolder, "系统未配置降维码策略控制器");
            TreeRuleCodeStrategy treeRuleCodeStrategy = this.treeRuleCodeStrategyHolder.getStrategy(null);
            Validate.notNull(treeRuleCodeStrategy, "系统未配置降维码策略");
            String ruleCode = treeRuleCodeStrategy.generate(ruleCodeLength, parentRuleCode, childrenDto);
            this.updateRuleCodeAllChildren(org.getOrgCode(), ruleCode, levelNum);
            current.setRuleCode(ruleCode);
        }
        OrgEventDto orgEventDto = nebulaToolkitService.copyObjectByWhiteList(current, OrgEventDto.class, HashSet.class, ArrayList.class);
        orgEventDto.setOldParentCode(Optional.ofNullable(oldParentCode).orElse(StringUtils.EMPTY));
        orgEventDto.setParentCode(Optional.ofNullable(org.getParentCode()).orElse(StringUtils.EMPTY));
        SerializableBiConsumer<OrgEventListener, OrgEventDto> sf = OrgEventListener::onUpdate;
        nebulaNetEventClient.publish(orgEventDto, OrgEventListener.class, sf);
        //日志
        OrgLogEventDto orgLogEventDto = new OrgLogEventDto();
        orgLogEventDto.setOriginal(this.nebulaToolkitService.copyObjectByWhiteList(oldOrg, OrgVo.class, HashSet.class, ArrayList.class));
        orgLogEventDto.setNewest(this.nebulaToolkitService.copyObjectByWhiteList(org, OrgVo.class, HashSet.class, ArrayList.class));
        SerializableBiConsumer<OrgLogEventListener, OrgLogEventDto> onUpdate =
                OrgLogEventListener::onUpdate;
        this.nebulaNetEventClient.publish(orgLogEventDto, OrgLogEventListener.class, onUpdate);
        return current;
    }

    /**
     * 创建
     *
     * @param org
     * @return
     */
    private Org createForm(Org org) {
        /*
         * 对静态模型的保存操作过程为：
         * 1、如果当前模型对象不是主模型
         *  1.1、那么创建前只会验证基本信息，直接的ManyToOne关联（单选）和ManyToMany关联（多选）
         *  1.2、验证完成后，也只会保存当前对象的基本信息，直接的单选
         *  1.3、ManyToMany的关联（多选），暂时需要开发人员自行处理
         * 2、如果当前模型对象是主业务模型
         *  2.1、创建前会验证当前模型的基本属性，单选和多选属性
         *  2.2、然后还会验证当前模型关联的各个OneToMany明细信息，调用明细对象的服务，明每一条既有明细进行验证
         *  （2.2的步骤还需要注意，如果当前被验证的关联对象是回溯对象，则不需要验证了）
         * 2.3、还会验证当前模型关联的各个OneToOne分组，调用分组对象的服务，对分组中的信息进行验证
         *   2.3.1、包括验证每一个分组项的基本信息、直接的单选、多选信息
         *   2.3.2、以及验证每个分组的OneToMany明细信息
         * */
        Date now = new Date();
        String account = this.loginUserService.getLoginAccountName();
        org.setCreateAccount(account);
        org.setModifyAccount(account);
        org.setTenantCode(TenantUtils.getTenantCode());
        org.setCreateTime(now);
        org.setModifyTime(now);
        org.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        org.setEnableStatus(
                null != org.getEnableStatus() ? org.getEnableStatus() : EnableStatusEnum.ENABLE.getCode());
        if (StringUtils.isBlank(org.getOrgCode())) {
            String code = crmSequeseFactoryService.nextVal(new OrgBySnowFlakeDto());
            org.setOrgCode(code);
        } else {
            Org exits = this.findByOrgCode(org.getOrgCode());
            Validate.isTrue(exits == null, "组织编码已经存在");
        }
        int levelNum = 1;
        if (StringUtils.isNotEmpty(org.getParentCode())) {
            Org parent = this.orgRepository.findDetailsByCode(org.getParentCode(), TenantUtils.getTenantCode());
            Validate.notNull(parent, "上级组织不存在!");
            levelNum = parent.getLevelNum() + 1;
        }
        org.setParentCode(Optional.ofNullable(org.getParentCode()).orElse(StringUtils.EMPTY));
        String parentCode = org.getParentCode();
        String ruleCode = this.getRuleCodeByParentCode(parentCode);
        org.setRuleCode(ruleCode);
        org.setLevelNum(levelNum);
        org.setLevelNumStr(String.valueOf(levelNum));
        String orgType = org.getOrgType();
        if (StringUtils.isBlank(orgType)) {
            for (OrgTypeEnum value : OrgTypeEnum.values()) {
                String order = value.getOrder();
                int integer = Integer.parseInt(order);
                if (integer == levelNum) {
                    org.setOrgType(value.getDictCode());
                }
            }
        }
        this.createValidation(org);
        return org;
    }

    /**
     * 校验数据
     *
     * @param org
     */
    private void createValidation(Org org) {
        this.baseValidation(org);
        org.setId(null);
        if (StringUtils.isNotBlank(org.getOrgCode())) {
            Validate.isTrue(org.getOrgCode().length() < 32, "组织编码信息，在进行新增时填入值超过了限定长度(32)，请检查!");
        }
    }

    private void baseValidation(Org org) {
        Validate.notNull(org, "进行当前操作时，信息对象必须传入!!");
        org.setTenantCode(TenantUtils.getTenantCode());
        Validate.notBlank(org.getOrgName(), "组织名称不能为空！");
        Validate.notBlank(org.getOrgType(), "组织类型不能为空！");
        String parentCode = org.getParentCode();
        if (StringUtils.isNotBlank(parentCode)) {
            Org parent = this.findByOrgCode(parentCode);
            Validate.notNull(parent, "上级组织不存在!");
        }
        Validate.isTrue(org.getOrgName().length() < 64, "组织名称，填入值超过了限定长度(64)，请检查!");
        Validate.isTrue(org.getOrgDesc() == null || org.getOrgDesc().length() < 64, "组织描述，填入值超过了限定长度(64)，请检查!");
        if (Objects.isNull(org.getStartTime())) {
            org.setStartTime(new Date());
        }
        if (Objects.isNull(org.getEndTime())) {
            org.setEndTime(DateUtil.parse(DateUtil.MAX_END_TIME, DateUtil.DEFAULT_DATE_ALL_PATTERN));
        }
        Validate.isTrue(org.getEndTime().compareTo(org.getStartTime()) > 0, "结束时间必须大于开始时间,请检查!");

        String availableStatus = org.getAvailableStatus();
        if (StrUtil.isNotBlank(availableStatus)) {
            Validate.isTrue(StrUtil.equals(BusinessConstant.BOOLEAN_YES, availableStatus) ||
                    StrUtil.equals(BusinessConstant.BOOLEAN_NO, availableStatus), "是否生效字段有误");
        } else {
            // 默认生效
            org.setAvailableStatus(BusinessConstant.BOOLEAN_YES);
        }
    }

    /**
     * 更新校验
     *
     * @param org
     */
    private void updateValidation(Org org) {
        this.baseValidation(org);
        Validate.notBlank(org.getId(), "修改信息时，id不能为空！");
    }

    /**
     * 通过id集合查询所以子节点的orgCode
     *
     * @param ids
     * @return
     */
    private List<String> findChildrenOrgCodeByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<String> ruleCodes = this.orgRepository.findRuleCodeByIds(ids);
        if (CollectionUtils.isEmpty(ruleCodes)) {
            return Lists.newArrayList();
        }
        return this.orgRepository.findChildrenOrgCodeByRuleCodes(ruleCodes, TenantUtils.getTenantCode());
    }

    private List<Org> findChildrenOrgByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<String> ruleCodes = this.orgRepository.findRuleCodeByIds(ids);
        if (CollectionUtils.isEmpty(ruleCodes)) {
            return Lists.newArrayList();
        }
        return this.orgRepository.findChildrenOrgByRuleCodes(ruleCodes, TenantUtils.getTenantCode());
    }

    /**
     * 更新组织及组织下级降维编码和层级
     *
     * @param orgCode  当前组织编码
     * @param ruleCode 当前组织降维编码
     * @param levelNum 当前层级
     */
    private void updateRuleCodeAllChildren(String orgCode, String ruleCode, int levelNum) {
        // 更新当前
        this.orgRepository.updateRuleCodeAndLevelNumByOrgCode(
                orgCode, ruleCode, levelNum, TenantUtils.getTenantCode());
        // 查询下一层
        List<Org> children = this.orgRepository.findByParentCode(orgCode, TenantUtils.getTenantCode());
        if (CollectionUtils.isNotEmpty(children)) {
            // 遍历下级
            for (int i = 0; i < children.size(); i++) {
                // 递归调用
                Org item = children.get(i);
                Validate.notNull(this.treeRuleCodeStrategyHolder, "系统未配置降维码策略控制器");
                TreeRuleCodeStrategy treeRuleCodeStrategy =
                        this.treeRuleCodeStrategyHolder.getStrategy(null);
                Validate.notNull(treeRuleCodeStrategy, "系统未配置降维码策略");
                updateRuleCodeAllChildren(
                        item.getOrgCode(),
                        ruleCode
                                //+ treeRuleCodeStrategy.generateByNum(OrgCodeConstant.RULE_CODE_LENGTH, i + ONE),
                                + treeRuleCodeStrategy.generateByNum(ruleCodeLength, i + ONE),
                        (levelNum + ONE));
            }
        }
    }

    /**
     * 获取降维码
     *
     * @param parentCode
     * @return
     */
    private String getRuleCodeByParentCode(String parentCode) {
        String parentRuleCode = null;
        if (StringUtils.isNotEmpty(parentCode)) {
            Org parent = this.orgRepository.findDetailsByCode(parentCode, TenantUtils.getTenantCode());
            Validate.notNull(parent, "上级组织不存在");
            parentRuleCode = parent.getRuleCode();
        }
        List<Org> childrenListByParentCode =
                this.orgRepository.findChildrenListByParentCode(parentCode, TenantUtils.getTenantCode());
        List<TreeDto> childrenDto =
                Lists.newArrayList(
                        nebulaToolkitService.copyCollectionByWhiteList(
                                childrenListByParentCode,
                                Org.class,
                                TreeDto.class,
                                HashSet.class,
                                ArrayList.class));
        // TODO 编码降维编码长度以前可以在系统设置中进行设置，该功能将在后续改造中实现，先设置为固定默认值
        TreeRuleCodeStrategy treeRuleCodeStrategy = treeRuleCodeStrategyHolder.getStrategy(null);
        return treeRuleCodeStrategy.generate(
                //OrgCodeConstant.RULE_CODE_LENGTH, parentRuleCode, childrenDto);
                ruleCodeLength, parentRuleCode, childrenDto);
    }
}
