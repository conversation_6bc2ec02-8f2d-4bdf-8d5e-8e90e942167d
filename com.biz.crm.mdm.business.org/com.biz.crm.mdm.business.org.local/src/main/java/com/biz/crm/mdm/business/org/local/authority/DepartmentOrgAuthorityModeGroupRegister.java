package com.biz.crm.mdm.business.org.local.authority;

import com.bizunited.nebula.mars.sdk.register.SelectAuthorityModeGroupRegister;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * @Author: heguanyun
 * @Date: 2022/4/7 11:09 description:按职位维度进行授权的选项分组
 */
@Component
public class DepartmentOrgAuthorityModeGroupRegister implements SelectAuthorityModeGroupRegister {
    @Override
    public String groupCode() {
        return "department_org_group";
    }

    @Override
    public String groupName() {
        return "按照使用部门维度授权";
    }

    @Override
    public Set<String> viewFieldNames() {
        return Sets.newHashSet( "belongDepartmentCode", "belong_department_code");
    }

    @Override
    public Set<String> repositoryFieldNames() {
        return Sets.newHashSet( "belong_department_code");
    }
}
