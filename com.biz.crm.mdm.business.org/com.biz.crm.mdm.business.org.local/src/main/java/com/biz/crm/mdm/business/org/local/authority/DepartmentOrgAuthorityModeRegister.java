package com.biz.crm.mdm.business.org.local.authority;

import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.mdm.business.position.sdk.service.PositionOrgVoService;
import com.bizunited.nebula.mars.sdk.register.SelectAuthorityModeRegister;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;

/**
 * @Author: heguanyun
 * @Date: 2022/4/7 10:11 description:匹配当前登录者/操作者所属组织
 */
@Component
public class DepartmentOrgAuthorityModeRegister implements SelectAuthorityModeRegister {

    @Autowired(required = false)
    private PositionOrgVoService positionOrgVoService;

    @Override
    public String modeKey() {
        return "departmentOrgAuthorityModeRegister";
    }

    @Override
    public String modeName() {
        return "按照当前登录者/操作者所属组织进行组织维度的值确认";
    }

    @Override
    public String controlKey() {
        return "departmentOrgAuthorityModeRegister";
    }

    @Override
    public int sort() {
        return 3;
    }

    @Override
    public String groupCode() {
        return "department_org_group";
    }

    @Override
    public boolean isArrayValue() {
        return false;
    }

    @Override
    public boolean isStaticValue() {
        return false;
    }

    @Override
    public Class<?> modeValueClass() {
        return String.class;
    }

    @Override
    public Object staticValue(String[] staticValues) {
        return CommonConstant.NOT_AUTHORITY_ARR;
    }

    @Override
    public Object dynamicValue(UserIdentity loginDetails, String modeGroupCode) {
        String identityType = loginDetails.getIdentityType();
        //如果不是后台管理用户，就不组织位字段进行权限控制
        if (!StringUtils.equals(identityType, "u")) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        AbstractCrmUserIdentity loginUserDetails = (AbstractCrmUserIdentity) loginDetails;
        Object object = loginUserDetails.invokeFieldValue("orgCode");
        if (Objects.isNull(object)) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        Set<String> orgsByPositionCode = positionOrgVoService.findOrgsByPositionCode(loginUserDetails.getPostCode());
        orgsByPositionCode.add(object.toString());
        return orgsByPositionCode.toArray(new String[0]);
    }


    @Override
    public String converterKey() {
        return "chartEqualMarsAuthorityValueAstConverter";
    }
}
