package com.biz.crm.mdm.business.org.local.service;

import com.biz.crm.mdm.business.org.local.entity.OrgRegion;

import java.util.List;

/**
 * 组织区域接口
 *
 * <AUTHOR>
 * @date 2021/09/27
 */
public interface OrgRegionService {

  /**
   * 按orgCode 查询
   *
   * @param orgCode
   * @return
   */
  List<OrgRegion> findByOrgCode(String orgCode);

  /**
   * 创建
   *
   * @param orgRegions
   * @return
   */
  List<OrgRegion> createBatch(List<OrgRegion> orgRegions);

  /**
   * 通过组织codes查询与区域的关联关系
   *
   * @param orgCodes
   * @return
   */
  List<OrgRegion> findByOrgCodes(List<String> orgCodes);
}
