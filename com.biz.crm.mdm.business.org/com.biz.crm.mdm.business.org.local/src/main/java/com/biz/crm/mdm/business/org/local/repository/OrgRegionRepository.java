package com.biz.crm.mdm.business.org.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.org.local.entity.OrgRegion;
import com.biz.crm.mdm.business.org.local.mapper.OrgRegionMapper;
import com.biz.crm.mdm.business.org.sdk.dto.OrgRegionDto;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description: 组织职位关系 数据层
 * @date 2021/9/26 下午4:32
 */
@Repository
public class OrgRegionRepository extends ServiceImpl<OrgRegionMapper, OrgRegion> {


  /**
   * 按orgCode 查询
   *
   * @param orgCode
   * @return
   */
  public List<OrgRegion> findByOrgCode(String orgCode, String tenantCode) {
    return this.lambdaQuery().eq(OrgRegion::getOrgCode, orgCode)
        .eq(OrgRegion::getTenantCode, tenantCode).list();
  }

  /**
   * 按条件查询
   *
   * @param dto
   * @return
   */
  public List<OrgRegion> findByConditions(OrgRegionDto dto) {
    return this.lambdaQuery()
        .eq(StringUtils.isNotBlank(dto.getCountryCode()), OrgRegion::getCountryCode, dto.getCountryCode())
        .eq(StringUtils.isNotBlank(dto.getProvinceCode()), OrgRegion::getProvinceCode, dto.getProvinceCode())
        .eq(StringUtils.isNotBlank(dto.getCityCode()), OrgRegion::getCityCode, dto.getCityCode())
        .eq(StringUtils.isNotBlank(dto.getDistrictCode()), OrgRegion::getDistrictCode, dto.getDistrictCode())
        .eq(StringUtils.isNotBlank(dto.getTownshipCode()), OrgRegion::getTownshipCode, dto.getTownshipCode())
        .eq(OrgRegion::getTenantCode, dto.getTenantCode())
        .list();
  }

  /**
   * 按orgCode删除
   *
   * @param orgCode
   * @param tenantCode
   */
  public void deleteByOrgCode(String orgCode, String tenantCode) {
    this.baseMapper.deleteByOrgCode(orgCode, tenantCode);
  }

  public List<OrgRegion> findByOrgCodes(List<String> orgCodes, String tenantCode) {
    return this.lambdaQuery().in(OrgRegion::getOrgCode, orgCodes)
        .eq(OrgRegion::getTenantCode, tenantCode).list();
  }
}
