package com.biz.crm.mdm.business.org.local.authority;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.mdm.business.org.local.entity.Org;
import com.biz.crm.mdm.business.org.local.service.OrgService;
import com.bizunited.nebula.mars.sdk.register.SelectAuthorityModeRegister;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 按照固定组织及其下级组织维度的值确认
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/8/2 14:12
 */
@Component
public class OrgSelectAuthorityModeRegisterForSelectDown implements SelectAuthorityModeRegister {

    @Autowired(required = false)
    private OrgService orgService;

    @Override
    public String modeKey() {
        return "orgSelectAuthorityModeRegisterForSelectDown";
    }

    @Override
    public String modeName() {
        return "按照固定组织及其下级组织维度的值确认";
    }

    @Override
    public String controlKey() {
        return "orgSelectAuthorityModeRegisterForSelectDown_selectOrgTree";
    }

    @Override
    public int sort() {
        return 7;
    }

    @Override
    public String groupCode() {
        return "org_group";
    }

    @Override
    public String converterKey() {
        return "chartArrayMarsAuthorityAstConverter";
    }

    @Override
    public boolean isArrayValue() {
        return true;
    }

    @Override
    public boolean isStaticValue() {
        return true;
    }

    @Override
    public Class<?> modeValueClass() {
        return String.class;
    }

    @Override
    public Object staticValue(String[] staticValues) {
        if (null == staticValues || staticValues.length == 0) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        List<String> orgCodes = Lists.newArrayList(staticValues);
        List<Org> children = orgService.findAllChildrenByOrgCodes(orgCodes);
        if (CollectionUtil.isEmpty(children)) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        List<String> childrenCodeList = children.stream().map(Org::getOrgCode)
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        childrenCodeList.addAll(orgCodes);
        if (CollectionUtil.isEmpty(childrenCodeList)) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        childrenCodeList = childrenCodeList.stream().distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(childrenCodeList)) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        return childrenCodeList.toArray(new String[0]);
    }

    @Override
    public Object dynamicValue(UserIdentity loginDetails, String modeGroupCode) {
        return CommonConstant.NOT_AUTHORITY_ARR;
    }
}
