package com.biz.crm.mdm.business.org.local.service.internal;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.business.common.sdk.service.TreeRuleCodeStrategy;
import com.biz.crm.business.common.sdk.service.TreeRuleCodeStrategyHolder;
import com.biz.crm.mdm.business.cost.sdk.service.MdmCostCenterVoService;
import com.biz.crm.mdm.business.cost.sdk.vo.MdmCostCenterVo;
import com.biz.crm.mdm.business.org.local.entity.Org;
import com.biz.crm.mdm.business.org.local.repository.OrgRepository;
import com.biz.crm.mdm.business.org.local.service.OrgService;
import com.biz.crm.mdm.business.org.sdk.constant.OrgConstant;
import com.biz.crm.mdm.business.org.sdk.dto.OrgPaginationDto;
import com.biz.crm.mdm.business.org.sdk.dto.OrgPositionUserQueryDto;
import com.biz.crm.mdm.business.org.sdk.dto.OrgQueryDto;
import com.biz.crm.mdm.business.org.sdk.dto.RelateOrgCodeQueryDto;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgPositionUserVo;
import com.biz.crm.mdm.business.org.sdk.vo.OrgTreeByTypeVo;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.org.sdk.vo.SfaOrgCountVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 组织vo serivce 实现
 * @date 2021/9/29 上午11:27
 */
@Service
public class OrgVoServiceImpl implements OrgVoService {

    @Autowired(required = false)
    private OrgService orgService;
    @Autowired(required = false)
    private OrgRepository orgRepository;
    @Autowired(required = false)
    @Qualifier("nebulaToolkitService")
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private TreeRuleCodeStrategyHolder treeRuleCodeStrategyHolder;
    @Autowired(required = false)
    private MdmCostCenterVoService costCenterVoService;
    @Resource
    private RedisService redisService;

    @Value("${crm.business.rule-code-length.org:}")
    private Integer ruleCodeLength;

    @Override
    public Page<OrgVo> findOrgByConditions(Pageable pageable, OrgPaginationDto dto) {
        return this.orgRepository.findOrgByConditions(pageable, dto);
    }

    /**
     * 通过组织名称查询组织信息
     *
     * @param orgNameList
     * @return
     */
    @Override
    public List<OrgVo> findOrgByOrgNameList(List<String> orgNameList) {
        List<OrgVo> orgList = Lists.newArrayList();
        List<List<String>> partitionList = Lists.partition(orgNameList, 800);
        for (List<String> strings : partitionList) {
            List<Org> orgs = orgRepository.findOrgByOrgNameList(strings);
            if (CollectionUtils.isNotEmpty(orgs)) {
                orgList.addAll(nebulaToolkitService.copyCollectionByWhiteList(orgs, Org.class, OrgVo.class, HashSet.class, ArrayList.class));
            }
        }
        return orgList;
    }

    @Override
    public Page<OrgVo> findByConditions(Pageable pageable, OrgPaginationDto paginationDto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
        paginationDto = ObjectUtils.defaultIfNull(paginationDto, new OrgPaginationDto());
        Page<Org> page = this.orgService.findByConditions(pageable, paginationDto);
        Page<OrgVo> pageVo = new Page<>();
        if (Objects.isNull(page)) {
            return pageVo;
        }
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            List<OrgVo> vos = (List<OrgVo>) this.nebulaToolkitService.copyCollectionByWhiteList(page.getRecords(), Org.class, OrgVo.class, HashSet.class, ArrayList.class);
            this.build(vos);
            pageVo.setRecords(vos);
        }
        pageVo.setCurrent(page.getCurrent());
        pageVo.setPages(page.getPages());
        pageVo.setTotal(page.getTotal());
        return pageVo;
    }

    @Override
    public Page<SfaOrgCountVo> findSfaOrgCount(Pageable pageable, SfaOrgCountVo dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
        dto = ObjectUtils.defaultIfNull(dto, new SfaOrgCountVo());
        if (Objects.isNull(dto.getLevelNum())) {
            //默认查询二级数据
            dto.setLevelNum(3);
        }
        Page<SfaOrgCountVo> page = this.orgRepository.findSfaOrgCount(pageable, dto);
        if (CollectionUtil.isNotEmpty(page.getRecords())) {
            List<SfaOrgCountVo> pageList = page.getRecords();
            List<String> orgCodeList = pageList.stream().map(SfaOrgCountVo::getOrgCode).collect(Collectors.toList());
            Map<String, List<OrgVo>> allChildrenByOrgCodesMap = this.findAllChildrenByOrgCodesMap(orgCodeList);
            for (SfaOrgCountVo vo : pageList) {
                if (allChildrenByOrgCodesMap.containsKey(vo.getOrgCode())) {
                    List<OrgVo> orgVoList = allChildrenByOrgCodesMap.get(vo.getOrgCode());
                    List<String> childOrgCodeList = orgVoList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
                    vo.setChildOrgCodeList(childOrgCodeList);
                } else {
                    vo.setChildOrgCodeList(Lists.newArrayList());
                }
            }
        }
        return page;
    }

    @Override
    public OrgVo findDetailsById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        Org org = this.orgService.findDetailsById(id);
        if (Objects.isNull(org)) {
            return null;
        }
        OrgVo vo = this.nebulaToolkitService.copyObjectByWhiteList(org, OrgVo.class, HashSet.class, ArrayList.class);
        this.build(vo);
        return vo;
    }

    @Override
    public OrgVo findByOrgCode(String orgCode) {
        if (StringUtils.isBlank(orgCode)) {
            return null;
        }
        Org org = this.orgService.findByOrgCode(orgCode);
        if (Objects.isNull(org)) {
            return null;
        }
        OrgVo vo = this.nebulaToolkitService.copyObjectByWhiteList(org, OrgVo.class, HashSet.class, ArrayList.class);
        this.build(vo);
        return vo;
    }

    @Override
    public List<OrgVo> findDetailsByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<Org> orgs = this.orgService.findDetailsByIds(ids);
        if (CollectionUtils.isEmpty(orgs)) {
            return Lists.newArrayList();
        }
        List<OrgVo> vos = (List<OrgVo>) this.nebulaToolkitService.copyCollectionByWhiteList(orgs, Org.class, OrgVo.class, HashSet.class, ArrayList.class);
        this.build(vos);
        return vos;
    }

    @Override
    public List<OrgVo> findAllParentByOrgCode(String orgCode) {
        if (StringUtils.isNotEmpty(orgCode)) {
            List<Org> orgs = orgService.findAllParentByOrgCode(orgCode);
            if (CollectionUtils.isEmpty(orgs)) {
                return Lists.newArrayList();
            }
            List<OrgVo> vos = (List<OrgVo>) this.nebulaToolkitService.copyCollectionByWhiteList(orgs, Org.class, OrgVo.class, HashSet.class, ArrayList.class);
            this.build(vos);
            return vos;
        }
        return Lists.newArrayList();
    }

    @Override
    public List<OrgVo> findAllParentByOrgCodes(List<String> orgCodes) {
        if (CollectionUtils.isNotEmpty(orgCodes)) {
            List<Org> orgs = orgService.findAllParentByOrgCodes(orgCodes);
            if (CollectionUtils.isEmpty(orgs)) {
                return Lists.newArrayList();
            }
            List<OrgVo> vos = (List<OrgVo>) this.nebulaToolkitService.copyCollectionByWhiteList(orgs, Org.class, OrgVo.class, HashSet.class, ArrayList.class);
            this.build(vos);
            return vos;
        }
        return Lists.newArrayList();
    }

    /**
     * 根据 组织编码集合查询 全部上级（含当前）组织列表
     *
     * @param orgCodes
     * @return
     */
    @Override
    public Map<String, List<OrgVo>> findAllParentByOrgCodesMap(List<String> orgCodes) {
        if (CollectionUtil.isEmpty(orgCodes)) {
            return Maps.newHashMap();
        }
        List<OrgVo> orgs = this.findAllParentByOrgCodes(orgCodes);
        if (CollectionUtil.isEmpty(orgs)) {
            return Maps.newHashMap();
        }
        Map<String, List<OrgVo>> result = Maps.newLinkedHashMap();
        Map<String, String> orgCodeRuleCodeMap = orgs.stream()
                .filter(k -> StringUtils.isNotEmpty(k.getOrgCode()))
                .filter(k -> StringUtils.isNotEmpty(k.getRuleCode()))
                .collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getRuleCode, (n, o) -> n));
        orgCodes.forEach(orgCode -> {
            String ruleCode = orgCodeRuleCodeMap.getOrDefault(orgCode, "");
            List<OrgVo> orgOneList = Lists.newArrayList();
            if (StringUtils.isNotEmpty(ruleCode)) {
                orgOneList.addAll(Lists.newArrayList(orgs).stream().filter(k -> StringUtils.isNotEmpty(k.getRuleCode()))
                        .filter(k -> ruleCode.startsWith(k.getRuleCode())).collect(Collectors.toMap(OrgVo::getOrgCode, v -> v, (n, o) -> n)).values());
            }
            result.put(orgCode, orgOneList);
        });

        return result;
    }

    @Override
    public Map<String, OrgTreeByTypeVo> findAllParentTreeByOrgCodes(List<String> orgCodes) {
        Map<String, OrgTreeByTypeVo> result = new HashMap<>();
        Map<String, List<OrgVo>> orgListMap = findAllParentByOrgCodesMap(orgCodes);
        if (CollUtil.isEmpty(orgListMap)) {
            return result;
        }
        orgListMap.forEach( (k,v) -> {
            OrgTreeByTypeVo orgTreeByTypeVo = new OrgTreeByTypeVo(v, k);
            orgTreeByTypeVo.init();
            result.put(k, orgTreeByTypeVo);
        });
        return result;
    }

    @Override
    public OrgTreeByTypeVo findAllParentTreeByOrgCode(String orgCode) {
        List<OrgVo> orgVoList =  findAllParentByOrgCode(orgCode);
        if (CollUtil.isEmpty(orgVoList)) {
            return new OrgTreeByTypeVo();
        }
        OrgTreeByTypeVo orgTreeByTypeVo = new OrgTreeByTypeVo(orgVoList, orgCode);
        orgTreeByTypeVo.init();
        return orgTreeByTypeVo;
    }

    @Override
    public Set<String> findByOrgQueryDto(OrgQueryDto dto) {
        if (Objects.isNull(dto) || StringUtils.isAllBlank(dto.getOrgName(), dto.getOrgType())) {
            return Sets.newHashSet();
        }
        return this.orgRepository.findByOrgQueryDto(dto);
    }

    @Override
    public List<OrgVo> findByOrgCodes(List<String> orgCodes) {
        if (CollectionUtils.isEmpty(orgCodes)) {
            return Lists.newArrayList();
        }
        List<Org> orgs = this.orgService.findByOrgCodes(orgCodes);
        if (CollectionUtils.isEmpty(orgs)) {
            return Lists.newArrayList();
        }
        List<OrgVo> vos = (List<OrgVo>) this.nebulaToolkitService.copyCollectionByWhiteList(orgs, Org.class, OrgVo.class, HashSet.class, ArrayList.class);
        this.build(vos);
        return vos;
    }

    @Override
    public List<OrgVo> findAllChildrenById(String id) {
        if (StringUtils.isBlank(id)) {
            return Lists.newArrayList();
        }
        List<Org> orgs = this.orgService.findAllChildrenById(id);
        if (CollectionUtils.isEmpty(orgs)) {
            return Lists.newArrayList();
        }
        List<OrgVo> vos = (List<OrgVo>) this.nebulaToolkitService.copyCollectionByWhiteList(orgs, Org.class, OrgVo.class, HashSet.class, ArrayList.class);
        this.build(vos);
        return vos;
    }

    @Override
    public List<OrgVo> findAllChildrenByOrgCode(String orgCode) {
        if (StringUtils.isBlank(orgCode)) {
            return Lists.newArrayList();
        }
        List<Org> orgs = this.orgService.findAllChildrenByOrgCode(orgCode);
        if (CollectionUtils.isEmpty(orgs)) {
            return Lists.newArrayList();
        }
        List<OrgVo> vos = (List<OrgVo>) this.nebulaToolkitService.copyCollectionByWhiteList(orgs, Org.class, OrgVo.class, HashSet.class, ArrayList.class);
        this.build(vos);
        return vos;
    }

    @Override
    public List<OrgVo> findAllChildrenByOrgCodes(List<String> orgCodes) {
        if (CollectionUtils.isEmpty(orgCodes)) {
            return Lists.newArrayList();
        }
        List<Org> orgs = this.orgService.findAllChildrenByOrgCodes(orgCodes);
        if (CollectionUtils.isEmpty(orgs)) {
            return Lists.newArrayList();
        }
        List<OrgVo> orgVoList = (List<OrgVo>) this.nebulaToolkitService.copyCollectionByWhiteList(orgs, Org.class, OrgVo.class, HashSet.class, ArrayList.class);
        this.build(orgVoList);
        return orgVoList;
    }

    @Override
    public Map<String, List<OrgVo>> findAllChildrenByOrgCodesMap(List<String> orgCodes) {
        if (CollectionUtil.isEmpty(orgCodes)) {
            return Maps.newHashMap();
        }
        List<OrgVo> orgs = this.findAllChildrenByOrgCodes(orgCodes);
        if (CollectionUtil.isEmpty(orgs)) {
            return Maps.newHashMap();
        }
        Map<String, List<OrgVo>> result = Maps.newLinkedHashMap();
        Map<String, String> orgCodeRuleCodeMap = orgs.stream()
                .filter(k -> StringUtils.isNotEmpty(k.getOrgCode()))
                .filter(k -> StringUtils.isNotEmpty(k.getRuleCode()))
                .collect(Collectors.toMap(OrgVo::getOrgCode, OrgVo::getRuleCode, (n, o) -> n));
        orgCodes.forEach(orgCode -> {
            String ruleCode = orgCodeRuleCodeMap.getOrDefault(orgCode, "");
            List<OrgVo> orgOneList = Lists.newArrayList();
            if (StringUtils.isNotEmpty(ruleCode)) {
                orgOneList.addAll(Lists.newArrayList(orgs).stream().filter(k -> StringUtils.isNotEmpty(k.getRuleCode()))
                        .filter(k -> k.getRuleCode().startsWith(ruleCode)).collect(Collectors.toMap(OrgVo::getOrgCode, v -> v, (n, o) -> n)).values());
            }
            this.build(orgOneList);
            result.put(orgCode, orgOneList);
        });

        return result;
    }

    @Override
    public List<OrgVo> findAllChildrenByOrgTypes(List<String> orgTypes) {
        if (CollectionUtils.isEmpty(orgTypes)) {
            return Lists.newArrayList();
        }
        List<Org> orgs = this.orgService.findAllChildrenByOrgTypes(orgTypes);
        if (CollectionUtils.isEmpty(orgs)) {
            return Lists.newArrayList();
        }
        return (List<OrgVo>) this.nebulaToolkitService.copyCollectionByWhiteList(orgs, Org.class, OrgVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public Map<String, String> findByRelateOrgCodeQueryDto(RelateOrgCodeQueryDto dto) {
        if (Objects.isNull(dto) || CollectionUtils.isEmpty(dto.getOrgCodeSet())) {
            return Maps.newHashMap();
        }
        dto.setSearchType(Optional.ofNullable(dto.getSearchType()).orElse(0));
        List<Org> orgList = this.orgService.findByOrgCodes(Lists.newArrayList(dto.getOrgCodeSet()));
        if (CollectionUtils.isEmpty(orgList)) {
            return Maps.newHashMap();
        }
        if (dto.getSearchType() == 0) {
            return orgList.stream()
                    .filter(a -> StringUtils.isNoneBlank(a.getOrgCode(), a.getRuleCode()))
                    .collect(Collectors.toMap(Org::getOrgCode, Org::getRuleCode, (a, b) -> a));
        }
        List<String> ruleCodeList =
                orgList.stream()
                        .filter(a -> StringUtils.isNotBlank(a.getRuleCode()))
                        .map(Org::getRuleCode)
                        .distinct()
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ruleCodeList)) {
            return Maps.newHashMap();
        }

        TreeRuleCodeStrategy treeRuleCodeStrategy = treeRuleCodeStrategyHolder.getStrategy(null);
        if (dto.getSearchType() > 0) {
            Set<String> ruleCodeSet =
                    treeRuleCodeStrategy.findParentRuleCodesByRuleCodesExcludeSelf(
                            //OrgCodeConstant.RULE_CODE_LENGTH, ruleCodeList);
                            ruleCodeLength, ruleCodeList);
            if (CollectionUtils.isEmpty(ruleCodeSet)) {
                ruleCodeSet = Sets.newHashSet();
            }
            ruleCodeSet.addAll(ruleCodeList);
            if (CollectionUtils.isNotEmpty(ruleCodeSet)) {
                final List<Org> cur = this.orgService.findByRuleCodesAndEnableStatus(Lists.newLinkedList(ruleCodeSet), null);
                if (CollectionUtils.isEmpty(cur)) {
                    return Maps.newHashMap();
                }
                return cur.stream()
                        .collect(Collectors.toMap(Org::getOrgCode, Org::getRuleCode, (a, b) -> a));
            }
        } else {
            final List<Org> cur = this.orgService.findChildrenByRuleCode(ruleCodeList);
            if (CollectionUtils.isEmpty(cur)) {
                return Maps.newHashMap();
            }
            return cur.stream().collect(Collectors.toMap(Org::getOrgCode, Org::getRuleCode, (a, b) -> a));
        }
        return Maps.newHashMap();
    }


    /**
     * 构建返回字段
     *
     * @param vo
     */
    private void build(OrgVo vo) {
        OrgVo parent = vo.getParent();
        if (parent != null) {
            vo.setParentName(parent.getOrgName());
        }
    }

    /**
     * 构建返回字段
     *
     * @param vos
     */
    private void build(List<OrgVo> vos) {
        if (CollectionUtil.isEmpty(vos)) {
            return;
        }
        List<String> orgCodes = vos.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
        Map<String, List<MdmCostCenterVo>> costCenterMap = costCenterVoService.findListByOrgCodes(orgCodes);
        for (OrgVo item : vos) {
            if (costCenterMap != null && !costCenterMap.isEmpty() && costCenterMap.containsKey(item.getOrgCode())) {
                Set<String> costCenterCodes = costCenterMap.get(item.getOrgCode()).stream().map(MdmCostCenterVo::getCostCenterCode)
                        .collect(Collectors.toSet());
                item.setCostCenterCodeSet(costCenterCodes);
            }
            this.build(item);
        }
    }

    @Override
    public List<OrgPositionUserVo> findOrgPositionUserByConditions(OrgPositionUserQueryDto dto) {
        if (null == dto) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(dto.getUserNames())) {
            return Lists.newArrayList();
        }
        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        return this.orgRepository.findOrgPositionUserByConditions(dto);
    }

    @Override
    public List<OrgVo> findByOrgType(String orgType) {
        if (StringUtils.isBlank(orgType)) {
            return Lists.newArrayList();
        }
        List<Org> orgs = this.orgService.findByOrgType(orgType);
        if (CollectionUtils.isEmpty(orgs)) {
            return Lists.newArrayList();
        }
        return (List<OrgVo>) this.nebulaToolkitService.copyCollectionByWhiteList(orgs, Org.class, OrgVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public Map<String, OrgVo> findOrgVoByOrgCodes(List<String> orgCodeList) {
        List<OrgVo> orgVoList = this.findByOrgCodes(orgCodeList);
        if (CollectionUtil.isEmpty(orgVoList)) {
            return Maps.newHashMap();
        }
        return orgVoList.stream().filter(k -> StringUtil.isNotEmpty(k.getOrgCode())).collect(Collectors.toMap(OrgVo::getOrgCode, v -> v, (n, o) -> n));
    }


    /**
     * 组织数据载入缓存
     */
    @Override
    public List<OrgVo> loadOrgListToCache() {
        if (redisService.hasKey(OrgConstant.ORG_LOAD_REDIS_KEY)) {
            return (List<OrgVo>) redisService.get(OrgConstant.ORG_LOAD_REDIS_KEY);
        }
        OrgPaginationDto dto = new OrgPaginationDto();
        dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        dto.setTenantCode(TenantUtils.getTenantCode());
        List<Org> orgs = orgRepository.findByConditions(dto);
        List<OrgVo> orgVoList = (List<OrgVo>) nebulaToolkitService.copyCollectionByBlankList(orgs, Org.class, OrgVo.class, HashSet.class, ArrayList.class);
        //存放5小时
        redisService.set(OrgConstant.ORG_LOAD_REDIS_KEY, orgVoList, 18000);
        return orgVoList;
    }

    @Override
    public List<OrgVo> findAll() {
        List<Org> all = this.orgRepository.findAll();
        List<OrgVo> orgVoList = (List<OrgVo>) nebulaToolkitService.copyCollectionByBlankList(all, Org.class, OrgVo.class, HashSet.class, ArrayList.class);
        return orgVoList;
    }

    @Override
    public List<OrgVo> findByRuleCodeLike(String ruleCode) {
        List<Org> orgs = this.orgRepository.findByRuleCodeLike(ruleCode);
        if (CollectionUtils.isNotEmpty(orgs)) {
            return (List<OrgVo>) nebulaToolkitService.copyCollectionByBlankList(orgs, Org.class, OrgVo.class, HashSet.class, ArrayList.class);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<OrgVo> findAllParentTreeByUserName(String userName) {
        List<Org> orgs = this.orgRepository.findAllParentTreeByUserName(userName);
        if (CollectionUtils.isNotEmpty(orgs)) {
            return (List<OrgVo>) nebulaToolkitService.copyCollectionByBlankList(orgs, Org.class, OrgVo.class, HashSet.class, ArrayList.class);
        }
        return Lists.newArrayList();
    }
}
