package com.biz.crm.mdm.business.cost.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * 成本中心与组织关系表实体类
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/20 11:00
 */
@Getter
@Setter
@ApiModel(value = "MdmCostCenterOrg", description = "成本中心与组织关系表实体类")
@EqualsAndHashCode(callSuper = false)
@TableName("mdm_cost_center_org")
@Table(name = "mdm_cost_center_org",
        indexes = {
                @Index(name = "mdm_cost_center_org_code_uk1", columnList = "cost_center_code,org_code", unique = true),
                @Index(name = "mdm_cost_center_org_code_idx1", columnList = "cost_center_code", unique = false),
                @Index(name = "mdm_cost_center_org_code_idx2", columnList = "org_code", unique = false),
        })
@Entity
public class MdmCostCenterOrg extends TenantEntity {

    private static final long serialVersionUID = -7099573190437123205L;

    @ApiModelProperty("成本中心编码")
    @Column(name = "cost_center_code", nullable = false, columnDefinition = "VARCHAR(32) COMMENT '成本中心编码'")
    private String costCenterCode;

    @ApiModelProperty("组织编码")
    @Column(name = "org_code", nullable = false, columnDefinition = "VARCHAR(32) COMMENT '组织编码'")
    private String orgCode;


}
