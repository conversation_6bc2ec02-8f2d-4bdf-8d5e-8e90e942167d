package com.biz.crm.mdm.business.org.local.service.notifier;
/**
 * Created by <PERSON><PERSON> on 2021-12-02 19:45.
 */

import com.biz.crm.mdm.business.org.local.entity.OrgPosition;
import com.biz.crm.mdm.business.org.local.service.OrgPositionService;
import com.biz.crm.mdm.business.org.sdk.dto.OrgEventBatchDto;
import com.biz.crm.mdm.business.org.sdk.dto.OrgEventDto;
import com.biz.crm.mdm.business.org.sdk.event.OrgEventListener;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: crm
 * @description: 组织与职位关联关系监听组织变更事件
 * @author: <PERSON><PERSON>
 * @create: 2021-12-02 19:45
 **/
@Component
public class OrgEventPositionListenerImpl implements OrgEventListener {
  @Autowired(required = false)
  private OrgPositionService orgPositionService;

  @Override
  public void onDelete(List<String> orgCodes) {
    if (CollectionUtils.isEmpty(orgCodes)) {
      return;
    }
    List<OrgPosition> orgPositions =
        orgPositionService.findByOrgCodes(orgCodes);
    Validate.isTrue(CollectionUtils.isEmpty(orgPositions), "存在组织和职位的关联，无法删除！");
  }

  @Override
  public void onDeleteBatch(OrgEventBatchDto orgEventBatchDto) {

  }

  @Override
  public void onEnableBatch(OrgEventBatchDto orgEventBatchDto) {

  }

  @Override
  public void onDisableBatch(OrgEventBatchDto orgEventBatchDto) {

  }

  @Override
  public void onUpdate(OrgEventDto orgEventDto) {

  }
}
