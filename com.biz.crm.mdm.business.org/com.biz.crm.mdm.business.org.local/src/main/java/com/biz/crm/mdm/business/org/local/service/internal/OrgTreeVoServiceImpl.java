package com.biz.crm.mdm.business.org.local.service.internal;

import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.org.local.entity.Org;
import com.biz.crm.mdm.business.org.local.service.OrgService;
import com.biz.crm.mdm.business.org.sdk.dto.OrgPaginationDto;
import com.biz.crm.mdm.business.org.sdk.service.OrgTreeVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgTreeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 组织树形vo service实现
 * @date 2021/10/13 下午3:02
 */
@Service
public class OrgTreeVoServiceImpl implements OrgTreeVoService {

  @Autowired(required = false)
  private OrgService orgService;
  
  @Autowired(required = false)
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;
  
  @Autowired(required = false)
  private DictDataVoService dictDataVoService;


  @Override
  public List<OrgTreeVo> findAllStruTree() {
    return this.findTree(new OrgPaginationDto());
  }

  @Override
  public List<OrgTreeVo> findByOrgNameStruTree(String orgName, String enableStatus) {
    //orgName 为空时是查询全部 不用做空校验
    List<OrgTreeVo> tree = new ArrayList<>();
    OrgPaginationDto dto = new OrgPaginationDto();
    dto.setOrgName(orgName);
    dto.setEnableStatus(enableStatus);
    List<Org> list = this.orgService.findByConditions(dto);
    if (CollectionUtils.isEmpty(list)) {
      return tree;
    }
    if (StringUtils.isNotBlank(orgName)) {
      List<String> ruleCodes = list.stream().map(Org::getRuleCode).collect(Collectors.toList());
      if (CollectionUtils.isNotEmpty(ruleCodes)) {
        list = this.orgService.findAllParentByRuleCodes(ruleCodes);
      }
    }
    List<OrgTreeVo> collect = (List<OrgTreeVo>) this.nebulaToolkitService.copyCollectionByBlankList(list, Org.class, OrgTreeVo.class, HashSet.class, ArrayList.class);
    Map<String, String> orgTypeMap = dictDataVoService.findMapByDictTypeCode(DictConstant.MDM_ORG_TYPE);
    if (orgTypeMap != null && orgTypeMap.size() > 0) {
      for (OrgTreeVo orgTreeVo : collect) {
        String orgType = orgTreeVo.getOrgType();
        if (StringUtils.isNotBlank(orgType)) {
          orgTreeVo.setOrgTypeName(orgTypeMap.get(orgType));
        }
      }
    }
    for (OrgTreeVo orgTreeVo : collect) {
      if (StringUtils.isBlank(orgTreeVo.getParentCode())) {
        tree.add(orgTreeVo);
      }
      List<OrgTreeVo> children = orgTreeVo.getChildren();
      if (children == null) {
        children = new ArrayList<>();
      }
      for (OrgTreeVo orgTreeRespVo : collect) {
        if (Objects.equals(orgTreeVo.getOrgCode(), orgTreeRespVo.getParentCode())) {
          children.add(orgTreeRespVo);
        }
      }
      orgTreeVo.setChildren(children);
    }
    return tree;
  }

  /**
   * 带条件查询整课树
   *
   * @param orgPaginationDto
   * @return
   */
  @Override
  public List<OrgTreeVo> findAllStruTreeByCondition(OrgPaginationDto orgPaginationDto) {
    return this.findTree(orgPaginationDto);
  }


  /**
   * 查询整课树公共方法
   *
   * @param orgPaginationDto
   * @return
   */
  private List<OrgTreeVo> findTree(OrgPaginationDto orgPaginationDto) {
    /**
     * 1。先查询所有组织
     * 2。找出parentCode为null或者parentCode不存在与数据中 ， 为第一层
     * 3。依次寻找每层的孩子节点
     */
    List<Org> list = this.orgService.findByConditions(orgPaginationDto);
    if (CollectionUtils.isEmpty(list)) {
      return Lists.newArrayList();
    }
    List<OrgTreeVo> totalList = (List<OrgTreeVo>) this.nebulaToolkitService.copyCollectionByBlankList(list, Org.class, OrgTreeVo.class, HashSet.class, ArrayList.class);
    //构建树list
    List<OrgTreeVo> treeList = new ArrayList<>();
    //当前操作层级数据 要查找的层级
    List<OrgTreeVo> currentLevelList = new ArrayList<>();
    //未操作数据
    List<OrgTreeVo> restList = new ArrayList<>();
    //key:id
    Map<String, OrgTreeVo> totalMap = totalList.stream().collect(Collectors.toMap(OrgTreeVo::getOrgCode, v -> v));
    //查找第一层 parentCode为null或者parentCode不存在与数据中 ， 为第一层
    for (OrgTreeVo item : totalList) {
      if (StringUtils.isBlank(item.getParentCode()) || !totalMap.containsKey(item.getParentCode())) {
        treeList.add(item);
        currentLevelList.add(item);
      } else {
        restList.add(item);
      }
    }
    //构建数据，从第二层开始
    while (currentLevelList.size() > 0 && restList.size() > 0) {
      List<OrgTreeVo> restTempList = new ArrayList<>();
      List<OrgTreeVo> curLevelTempList = new ArrayList<>();
      Set<String> curLevelSet = currentLevelList.stream().map(OrgTreeVo::getOrgCode).collect(Collectors.toSet());
      Map<String, List<OrgTreeVo>> curLevelChildrenMap = new HashMap<>();

      for (OrgTreeVo item : restList) {
        //是否属于当前层
        if (curLevelSet.contains(item.getParentCode())) {
          curLevelTempList.add(item);
          List<OrgTreeVo> childrenList = new ArrayList<>();
          if (curLevelChildrenMap.containsKey(item.getParentCode())) {
            childrenList.addAll(curLevelChildrenMap.get(item.getParentCode()));
          }
          childrenList.add(item);
          curLevelChildrenMap.put(item.getParentCode(), childrenList);
        } else {
          restTempList.add(item);
        }
      }
      for (OrgTreeVo item : currentLevelList) {
        if (curLevelChildrenMap.containsKey(item.getOrgCode())) {
          item.setChildren(curLevelChildrenMap.get(item.getOrgCode()));
        }
      }
      currentLevelList.clear();
      currentLevelList.addAll(curLevelTempList);
      restList.clear();
      restList.addAll(restTempList);
    }
    return treeList;
  }

}
