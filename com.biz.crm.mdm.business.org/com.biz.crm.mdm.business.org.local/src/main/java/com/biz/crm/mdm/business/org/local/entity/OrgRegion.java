package com.biz.crm.mdm.business.org.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantOpEntity;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 组织和行政区域关联表实体类
 *
 * <AUTHOR>
 * @date 2021-10-15 10:42:46
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@TableName("mdm_org_region")
@Table(name = "mdm_org_region")
@Entity
public class OrgRegion extends TenantOpEntity {
  private static final long serialVersionUID = -8275164507684832881L;

  /**
   * 组织编码
   */
  @Column(name = "org_code", columnDefinition = "VARCHAR(32) NOT NULL COMMENT '组织编码'")
  @TableField(value = "org_code")
  @ApiModelProperty("组织编码")
  private String orgCode;

  /**
   * 国家编码
   */
  @Column(name = "country_code", length = 64, columnDefinition = "VARCHAR(64) NOT NULL COMMENT '国家编码'")
  @TableField(value = "country_code")
  @ApiModelProperty("国家编码")
  private String countryCode;

  /**
   * 省份编码
   */
  @Column(name = "province_code", length = 64, columnDefinition = "VARCHAR(64) NOT NULL COMMENT '省份编码'")
  @TableField(value = "province_code")
  @ApiModelProperty("国家编码")
  private String provinceCode;

  /**
   * 城市编码
   */
  @Column(name = "city_code", length = 64, columnDefinition = "VARCHAR(64) NOT NULL COMMENT '城市编码'")
  @TableField(value = "city_code")
  @ApiModelProperty("城市编码")
  private String cityCode;

  /**
   * 区县编码
   */
  @Column(name = "district_code", length = 64, columnDefinition = "VARCHAR(64) NOT NULL COMMENT '区县编码'")
  @TableField(value = "district_code")
  @ApiModelProperty("区县编码")
  private String districtCode;

  /**
   * 乡镇编码
   */
  @Column(name = "township_code", length = 64, columnDefinition = "VARCHAR(64) NOT NULL COMMENT '乡镇编码¬'")
  @TableField(value = "township_code")
  @ApiModelProperty("乡镇编码")
  private String townshipCode;

}
