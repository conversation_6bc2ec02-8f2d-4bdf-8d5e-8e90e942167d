package com.biz.crm.mdm.business.org.local.deprecated.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.org.local.entity.Org;
import com.biz.crm.mdm.business.org.local.service.OrgService;
import com.biz.crm.mdm.business.org.sdk.common.enums.OrgTypeEnum;
import com.biz.crm.mdm.business.org.sdk.deprecated.vo.MdmOrgPageSelectReqVo;
import com.biz.crm.mdm.business.org.sdk.deprecated.vo.MdmOrgReqVo;
import com.biz.crm.mdm.business.org.sdk.deprecated.vo.MdmOrgRespVo;
import com.biz.crm.mdm.business.org.sdk.deprecated.vo.MdmOrgSearchReqVo;
import com.biz.crm.mdm.business.org.sdk.deprecated.vo.MdmOrgSelectReqVo;
import com.biz.crm.mdm.business.org.sdk.deprecated.vo.MdmOrgSelectRespVo;
import com.biz.crm.mdm.business.org.sdk.deprecated.vo.MdmOrgTreeRespVo;
import com.biz.crm.mdm.business.org.sdk.dto.OrgPaginationDto;
import com.biz.crm.mdm.business.org.sdk.service.OrgTreeVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgTreeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 旧版组织信息 http 接口
 * @date 2021/9/27 上午10:36
 */
@Api(tags = "旧版组织信息")
@Slf4j
@RestController
@RequestMapping(value = {"/mdmOrgController"})
@Deprecated
public class MdmOrgController {

  @Autowired(required = false)
  private OrgService orgService;
  @Autowired(required = false)
  private OrgTreeVoService orgTreeVoService;
  @Autowired(required = false)
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;

  @ApiOperation(value = "查询分页列表")
  @PostMapping(value = {"/pageList"})
  @Deprecated
  public Result<Page<MdmOrgRespVo>> pageList(@RequestBody MdmOrgReqVo reqVo) {
    try {
      Pageable pageable = PageRequest.of(reqVo.getPageNum(), reqVo.getPageSize());
      OrgPaginationDto paginationDto = new OrgPaginationDto();
      paginationDto.setEnableStatus(reqVo.getEnableStatus());
      paginationDto.setOrgType(reqVo.getOrgType());
      paginationDto.setOrgCode(reqVo.getOrgCode());
      paginationDto.setOrgName(reqVo.getOrgName());
      paginationDto.setParentCode(reqVo.getParentCode());
      paginationDto.setOrgTypeList(reqVo.getOrgTypeList());
      if (StringUtils.isNotBlank(reqVo.getOrgCode())) {
        Org org = this.orgService.findByOrgCode(reqVo.getCode());
        if (org != null) {
          paginationDto.setRuleCode(org.getRuleCode());
        }
      }
      Page<Org> page = this.orgService.findByConditions(pageable, paginationDto);
      Page<MdmOrgRespVo> pageVo = this.build(page);
      return Result.ok(pageVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "组织分页选择列表")
  @PostMapping("/pageSelectList")
  public Result<Page<MdmOrgRespVo>> pageSelectList(@RequestBody MdmOrgPageSelectReqVo reqVo) {
    try {
      Pageable pageable = PageRequest.of(reqVo.getPageNum(), reqVo.getPageSize());
      OrgPaginationDto dao = new OrgPaginationDto();
      dao.setEnableStatus(reqVo.getEnableStatus());
      dao.setOrgType(reqVo.getOrgType());
      dao.setOrgCode(reqVo.getOrgCode());
      dao.setOrgName(reqVo.getOrgName());
      dao.setParentCode(reqVo.getParentCode());
      dao.setSapCostOrgCode(reqVo.getSapCostOrgCode());
      dao.setSapProfitOrgCode(reqVo.getSapProfitOrgCode());
      dao.setSapOrgCode(reqVo.getSapOrgCode());
      dao.setIncludeAllChildrenOrgCode(reqVo.getAllUnderThisOrgCodeIncludeSelf());
      dao.setIncludeAllChildrenOrgCodeExcludeSelf(reqVo.getAllUnderThisOrgCodeExcludeSelf());
      dao.setAllChildrenOrgCodesExcludeAnySelf(reqVo.getAllUnderOrgCodeListExcludeAnySelf());
      dao.setAllChildrenOrgCodesExcludeSelf(reqVo.getAllUnderOrgCodeListExcludeSelf());
      dao.setAllChildrenOrgCodesIncludeSelf(reqVo.getAllUnderOrgCodeListIncludeSelf());
      Page<Org> page = this.orgService.findByConditions(pageable, dao);
      Page<MdmOrgRespVo> pageVo = this.build(page);
      return Result.ok(pageVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "详情")
  @GetMapping("/detail")
  public Result<MdmOrgRespVo> detail(@RequestParam(value = "id", required = false) String id, @RequestParam(value = "orgCode", required = false) String orgCode) {
    try {
      Org org = null;
      if (StringUtils.isNotBlank(id)) {
        org = this.orgService.findDetailsById(id);
      }
      if (StringUtils.isNotBlank(orgCode)) {
        org = this.orgService.findByOrgCode(orgCode);
      }
      MdmOrgRespVo vo = this.build(org);
      return Result.ok(vo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("根据id集合批量查询组织主表信息")
  @PostMapping("/detailBatchByIds")
  @Deprecated
  public Result<List<MdmOrgRespVo>> detailBatchByIds(@RequestBody List<String> ids) {
    try {
      List<Org> orgs = this.orgService.findDetailsByIds(ids);
      List<MdmOrgRespVo> vos = this.build(orgs);
      return Result.ok(vos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("根据组织编码集合批量查询组织主表信息")
  @PostMapping("/detailBatchByOrgCodeList")
  @Deprecated
  public Result<List<MdmOrgRespVo>> detailBatchByOrgCodeList(@RequestBody List<String> orgCodeList) {
    try {
      List<Org> orgs = this.orgService.findByOrgCodes(orgCodeList);
      List<MdmOrgRespVo> vos = this.build(orgs);
      return Result.ok(vos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "删除", httpMethod = "POST")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "ids", value = "id集合", required = true, paramType = "body")
  })
  @PostMapping("/delete")
  public Result<?> delete(@RequestBody List<String> ids) {
    try {
      this.orgService.deleteByIds(ids);
      return Result.ok("删除成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "启用", httpMethod = "POST")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "ids", value = "id集合", required = true, paramType = "body")
  })
  @PostMapping("/enable")
  @Deprecated
  public Result<?> enable(@RequestBody List<String> ids) {
    try {
      orgService.enableBatch(ids);
      return Result.ok("启用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "禁用", httpMethod = "POST")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "ids", value = "id集合", required = true, paramType = "body")
  })
  @PostMapping("/disable")
  @Deprecated
  public Result<?> disable(@RequestBody List<String> ids) {
    try {
      orgService.disableBatch(ids);
      return Result.ok("禁用成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "新增")
  @PostMapping("/save")
  public Result<?> save(@RequestBody Org org) {
    try {
      this.orgService.create(org);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "更新")
  @PostMapping("/update")
  public Result<?> update(@RequestBody Org org) {
    try {
      this.orgService.update(org);
      return Result.ok("修改成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("根据组织id或者编码查询全部下级（含当前）组织列表")
  @PostMapping("/findAllChildrenOrgList")
  public Result<List<MdmOrgRespVo>> findAllChildrenOrgList(@RequestBody OrgPaginationDto dto) {
    try {
      List<Org> list = null;
      if (StringUtils.isNotBlank(dto.getId())) {
        list = orgService.findAllChildrenById(dto.getId());
      } else {
        list = orgService.findAllChildrenByOrgCode(dto.getOrgCode());
      }
      List<MdmOrgRespVo> vos = this.build(list);
      return Result.ok(vos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("根据组织id或者编码查询当前下级组织列表")
  @PostMapping("/findChildrenOrgList")
  public Result<List<MdmOrgRespVo>> findChildrenOrgList(@RequestBody OrgPaginationDto dto) {
    try {
      List<Org> list = null;
      if (StringUtils.isNotBlank(dto.getId())) {
        list = orgService.findChildrenById(dto.getId());
      } else {
        list = orgService.findChildrenByOrgCode(dto.getOrgCode());
      }
      List<MdmOrgRespVo> vos = this.build(list);
      return Result.ok(vos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("根据组织id或者编码查询全部上级（含当前）组织列表")
  @PostMapping("/findAllParentOrgList")
  public Result<List<MdmOrgRespVo>> findAllParentOrgList(@RequestBody OrgPaginationDto dto) {
    try {
      List<Org> list = null;
      if (StringUtils.isNotBlank(dto.getId())) {
        list = orgService.findAllParentById(dto.getId());
      } else {
        list = orgService.findAllParentByOrgCode(dto.getOrgCode());
      }
      List<MdmOrgRespVo> vos = this.build(list);
      return Result.ok(vos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("查询当前组织的直接上级组织")
  @PostMapping("/findSingleParentOrg")
  public Result<MdmOrgRespVo> findSingleParentOrg(@RequestBody OrgPaginationDto dto) {
    try {
      Org parent = null;
      if (StringUtils.isNotBlank(dto.getId())) {
        parent = orgService.findParentById(dto.getId());
      } else {
        parent = orgService.findParentByOrgCode(dto.getOrgCode());
      }
      MdmOrgRespVo parentVo = this.build(parent);
      return Result.ok(parentVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据组织id或者编码查询当前组织的直接上级组织", httpMethod = "GET")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "id", value = "ID", required = false, dataType = "String", paramType = "query"),
      @ApiImplicitParam(name = "orgCode", value = "组织编码", required = false, dataType = "String", paramType = "query")
  })
  @GetMapping("/getParentOrg")
  public Result<MdmOrgRespVo> getParentOrg(@RequestParam(value = "id", required = false) String id, @RequestParam(value = "orgCode", required = false) String orgCode) {
    try {
      Org parent = null;
      if (StringUtils.isNotBlank(id)) {
        parent = orgService.findParentById(id);
      } else {
        parent = orgService.findParentByOrgCode(orgCode);
      }
      MdmOrgRespVo parentVo = this.build(parent);
      return Result.ok(parentVo);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("重置组织树降维编码")
  @PostMapping("/resetRuleCode")
  public Result<?> resetRuleCode() {
    try {
      orgService.updateRuleCode();
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("清除全部缓存")
  @PostMapping("/deleteAllCache")
  public Result<?> deleteAllCache() {
    try {
      //TODO
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("重新加载全部缓存")
  @PostMapping("/reloadAllCache")
  public Result<?> reloadAllCache() {
    try {
      //TODO
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("批量查询组织及下级组织,返回（pageSize）对应条数")
  @PostMapping("/findOrgAndChildrenList")
  public Result<List<MdmOrgRespVo>> findOrgAndChildrenList(@RequestBody MdmOrgSearchReqVo reqVo) {
    try {
      Pageable pageable = PageRequest.of(reqVo.getPageNum(), reqVo.getPageSize());
      List<Org> list = this.orgService.findAllChildrenByOrgCodes(pageable, reqVo.getUnderOrgCodeList());
      List<MdmOrgRespVo> vos = this.build(list);
      return Result.ok(vos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  @ApiOperation("根据组织编码集合查询组织列表")
  @PostMapping("/listCondition")
  public Result<List<MdmOrgRespVo>> listCondition(@RequestBody MdmOrgReqVo reqVo) {
    try {
      List<Org> list = this.orgService.findByOrgCodes(reqVo.getOrgCodeList().stream().collect(Collectors.toList()));
      List<MdmOrgRespVo> vos = this.build(list);
      return Result.ok(vos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("查询所有组织（不分页，数据量大，给后台用）(启用状态、组织类型、上级编码精确查询，组织编码、名称模糊查询)")
  @PostMapping("/findOrgList")
  public Result<List<MdmOrgRespVo>> findOrgList(@RequestBody MdmOrgReqVo reqVo) {
    try {
      OrgPaginationDto dao = new OrgPaginationDto();
      dao.setEnableStatus(reqVo.getEnableStatus());
      dao.setOrgType(reqVo.getOrgType());
      dao.setOrgCode(reqVo.getOrgCode());
      dao.setOrgName(reqVo.getOrgName());
      dao.setParentCode(reqVo.getParentCode());
      List<Org> list = this.orgService.findByConditions(dao);
      List<MdmOrgRespVo> vos = this.build(list);
      return Result.ok(vos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("查询全部组织")
  @GetMapping("/getAll")
  public Result<List<MdmOrgRespVo>> getAll() {
    try {
      OrgPaginationDto dao = new OrgPaginationDto();
      List<Org> list = this.orgService.findByConditions(dao);
      List<MdmOrgRespVo> vos = this.build(list);
      return Result.ok(vos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  @ApiOperation("查询所有组织树形结构（给后台使用），无筛选条件，全部返回（包含停用）")
  @GetMapping("/getOrgTree")
  public Result<List<MdmOrgTreeRespVo>> getOrgTree() {
    try {
      List<OrgTreeVo> trees = this.orgTreeVoService.findAllStruTree();
      List<MdmOrgTreeRespVo> treeVos = null;
      if (CollectionUtils.isNotEmpty(trees)) {
        treeVos = (List<MdmOrgTreeRespVo>) this.nebulaToolkitService.copyCollectionByBlankList(trees, OrgTreeVo.class, MdmOrgTreeRespVo.class, HashSet.class, ArrayList.class);
      }
      return Result.ok(treeVos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("组织搜索树")
  @PostMapping("/orgSearchTree")
  public Result<List<MdmOrgTreeRespVo>> orgSearchTree(@RequestBody MdmOrgReqVo mdmOrgReqVo) {
    try {
      List<OrgTreeVo> trees = orgTreeVoService.findByOrgNameStruTree(mdmOrgReqVo.getOrgName(), StringUtils.EMPTY);
      List<MdmOrgTreeRespVo> treeVos = null;
      if (CollectionUtils.isNotEmpty(trees)) {
        treeVos = (List<MdmOrgTreeRespVo>) this.nebulaToolkitService.copyCollectionByBlankList(trees, OrgTreeVo.class, MdmOrgTreeRespVo.class, HashSet.class, ArrayList.class);
      }
      return Result.ok(treeVos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("根据组织编码集合查询这些组织（不含自己）的上级组织编码，如果参数本身有上下级关系，则会返回处于上级的组织编码")
  @PostMapping("/findAllParentOrgCodeExcludeSelf")
  public Result<List<String>> findAllParentOrgCodeExcludeSelf(@RequestBody List<String> orgCodeList) {
    try {
      List<Org> list = this.orgService.findAllParentByOrgCodesExcludeSelf(orgCodeList);
      List<String> parentOrgCodes = null;
      if (CollectionUtils.isNotEmpty(list)) {
        parentOrgCodes = list.stream().map(Org::getOrgCode).collect(Collectors.toList());
      }
      return Result.ok(parentOrgCodes);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("组织下拉框(只查启用的)")
  @PostMapping("/select")
  public Result<List<MdmOrgSelectRespVo>> select(@RequestBody MdmOrgSelectReqVo reqVo) {
    try {
      Pageable pageable = PageRequest.of(1, reqVo.getPageSize());
      OrgPaginationDto dto = new OrgPaginationDto();
      dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
      if (reqVo != null) {
        dto.setOrgType(reqVo.getOrgType());
        dto.setOrgCode(reqVo.getOrgCode());
        dto.setOrgName(reqVo.getOrgName());
        dto.setParentCode(reqVo.getParentCode());
        dto.setCodeOrName(reqVo.getOrgCodeOrName());
        dto.setExcludeAllChildrenOrgCode(reqVo.getNotUnderThisOrgCode());
        dto.setIncludeAllChildrenOrgCode(reqVo.getUnderThisOrgCode());
      }
      Page<Org> page = this.orgService.findByConditions(pageable, dto);
      List<MdmOrgSelectRespVo> vos = null;
      if (Objects.nonNull(page)) {
        vos = (List<MdmOrgSelectRespVo>) this.nebulaToolkitService.copyCollectionByBlankList(page.getRecords(), Org.class, MdmOrgSelectRespVo.class, HashSet.class, ArrayList.class);
      }
      return Result.ok(vos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("组织下拉框")
  @PostMapping("/findOrgSelectList")
  public Result<List<MdmOrgSelectRespVo>> findOrgSelectList(@RequestBody MdmOrgSelectReqVo reqVo) {
    try {
      Pageable pageable = PageRequest.of(1, reqVo.getPageSize());
      OrgPaginationDto dto = new OrgPaginationDto();
      if (reqVo != null) {
        dto.setOrgType(reqVo.getOrgType());
        dto.setOrgCode(reqVo.getOrgCode());
        dto.setOrgName(reqVo.getOrgName());
        dto.setParentCode(reqVo.getParentCode());
        dto.setCodeOrName(reqVo.getOrgCodeOrName());
        dto.setExcludeAllChildrenOrgCode(reqVo.getNotUnderThisOrgCode());
        dto.setIncludeAllChildrenOrgCode(reqVo.getUnderThisOrgCode());
      }
      Page<Org> page = this.orgService.findByConditions(pageable, dto);
      List<MdmOrgSelectRespVo> vos = null;
      if (Objects.nonNull(page)) {
        vos = (List<MdmOrgSelectRespVo>) this.nebulaToolkitService.copyCollectionByBlankList(page.getRecords(), Org.class, MdmOrgSelectRespVo.class, HashSet.class, ArrayList.class);
      }
      return Result.ok(vos);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  /**
   * 新org转 旧org page
   *
   * @param page
   * @return
   */
  private Page<MdmOrgRespVo> build(Page<Org> page) {
    Page<MdmOrgRespVo> pageVo = new Page<>();
    if (Objects.isNull(page)) {
      pageVo.setCurrent(page.getCurrent());
    }
    if (CollectionUtils.isNotEmpty(page.getRecords())) {
      List<MdmOrgRespVo> vos = this.build(page.getRecords());
      pageVo.setRecords(vos);
    }
    pageVo.setCurrent(page.getCurrent());
    pageVo.setPages(page.getPages());
    pageVo.setTotal(page.getTotal());
    return pageVo;
  }


  /**
   * 新org转 旧org
   *
   * @param org
   * @return
   */
  private MdmOrgRespVo build(Org org) {
    if (Objects.isNull(org)) {
      return null;
    }
    MdmOrgRespVo mdmOrgRespVo = this.nebulaToolkitService.copyObjectByBlankList(org, MdmOrgRespVo.class, HashSet.class, ArrayList.class);
    if (Objects.nonNull(org.getParent())) {
      mdmOrgRespVo.setParentName(org.getParent().getOrgName());
    }
    return mdmOrgRespVo;
  }

  /**
   * 新org转 旧org list
   *
   * @param orgs
   * @return
   */
  private List<MdmOrgRespVo> build(List<Org> orgs) {
    if (CollectionUtils.isEmpty(orgs)) {
      return null;
    }
    List<MdmOrgRespVo> vos = orgs.stream().map(item -> {
      return this.build(item);
    }).collect(Collectors.toList());
    return vos;
  }
}
