package com.biz.crm.mdm.business.material.feign.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.material.feign.feign.MaterialCostVoFeign;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialCostDto;
import com.biz.crm.mdm.business.material.sdk.service.MaterialCostVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialCostQueryVo;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialCostVo;
import liquibase.pro.packaged.M;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Service
public class MaterialCostVoFeignServiceImpl implements MaterialCostVoService {


    @Resource
    private MaterialCostVoFeign materialCostVoFeign;

    @Override
    public Page<MaterialCostVo> findByConditions(Pageable pageable, MaterialCostDto dto) {
        throw new UnsupportedOperationException();
    }

    @Override
    public MaterialCostVo findDetailById(String id) {
        throw new UnsupportedOperationException();
    }

    @Override
    public List<MaterialCostVo> findDetailByIds(Set<String> ids) {
        throw new UnsupportedOperationException();
    }

    @Override
    public List<MaterialCostVo> findDetailByCode(String code) {
        throw new UnsupportedOperationException();
    }

    @Override
    public List<MaterialCostVo> findDetailByCodes(Set<String> codeList) {
        throw new UnsupportedOperationException();
    }

    @Override
    public MaterialCostVo findDetailByCodeAndCompanyCodeAndYear(String code, String companyCode, String yearStr) {
        return materialCostVoFeign.findDetailByCodeAndCompanyCodeAndYear(code, companyCode, yearStr).checkFeignResult();
    }

    @Override
    public List<MaterialCostVo> findDetailByCodesAndCompanyCodeAndYear(Set<String> codeList, String companyCode, String yearStr) {
        MaterialCostQueryVo vo = new MaterialCostQueryVo();
        vo.setCodeList(codeList);
        vo.setCompanyCode(companyCode);
        vo.setYears(yearStr);
        return materialCostVoFeign.findDetailByCondition(vo).checkFeignResult();
    }

    @Override
    public List<MaterialCostVo> findListByCodesAndCompanyCodesAndYear(Set<String> codeList, List<String> companyCodes, String years) {
        MaterialCostQueryVo queryVo = new MaterialCostQueryVo();
        queryVo.setCodeList(codeList);
        queryVo.setCompanyCodes(companyCodes);
        queryVo.setYears(years);
        return materialCostVoFeign.findListByCodesAndCompanyCodesAndYear(queryVo).checkFeignResult();
    }
}
