package com.biz.crm.mdm.business.material.feign.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.material.feign.feign.internal.MaterialVoServiceFeignImpl;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialDto;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

/**
 * 物料feign
 *
 * <AUTHOR>
 * @date 2021/11/16
 */
@FeignClient( name = "${mdm.feign-client.name:crm-mdm}", path = "crm-mdm", fallbackFactory = MaterialVoServiceFeignImpl.class)
public interface MaterialVoServiceFeign {

  /**
   * 查询所有物料信息
   *
   * @return 查询结果
   */
  @GetMapping("/findAll")
  Result<List<MaterialVo>> findAll();

  /**
   * 根据物料编码查询详情
   *
   * @param materialCode
   * @return
   */
  @GetMapping("/v1/material/material/findDetailByMaterialCode")
  Result<MaterialVo> findDetailByMaterialCode(@RequestParam(value = "materialCode") String materialCode);

  /**
   * 根据物料编码集合查询详情
   *
   * @param materialCodes
   * @return
   */
  @GetMapping("/v1/material/material/findDetailByMaterialCodes")
  Result<List<MaterialVo>> findDetailByMaterialCodes(@RequestParam(value = "materialCodes") Set<String> materialCodes);
  /**
   * 根据物料编码集合查询主信息
   *
   * @param materialCodes
   * @return
   */
  @PostMapping("/v1/material/material/findByMaterialCodes")
  Result<List<MaterialVo>> findByMaterialCodes(@RequestBody Set<String> materialCodes);

    @GetMapping("/v1/material/material/findByConditions")
    Result<Page<MaterialVo>> findByConditions(@RequestParam("page") Integer page, @RequestParam("size") Integer size,
                                              @SpringQueryMap MaterialDto materialDto);
}
