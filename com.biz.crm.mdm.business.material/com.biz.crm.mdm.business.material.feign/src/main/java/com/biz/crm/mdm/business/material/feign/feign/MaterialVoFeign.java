package com.biz.crm.mdm.business.material.feign.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.material.feign.feign.internal.MaterialVoFeignImpl;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialPageDto;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialSearchDto;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.price.sdk.vo.PriceMilkCostVo;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

/**
 * 物料feign接口类
 *
 * <AUTHOR>
 * @date 2022/2/18
 */
@FeignClient(

        name = "${mdm.feign-client.name:crm-mdm}",
        path = "crm-mdm",
        fallbackFactory = MaterialVoFeignImpl.class)
public interface MaterialVoFeign {

    /**
     * 获取物料数据分页信息
     *
     * @param dto  请求参数
     * @param page 当前页数
     * @param size 分页大小
     * @return 物料数据分页信息
     */
    @GetMapping("/v1/material/material/findByMaterialPageDto")
    Result<Page<MaterialVo>> findByMaterialPageDto(
            @RequestParam("page") Integer page,
            @RequestParam("size") Integer size,
            @SpringQueryMap MaterialPageDto dto);

    /**
     * 根据产品层级获取启用的未删除的物料编码集合
     *
     * @param productLevelCodeSet
     * @return
     */
    @PostMapping("/v1/material/material/findCodeByProductLevelCodes")
    Result<Set<String>> findCodeByProductLevelCodes(@RequestBody Set<String> productLevelCodeSet);

    /**
     * 根据物料编码集合查询详情
     *
     * @param materialCodes
     * @return
     */
    @GetMapping("/v1/material/material/findDetailByMaterialCodes")
    Result<List<MaterialVo>> findDetailByMaterialCodes(
            @RequestParam(value = "materialCodes") Set<String> materialCodes);

    /**
     * 根据物料编码获取物料信息
     *
     * @param materialCode
     * @return
     */
    @GetMapping("/v1/material/material/findDetailByMaterialCodes")
    Result<MaterialVo> findDetailByMaterialCode(@RequestParam(value = "materialCode") String materialCode);

    @PostMapping("/v1/material/material/findListByNameList")
    Result<List<MaterialVo>> findListByNameList(@RequestBody List<String> materialNames);

    @PostMapping("/v1/material/material/findBySearchDto")
    Result<List<MaterialVo>> findBySearchDto(@RequestBody MaterialSearchDto dto);

    /**
     * 根据物料组编码获取物料编码
     *
     * @param materialGroupCode
     * @return
     */
    @GetMapping("/v1/material/material/findByMaterialGroupCode")
    Result<List<String>> findByMaterialGroupCode(@RequestParam String materialGroupCode);


    /**
     * 根据奶卡物料编码获取奶卡成本价
     *
     * @param codeList
     * @return
     */
    @PostMapping("/v1/material/material/findMilkCostPriceByCodes")
    Result<List<PriceMilkCostVo>> findMilkCostPriceByCodes(@RequestBody Set<String> codeList);

    @ApiOperation(value = "物料载入缓存")
    @GetMapping("/v1/material/material/loadCacheMaterial")
    Result<List<MaterialVo>> loadCacheMaterial();
}
