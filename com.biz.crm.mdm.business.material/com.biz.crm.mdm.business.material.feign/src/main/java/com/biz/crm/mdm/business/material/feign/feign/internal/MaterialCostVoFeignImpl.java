package com.biz.crm.mdm.business.material.feign.feign.internal;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.material.feign.feign.MaterialCostVoFeign;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialCostQueryVo;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialCostVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Component
public class MaterialCostVoFeignImpl implements FallbackFactory<MaterialCostVoFeign> {
    @Override
    public MaterialCostVoFeign create(Throwable cause) {
        return new MaterialCostVoFeign() {

            @Override
            public Result<MaterialCostVo> findDetailByCodeAndCompanyCodeAndYear(String code, String companyCode, String yearStr) {
                throw new UnsupportedOperationException("进入熔断！");
            }

            @Override
            public Result<List<MaterialCostVo>> findDetailByCondition(MaterialCostQueryVo queryVo) {
                throw new UnsupportedOperationException("进入熔断！");
            }

            @Override
            public Result<List<MaterialCostVo>> findListByCodesAndCompanyCodesAndYear(MaterialCostQueryVo queryVo) {
                throw new UnsupportedOperationException("进入熔断！");
            }
        };
    }
}
