package com.biz.crm.mdm.business.material.local.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.material.local.entity.MaterialEntity;
import com.biz.crm.mdm.business.material.local.mapper.MaterialMapper;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialDto;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialPageDto;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialSearchDto;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 物料数据库操作层实现
 *
 * <AUTHOR>
 * @date 2021-09-27 14:44:10
 */
@Component
public class MaterialRepository extends ServiceImpl<MaterialMapper, MaterialEntity> {


    /**
     * 查询所有激活中物料数据
     *
     * @return 查询结果
     */
    public List<MaterialEntity> findAll() {
        return this.lambdaQuery()
                .eq(MaterialEntity::getTenantCode, TenantUtils.getTenantCode())
                .eq(MaterialEntity::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .list();
    }

    /**
     * 分页条件查询
     *
     * @param pageable 分页参数
     * @param dto      业务参数
     * @return Page
     */
    public Page<MaterialVo> findByConditions(Pageable pageable, @Param("dto") MaterialDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
        dto = Optional.ofNullable(dto).orElse(new MaterialDto());
        dto.setTenantCode(TenantUtils.getTenantCode());
        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        Page<MaterialEntity> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.baseMapper.findByConditions(page, dto);
    }

    /**
     * 根据id集合获取详情集合
     *
     * @param ids
     * @return
     */
    public List<MaterialEntity> findByIds(@Param("ids") List<String> ids) {
        return this.lambdaQuery()
                .in(MaterialEntity::getId, ids)
                .eq(MaterialEntity::getTenantCode, TenantUtils.getTenantCode())
                .list();
    }

    /**
     * 根据物料编码获取详情
     *
     * @param materialCode 物料编码
     * @return
     */
    public MaterialEntity findDetailByMaterialCode(@Param("tenantCode") String tenantCode, @Param("materialCode") String materialCode) {
        LambdaQueryWrapper<MaterialEntity> wrapper = Wrappers.<MaterialEntity>lambdaQuery();
        wrapper.eq(MaterialEntity::getTenantCode, tenantCode);
        wrapper.eq(MaterialEntity::getMaterialCode, materialCode);
        return this.baseMapper.selectOne(wrapper);
    }

    /**
     * 根据物料编码集合获取详情
     *
     * @param materialCodes 物料编码集合
     * @return
     */
    public List<MaterialEntity> findDetailByMaterialCodes(@Param("tenantCode") String tenantCode, @Param("materialCodes") Collection<String> materialCodes) {
        LambdaQueryWrapper<MaterialEntity> wrapper = Wrappers.<MaterialEntity>lambdaQuery();
        wrapper.eq(MaterialEntity::getTenantCode, tenantCode);
        wrapper.in(MaterialEntity::getMaterialCode, materialCodes);
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 根据主键集合，修改 enable_status
     *
     * @param enable
     * @param ids
     */
    public void updateEnableStatusByIdIn(EnableStatusEnum enable, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        LambdaUpdateWrapper<MaterialEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MaterialEntity::getEnableStatus, enable.getCode())
                .eq(MaterialEntity::getTenantCode, TenantUtils.getTenantCode())
                .in(MaterialEntity::getId, ids);
        this.baseMapper.update(null, updateWrapper);
    }

    /**
     * 根据主键集合，修改 del_flag
     *
     * @param delFlag
     * @param ids
     */
    public void updateDelFlagByIdIn(DelFlagStatusEnum delFlag, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        LambdaUpdateWrapper<MaterialEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MaterialEntity::getDelFlag, delFlag.getCode())
                .eq(MaterialEntity::getTenantCode, TenantUtils.getTenantCode())
                .in(MaterialEntity::getId, ids);
        this.baseMapper.update(null, updateWrapper);
    }

    /**
     * 通过物料id查询物料信息
     *
     * @param id 物料id
     * @return 物料信息
     */
    public MaterialEntity findById(String id) {
        return this.baseMapper.findById(id, TenantUtils.getTenantCode());
    }

    /**
     * 查询物料分页信息
     *
     * @param pageable 分页信息
     * @param dto      参数dto
     * @return 物料分页信息
     */
    public Page<MaterialEntity> findByMaterialPageDto(Pageable pageable, MaterialPageDto dto) {
        dto.setTenantCode(TenantUtils.getTenantCode());
        Page<MaterialEntity> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        dto.setTenantCode(TenantUtils.getTenantCode());
        return this.baseMapper.findByMaterialPageDto(page, dto);
    }

    /**
     * 查询对应层级的物料编码集合
     *
     * @param productLevelCodeSet
     * @return
     */
    public Set<String> findCodeByProductLevelCodes(Set<String> productLevelCodeSet) {
        return this.baseMapper.findCodeByProductLevelCodes(
                productLevelCodeSet,
                EnableStatusEnum.ENABLE.getCode(),
                DelFlagStatusEnum.NORMAL.getCode(),
                TenantUtils.getTenantCode());
    }

    /**
     * 重构查询方法 通过id和租户编号查询
     *
     * @param id
     * @param tenantCode
     * @return
     */
    public MaterialEntity findByIdAndTenantCode(String id, String tenantCode) {
        return this.lambdaQuery()
                .eq(MaterialEntity::getTenantCode, tenantCode)
                .in(MaterialEntity::getId, id)
                .one();
    }

    /**
     * 重构修改方法
     *
     * @param entity
     * @param tenantCode
     */
    public void updateByIdAndTenantCode(MaterialEntity entity, String tenantCode) {
        LambdaUpdateWrapper<MaterialEntity> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(MaterialEntity::getTenantCode, tenantCode);
        lambdaUpdateWrapper.in(MaterialEntity::getId, entity.getId());
        this.baseMapper.update(entity, lambdaUpdateWrapper);
    }

    public List<MaterialEntity> findAllByCodes(List<String> codeList) {
        if (CollectionUtils.isEmpty(codeList)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(MaterialEntity::getTenantCode, TenantUtils.getTenantCode())
                .in(MaterialEntity::getMaterialCode, codeList)
                .list();
    }

    public void saveBatchXml(List<MaterialEntity> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return;
        }
        Lists.partition(entityList, CommonConstant.MAX_PAGE_SIZE).forEach(list -> {
            this.baseMapper.insertBatchSomeColumn(list);
        });

    }

    public void updateBatchXml(List<MaterialEntity> updateList) {
        if (CollectionUtil.isEmpty(updateList)) {
            return;
        }
        Lists.partition(updateList, CommonConstant.MAX_PAGE_SIZE).forEach(this::updateBatchById);
    }

    public List<MaterialEntity> findListByNameList(List<String> materialNames) {
        return this.lambdaQuery()
                .in(MaterialEntity::getMaterialName, materialNames)
                .eq(MaterialEntity::getTenantCode, TenantUtils.getTenantCode())
                .eq(MaterialEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }


    public List<String> findFinanceIndexByKeywords(String keywords) {
        return this.baseMapper.findFinanceIndexByKeywords(keywords);
    }

    public void importUpdateXml(List<MaterialDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        this.baseMapper.importUpdateXml(dtoList);
    }

    public List<MaterialVo> findBySearchDto(MaterialSearchDto dto) {
        return this.baseMapper.findBySearchDto(dto);
    }

    public List<String> findByMaterialGroupCode(String materialGroupCode) {
        if (StringUtils.isBlank(materialGroupCode)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findByMaterialGroupCode(TenantUtils.getTenantCode(), materialGroupCode);
    }

    public List<MaterialEntity> loadCacheMaterial() {
        return this.lambdaQuery()
                .eq(MaterialEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(MaterialEntity::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .eq(MaterialEntity::getTenantCode, TenantUtils.getTenantCode())
                .list();
    }
}
