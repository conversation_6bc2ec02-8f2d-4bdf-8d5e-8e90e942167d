package com.biz.crm.mdm.business.material.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 物流成本实体类
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/27 20:21
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "mdm_material_cost", indexes = {
        @Index(name = "mdm_material_cost_uq1", columnList = "material_code,company_code,year_str", unique = true),
        @Index(name = "mdm_material_cost_uq2", columnList = "only_key", unique = true),
        @Index(name = "mdm_material_cost_idx1", columnList = "material_code", unique = false),
        @Index(name = "mdm_material_cost_idx2", columnList = "company_code", unique = false),
        @Index(name = "mdm_material_cost_idx3", columnList = "year_str", unique = false),
})
@TableName(value = "mdm_material_cost")
@org.hibernate.annotations.Table(appliesTo = "mdm_material_cost", comment = "物流成本实体类")
public class MaterialCostEntity extends TenantFlagOpEntity {

    private static final long serialVersionUID = -6511166651862708547L;

    @ApiModelProperty("物料编码")
    @Column(name = "material_code", columnDefinition = "varchar(32) COMMENT '物料编码'")
    private String materialCode;

    @ApiModelProperty("物料名称")
    @Column(name = "material_name", columnDefinition = "varchar(128) COMMENT '物料名称'")
    private String materialName;

    @ApiModelProperty("公司代码")
    @Column(name = "company_code", columnDefinition = "varchar(32) COMMENT '公司代码'")
    private String companyCode;

    @ApiModelProperty("会计年度")
    @Column(name = "year_str", columnDefinition = "varchar(4) COMMENT '会计年度'")
    private String yearStr;

    @ApiModelProperty("物料大类")
    @Column(name = "material_large_class", columnDefinition = "varchar(32) COMMENT '物料大类'")
    private String materialLargeClass;

    @ApiModelProperty("物料细类")
    @Column(name = "material_fine_class", columnDefinition = "varchar(32) COMMENT '物料细类'")
    private String materialFineClass;

    @ApiModelProperty("MD5(公司代码+会计年度+物料编码)")
    @Column(name = "only_key", nullable = false, columnDefinition = "varchar(32) COMMENT 'MD5(公司代码+会计年度+物料编码)'")
    private String onlyKey;

    @ApiModelProperty("大货物流价格")
    @Column(name = "large_logistics_price", columnDefinition = "decimal(10,4) COMMENT '大货物流价格'")
    private BigDecimal largeLogisticsPrice;

    @ApiModelProperty("快递物流价格")
    @Column(name = "express_logistics_price", columnDefinition = "decimal(10,4) COMMENT '快递物流价格'")
    private BigDecimal expressLogisticsPrice;

    @ApiModelProperty("冷链物流价格")
    @Column(name = "cold_logistics_price", columnDefinition = "decimal(10,4) COMMENT '冷链物流价格'")
    private BigDecimal coldLogisticsPrice;

    @ApiModelProperty("工厂直发价格")
    @Column(name = "factory_straight_price", columnDefinition = "decimal(10,4) COMMENT '工厂直发价格'")
    private BigDecimal factoryStraightPrice;

    @ApiModelProperty("数据同步时间")
    @Column(name = "sync_update_time", columnDefinition = "datetime DEFAULT CURRENT_TIMESTAMP COMMENT '数据同步时间'")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date syncUpdateTime;

    @ApiModelProperty("数据源[数据字典:mdm_data_source]")
    @Column(name = "data_source", columnDefinition = "VARCHAR(8) DEFAULT '' COMMENT '数据源[数据字典:mdm_data_source]'")
    private String dataSource;

}
