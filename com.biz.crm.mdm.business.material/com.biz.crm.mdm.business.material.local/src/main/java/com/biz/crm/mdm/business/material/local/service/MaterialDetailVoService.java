package com.biz.crm.mdm.business.material.local.service;

import com.biz.crm.mdm.business.material.sdk.vo.MaterialDetailVo;

import java.util.List;
import java.util.Set;

/**
 * 物料详情接口
 *
 * <AUTHOR>
 * @date 2021-09-27 14:44:10
 */
public interface MaterialDetailVoService {

    /**
     * 根据物料编码获取物料详情
     *
     * @param materialCode
     * @return java.util.List<com.biz.crm.mdm.business.material.sdk.vo.MaterialDetailVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/27 19:30
     */
    List<MaterialDetailVo> findDetailByMaterialCode(String materialCode);

    /**
     * 根据物料编码集合获取物料详情
     *
     * @param materialCodes
     * @return java.util.List<com.biz.crm.mdm.business.material.sdk.vo.MaterialDetailVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/27 19:30
     */
    List<MaterialDetailVo> findDetailByMaterialCodes(Set<String> materialCodes);
}

