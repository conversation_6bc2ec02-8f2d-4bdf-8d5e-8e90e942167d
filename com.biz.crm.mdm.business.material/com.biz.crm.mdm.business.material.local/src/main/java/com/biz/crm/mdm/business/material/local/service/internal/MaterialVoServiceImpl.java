package com.biz.crm.mdm.business.material.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.local.entity.UuidEntity;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.material.local.entity.MaterialDetailEntity;
import com.biz.crm.mdm.business.material.local.entity.MaterialEntity;
import com.biz.crm.mdm.business.material.local.entity.MaterialMediaEntity;
import com.biz.crm.mdm.business.material.local.repository.MaterialDetailRepository;
import com.biz.crm.mdm.business.material.local.repository.MaterialMediaRepository;
import com.biz.crm.mdm.business.material.local.repository.MaterialRepository;
import com.biz.crm.mdm.business.material.local.service.MaterialDetailVoService;
import com.biz.crm.mdm.business.material.local.service.MaterialMediaService;
import com.biz.crm.mdm.business.material.sdk.constant.MaterialConstant;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialDto;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialPageDto;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialSearchDto;
import com.biz.crm.mdm.business.material.sdk.event.MaterialEventListener;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialDetailVo;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialEventVo;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialMediaVo;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.material.unit.dto.MaterialUnitTypeDto;
import com.biz.crm.mdm.business.material.unit.service.MaterialUnitTypeVoService;
import com.biz.crm.mdm.business.material.unit.vo.MaterialUnitTypeVo;
import com.biz.crm.mdm.business.price.sdk.service.PriceMilkCostVoService;
import com.biz.crm.mdm.business.price.sdk.vo.PriceMilkCostVo;
import com.biz.crm.mdm.business.product.level.sdk.service.ProductLevelVoSdkService;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductLevelVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物料接口实现
 *
 * <AUTHOR>
 * @date 2021-09-27 14:44:10
 */
@Service
public class MaterialVoServiceImpl implements MaterialVoService {
    @Autowired(required = false)
    private MaterialRepository materialRepository;
    @Autowired(required = false)
    private GenerateCodeService generateCodeService;
    @Autowired(required = false)
    private ProductLevelVoSdkService productLevelVoSdkService;
    @Autowired(required = false)
    private MaterialUnitTypeVoService materialUnitTypeVoService;
    @Autowired(required = false)
    private DictDataVoService dictDataVoService;
    @Autowired(required = false)
    @Qualifier("nebulaToolkitService")
    private NebulaToolkitService nebulaToolkitService;
    @Autowired(required = false)
    private MaterialMediaService materialMediaService;
    @Autowired(required = false)
    private MaterialMediaRepository materialMediaRepository;
    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;
    @Autowired(required = false)
    private MaterialDetailVoService materialDetailVoService;
    @Autowired(required = false)
    private MaterialDetailRepository materialDetailRepository;
    @Autowired(required = false)
    private PriceMilkCostVoService priceMilkCostVoService;

    /**
     * 物料事件监听器
     */
    @Autowired(required = false)
    @Lazy
    private List<MaterialEventListener> materialEventListeners;

    private final static ThreadLocal<Map<String, String>> sapFactoryThreadLocal = new ThreadLocal<>();

    private String getFactoryCode(String companyCode) {
        Map<String, String> dictMap = sapFactoryThreadLocal.get();
        if (ObjectUtils.isEmpty(dictMap)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.MDM_SAP_FACTORY_TYPE);
            dictMap = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
        }
        return dictMap.get(companyCode);
    }

    @Override
    public List<MaterialVo> findAll() {
        List<MaterialEntity> entityList = materialRepository.findAll();
        return (List<MaterialVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entityList, MaterialEntity.class,
                MaterialVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public Page<MaterialVo> findByConditions(Pageable pageable, MaterialDto dto) {
        dto = Optional.ofNullable(dto).orElse(new MaterialDto());
        // 根据产品层级编码获取产品层级子级编码集合做物料范围过滤
        if (StringUtils.isNotBlank(dto.getProductLevelCode())) {
            List<String> productLevelCodes =
                    this.productLevelVoSdkService.findCurAndChildrenCodesByCodes(Sets.newHashSet(dto.getProductLevelCode()));
            dto.setProductLevelCodes(productLevelCodes);
        }
        if (ObjectUtils.isNotEmpty(dto.getCompanyCode())) {
            dto.setFactoryCode(getFactoryCode(dto.getCompanyCode()));
        }
        this.dealDto(dto);
        Page<MaterialVo> materialVoPage = this.materialRepository.findByConditions(pageable, dto);
        if (materialVoPage.getCurrent() == 0) {
            return materialVoPage;
        }
        // 返回值需要获取产品层级信息
        List<String> productLevelCodes = materialVoPage.getRecords().stream().map(MaterialVo::getProductLevelCode).distinct().collect(Collectors.toList());
        List<ProductLevelVo> productLevelVos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(productLevelCodes)) {
            productLevelVos = this.productLevelVoSdkService.findListByCodes(productLevelCodes);
        }
        // 获取物料图片
        List<String> materialIds = materialVoPage.getRecords().stream().map(MaterialVo::getId).collect(Collectors.toList());
        List<MaterialMediaEntity> mediaEntities = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(materialIds)) {
            mediaEntities = this.materialMediaRepository.findByMaterialIds(materialIds);
        }
        Map<String, List<MaterialMediaVo>> mediaMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(mediaEntities)) {
            mediaMap = mediaEntities.stream().collect(Collectors.groupingBy(MaterialMediaEntity::getMaterialId, Collectors.mapping(o ->
                    this.nebulaToolkitService.copyObjectByWhiteList(o, MaterialMediaVo.class, HashSet.class, ArrayList.class), Collectors.toList())));
        }
        // 按产品编码作为key转成产品层级map
        Map<String, ProductLevelVo> productLevelMap = productLevelVos.stream().collect(Collectors.toMap(ProductLevelVo::getProductLevelCode, Function.identity()));
        Map<String, String> materialType = this.dictDataVoService.findMapByDictTypeCode(MaterialConstant.MATERIAL_TYPE);
        Map<String, List<MaterialMediaVo>> finalMediaMap = mediaMap;
        materialVoPage.getRecords().forEach(vo -> {
            // 物料类型名称
            if (MapUtils.isNotEmpty(materialType) && StringUtils.isNotBlank(vo.getMaterialType()) && materialType.containsKey(vo.getMaterialType())) {
                vo.setMaterialTypeName(materialType.get(vo.getMaterialType()));
            }
            // 解析销售公司
            this.analysisSaleCompanyName(vo);
            // 解析物料文件信息
            vo.setPicList(finalMediaMap.get(vo.getId()));
            // 获取产品层级名称
            if (StringUtils.isNotBlank(vo.getProductLevelCode()) && productLevelMap.containsKey(vo.getProductLevelCode())) {
                vo.setProductLevelName(productLevelMap.get(vo.getProductLevelCode()).getProductLevelName());
            }
        });
        return materialVoPage;
    }

    private void dealDto(MaterialDto dto) {
        if (Objects.isNull(dto)) {
            return;
        }

        String productCode = dto.getMaterialCode();
        if (StringUtils.isNotBlank(productCode) && productCode.contains(",")) {
            dto.setMaterialCodes(Lists.newArrayList(productCode.split(",")));
            dto.setMaterialCode(null);
        }
        String productName = dto.getMaterialName();
        if (StringUtils.isNotBlank(productName) && productName.contains(",")) {
            dto.setMaterialNames(Lists.newArrayList(productName.split(",")));
            dto.setMaterialName(null);
        }
    }

    /**
     * 根据销售公司编码解析成集合
     * 注意saleCompany的值格式为“a,b,c”
     */
    private void analysisSaleCompanyName(MaterialVo vo) {
        if (StringUtils.isBlank(vo.getSaleCompany())) {
            return;
        }
        //解析封装成集合
        vo.setSaleCompanyList(Lists.newArrayList(vo.getSaleCompany().split(",")));
    }

    /**
     * 根据产品层级编码获取产品层级名称
     */
    private void analysisProductLevel(MaterialVo vo) {
        if (StringUtils.isBlank(vo.getProductLevelCode())) {
            return;
        }
        ProductLevelVo productLevelVo = this.productLevelVoSdkService.findDetailsByCode(vo.getProductLevelCode());
        if (productLevelVo == null) {
            return;
        }
        vo.setProductLevelName(productLevelVo.getProductLevelName());
    }

    /**
     * 解析物料文件信息
     */
    private void analysisMaterialMedia(MaterialVo vo) {
        if (StringUtils.isBlank(vo.getId())) {
            return;
        }
        List<MaterialMediaEntity> materialMediaEntities = this.materialMediaRepository.findByMaterialIds(Collections.singletonList(vo.getId()));
        if (CollectionUtils.isNotEmpty(materialMediaEntities)) {
            List<MaterialMediaVo> materialMediaVoList = Lists.newArrayList(this.nebulaToolkitService.copyCollectionByWhiteList(materialMediaEntities, MaterialMediaEntity.class, MaterialMediaVo.class, HashSet.class, ArrayList.class));
            vo.setPicList(materialMediaVoList);
        }
    }

    @Override
    public MaterialVo findDetailById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        MaterialEntity entity = this.materialRepository.findById(id);
        if (entity == null) {
            return null;
        }
        MaterialVo materialVo = this.nebulaToolkitService.copyObjectByBlankList(entity, MaterialVo.class, HashSet.class, ArrayList.class);
        materialVo.setDetailList(materialDetailVoService.findDetailByMaterialCode(entity.getMaterialCode()));
        // 解析销售公司
        this.analysisSaleCompanyName(materialVo);
        // 解析产品层级
        this.analysisProductLevel(materialVo);
        // 解析物料文件信息
        this.analysisMaterialMedia(materialVo);
        //物料单位类型
        MaterialUnitTypeDto materialUnitTypeDto = new MaterialUnitTypeDto();
        materialUnitTypeDto.setUnitTypeCodes(Collections.singletonList(materialVo.getUnitTypeCode()));
        List<MaterialUnitTypeVo> materialUnitTypeVos = materialUnitTypeVoService.findMaterialUnitTypeByConditions(materialUnitTypeDto);
        if (CollectionUtils.isEmpty(materialUnitTypeVos)) {
            return materialVo;
        }
        materialVo.setMaterialUnitTypeVo(materialUnitTypeVos.get(0));
        return materialVo;
    }

    @Override
    public MaterialVo findDetailByMaterialCode(String materialCode) {
        if (StringUtils.isBlank(materialCode)) {
            return null;
        }
        MaterialEntity entity = this.materialRepository.findDetailByMaterialCode(TenantUtils.getTenantCode(), materialCode);
        if (entity == null) {
            return null;
        }
        MaterialVo materialVo = this.nebulaToolkitService.copyObjectByWhiteList(entity, MaterialVo.class, HashSet.class, ArrayList.class);
        materialVo.setDetailList(materialDetailVoService.findDetailByMaterialCode(entity.getMaterialCode()));
        // 解析销售公司
        this.analysisSaleCompanyName(materialVo);
        // 解析产品层级
        this.analysisProductLevel(materialVo);
        // 解析物料文件信息
        this.analysisMaterialMedia(materialVo);
        //物料单位类型
        MaterialUnitTypeDto materialUnitTypeDto = new MaterialUnitTypeDto();
        materialUnitTypeDto.setUnitTypeCodes(Collections.singletonList(materialVo.getUnitTypeCode()));
        List<MaterialUnitTypeVo> materialUnitTypeVos = materialUnitTypeVoService.findMaterialUnitTypeByConditions(materialUnitTypeDto);
        if (CollectionUtils.isEmpty(materialUnitTypeVos)) {
            return materialVo;
        }
        materialVo.setMaterialUnitTypeVo(materialUnitTypeVos.get(0));
        return materialVo;
    }

    @Override
    public List<MaterialVo> findDetailByMaterialCodes(Set<String> materialCodes) {
        if (CollectionUtils.isEmpty(materialCodes)) {
            return Lists.newArrayList();
        }
        List<MaterialEntity> entities = this.materialRepository.findDetailByMaterialCodes(TenantUtils.getTenantCode(), materialCodes);
        if (CollectionUtils.isEmpty(entities)) {
            return Lists.newArrayList();
        }
        Map<String, String> materialType = this.dictDataVoService.findMapByDictTypeCode(MaterialConstant.MATERIAL_TYPE);
        // k-productLevelCode,v-productLevelName
        Map<String, String> mapLevel = Maps.newHashMap();
        Set<String> productLevelCodeSet =
                entities.stream()
                        .filter(a -> StringUtils.isNotBlank(a.getProductLevelCode()))
                        .map(MaterialEntity::getProductLevelCode)
                        .collect(Collectors.toSet());
        Map<String, List<ProductLevelVo>> productLevelMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productLevelCodeSet)) {
            List<ProductLevelVo> levelVoList =
                    this.productLevelVoSdkService.findListByCodes(Lists.newArrayList(productLevelCodeSet));
            if (CollectionUtils.isNotEmpty(levelVoList)) {
                mapLevel =
                        levelVoList.stream()
                                .filter(
                                        a -> StringUtils.isNoneBlank(a.getProductLevelCode(), a.getProductLevelName()))
                                .collect(
                                        Collectors.toMap(
                                                ProductLevelVo::getProductLevelCode,
                                                ProductLevelVo::getProductLevelName,
                                                (a, b) -> a));
                //构造产品层级map
                productLevelMap = levelVoList.stream().collect(Collectors.groupingBy(ProductLevelVo::getProductLevelCode));
            }
        }
        List<MaterialVo> voList = (List<MaterialVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entities, MaterialEntity.class, MaterialVo.class, HashSet.class, ArrayList.class);
        // 完善物料单位信息
        List<String> unitTypeCodes = voList.stream().filter(o -> StringUtils.isNotBlank(o.getUnitTypeCode())).map(MaterialVo::getUnitTypeCode).distinct().collect(Collectors.toList());
        MaterialUnitTypeDto materialUnitTypeDto = new MaterialUnitTypeDto();
        materialUnitTypeDto.setUnitTypeCodes(unitTypeCodes);
        List<MaterialUnitTypeVo> materialUnitTypeVos = materialUnitTypeVoService.findMaterialUnitTypeByConditions(materialUnitTypeDto);
        List<MaterialDetailVo> detailVoList = materialDetailVoService.findDetailByMaterialCodes(materialCodes);
        Map<String, MaterialUnitTypeVo> materialUnitTypeVoMap = Maps.newHashMap();
        Map<String, List<MaterialDetailVo>> detailMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(materialUnitTypeVos)) {
            materialUnitTypeVoMap.putAll(materialUnitTypeVos.stream()
                    .collect(Collectors.toMap(MaterialUnitTypeVo::getUnitTypeCode, v -> v, (n, o) -> n)));
        }
        if (CollectionUtils.isNotEmpty(detailVoList)) {
            detailMap.putAll(detailVoList.stream().collect(Collectors.groupingBy(MaterialDetailVo::getMaterialCode)));
        }
        // unitTypeCode 能确保唯一
        for (MaterialVo o : voList) {
            if (StringUtils.isNotBlank(o.getUnitTypeCode())
                    && materialUnitTypeVoMap.containsKey(o.getUnitTypeCode())) {
                o.setMaterialUnitTypeVo(materialUnitTypeVoMap.get(o.getUnitTypeCode()));
            }
            o.setProductLevelName(mapLevel.get(o.getProductLevelCode()));
            o.setMaterialTypeName(materialType.get(o.getMaterialType()));
            //补充产品层级的数据
            o.setProductLevels(productLevelMap.get(o.getProductLevelCode()));
            o.setDetailList(detailMap.getOrDefault(o.getMaterialCode(), Lists.newArrayList()));
        }
        return voList;
    }

    @Override
    public List<MaterialVo> findByMaterialCodes(Set<String> materialCodes) {
        if (CollectionUtils.isEmpty(materialCodes)) {
            return Lists.newArrayList();
        }
        List<MaterialEntity> entities = this.materialRepository.findDetailByMaterialCodes(TenantUtils.getTenantCode(), materialCodes);
        if (CollectionUtils.isEmpty(entities)) {
            return Lists.newArrayList();
        }

        return (List<MaterialVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entities, MaterialEntity.class,
                MaterialVo.class, HashSet.class, ArrayList.class);
    }

    /**
     * 把销售公司编码集合转成英文逗号拼接的字符串保存
     * 注意拼接后saleCompany值格式为“a,b,c”
     */
    private void analysisSaleCompanyCode(MaterialDto dto) {
        if (CollectionUtils.isEmpty(dto.getSaleCompanyList())) {
            return;
        }
        // 销售公司（前端页面多选）
        String connector = ",";
        dto.setSaleCompany(String.join(connector, dto.getSaleCompanyList()));
    }

    @Override
    @Transactional
    public void save(MaterialDto materialDto) {
        Validate.notNull(materialDto, "进行当前操作时，信息对象必须传入!!");
        Validate.notEmpty(materialDto.getMaterialName(), "添加信息时，物料名称不能为空！");
        Validate.notEmpty(materialDto.getUnitTypeCode(), "添加信息时，物料单位类型编码不能为空！");
        // TODO 成本价格式不正确：请输入正数，保留两位小数
        if (StringUtils.isBlank(materialDto.getMaterialCode())) {
            // redis生物料编码code
            List<String> codeList = this.generateCodeService.generateCode(MaterialConstant.MATERIAL_CODE, 1);
            Validate.isTrue(CollectionUtils.isNotEmpty(codeList), "添加信息时，生成物料编码失败！");
            materialDto.setMaterialCode(codeList.get(0));
            materialDto.setTenantCode(TenantUtils.getTenantCode());
        } else {
            MaterialEntity dbEntity = this.materialRepository.findDetailByMaterialCode(TenantUtils.getTenantCode(), materialDto.getMaterialCode());
            Validate.isTrue(Objects.isNull(dbEntity), "添加信息时，物料编码已存在！");
        }
        // 把销售公司编码集合转成英文逗号拼接的字符串保存
        this.analysisSaleCompanyCode(materialDto);
        MaterialEntity entity = this.nebulaToolkitService.copyObjectByWhiteList(materialDto, MaterialEntity.class, HashSet.class, ArrayList.class);
        entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        //新增租户编号
        entity.setTenantCode(TenantUtils.getTenantCode());
        this.materialRepository.save(entity);
        //更新物料图片信息
        this.materialMediaService.update(materialDto.getPicList(), entity.getId());
        //创建日志
        MaterialVo materialVo = this.nebulaToolkitService.copyObjectByWhiteList(entity, MaterialVo.class, HashSet.class, ArrayList.class);
        MaterialEventVo materialEventVo = new MaterialEventVo();
        materialEventVo.setNewVo(materialVo);
        materialEventVo.setOldVo(null);
        this.materialEventListeners.forEach(event -> event.onCreate(materialEventVo));
    }

    @Override
    @Transactional
    public void update(MaterialDto materialDto) {
        Validate.notNull(materialDto, "进行当前操作时，信息对象必须传入!!");
        Validate.notEmpty(materialDto.getId(), "修改信息时，id不能为空！");
        Validate.notEmpty(materialDto.getMaterialName(), "修改信息时，物料名称不能为空！");
        Validate.notEmpty(materialDto.getUnitTypeCode(), "添加信息时，物料单位类型编码不能为空！");
        final MaterialEntity oldEntity = this.materialRepository.findByIdAndTenantCode(materialDto.getId(), TenantUtils.getTenantCode());   //重构查询方法
        Validate.notNull(oldEntity, "不存在或已删除");
        // 把销售公司编码集合转成英文逗号拼接的字符串保存
        this.analysisSaleCompanyCode(materialDto);
        MaterialEntity entity = this.nebulaToolkitService.copyObjectByWhiteList(materialDto, MaterialEntity.class, HashSet.class, ArrayList.class);
        entity.setTenantCode(TenantUtils.getTenantCode());
        //重构修改的方法
        this.materialRepository.updateByIdAndTenantCode(entity, TenantUtils.getTenantCode());
        //更新物料图片信息
        this.materialMediaService.update(materialDto.getPicList(), entity.getId());
        // 更新物料时通知商品及商品的所有关联主数据信息做相应逻辑处理
        if (CollectionUtils.isNotEmpty(materialEventListeners)) {
            MaterialVo oldVo = this.nebulaToolkitService.copyObjectByWhiteList(oldEntity, MaterialVo.class, HashSet.class, ArrayList.class);
            MaterialVo materialVo = this.nebulaToolkitService.copyObjectByWhiteList(entity, MaterialVo.class, HashSet.class, ArrayList.class);
            MaterialEventVo materialEventVo = new MaterialEventVo();
            materialEventVo.setNewVo(materialVo);
            materialEventVo.setOldVo(oldVo);
            this.materialEventListeners.forEach(event -> event.onChange(materialEventVo));
        }
    }

    @Override
    @Transactional
    public void deleteBatch(List<String> ids) {
        Validate.isTrue(CollectionUtils.isNotEmpty(ids), "请选中要操作的数据");
        List<MaterialEntity> entities = this.materialRepository.findByIds(ids);
        Validate.isTrue(CollectionUtils.isNotEmpty(entities), "不存在或已删除！");
        this.materialRepository.updateDelFlagByIdIn(DelFlagStatusEnum.DELETE, ids);
        // 删除物料时通知商品及商品的所有关联主数据信息做相应逻辑处理
        if (CollectionUtils.isNotEmpty(materialEventListeners)) {
            List<MaterialVo> voList = (List<MaterialVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entities, MaterialEntity.class, MaterialVo.class, HashSet.class, ArrayList.class);
            MaterialEventVo materialEventVo = new MaterialEventVo();
            materialEventVo.setMaterialVos(voList);
            this.materialEventListeners.forEach(event -> event.onDelete(materialEventVo));
        }
    }

    @Override
    @Transactional
    public void enableBatch(List<String> ids) {
        Validate.isTrue(CollectionUtils.isNotEmpty(ids), "请选中要操作的数据");
        List<MaterialEntity> entities = this.materialRepository.findByIds(ids);
        Validate.isTrue(CollectionUtils.isNotEmpty(entities), "不存在或已删除！");
        this.materialRepository.updateEnableStatusByIdIn(EnableStatusEnum.ENABLE, ids);
        // 启用物料时通知商品及商品的所有关联主数据信息做相应逻辑处理
        List<MaterialVo> voList = (List<MaterialVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entities, MaterialEntity.class, MaterialVo.class, HashSet.class, ArrayList.class);
        MaterialEventVo materialEventVo = new MaterialEventVo();
        materialEventVo.setMaterialVos(voList);
        SerializableBiConsumer<MaterialEventListener, MaterialEventVo> sf = MaterialEventListener::onEnable;
        nebulaNetEventClient.publish(materialEventVo, MaterialEventListener.class, sf);
    }

    @Override
    @Transactional
    public void disableBatch(List<String> ids) {
        Validate.isTrue(CollectionUtils.isNotEmpty(ids), "请选中要操作的数据");
        List<MaterialEntity> entities = this.materialRepository.findByIds(ids);
        Validate.isTrue(CollectionUtils.isNotEmpty(entities), "不存在或已删除！");
        this.materialRepository.updateEnableStatusByIdIn(EnableStatusEnum.DISABLE, ids);
        // 禁用物料时通知商品及商品的所有关联主数据信息做相应逻辑处理
        if (CollectionUtils.isNotEmpty(materialEventListeners)) {
            List<MaterialVo> voList = (List<MaterialVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entities, MaterialEntity.class, MaterialVo.class, HashSet.class, ArrayList.class);
            MaterialEventVo materialEventVo = new MaterialEventVo();
            materialEventVo.setMaterialVos(voList);
            SerializableBiConsumer<MaterialEventListener, MaterialEventVo> disable = MaterialEventListener::onDisable;
            nebulaNetEventClient.publish(materialEventVo, MaterialEventListener.class, disable);
        }
    }

    @Override
    public Page<MaterialVo> findByMaterialPageDto(Pageable pageable, MaterialPageDto dto) {
        pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
        dto = ObjectUtils.defaultIfNull(dto, new MaterialPageDto());
        dto.setTenantCode(TenantUtils.getTenantCode());
        List<String> productLevelCodes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dto.getProductLevelCodes())) {
            productLevelCodes = dto.getProductLevelCodes();
        }
        //封装商品层级类类型对应的产品层级编码
        if (StringUtils.isNotBlank(dto.getProductLevelType())) {
            List<ProductLevelVo> levelVoList = this.productLevelVoSdkService.findByProductLevelType(dto.getProductLevelType());
            if (CollectionUtils.isNotEmpty(levelVoList)) {
                productLevelCodes.addAll(levelVoList.stream().map(ProductLevelVo::getProductLevelCode).collect(Collectors.toList()));
            }
        }
        dto.setProductLevelCodes(productLevelCodes);
        Page<MaterialEntity> entityPage = this.materialRepository.findByMaterialPageDto(pageable, dto);
        Page<MaterialVo> pageResult = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        List<MaterialEntity> entities = entityPage.getRecords();
        if (CollectionUtils.isEmpty(entityPage.getRecords())) {
            return pageResult;
        }
        pageResult.setRecords(this.covertEntityToVo(entities));
        pageResult.setTotal(entityPage.getTotal());
        pageResult.setCurrent(entityPage.getCurrent());
        pageResult.setSize(entityPage.getSize());
        return pageResult;
    }

    @Override
    public Set<String> findCodeByProductLevelCodes(Set<String> productLevelCodeSet) {
        if (CollectionUtils.isEmpty(productLevelCodeSet)) {
            return Sets.newHashSet();
        }
        return this.materialRepository.findCodeByProductLevelCodes(productLevelCodeSet);
    }


    /**
     * 通过物料名称查询物料信息
     *
     * @param materialNames
     * @return
     */
    @Override
    public List<MaterialVo> findListByNameList(List<String> materialNames) {
        List<MaterialEntity> entityList = Lists.newArrayList();
        List<List<String>> partitionList = Lists.partition(materialNames, 800);
        for (List<String> strings : partitionList) {
            List<MaterialEntity> list = materialRepository.findListByNameList(strings);
            if (CollectionUtils.isNotEmpty(list)) {
                entityList.addAll(list);
            }
        }
        return (List<MaterialVo>) nebulaToolkitService.copyCollectionByWhiteList(entityList, MaterialEntity.class, MaterialVo.class, HashSet.class, ArrayList.class);
    }


    /**
     * 查询财务经营指标
     *
     * @param keywords
     * @return
     */
    @Override
    public List<String> findFinanceIndexByKeywords(String keywords) {
        return materialRepository.findFinanceIndexByKeywords(keywords);
    }

    @Override
    public List<MaterialVo> findBySearchDto(MaterialSearchDto dto) {
        dto = Optional.ofNullable(dto).orElse(new MaterialSearchDto());
        dto.setTenantCode(TenantUtils.getTenantCode());
        Set<String> factoryCodeSet = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(dto.getCompanyCodeSet())) {
            for (String s : dto.getCompanyCodeSet()) {
                factoryCodeSet.add(getFactoryCode(s));
            }
        }
        dto.setFactoryCodeSet(factoryCodeSet);
        return this.materialRepository.findBySearchDto(dto);
    }

    @Override
    public List<String> findByMaterialGroupCode(String materialGroupCode) {
        if (StringUtils.isBlank(materialGroupCode)) {
            return Lists.newArrayList();
        }
        return this.materialRepository.findByMaterialGroupCode(materialGroupCode);
    }

    /**
     * 转换物料实体为vo
     *
     * @param entities 物料实体
     * @return 物料vo
     */
    private List<MaterialVo> covertEntityToVo(List<MaterialEntity> entities) {
        List<String> levelCodes = Lists.newArrayList();
        List<String> materialIds = Lists.newArrayList();
        Set<String> materialCodes = Sets.newHashSet();
        entities.forEach(materialEntity -> {
            if (StringUtils.isNotBlank(materialEntity.getProductLevelCode())) {
                levelCodes.add(materialEntity.getProductLevelCode());
            }
            materialIds.add(materialEntity.getId());
            materialCodes.add(materialEntity.getMaterialCode());
        });
        List<MaterialMediaEntity> mediaEntities = this.materialMediaRepository.findByMaterialIds(materialIds);
        Map<String, List<MaterialMediaVo>> mediaMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(mediaEntities)) {
            mediaMap = mediaEntities.stream().collect(Collectors.groupingBy(MaterialMediaEntity::getMaterialId, Collectors.mapping(o ->
                    this.nebulaToolkitService.copyObjectByWhiteList(o, MaterialMediaVo.class, HashSet.class, ArrayList.class), Collectors.toList())));
        }
        List<MaterialDetailVo> detailVoList = materialDetailVoService.findDetailByMaterialCodes(materialCodes);
        Map<String, List<ProductLevelVo>> levelMap = this.productLevelVoSdkService.findCurAndParentByCodes(levelCodes);
        Map<String, List<MaterialDetailVo>> detailMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(detailVoList)) {
            detailMap.putAll(detailVoList.stream().collect(Collectors.groupingBy(MaterialDetailVo::getMaterialCode)));
        }
        List<MaterialVo> list = Lists.newArrayList();
        for (MaterialEntity materialEntity : entities) {
            MaterialVo vo = this.nebulaToolkitService.copyObjectByWhiteList(materialEntity, MaterialVo.class, HashSet.class, ArrayList.class);
            // 解析销售公司
            this.analysisSaleCompanyName(vo);
            // 解析产品层级信息
            List<ProductLevelVo> productLevelVos = levelMap.get(materialEntity.getProductLevelCode());
            if (!CollectionUtils.isEmpty(productLevelVos)) {
                vo.setProductLevels(productLevelVos);
                productLevelVos.forEach(productLevelVo -> {
                    if (productLevelVo.getProductLevelCode().equals(materialEntity.getProductLevelCode())) {
                        vo.setProductLevelName(productLevelVo.getProductLevelName());
                    }
                });
            }
            //解析物料文件信息
            vo.setPicList(mediaMap.get(materialEntity.getId()));
            vo.setDetailList(detailMap.getOrDefault(materialEntity.getMaterialCode(), Lists.newArrayList()));
            list.add(vo);
        }
        return list;
    }

    /**
     * 保存或更新数据
     *
     * @param dtoList
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/27 22:59
     */
    @Override
    public void saveOrUpdateSapDataDto(List<MaterialDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Collection<MaterialEntity> entityList = this.nebulaToolkitService.copyCollectionByWhiteList(dtoList,
                MaterialDto.class, MaterialEntity.class, HashSet.class, ArrayList.class, "detailList");
        if (CollectionUtil.isEmpty(entityList)) {
            return;
        }
        Map<String, MaterialEntity> dataMap = entityList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getMaterialCode()))
                .collect(Collectors.toMap(MaterialEntity::getMaterialCode, v -> v, (n, o) -> n));
        this.saveOrUpdateMap(dataMap);

    }

    @Override
    public List<PriceMilkCostVo> findMilkCostPriceByCodes(Set<String> codeList) {
        return this.priceMilkCostVoService.findByMaterialCodes(codeList);
    }

    @Resource
    private RedisService redisService;

    @Override
    public List<MaterialVo> loadCacheMaterial() {
        if (redisService.hasKey(MaterialConstant.MATERIAL_LOAD_REDIS_KEY)) {
            return (List<MaterialVo>) redisService.get(MaterialConstant.MATERIAL_LOAD_REDIS_KEY);
        }
        List<MaterialEntity> materialEntities = materialRepository.loadCacheMaterial();
        List<MaterialVo> materialVoList = (List<MaterialVo>) nebulaToolkitService.copyCollectionByBlankList(materialEntities, MaterialEntity.class, MaterialVo.class, HashSet.class, ArrayList.class);
        redisService.set(MaterialConstant.MATERIAL_LOAD_REDIS_KEY, materialVoList, 18000);
        return materialVoList;
    }

    /**
     * 分离新增和更新的数据,并保存数据
     *
     * @param dataMap
     */
    private void saveOrUpdateMap(Map<String, MaterialEntity> dataMap) {
        if (CollectionUtil.isEmpty(dataMap)) {
            return;
        }
        List<MaterialEntity> oldList = materialRepository.findAllByCodes(Lists.newArrayList(dataMap.keySet()));
        if (Objects.isNull(oldList)) {
            oldList = Lists.newArrayList();
        }
        Map<String, MaterialEntity> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getMaterialCode()))
                .collect(Collectors.toMap(MaterialEntity::getMaterialCode, v -> v, (n, o) -> n));
        List<MaterialEntity> updateList = Lists.newArrayList();
        List<MaterialEntity> saveList = Lists.newArrayList();
        List<MaterialDetailEntity> detailList = Lists.newArrayList();
        dataMap.values().forEach(entity -> {
            if (CollectionUtil.isNotEmpty(entity.getDetailList())) {
                detailList.addAll(entity.getDetailList());
            }
            MaterialEntity oldEntity = oldMap.get(entity.getMaterialCode());
            if (Objects.nonNull(oldEntity)) {
                this.buildUpdateEntity(oldEntity, entity);
                updateList.add(oldEntity);
            } else {
                saveList.add(entity);
            }
        });
        List<MaterialDetailEntity> saveDetailList = Lists.newLinkedList(detailList.stream()
                .collect(Collectors.toMap(v -> v.getMaterialCode() + v.getFactoryTypeCode(), v -> v, (n, o) -> n)).values());
        this.saveOrUpdateList(saveList, updateList, saveDetailList);


    }

    /**
     * @param saveList   新增数据集合
     * @param updateList 更新数据集
     * @param detailList 明细集合
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/27 18:13
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NOT_SUPPORTED)
    public void saveOrUpdateList(List<MaterialEntity> saveList, List<MaterialEntity> updateList,
                                 List<MaterialDetailEntity> detailList) {
        if (CollectionUtil.isNotEmpty(saveList)) {
            materialRepository.saveBatchXml(saveList);
        }
        if (CollectionUtil.isNotEmpty(updateList)) {
            materialRepository.updateBatchXml(updateList);
        }

        if (CollectionUtil.isNotEmpty(detailList)) {
            List<MaterialDetailEntity> oldList = materialDetailRepository.findDetailByIds(detailList.stream()
                    .map(MaterialDetailEntity::getId)
                    .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList()));
            if (CollectionUtil.isEmpty(oldList)) {
                materialDetailRepository.saveBatchXml(detailList);
                return;
            }
            Map<String, String> oldMap = oldList.stream()
                    .filter(k -> StringUtil.isNotEmpty(k.getId()))
                    .collect(Collectors.toMap(MaterialDetailEntity::getId, UuidEntity::getId, (n, o) -> n));
            List<MaterialDetailEntity> saveDetailList = Lists.newArrayList();
            List<MaterialDetailEntity> updateDetailList = Lists.newArrayList();
            detailList.forEach(entity -> {
                if (oldMap.containsKey(entity.getId())) {
                    updateDetailList.add(entity);
                } else {
                    saveDetailList.add(entity);
                }
            });
            if (CollectionUtil.isNotEmpty(saveDetailList)) {
                materialDetailRepository.saveBatchXml(saveDetailList);
            }
            if (CollectionUtil.isNotEmpty(updateDetailList)) {
                materialDetailRepository.updateBatchXml(updateDetailList);
            }
        }

    }

    /**
     * 构建更新实体信息
     *
     * @param oldEntity
     * @param entity
     */
    private void buildUpdateEntity(MaterialEntity oldEntity, MaterialEntity entity) {
        oldEntity.setProductSmallClassCode(entity.getProductSmallClassCode());
        oldEntity.setProductSmallClassName(entity.getProductSmallClassName());
        oldEntity.setUnitTypeCode(entity.getUnitTypeCode());
        oldEntity.setMaterialName(entity.getMaterialName());
        oldEntity.setSimpleName(entity.getSimpleName());
        oldEntity.setZeroLevelCode(entity.getZeroLevelCode());
        oldEntity.setZeroLevelName(entity.getZeroLevelName());
        oldEntity.setOneLevelCode(entity.getOneLevelCode());
        oldEntity.setOneLevelName(entity.getOneLevelName());
        oldEntity.setTwoLevelCode(entity.getTwoLevelCode());
        oldEntity.setTwoLevelName(entity.getTwoLevelName());
        oldEntity.setThreeLevelCode(entity.getThreeLevelCode());
        oldEntity.setThreeLevelName(entity.getThreeLevelName());
        oldEntity.setFourLevelCode(entity.getFourLevelCode());
        oldEntity.setFourLevelName(entity.getFourLevelName());
        oldEntity.setConversionValue(entity.getConversionValue());
        oldEntity.setMaterialType(entity.getMaterialType());
        oldEntity.setStandardUnit(entity.getStandardUnit());
        oldEntity.setMaterialGroupCode(entity.getMaterialGroupCode());
        oldEntity.setMaterialGroupName(entity.getMaterialGroupName());
        oldEntity.setFinanceIndex(entity.getFinanceIndex());
        oldEntity.setBarCode(entity.getBarCode());
        oldEntity.setGrossWeight(entity.getGrossWeight());
        oldEntity.setNetWeight(entity.getNetWeight());
        oldEntity.setWeightUnit(entity.getWeightUnit());
        oldEntity.setCapacity(entity.getCapacity());
        oldEntity.setLongMetre(entity.getLongMetre());
        oldEntity.setWideMetre(entity.getWideMetre());
        oldEntity.setHighMetre(entity.getHighMetre());
        oldEntity.setTaxRate(entity.getTaxRate());
        oldEntity.setTaxRateStr(entity.getTaxRateStr());
        oldEntity.setProductPhaseCode(entity.getProductPhaseCode());
        oldEntity.setProductPhaseName(entity.getProductPhaseName());

        oldEntity.setDelFlag(entity.getDelFlag());
        oldEntity.setEnableStatus(entity.getEnableStatus());
        oldEntity.setSyncUpdateTime(entity.getSyncUpdateTime());
        oldEntity.setDataSource(entity.getDataSource());
        oldEntity.setModifyAccount(entity.getModifyAccount());
        oldEntity.setModifyName(entity.getModifyName());
        oldEntity.setModifyTime(entity.getModifyTime());
        if (Objects.isNull(oldEntity.getStartTime())) {
            oldEntity.setStartTime(entity.getStartTime());
        }
        oldEntity.setEndTime(entity.getEndTime());
    }

}
