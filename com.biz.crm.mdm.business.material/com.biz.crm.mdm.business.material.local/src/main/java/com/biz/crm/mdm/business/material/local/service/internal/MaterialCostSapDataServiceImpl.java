package com.biz.crm.mdm.business.material.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.auth.sdk.service.UrlApiService;
import com.biz.crm.business.common.auth.sdk.vo.UrlAddressVo;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.base.constant.ryytn.RyConstant;
import com.biz.crm.business.common.base.eunm.ExternalSystemEnum;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.base.util.BusinessHttpUtil;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.base.util.ryytn.RySignHeaderUtil;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.biz.crm.business.common.log.sdk.constant.ExternalLogGlobalConstants;
import com.biz.crm.business.common.log.sdk.dto.ExternalLogDetailDto;
import com.biz.crm.business.common.log.sdk.service.ExternalLogVoService;
import com.biz.crm.business.common.log.sdk.util.ExternalLogUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.material.local.entity.MaterialCostEntity;
import com.biz.crm.mdm.business.material.local.repository.MaterialCostRepository;
import com.biz.crm.mdm.business.material.local.service.MaterialCostSapDataService;
import com.biz.crm.mdm.business.material.sdk.constant.MaterialConstant;
import com.biz.crm.mdm.business.material.sdk.vo.SapMaterialCostVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.xml.bind.DatatypeConverter;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 物流成本接口实现
 *
 * <AUTHOR>
 * @date 2021-09-27 14:44:10
 */
@Service
@Slf4j
public class MaterialCostSapDataServiceImpl implements MaterialCostSapDataService {

    @Autowired(required = false)
    private UrlApiService urlApiService;

    @Autowired(required = false)
    private ExternalLogVoService externalLogVoService;

    @Autowired(required = false)
    private RedisLockService redisLockService;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private MaterialCostRepository materialCostRepository;


    /**
     * 拉取SAP物流成本
     *
     * @param isAll   是否全量   空|false 拉取今天、昨天、前天   true  拉取所有
     * @param yearStr
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/27 15:14
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NOT_SUPPORTED)
    public void pullMaterialCost(String isAll, String yearStr) {
        log.info("=====>   拉取物流成本 start    <=====");
        UrlAddressVo urlAddressVo = urlApiService.getUrlAddressByAccount(RyConstant.STD_SAP_ACCOUNT);
        String lockKey = MaterialConstant.PULL_MATERIAL_COST + DateUtil.dateStrNowYYYYMMDD();
        redisLockService.lock(lockKey, TimeUnit.MINUTES, 30);
        String url = urlAddressVo.getUrl();
        String interfaceAddress = String.format(RyConstant.SAP_INTERFACE_ADDRESS, urlAddressVo.getBusinessKey(), urlAddressVo.getEnvironment());
        List<String> updateTimeList = this.buildUpdateTimeList(isAll);
        Map<String, String> dictMap = dictDataVoService.findMapByDictTypeCode(DictConstant.MDM_SAP_MATERIAL_COST_COMPANY_CODE);
        try {
            Assert.notEmpty(dictMap, "数据字典[" + DictConstant.MDM_SAP_MATERIAL_COST_COMPANY_CODE + "]未配置公司代码!");
            AbstractCrmUserIdentity crmUserIdentity = loginUserService.getAbstractLoginUser();
            dictMap.forEach((companyCode, dictValue) -> {
                log.info("=====>   拉取物流成本 公司[{}] 财年[{}] start    <=====", companyCode, yearStr);
                String bodyJson = this.buildBodyJson(updateTimeList, companyCode, yearStr);
                log.error("=====>   拉取物流成本 修改时间[{}]    <=====", CollectionUtil.isNotEmpty(updateTimeList) ? updateTimeList : "全量");
                ExternalLogDetailDto logDetailDto = ExternalLogUtil.buildLogSaveInfo(bodyJson, urlAddressVo);
                Map<String, String> headMap = RySignHeaderUtil.getSignHeadMap(urlAddressVo.getAccessId(), urlAddressVo.getSecretKey(), interfaceAddress);
                logDetailDto.setReqHead(JSON.toJSONString(headMap));
                logDetailDto.setMethod(MaterialConstant.MATERIAL_COST_INTERFACE);
                logDetailDto.setRequestUri(interfaceAddress);
                logDetailDto.setMethodMsg("拉取SAP物流成本");
                externalLogVoService.addOrUpdateLog(logDetailDto, true);
                Result<String> result = BusinessHttpUtil.post(url + interfaceAddress, bodyJson, headMap);
                ExternalLogUtil.buildLogResult(logDetailDto, result);
                logDetailDto.setStatus(ExternalLogGlobalConstants.E);
                externalLogVoService.addOrUpdateLog(logDetailDto, false);
                if (result.isSuccess()
                        && StringUtil.isNotEmpty(result.getResult())) {
                    JSONObject jsonObject = JSONObject.parseObject(result.getResult());
                    if (jsonObject.containsKey(RyConstant.SAP_HEAD_DATA)
                            && Objects.nonNull(jsonObject.getJSONObject(RyConstant.SAP_HEAD_DATA))) {
                        JSONObject head = jsonObject.getJSONObject(RyConstant.SAP_HEAD_DATA);
                        String success = head.getString(RyConstant.SAP_HEAD_KEY);
                        String msg = head.getString(RyConstant.SAP_HEAD_MSG);
                        logDetailDto.setTipMsg(msg);
                        if (!ExternalLogGlobalConstants.S.equals(success)) {
                            externalLogVoService.addOrUpdateLog(logDetailDto, false);
                            return;

                        }
                    } else {
                        logDetailDto.setTipMsg("SAP返回的数据结构异常!");
                        externalLogVoService.addOrUpdateLog(logDetailDto, false);
                        return;
                    }
                    logDetailDto.setStatus(ExternalLogGlobalConstants.S);
                    externalLogVoService.addOrUpdateLog(logDetailDto, false);
                    if (jsonObject.containsKey(RyConstant.SAP_DETAIL_DATA)
                            && CollectionUtil.isNotEmpty(jsonObject.getJSONArray(RyConstant.SAP_DETAIL_DATA))) {
                        JSONArray jsonArray = jsonObject.getJSONArray(RyConstant.SAP_DETAIL_DATA);
                        logDetailDto.setRespJsonSize(jsonArray.size());
                        Map<String, MaterialCostEntity> dataMap = this.sapDataToEntityList(jsonArray, crmUserIdentity);
                        this.saveOrUpdateMap(dataMap);
                    }
                } else {
                    log.error("=====>   拉取物流成本 公司[{}] 修改时间{} 失败    <=====", companyCode, updateTimeList);
                    log.error("{}", result);
                }
                log.info("=====>   拉取物流成本  end    <=====");
            });
        } catch (Exception e) {
            log.error("=====>   拉取物流成本失败    <=====");
            log.error(e.getMessage(), e);
            throw e;
        } finally {
            redisLockService.unlock(lockKey);
            log.info("=====>   拉取物流成本 end    <=====");
        }
    }

    /**
     * 分离新增和更新的数据,并保存数据
     *
     * @param dataMap
     */
    private void saveOrUpdateMap(Map<String, MaterialCostEntity> dataMap) {
        if (CollectionUtil.isEmpty(dataMap)) {
            return;
        }
        List<MaterialCostEntity> oldList = materialCostRepository.findAllByOnlyKeys(Lists.newArrayList(dataMap.keySet()));
        if (Objects.isNull(oldList)) {
            oldList = Lists.newArrayList();
        }
        Map<String, MaterialCostEntity> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getOnlyKey()))
                .collect(Collectors.toMap(MaterialCostEntity::getOnlyKey, v -> v, (n, o) -> n));
        List<MaterialCostEntity> updateList = Lists.newArrayList();
        List<MaterialCostEntity> saveList = Lists.newArrayList();
        dataMap.values().forEach(entity -> {
            MaterialCostEntity oldEntity = oldMap.get(entity.getOnlyKey());
            if (Objects.nonNull(oldEntity)) {
                this.buildUpdateEntity(oldEntity, entity);
                updateList.add(oldEntity);
            } else {
                saveList.add(entity);
            }
        });

        if (CollectionUtil.isNotEmpty(saveList)) {
            materialCostRepository.saveBatchXml(saveList);
        }
        if (CollectionUtil.isNotEmpty(updateList)) {
            materialCostRepository.updateBatchXml(updateList);
        }
    }

    /**
     * 构建更新实体信息
     *
     * @param oldEntity
     * @param entity
     */
    private void buildUpdateEntity(MaterialCostEntity oldEntity, MaterialCostEntity entity) {
        oldEntity.setMaterialCode(entity.getMaterialCode());
        oldEntity.setMaterialName(entity.getMaterialName());
        oldEntity.setCompanyCode(entity.getCompanyCode());
        oldEntity.setYearStr(entity.getYearStr());

        oldEntity.setMaterialLargeClass(entity.getMaterialLargeClass());
        oldEntity.setMaterialFineClass(entity.getMaterialFineClass());

        oldEntity.setColdLogisticsPrice(entity.getColdLogisticsPrice());
        oldEntity.setExpressLogisticsPrice(entity.getExpressLogisticsPrice());
        oldEntity.setLargeLogisticsPrice(entity.getLargeLogisticsPrice());
        oldEntity.setFactoryStraightPrice(entity.getFactoryStraightPrice());

        oldEntity.setDataSource(entity.getDataSource());
        oldEntity.setSyncUpdateTime(entity.getSyncUpdateTime());
        oldEntity.setDelFlag(entity.getDelFlag());
        oldEntity.setEnableStatus(entity.getEnableStatus());
        oldEntity.setModifyAccount(entity.getModifyAccount());
        oldEntity.setModifyName(entity.getModifyName());
        oldEntity.setModifyTime(entity.getModifyTime());
    }

    /**
     * 获取更新时间
     *
     * @param isAll
     * @return
     */
    private List<String> buildUpdateTimeList(String isAll) {
        List<String> updateTimeList = Lists.newArrayList();
        if (StringUtil.isNotEmpty(isAll)
                && BooleanEnum.TRUE.getCapital().equals(isAll)) {
            updateTimeList.add("");
        } else {
            Date dateNow = new Date();
            updateTimeList.add(DateUtil.format(DateUtil.dateAddDay(dateNow, -2), DateUtil.DEFAULT_YEAR_MONTH_DAY_NO_CH));
            updateTimeList.add(DateUtil.format(DateUtil.dateAddDay(dateNow, -1), DateUtil.DEFAULT_YEAR_MONTH_DAY_NO_CH));
            updateTimeList.add(DateUtil.format(dateNow, DateUtil.DEFAULT_YEAR_MONTH_DAY_NO_CH));
        }
        return updateTimeList;
    }

    /**
     * SAP 的 JSON 数据转 实体
     *
     * @param jsonArray
     */
    private Map<String, MaterialCostEntity> sapDataToEntityList(JSONArray jsonArray, AbstractCrmUserIdentity crmUserIdentity) {
        if (CollectionUtil.isEmpty(jsonArray)) {
            return Maps.newHashMap();
        }
        List<SapMaterialCostVo> sapCostCenterVoList = jsonArray.toJavaList(SapMaterialCostVo.class);
        Map<String, MaterialCostEntity> centerMap = Maps.newConcurrentMap();
        String dataSource = ExternalSystemEnum.SAP.getCode();
        Date nowDate = new Date();
        sapCostCenterVoList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getBUKRS()))
                .filter(k -> StringUtil.isNotEmpty(k.getGJAHR()))
                .filter(k -> StringUtil.isNotEmpty(k.getMATNR()))
                .forEach(vo -> {
                    MaterialCostEntity entity = this.buildEntity(vo);
                    this.setBaseInfo(vo, entity, crmUserIdentity);
                    entity.setDataSource(dataSource);
                    entity.setSyncUpdateTime(nowDate);
                    try {
                        //MD5(公司代码+会计年度+物料编码)
                        String onlyKey = StringUtils.stripToEmpty(entity.getCompanyCode())
                                + StringUtils.stripToEmpty(entity.getYearStr())
                                + StringUtils.stripToEmpty(entity.getMaterialCode());
                        MessageDigest md5 = MessageDigest.getInstance("MD5");
                        byte[] digest = md5.digest(onlyKey.getBytes(StandardCharsets.UTF_8));
                        onlyKey = DatatypeConverter.printHexBinary(digest).toLowerCase();
                        entity.setOnlyKey(onlyKey);
                        centerMap.put(onlyKey, entity);
                    } catch (NoSuchAlgorithmException e) {
                        log.error("=====>   物流成本MD5(公司代码+会计年度+物料编码)异常 [{}]   <=====", JSON.toJSONString(vo));
                        log.error(e.getMessage(), e);
                    }
                });
        return centerMap;
    }

    /**
     * 构建实体
     *
     * @param vo
     * @return
     */
    private MaterialCostEntity buildEntity(SapMaterialCostVo vo) {
        MaterialCostEntity entity = new MaterialCostEntity();
        entity.setMaterialCode(vo.getMATNR());
        entity.setMaterialName(vo.getMAKTX());
        entity.setCompanyCode(vo.getBUKRS());
        entity.setYearStr(vo.getGJAHR());

        entity.setMaterialLargeClass(vo.getZWLDL());
        entity.setMaterialFineClass(vo.getZWLXL());

        entity.setLargeLogisticsPrice(vo.getZDHWLV());
        entity.setExpressLogisticsPrice(vo.getZKDWUV());
        entity.setColdLogisticsPrice(vo.getZLLWLV());
        entity.setFactoryStraightPrice(vo.getZGCZFV());
        return entity;
    }


    /**
     * 构建body参数
     *
     * @param updateTimeList 更新时间  yyyyMMdd 集合
     * @param companyCode    公司
     * @return
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/27 15:17
     */
    private String buildBodyJson(List<String> updateTimeList, String companyCode, String yearStr) {

        String dateNow = DateUtil.dateStrNowYYYYMMDD();
        JSONObject ctrl = new JSONObject();
        ctrl.put("SYSID", CommonConstant.CURRENT_SYSTEM);
        ctrl.put("REVID", ExternalSystemEnum.SAP.getCode());
        ctrl.put("FUNID", MaterialConstant.MATERIAL_COST_INTERFACE);
        ctrl.put("INFID", UuidCrmUtil.general());
        ctrl.put("UNAME", CommonConstant.CURRENT_SYSTEM);
        ctrl.put("DATUM", dateNow);
        ctrl.put("UZEIT", DateUtil.dateStrNowHms());
        ctrl.put("KEYID", "");
        ctrl.put("TABIX", 0);
        ctrl.put("MSGTY", "");
        ctrl.put("MSAGE", "");
        JSONArray jsonArray = new JSONArray();

        updateTimeList.forEach(updateTime -> {
            JSONObject data = new JSONObject();
            //公司代码 所有取表ZTCO007
            data.put("BUKRS", companyCode);
            //会计年度  yyyy
            data.put("GJAHR", yearStr);
            //物料编号
            data.put("MATNR", "");
            //更新日期
            data.put("LAEDA", updateTime);
            jsonArray.add(data);
        });

        JSONObject jsonObject = new JSONObject();
        jsonObject.put(RyConstant.SAP_HEAD_DATA, ctrl);
        jsonObject.put(RyConstant.SAP_DETAIL_DATA, jsonArray);
        return jsonObject.toJSONString();
    }


    /**
     * 设置基础信息
     *
     * @param flagOpEntity
     */
    private void setBaseInfo(SapMaterialCostVo item, TenantFlagOpEntity flagOpEntity, AbstractCrmUserIdentity crmUserIdentity) {
        Date date = new Date();
        flagOpEntity.setId(UuidCrmUtil.general());
        flagOpEntity.setTenantCode(TenantUtils.getTenantCode());
        flagOpEntity.setCreateAccount(crmUserIdentity.getUsername());
        flagOpEntity.setCreateName(crmUserIdentity.getRealName());
        flagOpEntity.setCreateTime(date);
        flagOpEntity.setModifyAccount(crmUserIdentity.getUsername());
        flagOpEntity.setModifyName(crmUserIdentity.getRealName());
        flagOpEntity.setModifyTime(date);
        flagOpEntity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        EnableStatusEnum enableStatusEnum = EnableStatusEnum.ENABLE;
        flagOpEntity.setEnableStatus(enableStatusEnum.getCode());
    }

    /**
     * 设置基础信息
     *
     * @param entity
     */
    private void setBaseInfo(TenantFlagOpEntity entity, AbstractCrmUserIdentity crmUserIdentity) {
        Date date = new Date();
        entity.setId(UuidCrmUtil.general());
        entity.setTenantCode(TenantUtils.getTenantCode());
        entity.setCreateAccount(crmUserIdentity.getUsername());
        entity.setCreateName(crmUserIdentity.getRealName());
        entity.setCreateTime(date);
        entity.setModifyAccount(crmUserIdentity.getUsername());
        entity.setModifyName(crmUserIdentity.getRealName());
        entity.setModifyTime(date);
        entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());

    }
}
