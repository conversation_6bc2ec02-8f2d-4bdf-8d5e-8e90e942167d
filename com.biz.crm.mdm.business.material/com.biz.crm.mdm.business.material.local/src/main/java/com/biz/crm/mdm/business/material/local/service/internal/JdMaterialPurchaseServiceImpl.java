package com.biz.crm.mdm.business.material.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.material.local.entity.JdMaterialPurchaseEntity;
import com.biz.crm.mdm.business.material.local.repository.JdMaterialPurchaseRepository;
import com.biz.crm.mdm.business.material.local.service.JdMaterialPurchaseService;
import com.biz.crm.mdm.business.material.sdk.dto.JdMaterialPurchaseDto;
import com.biz.crm.mdm.business.material.sdk.vo.JdMaterialPurchaseVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 物料采购信息接口实现
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
@Slf4j
public class JdMaterialPurchaseServiceImpl implements JdMaterialPurchaseService {

    @Autowired(required = false)
    private JdMaterialPurchaseRepository jdMaterialPurchaseRepository;

    @Autowired(required = false)
    @Qualifier("nebulaToolkitService")
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Override
    public Page<JdMaterialPurchaseVo> findByConditions(Pageable pageable, JdMaterialPurchaseDto dto) {
        Page<JdMaterialPurchaseVo> page = jdMaterialPurchaseRepository.findByConditions(pageable, dto);
        return page;
    }

    @Override
    public JdMaterialPurchaseVo findById(String id) {
        Validate.notEmpty(id, "ID不能为空");
        JdMaterialPurchaseEntity entity = jdMaterialPurchaseRepository.getById(id);
        Validate.notNull(entity, "不存在或已删除");
        JdMaterialPurchaseVo vo = nebulaToolkitService.copyObjectByWhiteList(entity, JdMaterialPurchaseVo.class, HashSet.class, ArrayList.class);
        return vo;
    }

    @Override
    public JdMaterialPurchaseVo findByMaterialCode(String materialCode) {
        Validate.notEmpty(materialCode, "物料编码不能为空");
        JdMaterialPurchaseEntity entity = jdMaterialPurchaseRepository.findByMaterialCode(materialCode);
        if (entity == null) {
            return null;
        }
        JdMaterialPurchaseVo vo = nebulaToolkitService.copyObjectByWhiteList(entity, JdMaterialPurchaseVo.class, HashSet.class, ArrayList.class);
        return vo;
    }

    @Override
    public List<JdMaterialPurchaseVo> findByMaterialCodes(List<String> materialCodes) {
        if (CollectionUtils.isEmpty(materialCodes)) {
            return Lists.newArrayList();
        }
        List<JdMaterialPurchaseVo> voList = jdMaterialPurchaseRepository.findByMaterialCodes(materialCodes);
        return voList;
    }

    @Override
    @Transactional
    public JdMaterialPurchaseVo create(JdMaterialPurchaseDto jdMaterialPurchaseDto) {
        Validate.notNull(jdMaterialPurchaseDto, "进行当前操作时，信息对象必须传入!!");
        Validate.notEmpty(jdMaterialPurchaseDto.getMaterialCode(), "添加信息时，物料编码不能为空！");
        Validate.notEmpty(jdMaterialPurchaseDto.getMaterialName(), "添加信息时，物料名称不能为空！");
        
        // 检查是否已存在
        JdMaterialPurchaseEntity existEntity = jdMaterialPurchaseRepository.findByMaterialCode(jdMaterialPurchaseDto.getMaterialCode());
        Validate.isTrue(existEntity == null, "该物料的采购信息已存在，不能重复添加");
        
        JdMaterialPurchaseEntity entity = nebulaToolkitService.copyObjectByWhiteList(jdMaterialPurchaseDto, JdMaterialPurchaseEntity.class, HashSet.class, ArrayList.class);
        entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        entity.setTenantCode(TenantUtils.getTenantCode());
        entity.setCreateAccount(loginUserService.getLoginAccountName());
        entity.setCreateTime(new Date());
        
        jdMaterialPurchaseRepository.save(entity);
        
        JdMaterialPurchaseVo vo = nebulaToolkitService.copyObjectByWhiteList(entity, JdMaterialPurchaseVo.class, HashSet.class, ArrayList.class);
        return vo;
    }

    @Override
    @Transactional
    public JdMaterialPurchaseVo update(JdMaterialPurchaseDto jdMaterialPurchaseDto) {
        Validate.notNull(jdMaterialPurchaseDto, "进行当前操作时，信息对象必须传入!!");
        Validate.notEmpty(jdMaterialPurchaseDto.getId(), "修改信息时，ID不能为空！");
        
        JdMaterialPurchaseEntity entity = jdMaterialPurchaseRepository.getById(jdMaterialPurchaseDto.getId());
        Validate.notNull(entity, "不存在或已删除");
        
        JdMaterialPurchaseEntity updateEntity = nebulaToolkitService.copyObjectByWhiteList(jdMaterialPurchaseDto, JdMaterialPurchaseEntity.class, HashSet.class, ArrayList.class);
        updateEntity.setTenantCode(TenantUtils.getTenantCode());
        updateEntity.setModifyAccount(loginUserService.getLoginAccountName());
        updateEntity.setModifyTime(new Date());
        
        jdMaterialPurchaseRepository.updateById(updateEntity);
        
        // 重新查询最新数据
        entity = jdMaterialPurchaseRepository.getById(jdMaterialPurchaseDto.getId());
        JdMaterialPurchaseVo vo = nebulaToolkitService.copyObjectByWhiteList(entity, JdMaterialPurchaseVo.class, HashSet.class, ArrayList.class);
        return vo;
    }

    @Override
    @Transactional
    public void deleteBatch(List<String> ids) {
        Validate.isTrue(CollectionUtils.isNotEmpty(ids), "请选中要操作的数据");
        jdMaterialPurchaseRepository.lambdaUpdate()
                .set(JdMaterialPurchaseEntity::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
                .set(JdMaterialPurchaseEntity::getModifyAccount, loginUserService.getLoginAccountName())
                .set(JdMaterialPurchaseEntity::getModifyTime, new Date())
                .in(JdMaterialPurchaseEntity::getId, ids)
                .update();
    }

    @Override
    @Transactional
    public void enableBatch(List<String> ids) {
        Validate.isTrue(CollectionUtils.isNotEmpty(ids), "请选中要操作的数据");
        jdMaterialPurchaseRepository.lambdaUpdate()
                .set(JdMaterialPurchaseEntity::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .set(JdMaterialPurchaseEntity::getModifyAccount, loginUserService.getLoginAccountName())
                .set(JdMaterialPurchaseEntity::getModifyTime, new Date())
                .in(JdMaterialPurchaseEntity::getId, ids)
                .update();
    }

    @Override
    @Transactional
    public void disableBatch(List<String> ids) {
        Validate.isTrue(CollectionUtils.isNotEmpty(ids), "请选中要操作的数据");
        jdMaterialPurchaseRepository.lambdaUpdate()
                .set(JdMaterialPurchaseEntity::getEnableStatus, EnableStatusEnum.DISABLE.getCode())
                .set(JdMaterialPurchaseEntity::getModifyAccount, loginUserService.getLoginAccountName())
                .set(JdMaterialPurchaseEntity::getModifyTime, new Date())
                .in(JdMaterialPurchaseEntity::getId, ids)
                .update();
    }

    @Override
    @Transactional
    public void importUpdateXml(List<JdMaterialPurchaseDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }
        dtoList.forEach(dto -> dto.setModifyAccount(loginUserService.getLoginAccountName()));
        jdMaterialPurchaseRepository.importUpdateXml(dtoList);
    }
}
