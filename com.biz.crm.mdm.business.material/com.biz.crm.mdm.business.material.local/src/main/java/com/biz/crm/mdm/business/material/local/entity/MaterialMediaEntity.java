package com.biz.crm.mdm.business.material.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.FileEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 物料图片实体
 *
 * <AUTHOR>
 * @date 2021/12/13
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MaterialMediaEntity", description = "物料图片实体")
@Entity
@Table(name = "mdm_material_media")
@TableName(value = "mdm_material_media")
@org.hibernate.annotations.Table(appliesTo = "mdm_material_media", comment = "物料图片表")
public class MaterialMediaEntity extends FileEntity {

  private static final long serialVersionUID = -3600811507086010031L;

  /**
   * 排序位置
   */
  @ApiModelProperty(name = "range_num", value = "排序位置", required = true)
  @Column(name = "range_num", columnDefinition = "INT(5) COMMENT '排序位置'")
  private Integer rangeNum;

  /**
   * 物料id
   */
  @ApiModelProperty(name = "material_id", value = "物料id", required = true)
  @Column(name = "material_id", length = 64, columnDefinition = "VARCHAR(64) NOT NULL COMMENT '物料id'")
  private String materialId;

  /**
   * 图片类型(central 主图片 display 陈列图片 detail 详情图片)
   */
  @ApiModelProperty(name = "business_type", value = "图片类型(central 主图片 display 陈列图片 detail 详情图片)", required = true)
  @Column(name = "business_type", length = 64, columnDefinition = "VARCHAR(64) COMMENT '图片类型(central 主图片 display 陈列图片 detail 详情图片)'")
  private String businessType;

}
