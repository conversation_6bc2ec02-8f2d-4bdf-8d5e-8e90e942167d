package com.biz.crm.mdm.business.material.local.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.mapper.BusinessBaseMapper;
import com.biz.crm.mdm.business.material.local.entity.MaterialCostEntity;
import com.biz.crm.mdm.business.material.local.entity.MaterialEntity;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialCostDto;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialCostVo;
import org.apache.ibatis.annotations.Param;

/**
 * 物流成本数据库接口层
 *
 * <AUTHOR>
 * @date 2021-09-27 14:44:10
 */
public interface MaterialCostMapper extends BusinessBaseMapper<MaterialCostEntity> {

    /**
     * 分页查询
     *
     * @param page
     * @param dto
     * @return
     */
    Page<MaterialCostVo> findByConditions(Page<MaterialCostVo> page, @Param("dto") MaterialCostDto dto);
}
