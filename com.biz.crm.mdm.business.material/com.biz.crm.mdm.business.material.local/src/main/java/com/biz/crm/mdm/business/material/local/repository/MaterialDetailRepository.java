package com.biz.crm.mdm.business.material.local.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.mdm.business.material.local.entity.MaterialDetailEntity;
import com.biz.crm.mdm.business.material.local.mapper.MaterialDetailMapper;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 物料详情表的数据库访问类 {@link MaterialDetailEntity}
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/27 17:23
 */
@Component
public class MaterialDetailRepository extends ServiceImpl<MaterialDetailMapper, MaterialDetailEntity> {

    public void saveBatchXml(List<MaterialDetailEntity> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return;
        }
        Lists.partition(entityList, CommonConstant.MAX_PAGE_SIZE).forEach(list -> {
            this.baseMapper.insertBatchSomeColumn(list);
        });
    }


    public void updateBatchXml(List<MaterialDetailEntity> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return;
        }
        Lists.partition(entityList, CommonConstant.MAX_PAGE_SIZE).forEach(this::updateBatchById);
    }

    public List<MaterialDetailEntity> findDetailByMaterialCodes(String tenantCode, Set<String> materialCodes) {
        if (CollectionUtil.isEmpty(materialCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(MaterialDetailEntity::getTenantCode, tenantCode)
                .eq(MaterialDetailEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .in(MaterialDetailEntity::getMaterialCode, materialCodes)
                .list();
    }

    public List<MaterialDetailEntity> findDetailByIds(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(MaterialDetailEntity::getId, ids)
                .list();
    }
}
