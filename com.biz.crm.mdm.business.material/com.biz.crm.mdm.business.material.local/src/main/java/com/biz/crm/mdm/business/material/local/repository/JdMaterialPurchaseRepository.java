package com.biz.crm.mdm.business.material.local.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.material.local.entity.JdMaterialPurchaseEntity;
import com.biz.crm.mdm.business.material.local.mapper.JdMaterialPurchaseMapper;
import com.biz.crm.mdm.business.material.sdk.dto.JdMaterialPurchaseDto;
import com.biz.crm.mdm.business.material.sdk.vo.JdMaterialPurchaseVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * 物料采购信息数据库操作层实现
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@Component
public class JdMaterialPurchaseRepository extends ServiceImpl<JdMaterialPurchaseMapper, JdMaterialPurchaseEntity> {

    /**
     * 分页条件查询
     *
     * @param pageable 分页信息
     * @param dto 查询条件
     * @return 分页结果
     */
    public Page<JdMaterialPurchaseVo> findByConditions(Pageable pageable, JdMaterialPurchaseDto dto) {
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
        dto = Optional.ofNullable(dto).orElse(new JdMaterialPurchaseDto());
        Page<JdMaterialPurchaseVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        if (StringUtils.isEmpty(dto.getTenantCode())) {
            dto.setTenantCode(TenantUtils.getTenantCode());
        }
        if (StringUtils.isEmpty(dto.getDelFlag())) {
            dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return this.baseMapper.findByConditions(page, dto);
    }

    /**
     * 根据物料编码查询采购信息
     *
     * @param materialCodes 物料编码集合
     * @return 采购信息列表
     */
    public List<JdMaterialPurchaseVo> findByMaterialCodes(List<String> materialCodes) {
        if (CollectionUtils.isEmpty(materialCodes)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findByMaterialCodes(materialCodes);
    }

    /**
     * 根据物料编码查询采购信息
     *
     * @param materialCode 物料编码
     * @return 采购信息
     */
    public JdMaterialPurchaseEntity findByMaterialCode(String materialCode) {
        if (StringUtils.isBlank(materialCode)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(JdMaterialPurchaseEntity::getMaterialCode, materialCode)
                .eq(JdMaterialPurchaseEntity::getTenantCode, TenantUtils.getTenantCode())
                .eq(JdMaterialPurchaseEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(JdMaterialPurchaseEntity::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .one();
    }

    /**
     * 批量保存
     *
     * @param entityList 实体列表
     */
    public void saveBatchXml(List<JdMaterialPurchaseEntity> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return;
        }
        Lists.partition(entityList, CommonConstant.MAX_PAGE_SIZE).forEach(list -> {
            this.baseMapper.insertBatchSomeColumn(list);
        });
    }

    /**
     * 批量更新
     *
     * @param updateList 更新列表
     */
    public void updateBatchXml(List<JdMaterialPurchaseEntity> updateList) {
        if (CollectionUtil.isEmpty(updateList)) {
            return;
        }
        Lists.partition(updateList, CommonConstant.MAX_PAGE_SIZE).forEach(this::updateBatchById);
    }

    /**
     * 导入更新
     *
     * @param dtoList DTO列表
     */
    public void importUpdateXml(List<JdMaterialPurchaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        this.baseMapper.importUpdateXml(dtoList);
    }
}
