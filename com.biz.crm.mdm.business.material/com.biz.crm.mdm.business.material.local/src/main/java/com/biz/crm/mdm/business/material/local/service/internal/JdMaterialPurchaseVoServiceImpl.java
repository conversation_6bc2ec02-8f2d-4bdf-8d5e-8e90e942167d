package com.biz.crm.mdm.business.material.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.material.local.service.JdMaterialPurchaseService;
import com.biz.crm.mdm.business.material.sdk.dto.JdMaterialPurchaseDto;
import com.biz.crm.mdm.business.material.sdk.service.JdMaterialPurchaseVoService;
import com.biz.crm.mdm.business.material.sdk.vo.JdMaterialPurchaseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 物料采购信息SDK接口实现
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class JdMaterialPurchaseVoServiceImpl implements JdMaterialPurchaseVoService {

    @Autowired(required = false)
    private JdMaterialPurchaseService jdMaterialPurchaseService;

    @Override
    public Page<JdMaterialPurchaseVo> findByConditions(Pageable pageable, JdMaterialPurchaseDto dto) {
        return jdMaterialPurchaseService.findByConditions(pageable, dto);
    }

    @Override
    public JdMaterialPurchaseVo findById(String id) {
        return jdMaterialPurchaseService.findById(id);
    }

    @Override
    public JdMaterialPurchaseVo findByMaterialCode(String materialCode) {
        return jdMaterialPurchaseService.findByMaterialCode(materialCode);
    }

    @Override
    public List<JdMaterialPurchaseVo> findByMaterialCodes(List<String> materialCodes) {
        return jdMaterialPurchaseService.findByMaterialCodes(materialCodes);
    }

    @Override
    public JdMaterialPurchaseVo create(JdMaterialPurchaseDto jdMaterialPurchaseDto) {
        return jdMaterialPurchaseService.create(jdMaterialPurchaseDto);
    }

    @Override
    public JdMaterialPurchaseVo update(JdMaterialPurchaseDto jdMaterialPurchaseDto) {
        return jdMaterialPurchaseService.update(jdMaterialPurchaseDto);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        jdMaterialPurchaseService.deleteBatch(ids);
    }

    @Override
    public void enableBatch(List<String> ids) {
        jdMaterialPurchaseService.enableBatch(ids);
    }

    @Override
    public void disableBatch(List<String> ids) {
        jdMaterialPurchaseService.disableBatch(ids);
    }

    @Override
    public void importUpdateXml(List<JdMaterialPurchaseDto> dtoList) {
        jdMaterialPurchaseService.importUpdateXml(dtoList);
    }
}
