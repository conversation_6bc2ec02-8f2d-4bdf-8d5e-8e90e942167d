package com.biz.crm.mdm.business.material.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 物流成本Dto
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/27 20:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "MaterialCostDto", description = "物流成本Dto")
public class MaterialCostDto extends TenantFlagOpDto {


    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("公司代码")
    private String companyCode;

    @ApiModelProperty("会计年度")
    private String yearStr;

    @ApiModelProperty("物料大类")
    private String materialLargeClass;

    @ApiModelProperty("物料细类")
    private String materialFineClass;

    @ApiModelProperty("MD5(公司代码+会计年度+物料编码)")
    private String onlyKey;

    @ApiModelProperty("大货物流价格")
    private BigDecimal largeLogisticsPrice;

    @ApiModelProperty("快递物流价格")
    private BigDecimal expressLogisticsPrice;

    @ApiModelProperty("冷链物流价格")
    private BigDecimal coldLogisticsPrice;

    @ApiModelProperty("工厂直发价格")
    private BigDecimal factoryStraightPrice;

    @ApiModelProperty("数据同步时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date syncUpdateTime;

    @ApiModelProperty("数据源[数据字典:mdm_data_source]")
    private String dataSource;

}
