package com.biz.crm.mdm.business.material.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * SAP物流成本VO
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/27 20:21
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ApiModel(value = "SapMaterialCostVo", description = "SAP物流成本VO")
public class SapMaterialCostVo implements Serializable {

    private static final long serialVersionUID = 60121450933775228L;

    @ApiModelProperty("公司代码")
    private String BUKRS;

    @ApiModelProperty("会计年度")
    private String GJAHR;

    @ApiModelProperty("物料编号")
    private String MATNR;

    @ApiModelProperty("物料描述")
    private String MAKTX;

    @ApiModelProperty("经营指标")
    private String ZKPI;

    @ApiModelProperty("物料大类")
    private String ZWLDL;

    @ApiModelProperty("物料细类")
    private String ZWLXL;

    @ApiModelProperty("大货物流价格")
    private BigDecimal ZDHWLV;

    @ApiModelProperty("快递物流价格")
    private BigDecimal ZKDWUV;

    @ApiModelProperty("冷链物流价格")
    private BigDecimal ZLLWLV;

    @ApiModelProperty("工厂直发价格")
    private BigDecimal ZGCZFV;

    @ApiModelProperty("创建人")
    private String ZCJNAME;

    @ApiModelProperty("创建日期")
    private String ZCJDATE;

    @ApiModelProperty("创建时间")
    private String ZCJTIME;

    @ApiModelProperty("修改人")
    private String ZXGNAME;

    @ApiModelProperty("修改日期")
    private String LAEDA;

    @ApiModelProperty("修改时间")
    private String ZXGTIME;

}
