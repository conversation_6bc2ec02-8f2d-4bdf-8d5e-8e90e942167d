package com.biz.crm.mdm.business.material.sdk.dto;

import com.biz.crm.business.common.sdk.dto.FileDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 物料照片信息请求dto
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "物料照片信息dto")
public class MaterialMediaDto extends FileDto {
  /**
   * 物料编码
   */
  @ApiModelProperty("物料编码")
  private String materialCode;

  /**
   * 排序位置
   */
  @ApiModelProperty("排序位置")
  private Integer rangeNum;

  /**
   * 图片类型 central 主图片 display 陈列图片 detail 详情图片
   */
  @ApiModelProperty("图片类型 central 主图片 display 陈列图片 detail 详情图片")
  private String businessType;
}
