package com.biz.crm.mdm.business.material.sdk.vo;

import com.biz.crm.business.common.sdk.vo.FileVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 物料照片信息Vo
 *
 * <AUTHOR>
 * @date 2021/12/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "物料照片信息Vo")
public class MaterialMediaVo extends FileVo {

  private static final long serialVersionUID = 2987333995363845305L;

  /**
   * 物料编码
   */
  @ApiModelProperty("物料编码")
  private String materialCode;

  /**
   * 排序位置
   */
  @ApiModelProperty("排序位置")
  private Integer rangeNum;

  /**
   * 图片类型 central 主图片 display 陈列图片 detail 详情图片
   */
  @ApiModelProperty("图片类型 central 主图片 display 陈列图片 detail 详情图片")
  private String businessType;
}
