package com.biz.crm.mdm.business.material.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.biz.crm.mdm.business.material.unit.vo.MaterialUnitTypeVo;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductLevelVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 物料vo
 *
 * <AUTHOR>
 * @date 2021-09-27 14:44:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "MaterialVo", description = "物料vo")
public class MaterialVo extends TenantFlagOpVo {
    private static final long serialVersionUID = 4433325597448264619L;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("物料类型[数据字典:material_type]")
    private String materialType;

    @ApiModelProperty("行销物料类型[数据字典:mdm_sell_material_type]")
    private String sellMaterialType;

    @ApiModelProperty("物料类型名称")
    private String materialTypeName;

    @ApiModelProperty("物料简称")
    private String simpleName;

    @ApiModelProperty("产品层级编码")
    private String productLevelCode;

    @ApiModelProperty("产品层级名称")
    private String productLevelName;

    @ApiModelProperty("物料组编码")
    private String materialGroupCode;

    @ApiModelProperty("物料组名称")
    private String materialGroupName;

    @ApiModelProperty("ai编码")
    private String aiCode;

    @ApiModelProperty("条形码/69码")
    private String barCode;

    @ApiModelProperty("财务经营指标")
    private String financeIndex;

    @ApiModelProperty("产品小类")
    private String productSmallClassCode;

    @ApiModelProperty("产品小类")
    private String productSmallClassName;

    @ApiModelProperty("工厂编码")
    private String factoryTypeCode;

    @ApiModelProperty("工厂编码")
    private String factoryTypeName;

    @ApiModelProperty("销售公司")
    private String saleCompany;

    @ApiModelProperty("销售公司编码集合")
    private List<String> saleCompanyList;

    @ApiModelProperty("销售公司编码（列表显示用）。多个名称用逗号隔开")
    private String saleCompanyName;

    @ApiModelProperty("规格")
    private String specification;

    @ApiModelProperty("成本价格")
    private BigDecimal costPrice;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    @ApiModelProperty("税率(展示使用)")
    private String taxRateStr;

    @ApiModelProperty("毛重")
    private String grossWeight;

    @ApiModelProperty("净重")
    private String netWeight;

    @ApiModelProperty("重量单位")
    private String weightUnit;

    @ApiModelProperty("长/米")
    private String longMetre;

    @ApiModelProperty("宽/米")
    private String wideMetre;

    @ApiModelProperty("高/米")
    private String highMetre;

    @ApiModelProperty("零级编码")
    private String zeroLevelCode;

    @ApiModelProperty("零级名称")
    private String zeroLevelName;

    @ApiModelProperty("一级编码")
    private String oneLevelCode;

    @ApiModelProperty("一级名称")
    private String oneLevelName;

    @ApiModelProperty("二级编码")
    private String twoLevelCode;

    @ApiModelProperty("二级名称")
    private String twoLevelName;

    @ApiModelProperty("三级编码")
    private String threeLevelCode;

    @ApiModelProperty("三级名称")
    private String threeLevelName;

    @ApiModelProperty("四级编码")
    private String fourLevelCode;

    @ApiModelProperty("四级名称")
    private String fourLevelName;

    @ApiModelProperty("容量/体积")
    private String capacity;

    @ApiModelProperty("标准单位/基本计量单位")
    private String standardUnit;

    @ApiModelProperty("物料单位/基本计量单位类型编码")
    private String unitTypeCode;

    @ApiModelProperty("基本计量单位名称")
    private String unitConversion;

    @ApiModelProperty("转换值")
    private String conversionValue;

    @ApiModelProperty("盒码单位转换系数")
    private BigDecimal boxUnitConversion;

    @ApiModelProperty("箱码单位转换系数")
    private BigDecimal caseUnitConversion;

    @ApiModelProperty("品相编码")
    private String productPhaseCode;

    @ApiModelProperty("品相名称")
    private String productPhaseName;

    @ApiModelProperty("数据同步时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date syncUpdateTime;

    @ApiModelProperty("数据源[数据字典:mdm_data_source]")
    private String dataSource;

    @ApiModelProperty("物料单位类型VO")
    private MaterialUnitTypeVo materialUnitTypeVo;

    @ApiModelProperty("图片信息")
    private List<MaterialMediaVo> picList;

    @ApiModelProperty("层级集合")
    private List<ProductLevelVo> productLevels;

    @ApiModelProperty("明细集合")
    private List<MaterialDetailVo> detailList;
}