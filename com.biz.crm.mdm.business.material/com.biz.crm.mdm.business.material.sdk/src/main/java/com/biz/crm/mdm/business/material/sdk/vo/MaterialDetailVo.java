package com.biz.crm.mdm.business.material.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 物料详情VO
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/27 17:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "MaterialDetailVo", description = "物料详情VO")
public class MaterialDetailVo extends TenantFlagOpVo {

    private static final long serialVersionUID = -7710814836868026204L;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("工厂编码")
    private String factoryTypeCode;

    @ApiModelProperty("工厂编码")
    private String factoryTypeName;

    @ApiModelProperty("成本价格")
    private BigDecimal costPrice;

    @ApiModelProperty("成本价格单位")
    private String costPriceUnit;

    @ApiModelProperty("是否发布标准成本标识;数据字典[mdm_material_cost_label]")
    private String costLabel;
}