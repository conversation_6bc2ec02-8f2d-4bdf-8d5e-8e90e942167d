package com.biz.crm.mdm.business.terminal.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 根据TerminalQueryDto获取匹配的终端编码
 *
 * <AUTHOR>
 * @date 2022/5/27
 */
@Data
public class TerminalQueryDto extends TenantFlagOpDto {

    @ApiModelProperty("终端编码模糊查询")
    private String terminalCode;

    @ApiModelProperty("终端名称模糊查询")
    private String terminalName;

    @ApiModelProperty("渠道编码")
    private String channel;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称模糊查询")
    private String orgName;

    @ApiModelProperty("组织上下级规则编码")
    private String orgRuleCode;

    @ApiModelProperty(value = "组织编码集合", hidden = true)
    private Set<String> orgCodes;

    @ApiModelProperty("审核状态")
    private String processStatus;

    @ApiModelProperty("终端编码集合查询")
    private Set<String> terminalCodeSet;

    @ApiModelProperty("组织编码集合")
    private Set<String> orgCodeSet;

    @ApiModelProperty("职位编码集合")
    private Set<String> positionCodeSet;

    @ApiModelProperty("终端标签")
    private String tag;

    @ApiModelProperty("费用投入情况")
    private String feeInvestment;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("回显编码集合")
    private List<String> selectedCodes;

    @ApiModelProperty("回显编码集合")
    private String selectedCode;

    @ApiModelProperty("所属系统")
    private String sourceSystem;
    @ApiModelProperty("关联客户名称")
    private String relaCusName;

    @ApiModelProperty("关联业务员名称")
    private String relaBusinessName;

    /**
     * 费用投入时间查询节点类型，month,quarter,year
     */
    @ApiModelProperty("费用投入时间查询节点类型")
    private String costDateType;

    /**
     * 费用记录所属年份
     */
    @ApiModelProperty("费用记录所属年份")
    private String costYear;

    /**
     * 费用记录查询字段 后端动态拼接 请勿接收任何来自前端传入的该字段的值，防止SQL注入
     */
    @ApiModelProperty("费用记录查询字段 后端动态拼接")
    private String selectField;

    @ApiModelProperty("终端关键字")
    private String terminalKeyWord;




}
