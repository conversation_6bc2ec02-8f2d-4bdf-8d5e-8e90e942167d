package com.biz.crm.mdm.business.terminal.sdk.event;

import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalEventDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-06-14 10:34
 * @description：终端信息日志变动
 */
public interface TerminalLogListener {

  /**
   * 创建时触发
   *
   * @param dto
   */
  default void onCreate(TerminalEventDto dto) {}

  /**
   * 审批通过
   *
   * @param dto
   */
  default void onApproved(TerminalEventDto dto) {}

  /**
   * 编辑时触发
   *
   * @param dto
   */
  default void onUpdate(TerminalEventDto dto) {}

  /**
   * 启用时触发
   *
   * @param dto
   */
  default void onEnable(TerminalEventDto dto) {}

  /**
   * 禁用时触发
   *
   * @param dto
   */
  default void onDisable(TerminalEventDto dto) {}
}
