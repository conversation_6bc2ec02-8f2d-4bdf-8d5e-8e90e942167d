package com.biz.crm.mdm.business.terminal.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 附近客户 请求dto
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "TerminalNearbyClientDto", description = "附近客户 ")
public class TerminalNearbyClientDto extends TenantFlagOpDto {

    @ApiModelProperty("职位编码集合")
    private Set<String> positionCodeSet;

    @ApiModelProperty("当前经纬度，格式104.10194,30.65984")
    private String location;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("客户编码")
    private String clientCode;

    @ApiModelProperty("客户名称")
    private String clientName;

    @ApiModelProperty("客户类型")
    private String clientType;

    @ApiModelProperty("渠道")
    private String channel;

    @ApiModelProperty("执行状态类型：查询已执行/未执行,传Y/N")
    private String type;

    @ApiModelProperty("合同客户;数据字典[yesOrNo]")
    private String contractCustomer;

    @ApiModelProperty("回显集合")
    private List<String> selectedCodes;

    @ApiModelProperty("包含编码查询条件")
    private List<String> includeCodeList;

    @ApiModelProperty("不包含编码查询条件")
    private List<String> notIncludeCodeList;
}
