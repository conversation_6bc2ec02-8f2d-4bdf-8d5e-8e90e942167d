package com.biz.crm.mdm.business.terminal.sdk.event;

import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalRelateOrgEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * 终端组织信息绑定关系变更事件接口
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
public interface TerminalRelateOrgEventListener extends NebulaEvent {

  /**
   * 重新绑定时触发
   *
   * @param dto
   */
  default void onRebind(TerminalRelateOrgEventDto dto) {}
}
