package com.biz.crm.mdm.business.terminal.sdk.dto;

import com.biz.crm.business.common.sdk.vo.UuidFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "终端扩展信息Vo")
public class TerminalRelaBusinessDto extends UuidFlagOpVo {

    private static final long serialVersionUID = 6719277991667861734L;

    @ApiModelProperty("终端编码")
    private String terminalCode;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("职位编码")
    private String positionCode;

    @ApiModelProperty("职位名称")
    private String positionName;

    @ApiModelProperty("人员姓名")
    private String fullName;

    @ApiModelProperty("组织-职位-人员名称")
    private String unionName;


}
