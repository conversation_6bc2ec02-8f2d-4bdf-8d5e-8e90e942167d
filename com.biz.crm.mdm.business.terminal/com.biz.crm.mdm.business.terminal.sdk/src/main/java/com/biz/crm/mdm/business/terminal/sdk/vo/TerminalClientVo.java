package com.biz.crm.mdm.business.terminal.sdk.vo;

import com.biz.crm.business.common.sdk.vo.UuidVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 终端客户信息Vo
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ApiModel(value = "TerminalClientVo", description = "终端客户信息Vo")
public class TerminalClientVo extends UuidVo {

  private static final long serialVersionUID = 1535385238781546122L;
  /**
   * 终端编码
   */
  @ApiModelProperty("终端编码")
  private String terminalCode;

  /**
   * 终端名称
   */
  @ApiModelProperty("终端名称")
  private String terminalName;

  /**
   * 终端类型
   */
  @ApiModelProperty("终端类型")
  private String terminalType;

  /**
   * 渠道
   */
  @ApiModelProperty("渠道")
  private String channel;

  /**
   * 终端地址
   */
  @ApiModelProperty("终端地址")
  private String terminalAddress;

  /**
   * 省
   */
  @ApiModelProperty("省")
  private String provinceCode;

  @ApiModelProperty("省名称")
  private String provinceName;

  /**
   * 市
   */
  @ApiModelProperty("市")
  private String cityCode;

  @ApiModelProperty("市名称")
  private String cityName;

  /**
   * 区
   */
  @ApiModelProperty("区")
  private String districtCode;

  @ApiModelProperty("区名称")
  private String districtName;

  /**
   * 店招照片
   */
  @ApiModelProperty("店招照片")
  private String shopImagePath;
  /**
   * 经度
   */
  @ApiModelProperty("经度")
  private BigDecimal longitude;

  /**
   * 纬度
   */
  @ApiModelProperty("纬度")
  private BigDecimal latitude;

  /**
   * 审批状态
   */
  @ApiModelProperty("审批状态 ")
  private String processStatus;

  @ApiModelProperty("联系人信息集合")
  private List<TerminalContactVo> contacts;

  @ApiModelProperty("终端标签集合")
  private List<TerminalTagVo> tags;
}