package com.biz.crm.mdm.business.terminal.sdk.enums;

import com.biz.crm.business.common.base.util.DateUtil;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @className: com.biz.crm.mdm.business.terminal.sdk.enums.TerminalCostSelectDateType
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-08-09 15:05
 */
@Getter
@AllArgsConstructor
public enum TerminalCostSelectDateEnum {

    JANUARY("january", Lists.newArrayList("01"), "一月", "month"),
    FEBRUARY("february", Lists.newArrayList("02"), "二月", "month"),
    MARCH("march", Lists.newArrayList("03"), "三月", "month"),
    APRIL("april", Lists.newArrayList("04"), "四月", "month"),
    MAY("may", Lists.newArrayList("05"), "五月", "month"),
    JUNE("june", Lists.newArrayList("06"), "六月", "month"),
    JULY("july", Lists.newArrayList("07"), "七月", "month"),
    AUGUST("august", Lists.newArrayList("08"), "八月", "month"),
    SEPTEMBER("september", Lists.newArrayList("09"), "九月", "month"),
    OCTOBER("october", Lists.newArrayList("10"), "十月", "month"),
    NOVEMBER("november", Lists.newArrayList("11"), "十一月", "month"),
    DECEMBER("december", Lists.newArrayList("12"), "十二月", "month"),
    QUARTER_ONE("quarter_one", Lists.newArrayList("01", "02", "03"), "季度一", "quarter"),
    QUARTER_TWO("quarter_two", Lists.newArrayList("04", "05", "06"), "季度二", "quarter"),
    QUARTER_THREE("quarter_three", Lists.newArrayList("07", "08", "09"), "季度三", "quarter"),
    QUARTER_FOUR("quarter_four", Lists.newArrayList("10", "11", "12"), "季度四", "quarter"),
    TOTAL_YEAR("total_year", Lists.newArrayList("01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"), "整年", "year");

    private final String code;
    private final List<String> month;
    private final String desc;
    private final String type;

    public static TerminalCostSelectDateEnum getByCode(String code) {
        if (StringUtil.isEmpty(code)) {
            return null;
        }
        return Arrays.stream(TerminalCostSelectDateEnum.values()).filter(item -> Objects.equals(item.getCode(), code))
                .findFirst().orElse(null);
    }

    /**
     * 根据选择条件 查询当前时间对应的查询时间节点 month,quarter,year
     * @param type
     * @return
     */
    public static TerminalCostSelectDateEnum getBySelectType(String type) {
        if (StringUtil.isEmpty(type)) {
            return null;
        }
        if (type.equals("year")) {
            return TOTAL_YEAR;
        }
        int currentMonth = DateUtil.getCurrentMonth();
        String nowMonth = "";
        if (currentMonth < 10) {
            nowMonth = "0" + currentMonth;
        } else {
            nowMonth = "" + currentMonth;
        }
        String finalNowMonth = nowMonth;
        return Arrays.stream(TerminalCostSelectDateEnum.values()).filter(item -> Objects.equals(item.getType(), type) && item.month.contains(finalNowMonth))
                .findFirst().orElse(null);
    }
}
