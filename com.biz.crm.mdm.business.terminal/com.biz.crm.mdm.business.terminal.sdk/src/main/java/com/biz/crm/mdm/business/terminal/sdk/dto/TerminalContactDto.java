package com.biz.crm.mdm.business.terminal.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 终端扩展信息dto
 *
 * <AUTHOR>
 * @since 2021-10-19 13:53:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "TerminalContactDto", description = "终端扩展信息dto")
public class TerminalContactDto extends TenantDto {

  /** 终端编码 */
  @ApiModelProperty("终端编码")
  private String terminalCode;

  /** 联系人姓名 */
  @ApiModelProperty("联系人姓名")
  private String contactName;

  /** 联系人电话 */
  @ApiModelProperty("联系人电话")
  private String contactPhone;

  /** 主联系人,true是,false否 */
  @ApiModelProperty("主联系人,true是,false否")
  private Boolean contactMain;
}
