package com.biz.crm.mdm.business.terminal.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 终端与组织关联表vo
 *
 * <AUTHOR>
 * @since 2021-10-19 13:54:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "终端与组织关联表Vo")
public class TerminalRelaOrgVo extends TenantVo {
  private static final long serialVersionUID = -5887337562577265036L;

  /** 终端编码 */
  @ApiModelProperty("终端编码")
  private String terminalCode;

  /** 组织编码 */
  @ApiModelProperty("组织编码")
  private String orgCode;

  /** 组织名称 */
  @ApiModelProperty("组织名称")
  private String orgName;
}
