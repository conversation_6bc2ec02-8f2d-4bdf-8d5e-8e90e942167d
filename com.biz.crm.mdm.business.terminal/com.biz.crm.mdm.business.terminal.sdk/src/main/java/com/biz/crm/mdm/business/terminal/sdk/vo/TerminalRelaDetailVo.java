package com.biz.crm.mdm.business.terminal.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Set;
import lombok.Data;

/**
 * @title TerminalRelaDetailVo
 * @date 2023/8/28 18:11
 * <AUTHOR>
 * @description
 */
@Data
@ApiModel("终端关联详情vo")
public class TerminalRelaDetailVo {

  /**
   * 组织编码集
   */
  @ApiModelProperty("组织编码集")
  Set<String> orgCodes;

  /**
   * 渠道编码集
   */
  @ApiModelProperty("渠道编码集")
  Set<String> channels;

  /**
   * 标签
   */
  @ApiModelProperty("标签")
  Set<String> tags;

  /**
   * 客户组织编码集
   */
  @ApiModelProperty("客户组织编码集")
  Set<String> customerOrgCodes;

}
