package com.biz.crm.mdm.business.terminal.sdk.deprecated.vo;

import com.biz.crm.business.common.sdk.deprecated.vo.CrmExtTenVo;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2020/9/7 3:58 下午
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Deprecated
public class MdmTerminalSupplyVo extends CrmExtTenVo {
  
  private static final long serialVersionUID = -3019931515747812938L;

  @ApiModelProperty("供货关系类型")
  private String supplyType;

  @ApiModelProperty("用户账号")
  private String userName;

  @ApiModelProperty("终端编码")
  private String terminalCode;

  private List<String> terminalCodeList;

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("组织名称")
  private String orgName;

  @ApiModelProperty("用户名称")
  private String fullName;

  @ApiModelProperty("上级客户编码")
  private String customerCode;

  @ApiModelProperty("上级客户名称")
  private String customerName;

  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty("职位名称")
  private String positionName;

  @ApiModelProperty("销售公司编码")
  private String saleCompany;

  @ApiModelProperty("渠道")
  private String channel;

  private String channelName;

  @ApiModelProperty("供货关系明细")
  private List<MdmTerminalSupplyDetailVo> details;

}
