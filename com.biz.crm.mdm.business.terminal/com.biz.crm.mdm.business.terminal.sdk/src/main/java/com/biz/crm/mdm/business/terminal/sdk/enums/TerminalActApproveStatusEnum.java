package com.biz.crm.mdm.business.terminal.sdk.enums;

import lombok.Getter;

/**
 * 终端审核状态
 *
 * <AUTHOR>
 * @date 2021/10/19
 */
@Getter
public enum TerminalActApproveStatusEnum {
  /** 终端审核状态 */
  SUBMIT("1", "待提交"),
  APPROVAL("2", "审批中"),
  PASS("3", "通过"),
  REJECT("4", "驳回"),
  RECOVER("5", "追回"),
  CLOSE("6", "关闭");

  private String code;
  private String des;

  TerminalActApproveStatusEnum(String code, String des) {
    this.code = code;
    this.des = des;
  }
}
