package com.biz.crm.mdm.business.terminal.sdk.event;

import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * 终端信息变更事件接口
 *
 * <AUTHOR>
 * @since 2021-10-19 13:53:14
 */
public interface TerminalLogEventListener extends NebulaEvent {

  /**
   * 创建时触发
   *
   * @param dto
   */
  default void onCreate(TerminalEventDto dto) {}

  /**
   * 审批通过
   *
   * @param dto
   */
  default void onApproved(TerminalEventDto dto) {}

  /**
   * 编辑时触发
   *
   * @param dto
   */
  default void onUpdate(TerminalEventDto dto) {}

  /**
   * 启用时触发
   *
   * @param dto
   */
  default void onEnable(TerminalEventDto dto) {}

  /**
   * 禁用时触发
   *
   * @param dto
   */
  default void onDisable(TerminalEventDto dto) {}

  /**
   * 删除时触发
   *
   * @param dto
   */
  default void onDelete(TerminalEventDto dto) {}
}
