package com.biz.crm.mdm.business.terminal.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 门店供货关系明细vo
 *
 * <AUTHOR>
 * @since 2021-10-19 13:55:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "门店供货关系明细Vo")
public class TerminalSupplyDetailVo extends TenantVo {
  private static final long serialVersionUID = 1900877236921320206L;

  /** 类型1商品2产品层级 */
  @ApiModelProperty("类型1商品2产品层级")
  private String dataType;

  /** 编码 */
  @ApiModelProperty("编码")
  private String code;

  /** 描述 */
  @ApiModelProperty("描述")
  private String name;

  /** 供货关系id */
  @ApiModelProperty("供货关系id")
  private String supplyId;

  @ApiModelProperty("商品关联的物料信息")
  private List<ProductMaterialDetailVo> materialDetailVoList;
}
