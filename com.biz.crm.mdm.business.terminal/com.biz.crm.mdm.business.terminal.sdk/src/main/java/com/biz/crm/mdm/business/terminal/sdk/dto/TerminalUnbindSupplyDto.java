package com.biz.crm.mdm.business.terminal.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 解绑供货关系dto
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Data
@ApiModel(value = "TerminalUnbindSupplyDto", description = "解绑供货关系dto")
public class TerminalUnbindSupplyDto {

  /** 岗位编码 */
  @ApiModelProperty("岗位编码")
  private String positionCode;

  /** 客户用户账户 */
  @ApiModelProperty("客户用户账户")
  private String customerUserName;

  /** 客户用户编码 */
  @ApiModelProperty("客户用户编码")
  private String customerCode;

  /** 终端编码集合 */
  @ApiModelProperty("终端编码集合")
  private List<String> terminalCodeList;
}
