package com.biz.crm.mdm.business.terminal.sdk.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @className: com.biz.crm.mdm.business.terminal.sdk.vo.ClientTagVo
 * @description: 描述
 * @author: xiaopeng.zhang
 * @create: 2024-07-30 16:49
 */
@Data
public class ClientTagVo {

    @ApiModelProperty("客户编码")
    private String clientCode;

    /**
     * 标签类型
     */
    @ApiModelProperty("标签类型")
    private String tagType;

    /**
     * 标签描述
     */
    @ApiModelProperty("标签描述")
    private String tagDescription;
}
