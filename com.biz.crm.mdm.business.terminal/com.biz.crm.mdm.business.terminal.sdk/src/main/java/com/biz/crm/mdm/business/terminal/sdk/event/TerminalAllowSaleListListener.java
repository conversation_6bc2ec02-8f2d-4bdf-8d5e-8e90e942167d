//package com.biz.crm.mdm.business.terminal.sdk.event;
//
//import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalAllowSaleEventDto;
//import com.bizunited.nebula.event.sdk.service.NebulaEvent;
//
///**
// * 终端信息变更通知允销监听
// *
// * <AUTHOR>
// * @date 2022/5/24
// */
//public interface TerminalAllowSaleListListener extends NebulaEvent {
//  /**
//   * 新增到匹配的可购清单
//   *
//   * @param dto
//   */
//  default void onCreate(TerminalAllowSaleEventDto dto) {}
//
//  /**
//   * 从现有的可购清单移除
//   *
//   * @param dto
//   */
//  default void onDelete(TerminalAllowSaleEventDto dto) {}
//}
