package com.biz.crm.mdm.business.terminal.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 附近客户信息vo
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "附近客户信息vo")
public class TerminalNearbyClientVo extends TenantFlagOpVo {

    private static final long serialVersionUID = -1745385819389469042L;

    @ApiModelProperty("客户编码")
    private String clientCode;

    @ApiModelProperty("客户名称")
    private String clientName;

    @ApiModelProperty("客户手机号")
    private String clientPhone;
    /**
     * 客户类型
     * {@link ClientTypeEnum#getDictCode()}
     */
    @ApiModelProperty("客户类型")
    private String clientType;

    @ApiModelProperty("头像文件fileCode")
    private String clientPhoto;

    @ApiModelProperty("客户细类")
    private String clientSubType;

    @ApiModelProperty("距离(公里)")
    private BigDecimal distance;

    @ApiModelProperty("距离单位(公里)")
    private String distanceUnit = "km";

    @ApiModelProperty("网点地址")
    private String clientAddress;

    @ApiModelProperty("网点经度")
    private BigDecimal longitude;

    @ApiModelProperty("网点纬度")
    private BigDecimal latitude;

    @ApiModelProperty("陌拜状态")
    private String strangeStatus;

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("异常状态")
    private String enableStatus;

    @ApiModelProperty("业务员人员账号")
    private String businessUserName;

    @ApiModelProperty("业务员人员姓名")
    private String businessRealName;

    @ApiModelProperty("业务员人员电话")
    private String businessUserPhone;

    @ApiModelProperty("业务员员职位编码")
    private String businessPositionCode;

    @ApiModelProperty("业务员员职位名称")
    private String businessPositionName;

    @ApiModelProperty("业务员员职位名称/业务员人员姓名")
    private String businessUnionName;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("创建人职位编码")
    private String createPostCode;

    @ApiModelProperty("创建人职位")
    private String createPostName;

    @ApiModelProperty("合同客户;数据字典[yesOrNo]")
    private String contractCustomer;

    @ApiModelProperty("联系人")
    private String contactName;

    @ApiModelProperty("客户标签")
    private String customerTag;

    @ApiModelProperty("标签集合")
    private List<ClientTagVo> tagVoList;

}
