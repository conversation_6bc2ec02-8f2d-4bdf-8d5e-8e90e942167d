package com.biz.crm.mdm.business.terminal.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.terminal.local.entity.Terminal;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalCodeSearchDto;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalLugAndLatQueryDto;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalPaginationDto;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 终端信息(Terminal)表服务接口
 *
 * <AUTHOR>
 * @since 2021-10-18 17:46:51
 */
public interface TerminalService {

  /**
   * 分页条件查询
   *
   * @param pageable
   * @param dto
   * @return
   */
  Page<Terminal> findByConditions(Pageable pageable, TerminalPaginationDto dto);

  /**
   * 按id查询详情
   *
   * @param id
   * @return
   */
  Terminal findDetailById(String id);

  /**
   * 创建
   *
   * @param terminal
   * @return
   */
  Terminal create(Terminal terminal);

  /**
   * 更新
   *
   * @param terminal
   * @return
   */
  Terminal update(Terminal terminal);

  /**
   * 按id集合启用
   *
   * @param ids
   */
  void enableBatch(List<String> ids);

  /**
   * 按id集合禁用
   *
   * @param ids
   */
  void disableBatch(List<String> ids);

  /**
   * 逻辑删除
   *
   * @param ids
   */
  void updateDelFlagByIds(List<String> ids);

  /**
   * 根据条件获取匹配的终端信息
   *
   * @param ids
   * @param terminalCodes
   * @return
   */
  List<Terminal> findDetailsByIdsOrTerminalCodes(List<String> ids, List<String> terminalCodes);

  /**
   * 根据终端编码集合查询
   *
   * @param terminalCodes
   * @return
   */
  List<Terminal> findByTerminalCodes(List<String> terminalCodes);

  /**
   * 根据客户编码集合查询
   *
   * @param customerOrgCodes
   * @return
   */
  List<Terminal> findByCustomerOrgCodes(List<String> customerOrgCodes);

  /**
   * 根据组织、渠道、标签获取匹配的终端编码
   *
   * @param dto
   * @return
   */
  Set<String> findByTerminalCodeSearchDto(TerminalCodeSearchDto dto);

  /**
   * 根据终端编码获取终端信息
   *
   * @param terminalCode
   * @return
   */
  Terminal findByTerminalCode(String terminalCode);

  /**
   * 根据流程编号获取终端信息
   *
   * @param processNumber
   * @return
   */
  Terminal findByProcessNumber(String processNumber);

  /**
   * 流程变更更新终端审批状态信息
   *
   * @param terminal
   */
  void updateByProcess(Terminal terminal);

  /**
   * 根据创建人账号，年月日期，业务系统类型统计数据
   *
   * @param createAccount
   * @param dateType
   * @param fromType
   * @return {"count":12}
   */
  Map<String, Integer> findCountByCreateAccountAndDateTypeAndFromType(String createAccount, String dateType, String fromType);

  /**
   * 根据经度和纬度查找范围内的数据
   *
   * @param dto
   * @return
   */
  List<Terminal> findByLngAndLat(TerminalLugAndLatQueryDto dto, String tenantCode);

}
