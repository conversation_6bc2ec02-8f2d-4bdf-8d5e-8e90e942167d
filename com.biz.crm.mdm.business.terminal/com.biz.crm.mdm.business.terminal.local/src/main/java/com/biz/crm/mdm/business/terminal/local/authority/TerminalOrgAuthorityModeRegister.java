package com.biz.crm.mdm.business.terminal.local.authority;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaOrg;
import com.biz.crm.mdm.business.terminal.local.service.TerminalRelaBusinessService;
import com.biz.crm.mdm.business.terminal.local.service.TerminalRelaOrgService;
import com.bizunited.nebula.mars.sdk.register.SelectAuthorityModeRegister;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * @Author: heguanyun
 * @Date: 2022/4/7 17:25
 * description:
 */
@Component
public class TerminalOrgAuthorityModeRegister implements SelectAuthorityModeRegister {

    @Autowired(required = false)
    private TerminalRelaOrgService terminalRelaOrgService;

    @Override
    public String modeKey() {
        return "terminalOrgAuthorityModeRegister";
    }

    @Override
    public String modeName() {
        return "按照当前登录者组织关联的终端维度的值确认";
    }

    @Override
    public String controlKey() {
        return "terminalOrgAuthorityModeRegister";
    }

    @Override
    public int sort() {
        return 7;
    }

    @Override
    public String groupCode() {
        return "terminal_position_scope";
    }

    @Override
    public boolean isArrayValue() {
        return true;
    }

    @Override
    public boolean isStaticValue() {
        return false;
    }

    @Override
    public Class<?> modeValueClass() {
        return String.class;
    }

    @Override
    public Object staticValue(String[] staticValues) {
        return CommonConstant.NOT_AUTHORITY_ARR;
    }

    @Override
    public Object dynamicValue(UserIdentity loginDetails, String modeGroupCode) {
        String identityType = loginDetails.getIdentityType();
        //如果不是后台管理用户，就不按职位字段进行权限控制
        if (!StringUtils.equals(identityType, "u")) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        AbstractCrmUserIdentity loginUserDetails = (AbstractCrmUserIdentity) loginDetails;
        Object object = loginUserDetails.invokeFieldValue("orgCode");

        List<String> orgCodeList = Collections.singletonList(object.toString());
        if (CollectionUtil.isEmpty(orgCodeList)) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        List<TerminalRelaOrg> terminalRelaOrgList = terminalRelaOrgService.findByOrgCodes(orgCodeList);
        if (CollectionUtil.isEmpty(terminalRelaOrgList)) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        return terminalRelaOrgList.stream().map(TerminalRelaOrg::getTerminalCode).distinct().toArray(String[]::new);
    }

    @Override
    public String converterKey() {
        return "chartArrayMarsAuthorityAstConverter";
    }
}
