<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.terminal.local.mapper.TerminalRelaOrgMapper">

  <select id="findAllowSaleTerminalByOrgCodes"
    resultType="com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaOrg">
    select a.terminal_code,a.org_code from mdm_terminal_r_org a
    left join mdm_terminal b on a.tenant_code=b.tenant_code and a.terminal_code=b.terminal_code
    where a.tenant_code=#{tenantCode}
    and b.del_flag=#{delFlag}
    and a.org_code in (<foreach collection="list" item="item" separator=",">#{item}</foreach>)
  </select>
</mapper>
