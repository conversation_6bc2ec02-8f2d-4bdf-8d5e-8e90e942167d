package com.biz.crm.mdm.business.terminal.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.UuidOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * @className: com.biz.crm.mdm.business.terminal.local.entity.TerminalCostRecordLog
 * @description: 终端费用投入记录表
 * @author: xiaopeng.zhang
 * @create: 2024-08-09 13:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("mdm_terminal_cost_record_log")
@Table(name = "mdm_terminal_cost_record_log", indexes = {
        @Index(name = "mdm_terminal_cost_uq1", columnList = "years,terminal_code", unique = true),
        @Index(name = "mdm_terminal_cost_index1", columnList = "terminal_code"),

})
@org.hibernate.annotations.Table(appliesTo = "mdm_terminal_cost_record_log", comment = "终端费用投入记录表")
@ApiModel(value = "TerminalCostRecordLog", description = "终端费用投入记录表")
public class TerminalCostRecordLog extends UuidOpEntity {

    /**
     * 终端编码
     */
    @ApiModelProperty("终端编码")
    @Column(name = "terminal_code", columnDefinition = "varchar(32) comment '终端编码'")
    private String terminalCode;

    /**
     * 年份 yyyy
     */
    @ApiModelProperty("年份")
    @Column(name = "years", columnDefinition = "varchar(20) comment '年份'")
    private String years;


    /************************各个月份，季度，整年的费用是否投入标记************************/
    //TODO 以下字段为该终端在对应年度下的各个月份，季度和整年的费用投入标记，Y：该时间节点下有费用标记，N：无费用标记；此表依据TPM相关费用记录更新，目前用于关联终端表

    /**
     * 一月
     */
    @ApiModelProperty("一月")
    @Column(name = "january", columnDefinition = "varchar(10) default 'N' COMMENT '一月'")
    private String january;

    /**
     * 二月
     */
    @ApiModelProperty("二月")
    @Column(name = "february", columnDefinition = "varchar(10) default 'N' COMMENT '二月'")
    private String february;

    /**
     * 三月
     */
    @ApiModelProperty("三月")
    @Column(name = "march", columnDefinition = "varchar(10) default 'N' COMMENT '三月'")
    private String march;

    /**
     * 四月
     */
    @ApiModelProperty("四月")
    @Column(name = "april", columnDefinition = "varchar(10) default 'N' COMMENT '四月'")
    private String april;

    /**
     * 五月
     */
    @ApiModelProperty("五月")
    @Column(name = "may", columnDefinition = "varchar(10) default 'N' COMMENT '五月'")
    private String may;

    /**
     * 六月
     */
    @ApiModelProperty("六月")
    @Column(name = "june", columnDefinition = "varchar(10) default 'N' COMMENT '六月'")
    private String june;

    /**
     * 七月
     */
    @ApiModelProperty("七月")
    @Column(name = "july", columnDefinition = "varchar(10) default 'N' COMMENT '七月'")
    private String july;

    /**
     * 八月
     */
    @ApiModelProperty("八月")
    @Column(name = "august", columnDefinition = "varchar(10) default 'N' COMMENT '八月'")
    private String august;

    /**
     * 九月
     */
    @ApiModelProperty("九月")
    @Column(name = "september", columnDefinition = "varchar(10) default 'N' COMMENT '九月'")
    private String september;

    /**
     * 十月
     */
    @ApiModelProperty("十月")
    @Column(name = "october", columnDefinition = "varchar(10) default 'N' COMMENT '十月'")
    private String october;

    /**
     * 十一月
     */
    @ApiModelProperty("十一月")
    @Column(name = "november", columnDefinition = "varchar(10) default 'N' COMMENT '十一月'")
    private String november;

    /**
     * 十二月
     */
    @ApiModelProperty("十二月")
    @Column(name = "december", columnDefinition = "varchar(10) default 'N' COMMENT '十二月'")
    private String december;

    /**
     * 季度一
     */
    @ApiModelProperty("季度一")
    @Column(name = "quarter_one", columnDefinition = "varchar(10) default 'N' COMMENT '季度一'")
    private String quarterOne;

    /**
     * 季度二
     */
    @ApiModelProperty("季度二")
    @Column(name = "quarter_two", columnDefinition = "varchar(10) default 'N' COMMENT '季度二'")
    private String quarterTwo;

    /**
     * 季度三
     */
    @ApiModelProperty("季度三")
    @Column(name = "quarter_three", columnDefinition = "varchar(10) default 'N' COMMENT '季度三'")
    private String quarterThree;

    /**
     * 季度四
     */
    @ApiModelProperty("季度四")
    @Column(name = "quarter_four", columnDefinition = "varchar(10) default 'N' COMMENT '季度四'")
    private String quarterFour;

    /**
     * 整年
     */
    @ApiModelProperty("整年")
    @Column(name = "total_year", columnDefinition = "varchar(10) default 'N' COMMENT '整年'")
    private String totalYear;
}
