package com.biz.crm.mdm.business.terminal.local.service;

import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaCus;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalRelaCusDto;

import java.util.List;

public interface TerminalRelaCusService {

    List<TerminalRelaCus> findByTerminalCodes(List<String> terminalCodes);

    List<TerminalRelaCus> findByTerminalCustomers(List<String> customerCodes, String customerName);

    /**
     * 保存数据
     * @param relaCusDtos
     * @param terminalCode
     */
    void saveBath(List<TerminalRelaCusDto> relaCusDtos,String terminalCode);

    void deleteByTerminalCodes(List<String> terminalCodes);
}
