package com.biz.crm.mdm.business.terminal.local.service;

import com.biz.crm.mdm.business.terminal.local.entity.TerminalSupplyDetail;
import java.util.List;

/**
 * 门店供货关系明细(TerminalSupplyDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2021-10-18 17:50:15
 */
public interface TerminalSupplyDetailService {

  /**
   * 根据供货关系id集合获取匹配数据集
   *
   * @param supplyIdList
   * @return
   */
  List<TerminalSupplyDetail> findBySupplyIds(List<String> supplyIdList);

  /**
   * 批量保存
   *
   * @param list
   */
  void saveBatch(List<TerminalSupplyDetail> list);

  /**
   * 根据supplyIdList删除数据，物理删除
   *
   * @param supplyIdList
   */
  void deleteBySupplyIds(List<String> supplyIdList);
}
