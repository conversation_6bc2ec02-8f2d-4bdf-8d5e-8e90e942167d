package com.biz.crm.mdm.business.terminal.local.authority;

import com.bizunited.nebula.mars.sdk.register.SelectAuthorityModeGroupRegister;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * @Author: heguanyun
 * @Date: 2022/4/7 17:27
 * description:
 */
@Component
public class TerminalPositionAuthorityModeGroupRegister implements SelectAuthorityModeGroupRegister {
    @Override
    public String groupCode() {
        return "terminal_position_scope";
    }

    @Override
    public String groupName() {
        return "按照职位关联终端或关联客户绑定终端维度授权";
    }

    @Override
    public Set<String> viewFieldNames() {
        return Sets.newHashSet("terminalCode");
    }

    @Override
    public Set<String> repositoryFieldNames() {
        return Sets.newHashSet("terminal_code");
    }
}
