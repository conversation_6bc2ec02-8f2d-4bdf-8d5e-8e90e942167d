package com.biz.crm.mdm.business.terminal.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalContact;
import com.biz.crm.mdm.business.terminal.local.service.TerminalContactService;
import com.biz.crm.mdm.business.terminal.sdk.service.TerminalContactVoService;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalContactVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * 终端扩展信息(TerminalContact)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-18 17:47:46
 */
@Service
public class TerminalContactVoServiceImpl implements TerminalContactVoService {

    @Autowired(required = false)
    private TerminalContactService terminalContactService;

    @Autowired(required = false)
    @Qualifier("nebulaToolkitService")
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public List<TerminalContactVo> findByTerminalCodes(List<String> terminalCodeList) {
        if (CollectionUtils.isEmpty(terminalCodeList)) {
            return Lists.newLinkedList();
        }
        List<TerminalContact> entityList = terminalContactService.findByTerminalCodes(terminalCodeList);
        if (CollectionUtil.isEmpty(entityList)) {
            return Lists.newArrayList();
        }
        return (List<TerminalContactVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entityList, TerminalContact.class, TerminalContactVo.class,
                HashSet.class, ArrayList.class);
    }

}
