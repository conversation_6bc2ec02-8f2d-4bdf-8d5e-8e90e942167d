package com.biz.crm.mdm.business.terminal.local.event;

import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalEventDto;
import com.biz.crm.mdm.business.terminal.sdk.event.TerminalLogListener;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-06-14 10:55
 * @description：
 */
@Component
public class TerminalLogListenerImpl implements TerminalLogListener {
  @Autowired(required = false)
  private CrmBusinessLogVoService crmBusinessLogVoService;

  @Override
  public void onCreate(TerminalEventDto dto) {
    TerminalVo newest = dto.getNewest();
    String onlyKey = newest.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(null);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }

  @Override
  public void onUpdate(TerminalEventDto dto) {
    TerminalVo newest = dto.getNewest();
    TerminalVo original = dto.getOriginal();
    String onlyKey = newest.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }

  @Override
  public void onEnable(TerminalEventDto dto) {
    String onlyKey = dto.getNewest().getId();
    TerminalVo newTer = new TerminalVo();
    newTer.setId(onlyKey);
    newTer.setEnableStatus(EnableStatusEnum.ENABLE.getDes());
    TerminalVo original = new TerminalVo();
    original.setId(onlyKey);
    original.setEnableStatus(EnableStatusEnum.DISABLE.getDes());
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.ENABLE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newTer);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }

  @Override
  public void onDisable(TerminalEventDto dto) {
    String onlyKey = dto.getNewest().getId();
    TerminalVo newTer = new TerminalVo();
    newTer.setId(onlyKey);
    newTer.setEnableStatus(EnableStatusEnum.DISABLE.getDes());
    TerminalVo original = new TerminalVo();
    original.setId(onlyKey);
    original.setEnableStatus(EnableStatusEnum.ENABLE.getDes());
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.DISABLE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newTer);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }

}
