package com.biz.crm.mdm.business.terminal.local.service.notifier;

import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.mdm.business.customer.user.sdk.dto.CustomerUserEventDto;
import com.biz.crm.mdm.business.customer.user.sdk.event.CustomerUserListener;
import com.biz.crm.mdm.business.customer.user.sdk.vo.CustomerUserVo;
import com.biz.crm.mdm.business.terminal.local.entity.Terminal;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalSupply;
import com.biz.crm.mdm.business.terminal.local.service.TerminalService;
import com.biz.crm.mdm.business.terminal.local.service.TerminalSupplyService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @describe: 客户用户事件监听器
 * @createTime 2022年01月11日 15:07:00
 */
@Component
public class CustomerUserListenerImpl implements CustomerUserListener {

  @Autowired(required = false)
  private TerminalSupplyService terminalSupplyService;

  @Autowired(required = false)
  private TerminalService terminalService;

  @Autowired(required = false)
  private CrmBusinessLogVoService crmBusinessLogVoService;

  /**
   * 客户用户新增事件
   *
   * @param customerUserEventDto
   */
  @Override
  public void onCreate(CustomerUserEventDto customerUserEventDto) {
    //添加日志
    CustomerUserVo newest = customerUserEventDto.getNewest();
    CustomerUserVo original = customerUserEventDto.getOriginal();
    String onlyKey = newest.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }

  /**
   * 更新时日志
   *
   * @param customerUserEventDto
   */
  @Override
  public void onUpdate(CustomerUserEventDto customerUserEventDto) {
    //添加日志
    CustomerUserVo newest = customerUserEventDto.getNewest();
    CustomerUserVo original = customerUserEventDto.getOriginal();
    String onlyKey = newest.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(newest);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }

  /**
   * 开启时日志
   *
   * @param userEventDto
   */
  @Override
  public void onEnable(CustomerUserEventDto userEventDto) {
    CustomerUserVo newLogVo = userEventDto.getNewest();
    CustomerUserVo oldLogVo = userEventDto.getOriginal();
    this.onEnable(oldLogVo, newLogVo);
  }

  /**
   * 禁用时日志
   *
   * @param userEventDto
   */
  @Override
  public void onDisable(CustomerUserEventDto userEventDto) {
    CustomerUserVo newLogVo = userEventDto.getNewest();
    CustomerUserVo oldLogVo = userEventDto.getOriginal();
    this.onDisable(oldLogVo, newLogVo);
  }

  private void onEnable(CustomerUserVo oldVo, CustomerUserVo newVo) {
    String onlyKey = newVo.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.ENABLE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(oldVo);
    crmBusinessLogDto.setNewObject(newVo);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }

  private void onDisable(CustomerUserVo oldVo, CustomerUserVo newVo) {
    String onlyKey = newVo.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.DISABLE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(oldVo);
    crmBusinessLogDto.setNewObject(newVo);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }


  /**
   * 客户用户删除事件
   *
   * @param customerUserEventDto
   */
  @Override
  public void onDelete(CustomerUserEventDto customerUserEventDto) {
    this.delete(customerUserEventDto);
        /*
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> userNameList = list.stream().map(CustomerUserVo::getUserName).collect(Collectors.toList());
        List<TerminalSupply> supplyList = this.terminalSupplyService.findByUserNames(userNameList);
        if (CollectionUtils.isEmpty(supplyList)) {
            return;
        }
        List<String> terminalCodes = supplyList.stream().map(TerminalSupply::getTerminalCode).collect(Collectors.toList());
        List<Terminal> terminalList = this.terminalService.findByTerminalCodes(terminalCodes);
        Validate.isTrue(CollectionUtils.isEmpty(terminalList), "该客户用户存在关联终端信息，无法删除!");
        //添加删除日志
        //记录删除日志
        list.stream().forEach(customerUserVo -> {
            String onlyKey = customerUserVo.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.DELETE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
            crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
            crmBusinessLogDto.setOldObject(customerUserVo);
            crmBusinessLogDto.setNewObject(null);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
        */
  }

  private void delete(CustomerUserEventDto customerUserEventDto) {
    CustomerUserVo original = customerUserEventDto.getOriginal();
    List<TerminalSupply> supplyList = this.terminalSupplyService
        .findByUserNames(Arrays.asList(original.getUserName()));
    if (CollectionUtils.isEmpty(supplyList)) {
      return;
    }
    List<String> terminalCodes = supplyList.stream().map(TerminalSupply::getTerminalCode)
        .collect(Collectors.toList());
    List<Terminal> terminalList = this.terminalService.findByTerminalCodes(terminalCodes);
    Validate.isTrue(CollectionUtils.isEmpty(terminalList), "该客户用户存在关联终端信息，无法删除!");
    //添加删除日志
    //记录删除日志
    String onlyKey = original.getId();
    CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
    crmBusinessLogDto.setOperationType(OperationTypeEunm.DELETE.getDictCode());
    crmBusinessLogDto.setOnlyKey(onlyKey);
    crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
    crmBusinessLogDto.setOldObject(original);
    crmBusinessLogDto.setNewObject(null);
    crmBusinessLogVoService.handleSave(crmBusinessLogDto);
  }
}
