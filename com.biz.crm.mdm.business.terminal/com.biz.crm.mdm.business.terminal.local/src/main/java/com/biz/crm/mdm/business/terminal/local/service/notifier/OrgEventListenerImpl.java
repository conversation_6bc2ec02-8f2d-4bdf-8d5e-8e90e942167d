package com.biz.crm.mdm.business.terminal.local.service.notifier;

import com.biz.crm.mdm.business.org.sdk.dto.OrgEventBatchDto;
import com.biz.crm.mdm.business.org.sdk.dto.OrgEventDto;
import com.biz.crm.mdm.business.org.sdk.event.OrgEventListener;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaOrg;
import com.biz.crm.mdm.business.terminal.local.service.TerminalRelaOrgService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 组织事件监听器实现
 *
 * <AUTHOR>
 * @date 2021/12/14
 */
@Component
public class OrgEventListenerImpl implements OrgEventListener {

  @Autowired(required = false) private TerminalRelaOrgService terminalRelaOrgService;

  @Override
  public void onDelete(List<String> orgCodes) {
    Validate.isTrue(CollectionUtils.isNotEmpty(orgCodes), "组织编码编码参数不能为空!");
    List<TerminalRelaOrg> list = this.terminalRelaOrgService.findByOrgCodes(orgCodes);
    Set<String> orgCodeSet =
        list.stream()
            .filter(a -> StringUtils.isNotBlank(a.getOrgCode()))
            .map(TerminalRelaOrg::getOrgCode)
            .collect(Collectors.toSet());

    Validate.isTrue(
        CollectionUtils.isEmpty(orgCodeSet),
        StringUtils.join(orgCodeSet, ",") + "存在已关联终端的组织,不能删除!");
  }

  @Override
  public void onDeleteBatch(OrgEventBatchDto orgEventBatchDto) {

  }

  @Override
  public void onEnableBatch(OrgEventBatchDto orgEventBatchDto) {

  }

  @Override
  public void onDisableBatch(OrgEventBatchDto orgEventBatchDto) {

  }

  @Override
  public void onUpdate(OrgEventDto orgEventDto) {

  }
}
