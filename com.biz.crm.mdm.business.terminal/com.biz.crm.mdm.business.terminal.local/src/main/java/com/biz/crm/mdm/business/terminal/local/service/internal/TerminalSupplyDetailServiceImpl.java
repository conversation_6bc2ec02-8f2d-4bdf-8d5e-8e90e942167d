package com.biz.crm.mdm.business.terminal.local.service.internal;

import com.biz.crm.mdm.business.terminal.local.entity.TerminalSupplyDetail;
import com.biz.crm.mdm.business.terminal.local.repository.TerminalSupplyDetailRepository;
import com.biz.crm.mdm.business.terminal.local.service.TerminalSupplyDetailService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 门店供货关系明细(TerminalSupplyDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-18 17:50:15
 */
@Service("terminalSupplyDetailService")
public class TerminalSupplyDetailServiceImpl implements TerminalSupplyDetailService {

    @Autowired(required = false)
    private TerminalSupplyDetailRepository terminalSupplyDetailRepository;

    @Override
    public List<TerminalSupplyDetail> findBySupplyIds(List<String> supplyIdList) {
        if (CollectionUtils.isEmpty(supplyIdList)) {
            return Lists.newLinkedList();
        }
        return terminalSupplyDetailRepository.findBySupplyIds(supplyIdList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<TerminalSupplyDetail> list) {
        Validate.isTrue(CollectionUtils.isNotEmpty(list), "供货关系明细信息不能为空");
        Optional<TerminalSupplyDetail> first =
                list.stream()
                        .filter(a -> StringUtils.isBlank(a.getSupplyId())
                                || StringUtils.isBlank(a.getDataType())
                                || StringUtils.isBlank(a.getCode()))
                        .findFirst();
        Validate.isTrue(!first.isPresent(), "关联id、类型、编码不能为空");
        Set<String> set = Sets.newHashSet();
        list.forEach(a -> Validate.isTrue(set.add(a.getDataType() + "-" + a.getCode()), "存在重复的记录信息"));

        List<String> supplyIdList = list.stream()
                .filter(a -> StringUtils.isNotBlank(a.getSupplyId()))
                .map(TerminalSupplyDetail::getSupplyId)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(supplyIdList)) {
            terminalSupplyDetailRepository.deleteBySupplyIds(supplyIdList);
        }
        for (TerminalSupplyDetail terminalSupplyDetail : list) {
            terminalSupplyDetail.setId(null);
            terminalSupplyDetail.setTenantCode(TenantUtils.getTenantCode());
        }
        terminalSupplyDetailRepository.saveBatch(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBySupplyIds(List<String> supplyIdList) {
        Validate.isTrue(CollectionUtils.isNotEmpty(supplyIdList), "关联id集合不能为空");
        terminalSupplyDetailRepository.deleteBySupplyIds(supplyIdList);
    }
}
