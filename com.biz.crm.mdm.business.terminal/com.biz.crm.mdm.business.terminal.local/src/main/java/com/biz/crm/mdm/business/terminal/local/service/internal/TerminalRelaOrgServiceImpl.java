package com.biz.crm.mdm.business.terminal.local.service.internal;

import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgTreeByTypeVo;
import com.biz.crm.mdm.business.terminal.local.entity.Terminal;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaOrg;
import com.biz.crm.mdm.business.terminal.local.repository.TerminalRelaOrgRepository;
import com.biz.crm.mdm.business.terminal.local.repository.TerminalRepository;
import com.biz.crm.mdm.business.terminal.local.service.TerminalRelaOrgService;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalRebindOrgDto;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalRelateOrgEventDto;
import com.biz.crm.mdm.business.terminal.sdk.event.TerminalRelateOrgEventListener;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 终端与组织关联表(TerminalROrg)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-18 17:48:34
 */
@Service("terminalRelaOrgService")
public class TerminalRelaOrgServiceImpl implements TerminalRelaOrgService {

  @Autowired(required = false) private TerminalRelaOrgRepository terminalRelaOrgRepository;

  @Autowired(required = false) private TerminalRepository terminalRepository;

  @Autowired(required = false) private NebulaNetEventClient nebulaNetEventClient;

  @Autowired(required = false) private NebulaToolkitService nebulaToolkitService;

  @Autowired(required = false)
  private OrgVoService orgVoService;

  /**
   * 基于数据库执行的数据视图执行内容缓存（最多500毫秒）
   */
  private static volatile Cache<String, List<TerminalRelaOrg>> cache = null;

  public TerminalRelaOrgServiceImpl(){
    if(cache == null) {
      synchronized (TerminalRelaOrgServiceImpl.class) {
        while(cache == null) {
          cache = CacheBuilder.newBuilder()
                  .initialCapacity(10000)
                  .expireAfterWrite(500, TimeUnit.MILLISECONDS)
                  .maximumSize(100000)
                  .build();
        }
      }
    }
  }

  @Override
  public List<TerminalRelaOrg> findByTerminalCodes(List<String> terminalCodeList) {
    if (CollectionUtils.isEmpty(terminalCodeList)) {
      return Lists.newLinkedList();
    }
    String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), terminalCodeList);
    List<TerminalRelaOrg> records = cache.getIfPresent(cacheKey);
    if (records == null) {
      records = terminalRelaOrgRepository.findByTerminalCodes(terminalCodeList);
      cache.put(cacheKey, records);
    }
    return records;
  }

  @Override
  @Transactional
  public void saveBatch(List<TerminalRelaOrg> list, String terminalCode) {
    Validate.notBlank(terminalCode, "终端编码信息不能为空");
    terminalRelaOrgRepository.deleteByTerminalCodes(Lists.newArrayList(terminalCode));
    if (CollectionUtils.isEmpty(list)) {
      return;
    }

    Optional<TerminalRelaOrg> first =
        list.stream()
            .filter(
                a ->
                    StringUtils.isBlank(a.getTerminalCode())
                        || StringUtils.isBlank(a.getOrgCode())
                        || !terminalCode.equals(a.getTerminalCode()))
            .findFirst();
    Validate.isTrue(!first.isPresent(), "终端编码或组织编码不能为空,且必须属于同一终端");
    Map<String, List<TerminalRelaOrg>> map =
        list.stream().collect(Collectors.groupingBy(TerminalRelaOrg::getOrgCode));
    map.entrySet().forEach(a -> Validate.isTrue(a.getValue().size() <= 1, "存在重复的组织信息"));


    List<String> orgCodes = list.stream().map(TerminalRelaOrg::getOrgCode)
            .distinct().collect(Collectors.toList());
    Map<String, OrgTreeByTypeVo> orgTreeByTypeVoMap = orgVoService.findAllParentTreeByOrgCodes(orgCodes);

    for (TerminalRelaOrg terminalRelaOrg : list) {
      terminalRelaOrg.setId(null);
      terminalRelaOrg.setTenantCode(TenantUtils.getTenantCode());

      OrgTreeByTypeVo orgTreeByTypeVo = orgTreeByTypeVoMap.get(terminalRelaOrg.getOrgCode());
      if (Objects.nonNull(orgTreeByTypeVo)) {
        terminalRelaOrg.setZeroOrgCode(orgTreeByTypeVo.getZeroOrgCode());
        terminalRelaOrg.setZeroOrgName(orgTreeByTypeVo.getZeroOrgName());
        terminalRelaOrg.setDivisionOrgCode(orgTreeByTypeVo.getDivisionOrgCode());
        terminalRelaOrg.setDivisionOrgName(orgTreeByTypeVo.getDivisionOrgName());
        terminalRelaOrg.setRegionOrgCode(orgTreeByTypeVo.getRegionOrgCode());
        terminalRelaOrg.setRegionOrgName(orgTreeByTypeVo.getRegionOrgName());
        terminalRelaOrg.setAreaOrgCode(orgTreeByTypeVo.getAreaOrgCode());
        terminalRelaOrg.setAreaOrgName(orgTreeByTypeVo.getAreaOrgName());
        terminalRelaOrg.setOrgName(orgTreeByTypeVo.getRelaOrgName());
      }
    }
    terminalRelaOrgRepository.saveBatch(list);
  }

  @Override
  @Transactional
  public void rebindOrg(TerminalRebindOrgDto dto) {
    Validate.notNull(dto, "参数不能为空");
    Validate.notBlank(dto.getNewOrgCode(), "更换后的组织信息不能为空");
    Validate.isTrue(CollectionUtils.isNotEmpty(dto.getTerminalCodeList()), "终端信息不能为空");
    List<String> deleteOrgCodeList = Lists.newArrayList(dto.getNewOrgCode());
    if (StringUtils.isNotBlank(dto.getOldOrgCode())) {
      deleteOrgCodeList.add(dto.getOldOrgCode());
    }
    terminalRelaOrgRepository.deleteByOrgCodesAndTerminalCodes(
        deleteOrgCodeList, dto.getTerminalCodeList());

    // 查询组织层级
    OrgTreeByTypeVo orgTreeByTypeVo =
            orgVoService.findAllParentTreeByOrgCode(dto.getNewOrgCode());

    List<TerminalRelaOrg> list = Lists.newLinkedList();
    Set<String> set = Sets.newLinkedHashSet(dto.getTerminalCodeList());
    for (String terminalCode : set) {
      TerminalRelaOrg cur = new TerminalRelaOrg();
      cur.setId(null);
      cur.setTenantCode(TenantUtils.getTenantCode());
      cur.setTerminalCode(terminalCode);
      cur.setOrgCode(dto.getNewOrgCode());

      if (Objects.nonNull(orgTreeByTypeVo)) {
        cur.setZeroOrgCode(orgTreeByTypeVo.getZeroOrgCode());
        cur.setZeroOrgName(orgTreeByTypeVo.getZeroOrgName());
        cur.setDivisionOrgCode(orgTreeByTypeVo.getDivisionOrgCode());
        cur.setDivisionOrgName(orgTreeByTypeVo.getDivisionOrgName());
        cur.setRegionOrgCode(orgTreeByTypeVo.getRegionOrgCode());
        cur.setRegionOrgName(orgTreeByTypeVo.getRegionOrgName());
        cur.setAreaOrgCode(orgTreeByTypeVo.getAreaOrgCode());
        cur.setAreaOrgName(orgTreeByTypeVo.getAreaOrgName());
        cur.setOrgName(orgTreeByTypeVo.getRelaOrgName());
      }
      list.add(cur);
    }
    terminalRelaOrgRepository.saveBatch(list);

    final List<Terminal> terminalList =
        this.terminalRepository.findByTerminalCodes(dto.getTerminalCodeList());
    if (CollectionUtils.isEmpty(terminalList)) {
      return;
    }
    List<TerminalVo> voList =
        (List<TerminalVo>)
            this.nebulaToolkitService.copyCollectionByBlankList(
                terminalList, Terminal.class, TerminalVo.class, HashSet.class, ArrayList.class);
    for (TerminalVo item : voList) {
      TerminalRelateOrgEventDto eventDto = new TerminalRelateOrgEventDto();
      eventDto.setOldOrgCode(dto.getOldOrgCode());
      eventDto.setNewOrgCode(dto.getNewOrgCode());
      eventDto.setTerminalVo(item);
      SerializableBiConsumer<TerminalRelateOrgEventListener, TerminalRelateOrgEventDto> onRebind =
          TerminalRelateOrgEventListener::onRebind;
      this.nebulaNetEventClient.publish(eventDto, TerminalRelateOrgEventListener.class, onRebind);
    }
  }

  @Override
  public List<TerminalRelaOrg> findByOrgCodes(List<String> orgCodes) {
    if (CollectionUtils.isEmpty(orgCodes)) {
      return Lists.newLinkedList();
    }
    String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), orgCodes);
    List<TerminalRelaOrg> records = cache.getIfPresent(cacheKey);
    if (records == null) {
      records = this.terminalRelaOrgRepository.findByOrgCodes(orgCodes);
      cache.put(cacheKey, records);
    }
    return records;
  }

  @Override
  public void deleteByTerminalCodes(List<String> terminalCodes) {
    if (CollectionUtils.isEmpty(terminalCodes)) {
      return;
    }
    this.terminalRelaOrgRepository.deleteByTerminalCodes(terminalCodes);
  }

  @Override
  @Transactional
  public void create(String orgCode, String terminalCode, String tenantCode) {
    Validate.notBlank(terminalCode, "终端编码信息不能为空");
    terminalRelaOrgRepository.deleteByTerminalCodes(Lists.newArrayList(terminalCode));
    if (StringUtils.isBlank(orgCode)) {
      return;
    }
    OrgTreeByTypeVo orgTreeByTypeVo =
            orgVoService.findAllParentTreeByOrgCode(orgCode);
    TerminalRelaOrg terminalRelaOrg = new TerminalRelaOrg();
    terminalRelaOrg.setTerminalCode(terminalCode);
    terminalRelaOrg.setOrgCode(orgCode);
    terminalRelaOrg.setTenantCode(tenantCode);
    if (Objects.nonNull(orgTreeByTypeVo)) {
      terminalRelaOrg.setZeroOrgCode(orgTreeByTypeVo.getZeroOrgCode());
      terminalRelaOrg.setZeroOrgName(orgTreeByTypeVo.getZeroOrgName());
      terminalRelaOrg.setDivisionOrgCode(orgTreeByTypeVo.getDivisionOrgCode());
      terminalRelaOrg.setDivisionOrgName(orgTreeByTypeVo.getDivisionOrgName());
      terminalRelaOrg.setRegionOrgCode(orgTreeByTypeVo.getRegionOrgCode());
      terminalRelaOrg.setRegionOrgName(orgTreeByTypeVo.getRegionOrgName());
      terminalRelaOrg.setAreaOrgCode(orgTreeByTypeVo.getAreaOrgCode());
      terminalRelaOrg.setAreaOrgName(orgTreeByTypeVo.getAreaOrgName());
      terminalRelaOrg.setOrgName(orgTreeByTypeVo.getRelaOrgName());
    }
    this.terminalRelaOrgRepository.save(terminalRelaOrg);
  }
}
