package com.biz.crm.mdm.business.terminal.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaCus;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TerminalRelaCusMapper extends BaseMapper<TerminalRelaCus> {

    List<String> findUserNameRelaCus(@Param("userName") String userNme,@Param("tenantCode")String tenantCode);

    List<TerminalRelaCus> findByTerminalCustomers(@Param("customerCodes") List<String> customerCodes, @Param("customerName") String customerName);
}
