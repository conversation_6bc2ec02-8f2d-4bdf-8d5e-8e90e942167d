package com.biz.crm.mdm.business.terminal.local.service.strategy;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.terminal.local.entity.Terminal;
import com.biz.crm.mdm.business.terminal.local.repository.TerminalRepository;
import com.biz.crm.mdm.business.terminal.sdk.strategy.CompletedCountStrategy;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 按照月类型统计数据
 *
 * <AUTHOR>
 * @date 2022/9/7
 */
@Component
public class MonthCompletedCountStrategy implements CompletedCountStrategy {

  @Autowired(required = false)
  private TerminalRepository terminalRepository;
  @Autowired(required = false)
  private CustomerVoService customerVoService;

  @Override
  public String dateType() {
    return "month";
  }

  @Override
  public Map<String, Integer> findCountByCreateAccountAndFromType(String createAccount, String fromType) {
    Date date = new Date();
    DateTime dateTime = DateUtil.beginOfMonth(date);
    String startDate = DateFormatUtils.format(dateTime, "yyyy-MM-dd HH:mm:ss");
    String endDate = DateFormatUtils.format(date, "yyyy-MM-dd HH:mm:ss");
    List<Terminal> terminals = this.terminalRepository.findCountByCreateAccountAndFromTypeAndCreateTimeScope(createAccount, fromType, startDate, endDate);
    Integer terminal = org.springframework.util.CollectionUtils.isEmpty(terminals) ? 0 : terminals.size();
    List<CustomerVo> customerVos = this.customerVoService.findCountByCreateAccountAndFromTypeAndCreateTimeScope(createAccount, fromType, startDate, endDate);
    Integer dealer = org.springframework.util.CollectionUtils.isEmpty(customerVos) ? 0 : customerVos.size();
    HashMap<String, Integer> hashMap = new HashMap<>(3);
    hashMap.put("terminal", terminal);
    hashMap.put("dealer", dealer);
    hashMap.put("total", terminal + dealer);
    return hashMap;
  }
}
