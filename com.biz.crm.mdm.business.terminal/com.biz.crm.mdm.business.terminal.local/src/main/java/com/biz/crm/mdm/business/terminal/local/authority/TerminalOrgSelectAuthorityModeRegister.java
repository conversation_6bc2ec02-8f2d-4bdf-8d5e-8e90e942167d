package com.biz.crm.mdm.business.terminal.local.authority;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaOrg;
import com.biz.crm.mdm.business.terminal.local.service.TerminalRelaOrgService;
import com.bizunited.nebula.mars.sdk.register.SelectAuthorityModeRegister;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: yangrui
 * @Date: 2025-05-27 13:44
 */
@Component
public class TerminalOrgSelectAuthorityModeRegister implements SelectAuthorityModeRegister {

    @Autowired(required = false)
    private OrgVoService orgVoService;

    @Autowired(required = false)
    private TerminalRelaOrgService terminalRelaOrgService;


    @Override
    public String modeKey() {
        return "TerminalOrgSelectAuthorityModeRegister";
    }

    @Override
    public String modeName() {
        return "按照固定组织及其所有下级组织关联的终端维度的值确认";
    }

    @Override
    public String controlKey() {
        return "TerminalOrgSelectAuthorityModeRegister_selectOrgTree";
    }

    @Override
    public int sort() {
        return 9;
    }

    @Override
    public String groupCode() {
        return "terminal_position_scope";
    }

    @Override
    public boolean isArrayValue() {
        return true;
    }

    @Override
    public boolean isStaticValue() {
        return true;
    }

    @Override
    public Class<?> modeValueClass() {
        return String.class;
    }

    @Override
    public Object staticValue(String[] staticValues) {
        if (null == staticValues || staticValues.length == 0) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        List<String> orgCodes = Lists.newArrayList(staticValues);
        List<OrgVo> children = orgVoService.findAllChildrenByOrgCodes(orgCodes);
        if (CollectionUtil.isEmpty(children)) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        List<String> childrenCodeList = children.stream().map(OrgVo::getOrgCode)
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        childrenCodeList.addAll(orgCodes);
        if (CollectionUtil.isEmpty(childrenCodeList)) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        childrenCodeList = childrenCodeList.stream().distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(childrenCodeList)) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }

        // 查询组织关联终端
        List<TerminalRelaOrg> terminalRelaOrgList = terminalRelaOrgService.findByOrgCodes(childrenCodeList);
        if (CollectionUtil.isEmpty(terminalRelaOrgList)) {
            return CommonConstant.NOT_AUTHORITY_ARR;
        }
        return terminalRelaOrgList.stream().map(TerminalRelaOrg::getTerminalCode).distinct().toArray(String[]::new);
    }

    @Override
    public Object dynamicValue(UserIdentity userIdentity, String s) {
        return CommonConstant.NOT_AUTHORITY_ARR;
    }

    @Override
    public String converterKey() {
        return "chartArrayMarsAuthorityAstConverter";
    }
}
