package com.biz.crm.mdm.business.terminal.local.register;

import com.biz.crm.mdm.business.terminal.sdk.constant.TerminalConstant;
import com.biz.crm.workflow.sdk.register.ProcessBusinessRegister;
import org.springframework.stereotype.Component;

/**
 * 终端审批功能注册信息
 *
 * <AUTHOR>
 * @date 2022/09/05
 */
@Component
public class ProcessBusinessRegisterOfTerminal implements ProcessBusinessRegister {
  @Override
  public String getBusinessCode() {
    return TerminalConstant.TERMINAL_PROCESS_NAME;
  }

  @Override
  public String getBusinessName() {
    return "终端创建时审批";
  }
}
