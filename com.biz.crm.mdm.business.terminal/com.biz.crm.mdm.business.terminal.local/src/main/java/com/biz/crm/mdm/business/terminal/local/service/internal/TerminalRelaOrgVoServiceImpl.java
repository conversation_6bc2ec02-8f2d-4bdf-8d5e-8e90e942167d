package com.biz.crm.mdm.business.terminal.local.service.internal;

import com.biz.crm.mdm.business.terminal.local.repository.TerminalRepository;
import com.biz.crm.mdm.business.terminal.sdk.service.TerminalRelaOrgVoService;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalRelaOrgVo;
import com.google.common.collect.Lists;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 终端组织关联关系sdk实现
 *
 * <AUTHOR>
 * @date 2021/11/25
 */
@Service
public class TerminalRelaOrgVoServiceImpl implements TerminalRelaOrgVoService {

  @Autowired(required = false)
  private TerminalRepository terminalRepository;

  @Override
  public List<TerminalRelaOrgVo> findByOrgCodes(List<String> orgCodeList) {
    if (CollectionUtils.isEmpty(orgCodeList)) {
      return Lists.newLinkedList();
    }
    return this.terminalRepository.findTerminalRelaOrgByOrgCodes(orgCodeList);
  }
}
