package com.biz.crm.mdm.business.terminal.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalBindSupplyDto;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalSearchDto;
import com.biz.crm.mdm.business.terminal.sdk.service.TerminalSupplyVoService;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalSupplyVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 供货关系
 *
 * <AUTHOR>
 * @date 2021/11/17
 */
@Slf4j
@Api(tags = "终端管理: TerminalSupplyVo: 供货关系")
@RestController
@RequestMapping(value = {"/v1/terminal/terminalSupply"})
public class TerminalSupplyVoController {

  @Autowired(required = false)
  private TerminalSupplyVoService terminalSupplyVoService;

  /**
   * 根据终端编码集合获取匹配信息
   *
   * @param codes
   * @return
   */
  @ApiOperation(value = "根据终端编码集合获取匹配信息")
  @GetMapping(value = {"/findByTerminalCodes"})
  public Result<List<TerminalSupplyVo>> findByTerminalCodes(
      @RequestParam("codes") List<String> codes) {
    try {
      List<TerminalSupplyVo> list = this.terminalSupplyVoService.findByTerminalCodes(codes);
      return Result.ok(list);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据经销商编码集合获取匹配信息
   *
   * @param codes
   * @return
   */
  @ApiOperation(value = "根据经销商编码集合获取匹配信息")
  @GetMapping(value = {"/findByCustomerCodes"})
  public Result<List<TerminalSupplyVo>> findByCustomerCodes(
      @RequestParam("codes") Set<String> codes) {
    try {
      List<TerminalSupplyVo> list = this.terminalSupplyVoService.findByCustomerCodes(codes);
      return Result.ok(list);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 获取终端指定物料的供货经销商编码集合
   *
   * @param terminalCode
   * @param materialCode
   * @returnø
   */
  @ApiOperation(value = "获取终端指定物料的供货经销商编码集合")
  @GetMapping(value = {"/findTerminalSupplyCustomerCodeSet"})
  public Result<Set<String>> findTerminalSupplyCustomerCodeSet(
      @RequestParam("terminalCode") String terminalCode,
      @RequestParam("materialCode") String materialCode) {
    try {
      return Result.ok(
          this.terminalSupplyVoService.findTerminalSupplyCustomerCodeSet(
              terminalCode, materialCode));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据职位获取匹配信息
   *
   * @param code
   * @return
   */
  @ApiOperation(value = "根据职位获取匹配信息")
  @GetMapping(value = {"/findByPositionCode"})
  public Result<List<TerminalSupplyVo>> findByPositionCode(@RequestParam("code") String code) {
    try {
      List<TerminalSupplyVo> list = this.terminalSupplyVoService.findByPositionCode(code);
      return Result.ok(list);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过职位统计数据
   *
   * @return {"total":12,"today":2}
   */
  @ApiOperation("通过职位统计数据")
  @GetMapping("findCountByPositionCode")
  public Result<?> findCountByPositionCode(@RequestParam("code") String code) {
    try {
      Map<String, Integer> map = this.terminalSupplyVoService.findCountByPositionCode(code);
      return Result.ok(map);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("根据职位/创建人/用户类型查询终端关联信息")
  @GetMapping("/findTerminalSupply")
  public Result<List<TerminalSupplyVo>> findTerminalSupplyByPostCodeAndCreateAccount(
      @RequestParam("postCode") String postCode,
      @RequestParam("userName") String userName,
      @RequestParam("terminalSupplyType") String terminalSupplyType) {
    try {
     List<TerminalSupplyVo> list = this.terminalSupplyVoService.findTerminalSupplyByPostCodeAndCreateAccount(postCode,userName,terminalSupplyType);
     return Result.ok(list);
    }catch (Exception e){
      log.error(e.getMessage(),e);
      return Result.error(e.getMessage());
    }
  }


  /**
   * 根据TerminalSearchDto里的终端编码集合获取匹配信息
   *
   * @param searchDto
   * @return
   */
  @ApiOperation(value = "根据TerminalSearchDto里的终端编码集合获取匹配信息")
  @PostMapping(value = {"/findByTerminalSearchDto"})
  public Result<List<TerminalSupplyVo>> findByTerminalSearchDto(
          @RequestBody TerminalSearchDto searchDto) {
    try {
      List<TerminalSupplyVo> list = this.terminalSupplyVoService.findByTerminalSearchDto(searchDto);
      return Result.ok(list);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation("根据经销商编码及是否分利过滤经销商")
  @GetMapping("/filterByCustomerCodesAndFlag")
  Result<Set<String>> filterByCustomerCodesAndFlag(
      @RequestParam("customerCodes") Set<String> customerCodes,
      @RequestParam("shareBenefits") boolean shareBenefits) {
    try {
      Set<String> customerCodeSet =
          this.terminalSupplyVoService.filterByCustomerCodesAndFlag(customerCodes, shareBenefits);
      return Result.ok(customerCodeSet);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
