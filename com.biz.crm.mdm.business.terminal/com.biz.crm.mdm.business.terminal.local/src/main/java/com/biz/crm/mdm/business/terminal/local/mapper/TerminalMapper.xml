<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.terminal.local.mapper.TerminalMapper">

    <!--分页查询-->
    <select id="findByConditions"
            resultType="com.biz.crm.mdm.business.terminal.local.entity.Terminal">
        select tt.* from(
        select a.*
        ,
        <choose>
            <when test="dto.longitude != null and dto.latitude != null">
                IF(a.longitude is null or a.latitude is null, ***********,
                ROUND( st_distance_sphere ( point ( a.longitude, a.latitude ),
                point ( #{dto.longitude}, #{dto.latitude} )) / 1000, 2 )) as distance
            </when>
            <otherwise>
                *********** as distance
            </otherwise>
        </choose>
        from mdm_terminal a
        <if test="dto.orgCode!=null and dto.orgCode!=''">
            left join mdm_terminal_r_org b on a.terminal_code=b.terminal_code and a.tenant_code=b.tenant_code
        </if>
        <if test="dto.customerOrgCode!=null and dto.customerOrgCode!=''">
            left join mdm_terminal_r_customer_org c on a.terminal_code=c.terminal_code and a.tenant_code=c.tenant_code
        </if>
        where 1=1
        <if test="dto.tenantCode != null and dto.tenantCode != ''">
            and a.tenant_code=#{dto.tenantCode}
        </if>
        <if test="dto.orgCode != null and dto.orgCode != ''">
            and b.org_code=#{dto.orgCode}
        </if>
        <if test="dto.customerOrgCode != null and dto.customerOrgCode != ''">
            and c.org_code=#{dto.customerOrgCode}
        </if>
        <if test="dto.keyword != null and dto.keyword != ''">
            <bind name="likeKeyword" value="'%' + dto.keyword + '%'"/>
            and (a.terminal_code like #{likeKeyword} or a.terminal_name like #{likeKeyword})
        </if>
        <if test="dto.terminalCode != null and dto.terminalCode != ''">
            <bind name="likeTerminalCode" value="'%' + dto.terminalCode + '%'"/>
            and a.terminal_code like #{likeTerminalCode}
        </if>
        <if test="dto.terminalName != null and dto.terminalName != ''">
            <bind name="likeTerminalName" value="'%' + dto.terminalName + '%'"/>
            and a.terminal_name like #{likeTerminalName}
        </if>
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and a.enable_status=#{dto.enableStatus}
        </if>
        <if test="dto.delFlag != null and dto.delFlag != ''">
            and a.del_flag=#{dto.delFlag}
        </if>
        <if test="dto.terminalType != null and dto.terminalType != ''">
            and a.terminal_type = #{dto.terminalType}
        </if>
        <if test="dto.channel != null and dto.channel != ''">
            and a.channel = #{dto.channel}
        </if>
        <if test="dto.notIncludeTerminalCodeList != null and dto.notIncludeTerminalCodeList.size() != 0">
            and a.terminal_code NOT IN
            <foreach item="item" index="index" collection="dto.notIncludeTerminalCodeList" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.tagName != null and dto.tagName != ''">
            <bind name="likeTagName" value="'%' + dto.tagName + '%'"/>
            and EXISTS (select 1 from mdm_terminal_tag t where t.tenant_code = a.tenant_code and t.terminal_code =
            a.terminal_code and t.tag_description like #{likeTagName})
        </if>
        <if test="dto.orgCodeList != null and dto.orgCodeList.size() != 0">
            and EXISTS (select 1 from mdm_terminal_r_org ro where ro.tenant_code = a.tenant_code and
            ro.terminal_code = a.terminal_code
            and ro.org_code IN
            <foreach item="item" index="index" collection="dto.orgCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="dto.licenseRegisterNumber != null and dto.licenseRegisterNumber != ''">
            and a.license_register_number = #{dto.licenseRegisterNumber}
        </if>
        <if test="dto.terminalAddress != null and dto.terminalAddress != ''">
            and a.terminal_address = #{dto.terminalAddress}
        </if>
        <if test="dto.shareBenefits == true">
            and a.share_benefits = #{dto.shareBenefits}
        </if>
        <if test="dto.shareBenefits == false">
            and (a.share_benefits = #{dto.shareBenefits} || a.share_benefits is null)
        </if>
        <if test="dto.sourceType != null and dto.sourceType != ''">
            and a.source_type = #{dto.sourceType}
        </if>
        <if test="dto.sourceValue != null and dto.sourceValue != ''">
            and a.source_value = #{dto.sourceValue}
        </if>
        <if test="dto.createAccount != null and dto.createAccount != ''">
            and a.create_account=#{dto.createAccount}
        </if>
        <if test="dto.customerCodeSet != null and dto.customerCodeSet.size()>0">
            and EXISTS (
            SELECT 1 from mdm_terminal_rela_cus d where a.terminal_code = d.terminal_code
            and d.cus_code in
            <foreach collection="dto.customerCodeSet" separator="," item="item" index="index" open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="dto.terminalTagSet != null and dto.terminalTagSet.size()>0">
            and exists(
            select 1 from mdm_terminal_tag tag where a.terminal_code = tag.terminal_code
            and tag.del_flag = '${@<EMAIL>()}'
            and tag.tag_type in
            <foreach collection="dto.terminalTagSet" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="dto.terminalCodeSet != null and dto.terminalCodeSet.size()>0">
            and a.terminal_code in
            <foreach collection="dto.terminalCodeSet" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ) tt
        where 1=1
        <if test="dto.distance != null">
            and tt.distance <![CDATA[ <= ]]> #{dto.distance}
        </if>
        order by tt.distance asc, tt.terminal_code asc, tt.id asc
    </select>

    <select id="findTerminalRelaOrgByOrgCodes"
            resultType="com.biz.crm.mdm.business.terminal.sdk.vo.TerminalRelaOrgVo">
        select
        a.terminal_code,b.org_code
        from
        mdm_terminal a
        left join mdm_terminal_r_org b on b.terminal_code=a.terminal_code and b.tenant_code=a.tenant_code
        where a.tenant_code=#{tenantCode}
        and b.org_code in(<foreach collection="list" item="item" separator=",">#{item}</foreach>)
        order by a.create_time desc
    </select>

    <select id="findByCustomerOrgCodes" resultType="com.biz.crm.mdm.business.terminal.local.entity.Terminal">
        select a.terminal_code,b.org_code,b.org_name
        from mdm_terminal a
        left join mdm_terminal_r_customer_org b on b.terminal_code=a.terminal_code and b.tenant_code=a.tenant_code
        where a.tenant_code=#{tenantCode}
        and a.del_flag=#{delFlag}
        <if test="list != null and list.size >0">
            and b.org_code in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by a.create_time desc
    </select>

    <select id="findByTerminalCodeSearchDto" resultType="java.lang.String">
        SELECT terminal_code
        FROM mdm_terminal
        WHERE 1=1 and tenant_code = #{dto.tenantCode}
        <if test="dto.delFlag!=null and dto.delFlag!=''">
            AND del_flag=#{dto.delFlag}
        </if>
        <if test="dto.enableStatus!=null and dto.enableStatus!=''">
            AND enable_status=#{dto.enableStatus}
        </if>
        <if test="dto.channelSet!=null and dto.channelSet.size>0">
            AND channel in(<foreach collection="dto.channelSet" separator="," item="item">#{item}</foreach>)
        </if>
        <if test="dto.orgSet!=null and dto.orgSet.size>0">
            AND EXISTS(
            SELECT 1 FROM mdm_terminal_r_org
            WHERE mdm_terminal_r_org.terminal_code=mdm_terminal.terminal_code
            AND mdm_terminal_r_org.org_code in (<foreach collection="dto.orgSet" item="item" separator=",">
            #{item}</foreach>))
        </if>
        <if test="dto.tagSet!=null and dto.tagSet.size>0">
            AND EXISTS(
            SELECT 1 FROM mdm_terminal_tag
            WHERE mdm_terminal_tag.terminal_code=mdm_terminal.terminal_code
            AND mdm_terminal_tag.tag_description in (<foreach collection="dto.tagSet" item="item" separator=",">
            #{item}</foreach>))
        </if>
    </select>

    <select id="findByTerminalQueryDto" resultType="java.lang.String">
        select distinct a.terminal_code from mdm_terminal a
        <if test="dto.orgCodeSet!=null and dto.orgCodeSet.size>0">
            left join mdm_terminal_r_org b on b.tenant_code=a.tenant_code and b.terminal_code=a.terminal_code
        </if>
        where a.tenant_code = #{tenantCode}
        <if test="dto.delFlag!=null and dto.delFlag!=''">
            and a.del_flag=#{dto.delFlag}
        </if>
        <if test="dto.enableStatus!=null and dto.enableStatus!=''">
            and a.enable_status=#{dto.enableStatus}
        </if>
        <if test="dto.channel!=null and dto.channel!=''">
            and a.channel=#{dto.channel}
        </if>
        <if test="dto.terminalCode!=null and dto.terminalCode!=''">
            <bind name="terminalCodeLike" value="dto.terminalCode +'%'"/>
            and a.terminal_code like #{terminalCodeLike}
        </if>
        <if test="dto.terminalName!=null and dto.terminalName!=''">
            <bind name="terminalNameLike" value="'%'+ dto.terminalName +'%'"/>
            and a.terminal_name like #{terminalNameLike}
        </if>
        <if test="dto.terminalCodeSet!=null and dto.terminalCodeSet.size>0">
            and a.terminal_code in(<foreach collection="dto.terminalCodeSet" item="item" separator=",">
            #{item}</foreach>)
        </if>
        <if test="dto.orgCodeSet!=null and dto.orgCodeSet.size>0">
            and b.org_code in(<foreach collection="dto.orgCodeSet" item="item" separator=",">
            #{item}</foreach>)
        </if>
    </select>
    <select id="findByTerminalSearchDto" resultType="com.biz.crm.mdm.business.terminal.local.entity.Terminal">
        SELECT
        *
        FROM
        mdm_terminal
        WHERE
        tenant_code = #{dto.tenantCode}
        <if test="dto.delFlag!=null and dto.delFlag!=''">
            AND del_flag=#{dto.delFlag}
        </if>
        <if test="dto.enableStatus!=null and dto.enableStatus!=''">
            AND enable_status=#{dto.enableStatus}
        </if>
        <if test="dto.shareBenefits != null">
            AND share_benefits = #{dto.shareBenefits}
        </if>
        <if test="dto.channelSet!=null and dto.channelSet.size>0">
            AND channel in(<foreach collection="dto.channelSet" separator="," item="item">#{item}</foreach>)
        </if>
        <if test="dto.orgCodeSet!=null and dto.orgCodeSet.size>0">
            AND EXISTS(
            SELECT 1 FROM mdm_terminal_r_org
            WHERE mdm_terminal_r_org.terminal_code=mdm_terminal.terminal_code
            AND mdm_terminal_r_org.org_code in (<foreach collection="dto.orgCodeSet" item="item" separator=",">
            #{item}</foreach>))
        </if>
        <if test="dto.tagSet!=null and dto.tagSet.size>0">
            AND EXISTS(
            SELECT 1 FROM mdm_terminal_tag
            WHERE mdm_terminal_tag.terminal_code=mdm_terminal.terminal_code
            AND mdm_terminal_tag.tag_description in (<foreach collection="dto.tagSet" item="item" separator=",">
            #{item}</foreach>))
        </if>
        <if test="dto.positionCodeSet!=null and dto.positionCodeSet.size>0">
            AND create_post_code in(<foreach collection="dto.positionCodeSet" separator="," item="item">
            #{item}</foreach>)
        </if>
        <if test="dto.sourceType!=null and dto.sourceType!=''">
            AND source_type= #{dto.sourceType}
        </if>
        <if test="dto.yearMonth!=null and dto.yearMonth!=''">
            <bind name="yearMonth" value="dto.yearMonth + '%'"/>
            AND create_time like #{yearMonth}
        </if>
    </select>

    <select id="findByLngAndLat"
            resultType="com.biz.crm.mdm.business.terminal.local.entity.Terminal">
        SELECT
        *
        FROM
        mdm_terminal a
        <where>
            AND a.tenant_code = #{tenantCode}
            AND a.del_flag = '${@<EMAIL>()}'
            AND a.enable_status = '${@<EMAIL>()}'
            <if test="dto.cityCode!=null and dto.cityCode!=''">
                AND a.city_code = #{dto.cityCode}
            </if>
            <if test="dto.cityName!=null and dto.cityName!=''">
                AND a.CITY_NAME = #{dto.cityName}
            </if>
            <if test="dto.terminalAddress != null and dto.terminalAddress != ''">
                <bind name="terminalAddressLike" value="'%' + dto.terminalAddress + '%'"/>
                AND a.terminal_address like #{terminalAddressLike}
            </if>
            AND a.longitude BETWEEN #{dto.minLng} AND #{dto.maxLng}
            AND a.latitude BETWEEN #{dto.minLat} AND #{dto.maxLat}
            <!--
            AND a.longitude <![CDATA[>]]> #{dto.minLng}
            AND a.longitude <![CDATA[<]]> #{dto.maxLng}
            AND a.latitude <![CDATA[>]]> #{dto.minLat}
            AND a.latitude <![CDATA[<]]> #{dto.maxLat}
            -->
        </where>
    </select>


    <select id="findByUserNameAndPostCode"
            resultType="com.biz.crm.mdm.business.terminal.local.entity.Terminal">
        select a.*
        from mdm_terminal a
        <where>
            a.enable_status = '${@<EMAIL>()}'
            and a.del_flag = '${@<EMAIL>()}'
            <if test="tenantCode != null  and  tenantCode != ''">
                and a.tenant_code = #{tenantCode}
            </if>
            and exists(
            select 1
            from mdm_terminal_rela_business d
            where a.terminal_code = d.terminal_code
            and a.tenant_code = d.tenant_code
            and d.position_code in (
            select position_code
            from mdm_user_position c
            <where>
                <if test="userName != null and userName != ''">
                    and user_name = #{userName}
                </if>
                <if test="postCode != null and postCode != ''">
                    and position_code = #{postCode}
                </if>
            </where>
            )
            )
        </where>
    </select>

    <select id="findByTerminalInfoDto" resultType="com.biz.crm.mdm.business.terminal.local.entity.Terminal">
        SELECT
        *
        FROM
        mdm_terminal
        WHERE
        tenant_code = #{dto.tenantCode}
        <if test="dto.delFlag!=null and dto.delFlag!=''">
            AND del_flag=#{dto.delFlag}
        </if>
        <if test="dto.enableStatus!=null and dto.enableStatus!=''">
            AND enable_status=#{dto.enableStatus}
        </if>
        <if test="dto.shareBenefits != null">
            AND share_benefits = #{dto.shareBenefits}
        </if>
        <if test="dto.terminalCodeSet!=null and dto.terminalCodeSet.size>0">
            and terminal_code in(<foreach collection="dto.terminalCodeSet" item="item" separator=",">
            #{item}</foreach>)
        </if>
        <if test="dto.channelSet!=null and dto.channelSet.size>0">
            AND channel in(<foreach collection="dto.channelSet" separator="," item="item">#{item}</foreach>)
        </if>
        <if test="dto.orgCodeSet!=null and dto.orgCodeSet.size>0">
            AND EXISTS(
            SELECT 1 FROM mdm_terminal_r_org
            WHERE mdm_terminal_r_org.terminal_code=mdm_terminal.terminal_code
            AND mdm_terminal_r_org.org_code in (<foreach collection="dto.orgCodeSet" item="item" separator=",">
            #{item}</foreach>))
        </if>
        <if test="dto.tagSet!=null and dto.tagSet.size>0">
            AND EXISTS(
            SELECT 1 FROM mdm_terminal_tag
            WHERE mdm_terminal_tag.terminal_code=mdm_terminal.terminal_code
            AND mdm_terminal_tag.tag_description in (<foreach collection="dto.tagSet" item="item" separator=",">
            #{item}</foreach>))
        </if>
    </select>

    <select id="findTerminalAndRelaCusByTerminalCodeList"
            resultType="com.biz.crm.mdm.business.terminal.sdk.vo.TerminalVo">
        select * from mdm_terminal
        <where>
            tenant_code = #{tenantCode}
            and del_flag = '${@<EMAIL>()}'
            and enable_status = '${@<EMAIL>()}'
            and terminal_code in
            <foreach collection="terminalNameList" separator="," item="item" index="index" open="(" close=")">
                #{item}
            </foreach>
        </where>
    </select>

<!--    <select id="findTerminalNumByOrgCode" resultType="com.biz.crm.mdm.business.terminal.sdk.vo.TerminalVo">-->
<!--        SELECT a.terminal_code,-->
<!--               b.terminal_name,-->
<!--               b.channel-->
<!--        FROM mdm_terminal_rela_business a-->
<!--                 left join mdm_org_position op on a.position_code = op.position_code-->
<!--                 LEFT JOIN mdm_terminal b ON a.terminal_code = b.terminal_code-->
<!--        WHERE b.del_flag = '${@<EMAIL>()}'-->
<!--          AND b.enable_status = '${@<EMAIL>()}'-->
<!--          AND op.org_code IN (SELECT org_code-->
<!--                              FROM mdm_org-->
<!--                              WHERE rule_code LIKE-->
<!--                                    concat((SELECT rule_code FROM mdm_org WHERE org_code = #{orgCode}), '%'))-->
<!--    </select> -->
    <select id="findTerminalNumByOrgCode" resultType="com.biz.crm.mdm.business.terminal.sdk.vo.TerminalVo">
        SELECT
            b.terminal_code,
            b.terminal_name,
            b.channel,
            c.tag_type,
            c.tag_description
        FROM
            mdm_terminal_r_org a
                LEFT JOIN mdm_terminal b ON a.terminal_code = b.terminal_code
                left join mdm_terminal_tag c on b.terminal_code = c.terminal_code
        WHERE
            b.del_flag = '${@<EMAIL>()}'
          AND b.enable_status = '${@<EMAIL>()}'
          AND a.org_code IN (
            SELECT
                mo.org_code
            FROM
                mdm_org mo
            WHERE
                mo.del_flag = '${@<EMAIL>()}'
              AND mo.rule_code LIKE concat(( SELECT rule_code FROM mdm_org WHERE org_code = #{orgCode} ), '%' ))
    </select>

    <select id="findTerminalByConditions" resultType="com.biz.crm.mdm.business.terminal.sdk.vo.TerminalVo">
        select t.*
        from (
        select
        mt.*,
        IF(mt.longitude is null or mt.latitude is null, ***********, ROUND
        (st_distance_sphere(point(mt.longitude, mt.latitude), point(#{dto.longitude}, #{dto.latitude})) / 1000, 2)) as
        distance,
        mo.org_code
        from mdm_terminal mt
        left join mdm_terminal_r_org tro on tro.tenant_code = mt.tenant_code and tro.terminal_code = mt.terminal_code
        left join mdm_org mo on mo.tenant_code = tro.tenant_code and mo.org_code = tro.org_code
        where mt.del_flag = '${@<EMAIL>()}'
        <if test="dto.positionCodeSet != null and dto.positionCodeSet.size() > 0">
            and mt.terminal_code in
            (
            select mtrb.terminal_code from mdm_terminal_rela_business mtrb
            where mtrb.position_code in
            <foreach collection="dto.positionCodeSet" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="dto.terminalName != null and dto.terminalName != ''">
            <bind name="terminalNamelike" value="'%' + dto.terminalName + '%'"/>
            and mt.terminal_name like #{terminalNamelike}
        </if>
        <if test="dto.terminalKeyWord != null and dto.terminalKeyWord != ''">
            <bind name="terminalKeyWord" value="'%' + dto.terminalKeyWord + '%'"/>
            and
            (
            mt.terminal_name like #{terminalKeyWord}
            or
            mt.terminal_code like #{terminalKeyWord}
            )

        </if>
        <if test="dto.relaCusName != null and dto.relaCusName != ''">
            <bind name="relaCusNameLike" value="'%' + dto.relaCusName + '%'"/>
            AND EXISTS (
            SELECT
            1
            FROM
            mdm_terminal_rela_cus mc
            WHERE
            mc.cus_name like #{relaCusNameLike}
            AND mc.terminal_code = mt.terminal_code
            )
        </if>
        <if test="dto.relaBusinessName != null and dto.relaBusinessName != ''">
            <bind name="relaBusinessNameLike" value="'%' + dto.relaBusinessName + '%'"/>
            AND EXISTS (
            SELECT
            1
            FROM
            mdm_terminal_rela_business mb
            WHERE
            mb.full_name like #{relaBusinessNameLike}
            AND mb.terminal_code = mt.terminal_code
            )
        </if>
        <if test="dto.sourceSystem != null and dto.sourceSystem != ''">
            <bind name="sourceSystemLike" value="'%' + dto.sourceSystem + '%'"/>
            and mt.source_system like #{sourceSystemLike}
        </if>
        <if test="dto.channel != null and dto.channel != ''">
            and mt.channel = #{dto.channel}
        </if>
        <!--        <if test="dto.feeInvestment != null and dto.feeInvestment != ''">-->
        <!--            and mt.fee_investment = #{dto.feeInvestment}-->
        <!--        </if>-->
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and mt.enable_status = #{dto.enableStatus}
        </if>
        <if test="dto.orgRuleCode !=null and dto.orgRuleCode != '' ">
            <bind name="orgRuleCodeLike" value="dto.orgRuleCode + '%'"/>
            and tro.org_code in (select mo.org_code from mdm_org mo
            where mo.del_flag = '${@<EMAIL>()}'
            and mo.tenant_code = #{dto.tenantCode}
            and mo.rule_code like #{orgRuleCodeLike}
            )
        </if>
        <if test="dto.orgCodes !=null and dto.orgCodes.size > 0">
            and tro.org_code in
            <foreach collection="dto.orgCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="dto.tag != null and dto.tag != ''">
            and mt.terminal_code in (
            select mtt.terminal_code from mdm_terminal_tag mtt
            where mtt.tag_type = #{dto.tag}
            )
        </if>
        ) t order by
        <if test="dto.selectedCodes != null and dto.selectedCodes.size > 0">
            CASE
            <foreach collection="dto.selectedCodes" item="item" index="index">
                WHEN t.terminal_code = #{item} THEN ${index}
            </foreach>
            ELSE 999 END asc,
        </if>
        t.distance asc, t.terminal_code asc
    </select>

    <select id="findTerminalByConditionsForMini" resultType="com.biz.crm.mdm.business.terminal.sdk.vo.TerminalVo">
        select t.*
        from (
        select
        mt.*,
        IF(mt.longitude is null or mt.latitude is null, ***********, ROUND
        (st_distance_sphere(point(mt.longitude, mt.latitude), point(#{dto.longitude}, #{dto.latitude})) / 1000, 2)) as
        distance,
        mo.org_code,
        mo.org_name
        from mdm_terminal mt
        left join mdm_terminal_r_org tro on tro.tenant_code = mt.tenant_code and tro.terminal_code = mt.terminal_code
        left join mdm_org mo on mo.tenant_code = tro.tenant_code and mo.org_code = tro.org_code
        where mt.del_flag = '${@<EMAIL>()}'
        <if test="dto.positionCodeSet != null and dto.positionCodeSet.size() > 0">
            and mt.terminal_code in
            (
            select mtrb.terminal_code from mdm_terminal_rela_business mtrb
            where mtrb.position_code in
            <foreach collection="dto.positionCodeSet" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="dto.terminalName != null and dto.terminalName != ''">
            <bind name="terminalNamelike" value="'%' + dto.terminalName + '%'"/>
            and mt.terminal_name like #{terminalNamelike}
        </if>
        <if test="dto.relaCusName != null and dto.relaCusName != ''">
            <bind name="relaCusNameLike" value="'%' + dto.relaCusName + '%'"/>
            AND EXISTS (
            SELECT
            1
            FROM
            mdm_terminal_rela_cus mc
            WHERE
            mc.cus_name like #{relaCusNameLike}
            AND mc.terminal_code = mt.terminal_code
            )
        </if>
        <if test="dto.relaBusinessName != null and dto.relaBusinessName != ''">
            <bind name="relaBusinessNameLike" value="'%' + dto.relaBusinessName + '%'"/>
            AND EXISTS (
            SELECT
            1
            FROM
            mdm_terminal_rela_business mb
            WHERE
            mb.full_name like #{relaBusinessNameLike}
            AND mb.terminal_code = mt.terminal_code
            )
        </if>
        <if test="dto.sourceSystem != null and dto.sourceSystem != ''">
            <bind name="sourceSystemLike" value="'%' + dto.sourceSystem + '%'"/>
            and mt.source_system like #{sourceSystemLike}
        </if>
        <if test="dto.costDateType != null and dto.costDateType != ''">
            AND EXISTS (
            SELECT
            1
            FROM
            mdm_terminal_cost_record_log mcr
            WHERE
            mcr.years = #{dto.costYear}
            AND mcr.${dto.selectField} = 'Y'
            AND mcr.terminal_code = mt.terminal_code
            )
        </if>
        <if test="dto.channel != null and dto.channel != ''">
            and mt.channel = #{dto.channel}
        </if>
<!--        <if test="dto.feeInvestment != null and dto.feeInvestment != ''">-->
<!--            and mt.fee_investment = #{dto.feeInvestment}-->
<!--        </if>-->
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and mt.enable_status = #{dto.enableStatus}
        </if>
        <if test="dto.orgRuleCode !=null and dto.orgRuleCode != '' ">
            <bind name="orgRuleCodeLike" value="dto.orgRuleCode + '%'"/>
            and tro.org_code in (select mo.org_code from mdm_org mo
            where mo.del_flag = '${@<EMAIL>()}'
            and mo.tenant_code = #{dto.tenantCode}
            and mo.rule_code like #{orgRuleCodeLike}
            )
        </if>
        <if test="dto.orgCodes !=null and dto.orgCodes.size > 0">
            and tro.org_code in
            <foreach collection="dto.orgCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="dto.tag != null and dto.tag != ''">
            and mt.terminal_code in (
            select mtt.terminal_code from mdm_terminal_tag mtt
            where mtt.tag_type = #{dto.tag}
            )
        </if>
        ) t order by
        <if test="dto.selectedCodes != null and dto.selectedCodes.size > 0">
            CASE
            <foreach collection="dto.selectedCodes" item="item" index="index">
                WHEN t.terminal_code = #{item} THEN ${index}
            </foreach>
            ELSE 999 END asc,
        </if>
        t.distance asc, t.terminal_code asc
    </select>

    <select id="findByNearbyClientTerminal"
            resultType="com.biz.crm.mdm.business.terminal.sdk.vo.TerminalNearbyClientVo">
        select
        t.*
        from (
        select
        tt.terminal_name as client_name,
        tt.terminal_code as client_code,
        tt.terminal_code,
        tt.enable_status as enable_status,
        tt.terminal_type as client_subtype,
        tt.shop_image_path as client_photo,
        tt.terminal_address as client_address,
        tt.create_name,
        tt.create_account,
        tt.create_time,
        tt.longitude,
        tt.latitude,
        'terminal' as clientType,
        IF(tt.longitude is null or tt.latitude is null, ***********,
        ROUND( st_distance_sphere ( point ( tt.longitude, tt.latitude ),
        point ( #{dto.longitude}, #{dto.latitude} )) / 1000, 2 )) as distance
        ,mo.org_code
        ,mo.org_name
        from mdm_terminal tt
        left join mdm_terminal_r_org tro on tro.tenant_code = tt.tenant_code and tro.terminal_code = tt.terminal_code
        left join mdm_org mo on mo.tenant_code = tro.tenant_code and mo.org_code = tro.org_code
        where 1=1
        and tt.del_flag = '${@<EMAIL>()}'
        <if test="dto.clientName != null and dto.clientName != ''">
            <bind name="clientName" value="'%' + dto.clientName + '%'"/>
            and tt.terminal_name like #{clientName}
        </if>
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and tt.enable_status = #{dto.enableStatus}
        </if>
        <if test="dto.channel != null and dto.channel != ''">
            and tt.channel = #{dto.channel}
        </if>
        <if test="dto.positionCodeSet != null and dto.positionCodeSet.size() > 0">
            and tt.terminal_code in
            (
            select mtrb.terminal_code from mdm_terminal_rela_business mtrb
            where mtrb.position_code in
            <foreach collection="dto.positionCodeSet" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="dto.includeCodeList != null and dto.includeCodeList.size()>0">
            and tt.terminal_code in
            <foreach collection="dto.includeCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.notIncludeCodeList != null and dto.notIncludeCodeList.size()>0">
            and tt.terminal_code not in
            <foreach collection="dto.notIncludeCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) t
        order by
        <if test="dto.selectedCodes != null and dto.selectedCodes.size > 0">
            CASE
            <foreach collection="dto.selectedCodes" item="item" index="index">
                WHEN t.client_code = #{item} THEN ${index}
            </foreach>
            ELSE 999 END asc,
        </if>
        t.distance asc, t.client_code asc

    </select>
    <select id="findByNearbyClientCustomer"
            resultType="com.biz.crm.mdm.business.terminal.sdk.vo.TerminalNearbyClientVo">
        select
        t.*
        from (
        select
        tt.customer_name as client_name,
        tt.customer_code as client_code,
        tt.customer_code,
        tt.enable_status as enable_status,
        tt.customer_type as client_subtype,
        '' as client_photo,
        tt.work_address as client_address,
        tt.create_name,
        tt.create_account,
        tt.create_time,
        tt.longitude,
        tt.latitude,
        tt.customer_tag,
        'dealer' as clientType,
        IF(tt.longitude is null or tt.latitude is null, ***********,
        ROUND( st_distance_sphere ( point ( tt.longitude, tt.latitude ),
        point ( #{dto.longitude}, #{dto.latitude} )) / 1000, 2 )) as distance
        ,mo.org_code
        ,mo.org_name
        from
        mdm_customer tt
        left join mdm_customer_r_org rc on tt.customer_code = rc.customer_code and tt.tenant_code = rc.tenant_code
        left join mdm_org mo on mo.org_code = rc.org_code and mo.tenant_code = rc.tenant_code
        where 1=1
        and tt.del_flag = '${@<EMAIL>()}'
        <if test="dto.clientName != null and dto.clientName != ''">
            <bind name="clientName" value="'%' + dto.clientName + '%'"/>
            and tt.customer_name like #{clientName}
        </if>
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and tt.enable_status = #{dto.enableStatus}
        </if>
        <if test="dto.channel != null and dto.channel != ''">
            and tt.channel_code = #{dto.channel}
        </if>
        <if test="dto.contractCustomer != null and dto.contractCustomer != ''">
            and tt.contract_customer = #{dto.contractCustomer}
        </if>
        <if test="dto.positionCodeSet != null and dto.positionCodeSet.size()>0">
            and tt.customer_code in
            (
            select mcd.customer_code from mdm_customer_docking mcd
            where mcd.position_code in
            <foreach collection="dto.positionCodeSet" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="dto.includeCodeList != null and dto.includeCodeList.size()>0">
            and tt.customer_code in
            <foreach collection="dto.includeCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.notIncludeCodeList != null and dto.notIncludeCodeList.size()>0">
            and tt.customer_code not in
            <foreach collection="dto.notIncludeCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) t
        order by
        <if test="dto.selectedCodes != null and dto.selectedCodes.size > 0">
            CASE
            <foreach collection="dto.selectedCodes" item="item" index="index">
                WHEN t.client_code = #{item} THEN ${index}
            </foreach>
            ELSE 999 END asc,
        </if>
        t.distance asc, t.client_code asc

    </select>

    <select id="findTerminalCodesByCondition" resultType="java.lang.String">
        select tt.terminal_code from(
        select a.*
        ,
        <choose>
            <when test="dto.longitude != null and dto.latitude != null">
                IF(a.longitude is null or a.latitude is null, ***********,
                ROUND( st_distance_sphere ( point ( a.longitude, a.latitude ),
                point ( #{dto.longitude}, #{dto.latitude} )) / 1000, 2 )) as distance
            </when>
            <otherwise>
                *********** as distance
            </otherwise>
        </choose>
        from mdm_terminal a
        <if test="dto.orgCode!=null and dto.orgCode!=''">
            left join mdm_terminal_r_org b on a.terminal_code=b.terminal_code and a.tenant_code=b.tenant_code
        </if>
        <if test="dto.customerOrgCode!=null and dto.customerOrgCode!=''">
            left join mdm_terminal_r_customer_org c on a.terminal_code=c.terminal_code and a.tenant_code=c.tenant_code
        </if>
        where 1=1
        <if test="dto.tenantCode != null and dto.tenantCode != ''">
            and a.tenant_code=#{dto.tenantCode}
        </if>
        <if test="dto.orgCode != null and dto.orgCode != ''">
            and b.org_code=#{dto.orgCode}
        </if>
        <if test="dto.customerOrgCode != null and dto.customerOrgCode != ''">
            and c.org_code=#{dto.customerOrgCode}
        </if>
        <if test="dto.keyword != null and dto.keyword != ''">
            <bind name="likeKeyword" value="'%' + dto.keyword + '%'"/>
            and (a.terminal_code like #{likeKeyword} or a.terminal_name like #{likeKeyword})
        </if>
        <if test="dto.terminalCode != null and dto.terminalCode != ''">
            <bind name="likeTerminalCode" value="'%' + dto.terminalCode + '%'"/>
            and a.terminal_code like #{likeTerminalCode}
        </if>
        <if test="dto.terminalName != null and dto.terminalName != ''">
            <bind name="likeTerminalName" value="'%' + dto.terminalName + '%'"/>
            and a.terminal_name like #{likeTerminalName}
        </if>
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and a.enable_status=#{dto.enableStatus}
        </if>
        <if test="dto.delFlag != null and dto.delFlag != ''">
            and a.del_flag=#{dto.delFlag}
        </if>
        <if test="dto.terminalType != null and dto.terminalType != ''">
            and a.terminal_type = #{dto.terminalType}
        </if>
        <if test="dto.channel != null and dto.channel != ''">
            and a.channel = #{dto.channel}
        </if>
        <if test="dto.notIncludeTerminalCodeList != null and dto.notIncludeTerminalCodeList.size() != 0">
            and a.terminal_code NOT IN
            <foreach item="item" index="index" collection="dto.notIncludeTerminalCodeList" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.tagName != null and dto.tagName != ''">
            <bind name="likeTagName" value="'%' + dto.tagName + '%'"/>
            and EXISTS (select 1 from mdm_terminal_tag t where t.tenant_code = a.tenant_code and t.terminal_code =
            a.terminal_code and t.tag_description like #{likeTagName})
        </if>
        <if test="dto.orgCodeList != null and dto.orgCodeList.size() != 0">
            and EXISTS (select 1 from mdm_terminal_r_org ro where ro.tenant_code = a.tenant_code and
            ro.terminal_code = a.terminal_code
            and ro.org_code IN
            <foreach item="item" index="index" collection="dto.orgCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="dto.licenseRegisterNumber != null and dto.licenseRegisterNumber != ''">
            and a.license_register_number = #{dto.licenseRegisterNumber}
        </if>
        <if test="dto.terminalAddress != null and dto.terminalAddress != ''">
            and a.terminal_address = #{dto.terminalAddress}
        </if>
        <if test="dto.shareBenefits == true">
            and a.share_benefits = #{dto.shareBenefits}
        </if>
        <if test="dto.shareBenefits == false">
            and (a.share_benefits = #{dto.shareBenefits} || a.share_benefits is null)
        </if>
        <if test="dto.sourceType != null and dto.sourceType != ''">
            and a.source_type = #{dto.sourceType}
        </if>
        <if test="dto.sourceValue != null and dto.sourceValue != ''">
            and a.source_value = #{dto.sourceValue}
        </if>
        <if test="dto.createAccount != null and dto.createAccount != ''">
            and a.create_account=#{dto.createAccount}
        </if>
        <if test="dto.customerCodeSet != null and dto.customerCodeSet.size()>0">
            and EXISTS (
            SELECT 1 from mdm_terminal_rela_cus d where a.terminal_code = d.terminal_code
            and d.cus_code in
            <foreach collection="dto.customerCodeSet" separator="," item="item" index="index" open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="dto.terminalTagSet != null and dto.terminalTagSet.size()>0">
            and exists(
            select 1 from mdm_terminal_tag tag where a.terminal_code = tag.terminal_code
            and tag.del_flag = '${@<EMAIL>()}'
            and tag.tag_type in
            <foreach collection="dto.terminalTagSet" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="dto.terminalCodeSet != null and dto.terminalCodeSet.size()>0">
            and a.terminal_code in
            <foreach collection="dto.terminalCodeSet" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ) tt
        where 1=1
    </select>

    <select id="loadCacheTerminal" resultType="com.biz.crm.mdm.business.terminal.sdk.vo.TerminalVo">
        SELECT
            a.*,
            b.cus_code,
            b.cus_name,
            c.user_name,
            c.full_name,
            e.org_name,
            e.org_code,
            f.tag_description,
            CONCAT_WS( '-', a.source_system, a.terminal_name, NULL ) AS systemAndName
        FROM
            mdm_terminal a
                LEFT JOIN (
                SELECT
                    GROUP_CONCAT(cus_code) cus_code,
                    GROUP_CONCAT(cus_name) cus_name,
                    terminal_code
                FROM
                    mdm_terminal_rela_cus
                GROUP BY
                    terminal_code) b ON a.terminal_code = b.terminal_code
                LEFT JOIN (
                SELECT
                    GROUP_CONCAT(a.full_name) full_name,
                    GROUP_CONCAT(b.user_name) user_name,
                    a.terminal_code
                FROM
                    mdm_terminal_rela_business a
                        LEFT JOIN mdm_user_position b ON a.position_code = b.position_code
                GROUP BY
                    a.terminal_code) c ON a.terminal_code = c.terminal_code
                LEFT JOIN mdm_terminal_r_org e ON a.terminal_code = e.terminal_code
                LEFT JOIN mdm_terminal_tag f on a.terminal_code = f.terminal_code
        where a.tenant_code = #{tenantCode}
          and a.enable_status = #{enableStatus} and a.del_flag = '${@<EMAIL>()}'
    </select>

    <select id="findTerminalChannelNumByCustomerCode" resultType="com.biz.crm.mdm.business.terminal.sdk.vo.TerminalVo">
        SELECT
            channel,
            count(1) terminalChannelNum
        FROM
            mdm_terminal a
                LEFT JOIN mdm_terminal_rela_cus b ON a.terminal_code = b.terminal_code
        <where>
            a.del_flag = '${@<EMAIL>()}'
            and a.enable_status = '${@<EMAIL>()}'
            and b.cus_code = #{customerCode}
        </where>
        GROUP BY
            channel
    </select>

    <select id="findByTerminalCodesAndChannels" resultType="com.biz.crm.mdm.business.terminal.sdk.vo.TerminalVo">
        SELECT
        a.terminal_code,
        a.terminal_name,
        a.channel,
        b.org_code,
        b.org_name
        FROM
        mdm_terminal a
        LEFT JOIN mdm_terminal_r_org b ON a.terminal_code = b.terminal_code
        <where>
            a.tenant_code = #{tenantCode}
            and a.del_flag = '${@<EMAIL>()}'
            <if test="terminalCodes != null and terminalCodes.size()>0">
                and a.terminal_code in
                <foreach collection="terminalCodes" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="channelCodes != null and channelCodes.size()>0">
                and a.channel in
                <foreach collection="channelCodes" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="findToTalNum" resultType="java.lang.String">
        SELECT
            t.terminal_code
        FROM
            mdm_terminal t
                LEFT JOIN mdm_terminal_r_org tro ON tro.tenant_code = t.tenant_code
                AND tro.terminal_code = t.terminal_code
                LEFT JOIN mdm_terminal_tag tag ON tag.terminal_code = t.terminal_code
        WHERE
            t.del_flag = '009'
          AND t.enable_status = '009'
        <if test="tagCodes != null and tagCodes.size() > 0">
            and tag.tag_type in <foreach collection="tagCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        </if>
        GROUP BY
            t.terminal_code
    </select>

</mapper>