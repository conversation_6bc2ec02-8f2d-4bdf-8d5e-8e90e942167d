package com.biz.crm.mdm.business.terminal.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店供货关系明细(TerminalSupplyDetail)实体类
 *
 * <AUTHOR>
 * @since 2021-10-18 18:21:45
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("mdm_terminal_supply_detail")
@Table(
    name = "mdm_terminal_supply_detail",
    indexes = {
      @Index(name = "terminal_supply_detail_index1", columnList = "code"),
      @Index(name = "terminal_supply_detail_index2", columnList = "tenant_code"),
      @Index(name = "terminal_supply_detail_index3", columnList = "supply_id")
    })
@org.hibernate.annotations.Table(appliesTo = "mdm_terminal_supply_detail", comment = "门店供货关系明细")
@ApiModel(value = "TerminalSupplyDetail", description = "门店供货关系明细")
public class TerminalSupplyDetail extends TenantEntity {
  private static final long serialVersionUID = -3437843942394407669L;

  /** 类型1商品2产品层级 */
  @ApiModelProperty("类型1商品2产品层级")
  @TableField(value = "data_type")
  @Column(name = "data_type", length = 60, columnDefinition = "varchar(60) COMMENT '类型1商品2产品层级'")
  private String dataType;

  /** 编码 */
  @ApiModelProperty("编码")
  @TableField(value = "code")
  @Column(name = "code", length = 60, columnDefinition = "varchar(60) COMMENT '编码'")
  private String code;

  /** 描述 */
  @ApiModelProperty("描述")
  @TableField(value = "name")
  @Column(name = "name", length = 60, columnDefinition = "varchar(60) COMMENT '描述'")
  private String name;

  /** 供货关系id */
  @ApiModelProperty("供货关系id")
  @TableField(value = "supply_id")
  @Column(name = "supply_id", length = 128, columnDefinition = "varchar(128) COMMENT '供货关系id'")
  private String supplyId;
}
