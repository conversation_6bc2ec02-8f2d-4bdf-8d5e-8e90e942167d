package com.biz.crm.mdm.business.terminal.local.service;

import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaBusiness;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalBindSupplyDto;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalRelaBusinessDto;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalUnbindSupplyDto;

import java.util.List;

public interface TerminalRelaBusinessService {

    /**
     * 查询业务负责信息
     *
     * @param terminalCodes
     * @return
     */
    List<TerminalRelaBusiness> findByTerminalCodes(List<String> terminalCodes);

    /**
     * 根据业务查询负责的终端编码信息
     *
     * @param positionCodes
     * @return
     */
    List<String> getTerminalCodesByPositionCodes(List<String> positionCodes);

    /**
     * 根据业务查询负责的终端信息
     *
     * @param positionCodes
     * @return
     */
    List<TerminalRelaBusiness> findByPositionCodes(List<String> positionCodes);

    /**
     * 保存业务负责信息
     *
     * @param relaBusinessDtos
     * @param terminalCode
     */
    void saveBatch(List<TerminalRelaBusinessDto> relaBusinessDtos, String terminalCode);

    /**
     * 删除数据
     *
     * @param terminalCodes
     */
    void deleteByTerminalCodes(List<String> terminalCodes);

    /**
     * 绑定职位终端关系
     *
     * @param dto
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/5 11:19
     */
    void bind(TerminalBindSupplyDto dto);

    /**
     * 解除职位终端关系
     *
     * @param dto
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/5 11:19
     */
    void unbind(TerminalUnbindSupplyDto dto);

    /**
     * 终端业务员关联关系创建
     * @param list
     */
    void createTerminalRelaBusiness(List<TerminalRelaBusiness> list);
}
