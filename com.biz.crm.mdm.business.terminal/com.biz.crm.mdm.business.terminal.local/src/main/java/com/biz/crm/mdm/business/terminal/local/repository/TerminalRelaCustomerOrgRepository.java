package com.biz.crm.mdm.business.terminal.local.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaCustomerOrg;
import com.biz.crm.mdm.business.terminal.local.mapper.TerminalRelaCustomerOrgMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 * 终端与客户组织关联表(repository)
 *
 * <AUTHOR>
 * @since 2021-10-18 17:48:34
 */
@Component
public class TerminalRelaCustomerOrgRepository extends
    ServiceImpl<TerminalRelaCustomerOrgMapper, TerminalRelaCustomerOrg> {

  /**
   * 根据终端编码获取关系集合
   *
   * @param terminalCodeList
   * @return
   */
  public List<TerminalRelaCustomerOrg> findByTerminalCodes(List<String> terminalCodeList) {
    return this.lambdaQuery().eq(TerminalRelaCustomerOrg::getTenantCode, TenantUtils.getTenantCode())
        .in(TerminalRelaCustomerOrg::getTerminalCode, terminalCodeList).list();
  }

  public void deleteByTerminalCodes(List<String> terminalCodeList) {
    LambdaQueryWrapper<TerminalRelaCustomerOrg> lambdaQuery = Wrappers.lambdaQuery();
    lambdaQuery.eq(TerminalRelaCustomerOrg::getTenantCode, TenantUtils.getTenantCode())
        .in(TerminalRelaCustomerOrg::getTerminalCode, terminalCodeList);
    baseMapper.delete(lambdaQuery);
  }

  public void deleteByOrgCodesAndTerminalCodes(List<String> orgCodeList,
      List<String> terminalCodeList) {
    LambdaQueryWrapper<TerminalRelaCustomerOrg> lambdaQuery = Wrappers.lambdaQuery();
    lambdaQuery.eq(TerminalRelaCustomerOrg::getTenantCode, TenantUtils.getTenantCode())
        .in(TerminalRelaCustomerOrg::getOrgCode, orgCodeList)
        .in(TerminalRelaCustomerOrg::getTerminalCode, terminalCodeList);
    baseMapper.delete(lambdaQuery);
  }
}
