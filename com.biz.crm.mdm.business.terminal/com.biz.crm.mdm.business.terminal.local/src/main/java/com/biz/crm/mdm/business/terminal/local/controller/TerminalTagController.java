package com.biz.crm.mdm.business.terminal.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalTag;
import com.biz.crm.mdm.business.terminal.local.service.TerminalTagService;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 终端标签(TerminalTag)表控制层
 *
 * <AUTHOR>
 * @date 2021-11-02 13:48:03
 */
@Slf4j
@Api(tags = "终端管理: TerminalTag: 终端标签")
@RestController
@RequestMapping(value = {"/v1/terminalTag/terminalTag"})
public class TerminalTagController {

  @Autowired(required = false) private TerminalTagService terminalTagService;

  @ApiOperation(value = "通过终端编码查询标签列表")
  @GetMapping(value = {"/findByTerminalCode"})
  public Result<List<TerminalTag>> findByTerminalCode(
      @RequestParam("terminalCode") String terminalCode) {
    try {
      List<TerminalTag> list =
          this.terminalTagService.findByTerminalCodes(Sets.newHashSet(terminalCode));
      return Result.ok(list);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "创建")
  @PostMapping(value = "")
  public Result<TerminalTag> create(@RequestBody TerminalTag terminalTag) {
    try {
      TerminalTag current = this.terminalTagService.create(terminalTag);
      return Result.ok(current);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "批量创建")
  @PostMapping(value = "createBatch")
  public Result createBatch(@RequestBody List<TerminalTag>  terminalTag) {
    try {
      this.terminalTagService.createBatch(terminalTag);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  @ApiOperation(value = "逻辑删除")
  @DeleteMapping("/delete")
  public Result<?> delete(@RequestParam("ids") List<String> ids) {
    try {
      this.terminalTagService.updateDelFlagByIds(ids);
      return Result.ok("删除成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
