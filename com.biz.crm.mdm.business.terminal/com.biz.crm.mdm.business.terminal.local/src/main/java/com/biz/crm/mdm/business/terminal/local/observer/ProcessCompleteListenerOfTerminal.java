package com.biz.crm.mdm.business.terminal.local.observer;

import com.biz.crm.mdm.business.terminal.local.entity.Terminal;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaOrg;
import com.biz.crm.mdm.business.terminal.local.service.TerminalRelaOrgService;
import com.biz.crm.mdm.business.terminal.local.service.TerminalService;
import com.biz.crm.mdm.business.terminal.local.service.TerminalSupplyService;
import com.biz.crm.mdm.business.terminal.sdk.constant.TerminalConstant;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalBindSupplyDto;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalEventDto;
import com.biz.crm.mdm.business.terminal.sdk.event.TerminalEventListener;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalRelaOrgVo;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalVo;
import com.biz.crm.mdm.business.user.sdk.service.UserPositionVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserPositionVo;
import com.biz.crm.workflow.sdk.dto.ProcessStatusDto;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.biz.crm.workflow.sdk.listener.ProcessCompleteListener;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 终端审批回调类
 *
 * <AUTHOR>
 * @date 2022/08/31
 */
@Component
public class ProcessCompleteListenerOfTerminal implements ProcessCompleteListener {

  @Autowired(required = false)
  private TerminalService terminalService;

  @Autowired(required = false)
  private NebulaNetEventClient nebulaNetEventClient;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @Autowired(required = false)
  private TerminalRelaOrgService terminalRelaOrgService;
  @Autowired(required = false)
  private TerminalSupplyService terminalSupplyService;
  @Autowired(required = false)
  private UserPositionVoService userPositionVoService;

  @Override
  public String getBusinessCode() {
    return TerminalConstant.TERMINAL_PROCESS_NAME;
  }

  @Override
  @Transactional
  public void onProcessComplete(ProcessStatusDto dto) {
    if (!dto.getBusinessCode().equals(TerminalConstant.TERMINAL_PROCESS_NAME)) {
      return;
    }
    // 校验单号(查询方法缺失)
    final Terminal terminal = this.terminalService.findByProcessNumber(dto.getProcessNo());
    Validate.notNull(terminal, "终端信息不存在");
    // 校验审批状态
    Validate.isTrue(
        ProcessStatusEnum.COMMIT.getDictCode().equals(terminal.getProcessStatus()),
        "终端非审批中状态，无法进行操作！");
    // 根据回调类别处理对应业务
    if (StringUtils.equals(
        String.valueOf(dto.getProcessStatus()), ProcessStatusEnum.PASS.getDictCode())) {
      // 审批通过业务处理
      terminal.setProcessStatus(ProcessStatusEnum.PASS.getDictCode());
    } else if (StringUtils.equals(
        String.valueOf(dto.getProcessStatus()), ProcessStatusEnum.REJECT.getDictCode())) {
      // 审批驳回业务处理
      terminal.setProcessStatus(ProcessStatusEnum.REJECT.getDictCode());
    } else if (StringUtils.equals(
        String.valueOf(dto.getProcessStatus()), ProcessStatusEnum.RECOVER.getDictCode())) {
      // 流程追回业务处理
      terminal.setProcessStatus(ProcessStatusEnum.RECOVER.getDictCode());
    }
    this.terminalService.updateByProcess(terminal);
    if (terminal.getProcessStatus().equals(ProcessStatusEnum.PASS.getDictCode())) {
      final List<TerminalRelaOrg> orgList =
          this.terminalRelaOrgService.findByTerminalCodes(
              Lists.newArrayList(terminal.getTerminalCode()));

      TerminalVo vo =
          this.nebulaToolkitService.copyObjectByBlankList(
              terminal, TerminalVo.class, HashSet.class, ArrayList.class);
      if (CollectionUtils.isNotEmpty(orgList)) {
        List<TerminalRelaOrgVo> orgVoList =
            (List<TerminalRelaOrgVo>)
                this.nebulaToolkitService.copyCollectionByBlankList(
                    orgList,
                    TerminalRelaOrg.class,
                    TerminalRelaOrgVo.class,
                    HashSet.class,
                    ArrayList.class);
        vo.setOrgList(orgVoList);
      }
      TerminalEventDto eventDto = new TerminalEventDto();
      eventDto.setOriginal(null);
      eventDto.setNewest(vo);
      // 终端审批通过创建事件
      SerializableBiConsumer<TerminalEventListener, TerminalEventDto> onApproved =
          TerminalEventListener::onApproved;
      this.nebulaNetEventClient.publish(eventDto, TerminalEventListener.class, onApproved);
      //todo 产品要求思念POC，根据数据来源绑定企业导购用户，我也不知道标品要不要，暂时打个todo代表临时解决
      if (StringUtils.isNoneBlank(terminal.getSourceType(), terminal.getSourceValue())) {
        switch (terminal.getSourceType()) {
          case "user_guide":
            List<UserPositionVo> positionVos = this.userPositionVoService.findByUserName(terminal.getTenantCode(), terminal.getSourceValue());
            if (CollectionUtils.isEmpty(positionVos)) {
              break;
            }
            TerminalBindSupplyDto bindSupplyDto = new TerminalBindSupplyDto();
            bindSupplyDto.setPositionCode(positionVos.get(0).getPositionCode());
            bindSupplyDto.setTerminalCodeList(Lists.newArrayList(terminal.getTerminalCode()));
            this.terminalSupplyService.bind(bindSupplyDto);
            break;
          default:
            break;
        }
      }
    }
    // ---------------  end -------------------
  }
}
