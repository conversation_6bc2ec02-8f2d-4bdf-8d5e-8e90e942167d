package com.biz.crm.mdm.business.terminal.local.service.internal;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalTag;
import com.biz.crm.mdm.business.terminal.local.repository.TerminalTagRepository;
import com.biz.crm.mdm.business.terminal.local.service.TerminalTagService;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalTagDto;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * 终端标签(TerminalTag)表服务实现类
 *
 * <AUTHOR>
 * @date 2021-11-02 13:48:03
 */
@Service("terminalTagService")
public class TerminalTagServiceImpl implements TerminalTagService {

    @Autowired(required = false)
    private TerminalTagRepository terminalTagRepository;

    @Autowired(required = false)
    @Qualifier("nebulaToolkitService")
    private NebulaToolkitService nebulaToolkitService;

    /**
     * 基于数据库执行的数据视图执行内容缓存（最多500毫秒）
     */
    private static volatile Cache<String, List<TerminalTag>> cache = null;

    public TerminalTagServiceImpl() {
        if (cache == null) {
            synchronized (TerminalTagServiceImpl.class) {
                while (cache == null) {
                    cache = CacheBuilder.newBuilder()
                            .initialCapacity(10000)
                            .expireAfterWrite(500, TimeUnit.MILLISECONDS)
                            .maximumSize(100000)
                            .build();
                }
            }
        }
    }

    @Override
    public List<TerminalTag> findByTerminalCodes(Set<String> terminalCodeSet) {
        if (CollectionUtils.isEmpty(terminalCodeSet)) {
            return Lists.newLinkedList();
        }
        String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), terminalCodeSet);
        List<TerminalTag> graph = cache.getIfPresent(cacheKey);
        if (graph == null) {
            graph = this.terminalTagRepository.findByTerminalCodes(terminalCodeSet);
            cache.put(cacheKey, graph);
        }
        return graph;
    }

    @Override
    public void createBatch(List<TerminalTag> terminalTag) {
        Validate.notEmpty(terminalTag, "终端标签不能为空！");
        String terminalCode = null;
        for (TerminalTag tag : terminalTag) {
            this.createValidation(tag);
            tag.setTenantCode(TenantUtils.getTenantCode());
            tag.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            tag.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            terminalCode = tag.getTerminalCode();
        }
        List<TerminalTag> byTerminalCodes = this.findByTerminalCodes(Sets.newHashSet(terminalCode));
        if (CollectionUtils.isNotEmpty(byTerminalCodes)) {
            List<String> ids = byTerminalCodes.stream().filter(e -> Objects.nonNull(e.getId())).map(TerminalTag::getId).collect(Collectors.toList());
            //逻辑删除旧数据
            this.terminalTagRepository.deleteByIds(ids);
        }
        terminalTagRepository.saveBatch(terminalTag);
    }

    @Override
    public void saveBath(List<TerminalTagDto> tagVos, String terminalCode) {
        Assert.hasLength(terminalCode, "终端编码不能为空!");

        this.terminalTagRepository.deleteByTerminal(terminalCode);
        //保存标签
        if (CollectionUtils.isEmpty(tagVos)) {
            return;
        }
        List<TerminalTag> terminalTagList = (List<TerminalTag>) nebulaToolkitService.copyCollectionByWhiteList(tagVos,
                TerminalTagDto.class, TerminalTag.class, HashSet.class, ArrayList.class);
        terminalTagList.forEach(x -> x.setTerminalCode(terminalCode));
        this.createBatch(terminalTagList);


    }

    @Override
    @Transactional
    public TerminalTag create(TerminalTag terminalTag) {
        this.createValidation(terminalTag);
        terminalTag.setTenantCode(TenantUtils.getTenantCode());
        terminalTag.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        terminalTag.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        terminalTagRepository.saveOrUpdate(terminalTag);
        return terminalTag;
    }

    @Override
    @Transactional
    public void updateDelFlagByIds(List<String> ids) {
        Validate.isTrue(CollectionUtils.isNotEmpty(ids), "id集合不能为空");
        terminalTagRepository.updateDelFlagByIds(ids);
    }

    /**
     * 在创建terminalTag模型对象之前，检查对象各属性的正确性，其主键属性必须没有值
     *
     * @param terminalTag 检查对象
     */
    private void createValidation(TerminalTag terminalTag) {
        Validate.notNull(terminalTag, "进行当前操作时，信息对象必须传入!");
        terminalTag.setTenantCode(TenantUtils.getTenantCode());
        terminalTag.setId(null);
        Validate.notBlank(terminalTag.getTagDescription(), "缺失标签描述");
        Validate.notBlank(terminalTag.getTerminalCode(), "缺失终端编码");
        Validate
                .isTrue(terminalTag.getTagDescription().length() < 64, "标签描述，在进行添加时填入值超过了限定长度(64)，请检查!");
    }
}
