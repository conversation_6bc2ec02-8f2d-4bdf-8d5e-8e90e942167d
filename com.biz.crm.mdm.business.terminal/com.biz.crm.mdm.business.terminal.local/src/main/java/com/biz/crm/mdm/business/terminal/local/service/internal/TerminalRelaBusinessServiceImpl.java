package com.biz.crm.mdm.business.terminal.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.mdm.business.org.sdk.common.constant.OrgCodeConstant;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.AbstractRelationView;
import com.biz.crm.mdm.business.position.sdk.vo.PositionRelationVo;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaBusiness;
import com.biz.crm.mdm.business.terminal.local.repository.TerminalRelaBusinessRepository;
import com.biz.crm.mdm.business.terminal.local.service.TerminalRelaBusinessService;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalBindSupplyDto;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalRelaBusinessDto;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalUnbindSupplyDto;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import io.jsonwebtoken.lang.Assert;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TerminalRelaBusinessServiceImpl implements TerminalRelaBusinessService {

    @Autowired(required = false)
    private TerminalRelaBusinessRepository terminalRelaBusinessRepository;

    @Autowired(required = false)
    private PositionVoService positionVoService;

    @Autowired(required = false)
    private OrgVoService orgVoService;

    @Autowired(required = false)
    public List<TerminalRelaBusiness> findByTerminalCodes(List<String> terminalCodes) {
        if (CollectionUtil.isEmpty(terminalCodes)) {
            return Lists.newArrayList();
        }
        List<TerminalRelaBusiness> list = terminalRelaBusinessRepository.lambdaQuery()
                .in(TerminalRelaBusiness::getTerminalCode, terminalCodes).list();
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<String> positionCodes = list.stream().map(TerminalRelaBusiness::getPositionCode).distinct().collect(Collectors.toList());
        List<PositionVo> positionVoList = positionVoService.findDetailsByIdsOrCodes(null, positionCodes);
        Map<String, PositionVo> positionMap = positionVoList.stream().collect(Collectors.toMap(PositionVo::getPositionCode, x -> x, (n, o) -> n));
        for (TerminalRelaBusiness business : list) {
            PositionVo positionVo = positionMap.get(business.getPositionCode());
            if (Objects.nonNull(positionVo)) {
                business.setUserName(positionVo.getUserName());
                business.setFullName(positionVo.getFullName());
                business.setPositionCode(positionVo.getPositionCode());
                business.setPositionName(positionVo.getPositionName());
                business.setOrgCode(positionVo.getOrgCode());
                business.setOrgName(positionVo.getOrgName());
            }
        }
        return list;
    }

    @Override
    public List<String> getTerminalCodesByPositionCodes(List<String> positionCodes) {
        if (CollectionUtil.isEmpty(positionCodes)) {
            return Lists.newArrayList();
        }
        List<TerminalRelaBusiness> list = terminalRelaBusinessRepository.lambdaQuery()
                .in(TerminalRelaBusiness::getPositionCode, positionCodes).list();
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().filter(k -> StringUtil.isNotEmpty(k.getTerminalCode()))
                .map(TerminalRelaBusiness::getTerminalCode).distinct().collect(Collectors.toList());
    }

    @Override
    public List<TerminalRelaBusiness> findByPositionCodes(List<String> positionCodes) {
        if (CollectionUtil.isEmpty(positionCodes)) {
            return Lists.newArrayList();
        }
        List<TerminalRelaBusiness> list = terminalRelaBusinessRepository.lambdaQuery()
                .in(TerminalRelaBusiness::getPositionCode, positionCodes).list();
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<PositionVo> positionVoList = positionVoService.findDetailsByIdsOrCodes(null, positionCodes);
        Map<String, List<PositionRelationVo>> positionMap = positionVoList.stream().collect(Collectors.toMap(PositionVo::getPositionCode, x -> ObjectUtils.defaultIfNull(x.getRelationData(), Lists.newArrayList())));
        for (TerminalRelaBusiness business : list) {
            if (positionMap.containsKey(business.getPositionCode())) {
                List<PositionRelationVo> relationVoList = positionMap.get(business.getPositionCode());
                List<String> orgCodes = Lists.newArrayList();
                for (PositionRelationVo relationVo : relationVoList) {
                    if (OrgCodeConstant.KEY.equals(relationVo.getRelationKey())) {
                        orgCodes.addAll(relationVo.getRelationData().stream().map(AbstractRelationView::getCode).collect(Collectors.toList()));
                    }
                }
                List<OrgVo> orgVoList = orgVoService.findByOrgCodes(orgCodes);
                String orgCodeStr = orgVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOrgCode())).map(OrgVo::getOrgCode).collect(Collectors.joining(","));
                String orgNameStr = orgVoList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOrgName())).map(OrgVo::getOrgName).collect(Collectors.joining(","));
                business.setOrgName(orgNameStr);
                business.setOrgCode(orgCodeStr);
            }
        }
        return list;
    }

    /**
     * 保存业务负责信息 此处必须是校验了职位信息的
     *
     * @param relaBusinessDtos
     * @param terminalCode
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<TerminalRelaBusinessDto> relaBusinessDtos, String terminalCode) {
        terminalRelaBusinessRepository.deleteByTerminalCodes(Lists.newArrayList(terminalCode));
        if (CollectionUtil.isEmpty(relaBusinessDtos)) {
            return;
        }
        List<TerminalRelaBusiness> relaBusinesses = relaBusinessDtos.stream().map(x -> {
            TerminalRelaBusiness business = new TerminalRelaBusiness();
            business.setTenantCode(TenantUtils.getTenantCode());
            business.setPositionCode(x.getPositionCode());
            business.setPositionName(x.getPositionName());
            business.setTerminalCode(terminalCode);
            business.setFullName(x.getFullName());
            business.setUnionName(x.getUnionName());
            return business;
        }).collect(Collectors.toList());
        terminalRelaBusinessRepository.saveBatch(relaBusinesses);
    }

    /**
     * 删除数据
     *
     * @param terminalCodes
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByTerminalCodes(List<String> terminalCodes) {
        if (CollectionUtils.isNotEmpty(terminalCodes)) {
            terminalRelaBusinessRepository.deleteByTerminalCodes(terminalCodes);
        }
    }

    /**
     * 绑定职位终端关系
     *
     * @param dto
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/5 11:19
     */
    @Override
    public void bind(TerminalBindSupplyDto dto) {
        Assert.notNull(dto, "对象不能为空!");
        Assert.hasLength(dto.getPositionCode(), "职位编码不能为空!");
        Assert.notEmpty(dto.getTerminalCodeList(), "终端编码集合不能为空!");
        PositionVo positionVo = positionVoService.findByPositionCode(dto.getPositionCode());
        Validate.notNull(positionVo, "职位信息查询为空");
        List<TerminalRelaBusiness> businesses = Lists.newArrayList();
        for (String s : dto.getTerminalCodeList()) {
            TerminalRelaBusiness business = new TerminalRelaBusiness();
            business.setTerminalCode(s);
            business.setPositionCode(positionVo.getPositionCode());
            business.setPositionName(positionVo.getPositionName());
            businesses.add(business);
        }
        terminalRelaBusinessRepository.saveBatch(businesses);
    }

    /**
     * 解除职位终端关系
     *
     * @param dto
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/5 11:19
     */
    @Override
    public void unbind(TerminalUnbindSupplyDto dto) {
        Assert.notNull(dto, "对象不能为空!");
        Assert.hasLength(dto.getPositionCode(), "职位编码不能为空!");
        Assert.notEmpty(dto.getTerminalCodeList(), "终端编码集合不能为空!");
        terminalRelaBusinessRepository.deleteByPositionCodeAndTerminalCodes(dto.getPositionCode(), dto.getTerminalCodeList());
    }

    @Override
    @Transactional
    public void createTerminalRelaBusiness(List<TerminalRelaBusiness> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //终端编码集合
        List<String> terminalCodeList = list.stream().map(TerminalRelaBusiness::getTerminalCode).distinct().collect(Collectors.toList());
        List<TerminalRelaBusiness> existList = this.terminalRelaBusinessRepository.findListByTerminalCodes(terminalCodeList);
        Map<String, TerminalRelaBusiness> existMap = existList.stream().collect(Collectors.toMap(item -> item.getTerminalCode() + "_" + item.getPositionCode(), o -> o, (v1, v2) -> v1));
        List<String> existName = new ArrayList<>();
        for (TerminalRelaBusiness item : list) {
            if (existMap.containsKey(item.getTerminalCode() + "_" + item.getPositionCode())) {
                existName.add(item.getPositionName());
            }
        }
        Validate.isTrue(CollectionUtil.isEmpty(existName), "重复关联业务员" + existName);
        this.terminalRelaBusinessRepository.saveBatch(list);
    }

}
