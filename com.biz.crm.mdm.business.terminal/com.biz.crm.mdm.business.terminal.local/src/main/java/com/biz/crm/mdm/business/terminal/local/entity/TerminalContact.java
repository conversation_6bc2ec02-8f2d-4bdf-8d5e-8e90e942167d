package com.biz.crm.mdm.business.terminal.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 终端联系人信息(TerminalContact)实体类
 *
 * <AUTHOR>
 * @since 2021-10-18 18:20:41
 */
@Data
@Entity
@EqualsAndHashCode(callSuper = false)
@TableName("mdm_terminal_contact")
@Table(
    name = "mdm_terminal_contact",
    indexes = {
      @Index(name = "mdm_terminal_contact_index1", columnList = "tenant_code"),
      @Index(name = "mdm_terminal_contact_index2", columnList = "terminal_code")
    })
@org.hibernate.annotations.Table(appliesTo = "mdm_terminal_contact", comment = "终端联系人信息")
@ApiModel(value = "TerminalContact", description = "终端联系人信息")
public class TerminalContact extends TenantEntity {
  private static final long serialVersionUID = 438728535074328051L;

  /** 终端编码 */
  @ApiModelProperty("终端编码")
  @TableField(value = "terminal_code")
  @Column(name = "terminal_code", length = 64, columnDefinition = "varchar(64) COMMENT '终端编码'")
  private String terminalCode;

  /** 联系人姓名 */
  @ApiModelProperty("联系人姓名")
  @TableField(value = "contact_name")
  @Column(name = "contact_name", length = 64, columnDefinition = "varchar(64) COMMENT '联系人姓名'")
  private String contactName;

  /** 联系人电话 */
  @ApiModelProperty("联系人电话")
  @TableField(value = "contact_phone")
  @Column(name = "contact_phone", length = 64, columnDefinition = "varchar(64) COMMENT '联系人电话'")
  private String contactPhone;

  /** 主联系人,1是0否 */
  @ApiModelProperty("主联系人,true是false否")
  @TableField(value = "contact_main")
  @Column(name = "contact_main", columnDefinition = "int COMMENT '主联系人,1是0否'")
  private Boolean contactMain;
}
