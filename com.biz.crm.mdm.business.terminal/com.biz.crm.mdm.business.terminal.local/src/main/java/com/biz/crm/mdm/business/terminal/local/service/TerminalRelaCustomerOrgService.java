package com.biz.crm.mdm.business.terminal.local.service;

import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaCustomerOrg;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalRebindCustomerOrgDto;
import java.util.List;

/**
 * 终端与客户组织关联
 *
 * <AUTHOR>
 * @since 2021-10-18 17:48:34
 */
public interface TerminalRelaCustomerOrgService {

  /**
   * 根据终端编码集合获取匹配信息
   *
   * @param terminalCodeList
   * @return
   */
  List<TerminalRelaCustomerOrg> findByTerminalCodes(List<String> terminalCodeList);

  /**
   * 批量保存
   *
   * @param list
   * @param terminalCode
   */
  void saveBatch(List<TerminalRelaCustomerOrg> list, String terminalCode);

  /**
   * 更换终端客户组织
   *
   * @param dto
   */
  void rebindOrg(TerminalRebindCustomerOrgDto dto);

  /**
   * 根据终端编码集合删除信息
   *
   * @param terminalCodes
   */
  void deleteByTerminalCodes(List<String> terminalCodes);

  /**
   * 新增客户组织信息
   *
   * @param customerOrgCode
   * @param terminalCode
   * @param tenantCode
   */
  void create(String customerOrgCode, String terminalCode, String tenantCode);
}
