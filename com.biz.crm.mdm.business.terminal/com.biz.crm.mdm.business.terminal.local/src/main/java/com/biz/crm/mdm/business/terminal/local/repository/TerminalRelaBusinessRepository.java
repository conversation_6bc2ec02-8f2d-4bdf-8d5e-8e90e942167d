package com.biz.crm.mdm.business.terminal.local.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaBusiness;
import com.biz.crm.mdm.business.terminal.local.mapper.TerminalRelaBusinessMapper;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TerminalRelaBusinessRepository extends ServiceImpl<TerminalRelaBusinessMapper, TerminalRelaBusiness> {

    public void deleteByTerminalCodes(List<String> terminalCodes) {
        this.remove(Wrappers.lambdaQuery(TerminalRelaBusiness.class)
                .in(TerminalRelaBusiness::getTerminalCode, terminalCodes));
    }

    public void deleteByPositionCodeAndTerminalCodes(String positionCode, List<String> terminalCodeList) {
        if (StringUtil.isEmpty(positionCode)
                || CollectionUtil.isEmpty(terminalCodeList)) {
            return;
        }
        this.lambdaUpdate()
                .eq(TerminalRelaBusiness::getPositionCode, positionCode)
                .in(TerminalRelaBusiness::getTerminalCode, terminalCodeList)
                .remove();
    }

    /**
     * 根据终端编码集合查询关联信息
     * @param terminalCodeList
     * @return
     */
    public List<TerminalRelaBusiness> findListByTerminalCodes(List<String> terminalCodeList) {
        if (CollectionUtil.isEmpty(terminalCodeList)){
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(TerminalRelaBusiness::getTerminalCode,terminalCodeList)
                .list();
    }

    /**
     * 根据职位编码查询关联信息
     * @param positionCodes
     * @return
     */
    public List<TerminalRelaBusiness> findListByPositionCodes(List<String> positionCodes){
        if (CollectionUtil.isEmpty(positionCodes)){
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(TerminalRelaBusiness::getPositionCode,positionCodes)
                .list();
    }

    public void deleteByPositionCodes(List<String> userPositions) {
        this.remove(Wrappers.lambdaQuery(TerminalRelaBusiness.class)
                .in(TerminalRelaBusiness::getPositionCode, userPositions));
    }
}
