package com.biz.crm.mdm.business.terminal.feign.feign;

import com.biz.crm.mdm.business.terminal.feign.feign.internal.TerminalRelaBusinessVoServiceFeignImpl;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalRelaBusinessVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import com.biz.crm.business.common.sdk.model.Result;

import java.util.List;

/**
 * @Author: yangrui
 * @Date: 2024-12-26 18:37
 */
@FeignClient(
        name = "${mdm.feign-client.name:crm-mdm}",
        path = "crm-mdm",
        fallbackFactory = TerminalRelaBusinessVoServiceFeignImpl.class)
public interface TerminalRelaBusinessVoServiceFeign {

    @GetMapping("/v1/terminal/terminalRelaBusiness/findByTerminalCodes")
    Result<List<TerminalRelaBusinessVo>> findByTerminalCodes(
            @RequestParam("terminalCodeList") List<String> terminalCodeList);

    @PostMapping("/v1/terminal/terminalRelaBusiness/deleteByPositionCode")
    Result<Boolean> deleteByPositionCode(@RequestBody List<String> userPositions);
}
