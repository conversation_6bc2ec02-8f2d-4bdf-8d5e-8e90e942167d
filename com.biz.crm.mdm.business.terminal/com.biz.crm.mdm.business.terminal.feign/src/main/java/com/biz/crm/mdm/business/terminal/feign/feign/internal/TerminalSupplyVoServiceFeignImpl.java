package com.biz.crm.mdm.business.terminal.feign.feign.internal;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.terminal.feign.feign.TerminalSupplyVoServiceFeign;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalSearchDto;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalSupplyVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 终端供货关系feign熔断实现
 *
 * <AUTHOR>
 * @date 2021/11/16
 */
@Component
public class TerminalSupplyVoServiceFeignImpl
    implements FallbackFactory<TerminalSupplyVoServiceFeign> {

  @Override
  public TerminalSupplyVoServiceFeign create(Throwable throwable) {
    return new TerminalSupplyVoServiceFeign() {
      @Override
      public Result<List<TerminalSupplyVo>> findByTerminalCodes(List<String> codes) {
        throw new UnsupportedOperationException("根据终端编码集合获取匹配信息熔断");
      }

      @Override
      public Result<List<TerminalSupplyVo>> findByCustomerCodes(Set<String> codes) {
        throw new UnsupportedOperationException("根据经销商编码集合获取匹配信息熔断");
      }

      @Override
      public Result<Set<String>> findTerminalSupplyCustomerCodeSet(
          String terminalCode, String materialCode) {
        throw new UnsupportedOperationException("获取终端指定物料的供货经销商编码集合ø熔断");
      }

      /**
       * 根据职位获取匹配信息
       *
       * @param code
       * @return
       */
      @Override
      public Result<List<TerminalSupplyVo>> findByPositionCode(String code) {
        throw new UnsupportedOperationException("根据职位编码获取数据集合ø熔断");
      }

      @Override
      public Result<List<TerminalSupplyVo>> findTerminalSupplyByPostCodeAndCreateAccount(
          String postCode, String userName, String terminalSupplyType) {
        throw new UnsupportedOperationException("根据职位编码创建用户终端关系类型查询终端关系信息开启熔断");
      }

      @Override
      public Result<List<TerminalSupplyVo>> findByTerminalSearchDto(TerminalSearchDto searchDto) {
        throw new UnsupportedOperationException("根据TerminalSearchDto里的终端编码集合获取匹配信息熔断");
      }

      @Override
      public Result<Set<String>> filterByCustomerCodesAndFlag(Set<String> customerCodes,
          boolean shareBenefits) {
        throw new UnsupportedOperationException("根据经销商编码及是否分利过滤经销商进入熔断！");
      }
    };
  }
}
