package com.biz.crm.mdm.business.terminal.feign.service.internal;

import com.biz.crm.mdm.business.terminal.feign.feign.TerminalRelaOrgVoServiceFeign;
import com.biz.crm.mdm.business.terminal.sdk.service.TerminalRelaOrgVoService;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalRelaOrgVo;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 终端组织关联关系sdk实现
 *
 * <AUTHOR>
 * @date 2021/11/25
 */
@Service
public class TerminalRelaOrgVoServiceImpl implements TerminalRelaOrgVoService {

  @Autowired(required = false) private TerminalRelaOrgVoServiceFeign terminalRelaOrgVoServiceFeign;

  @Override
  public List<TerminalRelaOrgVo> findByOrgCodes(List<String> orgCodeList) {
    orgCodeList = Optional.ofNullable(orgCodeList).orElse(Lists.newLinkedList());
    return this.terminalRelaOrgVoServiceFeign.findByOrgCodes(orgCodeList).checkFeignResult();
  }
}
