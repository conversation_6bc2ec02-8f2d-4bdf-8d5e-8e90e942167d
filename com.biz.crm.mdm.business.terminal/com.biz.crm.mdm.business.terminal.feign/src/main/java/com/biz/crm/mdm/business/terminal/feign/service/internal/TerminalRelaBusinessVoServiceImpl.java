package com.biz.crm.mdm.business.terminal.feign.service.internal;

import com.biz.crm.mdm.business.terminal.feign.feign.TerminalContactVoServiceFeign;
import com.biz.crm.mdm.business.terminal.feign.feign.TerminalRelaBusinessVoServiceFeign;
import com.biz.crm.mdm.business.terminal.sdk.service.TerminalRelaBusinessVoService;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalRelaBusinessVo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: yangrui
 * @Date: 2024-12-26 18:35
 */
@Service
public class TerminalRelaBusinessVoServiceImpl implements TerminalRelaBusinessVoService {

    @Autowired(required = false)
    private TerminalRelaBusinessVoServiceFeign terminalRelaBusinessVoServiceFeign;

    @Override
    public List<TerminalRelaBusinessVo> findByTerminalCodes(List<String> terminalCodes) {
        if (CollectionUtils.isEmpty(terminalCodes)) {
            return new ArrayList<>();
        }
        return terminalRelaBusinessVoServiceFeign.findByTerminalCodes(terminalCodes).checkFeignResult();
    }

    @Override
    public Boolean deleteByPositionCode(List<String> userPositions) {
        if (CollectionUtils.isEmpty(userPositions)) {
            return false;
        }
        return terminalRelaBusinessVoServiceFeign.deleteByPositionCode(userPositions).checkFeignResult();
    }
}
