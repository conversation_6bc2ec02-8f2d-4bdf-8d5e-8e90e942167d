package com.biz.crm.mdm.business.terminal.feign.feign.internal;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.terminal.feign.feign.TerminalRelaOrgVoServiceFeign;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalRelaOrgVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 终端组织关系feign熔断实现
 *
 * <AUTHOR>
 * @date 2021/11/16
 */
@Component
public class TerminalRelaOrgVoServiceFeignImpl
    implements FallbackFactory<TerminalRelaOrgVoServiceFeign> {

  @Override
  public TerminalRelaOrgVoServiceFeign create(Throwable throwable) {
    return new TerminalRelaOrgVoServiceFeign() {
      @Override
      public Result<List<TerminalRelaOrgVo>> findByOrgCodes(List<String> orgCodeList) {
        throw new UnsupportedOperationException("根据企业组织编码获取匹配的终端信息熔断");
      }
    };
  }
}
