package com.biz.crm.mdm.business.terminal.channel.sdk.vo;

import com.biz.crm.business.common.local.entity.TenantFlagTreeOpEntity;
import com.biz.crm.business.common.sdk.vo.TenantTreeFlagOpVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年11月11日 15:53:00
 */
@Data
public class MdmTerminalChannelVo extends TenantTreeFlagOpVo {
  /**
   * 终端渠道编码
   */
  @ApiModelProperty(value = "终端渠道编码")
  private String terminalChannelCode;
  /**
   * 终端渠道名称
   */
  @ApiModelProperty(value = "终端渠道名称")
  private String terminalChannelName;
  /**
   * 上级渠道编码
   */
  @ApiModelProperty(value = "上级渠道编码")
  private String parentChannelCode;

  /**
   * 上级渠道名称
   */
  @ApiModelProperty(value = "上级渠道名称")
  private String parentChannelName;
}
