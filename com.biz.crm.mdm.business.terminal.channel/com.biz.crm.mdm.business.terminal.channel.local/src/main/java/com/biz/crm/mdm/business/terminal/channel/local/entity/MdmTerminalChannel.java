package com.biz.crm.mdm.business.terminal.channel.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagTreeOpEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * <AUTHOR>
 * @describe: 客户渠道实体
 * @createTime 2022年10月31日 15:20:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mdm_terminal_channel")
@Entity
@Table(name = "mdm_terminal_channel", indexes = {
    @Index(columnList = "terminal_channel_code,tenant_code", unique = true)
    , @Index(columnList = "tenant_code")})
@org.hibernate.annotations.Table(appliesTo = "mdm_terminal_channel", comment = "终端渠道主表")
public class MdmTerminalChannel extends TenantFlagTreeOpEntity {


  private static final long serialVersionUID = -6387089221376145615L;
  /**
   * 终端渠道编码
   */
  @Column(name = "terminal_channel_code", nullable = false, unique = true, length = 32, columnDefinition = "VARCHAR(32) COMMENT '终端渠道编码'")
  @ApiModelProperty(value = "终端渠道编码")
  private String terminalChannelCode;
  /**
   * 终端渠道名称
   */
  @Column(name = "terminal_channel_name",  length = 64, columnDefinition = "VARCHAR(64) COMMENT '终端渠道名称'")
  @ApiModelProperty(value = "终端渠道名称")
  private String terminalChannelName;
  /**
   * 上级渠道编码
   */
  @Column(name = "parent_channel_code",  length = 32, columnDefinition = "VARCHAR(32) COMMENT '上级渠道编码'")
  @ApiModelProperty(value = "上级渠道编码")
  private String parentChannelCode;

  /**
   * 父节点
   */
  @ApiModelProperty("父节点")
  @TableField(exist = false)
  @Transient
  private MdmTerminalChannel parent;

  /**
   * 子节点
   */
  @ApiModelProperty("子节点")
  @TableField(exist = false)
  @Transient
  private List<MdmTerminalChannel> children;
}
