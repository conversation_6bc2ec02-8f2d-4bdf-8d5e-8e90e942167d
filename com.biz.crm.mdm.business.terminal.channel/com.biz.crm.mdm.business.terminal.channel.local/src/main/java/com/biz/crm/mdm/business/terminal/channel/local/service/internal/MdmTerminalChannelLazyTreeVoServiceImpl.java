package com.biz.crm.mdm.business.terminal.channel.local.service.internal;

import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.service.TreeRuleCodeStrategy;
import com.biz.crm.business.common.sdk.service.TreeRuleCodeStrategyHolder;
import com.biz.crm.business.common.sdk.utils.TreeUtil;
import com.biz.crm.business.common.sdk.vo.LazyTreeVo;
import com.biz.crm.mdm.business.terminal.channel.local.entity.MdmTerminalChannel;
import com.biz.crm.mdm.business.terminal.channel.local.repository.MdmTerminalChannelRepository;
import com.biz.crm.mdm.business.terminal.channel.sdk.service.MdmTerminalChannelLazyTreeVoService;
import com.biz.crm.mdm.business.terminal.channel.sdk.contant.TerminalChannelConstant;
import com.biz.crm.mdm.business.terminal.channel.sdk.dto.TerminalChannelDto;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年11月09日 11:08:00
 */
@Service
public class MdmTerminalChannelLazyTreeVoServiceImpl implements MdmTerminalChannelLazyTreeVoService {

  @Autowired(required = false)
  private TreeRuleCodeStrategyHolder treeRuleCodeStrategyHolder;

  @Autowired
  private MdmTerminalChannelRepository mdmTerminalChannelRepository;

  /**
   * 根据条件查询树形
   * @param dto
   * @return
   */
  @Override
  public List<LazyTreeVo> findByTreeDto(TerminalChannelDto dto) {
    List<LazyTreeVo> tree=new ArrayList<>();
    if (!StringUtils.isEmpty(dto.getParentCode())) {
      //查询parentCode下一级 查询该编码下一级
      List<LazyTreeVo> lazyTreeVos = this.mdmTerminalChannelRepository.findOrgLazyTreeList(dto.getEnableStatus(), null, dto.getParentCode(), null, null, null, TenantUtils.getTenantCode());
      tree.addAll(lazyTreeVos);
    }
    else if (StringUtils.isNotBlank(dto.getName())){
      List<MdmTerminalChannel> likeList = this.mdmTerminalChannelRepository.findOrgCodeAndRuleCodeByEnableStatusOptAndOrgNameLike(dto.getEnableStatus(), dto.getName(), TenantUtils.getTenantCode());
      if (!CollectionUtils.isEmpty(likeList)) {
        TreeRuleCodeStrategy strategy = this.treeRuleCodeStrategyHolder.getStrategy(null);
        Set<String> parentRuleCodes=strategy.findParentRuleCodeByRuleCodes(TerminalChannelConstant.RULE_CODE_LENGTH, likeList.stream().map(MdmTerminalChannel::getRuleCode).collect(Collectors.toList()));
        tree.addAll(this.mdmTerminalChannelRepository.findOrgLazyTreeList(dto.getEnableStatus(), null, null, null, new ArrayList<>(parentRuleCodes), null, TenantUtils.getTenantCode()));
      }
    }
    else{
      //查询第一层  启用状态，传true只查第一层
      tree.addAll(this.mdmTerminalChannelRepository.findOrgLazyTreeList(dto.getEnableStatus(), true, null, null, null, null, TenantUtils.getTenantCode()));
    }
    if (!CollectionUtils.isEmpty(tree)) {
      tree.forEach(item -> item.setHasChild(BooleanEnum.TRUE.getNumStr().equalsIgnoreCase(String.valueOf(item.getHasChildFlag()))));
      return TreeUtil.generateLazyTreeByParentCode(tree);
    }
    return new ArrayList<>(0);
  }
}
