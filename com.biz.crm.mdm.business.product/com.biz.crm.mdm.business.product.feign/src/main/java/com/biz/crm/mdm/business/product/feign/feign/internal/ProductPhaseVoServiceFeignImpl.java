package com.biz.crm.mdm.business.product.feign.feign.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.product.feign.feign.ProductPhaseVoServiceFeign;
import com.biz.crm.mdm.business.product.sdk.dto.ProductPhaseDto;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 商品品相feign熔断实现
 *
 * <AUTHOR>
 * @date 2021/11/16
 */
@Component
public class ProductPhaseVoServiceFeignImpl implements FallbackFactory<ProductPhaseVoServiceFeign> {

    @Override
    public ProductPhaseVoServiceFeign create(Throwable throwable) {
        return new ProductPhaseVoServiceFeign() {


            @Override
            public Result<Map<String, String>> findNameByCodes(Set<String> codes) {
                throw new UnsupportedOperationException("根据商品品相编码集合获取名称熔断");
            }

            @Override
            public Result<Page<ProductPhaseVo>> findByConditions(Integer page, Integer size, ProductPhaseDto dto) {
                throw new UnsupportedOperationException("根据分页信息获取商品品相信息熔断");
            }

            @Override
            public Result<ProductPhaseVo> findByIdOrCode(String id, String code) {
                throw new UnsupportedOperationException("根据商品品相id或编码获取商品品相信息熔断");
            }

            @Override
            public Result<List<ProductPhaseVo>> findByIds(Set<String> ids) {
                throw new UnsupportedOperationException("根据商品品相id集合获取商品品相信息熔断");
            }

            @Override
            public Result<List<ProductPhaseVo>> findByCodes(Set<String> codes) {
                throw new UnsupportedOperationException("根据商品品相编码集合获取商品品相信息熔断");
            }

            @Override
            public Result<Map<String, List<ProductPhaseVo>>> findListByPhaseCodes(List<String> codes) {
                throw new UnsupportedOperationException("根据品项编码查询信息进入熔断");
            }

            @Override
            public Result<List<ProductPhaseVo>> loadCacheProductPhase() {
                throw new UnsupportedOperationException("品项载入缓存进入熔断");
            }
        };
    }
}
