package com.biz.crm.mdm.business.product.feign.feign.internal;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.product.feign.feign.ProductMediaVoServiceFeign;
import com.biz.crm.mdm.business.product.sdk.vo.ProductMediaVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 商品附件feign熔断实现
 *
 * <AUTHOR>
 * @date 2022-09-16 22:30:37
 */
@Component
public class ProductMediaVoServiceFeignImpl implements FallbackFactory<ProductMediaVoServiceFeign> {

  @Override
  public ProductMediaVoServiceFeign create(Throwable throwable) {
    return new ProductMediaVoServiceFeign() {
      @Override
      public Result<List<ProductMediaVo>> findByProductCodes(List<String> productCodeList) {
        throw new UnsupportedOperationException("根据商品编码集合获取对应的媒体信息熔断");
      }

    };
  }
}
