package com.biz.crm.mdm.business.product.feign.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.product.feign.feign.ProductSmallClassVoServiceFeign;
import com.biz.crm.mdm.business.product.sdk.dto.ProductSmallClassDto;
import com.biz.crm.mdm.business.product.sdk.dto.ProductSmallClassSelectDto;
import com.biz.crm.mdm.business.product.sdk.service.ProductSmallClassVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductSmallClassSelectVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductSmallClassVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * 商品小类sdk实现类
 *
 * <AUTHOR>
 * @date 2021/10/13
 */
@Service
public class ProductSmallClassVoServiceImpl implements ProductSmallClassVoService {

    @Autowired(required = false)
    private ProductSmallClassVoServiceFeign productSmallClassVoServiceFeign;

    /**
     * 根据编码获取名称
     *
     * @param codes
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/18 13:17
     */
    @Override
    public Map<String, String> findNameByCodes(Set<String> codes) {
        if (CollectionUtil.isEmpty(codes)) {
            return Maps.newHashMap();
        }
        return productSmallClassVoServiceFeign.findNameByCodes(codes).checkFeignResult();
    }

    /**
     * 分页查询
     *
     * @param pageable
     * @param dto
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.biz.crm.mdm.business.product.sdk.vo.ProductSmallClassVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/18 13:18
     */
    @Override
    public Page<ProductSmallClassVo> findByConditions(Pageable pageable, ProductSmallClassDto dto) {
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
        dto = Optional.ofNullable(dto).orElse(new ProductSmallClassDto());
        return productSmallClassVoServiceFeign.findByConditions(pageable.getPageNumber(), pageable.getPageSize(), dto).checkFeignResult();
    }

    /**
     * 分页下拉
     *
     * @param pageable
     * @param dto
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.biz.crm.mdm.business.product.sdk.vo.ProductSmallClassSelectVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/18 13:18
     */
    @Override
    public Page<ProductSmallClassSelectVo> findByProductSmallClassSelectDto(Pageable pageable, ProductSmallClassSelectDto dto) {
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
        dto = Optional.ofNullable(dto).orElse(new ProductSmallClassSelectDto());
        return productSmallClassVoServiceFeign.findByProductSmallClassSelectDto(pageable.getPageNumber(), pageable.getPageSize(), dto).checkFeignResult();
    }

    /**
     * 根据ID或编码获取详情
     *
     * @param id
     * @param code
     * @return com.biz.crm.mdm.business.product.sdk.vo.ProductSmallClassVo
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/18 13:18
     */
    @Override
    public ProductSmallClassVo findByIdOrCode(String id, String code) {
        if (StringUtil.isAllEmpty(id, code)) {
            return null;
        }
        return productSmallClassVoServiceFeign.findByIdOrCode(id, code).checkFeignResult();
    }

    /**
     * 根据ID集合或编码集合获取详情
     *
     * @param ids
     * @param codes
     * @return java.util.List<com.biz.crm.mdm.business.product.sdk.vo.ProductSmallClassVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/18 13:18
     */
    @Override
    public List<ProductSmallClassVo> findByIdsOrCodes(Set<String> ids, Set<String> codes) {
        if (CollectionUtil.isEmpty(ids)
                && CollectionUtil.isEmpty(codes)) {
            return Lists.newArrayList();
        }

        return productSmallClassVoServiceFeign.findByIdsOrCodes(ids, codes).checkFeignResult();
    }

    @Override
    public List<ProductSmallClassVo> loadCacheProductSmallClass() {
        return productSmallClassVoServiceFeign.loadCacheProductSmallClass().checkFeignResult();
    }
}
