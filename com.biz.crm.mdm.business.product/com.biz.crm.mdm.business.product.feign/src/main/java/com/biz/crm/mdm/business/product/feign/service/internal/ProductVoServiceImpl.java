package com.biz.crm.mdm.business.product.feign.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.product.feign.feign.ProductVoServiceFeign;
import com.biz.crm.mdm.business.product.sdk.dto.*;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductItemVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductMaterialVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPriceVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 商品sdk实现
 *
 * <AUTHOR>
 * @date 2021/11/16
 */
@Service
public class ProductVoServiceImpl implements ProductVoService {

    @Autowired(required = false)
    private ProductVoServiceFeign productVoServiceFeign;

    @Override
    public List<String> getProductUnitList(String productCode) {
        if (StringUtil.isEmpty(productCode)) {
            return Lists.newArrayList();
        }
        return this.productVoServiceFeign.getProductUnitList(productCode).checkFeignResult();
    }

    @Override
    public Map<String, List<String>> getProductsUnitList(List<String> productCodes) {
        if (CollectionUtil.isEmpty(productCodes)) {
            return Maps.newHashMap();
        }
        return this.productVoServiceFeign.getProductsUnitList(productCodes).checkFeignResult();
    }

    @Override
    public List<ProductVo> findByMaterialCodes(List<String> materialCodes) {
        if (CollectionUtil.isEmpty(materialCodes)) {
            return Lists.newArrayList();
        }
        return productVoServiceFeign.findByMaterialCodes(materialCodes).checkFeignResult();
    }

    @Override
    public List<String> findByProductSmallClass(List<String> smallClassList) {
        if (CollectionUtil.isEmpty(smallClassList)) {
            return Lists.newArrayList();
        }
        return productVoServiceFeign.findByProductSmallClass(smallClassList).checkFeignResult();
    }

    @Override
    public List<ProductVo> findByProductSmallClassCodes(List<String> productSmallClassCodes) {
        if (CollectionUtil.isEmpty(productSmallClassCodes)) {
            return Lists.newArrayList();
        }
        return this.productVoServiceFeign.findByProductSmallClassCodes(productSmallClassCodes).checkFeignResult();
    }

    @Override
    public Map<String, String> findBusinessTypeByProductCodes(List<String> productCodeList) {
        return this.productVoServiceFeign.findBusinessTypeByProductCodes(productCodeList).checkFeignResult();
    }

    @Override
    public List<ProductVo> findAllDownListByProductLevelCodes(ProductPaginationDto dto) {
        return this.productVoServiceFeign.findAllDownListByProductLevelCodes(dto).checkFeignResult();
    }

    @Override
    public List<ProductVo> findByProductNames(Set<String> productNames) {
        return this.productVoServiceFeign.findByProductNames(productNames).checkFeignResult();
    }

    @Override
    public List<ProductPriceVo> findProductPriceByDto(ProductPriceVo dto) {
        return this.productVoServiceFeign.findProductPriceByDto(dto).checkFeignResult();
    }

    @Override
    public List<ProductVo> findProductListByProductPhaseCodes(List<String> productPhaseCodes) {
        return productVoServiceFeign.findProductListByProductPhaseCodes(productPhaseCodes).checkFeignResult();
    }

    @Override
    public List<ProductVo> findDetailsByIdsOrProductCodes(List<String> ids, List<String> productCodes) {
        ids = Optional.ofNullable(ids).orElse(Lists.newLinkedList());
        productCodes = Optional.ofNullable(productCodes).orElse(Lists.newLinkedList());
        ProductDetailDto dto = new ProductDetailDto();
        dto.setIds(ids);
        dto.setCodes(productCodes);
        if (CollectionUtil.isEmpty(ids)
                && CollectionUtil.isEmpty(productCodes)) {
            return Lists.newArrayList();
        }
        //修改成POST请求 解决访问头部数据量过大
        return this.productVoServiceFeign.findProductDetailsByIdsOrCodes(dto).checkFeignResult();
    }

    @Override
    public ProductVo create(ProductDto dto) {
        throw new UnsupportedOperationException();
    }

    @Override
    public ProductVo update(ProductDto dto) {
        throw new UnsupportedOperationException();
    }

    @Override
    public List<ProductVo> findByProductLevelCodes(List<String> productLevelCodeList) {
        if (CollectionUtil.isEmpty(productLevelCodeList)) {
            return Lists.newArrayList();
        }
        return this.productVoServiceFeign.findByProductLevelCodes(productLevelCodeList).checkFeignResult();
    }

    @Override
    public List<ProductVo> findMainDetailsByProductCodes(List<String> productCodeList) {
        if (CollectionUtil.isEmpty(productCodeList)) {
            return Lists.newArrayList();
        }
        return this.productVoServiceFeign.findMainDetailsByProductCodes(productCodeList).checkFeignResult();
    }

    @Override
    public List<ProductVo> findMainDetailsByDto(ProductDetailQueryDto dto) {
        if (CollectionUtil.isEmpty(dto.getCodes())) {
            return Lists.newArrayList();
        }
        return this.productVoServiceFeign.findMainDetailsByDto(dto).checkFeignResult();
    }

    @Override
    public List<ProductVo> findByProductQueryDto(ProductQueryDto dto) {
        return this.productVoServiceFeign.findByProductQueryDto(dto).checkFeignResult();
    }

    @Override
    public List<ProductVo> findByQueryDto(ProductQueryDto dto) {
        return this.productVoServiceFeign.findByQueryDto(dto).checkFeignResult();
    }

    @Override
    public List<ProductVo> findDetailsByProductDetailQueryDto(ProductDetailQueryDto dto) {
        return this.productVoServiceFeign.findDetailsByProductDetailQueryDto(dto).checkFeignResult();
    }

    @Override
    public Map<String, String> findAllowSaleProductByProductLevelCodes(Set<String> productLevelCodes) {
        if (CollectionUtil.isEmpty(productLevelCodes)) {
            return Maps.newHashMap();
        }
        return this.productVoServiceFeign
                .findAllowSaleProductByProductLevelCodes(productLevelCodes)
                .checkFeignResult();
    }

    @Override
    public Page<ProductVo> findByConditions(Pageable pageable, ProductPaginationDto dto) {
        return this.productVoServiceFeign.findByConditions(pageable, dto).checkFeignResult();
    }

    @Override
    public Set<String> findParentLevelCodeSetByProductCodes(Set<String> productCodeSet) {
        if (CollectionUtil.isEmpty(productCodeSet)) {
            return Sets.newHashSet();
        }
        return this.productVoServiceFeign.findParentLevelCodeSetByProductCodes(productCodeSet).checkFeignResult();
    }

    @Override
    public List<ProductMaterialVo> findMaterialProductVoByProductCodes(List<String> productCodes) {
        if (CollectionUtil.isEmpty(productCodes)) {
            return Lists.newArrayList();
        }
        return this.productVoServiceFeign.findMaterialProductVoByProductCodes(productCodes).checkFeignResult();
    }

    @Override
    public List<ProductVo> findDetailByBarCode(String barCode) {
        if (StringUtil.isEmpty(barCode)) {
            return Lists.newArrayList();
        }
        return this.productVoServiceFeign.findDetailByBarCode(barCode).checkFeignResult();
    }

    @Override
    public List<ProductVo> findDetailByBarCodes(Set<String> barCodes) {
        if (CollectionUtil.isEmpty(barCodes)) {
            return Lists.newArrayList();
        }
        return this.productVoServiceFeign.findDetailByBarCodes(barCodes).checkFeignResult();
    }

    @Override
    public List<ProductItemVo> findItemListByItemNames(List<String> itemNames) {
        if (CollectionUtil.isEmpty(itemNames)) {
            return Lists.newArrayList();
        }
        return productVoServiceFeign.findItemListByItemNames(itemNames).checkFeignResult();
    }

    @Override
    public List<ProductItemVo> findItemListByItemCodes(List<String> itemCodes) {
        if (CollectionUtil.isEmpty(itemCodes)) {
            return Lists.newArrayList();
        }
        return productVoServiceFeign.findItemListByItemCodes(itemCodes).checkFeignResult();
    }

    @Override
    public Page<ProductVo> findAllowProductByDto(ProductPaginationDto dto) {
        if (Objects.isNull(dto)) {
            return new Page<>(1, 1, 0);
        }
        return this.productVoServiceFeign.findAllowProductByDto(dto).checkFeignResult();
    }

    @Override
    public Page<ProductVo> findAllowProductPageByDto(ProductPaginationDto dto) {
        if (Objects.isNull(dto)) {
            return new Page<>(1, 1, 0);
        }
        return this.productVoServiceFeign.findAllowProductPageByDto(dto).checkFeignResult();
    }
}
