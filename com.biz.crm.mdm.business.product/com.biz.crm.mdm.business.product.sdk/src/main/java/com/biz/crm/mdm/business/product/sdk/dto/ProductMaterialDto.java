package com.biz.crm.mdm.business.product.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品物料dto
 *
 * <AUTHOR>
 * @since 2021-10-13 17:26:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "商品物料dto")
public class ProductMaterialDto extends TenantDto {

  /** 商品编码 */
  @ApiModelProperty("商品编码")
  private String productCode;

  /** 物料编码 */
  @ApiModelProperty("物料编码")
  private String materialCode;

  /** 物料名称 */
  @ApiModelProperty("物料名称")
  private String materialName;

  /** 物料单位编码 */
  @ApiModelProperty("物料单位编码")
  private String unitCode;

  /** 物料单位名称 */
  @ApiModelProperty("物料单位名称")
  private String unitName;

  /** 比例 */
  @ApiModelProperty("比例")
  private BigDecimal ratio;

  /** 物料数量 */
  @ApiModelProperty("物料数量")
  private BigDecimal count;
}
