package com.biz.crm.mdm.business.product.sdk.vo;

import com.biz.crm.business.common.sdk.vo.FileVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品图片视频表vo
 *
 * <AUTHOR>
 * @since 2021-10-13 17:27:07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "商品图片视频表Vo")
public class ProductMediaVo extends FileVo {
  private static final long serialVersionUID = -3455197601876450374L;

  @ApiModelProperty("商品编码")
  private String productCode;

  @ApiModelProperty("文件类型('picture'图片,'video'视频)")
  private String type;

  @ApiModelProperty("主图片")
  private String primaryPicture;
}
