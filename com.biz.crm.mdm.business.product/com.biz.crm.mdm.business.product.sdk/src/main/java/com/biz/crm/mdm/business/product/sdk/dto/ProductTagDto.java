package com.biz.crm.mdm.business.product.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 商品标签信息dto
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/23 15:44
 */
@Data
@ApiModel(value = "ProductTagDto", description = "商品标签信息dto")
public class ProductTagDto extends TenantFlagOpDto {

    @ApiModelProperty("标签编码")
    private String tagCode;

    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty("标签icon")
    private String tagUrl;

    @ApiModelProperty("标签描述")
    private String tagDescription;

    @ApiModelProperty("显示顺序")
    private Integer showOrder;

    @ApiModelProperty("商品编码集合")
    private List<String> productCodeList;
}
