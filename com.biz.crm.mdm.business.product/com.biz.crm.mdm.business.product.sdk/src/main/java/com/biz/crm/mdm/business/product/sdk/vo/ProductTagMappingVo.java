package com.biz.crm.mdm.business.product.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 商品关联标签信息分页查询dto
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/23 16:00
 */
@Data
@ApiModel(value = "ProductTagMappingDto", description = "商品关联标签信息分页查询dto")
public class ProductTagMappingVo extends ProductVo {

    private static final long serialVersionUID = 3305556063349496125L;

    @ApiModelProperty("标签编码")
    private String tagCode;

    @ApiModelProperty("标签名称")
    private String tagName;

}
