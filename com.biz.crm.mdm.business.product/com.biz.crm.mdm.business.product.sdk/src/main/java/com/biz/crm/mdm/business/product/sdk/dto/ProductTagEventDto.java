package com.biz.crm.mdm.business.product.sdk.dto;

import com.biz.crm.mdm.business.product.sdk.vo.ProductTagVo;
import com.bizunited.nebula.event.sdk.service.NebulaEventDto;
import lombok.Data;

import java.util.List;

/**
 * 商品标签事件dto
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/23 19:25
 */
@Data
public class ProductTagEventDto implements NebulaEventDto {

    /**
     * 原始
     */
    private ProductTagVo original;

    /**
     * 最新
     */
    private ProductTagVo newest;

    /**
     * 最新
     */
    private List<ProductTagVo> newestList;
}
