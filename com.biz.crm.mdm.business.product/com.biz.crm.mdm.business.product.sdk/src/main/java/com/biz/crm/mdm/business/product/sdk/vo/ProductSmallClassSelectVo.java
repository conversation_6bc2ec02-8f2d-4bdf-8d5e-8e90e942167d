package com.biz.crm.mdm.business.product.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 商品小类信息下拉选择Vo
 *
 * <AUTHOR>
 * @since 2021-10-13 17:24:35
 */
@Getter
@Setter
@ApiModel(value = "商品小类信息下拉选择Vo")
public class ProductSmallClassSelectVo extends TenantVo {

    private static final long serialVersionUID = -5817471729175637512L;

    @ApiModelProperty("产品小类编码")
    private String productSmallClassCode;

    @ApiModelProperty("产品小类名称")
    private String productSmallClassName;

    @ApiModelProperty("产品小类名称")
    private String unionName;
}
