package com.biz.crm.mdm.business.product.sdk.vo;

import com.bizunited.nebula.common.vo.TenantVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 商品物料表vo
 *
 * <AUTHOR>
 * @since 2021-10-13 17:26:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "商品物料表Vo")
public class ProductMaterialVo extends TenantVo {

  private static final long serialVersionUID = 7940769408114336106L;

  /** 商品编码 */
  @ApiModelProperty("商品编码")
  private String productCode;

  /** 物料编码 */
  @ApiModelProperty("物料编码")
  private String materialCode;

  /** 物料名称 */
  @ApiModelProperty("物料名称")
  private String materialName;

  @ApiModelProperty("产品小类")
  private String productSmallClassCode;

  @ApiModelProperty("产品小类名称")
  private String productSmallClassName;

  /** 物料单位编码 */
  @ApiModelProperty("物料单位编码")
  private String unitCode;

  /** 物料单位名称 */
  @ApiModelProperty("物料单位名称")
  private String unitName;

  /** 比例 */
  @ApiModelProperty("比例")
  private BigDecimal ratio;

  /** 物料数量 */
  @ApiModelProperty("物料数量")
  private BigDecimal count;

  /**
   * 产品层级编码
   */
  @ApiModelProperty("产品层级编码")
  private String productLevelCode;

  /**
   * 产品层级名称
   */
  @ApiModelProperty("产品层级名称")
  private String productLevelName;

  /**
   * 物料类型
   */
  @ApiModelProperty("物料类型")
  private String materialType;

  /**
   * 物料类型名称
   */
  @ApiModelProperty("物料类型名称")
  private String materialTypeName;

  /**
   * 物料单位类型编码
   */
  @ApiModelProperty("物料单位类型编码")
  private String unitTypeCode;
}
