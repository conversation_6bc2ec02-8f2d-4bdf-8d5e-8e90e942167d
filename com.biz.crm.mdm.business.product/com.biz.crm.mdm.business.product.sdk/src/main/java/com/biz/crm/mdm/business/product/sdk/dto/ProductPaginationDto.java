package com.biz.crm.mdm.business.product.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import java.util.List;
import java.util.Set;

/**
 * mdm_product 商品主数据分页查询dto
 *
 * <AUTHOR>
 * @since 2021-10-13 17:11:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ProductPaginationDto", description = "mdm_product 商品主数据分页查询dto")
public class ProductPaginationDto extends TenantFlagOpDto {

    /**
     * 商品层级编码
     */
    @ApiModelProperty("商品层级编码")
    private String productLevelCode;

    /**
     * 商品层级名称
     */
    @ApiModelProperty("商品层级名称")
    private String productLevelName;

    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    private String productCode;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String productName;

    /**
     * 上下加状态
     */
    @ApiModelProperty("上下加状态")
    private String isShelf;

    /**
     * 编码名称模糊查询
     */
    @ApiModelProperty("编码名称模糊查询")
    private String keyword;

    @ApiModelProperty("品相编码")
    private String productPhaseCode;

    @ApiModelProperty("商品类型")
    private String productType;

    /**
     * 用于下拉列表选择,输入回显编码集合，字符串集合，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据
     */
    @ApiModelProperty("用于下拉列表选择,输入回显编码集合，字符串集合，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据")
    private List<String> selectedCode;

    /**
     * 用于商品层级编码集合查询
     */
    @ApiModelProperty("商品层级编码集合")
    private Set<String> productLevelCodes;

    /**
     * 用于商品编码集合查询
     */
    @ApiModelProperty("商品编码集合")
    private Set<String> productCodes;

    @ApiModelProperty("品项-财务经营指标")
    private Set<String> itemSet;

    /**
     * 产品标签
     */
    @ApiModelProperty("产品标签")
    private String skuTag;

    /**
     * 每页显示条数
     */
    @ApiModelProperty(value = "每页显示条数")
    private Integer size;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer page;

    /**
     * 物料组编码
     */
    @ApiModelProperty(value = "物料组编码")
    private String materialGroupCode;

    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    private String materialCode;

    /**
     * 排除的物料组编码
     */
    @ApiModelProperty(value = "排除的物料组编码")
    private List<String> excludeMaterialGroupCodeList;

    /**
     * 客户代码
     */
    @ApiModelProperty("客户代码")
    private String customerCode;

    @ApiModelProperty("业态")
    private String businessType;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("发货工厂编码")
    private String factoryCode;

    @ApiModelProperty("是否发布")
    private String costLabel;

    @ApiModelProperty("产品层级规则编码")
    private Set<String> productPhaseCodeSet;

    @ApiModelProperty("零级查询标识")
    private String zeroLevelFlag;

    @ApiModelProperty("查询出相同的产品小类的产品")
    private String productCodeForProductSmallClassCode;

    @ApiModelProperty("商品名称集合")
    private Set<String> productNames;

    @ApiModelProperty("产品小类编码")
    private String productSmallClassCode;

    @ApiModelProperty("code或者名称")
    private String productCodeOrName;
}
