package com.biz.crm.mdm.business.product.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 商品小类信息Vo
 *
 * <AUTHOR>
 * @since 2021-10-13 17:24:35
 */
@Getter
@Setter
@ApiModel(value = "商品小类信息Vo")
public class ProductSmallClassVo extends TenantFlagOpVo {

    private static final long serialVersionUID = 5300117647586826015L;

    @ApiModelProperty("产品小类编码")
    private String productSmallClassCode;

    @ApiModelProperty("产品小类名称")
    private String productSmallClassName;
}
