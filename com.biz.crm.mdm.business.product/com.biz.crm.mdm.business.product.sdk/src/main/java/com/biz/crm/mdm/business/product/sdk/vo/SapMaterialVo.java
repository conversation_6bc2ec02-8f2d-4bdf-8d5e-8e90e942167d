package com.biz.crm.mdm.business.product.sdk.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;


/**
 * SAP物料vo
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/27 12:42
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ApiModel(value = "SapMaterialVo", description = "SAP物料vo")
public class SapMaterialVo implements Serializable {

    private static final long serialVersionUID = 580643956110563022L;

    @ApiModelProperty("物料编号")
    private String MATNR;

    @ApiModelProperty("物料描述")
    private String MAKTX;

    @ApiModelProperty("创建对象的人员名称")
    private String ERNAM;

    @ApiModelProperty("完整的人员名称")
    private String ERNAMT;

    @ApiModelProperty("创建日期")
    private String ERSDA;

    @ApiModelProperty("创建时间")
    private String CREATED;

    @ApiModelProperty("物料类型")
    private String MTART;

    @ApiModelProperty("物料类型描述")
    private String MTARTX;

    @ApiModelProperty("物料组")
    private String MATKL;

    @ApiModelProperty("物料组描述")
    private String MATKLX;

    @ApiModelProperty("基本计量单位")
    private String MEINS;

    @ApiModelProperty("毛重")
    private String BRGEW;

    @ApiModelProperty("净重")
    private String NTGEW;

    @ApiModelProperty("重量单位")
    private String GEWEI;

    @ApiModelProperty("产品组")
    private String SPART;

    @ApiModelProperty("69码")
    private String BCODE;

    @ApiModelProperty("商品简称")
    private String BRNAM;

    @ApiModelProperty("税率")
    private String ZSL;

    @ApiModelProperty("长")
    private String ZLENGTH;

    @ApiModelProperty("宽")
    private String ZWIDTH;

    @ApiModelProperty("高")
    private String ZHIGH;

    @ApiModelProperty("体积")
    private String ZVOLUME;

    @ApiModelProperty("产品类别")
    private String ZPRODUCT_LB;

    @ApiModelProperty("产品小类编码")
    private String ZCPXLID;

    @ApiModelProperty("产品小类")
    private String ZCPXL;

    @ApiModelProperty("分类编码")
    private String ZFLID;

    @ApiModelProperty("分类名称")
    private String ZFLMC;

    @ApiModelProperty("财务经营指标")
    private String ZCPSJ_CW;

    @ApiModelProperty("跨工厂冻结状态")
    private String MSTAE;

    @ApiModelProperty("四级分类编码")
    private String ZZSJFLID;

    @ApiModelProperty("四级分类名称")
    private String ZZSJFL;

    @ApiModelProperty("转化值")
    private String ZZHZ;

    @ApiModelProperty("明细")
    private List<SapMaterialItemVo> ITEM;

}