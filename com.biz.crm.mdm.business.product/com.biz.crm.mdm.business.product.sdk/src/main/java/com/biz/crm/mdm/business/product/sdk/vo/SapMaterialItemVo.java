package com.biz.crm.mdm.business.product.sdk.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * SAP物料明细vo
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/27 12:42
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ApiModel(value = "SapMaterialItemVo", description = "SAP物料明细vo")
public class SapMaterialItemVo implements Serializable {

    private static final long serialVersionUID = -2596328223243628248L;


    @ApiModelProperty("是否发布标准成本标识")
    private String ZSFGS;

    @ApiModelProperty("工厂")
    private String WERKS;

    @ApiModelProperty("成本价")
    private String STPRS;

    @ApiModelProperty("价格单位")
    private String PEINH;

    @ApiModelProperty("工厂层级冻结状态")
    private String MMSTA;


}