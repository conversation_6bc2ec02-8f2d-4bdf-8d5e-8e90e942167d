package com.biz.crm.mdm.business.product.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.product.sdk.dto.ProductSmallClassDto;
import com.biz.crm.mdm.business.product.sdk.dto.ProductSmallClassSelectDto;
import com.biz.crm.mdm.business.product.sdk.vo.ProductSmallClassSelectVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductSmallClassVo;
import com.google.common.collect.Maps;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 商品小类主数据Vo接口
 *
 * <AUTHOR>
 * @since 2021-10-13 17:36:03
 */
public interface ProductSmallClassVoService {
    /**
     * 根据编码获取名称
     *
     * @param codes
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/18 13:17
     */
    default Map<String, String> findNameByCodes(Set<String> codes) {
        return Maps.newHashMap();
    }

    /**
     * 分页查询
     *
     * @param pageable
     * @param dto
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.biz.crm.mdm.business.product.sdk.vo.ProductSmallClassVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/18 13:18
     */
    Page<ProductSmallClassVo> findByConditions(Pageable pageable, ProductSmallClassDto dto);

    /**
     * 分页下拉
     *
     * @param pageable
     * @param dto
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.biz.crm.mdm.business.product.sdk.vo.ProductSmallClassSelectVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/18 13:18
     */
    Page<ProductSmallClassSelectVo> findByProductSmallClassSelectDto(Pageable pageable, ProductSmallClassSelectDto dto);

    /**
     * 根据ID或编码获取详情
     *
     * @param id
     * @param code
     * @return com.biz.crm.mdm.business.product.sdk.vo.ProductSmallClassVo
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/18 13:18
     */
    ProductSmallClassVo findByIdOrCode(String id, String code);

    /**
     * 根据ID集合或编码集合获取详情
     *
     * @param ids
     * @param codes
     * @return java.util.List<com.biz.crm.mdm.business.product.sdk.vo.ProductSmallClassVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/18 13:18
     */
    List<ProductSmallClassVo> findByIdsOrCodes(Set<String> ids, Set<String> codes);

    List<ProductSmallClassVo> loadCacheProductSmallClass();
}
