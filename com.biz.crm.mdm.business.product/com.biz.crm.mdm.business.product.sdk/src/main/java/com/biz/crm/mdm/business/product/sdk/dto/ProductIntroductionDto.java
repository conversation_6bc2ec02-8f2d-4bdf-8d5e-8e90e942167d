package com.biz.crm.mdm.business.product.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品富文本dto
 *
 * <AUTHOR>
 * @since 2021-10-13 17:26:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "商品富文本dto")
public class ProductIntroductionDto extends TenantDto {

  @ApiModelProperty("商品编码")
  private String productCode;

  @ApiModelProperty("富文本介绍")
  private String introductionText;
}
