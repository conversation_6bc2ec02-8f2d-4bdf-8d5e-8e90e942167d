package com.biz.crm.mdm.business.product.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * 商品与物料关系实体
 *
 * <AUTHOR>
 * @date 2021/10/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("mdm_product_material")
@ApiModel(value = "ProductMaterial", description = "商品与物料关系实体")
@Table(name = "mdm_product_material", indexes = {
        @Index(name = "mdm_product_material_uq1", columnList = "product_code,material_code", unique = true),
})
@org.hibernate.annotations.Table(appliesTo = "mdm_product_material", comment = "商品与物料关系中间表")
public class ProductMaterial extends TenantEntity {

    private static final long serialVersionUID = 7394531336685905765L;

    @ApiModelProperty("商品编码")
    @TableField(value = "product_code")
    @Column(name = "product_code", columnDefinition = "VARCHAR(32) COMMENT '商品编码'")
    private String productCode;

    @ApiModelProperty("物料编码")
    @TableField(value = "material_code")
    @Column(name = "material_code", columnDefinition = "VARCHAR(32) COMMENT '物料编码'")
    private String materialCode;

    @ApiModelProperty("物料单位编码")
    @Column(name = "unit_code", columnDefinition = "VARCHAR(32) COMMENT ' 物料单位编码 '")
    private String unitCode;

    @ApiModelProperty("物料单位名称")
    @Column(name = "unit_name", columnDefinition = "VARCHAR(128) COMMENT ' 物料单位名称 '")
    private String unitName;

    @ApiModelProperty("比例")
    @TableField(value = "ratio")
    @Column(name = "ratio", columnDefinition = "decimal(12,8) COMMENT '比例'")
    private BigDecimal ratio;

    @ApiModelProperty("物料数量")
    @TableField(value = "count")
    @Column(name = "count", columnDefinition = "decimal(12,8) COMMENT '比例'")
    private BigDecimal count;

    @ApiModelProperty("物料名称")
    @TableField(exist = false)
    @Transient
    private String materialName;
}
