package com.biz.crm.mdm.business.product.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.biz.crm.mdm.business.product.sdk.dto.ProductTagMappingDto;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.util.List;

/**
 * 商品标签信息(ProductTag)实体类
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/23 15:42
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("mdm_product_tag")
@Table(
        name = "mdm_product_tag",
        indexes = {
                @Index(name = "mdm_product_tag_up1", columnList = "tag_code", unique = true),
        })
@ApiModel(value = "ProductTag", description = "商品标签信息")
@org.hibernate.annotations.Table(appliesTo = "mdm_product_tag", comment = "商品标签信息")
public class ProductTag extends TenantFlagOpEntity {

    private static final long serialVersionUID = -4801376958684896473L;

    @ApiModelProperty("标签编码")
    @Column(name = "tag_code", columnDefinition = "varchar(32) COMMENT '标签编码'")
    private String tagCode;

    @ApiModelProperty("标签名称")
    @Column(name = "tag_name", columnDefinition = "varchar(256) COMMENT '标签名称'")
    private String tagName;

    @ApiModelProperty("标签icon")
    @Column(name = "tag_url", columnDefinition = "varchar(256) COMMENT '标签icon'")
    private String tagUrl;

    @ApiModelProperty("标签描述")
    @Column(name = "tag_description", columnDefinition = "varchar(256) COMMENT '标签描述'")
    private String tagDescription;

    @ApiModelProperty("显示顺序")
    @Column(name = "show_order", columnDefinition = "int(6) COMMENT '显示顺序'")
    private Integer showOrder;

    @ApiModelProperty("商品信息")
    @TableField(exist = false)
    @Transient
    private List<ProductTagMapping> tagList;

}
