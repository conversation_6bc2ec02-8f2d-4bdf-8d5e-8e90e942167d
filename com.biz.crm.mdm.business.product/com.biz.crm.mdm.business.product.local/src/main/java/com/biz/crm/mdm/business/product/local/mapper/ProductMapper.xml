<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.product.local.mapper.ProductMapper">

    <!--分页查询-->
    <select id="findByConditions"
            resultType="com.biz.crm.mdm.business.product.local.entity.Product">
        select mp.*
        from mdm_product mp
        where 1=1
        and mp.tenant_code = #{dto.tenantCode}
        <if test="dto.keyword != null and dto.keyword != ''">
            <bind name="likeKeyword" value="'%' + dto.keyword + '%'"/>
            and (mp.product_code like #{likeKeyword} or mp.product_name like #{likeKeyword})
        </if>
        <if test="dto.productCode != null and dto.productCode != ''">
            <bind name="likeProductCode" value="'%' + dto.productCode + '%'"/>
            and mp.product_code like #{likeProductCode}
        </if>
        <if test="dto.productName != null and dto.productName != ''">
            <bind name="likeProductName" value="'%' + dto.productName + '%'"/>
            and mp.product_name like #{likeProductName}
        </if>
        <if test="dto.productType != null and dto.productType != ''">
            and mp.product_type=#{dto.productType}
        </if>
        <if test="dto.delFlag != null and dto.delFlag != ''">
            and mp.del_flag=#{dto.delFlag}
        </if>
        <if test="dto.isShelf != null and dto.isShelf != ''">
            and mp.is_shelf=#{dto.isShelf}
        </if>
        <if test="dto.productLevelCode != null and dto.productLevelCode != ''">
            and mp.product_level_code = #{dto.productLevelCode}
        </if>
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and mp.enable_status=#{dto.enableStatus}
        </if>
        <if test="dto.productCodeForProductSmallClassCode != null and dto.productCodeForProductSmallClassCode != ''">
            and mp.product_small_class_code is not null
            and mp.product_small_class_code in (
            select mmp.product_small_class_code
            from mdm_product mmp
            where mmp.del_flag = '${@<EMAIL>()}'
            and mmp.product_code = #{dto.productCodeForProductSmallClassCode})
        </if>
        <if test="dto.productPhaseCode != null and dto.productPhaseCode != ''">
            and mp.product_phase_code = #{dto.productPhaseCode}
        </if>
        <if test="dto.productSmallClassCode != null and dto.productSmallClassCode != ''">
            and mp.product_small_class_code = #{dto.productSmallClassCode}
        </if>
        <if test="dto.itemSet != null and dto.itemSet.size()>0">
            and mp.product_phase_code in
            <foreach collection="dto.itemSet" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="dto.productNames != null and dto.productNames.size()>0">
            and mp.product_name in
            <foreach collection="dto.productNames" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="dto.productPhaseCodeSet != null and dto.productPhaseCodeSet.size()>0">
            and mp.product_phase_code in
            <foreach collection="dto.productPhaseCodeSet" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="dto.costLabel != null and dto.costLabel != '' and dto.costLabel == 'Y'.toString()">
            and exists (
            SELECT
            1
            FROM
            mdm_material_detail b
            WHERE
            b.material_code = mp.product_code
            <if test=" dto.factoryCode != null and dto.factoryCode != ''">
                AND b.factory_type_code = #{dto.factoryCode}
            </if>
            AND b.cost_label = 'Y'
            )
        </if>
        <choose>
            <when test="dto.productLevelCodes != null and dto.productLevelCodes.size() != 0 and dto.productCodes != null and dto.productCodes.size() != 0">
                and ( mp.product_level_code in
                <foreach collection="dto.productLevelCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or mp.product_code in
                <foreach collection="dto.productCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </when>
            <when test="dto.productLevelCodes != null and dto.productLevelCodes.size() != 0">
                and mp.product_level_code in
                <foreach collection="dto.productLevelCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <when test="dto.productCodes != null and dto.productCodes.size() != 0">
                and mp.product_code in
                <foreach collection="dto.productCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        order by
        <if test="dto.selectedCode != null and dto.selectedCode.size > 0">
            CASE
            <foreach collection="dto.selectedCode" item="item" index="index">
                WHEN mp.product_code = #{item} THEN ${index}
            </foreach>
            ELSE #{page.size} END asc,
        </if>
        mp.product_code asc
    </select>

    <select id="findByProductLevelCodes"
            resultType="com.biz.crm.mdm.business.product.sdk.vo.ProductVo">
        select a.*
        from mdm_product a
        where a.tenant_code=#{tenantCode}
        and a.del_flag=#{delFlag}
        and a.product_level_code in (<foreach collection="list" item="item" separator=",">
        #{item}</foreach>)
        order by a.product_code asc
    </select>

    <select id="findProductLevelCodeSetByProductCodes" resultType="java.lang.String">
        select distinct product_level_code
        from mdm_product
        where tenant_code = #{tenantCode}
        and product_code in (<foreach collection="list" separator="," item="item">#{item}</foreach>)
    </select>
    <select id="findByProductQueryDto"
            resultType="com.biz.crm.mdm.business.product.local.entity.Product">
        select * from mdm_product a
        where tenant_code = #{dto.tenantCode}
        <if test="dto.delFlag != null and dto.delFlag != ''">
            and a.del_flag=#{dto.delFlag}
        </if>
        <if test="dto.isShelf != null and dto.isShelf != ''">
            and a.is_shelf=#{dto.isShelf}
        </if>
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and a.enable_status=#{dto.enableStatus}
        </if>
        <if test="dto.productCode!=null and dto.productCode!=''">
            <bind name="productCodeLike" value="'%'+dto.productCode+'%'"/>
            and a.product_code like #{productCodeLike}
        </if>
        <if test="dto.productName!=null and dto.productName!=''">
            <bind name="productNameLike" value="'%'+dto.productName+'%'"/>
            and a.product_name like #{productNameLike}
        </if>
        <if test="dto.productLevelCodeList!=null and dto.productLevelCodeList.size>0">
            and a.product_level_code in(<foreach collection="dto.productLevelCodeList" item="item" separator=",">
            #{item}</foreach>)
        </if>
        <if test="dto.productCodeList!=null and dto.productCodeList.size>0">
            and a.product_code in(<foreach collection="dto.productCodeList" item="item" separator=",">#{item}</foreach>)
        </if>
        <if test="dto.smallClassCodeSet != null and dto.smallClassCodeSet.size()>0">
            and a.product_small_class_code in
            <foreach collection="dto.smallClassCodeSet" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="dto.productType != null and dto.productType != ''">
            and a.product_type = #{dto.productType}
        </if>
        <if test="dto.itemCodeSet != null and dto.itemCodeSet.size()>0">
            and a.product_phase_code in
            <foreach collection="dto.itemCodeSet" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="dto.productPhaseCodeSet != null and dto.productPhaseCodeSet.size()>0">
            and a.product_phase_code in
            <foreach collection="dto.productPhaseCodeSet" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.costLabel != null and dto.costLabel != '' and dto.factoryCode != null and dto.factoryCode != ''">
            <if test="'Y'.toString() == dto.costLabel">
                and exists (
                SELECT
                1
                FROM
                mdm_material_detail b
                WHERE
                b.material_code = a.product_code
                AND b.factory_type_code in
                <foreach collection="dto.factoryCodeSet" open="(" close=")" separator="," item="item" index="index">
                    #{item}
                </foreach>
                AND b.cost_label = 'Y'
                )
            </if>
        </if>
        <if test="dto.materialCodeSet != null and dto.materialCodeSet.size()>0">
            and a.material_code in
            <foreach collection="dto.materialCodeSet" open="(" close=")" separator="," item="item" index="">
                #{item}
            </foreach>
        </if>
        order by a.product_code asc
    </select>

    <select id="findByContractProductQueryDto"
            resultType="com.biz.crm.mdm.business.product.local.entity.Product">
        <if test="dto.productCodeSet!=null and dto.productCodeSet.size>0">
            select product_code,product_name from mdm_product
            where tenant_code=#{tenantCode}
            and del_flag=#{delFlag}
            and is_shelf=#{shelfFlag}
            and enable_status=#{enableStatus}
            and product_code in (<foreach collection="dto.productCodeSet" item="item" separator=",">#{item}</foreach>)
        </if>
        <if test="dto.unionType==true">
            union
        </if>
        <if test="dto.productLevelCodeSet!=null and dto.productLevelCodeSet.size>0">
            select product_code,product_name from mdm_product
            where tenant_code=#{tenantCode}
            and del_flag=#{delFlag}
            and is_shelf=#{shelfFlag}
            and enable_status=#{enableStatus}
            and product_level_code in (<foreach collection="dto.productLevelCodeSet" item="item" separator=",">
            #{item}</foreach>)
        </if>
    </select>

    <select id="findItemByNames" resultType="com.biz.crm.mdm.business.product.sdk.vo.ProductItemVo">
        SELECT
        a.product_level_code itemCode,
        d.product_level_name itemName,
        b.product_code,
        c.product_name
        FROM mdm_material a
        LEFT JOIN mdm_product_material b ON a.material_code = b.material_code
        LEFT JOIN mdm_product c ON b.product_code = c.product_code
        left join mdm_product_level_sap d on a.product_level_code = d.product_level_code
        WHERE c.is_shelf = 'Y'
        AND c.del_flag = '${@<EMAIL>()}'
        AND c.enable_status = '${@<EMAIL>()}'
        and d.product_level_name in
        <foreach collection="itemNames" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="findProductByItemCodeAndProductLevelCodes" resultType="com.biz.crm.mdm.business.product.sdk.vo.ProductVo">
        SELECT
        a.*
        FROM mdm_product a
        WHERE a.is_shelf = 'Y'
        AND a.del_flag = '${@<EMAIL>()}'
        AND a.enable_status = '${@<EMAIL>()}'
        <if test="itemCode != null and itemCode != ''">
            and a.product_phase_code = #{itemCode}
        </if>
        <if test="productLevelCodeSet != null and productLevelCodeSet.size()>0">
            and a.product_level_code in
            <foreach collection="productLevelCodeSet" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findAllowProductByDto" resultType="com.biz.crm.mdm.business.product.sdk.vo.ProductVo">
        select mp.*
        from mdm_product mp
        <if test="dto.productLevelName != null and dto.productLevelName != ''">
            left join mdm_product_level l on mp.product_level_code = l.product_level_code
        </if>
        left join crm_mdm.mdm_material mm on mp.product_code = mm.material_code
        left join crm_mdm.mdm_material_detail mmd on mp.product_code = mmd.material_code
        where mp.tenant_code = #{dto.tenantCode}
        and mm.enable_status = '${@<EMAIL>()}'
        and mmd.factory_type_code = #{dto.factoryCode}
        and mmd.enable_status = '${@<EMAIL>()}'
        and mp.del_flag = '${@<EMAIL>()}'
        and mp.product_code in
        <foreach collection="dto.productCodes" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
        <if test="dto.isShelf != null and dto.isShelf != ''">
            and mp.is_shelf = #{dto.isShelf}
        </if>
        <if test="dto.productType != null and dto.productType != ''">
            and mp.product_type = #{dto.productType}
        </if>
        <if test="dto.costLabel != null and dto.costLabel != '' and dto.costLabel == 'Y'.toString()">
            and exists (
            SELECT
            1
            FROM
            mdm_material_detail b
            WHERE
            b.material_code = mp.product_code
            <if test=" dto.factoryCode != null and dto.factoryCode != ''">
                AND b.factory_type_code = #{dto.factoryCode}
            </if>
            AND b.cost_label = 'Y'
            )
        </if>
        <if test="dto.keyword != null and dto.keyword != ''">
            and (mp.product_code = #{dto.keyword} or mp.product_name like concat('%', #{dto.keyword}, '%'))
        </if>
        <if test="dto.productLevelCode != null and dto.productLevelCode != ''">
            and mp.product_level_code in
            (
            select
            mplc.product_level_code
            from mdm_product_level mpl
            left join mdm_product_level mplc on mplc.rule_code like concat(mpl.rule_code, '%')
            and mpl.rule_code is not null and mpl.rule_code != ''
            where mpl.product_level_code = #{dto.productLevelCode}
            )
        </if>
        <if test="dto.productCode != null and dto.productCode != ''">
            and mp.product_code = #{dto.productCode}
        </if>
        <if test="dto.productName != null and dto.productName != ''">
            and mp.product_name like concat('%', #{dto.productName}, '%')
        </if>
        <if test="dto.materialCode != null and dto.materialCode != ''">
            and mp.material_code = #{dto.materialCode}
        </if>
        <if test="dto.productPhaseCode != null and dto.productPhaseCode != ''">
            and mp.product_phase_code = #{dto.productPhaseCode}
        </if>
        <if test="dto.materialGroupCode != null and dto.materialGroupCode != ''">
            and mp.material_group_code = #{dto.materialGroupCode}
        </if>
        <if test="dto.excludeMaterialGroupCodeList != null and dto.excludeMaterialGroupCodeList.size > 0">
            and
            (
            mp.material_group_code is null
            or
            mp.material_group_code not in
            <foreach collection="dto.excludeMaterialGroupCodeList" open="(" close=")" index="index" item="item"
                     separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="dto.productLevelName != null and dto.productLevelName != ''">
            and l.del_flag = '${@<EMAIL>()}'
            <bind name="likeProductLevelName" value="'%' + dto.productLevelName + '%'"/>
            and l.product_level_name like #{likeProductLevelName}
        </if>
    </select>

    <select id="findByMaterialCodes" resultType="com.biz.crm.mdm.business.product.sdk.vo.ProductVo">
        select product_code, material_code, product_name
        from mdm_product
        where is_shelf = 'Y'
        and tenant_code = #{tenantCode}
        AND del_flag = '${@<EMAIL>()}'
        AND enable_status = '${@<EMAIL>()}'
        and material_code in
        <foreach collection="materialCodes" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="findByProductSmallClass" resultType="java.lang.String">
        select product_small_class
        from mdm_product
        where is_shelf = 'Y'
        and tenant_code = #{tenantCode}
        AND del_flag = '${@<EMAIL>()}'
        AND enable_status = '${@<EMAIL>()}'
        and product_small_class in
        <foreach collection="smallClassList" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
        group by product_small_class
    </select>

    <select id="findAllDownListByProductLevelCodes" resultType="com.biz.crm.mdm.business.product.sdk.vo.ProductVo">
        select mp.*
        from mdm_product mp
        where mp.tenant_code = #{tenantCode}
        and mp.del_flag = '${@<EMAIL>()}'
        and
        <foreach collection="dto.productLevelCodes" item="item" open="(" separator=" or " close=")">
            mp.product_level_code in
            (
            select
            mplc.product_level_code
            from mdm_product_level mpl
            left join mdm_product_level mplc on mplc.rule_code like concat(mpl.rule_code, '%')
            and mpl.rule_code is not null and mpl.rule_code != ''
            where mpl.product_level_code = #{item}
            )
        </foreach>
        <if test="dto.isShelf != null and dto.isShelf != ''">
            and mp.is_shelf = #{dto.isShelf}
        </if>
        <if test="dto.keyword != null and dto.keyword != ''">
            and (mp.product_code like concat('%', #{dto.keyword}, '%') or mp.product_name like concat('%',
            #{dto.keyword}, '%'))
        </if>
        <if test="dto.productCode != null and dto.productCode != ''">
            and mp.product_code like concat('%', #{dto.productCode}, '%')
        </if>
        <if test="dto.productName != null and dto.productName != ''">
            and mp.product_name like concat('%', #{dto.productName}, '%')
        </if>
        <if test="dto.productCodeOrName != null and dto.productCodeOrName != ''">
            and (mp.product_code like concat('%', #{dto.productCodeOrName}, '%') or mp.product_name like concat('%',
            #{dto.productCodeOrName}, '%'))
        </if>
    </select>
    <select id="findProductPriceByDto" resultType="com.biz.crm.mdm.business.product.sdk.vo.ProductPriceVo">
        SELECT
        t.*,
        p.relate_code AS productCode,
        p.relate_name AS productName
        FROM
        mdm_price t
        LEFT JOIN mdm_price_dimension p ON t.price_code = p.price_code
        WHERE
        t.del_flag = '${@<EMAIL>()}'
        AND t.enable_status = '${@<EMAIL>()}'
        AND t.type_code = #{dto.typeCode}
        AND t.begin_time &lt;= NOW() AND t.end_time &gt;= NOW()
    </select>
</mapper>
