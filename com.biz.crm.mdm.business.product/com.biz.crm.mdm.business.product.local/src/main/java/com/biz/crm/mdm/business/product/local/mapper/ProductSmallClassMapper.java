package com.biz.crm.mdm.business.product.local.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.mapper.BusinessBaseMapper;
import com.biz.crm.mdm.business.product.local.entity.Product;
import com.biz.crm.mdm.business.product.local.entity.ProductSmallClass;
import com.biz.crm.mdm.business.product.sdk.dto.*;
import com.biz.crm.mdm.business.product.sdk.vo.ProductItemVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductSmallClassSelectVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductSmallClassVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * mdm_product_small_class 商品小类主数据(Product)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-10-13 15:38:54
 */
public interface ProductSmallClassMapper extends BusinessBaseMapper<ProductSmallClass> {

    /**
     * 分页列表
     *
     * @param page 分页信息
     * @param dto  分页参数dto
     * @return 分页列表
     */
    Page<ProductSmallClassVo> findByConditions(Page<ProductSmallClassVo> page, @Param("dto") ProductSmallClassDto dto);


    /**
     * 分页列表
     *
     * @param page 分页信息
     * @param dto  分页参数dto
     * @return 分页列表
     */
    Page<ProductSmallClassSelectVo> findByProductSmallClassSelectDto(Page<ProductSmallClassSelectVo> page, @Param("dto") ProductSmallClassSelectDto dto);


}
