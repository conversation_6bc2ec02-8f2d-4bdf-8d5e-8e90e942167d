package com.biz.crm.mdm.business.product.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.auth.sdk.service.UrlApiService;
import com.biz.crm.business.common.auth.sdk.vo.UrlAddressVo;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.base.constant.ryytn.RyConstant;
import com.biz.crm.business.common.base.eunm.ExternalSystemEnum;
import com.biz.crm.business.common.base.service.RedisLockService;
import com.biz.crm.business.common.base.util.BusinessHttpUtil;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.base.util.ryytn.RySignHeaderUtil;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.biz.crm.business.common.log.sdk.constant.ExternalLogGlobalConstants;
import com.biz.crm.business.common.log.sdk.dto.ExternalLogDetailDto;
import com.biz.crm.business.common.log.sdk.service.ExternalLogVoService;
import com.biz.crm.business.common.log.sdk.util.ExternalLogUtil;
import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialDetailDto;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialDto;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.product.level.sdk.enums.ProductLevelEnum;
import com.biz.crm.mdm.business.product.level.sdk.service.ProductLevelSapVoSdkService;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductLevelSapVo;
import com.biz.crm.mdm.business.product.local.entity.Product;
import com.biz.crm.mdm.business.product.local.entity.ProductMaterial;
import com.biz.crm.mdm.business.product.local.entity.ProductPhase;
import com.biz.crm.mdm.business.product.local.entity.ProductSmallClass;
import com.biz.crm.mdm.business.product.local.repository.ProductRepository;
import com.biz.crm.mdm.business.product.local.repository.ProductSmallClassRepository;
import com.biz.crm.mdm.business.product.local.service.MaterialSapDataSaveService;
import com.biz.crm.mdm.business.product.local.service.MaterialSapDataService;
import com.biz.crm.mdm.business.product.local.service.ProductPhaseService;
import com.biz.crm.mdm.business.product.local.service.ProductSmallClassService;
import com.biz.crm.mdm.business.product.sdk.constant.ProductConstant;
import com.biz.crm.mdm.business.product.sdk.enums.IsShelfEnum;
import com.biz.crm.mdm.business.product.sdk.vo.SapMaterialVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 物料接口实现
 *
 * <AUTHOR>
 * @date 2021-09-27 14:44:10
 */
@Service
@Slf4j
public class MaterialSapDataServiceImpl implements MaterialSapDataService {

    @Autowired(required = false)
    private UrlApiService urlApiService;

    @Autowired(required = false)
    private ExternalLogVoService externalLogVoService;

    @Autowired(required = false)
    private RedisLockService redisLockService;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private MaterialVoService materialVoService;

    @Autowired(required = false)
    private GenerateCodeService generateCodeService;

    @Autowired(required = false)
    private ProductRepository productRepository;

    @Autowired(required = false)
    private ProductSmallClassRepository productSmallClassRepository;

    @Autowired(required = false)
    private MaterialSapDataSaveService materialSapDataSaveService;

    @Autowired(required = false)
    private ProductSmallClassService productSmallClassService;

    @Autowired(required = false)
    private ProductLevelSapVoSdkService productLevelSapVoSdkService;

    @Autowired(required = false)
    private ProductPhaseService productPhaseService;


    /**
     * 拉取物料
     *
     * @param isAll 是否全量   空|false 拉取今天、昨天、前天   true  拉取所有
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/27 15:14
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NOT_SUPPORTED)
    public void pullMaterial(String isAll) {
        log.info("=====>   拉取物料 start    <=====");
        UrlAddressVo urlAddressVo = urlApiService.getUrlAddressByAccount(RyConstant.STD_SAP_ACCOUNT);
        String lockKey = ProductConstant.PULL_MATERIAL + DateUtil.dateStrNowYYYYMMDD();
        redisLockService.lock(lockKey, TimeUnit.MINUTES, 30);
        String url = urlAddressVo.getUrl();
        String interfaceAddress = String.format(RyConstant.SAP_INTERFACE_ADDRESS, urlAddressVo.getBusinessKey(), urlAddressVo.getEnvironment());
        Map<String, String> dictMap = dictDataVoService.findMapByDictTypeCode(DictConstant.MDM_SAP_PLANT_MATERIAL);
        List<String> updateTimeList = this.buildUpdateTimeList(isAll);
        try {
            Assert.notEmpty(dictMap, "数据字典[" + DictConstant.MDM_SAP_PLANT_MATERIAL + "]未配置工厂代码!");
            AbstractCrmUserIdentity crmUserIdentity = loginUserService.getAbstractLoginUser();
            List<ProductLevelSapVo> zeroLevelList = productLevelSapVoSdkService.findByProductLevelType(ProductLevelEnum.BRAND.getCode());
            Map<String, ProductLevelSapVo> zeroLevelMap = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(zeroLevelList)) {
                zeroLevelMap.putAll(zeroLevelList.stream().filter(k -> StringUtil.isNotEmpty(k.getProductLevelCode()))
                        .collect(Collectors.toMap(ProductLevelSapVo::getProductLevelCode, v -> v, (n, o) -> n)));
            }
            List<ProductLevelSapVo> oneLevelList = productLevelSapVoSdkService.findByProductLevelType(ProductLevelEnum.SERIES.getCode());
            Map<String, ProductLevelSapVo> oneLevelMap = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(oneLevelList)) {
                oneLevelMap.putAll(oneLevelList.stream().filter(k -> StringUtil.isNotEmpty(k.getProductLevelCode()))
                        .collect(Collectors.toMap(ProductLevelSapVo::getProductLevelCode, v -> v, (n, o) -> n)));
            }
            List<ProductLevelSapVo> twoLevelList = productLevelSapVoSdkService.findByProductLevelType(ProductLevelEnum.MAX.getCode());
            Map<String, ProductLevelSapVo> twoLevelMap = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(twoLevelList)) {
                twoLevelMap.putAll(twoLevelList.stream().filter(k -> StringUtil.isNotEmpty(k.getProductLevelCode()))
                        .collect(Collectors.toMap(ProductLevelSapVo::getProductLevelCode, v -> v, (n, o) -> n)));
            }
            List<ProductLevelSapVo> threeLevelList = productLevelSapVoSdkService.findByProductLevelType(ProductLevelEnum.MIN.getCode());
            Map<String, ProductLevelSapVo> threeLevelMap = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(threeLevelList)) {
                threeLevelMap.putAll(threeLevelList.stream().filter(k -> StringUtil.isNotEmpty(k.getProductLevelCode()))
                        .collect(Collectors.toMap(ProductLevelSapVo::getProductLevelCode, v -> v, (n, o) -> n)));
            }
            List<ProductLevelSapVo> fourLevelList = productLevelSapVoSdkService.findByProductLevelType(ProductLevelEnum.FOUR.getCode());
            Map<String, ProductLevelSapVo> fourLevelMap = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(fourLevelList)) {
                fourLevelMap.putAll(fourLevelList.stream().filter(k -> StringUtil.isNotEmpty(k.getProductLevelCode()))
                        .collect(Collectors.toMap(ProductLevelSapVo::getProductLevelCode, v -> v, (n, o) -> n)));
            }
            dictMap.keySet().forEach(factoryTypeCode -> {
                String bodyJson = this.buildBodyJson(updateTimeList, factoryTypeCode);
                log.error("=====>   拉取物料 工厂代码{} 修改时间[{}]    <=====", factoryTypeCode, CollectionUtil.isNotEmpty(updateTimeList) ? updateTimeList : "全量");
                ExternalLogDetailDto logDetailDto = ExternalLogUtil.buildLogSaveInfo(bodyJson, urlAddressVo);
                Map<String, String> headMap = RySignHeaderUtil.getSignHeadMap(urlAddressVo.getAccessId(), urlAddressVo.getSecretKey(), interfaceAddress);
                logDetailDto.setReqHead(JSON.toJSONString(headMap));
                logDetailDto.setMethod(ProductConstant.MATERIAL_INTERFACE);
                logDetailDto.setRequestUri(interfaceAddress);
                logDetailDto.setMethodMsg("拉取SAP物料");
                externalLogVoService.addOrUpdateLog(logDetailDto, true);
                Result<String> result = BusinessHttpUtil.post(url + interfaceAddress, bodyJson, headMap);
                ExternalLogUtil.buildLogResult(logDetailDto, result);
                logDetailDto.setStatus(ExternalLogGlobalConstants.E);
                externalLogVoService.addOrUpdateLog(logDetailDto, false);
                if (result.isSuccess()
                        && StringUtil.isNotEmpty(result.getResult())) {
                    JSONObject jsonObject = JSONObject.parseObject(result.getResult());
                    if (jsonObject.containsKey(RyConstant.SAP_HEAD_DATA)
                            && Objects.nonNull(jsonObject.getJSONObject(RyConstant.SAP_HEAD_DATA))) {
                        JSONObject head = jsonObject.getJSONObject(RyConstant.SAP_HEAD_DATA);
                        String success = head.getString(RyConstant.SAP_HEAD_KEY);
                        String msg = head.getString(RyConstant.SAP_HEAD_MSG);
                        logDetailDto.setTipMsg(msg);
                        if (!ExternalLogGlobalConstants.S.equals(success)) {
                            externalLogVoService.addOrUpdateLog(logDetailDto, false);
                            return;
                        }
                    } else {
                        logDetailDto.setTipMsg("SAP返回的数据结构异常!");
                        externalLogVoService.addOrUpdateLog(logDetailDto, false);
                        return;
                    }
                    logDetailDto.setStatus(ExternalLogGlobalConstants.S);
                    externalLogVoService.addOrUpdateLog(logDetailDto, false);
                    if (jsonObject.containsKey(RyConstant.SAP_DETAIL_DATA)
                            && CollectionUtil.isNotEmpty(jsonObject.getJSONArray(RyConstant.SAP_DETAIL_DATA))) {
                        JSONArray jsonArray = jsonObject.getJSONArray(RyConstant.SAP_DETAIL_DATA);
                        logDetailDto.setRespJsonSize(jsonArray.size());
                        Map<String, MaterialDto> dataMap = this.sapDataToEntityList(jsonArray, crmUserIdentity);
                        List<MaterialDto> dtoList = Lists.newArrayList(dataMap.values());
                        log.info("pullMaterial.dtosize1: {}", dtoList.size());
                        this.buildMaterialInfo(dtoList, zeroLevelMap, oneLevelMap, twoLevelMap, threeLevelMap, fourLevelMap);
                        log.info("pullMaterial.dtosize2: {}", dtoList.size());
                        materialVoService.saveOrUpdateSapDataDto(dtoList);
                        log.info("pullMaterial.dtosize3: {}", dtoList.size());
                        this.saveOrUpdateMaterialDtoForProduct(dtoList, crmUserIdentity);
                        log.info("pullMaterial.dtosize4: {}", dtoList.size());
                        this.saveOrUpdateMaterialDtoForProductSmallClass(dtoList, crmUserIdentity);
                        log.info("pullMaterial.dtosize5: {}", dtoList.size());
                        this.saveOrUpdateMaterialDtoForProductPhase(dtoList, crmUserIdentity);
                    }
                } else {
                    log.error("=====>   拉取物料 工厂代码[{}] 修改时间{} 失败    <=====", factoryTypeCode, updateTimeList);
                    log.error("{}", result);
                }
            });
            log.info("=====>   拉取物料  end    <=====");
        } catch (Exception e) {
            log.error("=====>   拉取物料失败    <=====");
            log.error(e.getMessage(), e);
            throw e;
        } finally {
            redisLockService.unlock(lockKey);
            log.info("=====>   拉取物料 end    <=====");
        }
    }

    /**
     * 设置物料分类信息
     *
     * @param dtoList
     * @param oneLevelMap
     * @param twoLevelMap
     * @param threeLevelMap
     * @param fourLevelMap
     */
    private void buildMaterialInfo(List<MaterialDto> dtoList, Map<String, ProductLevelSapVo> zeroLevelMap,
                                   Map<String, ProductLevelSapVo> oneLevelMap, Map<String, ProductLevelSapVo> twoLevelMap,
                                   Map<String, ProductLevelSapVo> threeLevelMap, Map<String, ProductLevelSapVo> fourLevelMap) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        dtoList.forEach(item -> {
            if (StringUtil.isEmpty(item.getFourLevelCode())) {
                return;
            }
            ProductLevelSapVo fourVo = fourLevelMap.get(item.getFourLevelCode());
            if (Objects.isNull(fourVo)) {
                return;
            }
            ProductLevelSapVo threeVo = threeLevelMap.get(StringUtils.stripToEmpty(fourVo.getParentCode()));
            if (Objects.isNull(threeVo)) {
                return;
            }
            item.setThreeLevelCode(threeVo.getProductLevelCode());
            item.setThreeLevelName(threeVo.getProductLevelName());
            ProductLevelSapVo twoVo = twoLevelMap.get(StringUtils.stripToEmpty(threeVo.getParentCode()));
            if (Objects.isNull(twoVo)) {
                return;
            }
            item.setTwoLevelCode(twoVo.getProductLevelCode());
            item.setTwoLevelName(twoVo.getProductLevelName());
            ProductLevelSapVo oneVo = oneLevelMap.get(StringUtils.stripToEmpty(twoVo.getParentCode()));
            if (Objects.isNull(oneVo)) {
                return;
            }
            item.setOneLevelCode(oneVo.getProductLevelCode());
            item.setOneLevelName(oneVo.getProductLevelName());
            ProductLevelSapVo zeroVo = zeroLevelMap.get(StringUtils.stripToEmpty(oneVo.getParentCode()));
            if (Objects.isNull(zeroVo)) {
                return;
            }
            item.setZeroLevelCode(zeroVo.getProductLevelCode());
            item.setZeroLevelName(zeroVo.getProductLevelName());
        });

    }

    /**
     * 根据物料信息,新增商品(此商品相当于产品)
     *
     * @param dtoList
     */
    private void saveOrUpdateMaterialDtoForProductSmallClass(List<MaterialDto> dtoList, AbstractCrmUserIdentity crmUserIdentity) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, ProductSmallClass> resultMap = Maps.newHashMap();
        //构建产品信息
        this.buildProductSmallClassEntity(dtoList, crmUserIdentity, resultMap);

        List<ProductSmallClass> saveList = Lists.newArrayList();
        List<ProductSmallClass> updateList = Lists.newArrayList();
        //构建产品信息
        this.separateProductSmallClassSaveOrUpdate(saveList, updateList, resultMap);
        productSmallClassService.saveOrUpdateList(saveList, updateList);
    }

    /**
     * 根据物料信息,新增商品品相
     *
     * @param dtoList
     */
    private void saveOrUpdateMaterialDtoForProductPhase(List<MaterialDto> dtoList, AbstractCrmUserIdentity crmUserIdentity) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, ProductPhase> resultMap = Maps.newHashMap();
        //构建产品信息
        this.buildProductPhaseEntity(dtoList, crmUserIdentity, resultMap);

        productPhaseService.saveOrUpdateList(new ArrayList<>(resultMap.values()));
    }

    /**
     * 构建商品品相信息
     *
     * @param dtoList
     * @param crmUserIdentity
     * @param resultMap
     */
    private void buildProductPhaseEntity(List<MaterialDto> dtoList, AbstractCrmUserIdentity crmUserIdentity,
                                         Map<String, ProductPhase> resultMap) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        //根据物料生成商品小类  所以逻辑特殊
        dtoList.forEach(dto -> {
            if (StringUtil.isNotEmpty(dto.getProductPhaseCode())) {
                ProductPhase entity = new ProductPhase();
                this.setBaseInfo(entity, crmUserIdentity);
                entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                entity.setProductPhaseCode(dto.getProductPhaseCode());
                entity.setProductPhaseName(dto.getProductPhaseName());
                entity.setZeroLevelCode(dto.getZeroLevelCode());
                resultMap.put(entity.getProductPhaseCode(), entity);
            }
        });
    }

    /**
     * 分离新增和更新
     *
     * @param saveList
     * @param updateList
     * @param resultMap
     */
    private void separateProductSmallClassSaveOrUpdate(List<ProductSmallClass> saveList, List<ProductSmallClass> updateList,
                                                       Map<String, ProductSmallClass> resultMap) {
        if (CollectionUtil.isEmpty(resultMap)) {
            return;
        }
        List<ProductSmallClass> oldList = productSmallClassRepository.findAllByCodes(resultMap.keySet());
        if (Objects.isNull(oldList)) {
            oldList = Lists.newArrayList();
        }
        Map<String, ProductSmallClass> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getProductSmallClassCode()))
                .collect(Collectors.toMap(ProductSmallClass::getProductSmallClassCode, v -> v, (n, o) -> n));
        resultMap.forEach((code, entity) -> {
            ProductSmallClass oldEntity = oldMap.get(code);
            if (Objects.nonNull(oldEntity)) {
                oldEntity.setProductSmallClassName(entity.getProductSmallClassName());
                oldEntity.setEnableStatus(entity.getEnableStatus());
                oldEntity.setDelFlag(entity.getDelFlag());
                oldEntity.setModifyAccount(entity.getModifyAccount());
                oldEntity.setModifyName(entity.getModifyName());
                oldEntity.setModifyTime(entity.getModifyTime());
                updateList.add(oldEntity);
            } else {
                saveList.add(entity);
            }
        });

    }

    /**
     * 根据物料信息,新增商品(此商品相当于产品)
     *
     * @param dtoList
     */
    private void saveOrUpdateMaterialDtoForProduct(List<MaterialDto> dtoList, AbstractCrmUserIdentity crmUserIdentity) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, Product> productMap = Maps.newHashMap();
        Map<String, ProductMaterial> productMaterialHashMap = Maps.newHashMap();
        //构建产品信息
        this.buildProductEntity(dtoList, crmUserIdentity, productMap, productMaterialHashMap);

        List<Product> saveList = Lists.newArrayList();
        List<Product> updateList = Lists.newArrayList();
        List<ProductMaterial> productMaterialList = Lists.newArrayList();
        //构建产品信息
        this.separateProductSaveOrUpdate(saveList, updateList, productMaterialList, productMap, productMaterialHashMap);
        materialSapDataSaveService.saveOrUpdateList(saveList, updateList, productMaterialHashMap.keySet(), productMaterialList);
    }


    /**
     * 分离新增和更新
     *
     * @param saveList
     * @param updateList
     * @param productMaterialList
     * @param productMap
     * @param productMaterialMap
     */
    private void separateProductSaveOrUpdate(List<Product> saveList, List<Product> updateList, List<ProductMaterial> productMaterialList,
                                             Map<String, Product> productMap, Map<String, ProductMaterial> productMaterialMap) {
        if (CollectionUtil.isEmpty(productMaterialMap)) {
            return;
        }
        List<Product> oldList = productRepository.findByMaterialCodes(productMap.keySet());
        if (Objects.isNull(oldList)) {
            oldList = Lists.newArrayList();
        }
        Map<String, Product> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getMaterialCode()))
                .collect(Collectors.toMap(Product::getMaterialCode, v -> v, (n, o) -> n));
        productMap.forEach((materialCode, entity) -> {
            Product oldEntity = oldMap.get(materialCode);
            ProductMaterial material = productMaterialMap.get(materialCode);
            if (Objects.nonNull(oldEntity)) {
                entity.setProductCode(oldEntity.getProductCode());
                material.setProductCode(oldEntity.getProductCode());
                productMaterialMap.put(materialCode, material);
                this.buildUpdateEntity(oldEntity, entity);
                updateList.add(oldEntity);
            } else {
                entity.setMaintenanceIntroduction(false);
                entity.setMaintenancePicture(false);
                saveList.add(entity);
            }
        });
        if (CollectionUtil.isNotEmpty(saveList)) {
            //List<String> productCodeList = this.generateCodeService.generateCode(ProductConstant.PRODUCT_CODE, saveList.size());
            //AtomicInteger index = new AtomicInteger(0);
            saveList.forEach(entity -> {
                String materialCode = entity.getMaterialCode();
                //entity.setProductCode(productCodeList.get(index.getAndIncrement()));
                entity.setProductCode(materialCode);
                ProductMaterial material = productMaterialMap.get(materialCode);
                material.setProductCode(entity.getProductCode());
                productMaterialMap.put(materialCode, material);
            });
        }
        productMaterialList.addAll(productMaterialMap.values());

    }

    /**
     * 构建需要更新的字段
     *
     * @param oldEntity
     * @param entity
     */
    private void buildUpdateEntity(Product oldEntity, Product entity) {

        oldEntity.setMaterialCode(entity.getMaterialCode());
        oldEntity.setProductSmallClassCode(entity.getProductSmallClassCode());
        oldEntity.setProductSmallClassName(entity.getProductSmallClassName());
        oldEntity.setProductName(entity.getProductName());
        oldEntity.setProductType(entity.getProductType());
        oldEntity.setBarCode(entity.getBarCode());
        oldEntity.setBeginDateTime(entity.getBeginDateTime());
        oldEntity.setEndDateTime(entity.getEndDateTime());
        oldEntity.setSpec(entity.getSpec());
        oldEntity.setBaseUnit(entity.getBaseUnit());
        oldEntity.setSaleUnit(entity.getSaleUnit());

        oldEntity.setSimpleName(entity.getSimpleName());
        oldEntity.setZeroLevelCode(entity.getZeroLevelCode());
        oldEntity.setZeroLevelName(entity.getZeroLevelName());
        oldEntity.setOneLevelCode(entity.getOneLevelCode());
        oldEntity.setOneLevelName(entity.getOneLevelName());
        oldEntity.setTwoLevelCode(entity.getTwoLevelCode());
        oldEntity.setTwoLevelName(entity.getTwoLevelName());
        oldEntity.setThreeLevelCode(entity.getThreeLevelCode());
        oldEntity.setThreeLevelName(entity.getThreeLevelName());
        oldEntity.setFourLevelCode(entity.getFourLevelCode());
        oldEntity.setFourLevelName(entity.getFourLevelName());
        oldEntity.setConversionValue(entity.getConversionValue());
        oldEntity.setMaterialGroupCode(entity.getMaterialGroupCode());
        oldEntity.setMaterialGroupName(entity.getMaterialGroupName());
        oldEntity.setFinanceIndex(entity.getFinanceIndex());
        oldEntity.setGrossWeight(entity.getGrossWeight());
        oldEntity.setNetWeight(entity.getNetWeight());
        oldEntity.setWeightUnit(entity.getWeightUnit());
        oldEntity.setCapacity(entity.getCapacity());
        oldEntity.setLongMetre(entity.getLongMetre());
        oldEntity.setWideMetre(entity.getWideMetre());
        oldEntity.setHighMetre(entity.getHighMetre());
        oldEntity.setTaxRate(entity.getTaxRate());
        oldEntity.setTaxRateStr(entity.getTaxRateStr());
        oldEntity.setProductPhaseCode(entity.getProductPhaseCode());
        oldEntity.setProductPhaseName(entity.getProductPhaseName());
        oldEntity.setModifyAccount(entity.getModifyAccount());
        oldEntity.setModifyName(entity.getModifyName());
        oldEntity.setModifyTime(entity.getModifyTime());
//        oldEntity.setEnableStatus(entity.getEnableStatus());
//        oldEntity.setIsShelf(entity.getDelFlag());
    }

    /**
     * 构建产品信息
     *
     * @param dtoList
     * @param crmUserIdentity
     * @param resultMap
     */
    private void buildProductSmallClassEntity(List<MaterialDto> dtoList, AbstractCrmUserIdentity crmUserIdentity,
                                              Map<String, ProductSmallClass> resultMap) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        //根据物料生成商品小类  所以逻辑特殊
        dtoList.forEach(dto -> {
            if (StringUtil.isNotEmpty(dto.getProductSmallClassCode())) {
                ProductSmallClass entity = new ProductSmallClass();
                this.setBaseInfo(entity, crmUserIdentity);
                entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                entity.setProductSmallClassCode(dto.getProductSmallClassCode());
                entity.setProductSmallClassName(dto.getProductSmallClassName());
                resultMap.put(entity.getProductSmallClassCode(), entity);
            }
        });
    }

    /**
     * 构建产品信息
     *
     * @param dtoList
     * @param crmUserIdentity
     * @param productMap
     * @param productMaterialMap
     */
    private void buildProductEntity(List<MaterialDto> dtoList, AbstractCrmUserIdentity crmUserIdentity,
                                    Map<String, Product> productMap, Map<String, ProductMaterial> productMaterialMap) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        String tenantCode = TenantUtils.getTenantCode();
        //根据物料生成商品  所以逻辑特殊
        dtoList.forEach(dto -> {
            String materialCode = dto.getMaterialCode();
            ProductMaterial material = new ProductMaterial();
            material.setId(UuidCrmUtil.general());
            material.setTenantCode(tenantCode);
            material.setProductCode(materialCode);
            material.setMaterialCode(materialCode);
            material.setUnitCode(dto.getStandardUnit());
            material.setRatio(BigDecimal.ONE);
            material.setCount(BigDecimal.ONE);
            productMaterialMap.put(materialCode, material);

            Product entity = new Product();
            this.setBaseInfo(entity, crmUserIdentity);

            entity.setMaterialCode(dto.getMaterialCode());
            entity.setProductSmallClassCode(dto.getProductSmallClassCode());
            entity.setProductSmallClassName(dto.getProductSmallClassName());
            entity.setProductName(dto.getMaterialName());
            entity.setProductType(dto.getMaterialType());
            entity.setBarCode(dto.getBarCode());
            entity.setBeginDateTime(dto.getStartTime());
            entity.setEndDateTime(dto.getEndTime());
            entity.setSpec(dto.getSpecification());
            entity.setProductLevelCode(dto.getProductLevelCode());
            entity.setBaseUnit(dto.getStandardUnit());
            entity.setSaleUnit(dto.getStandardUnit());

            entity.setSimpleName(dto.getSimpleName());
            entity.setZeroLevelCode(dto.getZeroLevelCode());
            entity.setZeroLevelName(dto.getZeroLevelName());
            entity.setOneLevelCode(dto.getOneLevelCode());
            entity.setOneLevelName(dto.getOneLevelName());
            entity.setTwoLevelCode(dto.getTwoLevelCode());
            entity.setTwoLevelName(dto.getTwoLevelName());
            entity.setThreeLevelCode(dto.getThreeLevelCode());
            entity.setThreeLevelName(dto.getThreeLevelName());
            entity.setFourLevelCode(dto.getFourLevelCode());
            entity.setFourLevelName(dto.getFourLevelName());
            entity.setConversionValue(dto.getConversionValue());
            entity.setMaterialGroupCode(dto.getMaterialGroupCode());
            entity.setMaterialGroupName(dto.getMaterialGroupName());
            entity.setFinanceIndex(dto.getFinanceIndex());
            entity.setGrossWeight(dto.getGrossWeight());
            entity.setNetWeight(dto.getNetWeight());
            entity.setWeightUnit(dto.getWeightUnit());
            entity.setCapacity(dto.getCapacity());
            entity.setLongMetre(dto.getLongMetre());
            entity.setWideMetre(dto.getWideMetre());
            entity.setHighMetre(dto.getHighMetre());
            entity.setTaxRate(dto.getTaxRate());
            entity.setTaxRateStr(dto.getTaxRateStr());
            entity.setProductPhaseCode(dto.getProductPhaseCode());
            entity.setProductPhaseName(dto.getProductPhaseName());

            entity.setEnableStatus(dto.getEnableStatus());
            IsShelfEnum isShelfEnum = IsShelfEnum.DOWN;
            if (EnableStatusEnum.ENABLE.getCode().equals(dto.getEnableStatus())) {
                isShelfEnum = IsShelfEnum.UP;
            }
            entity.setIsShelf(isShelfEnum.getCode());
            productMap.put(materialCode, entity);
        });
    }

    /**
     * 获取更新时间
     *
     * @param isAll
     * @return
     */
    private List<String> buildUpdateTimeList(String isAll) {
        List<String> updateTimeList = Lists.newArrayList();
        if (StringUtil.isNotEmpty(isAll)
                && BooleanEnum.TRUE.getCapital().equals(isAll)) {
            updateTimeList.add("");
        } else {
            Date dateNow = new Date();
            updateTimeList.add(DateUtil.format(DateUtil.dateAddDay(dateNow, -2), DateUtil.DEFAULT_YEAR_MONTH_DAY_NO_CH));
            updateTimeList.add(DateUtil.format(DateUtil.dateAddDay(dateNow, -1), DateUtil.DEFAULT_YEAR_MONTH_DAY_NO_CH));
            updateTimeList.add(DateUtil.format(dateNow, DateUtil.DEFAULT_YEAR_MONTH_DAY_NO_CH));
        }
        return updateTimeList;
    }

    /**
     * SAP 的 JSON 数据转 实体
     *
     * @param jsonArray
     */
    private Map<String, MaterialDto> sapDataToEntityList(JSONArray jsonArray, AbstractCrmUserIdentity crmUserIdentity) {
        if (CollectionUtil.isEmpty(jsonArray)) {
            return Maps.newHashMap();
        }
        List<SapMaterialVo> sapCostCenterVoList = jsonArray.toJavaList(SapMaterialVo.class);
        Map<String, MaterialDto> centerMap = Maps.newConcurrentMap();
        String dataSource = ExternalSystemEnum.SAP.getCode();
        BigDecimal hundred = BigDecimal.valueOf(100);
        Date nowDate = new Date();
        Date endTime = DateUtil.parse(DateUtil.MAX_END_TIME, DateUtil.DEFAULT_DATE_ALL_PATTERN);
        sapCostCenterVoList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getMATNR()))
                .forEach(vo -> {
                    MaterialDto dto = this.buildMaterialDto(vo);
                    dto.setStartTime(nowDate);
                    if (StringUtil.isNotEmpty(vo.getZSL())) {
                        BigDecimal taxRate = new BigDecimal(vo.getZSL())
                                .setScale(2, BigDecimal.ROUND_HALF_UP);
                        dto.setTaxRate(taxRate);
                        dto.setTaxRateStr(taxRate.multiply(hundred)
                                .setScale(0, BigDecimal.ROUND_HALF_UP).toString() + "%");
                    }
                    this.setBaseInfo(vo, dto, crmUserIdentity);
                    dto.setDataSource(dataSource);
                    dto.setSyncUpdateTime(nowDate);
                    this.buildDetailDto(vo, dto, crmUserIdentity);
                    if (EnableStatusEnum.DISABLE.getCode().equals(dto.getEnableStatus())) {
                        dto.setEndTime(nowDate);
                    } else {
                        dto.setEndTime(endTime);
                    }
                    centerMap.put(dto.getMaterialCode(), dto);
                });
        return centerMap;
    }

    /**
     * 构建物料明细信息
     *
     * @param vo
     * @param dto
     */
    private void buildDetailDto(SapMaterialVo vo, MaterialDto dto, AbstractCrmUserIdentity crmUserIdentity) {
        if (Objects.isNull(vo)
                || Objects.isNull(dto)
                || CollectionUtil.isEmpty(vo.getITEM())) {
            return;
        }
        List<MaterialDetailDto> detailList = Lists.newArrayList();
        vo.getITEM().forEach(item -> {
            MaterialDetailDto detailDto = new MaterialDetailDto();
            this.setBaseInfo(vo, detailDto, crmUserIdentity);
            detailDto.setMaterialCode(dto.getMaterialCode());
            BooleanEnum booleanEnum = BooleanEnum.FALSE;
            if (StringUtil.isNotEmpty(item.getZSFGS())) {
                booleanEnum = BooleanEnum.TRUE;
            }
            detailDto.setCostLabel(booleanEnum.getCapital());
            detailDto.setFactoryTypeCode(item.getWERKS());
            detailDto.setId(detailDto.getMaterialCode() + "_" + detailDto.getFactoryTypeCode());
            if (StringUtil.isNotEmpty(item.getSTPRS())) {
                detailDto.setCostPrice(new BigDecimal(item.getSTPRS()));
            }
            detailDto.setCostPriceUnit(item.getPEINH());
            if (EnableStatusEnum.ENABLE.getCode().equals(detailDto.getEnableStatus())) {
                EnableStatusEnum enableStatusEnum = EnableStatusEnum.ENABLE;
                //为空不冻结，不为空冻结
                if (StringUtil.isNotEmpty(item.getMMSTA())) {
                    enableStatusEnum = EnableStatusEnum.DISABLE;
                }
                detailDto.setEnableStatus(enableStatusEnum.getCode());
            }
            detailList.add(detailDto);
        });

        dto.setDetailList(detailList);
    }

    /**
     * 构建实体
     *
     * @param vo
     * @return
     */
    private MaterialDto buildMaterialDto(SapMaterialVo vo) {
        MaterialDto dto = new MaterialDto();
        dto.setId(UuidCrmUtil.general());
        dto.setMaterialCode(vo.getMATNR());
        dto.setUnitTypeCode(vo.getMATNR());
        dto.setMaterialName(vo.getMAKTX());
        dto.setSimpleName(vo.getBRNAM());
        dto.setFourLevelCode(vo.getZZSJFLID());
        dto.setFourLevelName(vo.getZZSJFL());
        dto.setConversionValue(vo.getZZHZ());
        dto.setMaterialType(vo.getMTART());
        dto.setStandardUnit(vo.getMEINS());
        dto.setMaterialGroupCode(vo.getMATKL());
        dto.setMaterialGroupName(vo.getMATKLX());
        dto.setFinanceIndex(vo.getZCPSJ_CW());
        dto.setBarCode(vo.getBCODE());
        dto.setGrossWeight(vo.getBRGEW());
        dto.setNetWeight(vo.getNTGEW());
        dto.setWeightUnit(vo.getGEWEI());
        dto.setCapacity(vo.getZVOLUME());
        dto.setLongMetre(vo.getZLENGTH());
        dto.setWideMetre(vo.getZWIDTH());
        dto.setHighMetre(vo.getZHIGH());

        dto.setProductSmallClassCode(vo.getZCPXLID());
        dto.setProductSmallClassName(vo.getZCPXL());
        dto.setProductPhaseCode(vo.getZFLID());
        dto.setProductPhaseName(vo.getZFLMC());
        return dto;
    }


    /**
     * 构建body参数
     *
     * @param updateTimeList 更新时间  yyyyMMdd 集合
     * @return
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/27 15:17
     */
    private String buildBodyJson(List<String> updateTimeList, String factoryTypeCode) {

        String dateNow = DateUtil.dateStrNowYYYYMMDD();
        JSONObject ctrl = new JSONObject();
        ctrl.put("SYSID", CommonConstant.CURRENT_SYSTEM);
        ctrl.put("REVID", ExternalSystemEnum.SAP.getCode());
        ctrl.put("FUNID", ProductConstant.MATERIAL_INTERFACE);
        ctrl.put("INFID", UuidCrmUtil.general());
        ctrl.put("UNAME", CommonConstant.CURRENT_SYSTEM);
        ctrl.put("DATUM", dateNow);
        ctrl.put("UZEIT", DateUtil.dateStrNowHms());
        ctrl.put("KEYID", "");
        ctrl.put("TABIX", 0);
        ctrl.put("MSGTY", "");
        ctrl.put("MSAGE", "");
        JSONArray jsonArray = new JSONArray();
        updateTimeList.forEach(updateTime -> {
            JSONObject data = new JSONObject();
            //物料编号
            data.put("MATNR", "");
            //更新日期  yyyyMMdd
            data.put("LAEDA", updateTime);
            //物料类型
            data.put("MTART", "");
            //工厂
            data.put("WERKS", factoryTypeCode);
            jsonArray.add(data);
        });

        JSONObject jsonObject = new JSONObject();
        jsonObject.put(RyConstant.SAP_HEAD_DATA, ctrl);
        jsonObject.put(RyConstant.SAP_DETAIL_DATA, jsonArray);
        return jsonObject.toJSONString();
    }


    /**
     * 设置基础信息
     *
     * @param dto
     */
    private void setBaseInfo(SapMaterialVo item, TenantFlagOpDto dto, AbstractCrmUserIdentity crmUserIdentity) {
        Date date = new Date();
        dto.setId(UuidCrmUtil.general());
        dto.setTenantCode(TenantUtils.getTenantCode());

        dto.setCreateAccount(crmUserIdentity.getUsername());
        dto.setCreateName(crmUserIdentity.getRealName());
        dto.setCreateTime(date);
        dto.setModifyAccount(crmUserIdentity.getUsername());
        dto.setModifyName(crmUserIdentity.getRealName());
        dto.setModifyTime(date);

        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());

        //1、销售总体冻结 || 集中冻结 || 中心记账冻结，冻结则该客户编码的数据全部为禁用状态
        //2、销售选定冻结，则只针对该条数据冻结
        EnableStatusEnum enableStatusEnum = EnableStatusEnum.ENABLE;
        //为空不冻结，不为空冻结
        if (StringUtil.isNotEmpty(item.getMSTAE())) {
            enableStatusEnum = EnableStatusEnum.DISABLE;
        }
        dto.setEnableStatus(enableStatusEnum.getCode());
    }

    /**
     * 设置基础信息
     *
     * @param entity
     */
    private void setBaseInfo(TenantFlagOpEntity entity, AbstractCrmUserIdentity crmUserIdentity) {
        Date date = new Date();
        entity.setId(UuidCrmUtil.general());
        entity.setTenantCode(TenantUtils.getTenantCode());
        entity.setCreateAccount(crmUserIdentity.getUsername());
        entity.setCreateName(crmUserIdentity.getRealName());
        entity.setCreateTime(date);
        entity.setModifyAccount(crmUserIdentity.getUsername());
        entity.setModifyName(crmUserIdentity.getRealName());
        entity.setModifyTime(date);
        entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());

    }
}
