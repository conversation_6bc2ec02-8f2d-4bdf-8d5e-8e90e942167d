package com.biz.crm.mdm.business.product.local.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.product.local.entity.Product;
import com.biz.crm.mdm.business.product.local.mapper.ProductMapper;
import com.biz.crm.mdm.business.product.sdk.dto.ContractProductQueryDto;
import com.biz.crm.mdm.business.product.sdk.dto.ProductPaginationDto;
import com.biz.crm.mdm.business.product.sdk.dto.ProductQueryDto;
import com.biz.crm.mdm.business.product.sdk.enums.IsShelfEnum;
import com.biz.crm.mdm.business.product.sdk.vo.ProductItemVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPriceVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * mdm_product 商品主数据(repository)
 *
 * <AUTHOR>
 * @since 2021-10-13 15:38:57
 */
@Component
public class ProductRepository extends ServiceImpl<ProductMapper, Product> {

    private static final Integer SIZE = 1000;

    /**
     * 分页
     *
     * @param pageable
     * @param dto
     * @return
     */
    public Page<Product> findByConditions(Pageable pageable, ProductPaginationDto dto) {
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
        dto = Optional.ofNullable(dto).orElse(new ProductPaginationDto());
        Page<Product> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        if (StringUtils.isEmpty(dto.getTenantCode())) {
            dto.setTenantCode(TenantUtils.getTenantCode());
        }
        if (StringUtils.isEmpty(dto.getDelFlag())) {
            dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        return baseMapper.findByConditions(page, dto);
    }

    public Product findById(String id) {
        return this.lambdaQuery()
                .eq(Product::getId, id)
                .eq(Product::getTenantCode, TenantUtils.getTenantCode())
                .one();
    }

    /**
     * 根据编码集合获取商品集合
     *
     * @param productCodes
     * @return
     */
    public List<Product> findByProductCodes(List<String> productCodes) {
        if (CollectionUtil.isEmpty(productCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(Product::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(Product::getTenantCode, TenantUtils.getTenantCode())
                .in(Product::getProductCode, productCodes)
                .list();
    }

    /**
     * 根据编码集合获取商品集合
     *
     * @param productCodes
     * @return
     */
    public List<Product> findAllByProductCodes(List<String> productCodes) {
        if (CollectionUtil.isEmpty(productCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(Product::getTenantCode, TenantUtils.getTenantCode())
                .in(Product::getProductCode, productCodes)
                .list();
    }

    /**
     * 根据id或编码集合获取商品集合
     *
     * @param ids
     * @param productCodes
     * @return
     */
    public List<Product> findByIdsOrProductCodes(List<String> ids, List<String> productCodes) {
        if (CollectionUtil.isEmpty(ids)
                && CollectionUtil.isEmpty(productCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(Product::getTenantCode, TenantUtils.getTenantCode())
                .in(CollectionUtils.isNotEmpty(ids), Product::getId, ids)
                .in(CollectionUtils.isNotEmpty(productCodes), Product::getProductCode, productCodes)
                .list();
    }

    /**
     * 根据id集合 更新组织启用/禁用状态
     *
     * @param ids
     * @param enable
     */
    public void updateEnableStatusByIds(List<String> ids, EnableStatusEnum enable) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        this.lambdaUpdate()
                .in(Product::getId, ids)
                .eq(Product::getTenantCode, TenantUtils.getTenantCode())
                .set(Product::getEnableStatus, enable.getCode())
                .update();
    }

    /**
     * 逻辑删除
     *
     * @param ids
     */
    public void updateDelFlagByIds(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        this.lambdaUpdate()
                .in(Product::getId, ids)
                .eq(Product::getTenantCode, TenantUtils.getTenantCode())
                .set(Product::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
                .set(Product::getEnableStatus, EnableStatusEnum.DISABLE.getCode())
                .set(Product::getIsShelf, IsShelfEnum.DOWN.getCode())
                .update();
    }

    /**
     * 更新上下加状态
     *
     * @param ids
     * @param code
     */
    public void updateIsShelfByIds(List<String> ids, String code) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        this.lambdaUpdate().in(Product::getId, ids)
                .eq(Product::getTenantCode, TenantUtils.getTenantCode())
                .set(Product::getIsShelf, code)
                .update();
    }

    /**
     * 根据商品编码更新上下加状态
     *
     * @param productCodeList
     * @param code
     */
    public void updateIsShelfByByProductCodes(List<String> productCodeList, String code) {
        if (CollectionUtil.isEmpty(productCodeList)) {
            return;
        }
        this.lambdaUpdate()
                .in(Product::getProductCode, productCodeList)
                .eq(Product::getTenantCode, TenantUtils.getTenantCode())
                .set(Product::getIsShelf, code)
                .update();
    }

    /**
     * 根据商品层级编码或删除标获取匹配商品数量
     *
     * @param productLevelCodes
     * @param delFlag
     * @return
     */
    public Integer countByProductLevelCodesAndDelFlag(List<String> productLevelCodes, String delFlag) {
        if (CollectionUtil.isEmpty(productLevelCodes)) {
            return 0;
        }
        return lambdaQuery().eq(StringUtils.isNotBlank(delFlag), Product::getDelFlag, delFlag)
                .eq(Product::getTenantCode, TenantUtils.getTenantCode())
                .in(CollectionUtils.isNotEmpty(productLevelCodes), Product::getProductLevelCode, productLevelCodes)
                .count();
    }

    public List<ProductVo> findByProductLevelCodes(List<String> productLevelCodeList) {
        if (CollectionUtil.isEmpty(productLevelCodeList)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findByProductLevelCodes(productLevelCodeList, TenantUtils.getTenantCode(), DelFlagStatusEnum.NORMAL.getCode());
    }

    /**
     * 获取商品对应的商品层级编码集合
     *
     * @param productCodeSet
     * @return
     */
    public Set<String> findProductLevelCodeSetByProductCodes(Set<String> productCodeSet) {
        if (CollectionUtil.isEmpty(productCodeSet)) {
            return Sets.newHashSet();
        }
        return this.baseMapper.findProductLevelCodeSetByProductCodes(productCodeSet, TenantUtils.getTenantCode());
    }

    /**
     * 通过商品编码获取
     *
     * @param code
     * @param delFlag
     * @return
     */
    public Product findByProductCode(String code, String delFlag) {
        if (StringUtil.isEmpty(code)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(Product::getProductCode, code)
                .eq(Product::getTenantCode, TenantUtils.getTenantCode())
                .eq(Product::getDelFlag, delFlag)
                .one();
    }

    /**
     * 根据ProductQueryDto获取商品主信息
     *
     * @param dto
     * @return
     */
    public List<Product> findByProductQueryDto(ProductQueryDto dto) {
        dto.setTenantCode(TenantUtils.getTenantCode());
        List<Product> list = Lists.newLinkedList();
        if (CollectionUtils.isNotEmpty(dto.getProductCodeList())) {
            for (List<String> item : Lists.partition(Lists.newArrayList(dto.getProductCodeList()), SIZE)) {
                dto.setProductCodeList(item);
                List<Product> cur = this.baseMapper.findByProductQueryDto(dto);
                if (CollectionUtils.isNotEmpty(cur)) {
                    list.addAll(cur);
                }
            }
        } else {
            list = this.baseMapper.findByProductQueryDto(dto);
        }
        return list;
    }

    /**
     * 合同商品信息获取
     *
     * @param dto
     * @return
     */
    public List<Product> findByContractProductQueryDto(ContractProductQueryDto dto) {
        String tenantCode = TenantUtils.getTenantCode();
        String delFlag = DelFlagStatusEnum.NORMAL.getCode();
        String enableStatus = EnableStatusEnum.ENABLE.getCode();
        String shelfFlag = IsShelfEnum.UP.getCode();
        return this.baseMapper.findByContractProductQueryDto(
                dto, tenantCode, delFlag, enableStatus, shelfFlag);
    }

    public List<Product> findDetailByBarCodes(Set<String> barCodes) {
        if (CollectionUtil.isEmpty(barCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(Product::getBarCode, barCodes)
                .eq(Product::getTenantCode, TenantUtils.getTenantCode())
                .eq(Product::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }


    public void saveBatchXml(List<Product> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return;
        }
        Lists.partition(entityList, CommonConstant.MAX_PAGE_SIZE).forEach(list -> {
            this.baseMapper.insertBatchSomeColumn(list);
        });

    }

    public void updateBatchXml(List<Product> updateList) {
        if (CollectionUtil.isEmpty(updateList)) {
            return;
        }
        Lists.partition(updateList, CommonConstant.MAX_PAGE_SIZE).forEach(this::updateBatchById);
    }

    public List<ProductItemVo> findItemByNames(List<String> items) {
        return this.baseMapper.findItemByNames(items);
    }

    public List<ProductVo> findProductByItemCodeAndProductLevelCodes(String itemCode, Set<String> productLevelCodeSet) {
        return this.baseMapper.findProductByItemCodeAndProductLevelCodes(itemCode, productLevelCodeSet);
    }

    public List<ProductVo> findByMaterialCodes(List<String> materialCodes) {
        return this.baseMapper.findByMaterialCodes(materialCodes, TenantUtils.getTenantCode());
    }

    public List<String> findByProductSmallClass(List<String> productSmallClassList) {
        return this.baseMapper.findByProductSmallClass(productSmallClassList, TenantUtils.getTenantCode());
    }

    public List<Product> findByMaterialCodes(Set<String> materialCodeSet) {
        if (CollectionUtil.isEmpty(materialCodeSet)) {
            return Lists.newArrayList();
        }
        return lambdaQuery()
                .eq(Product::getTenantCode, TenantUtils.getTenantCode())
                .in(Product::getMaterialCode, materialCodeSet)
                .list();
    }

    public Page<ProductVo> findAllowProductByDto(ProductPaginationDto dto) {
        return this.baseMapper.findAllowProductByDto(new Page<>(dto.getPage(), dto.getSize()), dto);
    }

    public List<Product> findByProductSmallClassCodes(List<String> productSmallClassCodes) {
        if (CollectionUtil.isEmpty(productSmallClassCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(Product::getProductSmallClassCode, productSmallClassCodes)
                .eq(Product::getTenantCode, TenantUtils.getTenantCode())
                .eq(Product::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }

    public List<ProductVo> findAllDownListByProductLevelCodes(ProductPaginationDto dto) {
        if (CollectionUtil.isEmpty(dto.getProductLevelCodes())) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findAllDownListByProductLevelCodes(dto, TenantUtils.getTenantCode());
    }

    public List<Product> findByProductNames(Set<String> productNames) {
        if (CollectionUtils.isEmpty(productNames)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(Product::getTenantCode, TenantUtils.getTenantCode())
                .eq(Product::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .in(Product::getProductName, productNames)
                .list();
    }

    public List<ProductPriceVo> findProductPriceByDto(ProductPriceVo dto) {
        return this.baseMapper.findProductPriceByDto(dto);
    }


    public List<Product> productPhaseCodes(List<String> productPhaseCodes){
        return this.lambdaQuery()
                .eq(Product::getProductPhaseCode,productPhaseCodes)
                .eq(Product::getDelFlag,DelFlagStatusEnum.NORMAL.getCode())
                .list();
    }
}
