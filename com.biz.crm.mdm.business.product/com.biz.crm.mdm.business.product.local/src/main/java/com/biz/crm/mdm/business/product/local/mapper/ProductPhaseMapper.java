package com.biz.crm.mdm.business.product.local.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.mapper.BusinessBaseMapper;
import com.biz.crm.mdm.business.product.local.entity.ProductPhase;
import com.biz.crm.mdm.business.product.local.entity.ProductSmallClass;
import com.biz.crm.mdm.business.product.sdk.dto.ProductPhaseDto;
import com.biz.crm.mdm.business.product.sdk.dto.ProductSmallClassDto;
import com.biz.crm.mdm.business.product.sdk.dto.ProductSmallClassSelectDto;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductSmallClassSelectVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductSmallClassVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * mdm_product_small_class 商品小类主数据(Product)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-10-13 15:38:54
 */
public interface ProductPhaseMapper extends BusinessBaseMapper<ProductPhase> {

    /**
     * 分页列表
     *
     * @param page 分页信息
     * @param dto  分页参数dto
     * @return 分页列表
     */
    Page<ProductPhaseVo> findByConditions(Page<ProductPhaseVo> page, @Param("dto") ProductPhaseDto dto);

    List<ProductPhaseVo> findListByZeroLevels(@Param("zeroLevelCodes") Set<String> zeroLevelCodes,@Param("tenantCode")String tenantCode);

    List<ProductPhaseVo> findListByCanShareIsNull(@Param("tenantCode")String tenantCode);

    List<ProductPhaseVo> loadCacheProductPhase(@Param("tenantCode")String tenantCode,@Param("enableStatus")String enableStatus);

}
