package com.biz.crm.mdm.business.product.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.product.local.service.MaterialSapDataService;
import com.biz.crm.mdm.business.product.sdk.dto.*;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductItemVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductMaterialVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPriceVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * 商品主数据(Product)表控制层
 *
 * <AUTHOR>
 * @since 2021-10-13 16:48:13
 */
@Slf4j
@Api(tags = "商品管理: ProductVo: 商品管理")
@RestController
@RequestMapping(value = {"/v1/product/product"})
public class ProductVoController {

    @Autowired(required = false)
    private ProductVoService productVoService;

    @Autowired(required = false)
    private MaterialSapDataService materialSapDataService;

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "详情")
    @GetMapping(value = {"/findDetailById"})
    public Result<ProductVo> findDetailById(@RequestParam("id") String id) {
        ProductVo vo = null;
        List<String> ids = Lists.newLinkedList();
        if (StringUtils.isNotBlank(id)) {
            ids.add(id);
        }
        try {
            List<ProductVo> list = productVoService.findDetailsByIdsOrProductCodes(ids, null);
            Optional<ProductVo> optional = list.stream().findFirst();
            if (optional.isPresent()) {
                vo = optional.get();
            }
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "根据商品69码获取商品信息")
    @GetMapping(value = {"/findDetailByBarCode"})
    public Result<List<ProductVo>> findDetailByBarCode(@RequestParam("barCode") String barCode) {
        try {
            List<ProductVo> list = productVoService.findDetailByBarCode(barCode);
            return Result.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "根据商品69码集合获取商品信息")
    @GetMapping(value = {"/findDetailByBarCodes"})
    public Result<List<ProductVo>> findDetailByBarCodes(@RequestParam("barCodes") Set<String> barCodes) {
        try {
            List<ProductVo> list = productVoService.findDetailByBarCodes(barCodes);
            return Result.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据商品商品id集合或编码集合获取商品信息
     *
     * @param ids
     * @param codes
     * @return
     */
    @ApiOperation(value = "根据商品商品id集合或编码集合获取商品信息")
    @GetMapping(value = {"/findDetailsByIdsOrCodes"})
    public Result<List<ProductVo>> findDetailsByIdsOrCodes(@RequestParam("ids") List<String> ids, @RequestParam("codes") List<String> codes) {
        try {
            List<ProductVo> list = productVoService.findDetailsByIdsOrProductCodes(ids, codes);
            return Result.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据商品商品id集合或编码集合获取商品信息
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "根据商品商品id集合或编码集合获取商品信息_POST请求")
    @PostMapping(value = {"/findProductDetailsByIdsOrCodes"})
    public Result<List<ProductVo>> findProductDetailsByIdsOrCodes(@RequestBody ProductDetailDto dto) {
        try {
            List<ProductVo> list = productVoService.findDetailsByIdsOrProductCodes(dto.getIds(), dto.getCodes());
            return Result.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 创建
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "创建")
    @PostMapping(value = "")
    public Result<ProductVo> create(@RequestBody ProductDto dto) {
        try {
            ProductVo current = this.productVoService.create(dto);
            return Result.ok(current);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 更新
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "更新")
    @PatchMapping(value = "")
    public Result<ProductVo> update(@RequestBody ProductDto dto) {
        try {
            ProductVo current = this.productVoService.update(dto);
            return Result.ok(current);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据商品层级获取对应的商品信息
     *
     * @param productLevelCodeList
     * @return
     */
    @ApiOperation(value = "根据商品层级获取对应的商品信息")
    @GetMapping("/findByProductLevelCodes")
    public Result<List<ProductVo>> findByProductLevelCodes(@RequestParam("productLevelCodeList") List<String> productLevelCodeList) {
        try {
            return Result.ok(productVoService.findByProductLevelCodes(productLevelCodeList));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据商品商品编码集合获取商品主信息+层级信息
     *
     * @param productCodeList
     * @return
     */
    @ApiOperation(value = "根据商品商品编码集合获取商品主信息+层级信息")
    @PostMapping(value = {"/findMainDetailsByProductCodes"})
    public Result<List<ProductVo>> findMainDetailsByProductCodes(@RequestBody List<String> productCodeList) {
        try {
            List<ProductVo> list = productVoService.findMainDetailsByProductCodes(productCodeList);
            return Result.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "根据商品编码或id查商品")
    @PostMapping(value = {"/findMainDetailsByDto"})
    public Result<List<ProductVo>> findMainDetailsByDto(@RequestBody ProductDetailQueryDto dto) {
        try {
            List<ProductVo> list = productVoService.findMainDetailsByDto(dto);
            return Result.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据ProductQueryDto获取商品主信息
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "根据ProductQueryDto获取商品主信息")
    @PostMapping(value = {"/findByProductQueryDto"})
    public Result<List<ProductVo>> findByProductQueryDto(ProductQueryDto dto) {
        try {
            List<ProductVo> list = productVoService.findByProductQueryDto(dto);
            return Result.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据ProductQueryDto获取商品主信息(针对请求数据量特别大的情况使用)
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "根据ProductQueryDto获取商品主信息")
    @PostMapping(value = {"/findByQueryDto"})
    public Result<List<ProductVo>> findByQueryDto(@RequestBody ProductQueryDto dto) {
        try {
            List<ProductVo> list = productVoService.findByProductQueryDto(dto);
            return Result.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据ProductDetailQueryDto获取商品信息
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "根据ProductDetailQueryDto获取商品信息")
    @GetMapping(value = {"/findDetailsByProductDetailQueryDto"})
    public Result<List<ProductVo>> findDetailsByProductDetailQueryDto(ProductDetailQueryDto dto) {
        try {
            List<ProductVo> list = productVoService.findDetailsByProductDetailQueryDto(dto);
            return Result.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据层级获取层级及下级所有的上架且启用的产品集合
     *
     * @param productLevelCodes
     * @return
     */
    @ApiOperation(value = "根据层级获取层级及下级所有的上架且启用的产品集合")
    @GetMapping(value = {"/findAllowSaleProductByProductLevelCodes"})
    public Result<Map<String, String>> findAllowSaleProductByProductLevelCodes(
            @RequestParam("productLevelCodes") Set<String> productLevelCodes) {
        try {
            Map<String, String> map =
                    productVoService.findAllowSaleProductByProductLevelCodes(productLevelCodes);
            return Result.ok(map);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 查询分页列表
     *
     * @param pageable
     * @param paginationDto
     * @return
     */
    @ApiOperation(value = "查询分页列表")
    @GetMapping(value = {"/findPageProductVoByConditions"})
    public Result<Page<ProductVo>> findByConditions(@PageableDefault(50) Pageable pageable, ProductPaginationDto paginationDto) {
        try {
            paginationDto = Optional.ofNullable(paginationDto).orElse(new ProductPaginationDto());
            paginationDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            Page<ProductVo> result = this.productVoService.findByConditions(pageable, paginationDto);
            return Result.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取商品对应层级及其上级层级编码集合
     *
     * @param productCodeSet 商品编码
     * @return
     */
    @ApiOperation(value = "获取商品对应层级及其上级层级编码集合")
    @GetMapping(value = {"/findParentLevelCodeSetByProductCodes"})
    public Result<Set<String>> findParentLevelCodeSetByProductCodes(Set<String> productCodeSet) {
        try {
            Set<String> result = this.productVoService
                    .findParentLevelCodeSetByProductCodes(productCodeSet);
            return Result.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    /**
     * 获取商品编码查询商品关联的所有物料
     *
     * @param productCodes 商品编码
     * @return
     */
    @ApiOperation(value = "获取商品编码查询商品关联的所有物料")
    @GetMapping(value = {"/findMaterialProductVoByProductCodes"})
    public Result<List<ProductMaterialVo>> findMaterialProductVoByProductCodes(@RequestParam("productCodes") List<String> productCodes) {
        try {
            List<ProductMaterialVo> productMaterialVos = this.productVoService
                    .findMaterialProductVoByProductCodes(productCodes);
            return Result.ok(productMaterialVos);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "拉取SAP物料", httpMethod = "GET")
    @GetMapping("/pullMaterial")
    public Result pullMaterial(@RequestParam(value = "isAll", required = false) String isAll) {
        try {
            this.materialSapDataService.pullMaterial(isAll);
            return Result.ok("拉取SAP物料成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    @ApiOperation(value = "通过品项查询商品信息")
    @PostMapping("findItemListByItemNames")
    public Result<List<ProductItemVo>> findItemListByItemNames(@RequestBody List<String> itemNames) {
        return Result.ok(productVoService.findItemListByItemNames(itemNames));
    }

    @ApiOperation(value = "通过品项编码查询商品信息")
    @PostMapping("findItemListByItemCodes")
    public Result<List<ProductItemVo>> findItemListByItemCodes(@RequestBody List<String> itemCodes) {
        return Result.ok(productVoService.findItemListByItemCodes(itemCodes));
    }


    @ApiOperation(value = "根据产品编码获取单位集合", httpMethod = "GET")
    @GetMapping("/getProductUnitList")
    public Result<List<String>> getProductUnitList(@RequestParam(value = "productCode") String productCode) {
        try {
            return Result.ok(this.productVoService.getProductUnitList(productCode));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "根据产品编码集合获取单位集合", httpMethod = "GET")
    @GetMapping("/getProductsUnitList")
    public Result<Map<String, List<String>>> getProductsUnitList(@RequestParam(value = "productCodes") List<String> productCodes) {
        try {
            return Result.ok(this.productVoService.getProductsUnitList(productCodes));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询可购商品")
    @PostMapping("findAllowProductByDto")
    public Result<Page<ProductVo>> findAllowProductByDto(@RequestBody ProductPaginationDto dto) {
        return Result.ok(productVoService.findAllowProductByDto(dto));
    }

    @ApiOperation(value = "分页查询可购商品-可过滤业态、组装价格")
    @PostMapping("findAllowProductPageByDto")
    public Result<Page<ProductVo>> findAllowProductPageByDto(@RequestBody ProductPaginationDto dto) {
        return Result.ok(productVoService.findAllowProductPageByDto(dto));
    }

    @ApiOperation(value = "通过物料编码查询商品信息")
    @PostMapping("findByMaterialCodes")
    public Result<List<ProductVo>> findByMaterialCodes(@RequestBody List<String> materialCodes) {
        return Result.ok(productVoService.findByMaterialCodes(materialCodes));
    }

    @ApiOperation(value = "查询产品小类")
    @PostMapping("findByProductSmallClass")
    public Result<List<String>> findByProductSmallClass(@RequestBody List<String> smallClassList) {
        return Result.ok(productVoService.findByProductSmallClass(smallClassList));
    }

    @ApiOperation(value = "根据产品小类编码查询产品")
    @PostMapping("findByProductSmallClassCodes")
    public Result<List<ProductVo>> findByProductSmallClassCodes(@RequestBody List<String> productSmallClassCodes) {
        return Result.ok(productVoService.findByProductSmallClassCodes(productSmallClassCodes));
    }

    @ApiOperation(value = "根据产品编码查询产品业态关系")
    @PostMapping("findBusinessTypeByProductCodes")
    public Result<Map<String, String>> findBusinessTypeByProductCodes(@RequestBody List<String> productCodeList) {
        return Result.ok(productVoService.findBusinessTypeByProductCodes(productCodeList));
    }

    @ApiOperation(value = "根据产品层级编码查询下级所有产品")
    @PostMapping("findAllDownListByProductLevelCodes")
    public Result<List<ProductVo>> findAllDownListByProductLevelCodes(@RequestBody ProductPaginationDto dto) {
        return Result.ok(productVoService.findAllDownListByProductLevelCodes(dto));
    }

    @ApiOperation(value = "根据商品名称查询")
    @PostMapping("findByProductNames")
    public Result<List<ProductVo>> findByProductNames(@RequestBody Set<String> productNames) {
        return Result.ok(this.productVoService.findByProductNames(productNames));
    }

    @ApiOperation(value = "商品破价价格查询")
    @PostMapping("findProductPriceByDto")
    public Result<List<ProductPriceVo>> findProductPriceByDto(@RequestBody ProductPriceVo dto) {
        return Result.ok(this.productVoService.findProductPriceByDto(dto));
    }


    @ApiOperation(value = "通过品项编码查询商品信息")
    @PostMapping("findProductListByProductPhaseCodes")
    public Result<List<ProductVo>> findProductListByProductPhaseCodes(@RequestBody List<String> productPhaseCodes) {
        return Result.ok(productVoService.findProductListByProductPhaseCodes(productPhaseCodes));
    }

}
