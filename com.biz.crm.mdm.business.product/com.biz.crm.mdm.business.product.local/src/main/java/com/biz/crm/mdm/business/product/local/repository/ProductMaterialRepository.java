package com.biz.crm.mdm.business.product.local.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.product.local.entity.ProductMaterial;
import com.biz.crm.mdm.business.product.local.mapper.ProductMaterialMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import java.util.List;
import java.util.Set;

import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

/**
 * 商品物料表(repository)
 *
 * <AUTHOR>
 * @since 2021-10-14 16:06:46
 */
@Component
public class ProductMaterialRepository extends ServiceImpl<ProductMaterialMapper, ProductMaterial> {

  public List<ProductMaterial> findByProductCodes(List<String> productCodeList) {
    return lambdaQuery()
        .eq(ProductMaterial::getTenantCode, TenantUtils.getTenantCode())
        .in(ProductMaterial::getProductCode, productCodeList)
        .list();
  }

  public void deleteByProductCodes(List<String> productCodes) {
    LambdaQueryWrapper<ProductMaterial> lambdaQuery = Wrappers.lambdaQuery();
    lambdaQuery
        .eq(ProductMaterial::getTenantCode, TenantUtils.getTenantCode())
        .in(ProductMaterial::getProductCode, productCodes);
    baseMapper.delete(lambdaQuery);
  }

  /**
   * 根据物料编码获取匹配的数量
   *
   * @param materialCodes
   * @return
   */
  public Integer countByMaterialCodes(List<String> materialCodes) {
    return lambdaQuery()
        .eq(ProductMaterial::getTenantCode, TenantUtils.getTenantCode())
        .in(ProductMaterial::getMaterialCode, materialCodes)
        .count();
  }

  /**
   * 获取物料关联的商品编码集合
   *
   * @param materialCode
   * @return
   */
  public Set<String> findProductCodeByMaterialCode(String materialCode) {
    return this.baseMapper.findProductCodeByMaterialCode(
        materialCode,
        TenantUtils.getTenantCode(),
        EnableStatusEnum.ENABLE.getCode(),
        DelFlagStatusEnum.NORMAL.getCode());
  }

  public List<ProductMaterial> findByMaterialCodes(Set<String> materialCodeSet) {
    if (CollectionUtil.isEmpty(materialCodeSet)) {
      return Lists.newArrayList();
    }
    return lambdaQuery()
            .eq(ProductMaterial::getTenantCode, TenantUtils.getTenantCode())
            .in(ProductMaterial::getMaterialCode, materialCodeSet)
            .list();
  }


  public void deleteByCodes(Set<String> codeList) {
    if (CollectionUtil.isEmpty(codeList)){
      return;
    }
    this.lambdaUpdate()
            .in(ProductMaterial::getMaterialCode, codeList)
            .remove();
  }

  public void saveBatchXml(List<ProductMaterial> saveList) {
    if (CollectionUtil.isEmpty(saveList)) {
      return;
    }
    Lists.partition(saveList, CommonConstant.MAX_PAGE_SIZE).forEach(list -> {
      this.baseMapper.insertBatchSomeColumn(list);
    });
  }
}
