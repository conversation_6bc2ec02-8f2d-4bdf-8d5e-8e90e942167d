package com.biz.crm.mdm.business.product.local.service.notifier;

import com.biz.crm.mdm.business.material.sdk.event.MaterialEventListener;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialEventVo;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.product.local.service.ProductMaterialService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 物料通知实现
 *
 * <AUTHOR>
 * @date 2021/10/18
 */
@Component
public class MaterialEventListenerImpl implements MaterialEventListener {

  @Autowired(required = false) private ProductMaterialService productMaterialService;

  /**
   * 当删除时，触发事件
   *
   * @param materialEventVo
   * @return
   */
  @Override
  public void onDelete(MaterialEventVo materialEventVo) {
    List<MaterialVo> materialVos = materialEventVo.getMaterialVos();
    Validate.isTrue(CollectionUtils.isNotEmpty(materialVos), "触发参数不能为空");
    List<String> materialCodes =
        materialVos.stream()
            .filter(a -> StringUtils.isNotBlank(a.getMaterialCode()))
            .map(MaterialVo::getMaterialCode)
            .collect(Collectors.toList());
    Validate.isTrue(CollectionUtils.isNotEmpty(materialCodes), "物料编码编码参数不能为空");
    Integer count = productMaterialService.countByMaterialCodes(materialCodes);
    count = Optional.ofNullable(count).orElse(0);
    Validate.isTrue(count.compareTo(0) <= 0, "存在对应的商品信息,无法执行删除");
  }

  /**
   * 当启用时，触发事件
   *
   * @param materialEventVo
   * @return
   */
  @Override
  public void onEnable(MaterialEventVo materialEventVo) {
    // 无需实现
  }

  /**
   * 当禁用时，触发事件
   *
   * @param materialEventVo
   * @return
   */
  @Override
  public void onDisable(MaterialEventVo materialEventVo) {
    // 无需实现
  }

  /**
   * 当修改时，触发事件
   *
   * @param materialEventVo
   * @return
   */
  @Override
  public void onChange(MaterialEventVo materialEventVo) {
    // 无需实现
  }

  /**
   * 当新建时，触发事件
   *
   * @param materialEventVo
   * @return
   */
  @Override
  public void onCreate(MaterialEventVo materialEventVo) { }
}
