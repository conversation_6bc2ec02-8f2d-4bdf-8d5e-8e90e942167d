<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.product.local.mapper.ProductMaterialMapper">

  <select id="findProductCodeByMaterialCode" resultType="java.lang.String">
    select distinct a.product_code
    from mdm_product_material a
             left join mdm_product b
                       on a.product_code = b.product_code and a.tenant_code = b.tenant_code
    where b.tenant_code = #{tenantCode}
      and b.del_flag = #{delFlag}
      and b.enable_status = #{enableStatus}
      and a.material_code = #{materialCode}
  </select>
</mapper>
