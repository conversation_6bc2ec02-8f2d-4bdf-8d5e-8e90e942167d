<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.product.local.mapper.ProductPhaseMapper">

    <!--分页查询-->

    <select id="findByConditions" resultType="com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo">
        select mpp.*
        ,mpls.product_level_name as zeroLevelName
        from mdm_product_phase mpp
        left join mdm_product_level_sap mpls on
        mpls.product_level_code = mpp.zero_level_code and mpls.tenant_code = mpp.tenant_code
        and mpls.del_flag = '${@<EMAIL>()}'
        where mpp.del_flag = '${@<EMAIL>()}'
        and mpp.tenant_code = #{dto.tenantCode}
        <if test="dto.keyword != null and dto.keyword != ''">
            <bind name="likeKeyword" value="'%' + dto.keyword + '%'"/>
            and (mpp.product_phase_code like #{likeKeyword} or mpp.product_phase_name like #{likeKeyword})
        </if>
        <if test="dto.productPhaseCode != null and dto.productPhaseCode != ''">
            <bind name="productPhaseCode" value="'%' + dto.productPhaseCode + '%'"/>
            and mpp.product_phase_code like #{productPhaseCode}
        </if>
        <if test="dto.productPhaseName != null and dto.productPhaseName != ''">
            <bind name="productPhaseName" value="'%' + dto.productPhaseName + '%'"/>
            and mpp.product_phase_name like #{productPhaseName}
        </if>
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and mpp.enable_status = #{dto.enableStatus}
        </if>
        <if test="dto.productPhaseCodeSet != null and dto.productPhaseCodeSet.size()>0">
            and mpp.product_phase_code in
            <foreach collection="dto.productPhaseCodeSet" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </if>
        order by
        <if test="dto.selectedCodes != null and dto.selectedCodes.size > 0">
            CASE
            <foreach collection="dto.selectedCodes" item="item" index="index">
                WHEN mpp.product_phase_code = #{item} THEN ${index}
            </foreach>
            ELSE -1 END desc,
        </if>
        mpp.create_time desc,mpp.id desc
    </select>

    <select id="findListByZeroLevels" resultType="com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo">
        select * from mdm_product_phase
        <where>
            tenant_code = #{tenantCode}
            and del_flag = '${@<EMAIL>()}'
            and (can_share is null or can_share = 'N')
            <if test="zeroLevelCodes != null and zeroLevelCodes.size()>0">
                and zero_level_code in
                <foreach collection="zeroLevelCodes" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findListByCanShareIsNull" resultType="com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo">
        select * from mdm_product_phase
        <where>
            tenant_code = #{tenantCode}
            and del_flag = '${@<EMAIL>()}'
            and (can_share is null or can_share = 'N')
        </where>
    </select>

    <select id="loadCacheProductPhase" resultType="com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo">
        select mpp.*, mpls.product_level_name as zero_level_name
        from mdm_product_phase mpp
                 left join mdm_product_level_sap mpls
                           on mpls.product_level_code = mpp.zero_level_code and mpls.tenant_code = mpp.tenant_code
        where mpp.tenant_code = #{tenantCode}
          and mpp.enable_status = #{enableStatus}
    </select>
</mapper>
