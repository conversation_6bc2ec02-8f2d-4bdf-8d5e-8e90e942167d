package com.biz.crm.mdm.business.product.local.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.mdm.business.product.local.entity.ProductPhase;
import com.biz.crm.mdm.business.product.local.entity.ProductTag;
import com.biz.crm.mdm.business.product.local.mapper.ProductTagMapper;
import com.biz.crm.mdm.business.product.sdk.dto.ProductTagDto;
import com.biz.crm.mdm.business.product.sdk.vo.ProductTagVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 商品标签信息(repository)
 *
 * <AUTHOR>
 * @date 2021-12-02 17:07:24
 */
@Component
public class ProductTagRepository extends ServiceImpl<ProductTagMapper, ProductTag> {

    /**
     * 多条件分页查询
     *
     * @param pageable
     * @param dto
     * @return
     */
    public Page<ProductTagVo> findByConditions(Pageable pageable, ProductTagDto dto) {
        dto = Optional.ofNullable(dto).orElse(new ProductTagDto());
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
        if (StringUtil.isEmpty(dto.getTenantCode())) {
            dto.setTenantCode(TenantUtils.getTenantCode());
        }
        if (StringUtil.isEmpty(dto.getDelFlag())) {
            dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        }
        Page<ProductTagVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.baseMapper.findByConditions(page, dto);
    }

    /**
     * 根据名称查询
     *
     * @param name
     * @return
     */
    public List<ProductTag> findByName(String name) {
        if (StringUtil.isEmpty(name)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(ProductTag::getTagName, name)
                .eq(ProductTag::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(ProductTag::getTenantCode, TenantUtils.getTenantCode())
                .list();
    }

    /**
     * 修改启禁用
     *
     * @param ids
     * @param code
     */
    public void updateEnableStatusByIds(List<String> ids, String code) {
        if (CollectionUtil.isEmpty(ids)
                || StringUtil.isEmpty(code)) {
            return;
        }
        this.lambdaUpdate()
                .in(ProductTag::getId, ids)
                .eq(ProductTag::getTenantCode, TenantUtils.getTenantCode())
                .set(ProductTag::getEnableStatus, code)
                .update();
    }

    /**
     * 修改启禁用
     *
     * @param id
     * @param code
     */
    public ProductTag findByIdOrCode(String id, String code) {
        if (StringUtils.isAllEmpty(id, code)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(StringUtil.isNotEmpty(id), ProductTag::getId, id)
                .eq(StringUtil.isNotEmpty(code), ProductTag::getTagCode, code)
                .eq(ProductTag::getTenantCode, TenantUtils.getTenantCode())
                .one();
    }

    /**
     * 查看详情
     *
     * @param ids
     * @return
     */
    public List<ProductTag> findByIds(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(ProductTag::getId, ids)
                .eq(ProductTag::getTenantCode, TenantUtils.getTenantCode())
                .list();
    }

    /**
     * 根据编码获取详情
     *
     * @param tagCode
     * @return
     */
    public ProductTag findByTagCode(String tagCode) {
        if (StringUtils.isEmpty(tagCode)) {
            return null;
        }
        return this.lambdaQuery()
                .eq(ProductTag::getTagCode, tagCode)
                .eq(ProductTag::getTenantCode, TenantUtils.getTenantCode())
                .one();
    }

    /**
     * 根据编码获取详情
     *
     * @param tagCodes
     * @return
     */
    public List<ProductTag> findByTagCodes(List<String> tagCodes) {
        if (CollectionUtil.isEmpty(tagCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(ProductTag::getTagCode, tagCodes)
                .eq(ProductTag::getTenantCode, TenantUtils.getTenantCode())
                .list();
    }

    /**
     * 重构修改方法
     *
     * @param productTag
     * @param tenantCode
     */
    public void updateByIdAndTenantCode(ProductTag productTag, String tenantCode) {
        LambdaUpdateWrapper<ProductTag> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(ProductTag::getTenantCode, tenantCode);
        lambdaUpdateWrapper.eq(ProductTag::getId, productTag.getId());
        this.baseMapper.update(productTag, lambdaUpdateWrapper);
    }
}
