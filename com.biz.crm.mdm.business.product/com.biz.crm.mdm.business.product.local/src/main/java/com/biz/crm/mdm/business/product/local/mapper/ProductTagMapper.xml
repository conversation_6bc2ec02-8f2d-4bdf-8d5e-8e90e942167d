<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.product.local.mapper.ProductTagMapper">

    <select id="findByConditions" resultType="com.biz.crm.mdm.business.product.sdk.vo.ProductTagVo">
        select * from (
        select t.*,
        IFNULL(t.show_order, 9999) show_order_sort
        from mdm_product_tag t
        where t.tenant_code = #{dto.tenantCode}
        and t.del_flag = #{dto.delFlag}
        <if test="dto.tagCode != null and dto.tagCode != ''">
            <bind name="tagCode" value="'%' + dto.tagCode + '%'"/>
            and t.tag_code like #{tagCode}
        </if>
        <if test="dto.tagName != null and dto.tagName != ''">
            <bind name="tagName" value="'%' + dto.tagName + '%'"/>
            and t.tag_name like #{tagName}
        </if>
        <if test="dto.tagDescription != null and dto.tagDescription != ''">
            <bind name="tagDescription" value="'%' + dto.tagDescription + '%'"/>
            and t.tag_description like #{tagDescription}
        </if>
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and t.enable_status = #{dto.enableStatus}
        </if>) tt

        order by tt.show_order_sort asc,tt.id asc
    </select>
</mapper>
