package com.biz.crm.mdm.business.product.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Transient;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 商品主表实体
 *
 * <AUTHOR>
 * @date 2021/10/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("mdm_product")
@Api(tags = "商品主表实体")
@Table(name = "mdm_product", indexes = {
        @Index(name = "mdm_product_uq1", columnList = "product_code", unique = true),
        @Index(name = "mdm_product_uq2", columnList = "material_code", unique = true),
        @Index(name = "mdm_product_index1", columnList = "tenant_code,product_code", unique = true),
        @Index(name = "mdm_product_index2", columnList = "product_level_code", unique = false),
        @Index(name = "mdm_product_index3", columnList = "bar_code", unique = false),
})
@org.hibernate.annotations.Table(appliesTo = "mdm_product", comment = "商品主表")
public class Product extends TenantFlagOpEntity {

  private static final long serialVersionUID = 2274980263975908479L;

  @ApiModelProperty("商品编码")
  @TableField(value = "product_code")
  @Column(name = "product_code", columnDefinition = "VARCHAR(32) COMMENT '商品编码'")
  private String productCode;

  @ApiModelProperty("物料编码")
  @Column(name = "material_code", columnDefinition = "varchar(32) COMMENT '物料编码'")
  private String materialCode;

  @ApiModelProperty("产品小类编码")
  @Column(name = "product_small_class_code", columnDefinition = "varchar(32) COMMENT '产品小类编码'")
  private String productSmallClassCode;

  @ApiModelProperty("产品小类名称")
  @Column(name = "product_small_class_name", columnDefinition = "varchar(128) COMMENT '产品小类名称'")
  private String productSmallClassName;

  @ApiModelProperty("商品名称")
  @TableField(value = "product_name")
  @Column(name = "product_name", columnDefinition = "VARCHAR(128) COMMENT '商品名称'")
  private String productName;

  @ApiModelProperty("产品层级编码")
  @TableField(value = "product_level_code")
  @Column(name = "product_level_code", columnDefinition = "VARCHAR(32) COMMENT '产品层级编码'")
  private String productLevelCode;

  @ApiModelProperty("产品层级")
  @TableField(exist = false)
  @Transient
  private String productLevelName;

  @ApiModelProperty("商品类型")
  @TableField(value = "product_type")
  @Column(name = "product_type", columnDefinition = "VARCHAR(32) COMMENT '商品类型'")
  private String productType;

  @ApiModelProperty("销售单位")
  @TableField(value = "sale_unit")
  @Column(name = "sale_unit", columnDefinition = "VARCHAR(32) COMMENT '销售单位'")
  private String saleUnit;

  @ApiModelProperty("规格")
  @TableField(value = "spec")
  @Column(name = "spec", columnDefinition = "VARCHAR(64) COMMENT '规格'")
  private String spec;

  @ApiModelProperty("基本单位")
  @TableField(value = "base_unit")
  @Column(name = "base_unit", columnDefinition = "VARCHAR(32) COMMENT '基本单位'")
  private String baseUnit;

  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @ApiModelProperty("开始时间")
  @TableField(value = "begin_date_time")
  @Column(name = "begin_date_time", columnDefinition = "datetime COMMENT '开始时间'")
  private Date beginDateTime;

  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @ApiModelProperty("结束时间")
  @TableField(value = "end_date_time")
  @Column(name = "end_date_time", columnDefinition = "datetime COMMENT '结束时间'")
  private Date endDateTime;

  @ApiModelProperty("上下架状态")
  @TableField(value = "is_shelf")
  @Column(name = "is_shelf", columnDefinition = "VARCHAR(4) COMMENT '上下架状态'")
  private String isShelf;

  @ApiModelProperty("条形码")
  @TableField(value = "bar_code")
  @Column(name = "bar_code", columnDefinition = "VARCHAR(32) COMMENT '条形码'")
  private String barCode;

  @ApiModelProperty("是否维护图片")
  @TableField(value = "maintenance_picture")
  @Column(name = "maintenance_picture", columnDefinition = "int COMMENT '是否维护图片'")
  private Boolean maintenancePicture;

  @ApiModelProperty("是否维护详情")
  @TableField(value = "maintenance_introduction")
  @Column(name = "maintenance_introduction", columnDefinition = "int COMMENT '是否维护详情'")
  private Boolean maintenanceIntroduction;

  @ApiModelProperty("销售公司")
  @TableField(value = "sale_company")
  @Column(name = "sale_company", columnDefinition = "VARCHAR(32) COMMENT '销售公司'")
  private String saleCompany;

  @ApiModelProperty("sfa图片地址")
  @TableField(value = "pic_url")
  @Column(name = "pic_url", columnDefinition = "VARCHAR(255) COMMENT 'sfa图片地址'")
  private String picUrl;

  @ApiModelProperty("主图片url")
  @TableField(value = "primary_picture_url")
  @Column(name = "primary_picture_url", columnDefinition = "VARCHAR(255) COMMENT '主图片url'")
  private String primaryPictureUrl;

  @ApiModelProperty("财务经营指标")
  @Column(name = "finance_index", columnDefinition = "varchar(32) COMMENT '财务经营指标'")
  private String financeIndex;

  @ApiModelProperty("物料简称")
  @Column(name = "simple_name", columnDefinition = "varchar(64) COMMENT '物料简称'")
  private String simpleName;

  @ApiModelProperty("零级编码")
  @Column(name = "zero_level_code", columnDefinition = "varchar(32) COMMENT '零级编码'")
  private String zeroLevelCode;

  @ApiModelProperty("零级名称")
  @Column(name = "zero_level_name", columnDefinition = "varchar(128) COMMENT '零级名称'")
  private String zeroLevelName;

  @ApiModelProperty("一级编码")
  @Column(name = "one_level_code", columnDefinition = "varchar(32) COMMENT '一级编码'")
  private String oneLevelCode;

  @ApiModelProperty("一级名称")
  @Column(name = "one_level_name", columnDefinition = "varchar(128) COMMENT '一级名称'")
  private String oneLevelName;

  @ApiModelProperty("二级编码")
  @Column(name = "two_level_code", columnDefinition = "varchar(32) COMMENT '二级编码'")
  private String twoLevelCode;

  @ApiModelProperty("二级名称")
  @Column(name = "two_level_name", columnDefinition = "varchar(128) COMMENT '二级名称'")
  private String twoLevelName;

  @ApiModelProperty("三级编码")
  @Column(name = "three_level_code", columnDefinition = "varchar(32) COMMENT '三级编码'")
  private String threeLevelCode;

  @ApiModelProperty("三级名称")
  @Column(name = "three_level_name", columnDefinition = "varchar(128) COMMENT '三级名称'")
  private String threeLevelName;

  @ApiModelProperty("四级编码")
  @Column(name = "four_level_code", columnDefinition = "varchar(32) COMMENT '四级编码'")
  private String fourLevelCode;

  @ApiModelProperty("四级名称")
  @Column(name = "four_level_name", columnDefinition = "varchar(128) COMMENT '四级名称'")
  private String fourLevelName;

  @ApiModelProperty("转换值")
  @Column(name = "conversion_value", columnDefinition = "varchar(8) COMMENT '转换值'")
  private String conversionValue;

  @ApiModelProperty("物料组编码")
  @Column(name = "material_group_code", columnDefinition = "varchar(32) COMMENT '物料组编码'")
  private String materialGroupCode;

  @ApiModelProperty("物料组名称")
  @Column(name = "material_group_name", columnDefinition = "varchar(128) COMMENT '物料组名称'")
  private String materialGroupName;

  @ApiModelProperty("税率")
  @Column(name = "tax_rate", columnDefinition = "decimal(6,4) COMMENT '税率'")
  private BigDecimal taxRate;

  @ApiModelProperty("税率(展示使用)")
  @Column(name = "tax_rate_str", columnDefinition = "varchar(8) COMMENT '税率(展示使用)'")
  private String taxRateStr;

  @ApiModelProperty("毛重")
  @Column(name = "gross_weight", columnDefinition = "varchar(64) COMMENT '毛重'")
  private String grossWeight;

  @ApiModelProperty("净重")
  @Column(name = "net_weight", columnDefinition = "varchar(64) COMMENT '净重'")
  private String netWeight;

  @ApiModelProperty("重量单位")
  @Column(name = "weight_unit", columnDefinition = "varchar(64) COMMENT '重量单位'")
  private String weightUnit;

  @ApiModelProperty("容量/体积")
  @Column(name = "capacity", columnDefinition = "varchar(64) COMMENT '容量/体积'")
  private String capacity;

  @ApiModelProperty("长/米")
  @Column(name = "long_metre", columnDefinition = "varchar(16) COMMENT '长/米'")
  private String longMetre;

  @ApiModelProperty("宽/米")
  @Column(name = "wide_metre", columnDefinition = "varchar(16) COMMENT '宽/米'")
  private String wideMetre;

  @ApiModelProperty("高/米")
  @Column(name = "high_metre", columnDefinition = "varchar(16) COMMENT '高/米'")
  private String highMetre;

  @ApiModelProperty("是否线下产品")
  @Column(name = "offline_product", columnDefinition = "varchar(2) DEFAULT 'Y' COMMENT '是否线下产品;数据字典[yesOrNo]'")
  private String offlineProduct;

  @ApiModelProperty("品相编码")
  @Column(name = "product_phase_code", columnDefinition = "varchar(32) COMMENT '品相编码'")
  private String productPhaseCode;

  @ApiModelProperty("品相名称")
  @Column(name = "product_phase_name", columnDefinition = "varchar(256) COMMENT '品相名称'")
  private String productPhaseName;

  /**
   * SFA排序权值字段（越大越靠前）
   */
  @ApiModelProperty("SFA排序权值")
  @TableField(value = "sfa_sort")
  @Column(name = "sfa_sort", columnDefinition = "int(10) DEFAULT '0' COMMENT 'SFA排序权值'")
  private Integer sfaSort;

  /** 商品介绍 */
  @TableField(exist = false)
  @Transient
  private ProductIntroduction introduction;

  /** 物料信息 */
  @TableField(exist = false)
  @Transient
  private List<ProductMaterial> materialList;

  /** 视频图片 */
  @TableField(exist = false)
  @Transient
  private List<ProductMedia> mediaList;
}
