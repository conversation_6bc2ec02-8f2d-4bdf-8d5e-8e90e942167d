package com.biz.crm.mdm.business.product.local.service.notifier;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.biz.crm.mdm.business.product.sdk.dto.ProductTagEventDto;
import com.biz.crm.mdm.business.product.sdk.event.ProductTagEventListener;
import com.biz.crm.mdm.business.product.sdk.vo.ProductTagVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品标签监听实现
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/23 19:39
 */
@Component
public class ProductTagEventListenerImpl implements ProductTagEventListener {

    @Autowired(required = false)
    private CrmBusinessLogVoService crmBusinessLogVoService;

    @Autowired(required = false)
    @Qualifier("nebulaToolkitService")
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public void onCreate(ProductTagEventDto dto) {
        List<ProductTagVo> newList = Lists.newArrayList();
        if (Objects.nonNull(dto.getNewest())) {
            newList.add(dto.getNewest());
        }
        if (CollectionUtil.isNotEmpty(dto.getNewestList())) {
            newList.addAll(dto.getNewestList());
        }
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            crmBusinessLogDto.setOldObject(null);
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }

    @Override
    public void onUpdate(ProductTagEventDto dto) {
        ProductTagVo original = dto.getOriginal();
        ProductTagVo newest = dto.getNewest();
        String onlyKey = newest.getId();
        CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
        crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
        crmBusinessLogDto.setOnlyKey(onlyKey);
        crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
        crmBusinessLogDto.setOldObject(original);
        crmBusinessLogDto.setNewObject(newest);
        crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    }

    @Override
    public void onDelete(ProductTagEventDto dto) {
        List<ProductTagVo> newList = Lists.newArrayList();
        if (Objects.nonNull(dto.getNewest())) {
            newList.add(dto.getNewest());
        }
        if (CollectionUtil.isNotEmpty(dto.getNewestList())) {
            newList.addAll(dto.getNewestList());
        }
        List<ProductTagVo> oldList = (List<ProductTagVo>) this.nebulaToolkitService.copyCollectionByBlankList(newList, ProductTagVo.class, ProductTagVo.class, HashSet.class, ArrayList.class);
        Map<String, ProductTagVo> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(ProductTagVo::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.DELETE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            ProductTagVo original = oldMap.getOrDefault(newest.getId(), new ProductTagVo());
            original.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setDelFlag(DelFlagStatusEnum.DELETE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });

    }

    @Override
    public void onEnable(ProductTagEventDto dto) {
        List<ProductTagVo> newList = Lists.newArrayList();
        if (Objects.nonNull(dto.getNewest())) {
            newList.add(dto.getNewest());
        }
        if (CollectionUtil.isNotEmpty(dto.getNewestList())) {
            newList.addAll(dto.getNewestList());
        }
        List<ProductTagVo> oldList = (List<ProductTagVo>) this.nebulaToolkitService.copyCollectionByBlankList(newList, ProductTagVo.class, ProductTagVo.class, HashSet.class, ArrayList.class);
        Map<String, ProductTagVo> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(ProductTagVo::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.ENABLE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            ProductTagVo original = oldMap.getOrDefault(newest.getId(), new ProductTagVo());
            original.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }

    @Override
    public void onDisable(ProductTagEventDto dto) {
        List<ProductTagVo> newList = Lists.newArrayList();
        if (Objects.nonNull(dto.getNewest())) {
            newList.add(dto.getNewest());
        }
        if (CollectionUtil.isNotEmpty(dto.getNewestList())) {
            newList.addAll(dto.getNewestList());
        }
        List<ProductTagVo> oldList = (List<ProductTagVo>) this.nebulaToolkitService.copyCollectionByBlankList(newList, ProductTagVo.class, ProductTagVo.class, HashSet.class, ArrayList.class);
        Map<String, ProductTagVo> oldMap = oldList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getId()))
                .collect(Collectors.toMap(ProductTagVo::getId, v -> v, (n, o) -> n));
        String tenantCode = TenantUtils.getTenantCode();
        newList.forEach(newest -> {
            String onlyKey = newest.getId();
            CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
            crmBusinessLogDto.setOperationType(OperationTypeEunm.DISABLE.getDictCode());
            crmBusinessLogDto.setOnlyKey(onlyKey);
            crmBusinessLogDto.setAppCode(tenantCode);
            crmBusinessLogDto.setTenantCode(tenantCode);
            ProductTagVo original = oldMap.getOrDefault(newest.getId(), new ProductTagVo());
            original.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
            crmBusinessLogDto.setOldObject(original);
            original.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
            crmBusinessLogDto.setNewObject(newest);
            crmBusinessLogVoService.handleSave(crmBusinessLogDto);
        });
    }
}
