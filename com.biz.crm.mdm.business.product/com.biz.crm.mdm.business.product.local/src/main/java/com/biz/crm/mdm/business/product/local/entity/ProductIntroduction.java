package com.biz.crm.mdm.business.product.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品富文本实体
 *
 * <AUTHOR>
 * @date 2021/10/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("mdm_product_introduction")
@Api(tags = "商品富文本实体")
@Table(
    name = "mdm_product_introduction",
    indexes = {
      @Index(name = "mdm_product_introduction_index1", columnList = "tenant_code"),
      @Index(name = "mdm_product_introduction_index2", columnList = "product_code")
    })
@org.hibernate.annotations.Table(appliesTo = "mdm_product_introduction", comment = "商品富文本")
public class ProductIntroduction extends TenantEntity {
  private static final long serialVersionUID = 7955387136082613417L;

  @ApiModelProperty("商品编码")
  @TableField(value = "product_code")
  @Column(name = "product_code", columnDefinition = "VARCHAR(32) COMMENT '商品编码'")
  private String productCode;

  @ApiModelProperty("富文本介绍")
  @TableField(value = "introduction_text")
  @Column(name = "introduction_text", columnDefinition = "BLOB COMMENT '富文本介绍'")
  private String introductionText;
}
