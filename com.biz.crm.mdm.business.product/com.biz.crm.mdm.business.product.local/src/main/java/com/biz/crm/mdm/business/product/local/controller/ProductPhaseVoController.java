package com.biz.crm.mdm.business.product.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.product.sdk.dto.ProductPhaseDto;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * 商品品相控制层
 *
 * <AUTHOR>
 * @since 2021-10-13 16:48:13
 */
@Slf4j
@Api(tags = "商品品相管理: ProductPhaseVo: 商品品相管理")
@RestController
@RequestMapping(value = {"/v1/productPhase/productPhase"})
public class ProductPhaseVoController {

    @Autowired(required = false)
    private ProductPhaseVoService productPhaseVoService;

    @ApiOperation(value = "详情")
    @GetMapping(value = {"/findByIdOrCode"})
    public Result<ProductPhaseVo> findByIdOrCode(@RequestParam(value = "id", required = false) String id,
                                                 @RequestParam(value = "code", required = false) String code) {
        try {
            return Result.ok(productPhaseVoService.findByIdOrCode(id, code));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "根据ID集合获取信息")
    @PostMapping(value = {"/findByIds"})
    public Result<List<ProductPhaseVo>> findByIds(@RequestBody Set<String> ids) {
        try {
            List<ProductPhaseVo> list = productPhaseVoService.findByIds(ids);
            return Result.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    @ApiOperation(value = "根据编码集合获取信息")
    @PostMapping(value = {"/findByCodes"})
    public Result<List<ProductPhaseVo>> findByCodes(@RequestBody Set<String> codes) {
        try {
            List<ProductPhaseVo> list = productPhaseVoService.findByCodes(codes);
            return Result.ok(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    @ApiOperation(value = "根据编码获取品项信息")
    @PostMapping("findListByPhaseCodes")
    public Result<Map<String, List<ProductPhaseVo>>> findListByPhaseCodes(@RequestBody List<String> codes) {
        return Result.ok(productPhaseVoService.findListByPhaseCodes(codes));
    }

    @ApiOperation(value = "根据编码集合获取名称")
    @GetMapping(value = {"/findNameByCodes"})
    public Result<Map<String, String>> findNameByCodes(@RequestParam("codes") Set<String> codes) {
        try {
            Map<String, String> map = productPhaseVoService.findNameByCodes(codes);
            return Result.ok(map);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "查询分页列表")
    @GetMapping(value = {"/findByConditions"})
    public Result<Page<ProductPhaseVo>> findByConditions(@PageableDefault(50) Pageable pageable, ProductPhaseDto dto) {
        try {
            pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
            dto = Optional.ofNullable(dto).orElse(new ProductPhaseDto());
            dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            Page<ProductPhaseVo> result = productPhaseVoService.findByConditions(pageable, dto);
            return Result.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    @ApiOperation(value = "品项载入缓存")
    @GetMapping("loadCacheProductPhase")
    public Result<List<ProductPhaseVo>> loadCacheProductPhase() {
        return Result.ok(productPhaseVoService.loadCacheProductPhase());
    }
}
