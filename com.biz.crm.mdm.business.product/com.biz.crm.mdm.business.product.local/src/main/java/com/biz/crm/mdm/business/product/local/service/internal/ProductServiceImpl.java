package com.biz.crm.mdm.business.product.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.product.level.sdk.dto.RelateProductLevelCodeQueryDto;
import com.biz.crm.mdm.business.product.level.sdk.service.ProductLevelVoSdkService;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductLevelVo;
import com.biz.crm.mdm.business.product.local.entity.Product;
import com.biz.crm.mdm.business.product.local.repository.ProductRepository;
import com.biz.crm.mdm.business.product.local.service.ProductService;
import com.biz.crm.mdm.business.product.sdk.dto.ContractProductQueryDto;
import com.biz.crm.mdm.business.product.sdk.dto.ProductEventDto;
import com.biz.crm.mdm.business.product.sdk.dto.ProductPaginationDto;
import com.biz.crm.mdm.business.product.sdk.dto.ProductSingleEventDto;
import com.biz.crm.mdm.business.product.sdk.enums.IsShelfEnum;
import com.biz.crm.mdm.business.product.sdk.event.ProductEventListener;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductPhaseVo;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * mdm_product 商品主数据(Product)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-13 16:30:34
 */
@Service("productService")
public class ProductServiceImpl implements ProductService {

    @Autowired(required = false)
    private ProductRepository productRepository;

    @Autowired(required = false)
    private ProductVoService productVoService;

    @Autowired(required = false)
    private ProductLevelVoSdkService productLevelVoSdkService;

    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;

    @Autowired(required = true)
    private DictDataVoService dictDataVoService;

    @Resource
    private ProductPhaseVoService productPhaseVoService;

    private final static ThreadLocal<Map<String, String>> sapFactoryThreadLocal = new ThreadLocal<>();

    private String getFactoryCode(String companyCode) {
        Map<String, String> dictMap = sapFactoryThreadLocal.get();
        if (ObjectUtils.isEmpty(dictMap)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.MDM_SAP_FACTORY_TYPE);
            dictMap = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
        }
        return dictMap.get(companyCode);
    }

    /**
     * 基于数据库执行的数据视图执行内容缓存（最多500毫秒）
     */
    private static volatile Cache<String, List<Product>> cache = null;

    public ProductServiceImpl() {
        if (cache == null) {
            synchronized (ProductServiceImpl.class) {
                while (cache == null) {
                    cache = CacheBuilder.newBuilder()
                            .initialCapacity(10000)
                            .expireAfterWrite(500, TimeUnit.MILLISECONDS)
                            .maximumSize(100000)
                            .build();
                }
            }
        }
    }

    @Override
    public Page<Product> findByConditions(Pageable pageable, ProductPaginationDto dto) {
        this.dealDto(dto);
        if (ObjectUtils.isNotEmpty(dto.getCompanyCode())) {
            dto.setFactoryCode(getFactoryCode(dto.getCompanyCode()));
        }
        if (CollectionUtils.isNotEmpty(dto.getItemSet())) {
            List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(dto.getItemSet());
            Set<String> itemCodeSet = productPhaseVos.stream().filter(x -> BooleanEnum.FALSE.getCapital().equals(x.getCanShare()) || ObjectUtils.isEmpty(x.getCanShare())).map(x -> x.getProductPhaseCode()).collect(Collectors.toSet());
            dto.setItemSet(null);
            Set<String> zeroLevelCodeSet = Sets.newHashSet();
            if (CollectionUtils.isNotEmpty(itemCodeSet)) {
                zeroLevelCodeSet.addAll(itemCodeSet);
            }
            //是需要分摊 并且有零级的
            Set<String> levelCodeSet = productPhaseVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getZeroLevelCode()) &&
                            BooleanEnum.TRUE.getCapital().equals(x.getCanShare()))
                    .map(x -> x.getZeroLevelCode()).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(levelCodeSet)) {
                List<ProductPhaseVo> productPhaseVos1 = productPhaseVoService.findListByZeroLevels(levelCodeSet);
                zeroLevelCodeSet.addAll(productPhaseVos1.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductPhaseCode()))
                        .map(ProductPhaseVo::getProductPhaseCode).collect(Collectors.toSet()));
            }
            Set<String> levelCodeSet2 = productPhaseVos.stream().filter(x -> ObjectUtils.isEmpty(x.getZeroLevelCode()) &&
                    BooleanEnum.TRUE.getCapital().equals(x.getCanShare())).map(x -> x.getZeroLevelCode()).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(levelCodeSet2)) {
                List<ProductPhaseVo> productPhaseVos1 = productPhaseVoService.findListByCanShareIsNull();
                zeroLevelCodeSet.addAll(productPhaseVos1.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductPhaseCode()))
                        .map(ProductPhaseVo::getProductPhaseCode).collect(Collectors.toSet()));
            }
            dto.setProductPhaseCodeSet(zeroLevelCodeSet);
        }
        Page<Product> pageResult = this.productRepository.findByConditions(pageable, dto);

        // 绑定商品层级名称
        List<String> productLevelCodes = Lists.newLinkedList();
        Map<String, String> productLevelMap = Maps.newHashMap();
        List<ProductLevelVo> productLevelVos = Lists.newLinkedList();
        Boolean flag = pageResult != null && CollectionUtils.isNotEmpty(pageResult.getRecords());
        if (Boolean.TRUE.equals(flag)) {
            productLevelCodes = pageResult.getRecords().stream()
                    .filter(a -> StringUtils.isNotBlank(a.getProductLevelCode()))
                    .map(Product::getProductLevelCode)
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(productLevelCodes)) {
            productLevelVos = this.productLevelVoSdkService.findListByCodes(productLevelCodes);
        }
        if (CollectionUtils.isNotEmpty(productLevelVos)) {
            productLevelMap = productLevelVos.stream()
                    .filter(a -> StringUtils.isNotBlank(a.getProductLevelCode()))
                    .filter(a -> StringUtils.isNotBlank(a.getProductLevelName()))
                    .collect(Collectors.toMap(ProductLevelVo::getProductLevelCode, ProductLevelVo::getProductLevelName,
                            (a, b) -> a));
        }
        if (Boolean.TRUE.equals(flag)) {
            for (Product item : pageResult.getRecords()) {
                item.setProductLevelName(productLevelMap.getOrDefault(item.getProductLevelCode(), StringUtils.EMPTY));
            }
        }
        return pageResult;
    }

    @Override
    public List<Product> findDetailsByIdsOrProductCodes(List<String> ids, List<String> productCodes) {
        if (CollectionUtils.isEmpty(ids) && CollectionUtils.isEmpty(productCodes)) {
            return Lists.newLinkedList();
        }
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isNotEmpty(ids)) {
            sb.append(StringUtils.join(ids));
        }
        if (CollectionUtils.isNotEmpty(productCodes)) {
            sb.append(StringUtils.join(productCodes));
        }
        String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), sb.toString());
        List<Product> graph = cache.getIfPresent(cacheKey);
        if (graph == null) {
            graph = this.productRepository.findByIdsOrProductCodes(ids, productCodes);
            cache.put(cacheKey, graph);
        }
        return graph;
    }

    @Override
    @Transactional
    public Product create(Product product) {
        this.createValidation(product);
        product.setId(null);
        product.setTenantCode(TenantUtils.getTenantCode());
        product.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        product.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        this.productRepository.saveOrUpdate(product);
        return product;
    }

    @Override
    @Transactional
    public Product update(Product product) {
        this.updateValidation(product);
        String currentId = product.getId();
        Product current = this.productRepository.findById(currentId);
        Validate.notNull(current, "修改信息不存在");
        Validate.isTrue(product.getProductCode().equals(current.getProductCode()), "商品编码不能修改");
        //新增租户编号
        product.setTenantCode(TenantUtils.getTenantCode());
        this.productRepository.saveOrUpdate(product);
        return product;
    }

    @Override
    @Transactional
    public void updateDelFlagByIds(List<String> ids) {
        Validate.isTrue(CollectionUtils.isNotEmpty(ids), "id集合不能为空");
        List<ProductVo> list = this.productVoService.findDetailsByIdsOrProductCodes(ids, null);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        this.productRepository.updateDelFlagByIds(ids);
        ProductEventDto productEventDto = new ProductEventDto();
        for (ProductVo productVo : list) {
            productEventDto.setOriginal(productVo);
            productEventDto.setNewest(null);
            SerializableBiConsumer<ProductEventListener, ProductEventDto> onDelete =
                    ProductEventListener::onDelete;
            this.nebulaNetEventClient.publish(productEventDto, ProductEventListener.class, onDelete);
        }
    }

    @Override
    @Transactional
    public void upShelf(List<String> ids) {
        Validate.isTrue(CollectionUtils.isNotEmpty(ids), "id集合不能为空");
        this.productRepository.updateIsShelfByIds(ids, IsShelfEnum.UP.getCode());
        List<ProductVo> list = this.productVoService.findDetailsByIdsOrProductCodes(ids, null);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (ProductVo productVo : list) {
            ProductSingleEventDto productSingleEventDto = new ProductSingleEventDto();
            productSingleEventDto.setId(productVo.getId());
            final JSONObject object1 = new JSONObject();
            object1.put("isShelf", IsShelfEnum.DOWN.getCode());
            object1.put("enableStatus", productVo.getEnableStatus());
            object1.put("productCode", productVo.getProductCode());
            object1.put("productLevelCode", productVo.getProductLevelCode());
            final JSONObject object2 = new JSONObject();
            object2.put("isShelf", IsShelfEnum.UP.getCode());
            object2.put("enableStatus", productVo.getEnableStatus());
            object2.put("productCode", productVo.getProductCode());
            object2.put("productLevelCode", productVo.getProductLevelCode());
            productSingleEventDto.setOriginal(object1);
            productSingleEventDto.setNewest(object2);
            SerializableBiConsumer<ProductEventListener, ProductSingleEventDto> onUpShelf =
                    ProductEventListener::onUpShelf;
            this.nebulaNetEventClient.publish(
                    productSingleEventDto, ProductEventListener.class, onUpShelf);
        }
    }

    @Override
    @Transactional
    public void downShelf(List<String> ids) {
        Validate.isTrue(CollectionUtils.isNotEmpty(ids), "id集合不能为空");
        this.productRepository.updateIsShelfByIds(ids, IsShelfEnum.DOWN.getCode());
        List<ProductVo> list = this.productVoService.findDetailsByIdsOrProductCodes(ids, null);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (ProductVo productVo : list) {
            ProductSingleEventDto productSingleEventDto = new ProductSingleEventDto();
            productSingleEventDto.setId(productVo.getId());
            final JSONObject object1 = new JSONObject();
            object1.put("isShelf", IsShelfEnum.UP.getCode());
            object1.put("enableStatus", productVo.getEnableStatus());
            object1.put("productCode", productVo.getProductCode());
            object1.put("productLevelCode", productVo.getProductLevelCode());
            final JSONObject object2 = new JSONObject();
            object2.put("isShelf", IsShelfEnum.DOWN.getCode());
            object2.put("enableStatus", productVo.getEnableStatus());
            object2.put("productCode", productVo.getProductCode());
            object2.put("productLevelCode", productVo.getProductLevelCode());
            productSingleEventDto.setOriginal(object1);
            productSingleEventDto.setNewest(object2);
            SerializableBiConsumer<ProductEventListener, ProductSingleEventDto> onDownShelf =
                    ProductEventListener::onDownShelf;
            this.nebulaNetEventClient.publish(
                    productSingleEventDto, ProductEventListener.class, onDownShelf);
        }
    }

    @Override
    @Transactional
    public void upShelfByProductCodes(List<String> productCodeList) {
        Validate.isTrue(CollectionUtils.isNotEmpty(productCodeList), "productCode集合不能为空");
        this.productRepository.updateIsShelfByByProductCodes(productCodeList, IsShelfEnum.UP.getCode());

        List<ProductVo> list =
                this.productVoService.findDetailsByIdsOrProductCodes(null, productCodeList);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (ProductVo productVo : list) {
            ProductSingleEventDto productSingleEventDto = new ProductSingleEventDto();
            productSingleEventDto.setId(productVo.getId());
            final JSONObject object1 = new JSONObject();
            object1.put("isShelf", IsShelfEnum.DOWN.getCode());
            object1.put("enableStatus", productVo.getEnableStatus());
            object1.put("productCode", productVo.getProductCode());
            object1.put("productLevelCode", productVo.getProductLevelCode());
            final JSONObject object2 = new JSONObject();
            object2.put("isShelf", IsShelfEnum.UP.getCode());
            object2.put("enableStatus", productVo.getEnableStatus());
            object2.put("productCode", productVo.getProductCode());
            object2.put("productLevelCode", productVo.getProductLevelCode());
            productSingleEventDto.setOriginal(object1);
            productSingleEventDto.setNewest(object2);
            SerializableBiConsumer<ProductEventListener, ProductSingleEventDto> onUpShelf =
                    ProductEventListener::onUpShelf;
            this.nebulaNetEventClient.publish(
                    productSingleEventDto, ProductEventListener.class, onUpShelf);
        }
    }

    @Override
    @Transactional
    public void downShelfByProductCodes(List<String> productCodeList) {
        Validate.isTrue(CollectionUtils.isNotEmpty(productCodeList), "productCode集合不能为空");
        this.productRepository.updateIsShelfByByProductCodes(
                productCodeList, IsShelfEnum.DOWN.getCode());
        List<ProductVo> list =
                this.productVoService.findDetailsByIdsOrProductCodes(null, productCodeList);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (ProductVo productVo : list) {
            ProductSingleEventDto productSingleEventDto = new ProductSingleEventDto();
            productSingleEventDto.setId(productVo.getId());
            final JSONObject object1 = new JSONObject();
            object1.put("isShelf", IsShelfEnum.UP.getCode());
            object1.put("enableStatus", productVo.getEnableStatus());
            object1.put("productCode", productVo.getProductCode());
            object1.put("productLevelCode", productVo.getProductLevelCode());
            final JSONObject object2 = new JSONObject();
            object2.put("isShelf", IsShelfEnum.DOWN.getCode());
            object2.put("enableStatus", productVo.getEnableStatus());
            object2.put("productCode", productVo.getProductCode());
            object2.put("productLevelCode", productVo.getProductLevelCode());
            productSingleEventDto.setOriginal(object1);
            productSingleEventDto.setNewest(object2);
            SerializableBiConsumer<ProductEventListener, ProductSingleEventDto> onDownShelf =
                    ProductEventListener::onDownShelf;
            this.nebulaNetEventClient.publish(
                    productSingleEventDto, ProductEventListener.class, onDownShelf);
        }
    }

    @Override
    @Transactional
    public void enableBatch(List<String> ids) {
        Validate.isTrue(CollectionUtils.isNotEmpty(ids), "id集合不能为空");
        this.productRepository.updateEnableStatusByIds(ids, EnableStatusEnum.ENABLE);
        List<ProductVo> list = this.productVoService.findDetailsByIdsOrProductCodes(ids, null);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (ProductVo productVo : list) {
            ProductSingleEventDto productSingleEventDto = new ProductSingleEventDto();
            productSingleEventDto.setId(productVo.getId());
            final JSONObject object1 = new JSONObject();
            object1.put("isShelf", productVo.getIsShelf());
            object1.put("enableStatus", EnableStatusEnum.DISABLE.getDes());
            object1.put("productCode", productVo.getProductCode());
            object1.put("productLevelCode", productVo.getProductLevelCode());
            final JSONObject object2 = new JSONObject();
            object2.put("isShelf", productVo.getIsShelf());
            object2.put("enableStatus", EnableStatusEnum.ENABLE.getDes());
            object2.put("productCode", productVo.getProductCode());
            object2.put("productLevelCode", productVo.getProductLevelCode());
            productSingleEventDto.setOriginal(object1);
            productSingleEventDto.setNewest(object2);
            SerializableBiConsumer<ProductEventListener, ProductSingleEventDto> onEnable =
                    ProductEventListener::onEnable;
            this.nebulaNetEventClient.publish(
                    productSingleEventDto, ProductEventListener.class, onEnable);
        }
    }

    @Override
    @Transactional
    public void disableBatch(List<String> ids) {
        Validate.isTrue(CollectionUtils.isNotEmpty(ids), "id集合不能为空");
        this.productRepository.updateEnableStatusByIds(ids, EnableStatusEnum.DISABLE);
        this.productRepository.updateIsShelfByIds(ids, IsShelfEnum.DOWN.getCode());
        List<ProductVo> list = this.productVoService.findDetailsByIdsOrProductCodes(ids, null);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (ProductVo productVo : list) {
            ProductSingleEventDto productSingleEventDto = new ProductSingleEventDto();
            productSingleEventDto.setId(productVo.getId());
            final JSONObject object1 = new JSONObject();
            object1.put("isShelf", productVo.getIsShelf());
            object1.put("enableStatus", EnableStatusEnum.ENABLE.getDes());
            object1.put("productCode", productVo.getProductCode());
            object1.put("productLevelCode", productVo.getProductLevelCode());
            final JSONObject object2 = new JSONObject();
            object2.put("isShelf", productVo.getIsShelf());
            object2.put("enableStatus", EnableStatusEnum.DISABLE.getDes());
            object2.put("productCode", productVo.getProductCode());
            object2.put("productLevelCode", productVo.getProductLevelCode());
            productSingleEventDto.setOriginal(object1);
            productSingleEventDto.setNewest(object2);
            SerializableBiConsumer<ProductEventListener, ProductSingleEventDto> onDisable =
                    ProductEventListener::onDisable;
            this.nebulaNetEventClient.publish(
                    productSingleEventDto, ProductEventListener.class, onDisable);
        }
    }

    @Override
    public Integer countByProductLevelCodesAndDelFlag(
            List<String> productLevelCodes, String delFlag) {
        if (CollectionUtils.isEmpty(productLevelCodes) || StringUtils.isBlank(delFlag)) {
            return 0;
        }
        return this.productRepository.countByProductLevelCodesAndDelFlag(productLevelCodes, delFlag);
    }

    @Override
    public Set<String> findProductLevelCodeSetByProductCodes(Set<String> productCodeSet) {
        if (CollectionUtils.isEmpty(productCodeSet)) {
            return Sets.newHashSet();
        }
        return this.productRepository.findProductLevelCodeSetByProductCodes(productCodeSet);
    }

    @Override
    public Product findByProductCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return this.productRepository.findByProductCode(code, DelFlagStatusEnum.NORMAL.getCode());
    }

    @Override
    public List<Product> findByContractProductQueryDto(ContractProductQueryDto dto) {
        if (Objects.isNull(dto)
                || (CollectionUtils.isEmpty(dto.getProductCodeSet())
                && CollectionUtils.isEmpty(dto.getProductLevelCodeSet()))) {
            return Lists.newLinkedList();
        }
        Set<String> levelCodeSet = Sets.newHashSet();
        // 获取层级及下级
        if (!CollectionUtils.isEmpty(dto.getProductLevelCodeSet())) {
            final RelateProductLevelCodeQueryDto queryDto = new RelateProductLevelCodeQueryDto();
            queryDto.setSearchType(-1);
            queryDto.setProductLevelCodeSet(dto.getProductLevelCodeSet());
            // k-层级编码
            final Map<String, String> mapLevel =
                    this.productLevelVoSdkService.findByRelateProductLevelCodeQueryDto(queryDto);
            if (!mapLevel.isEmpty()) {
                levelCodeSet.addAll(mapLevel.keySet());
            }
        }
        dto.setProductLevelCodeSet(levelCodeSet);
        dto.setUnionType(Boolean.FALSE);
        if (!CollectionUtils.isEmpty(dto.getProductCodeSet())
                && !CollectionUtils.isEmpty(dto.getProductLevelCodeSet())) {
            dto.setUnionType(Boolean.TRUE);
        }
        return this.productRepository.findByContractProductQueryDto(dto);
    }

    private void createValidation(Product product) {
        this.validation(product);
    }

    private void updateValidation(Product product) {
        Validate.notNull(product, "商品信息缺失");
        Validate.isTrue(StringUtils.isNotBlank(product.getId()), "商品id不能为空");
        this.validation(product);
    }

    private void validation(Product product) {
        Validate.notNull(product, "商品信息缺失");
        Validate.isTrue(StringUtils.isNotBlank(product.getProductCode()), "商品编码不能为空");
        //Validate.isTrue(StringUtils.isNotBlank(product.getProductType()), "商品类型不能为空");
        Validate.isTrue(StringUtils.isNotBlank(product.getProductName()), "商品名称不能为空");
        Validate.isTrue(StringUtils.isNotBlank(product.getIsShelf()), "上下架不能为空");
        Validate.notNull(product.getBeginDateTime(), "开始时间不能为空");
        Validate.notNull(product.getEndDateTime(), "结束时间不能为空");
    }

    private void dealDto(ProductPaginationDto dto) {
        if (Objects.isNull(dto)) {
            return;
        }
        String productCode = dto.getProductCode();
        if (StringUtils.isNotBlank(productCode) && productCode.contains(",")) {
            dto.setProductCodes(Sets.newHashSet(productCode.split(",")));
            dto.setProductCode(null);
        }
        String productName = dto.getProductName();
        if (StringUtils.isNotBlank(productName) && productName.contains(",")) {
            dto.setProductNames(Sets.newHashSet(productName.split(",")));
            dto.setProductName(null);
        }
    }
}
