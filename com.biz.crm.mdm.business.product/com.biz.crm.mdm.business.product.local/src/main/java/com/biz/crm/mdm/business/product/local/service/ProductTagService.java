package com.biz.crm.mdm.business.product.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.product.local.entity.ProductTag;
import com.biz.crm.mdm.business.product.sdk.dto.ProductTagDto;
import com.biz.crm.mdm.business.product.sdk.vo.ProductTagVo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 商品标签信息(ProductTag)表服务接口
 *
 * <AUTHOR>
 * @date 2021-12-02 17:07:24
 */
public interface ProductTagService {

    /**
     * 多条件分页查询
     *
     * @param pageable
     * @param paginationDto
     * @return
     */
    Page<ProductTagVo> findByConditions(Pageable pageable, ProductTagDto paginationDto);

    /**
     * 创建
     *
     * @param productTag
     * @return
     */
    ProductTag create(ProductTag productTag);

    /**
     * 修改
     *
     * @param productTag
     * @return
     */
    ProductTag update(ProductTag productTag);

    /**
     * 批量启用
     *
     * @param ids
     */
    void enableBatch(List<String> ids);

    /**
     * 批量禁用
     *
     * @param ids
     */
    void disableBatch(List<String> ids);

    /**
     * 批量删除
     *
     * @param ids
     */
    void deleteBatch(List<String> ids);

    /**
     * 查看详情
     *
     * @param id
     * @param code
     * @return
     */
    ProductTagVo findByIdOrCode(String id, String code);
}
