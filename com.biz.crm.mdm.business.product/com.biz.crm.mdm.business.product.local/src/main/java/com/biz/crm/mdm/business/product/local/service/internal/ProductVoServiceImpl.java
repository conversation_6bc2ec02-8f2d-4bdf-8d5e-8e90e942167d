package com.biz.crm.mdm.business.product.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.material.unit.service.MaterialUnitVoService;
import com.biz.crm.mdm.business.material.unit.vo.MaterialUnitVo;
import com.biz.crm.mdm.business.product.level.sdk.dto.RelateProductLevelCodeQueryDto;
import com.biz.crm.mdm.business.product.level.sdk.enums.ProductLevelEnum;
import com.biz.crm.mdm.business.product.level.sdk.service.ProductLevelVoSdkService;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductLevelVo;
import com.biz.crm.mdm.business.product.local.entity.Product;
import com.biz.crm.mdm.business.product.local.entity.ProductIntroduction;
import com.biz.crm.mdm.business.product.local.entity.ProductMaterial;
import com.biz.crm.mdm.business.product.local.entity.ProductMedia;
import com.biz.crm.mdm.business.product.local.repository.ProductMaterialRepository;
import com.biz.crm.mdm.business.product.local.repository.ProductRepository;
import com.biz.crm.mdm.business.product.local.repository.ProductTagMappingRepository;
import com.biz.crm.mdm.business.product.local.service.*;
import com.biz.crm.mdm.business.product.local.utils.ProductPictureUtils;
import com.biz.crm.mdm.business.product.sdk.constant.ProductConstant;
import com.biz.crm.mdm.business.product.sdk.dto.*;
import com.biz.crm.mdm.business.product.sdk.enums.IsShelfEnum;
import com.biz.crm.mdm.business.product.sdk.enums.MediaTypeEnum;
import com.biz.crm.mdm.business.product.sdk.enums.ProductExtInfoTypeEnum;
import com.biz.crm.mdm.business.product.sdk.event.ProductEventListener;
import com.biz.crm.mdm.business.product.sdk.service.ProductPhaseVoService;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.*;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品sdk实现类
 *
 * <AUTHOR>
 * @date 2021/10/13
 */
@Service("productVoService")
public class ProductVoServiceImpl implements ProductVoService {

    @Autowired(required = false)
    private ProductService productService;

    @Autowired(required = false)
    private ProductMaterialService productMaterialService;

    @Autowired(required = false)
    private ProductMediaService productMediaService;

    @Autowired(required = false)
    private ProductIntroductionService productIntroductionService;

    @Autowired(required = false)
    private ProductLevelVoSdkService productLevelVoSdkService;

    @Autowired(required = false)
    private MaterialVoService materialVoService;

    @Autowired(required = false)
    private GenerateCodeService generateCodeService;

    @Autowired(required = false)
    private ProductRepository productRepository;

    @Autowired(required = false)
    @Qualifier("nebulaToolkitService")
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;

    @Autowired(required = false)
    private ProductMaterialRepository productMaterialRepository;

    @Autowired(required = false)
    private ProductPictureUtils productPictureUtils;

    @Autowired(required = false)
    private MaterialUnitVoService materialUnitVoService;

    @Autowired(required = true)
    private ProductTagMappingRepository tagMappingRepository;

    @Autowired(required = true)
    private DictDataVoService dictDataVoService;

    @Resource
    private ProductPhaseVoService productPhaseVoService;

    @Autowired(required = false)
    private CustomerVoService customerVoService;

    private final static ThreadLocal<Map<String, String>> sapFactoryThreadLocal = new ThreadLocal<>();

    private String getFactoryCode(String companyCode) {
        Map<String, String> dictMap = sapFactoryThreadLocal.get();
        if (ObjectUtils.isEmpty(dictMap)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.MDM_SAP_FACTORY_TYPE);
            dictMap = dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
        }
        return dictMap.get(companyCode);
    }

    @Override
    @Transactional
    public ProductVo create(ProductDto dto) {
        this.createValidation(dto);
        Product product = this.buildProductByDto(dto);
        dto.setProductCode(product.getProductCode());
        //新增租户编号
        product.setTenantCode(TenantUtils.getTenantCode());
        this.bindProductExtInfo(dto);
        this.productService.create(product);
        ProductEventDto productEventDto = new ProductEventDto();
        productEventDto.setOriginal(null);
        ProductVo productVo = this.nebulaToolkitService.copyObjectByBlankList(product, ProductVo.class, HashSet.class, LinkedList.class);
        productEventDto.setNewest(productVo);
        // 商品创建事件
        SerializableBiConsumer<ProductEventListener, ProductEventDto> onCreate = ProductEventListener::onCreate;
        this.nebulaNetEventClient.publish(productEventDto, ProductEventListener.class, onCreate);
        return this.buildByDtoAndProduct(dto, product);
    }

    @Override
    @Transactional
    public ProductVo update(ProductDto dto) {
        this.updateValidation(dto);
        List<ProductVo> current = this.findDetailsByIdsOrProductCodes(null, Collections.singletonList(dto.getProductCode()));
        ProductVo current2 = current.stream().findFirst().orElse(null);
        Validate.notNull(current2, "未获取到修改的商品信息");
        ProductVo productVo = this.nebulaToolkitService.copyObjectByBlankList(current2, ProductVo.class, HashSet.class, LinkedList.class);
        Product product = this.buildProductByDto(dto);
        this.bindProductExtInfo(dto);
        this.productService.update(product);
        ProductEventDto productEventDto = new ProductEventDto();
        productEventDto.setOriginal(productVo);
        List<ProductVo> newCurrent = this.findDetailsByIdsOrProductCodes(null, Collections.singletonList(dto.getProductCode()));
        ProductVo newLogVo = newCurrent.stream().findFirst().orElse(null);
        Validate.notNull(newLogVo, "未获取到修改后的商品信息");
        productEventDto.setNewest(newLogVo);
        // 商品编辑事件
        SerializableBiConsumer<ProductEventListener, ProductEventDto> onUpdate = ProductEventListener::onUpdate;
        this.nebulaNetEventClient.publish(productEventDto, ProductEventListener.class, onUpdate);
        return this.buildByDtoAndProduct(dto, product);
    }

    @Override
    public List<ProductVo> findSelectByKeyword(String keyword, String selectedCode) {
        Pageable pageable = PageRequest.of(1, 20);
        ProductPaginationDto dto = new ProductPaginationDto();
        dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        dto.setKeyword(keyword);
        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        if (StringUtil.isNotEmpty(selectedCode)) {
            dto.setSelectedCode(Collections.singletonList(selectedCode));
        }
        Page<Product> pageResult = this.productRepository.findByConditions(pageable, dto);
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return Lists.newLinkedList();
        }
        List<ProductVo> list = Lists.newArrayList();
        for (Product item : pageResult.getRecords()) {
            ProductVo cur = new ProductVo();
            cur.setProductCode(item.getProductCode());
            cur.setProductName(item.getProductName());
            list.add(cur);
        }
        return list;
    }

    @Override
    public MaterialProductVo findMaterialProductVoByMaterialCode(String materialCode) {
        if (StringUtils.isBlank(materialCode)) {
            return null;
        }
        Set<String> productCodeSet = this.productMaterialService.findProductCodeByMaterialCode(materialCode);
        if (CollectionUtils.isEmpty(productCodeSet)) {
            return null;
        }
        MaterialProductVo re = new MaterialProductVo();
        re.setProductCodeSet(productCodeSet);
        Set<String> productParentLevelCodeSet = this.findParentLevelCodeSetByProductCodes(productCodeSet);
        re.setProductLevelCodeSet(productParentLevelCodeSet);
        return re;
    }

    @Override
    public List<ProductMaterialVo> findMaterialProductVoByProductCodes(List<String> productCodes) {
        if (CollectionUtils.isEmpty(productCodes)) {
            return null;
        }
        List<ProductMaterial> list = productMaterialRepository.lambdaQuery().in(ProductMaterial::getProductCode, productCodes).eq(ProductMaterial::getTenantCode, TenantUtils.getTenantCode()).list();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return (List<ProductMaterialVo>) this.nebulaToolkitService.copyCollectionByBlankList(list, ProductMaterial.class, ProductMaterialVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public List<ProductVo> findByProductLevelCodes(List<String> productLevelCodeList) {
        if (CollectionUtils.isEmpty(productLevelCodeList)) {
            return Lists.newLinkedList();
        }
        return this.productRepository.findByProductLevelCodes(productLevelCodeList);
    }

    @Override
    public Set<String> findParentLevelCodeSetByProductCodes(Set<String> productCodeSet) {
        if (CollectionUtils.isEmpty(productCodeSet)) {
            return Sets.newHashSet();
        }
        Set<String> productLevelCodeSet = this.productService.findProductLevelCodeSetByProductCodes(productCodeSet);
        if (CollectionUtils.isEmpty(productLevelCodeSet)) {
            return Sets.newHashSet();
        }
        Map<String, List<ProductLevelVo>> map = this.productLevelVoSdkService.findCurAndParentByCodes(Lists.newArrayList(productLevelCodeSet));
        if (map.isEmpty()) {
            return Sets.newHashSet();
        }
        Set<String> re = Sets.newHashSet();
        for (Entry<String, List<ProductLevelVo>> item : map.entrySet()) {
            for (ProductLevelVo sub : item.getValue()) {
                re.add(sub.getProductLevelCode());
            }
        }
        return re;
    }

    @Override
    public List<ProductVo> findByProductQueryDto(ProductQueryDto dto) {
        Boolean f = Objects.isNull(dto) || (StringUtils.isAllBlank(dto.getProductCode(), dto.getProductName(), dto.getProductName(), dto.getIsShelf(), dto.getEnableStatus()) && CollectionUtils.isEmpty(dto.getProductLevelCodeList()) && CollectionUtils.isEmpty(dto.getProductCodeList()));
        if (Boolean.TRUE.equals(f)) {
            return Lists.newLinkedList();
        }
        Set<String> factoryCodeSet = Sets.newHashSet();
        if (ObjectUtils.isNotEmpty(dto.getCompanyCode())) {
            factoryCodeSet.add(getFactoryCode(dto.getCompanyCode()));
        }
        if (CollectionUtils.isNotEmpty(dto.getCompanyCodeSet())) {
            for (String s : dto.getCompanyCodeSet()) {
                factoryCodeSet.add(getFactoryCode(s));
            }
        }
        dto.setFactoryCodeSet(factoryCodeSet);
        if (CollectionUtils.isNotEmpty(dto.getItemCodeSet())) {
            List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(dto.getItemCodeSet());
            //不需要分摊的
            Set<String> itemCodeSet = productPhaseVos.stream().filter(x -> BooleanEnum.FALSE.getCapital().equals(x.getCanShare()) || ObjectUtils.isEmpty(x.getCanShare())).map(x -> x.getProductPhaseCode()).collect(Collectors.toSet());
            dto.setItemCodeSet(null);
//            dto.setItemCodeSet(itemCodeSet);

            Set<String> zeroLevelCodeSet = Sets.newHashSet();
            if (CollectionUtils.isNotEmpty(itemCodeSet)) {
                zeroLevelCodeSet.addAll(itemCodeSet);
            }
            //是需要分摊 并且有零级的
            Set<String> levelCodeSet = productPhaseVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getZeroLevelCode()) &&
                            BooleanEnum.TRUE.getCapital().equals(x.getCanShare()))
                    .map(x -> x.getZeroLevelCode()).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(levelCodeSet)) {
                List<ProductPhaseVo> productPhaseVos1 = productPhaseVoService.findListByZeroLevels(levelCodeSet);
                zeroLevelCodeSet.addAll(productPhaseVos1.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductPhaseCode()))
                        .map(ProductPhaseVo::getProductPhaseCode).collect(Collectors.toSet()));
            }
            //不需要分摊的
            Set<String> levelCodeSet2 = productPhaseVos.stream().filter(x -> ObjectUtils.isEmpty(x.getZeroLevelCode()) &&
                    BooleanEnum.FALSE.getCapital().equals(x.getCanShare())).map(x -> x.getZeroLevelCode()).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(levelCodeSet2)) {
                List<ProductPhaseVo> productPhaseVos1 = productPhaseVoService.findListByCanShareIsNull();
                zeroLevelCodeSet.addAll(productPhaseVos1.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductPhaseCode()))
                        .map(ProductPhaseVo::getProductPhaseCode).collect(Collectors.toSet()));
            }
            dto.setProductPhaseCodeSet(zeroLevelCodeSet);
        }
        List<Product> list = this.productRepository.findByProductQueryDto(dto);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newLinkedList();
        }
        return (List<ProductVo>) this.nebulaToolkitService.copyCollectionByBlankList(list, Product.class, ProductVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public List<ProductVo> findDetailsByIdsOrProductCodes(List<String> ids, List<String> productCodes) {
        if (CollectionUtils.isEmpty(ids) && CollectionUtils.isEmpty(productCodes)) {
            return Lists.newLinkedList();
        }
        final ProductDetailQueryDto dto = new ProductDetailQueryDto();
        dto.setExInfoCodes(ProductExtInfoTypeEnum.findAllKey());
        if (CollectionUtils.isNotEmpty(ids)) {
            dto.setCodeQueryFlag(false);
            dto.setCodes(ids);
        } else if (CollectionUtils.isNotEmpty(productCodes)) {
            dto.setCodeQueryFlag(true);
            dto.setCodes(productCodes);
        }
        return this.findDetailsByProductDetailQueryDto(dto);
    }

    @Override
    public List<ProductVo> findMainDetailsByProductCodes(List<String> productCodeList) {
        if (CollectionUtils.isEmpty(productCodeList)) {
            return Lists.newLinkedList();
        }
        final ProductDetailQueryDto dto = new ProductDetailQueryDto();
        dto.setExInfoCodes(Sets.newHashSet(ProductExtInfoTypeEnum.PRODUCT_LEVEL.getDictCode()));
        dto.setCodeQueryFlag(true);
        dto.setCodes(productCodeList);
        return this.findDetailsByProductDetailQueryDto(dto);
    }

    @Override
    public List<ProductVo> findMainDetailsByDto(ProductDetailQueryDto dto) {
        if (CollectionUtils.isEmpty(dto.getCodes())) {
            return Lists.newLinkedList();
        }
        dto.setExInfoCodes(Sets.newHashSet(ProductExtInfoTypeEnum.PRODUCT_LEVEL.getDictCode()));
        dto.setCodeQueryFlag(true);
        return this.findDetailsByProductDetailQueryDto(dto);
    }

    @Override
    public List<ProductVo> findDetailsByProductDetailQueryDto(ProductDetailQueryDto dto) {
        if (Objects.isNull(dto) || CollectionUtils.isEmpty(dto.getCodes())) {
            return Lists.newLinkedList();
        }
        List<String> ids = Lists.newLinkedList();
        List<String> productCodes = Lists.newLinkedList();
        if (Boolean.TRUE.equals(dto.getCodeQueryFlag())) {
            productCodes = dto.getCodes();
        } else {
            ids = dto.getCodes();
        }
        List<Product> productList = productService.findDetailsByIdsOrProductCodes(ids, productCodes);
        if (CollectionUtils.isEmpty(productList)) {
            return Lists.newLinkedList();
        }
        List<ProductVo> list = (List<ProductVo>) this.nebulaToolkitService.copyCollectionByBlankList(productList, Product.class, ProductVo.class, HashSet.class, ArrayList.class);
        this.buildUnit(list);
        Set<String> productCodeSet = list.stream().filter(a -> StringUtils.isNotBlank(a.getProductCode())).map(ProductVo::getProductCode).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(dto.getExInfoCodes()) || CollectionUtils.isEmpty(productCodeSet)) {
            return list;
        }
        if (dto.getExInfoCodes().contains(ProductExtInfoTypeEnum.PRODUCT_LEVEL.getDictCode())) {
            this.findProductLevelInfo(list);
        }
        if (dto.getExInfoCodes().contains(ProductExtInfoTypeEnum.MATERIAL.getDictCode())) {
            this.findProductMaterialInfo(list, productCodeSet);
        }
        if (dto.getExInfoCodes().contains(ProductExtInfoTypeEnum.PICTURE.getDictCode()) || dto.getExInfoCodes().contains(ProductExtInfoTypeEnum.VIDEO.getDictCode())) {
            this.findProductMediaInfo(list, productCodeSet);
        }
        if (dto.getExInfoCodes().contains(ProductExtInfoTypeEnum.INTRO.getDictCode())) {
            this.findProductIntroInfo(list, productCodeSet);
        }
        if (dto.getBusinessTypeQueryFlag()) {
            this.loadBusinessType(list);
        }
        return list;
    }

    @Override
    public Map<String, String> findAllowSaleProductByProductLevelCodes(Set<String> productLevelCodes) {
        if (CollectionUtils.isEmpty(productLevelCodes)) {
            return Maps.newHashMap();
        }
        final RelateProductLevelCodeQueryDto queryDto = new RelateProductLevelCodeQueryDto();
        queryDto.setProductLevelCodeSet(productLevelCodes);
        queryDto.setSearchType(-1);
        final Map<String, String> productLevelRuleMap = this.productLevelVoSdkService.findByRelateProductLevelCodeQueryDto(queryDto);
        if (productLevelRuleMap.isEmpty()) {
            return Maps.newHashMap();
        }
        final ProductQueryDto productQueryDto = new ProductQueryDto();
        productQueryDto.setProductLevelCodeList(Lists.newArrayList(productLevelCodes));
        productQueryDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        productQueryDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        productQueryDto.setIsShelf(IsShelfEnum.UP.getCode());
        productQueryDto.setTenantCode(TenantUtils.getTenantCode());
        List<ProductVo> list = this.findByProductQueryDto(productQueryDto);
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        Map<String, String> map = list.stream().filter(a -> StringUtils.isNoneBlank(a.getProductCode(), a.getProductLevelCode()) && productLevelRuleMap.keySet().contains(a.getProductLevelCode())).collect(Collectors.toMap(ProductVo::getProductCode, ProductVo::getProductLevelCode, (a, b) -> a));
        Map<String, String> re = Maps.newHashMap();
        for (Entry<String, String> item : map.entrySet()) {
            final String s = productLevelRuleMap.get(item.getValue());
            if (StringUtils.isBlank(s)) {
                continue;
            }
            re.put(item.getKey(), s);
        }
        return re;
    }

    @Override
    public Page<ProductVo> findByConditions(Pageable pageable, ProductPaginationDto dto) {
        if (ObjectUtils.isNotEmpty(dto.getCompanyCode())) {
            dto.setFactoryCode(getFactoryCode(dto.getCompanyCode()));
        }
        if (CollectionUtils.isNotEmpty(dto.getItemSet())) {
            List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(dto.getItemSet());
            Set<String> itemCodeSet = productPhaseVos.stream().filter(x -> BooleanEnum.FALSE.getCapital().equals(x.getCanShare()) || ObjectUtils.isEmpty(x.getCanShare())).map(x -> x.getProductPhaseCode()).collect(Collectors.toSet());
            dto.setItemSet(null);
            Set<String> zeroLevelCodeSet = Sets.newHashSet();
            if (CollectionUtils.isNotEmpty(itemCodeSet)) {
                zeroLevelCodeSet.addAll(itemCodeSet);
            }
            //是需要分摊 并且有零级的
            Set<String> levelCodeSet = productPhaseVos.stream().filter(x -> ObjectUtils.isNotEmpty(x.getZeroLevelCode()) &&
                            BooleanEnum.TRUE.getCapital().equals(x.getCanShare()))
                    .map(x -> x.getZeroLevelCode()).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(levelCodeSet)) {
                List<ProductPhaseVo> productPhaseVos1 = productPhaseVoService.findListByZeroLevels(levelCodeSet);
                zeroLevelCodeSet.addAll(productPhaseVos1.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductPhaseCode()))
                        .map(ProductPhaseVo::getProductPhaseCode).collect(Collectors.toSet()));
            }
            Set<String> levelCodeSet2 = productPhaseVos.stream().filter(x -> ObjectUtils.isEmpty(x.getZeroLevelCode()) &&
                    BooleanEnum.TRUE.getCapital().equals(x.getCanShare())).map(x -> x.getZeroLevelCode()).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(levelCodeSet2)) {
                List<ProductPhaseVo> productPhaseVos1 = productPhaseVoService.findListByCanShareIsNull();
                zeroLevelCodeSet.addAll(productPhaseVos1.stream().filter(x -> ObjectUtils.isNotEmpty(x.getProductPhaseCode()))
                        .map(ProductPhaseVo::getProductPhaseCode).collect(Collectors.toSet()));
            }
            dto.setProductPhaseCodeSet(zeroLevelCodeSet);
        }
        Page<Product> pageResult = this.productRepository.findByConditions(pageable, dto);
        Page<ProductVo> productVoPage = new Page<>(pageable.getPageNumber(), pageable.getPageSize(), pageResult.getTotal());
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return productVoPage;
        }
        List<ProductVo> productVos = (List<ProductVo>) this.nebulaToolkitService.copyCollectionByBlankList(pageResult.getRecords(), Product.class, ProductVo.class, HashSet.class, ArrayList.class);
        productVoPage.setRecords(productVos);
        productVoPage.setPages(pageResult.getPages());
        productVoPage.setTotal(pageResult.getTotal());
        productVoPage.setCurrent(pageResult.getCurrent());
        productVoPage.setCountId(pageResult.getCountId());
        productVoPage.setHitCount(pageResult.isHitCount());
        productVoPage.setOptimizeCountSql(pageResult.isOptimizeCountSql());
        productVoPage.setSearchCount(pageResult.isSearchCount());
        return productVoPage;
    }

    /**
     * 保存商品关联的物料图片视频以及介绍信息
     *
     * @param dto
     */
    private void bindProductExtInfo(ProductDto dto) {
        /*
         * 保存商品关联的物料图片视频以及介绍信息：
         * 1、保存关联物料
         * 2、保存图片视频信息
         * 3、保存商品介绍信息
         * */
        Validate.notNull(dto, "商品信息缺失");
        String tenantCode = TenantUtils.getTenantCode();
        List<ProductMaterial> productMaterialList = Lists.newLinkedList();
        if (CollectionUtils.isNotEmpty(dto.getMaterialList())) {
            productMaterialList = (List<ProductMaterial>) this.nebulaToolkitService.copyCollectionByWhiteList(dto.getMaterialList(), ProductMaterialDto.class, ProductMaterial.class, HashSet.class, ArrayList.class);
            productMaterialList.forEach(a -> {
                a.setProductCode(dto.getProductCode());
                a.setTenantCode(tenantCode);
            });
        }
        productMaterialService.saveBatch(productMaterialList, dto.getProductCode());

        List<ProductMedia> productMediaList = Lists.newLinkedList();
        if (CollectionUtils.isNotEmpty(dto.getPicturePrimaryList())) {
            dto.getPicturePrimaryList().forEach(a -> {
                a.setType(MediaTypeEnum.PICTURE.getCode());
                a.setPrimaryPicture(BooleanEnum.TRUE.getCapital());
                a.setProductCode(dto.getProductCode());
                a.setTenantCode(tenantCode);
            });
            productMediaList.addAll(this.nebulaToolkitService.copyCollectionByWhiteList(dto.getPicturePrimaryList(), ProductMediaDto.class, ProductMedia.class, HashSet.class, ArrayList.class));
        }
        if (CollectionUtils.isNotEmpty(dto.getPictureMediaList())) {
            dto.getPictureMediaList().forEach(a -> {
                a.setType(MediaTypeEnum.PICTURE.getCode());
                a.setPrimaryPicture(BooleanEnum.FALSE.getCapital());
                a.setProductCode(dto.getProductCode());
                a.setTenantCode(tenantCode);
            });
            productMediaList.addAll(this.nebulaToolkitService.copyCollectionByWhiteList(dto.getPictureMediaList(), ProductMediaDto.class, ProductMedia.class, HashSet.class, ArrayList.class));
        }

        if (CollectionUtils.isNotEmpty(dto.getVideoMediaList())) {
            dto.getVideoMediaList().forEach(a -> {
                a.setType(MediaTypeEnum.VIDEO.getCode());
                a.setPrimaryPicture(BooleanEnum.FALSE.getCapital());
                a.setProductCode(dto.getProductCode());
                a.setTenantCode(tenantCode);
            });
            productMediaList.addAll(this.nebulaToolkitService.copyCollectionByWhiteList(dto.getVideoMediaList(), ProductMediaDto.class, ProductMedia.class, HashSet.class, ArrayList.class));
        }
        productMediaService.saveBatch(productMediaList, dto.getProductCode());
        ProductIntroduction productIntroduction = null;
        if (Objects.nonNull(dto.getIntroduction())) {
            productIntroduction = this.nebulaToolkitService.copyObjectByWhiteList(dto.getIntroduction(), ProductIntroduction.class, HashSet.class, ArrayList.class);
            productIntroduction.setProductCode(dto.getProductCode());
            //新增租户编号
            productIntroduction.setTenantCode(tenantCode);
        }
        productIntroductionService.saveIntroduction(productIntroduction, dto.getProductCode());
    }

    /**
     * 构建需要落库的商品信息
     *
     * @param dto
     * @return
     */
    private Product buildProductByDto(ProductDto dto) {
        dto.setTenantCode(TenantUtils.getTenantCode());
        Product product = this.nebulaToolkitService.copyObjectByWhiteList(dto, Product.class, HashSet.class, ArrayList.class);
        product.setMaintenancePicture(CollectionUtils.isNotEmpty(dto.getPictureMediaList()));
        product.setMaintenanceIntroduction(Objects.nonNull(dto.getIntroduction()));
        if (StringUtils.isBlank(product.getProductCode())) {
            String productCode = this.generateCodeService.generateCode(ProductConstant.PRODUCT_CODE);
            product.setProductCode(productCode);
        }
        if (CollectionUtils.isEmpty(dto.getPicturePrimaryList())) {
            return product;
        }
        Optional<ProductMediaDto> first = dto.getPicturePrimaryList().stream().findFirst();
        first.ifPresent(productMediaDto -> product.setPrimaryPictureUrl(productPictureUtils.getFileUrl(productMediaDto.getFileCode())));
        dto.setPrimaryPictureUrl(product.getPrimaryPictureUrl());
        if (StringUtils.isNotBlank(product.getPrimaryPictureUrl())) {
            product.setMaintenancePicture(true);
        } else {
            product.setMaintenancePicture(false);
        }
        return product;
    }

    /**
     * dto转vo
     *
     * @param dto
     * @param product
     * @return
     */
    private ProductVo buildByDtoAndProduct(ProductDto dto, Product product) {
        ProductVo vo = this.nebulaToolkitService.copyObjectByWhiteList(product, ProductVo.class, HashSet.class, ArrayList.class);
        if (CollectionUtils.isNotEmpty(dto.getMaterialList())) {
            vo.setMaterialList((List<ProductMaterialVo>) this.nebulaToolkitService.copyCollectionByWhiteList(dto.getMaterialList(), ProductMaterialDto.class, ProductMaterialVo.class, HashSet.class, ArrayList.class));
        }
        if (CollectionUtils.isNotEmpty(dto.getPicturePrimaryList())) {
            vo.setPicturePrimaryList((List<ProductMediaVo>) this.nebulaToolkitService.copyCollectionByWhiteList(dto.getPicturePrimaryList(), ProductMediaDto.class, ProductMediaVo.class, HashSet.class, ArrayList.class));
        }
        if (CollectionUtils.isNotEmpty(dto.getPictureMediaList())) {
            vo.setPictureMediaList((List<ProductMediaVo>) this.nebulaToolkitService.copyCollectionByWhiteList(dto.getPictureMediaList(), ProductMediaDto.class, ProductMediaVo.class, HashSet.class, ArrayList.class));
        }
        if (CollectionUtils.isNotEmpty(dto.getVideoMediaList())) {
            vo.setVideoMediaList((List<ProductMediaVo>) this.nebulaToolkitService.copyCollectionByWhiteList(dto.getVideoMediaList(), ProductMediaDto.class, ProductMediaVo.class, HashSet.class, ArrayList.class));
        }
        if (Objects.nonNull(dto.getIntroduction())) {
            vo.setIntroduction(this.nebulaToolkitService.copyObjectByWhiteList(dto.getIntroduction(), ProductIntroductionVo.class, HashSet.class, ArrayList.class));
        }
        return vo;
    }

    /**
     * 获取商品的层级信息
     *
     * @param list
     */
    private void findProductLevelInfo(List<ProductVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<String> productLevelCodeSet = list.stream().filter(a -> StringUtils.isNotBlank(a.getProductLevelCode())).map(ProductVo::getProductLevelCode).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(productLevelCodeSet)) {
            return;
        }
        List<ProductLevelVo> productLevelVoList = this.productLevelVoSdkService.findListByCodes(Lists.newArrayList(productLevelCodeSet));
        // k-productLevelCode,v-productLevelName
        Map<String, String> mapLevel = productLevelVoList.stream().filter(a -> StringUtils.isNoneBlank(a.getProductLevelCode(), a.getProductLevelName())).collect(Collectors.toMap(ProductLevelVo::getProductLevelCode, ProductLevelVo::getProductLevelName, (a, b) -> a));
        for (ProductVo item : list) {
            item.setProductLevelName(mapLevel.get(item.getProductLevelCode()));
        }
    }

    /**
     * 获取商品物料信息
     *
     * @param list
     * @param productCodeSet
     */
    private void findProductMaterialInfo(List<ProductVo> list, Set<String> productCodeSet) {
        if (CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(productCodeSet)) {
            return;
        }
        List<ProductMaterial> materialList = productMaterialService.findByProductCodes(Lists.newArrayList(productCodeSet));
        if (CollectionUtils.isEmpty(materialList)) {
            return;
        }
        Set<String> materialCodes = materialList.stream().map(ProductMaterial::getMaterialCode).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(materialCodes)) {
            return;
        }
        // 获取物料表信息
        List<MaterialVo> materialVoList = materialVoService.findDetailByMaterialCodes(materialCodes);
        if (CollectionUtils.isEmpty(materialVoList)) {
            return;
        }
        Map<String, MaterialVo> map = materialVoList.stream().filter(a -> StringUtils.isNotBlank(a.getMaterialCode())).collect(Collectors.toMap(MaterialVo::getMaterialCode, Function.identity(), (a, b) -> a));
        List<ProductMaterialVo> voList = (List<ProductMaterialVo>) this.nebulaToolkitService.copyCollectionByBlankList(materialList, ProductMaterial.class, ProductMaterialVo.class, HashSet.class, ArrayList.class);
        for (ProductMaterialVo v : voList) {
            if (!map.containsKey(v.getMaterialCode())) {
                continue;
            }
            MaterialVo materialVo = map.get(v.getMaterialCode());
            v.setProductLevelCode(materialVo.getProductLevelCode());
            v.setProductLevelName(materialVo.getProductLevelName());
            v.setMaterialType(materialVo.getMaterialType());
            v.setMaterialTypeName(materialVo.getMaterialTypeName());
            v.setUnitTypeCode(materialVo.getUnitTypeCode());
            v.setMaterialName(materialVo.getMaterialName());
            v.setProductSmallClassCode(materialVo.getProductSmallClassCode());
            v.setProductSmallClassName(materialVo.getProductSmallClassName());
        }
        Map<String, List<ProductMaterialVo>> mapProductMaterial = voList.stream().filter(a -> StringUtils.isNotBlank(a.getProductCode())).collect(Collectors.groupingBy(ProductMaterialVo::getProductCode));
        for (ProductVo item : list) {
            item.setMaterialList(mapProductMaterial.get(item.getProductCode()));
        }
    }

    /**
     * 获取商品图片视频信息
     *
     * @param list
     * @param productCodeSet
     */
    private void findProductMediaInfo(List<ProductVo> list, Set<String> productCodeSet) {
        if (CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(productCodeSet)) {
            return;
        }
        List<ProductMedia> mediaList = productMediaService.findByProductCodes(Lists.newArrayList(productCodeSet));

        if (CollectionUtils.isEmpty(mediaList)) {
            return;
        }
        List<ProductMediaVo> voList = (List<ProductMediaVo>) this.nebulaToolkitService.copyCollectionByWhiteList(mediaList, ProductMedia.class, ProductMediaVo.class, HashSet.class, ArrayList.class);
        Map<String, List<ProductMediaVo>> map = voList.stream().collect(Collectors.groupingBy(ProductMediaVo::getType));
        Map<String, List<ProductMediaVo>> mapPrimaryPictureMedia = map.getOrDefault(MediaTypeEnum.PICTURE.getCode(), Lists.newArrayList()).stream().filter(k -> BooleanEnum.TRUE.getCapital().equals(k.getPrimaryPicture())).collect(Collectors.groupingBy(ProductMediaVo::getProductCode));
        Map<String, List<ProductMediaVo>> mapPictureMedia = map.getOrDefault(MediaTypeEnum.PICTURE.getCode(), Lists.newArrayList()).stream().filter(k -> BooleanEnum.FALSE.getCapital().equals(k.getPrimaryPicture())).collect(Collectors.groupingBy(ProductMediaVo::getProductCode));
        Map<String, List<ProductMediaVo>> mapVideoMedia = map.getOrDefault(MediaTypeEnum.VIDEO.getCode(), Lists.newArrayList()).stream().collect(Collectors.groupingBy(ProductMediaVo::getProductCode));
        for (ProductVo item : list) {
            item.setPicturePrimaryList(mapPrimaryPictureMedia.get(item.getProductCode()));
            item.setPictureMediaList(mapPictureMedia.get(item.getProductCode()));
            item.setVideoMediaList(mapVideoMedia.get(item.getProductCode()));
        }
    }

    /**
     * 获取商品详情信息
     *
     * @param list
     * @param productCodeSet
     */
    private void findProductIntroInfo(List<ProductVo> list, Set<String> productCodeSet) {
        if (CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(productCodeSet)) {
            return;
        }
        List<ProductIntroduction> introductionList = productIntroductionService.findByProductCodes(Lists.newArrayList(productCodeSet));
        if (CollectionUtils.isEmpty(introductionList)) {
            return;
        }
        List<ProductIntroductionVo> voList = (List<ProductIntroductionVo>) this.nebulaToolkitService.copyCollectionByWhiteList(introductionList, ProductIntroduction.class, ProductIntroductionVo.class, HashSet.class, ArrayList.class);
        Map<String, ProductIntroductionVo> map = voList.stream().collect(Collectors.toMap(ProductIntroductionVo::getProductCode, Function.identity(), (a, b) -> a));
        for (ProductVo item : list) {
            item.setIntroduction(map.get(item.getProductCode()));
        }
    }

    /**
     * 业态
     *
     * @param list
     */
    private void loadBusinessType(List<ProductVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> productCodes = list.stream().filter(e -> StringUtils.isNotBlank(e.getProductLevelCode())).map(ProductVo::getProductCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productCodes)) {
            return;
        }
        Map<String, String> businessTypeMap = this.findBusinessTypeByProductCodes(productCodes);
        for (ProductVo vo : list) {
            vo.setBusinessType(businessTypeMap.get(vo.getProductCode()));
        }
    }

    private void createValidation(ProductDto dto) {
        this.validation(dto);
    }

    private void updateValidation(ProductDto dto) {
        Validate.notNull(dto, "商品信息缺失");
        Validate.isTrue(StringUtils.isNotBlank(dto.getId()), "商品id不能为空");
        this.validation(dto);
    }

    private void validation(ProductDto dto) {
        Validate.notNull(dto, "商品信息缺失");
        // 商品编码前端如果不填，后台自动生成
        if (StringUtils.isNotBlank(dto.getProductCode())) {
            Product byProductCode = productService.findByProductCode(dto.getProductCode());
            Validate.isTrue(Objects.isNull(byProductCode) || (StringUtils.isNotBlank(dto.getId()) && dto.getId().equals(byProductCode.getId())), "商品编码重复");
        }
        //Validate.notBlank(dto.getProductType(), "商品类型不能为空");
        Validate.notBlank(dto.getProductName(), "商品名称不能为空");
        Validate.notBlank(dto.getIsShelf(), "上下架不能为空");
        Validate.notNull(dto.getBeginDateTime(), "开始时间不能为空");
        Validate.notNull(dto.getEndDateTime(), "结束时间不能为空");
//        Validate.isTrue(CollectionUtils.isNotEmpty(dto.getMaterialList()), "物料信息不能为空");
    }


    /**
     * 根据商品69码获取商品信息
     *
     * @param barCode
     * @return com.biz.crm.mdm.business.product.sdk.vo.ProductVo
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/28 9:49
     */
    @Override
    public List<ProductVo> findDetailByBarCode(String barCode) {
        if (StringUtil.isEmpty(barCode)) {
            return null;
        }
        return this.findDetailByBarCodes(Collections.singleton(barCode));
    }

    /**
     * 根据商品69码集合获取商品信息
     *
     * @param barCodes
     * @return com.biz.crm.mdm.business.product.sdk.vo.ProductVo
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/28 9:49
     */
    @Override
    public List<ProductVo> findDetailByBarCodes(Set<String> barCodes) {
        if (CollectionUtil.isEmpty(barCodes)) {
            return Lists.newArrayList();
        }
        List<Product> entityList = productRepository.findDetailByBarCodes(barCodes);
        if (CollectionUtil.isEmpty(entityList)) {
            return Lists.newArrayList();
        }
        //关联产品层级编码集合
        List<String> productLevelCodeList = entityList.stream().map(Product::getProductLevelCode).distinct().collect(Collectors.toList());
        List<ProductLevelVo> productLevelVoList = this.productLevelVoSdkService.findListByCodes(productLevelCodeList);
        if (CollectionUtils.isNotEmpty(productLevelVoList)) {
            Map<String, String> nameMap = productLevelVoList.stream().collect(Collectors.toMap(item -> item.getProductLevelCode(), o -> o.getProductLevelName(), (v1, v2) -> v1));
            for (Product product : entityList) {
                if (nameMap.containsKey(product.getProductLevelCode())) {
                    product.setProductLevelName(nameMap.get(product.getProductLevelCode()));
                }
            }
        }
        List<ProductVo> products = (List<ProductVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entityList, Product.class, ProductVo.class, HashSet.class, ArrayList.class);
        this.buildUnit(products);
        return products;
    }


    /**
     * 构建终端产品价格
     *
     * @param products
     */
    private void buildUnit(List<ProductVo> products) {
        if (CollectionUtils.isEmpty(products)) {
            return;
        }
        Map<String, List<String>> resultMap = this.getProductsUnitList(products.stream().map(ProductVo::getProductCode).distinct().collect(Collectors.toList()));
        if (Objects.isNull(resultMap)) {
            return;
        }
        products.forEach(productReportVo -> productReportVo.setUnitList(resultMap.getOrDefault(productReportVo.getProductCode(), Lists.newArrayList())));
    }

    /**
     * 通过品项名称查询商品信息
     *
     * @param itemNames
     * @return
     */
    @Override
    public List<ProductItemVo> findItemListByItemNames(List<String> itemNames) {
        if (CollectionUtil.isEmpty(itemNames)) {
            return Lists.newArrayList();
        }
        List<ProductItemVo> list = productRepository.findItemByNames(itemNames);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        Map<String, List<ProductItemVo>> map = list.stream().collect(Collectors.groupingBy(x -> x.getItemCode() + ":" + x.getItemName()));
        List<ProductItemVo> dataList = Lists.newArrayList();
        map.forEach((key, value) -> {
            ProductItemVo itemVo = new ProductItemVo();
            String[] items = key.split(":");
            itemVo.setItemCode(items[0]);
            itemVo.setItemName(items[1]);
            List<ProductVo> productVoList = (List<ProductVo>) nebulaToolkitService.copyCollectionByWhiteList(value, ProductItemVo.class, ProductVo.class, HashSet.class, ArrayList.class);
            itemVo.setProductList(productVoList);
            dataList.add(itemVo);
        });
        return dataList;
    }

    @Override
    public List<ProductItemVo> findItemListByItemCodes(List<String> itemCodes) {
        if (CollectionUtil.isEmpty(itemCodes)) {
            return Lists.newArrayList();
        }
        List<ProductItemVo> dataList = Lists.newArrayList();
        List<ProductPhaseVo> productPhaseVos = productPhaseVoService.findByCodes(Sets.newHashSet(itemCodes));
        for (ProductPhaseVo phaseVo : productPhaseVos) {
            List<ProductVo> productVoList = Lists.newArrayList();
            if (BooleanEnum.FALSE.getCapital().equals(phaseVo.getCanShare()) || ObjectUtils.isEmpty(phaseVo.getCanShare())) {
                ProductVo productVo = new ProductVo();
                productVo.setProductPhaseName(phaseVo.getProductPhaseName());
                productVo.setProductPhaseCode(phaseVo.getProductPhaseCode());
                productVoList.add(productVo);
            }

            if (BooleanEnum.TRUE.getCapital().equals(phaseVo.getCanShare())) {
                if (ObjectUtils.isNotEmpty(phaseVo.getZeroLevelCode())) {
                    List<ProductPhaseVo> productPhaseVoList = productPhaseVoService.findListByZeroLevels(Sets.newHashSet(phaseVo.getZeroLevelCode()));
                    if (CollectionUtil.isNotEmpty(productPhaseVoList)) {
                        productVoList.addAll(productPhaseVoList.stream().map(x -> {
                            ProductVo productVo = new ProductVo();
                            productVo.setProductPhaseCode(x.getProductPhaseCode());
                            productVo.setProductPhaseName(x.getProductPhaseName());
                            return productVo;
                        }).collect(Collectors.toList()));
                    }
                } else {
                    //如果是为空的 则取是否分摊为空的或者是为否的
                    List<ProductPhaseVo> productPhaseVoList = productPhaseVoService.findListByCanShareIsNull();
                    if (CollectionUtil.isNotEmpty(productPhaseVoList)) {
                        productVoList.addAll(productPhaseVoList.stream().map(x -> {
                            ProductVo productVo = new ProductVo();
                            productVo.setProductPhaseCode(x.getProductPhaseCode());
                            productVo.setProductPhaseName(x.getProductPhaseName());
                            return productVo;
                        }).collect(Collectors.toList()));
                    }
                }

            }
            ProductItemVo itemVo = new ProductItemVo();
            itemVo.setItemCode(phaseVo.getProductPhaseCode());
            itemVo.setItemName(phaseVo.getProductPhaseName());
            itemVo.setProductList(productVoList);
            dataList.add(itemVo);
        }

        return dataList;
    }

    /**
     * 根据产品编码获取物料单位集合
     *
     * @param productCode 产品编码
     * @return java.util.List<java.lang.String>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/7 21:35
     */
    @Override
    public List<String> getProductUnitList(String productCode) {
        if (StringUtil.isEmpty(productCode)) {
            return Lists.newArrayList();
        }
        Map<String, List<String>> map = this.getProductsUnitList(Collections.singletonList(productCode));
        return map.getOrDefault(productCode, Lists.newArrayList());
    }

    /**
     * 根据产品编码集合获取物料单位集合
     *
     * @param productCodes 产品编码集合
     * @return java.util.Map<java.lang.String, java.util.List < java.lang.String>>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/12 16:46
     */
    @Override
    public Map<String, List<String>> getProductsUnitList(List<String> productCodes) {
        if (CollectionUtil.isEmpty(productCodes)) {
            return Maps.newHashMap();
        }
        List<MaterialUnitVo> unitVoList = materialUnitVoService.findByMaterialList(new HashSet<>(productCodes));
        if (CollectionUtil.isEmpty(unitVoList)) {
            return Maps.newHashMap();
        }
        List<DictDataVo> dictDataVoList = dictDataVoService.findByDictTypeCode(DictConstant.PRODUCT_SALE_UNIT);
        dictDataVoList.forEach(item -> {
            if (Objects.isNull(item.getDictSort())) {
                item.setDictSort(1000);
            }
        });
        List<DictDataVo> dictDataVoSortList = dictDataVoList.stream().sorted(Comparator.comparing(DictDataVo::getDictSort)).collect(Collectors.toList());
        Map<String, List<String>> materialMap = unitVoList.stream().collect(Collectors.groupingBy(MaterialUnitVo::getUnitTypeCode, Collectors.mapping(MaterialUnitVo::getUnitCode, Collectors.toList())));
        Map<String, List<String>> resultMap = Maps.newHashMap();
        Set<String> unitSet = Sets.newLinkedHashSet();
        for (DictDataVo dictDataVo : dictDataVoSortList) {
            unitSet.add(dictDataVo.getDictCode());
        }
        productCodes.forEach(materialCode -> {
            List<String> list = materialMap.getOrDefault(materialCode, Lists.newArrayList());
            List<String> result = Lists.newLinkedList();
            if (CollectionUtil.isNotEmpty(list)) {
                for (String unitCode : unitSet) {
                    if (list.contains(unitCode)) {
                        result.add(unitCode);
                    }
                }
            }
            resultMap.put(materialCode, result);
        });
        return resultMap;
    }

    @Override
    public Page<ProductVo> findAllowProductByDto(ProductPaginationDto dto) {
        Validate.notNull(dto, "查询参数实体不能为空");
        Validate.notEmpty(dto.getProductCodes(), "可购商品编码不能为空");
        Validate.notBlank(dto.getCustomerCode(), "客户编码不能为空");
        if (Objects.isNull(dto.getPage())) {
            dto.setPage(1);
        }
        if (Objects.isNull(dto.getSize())) {
            dto.setSize(15);
        }
        if (ObjectUtils.isNotEmpty(dto.getCompanyCode())) {
            dto.setFactoryCode(getFactoryCode(dto.getCompanyCode()));
        }
        dto.setTenantCode(TenantUtils.getTenantCode());
        Page<ProductVo> page = new Page<>(dto.getPage(), dto.getSize(), 0);
        // 标签不为空时根据标签查询产品编码
        if (StringUtils.isNotBlank(dto.getSkuTag())) {
            Set<String> tagProductSet = this.tagMappingRepository.findProductCodeByTagCode(dto.getSkuTag());
            if (CollectionUtils.isEmpty(tagProductSet)) {
                return page;
            }
            dto.getProductCodes().retainAll(tagProductSet);
        }
        if (CollectionUtils.isEmpty(dto.getProductCodes())) {
            return page;
        }
        // 查询工厂
        CustomerVo customerVo = this.customerVoService.findDetailsByIdOrCode(null, dto.getCustomerCode());
        Validate.notNull(customerVo, "客户【%】不存在", dto.getCustomerCode());
        DictDataVo factoryType = this.dictDataVoService.findByDictTypeCodeAndDictCode(DictConstant.MDM_SAP_FACTORY_TYPE,
                customerVo.getCompanyCode());
        Validate.notNull(factoryType, "公司【%s】的工厂为空，请前往数据字典【%s】维护", customerVo.getCompanyCode(), DictConstant.MDM_SAP_FACTORY_TYPE);
        dto.setFactoryCode(factoryType.getDictValue());
        Page<ProductVo> productVoPage = this.productRepository.findAllowProductByDto(dto);
        if (CollectionUtils.isEmpty(productVoPage.getRecords())) {
            return page;
        }
        page.setTotal(productVoPage.getTotal());
        ProductDetailQueryDto queryDto = new ProductDetailQueryDto();
        queryDto.setExInfoCodes(Sets.newHashSet(ProductExtInfoTypeEnum.PRODUCT_LEVEL.getDictCode(), ProductExtInfoTypeEnum.MATERIAL.getDictCode(), ProductExtInfoTypeEnum.PICTURE.getDictCode(), ProductExtInfoTypeEnum.VIDEO.getDictCode(), ProductExtInfoTypeEnum.INTRO.getDictCode()));
        queryDto.setCodeQueryFlag(true);
        queryDto.setCodes(productVoPage.getRecords().stream().map(ProductVo::getProductCode).collect(Collectors.toList()));
        List<ProductVo> productVoList = this.findDetailsByProductDetailQueryDto(queryDto);
        page.setRecords(productVoList);
        return page;
    }

    @Override
    public Page<ProductVo> findAllowProductPageByDto(ProductPaginationDto dto) {
        if (Objects.isNull(dto.getPage())) {
            dto.setPage(1);
        }
        if (Objects.isNull(dto.getSize())) {
            dto.setSize(15);
        }
        Page<ProductVo> page = new Page<>(dto.getPage(), dto.getSize(), 0);
        if (CollectionUtils.isEmpty(dto.getProductCodes())) {
            return page;
        }
        List<String> productCodes = new ArrayList<>(dto.getProductCodes());
        // 根据产品层级码找到业态(一级产品层级)
        if (StringUtils.isNotBlank(dto.getBusinessType())) {
            List<ProductVo> productVoList = this.findMainDetailsByProductCodes(productCodes);
            Map<String, String> productLevelVoMap = productVoList.stream().filter(e -> StringUtils.isNotBlank(e.getProductLevelCode())).collect(Collectors.toMap(ProductVo::getProductCode, ProductVo::getProductLevelCode));

            Map<String, String> productZeroLevelVoMap = new HashMap<>();
            if (BooleanEnum.TRUE.getCapital().equals(dto.getZeroLevelFlag())) {
                productZeroLevelVoMap.putAll(productVoList.stream().filter(e -> StringUtils.isNotBlank(e.getZeroLevelCode()))
                        .collect(Collectors.toMap(ProductVo::getProductCode, ProductVo::getZeroLevelCode)));
            }
            // 根据产品层级码找到业态(一级产品层级)
            Map<String, List<ProductLevelVo>> curAndParentProductLevelListMap = this.productLevelVoSdkService.findCurAndParentByCodes(new ArrayList<>(productLevelVoMap.values()));
            Map<String, String> productBusinessTypeMap = new HashMap<>();

            productLevelVoMap.forEach((k, v) -> {
                if (!curAndParentProductLevelListMap.containsKey(v)) {
                    return;
                }
                Optional<ProductLevelVo> first = curAndParentProductLevelListMap.getOrDefault(v, Lists.newArrayList()).stream().filter(e -> StringUtils.equals(ProductLevelEnum.SERIES.getCode(), e.getProductLevelType())).findFirst();
                first.ifPresent(vo -> productBusinessTypeMap.put(k, vo.getProductLevelCode()));
            });
            //业态过滤
            productCodes = productCodes.stream().filter(e -> {
                if (BooleanEnum.TRUE.getCapital().equals(dto.getZeroLevelFlag())) {
                    return StringUtils.equals(dto.getBusinessType(), productBusinessTypeMap.get(e))
                            || StringUtils.equals(dto.getBusinessType(), productZeroLevelVoMap.get(e));
                } else {
                    return StringUtils.equals(dto.getBusinessType(), productBusinessTypeMap.get(e));
                }
            }).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(productCodes)) {
            return page;
        }
        dto.setProductCodes(Sets.newHashSet(productCodes));
        page = this.findAllowProductByDto(dto);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return page;
        }
        return page;
    }

    @Override
    public List<ProductVo> findByMaterialCodes(List<String> materialCodes) {
        return productRepository.findByMaterialCodes(materialCodes);
    }

    @Override
    public List<String> findByProductSmallClass(List<String> smallClassList) {
        return productRepository.findByProductSmallClass(smallClassList);
    }

    @Override
    public List<ProductVo> findByProductSmallClassCodes(List<String> productSmallClassCodes) {
        if (CollectionUtil.isEmpty(productSmallClassCodes)) {
            return Lists.newArrayList();
        }
        List<Product> productList = this.productRepository.findByProductSmallClassCodes(productSmallClassCodes);
        if (CollectionUtil.isEmpty(productList)) {
            return Lists.newArrayList();
        }
        return (List<ProductVo>) this.nebulaToolkitService.copyCollectionByWhiteList(productList, Product.class, ProductVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public Map<String, String> findBusinessTypeByProductCodes(List<String> productCodeList) {
        if (CollectionUtils.isEmpty(productCodeList)) {
            return Maps.newHashMap();
        }
        List<ProductVo> productVoList = this.findMainDetailsByProductCodes(productCodeList);
        Map<String, String> productLevelVoMap = productVoList.stream().filter(e -> StringUtils.isNotBlank(e.getProductLevelCode())).collect(Collectors.toMap(ProductVo::getProductCode, ProductVo::getProductLevelCode));
        // 根据产品层级码找到业态(一级产品层级)
        Map<String, List<ProductLevelVo>> curAndParentProductLevelListMap = this.productLevelVoSdkService.findCurAndParentByCodes(new ArrayList<>(productLevelVoMap.values()));
        Map<String, String> productBusinessTypeMap = new HashMap<>();
        productLevelVoMap.forEach((k, v) -> {
            if (!curAndParentProductLevelListMap.containsKey(v)) {
                return;
            }
            Optional<ProductLevelVo> first = curAndParentProductLevelListMap.getOrDefault(v, Lists.newArrayList()).stream().filter(e -> StringUtils.equals(ProductLevelEnum.SERIES.getCode(), e.getProductLevelType())).findFirst();
            first.ifPresent(vo -> productBusinessTypeMap.put(k, vo.getProductLevelCode()));
        });
        return productBusinessTypeMap;
    }

    @Override
    public List<ProductVo> findAllDownListByProductLevelCodes(ProductPaginationDto dto) {
        if (CollectionUtils.isEmpty(dto.getProductLevelCodes())) {
            return Lists.newArrayList();
        }
        List<ProductVo> productVos = this.productRepository.findAllDownListByProductLevelCodes(dto);
        if (StringUtils.isBlank(dto.getBusinessType()) || CollectionUtils.isEmpty(productVos)) {
            return productVos;
        }
        List<String> productCodes = productVos.stream().map(ProductVo::getProductCode).collect(Collectors.toList());
        Map<String, String> businessTypeMap = this.findBusinessTypeByProductCodes(productCodes);
        productVos = productVos.stream().filter(e -> StringUtils.equals(dto.getBusinessType(), businessTypeMap.get(e.getProductCode()))).collect(Collectors.toList());
        return productVos;
    }

    @Override
    public List<ProductVo> findByProductNames(Set<String> productNames) {
        if (CollectionUtils.isEmpty(productNames)) {
            return Lists.newArrayList();
        }
        List<Product> productList = this.productRepository.findByProductNames(productNames);
        if (CollectionUtils.isEmpty(productList)) {
            return Lists.newArrayList();
        }
        return (List<ProductVo>) this.nebulaToolkitService.copyCollectionByBlankList(productList, Product.class, ProductVo.class, HashSet.class, ArrayList.class);
    }

    @Override
    public List<ProductPriceVo> findProductPriceByDto(ProductPriceVo dto) {
        if (Objects.isNull(dto)) {
            return Lists.newArrayList();
        }
        return this.productRepository.findProductPriceByDto(dto);
    }


    @Override
    public List<ProductVo> findProductListByProductPhaseCodes(List<String> productPhaseCodes) {
        List<Product> productList = productRepository.productPhaseCodes(productPhaseCodes);
        if (CollectionUtils.isEmpty(productList)) {
            return Lists.newArrayList();
        }
        List<ProductVo> productVoList = (List<ProductVo>) nebulaToolkitService.copyCollectionByWhiteList(productList,Product.class,ProductVo.class,HashSet.class,ArrayList.class);
        return productVoList;
    }
}
