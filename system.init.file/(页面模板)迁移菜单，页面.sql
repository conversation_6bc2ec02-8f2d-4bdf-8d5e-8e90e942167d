菜单数据，增量迁移
  查询expose（测试）已有的菜单 --总控
	select code from engine_competence where type = 'default';
	查询test（对接）不在expose的菜单 --总控
	select * from engine_competence where type = 'default' and  code not in ()； 
	查询expose（测试）已有的菜单
	select * from engine_default_competence where competence_code not like '%@%'  order by competence_code;
	查询test（对接）不在expose的菜单 
	select * from engine_default_competence where competence_code not in ();
页面模板，页面模板按钮，字段配置，覆盖迁移
  下面是expose侧的表字段，test侧要按字段查询；
  select `ID`, `function_code`, `function_name`, `function_type`, `parent_code`, `api_url`, `permission_obj`,  `create_name`,  `del_flag`, `enable_status`,       `vo_name`,   `create_account`, `create_time`, `modify_account`, `modify_name`, `modify_time`, `remark`, `tenant_code`, `code` from mdm_function_sub;

  select `ID`, `code`, `button_code`, `button_name`, `button_type`, `icon_code`, `ask`, `query_url`, `api_url`, `button_operation_type`, `parent_code`, `function_code`, `button_order`, `config_code`, `do_code`, `visible`,   `create_name`, `del_flag`, `enable_status`,   `create_account`, `create_time`, `modify_account`, `modify_name`, `modify_time`, `remark`, `tenant_code` from mdm_function_sub_button;
	
	select `ID`, `parent_code`, `function_code`, `column_export`, `field`, `title`, `visible`, `align`, `type`, `width`, `fixed`, `formorder`, `show_overflow`,    `create_name`,    `del_flag`, `enable_status`,  `required`, `dict_code`, `col`,  `formon`, `formoptions`, `props`, `refresh`, `formvalue`, `class_name`, `search`, `request_url`, `request_search`, `edit_view`, `search_type`, `entity_field_name`, `form_control`, `editable_in_create`, `editable_in_edit`, `visible_in_edit`, `visible_in_look`,  `create_account`, `create_time`, `modify_account`, `modify_name`, `modify_time`, `remark` from mdm_column_config;
	
删除页面模板总控，页面模板按钮总控的数据，还有总空间关联表数据

	DELETE from engine_button_competence_mapping where competence_id in ();
	
  delete from  engine_competence where type = 'PAGE_ENGINE' ;
	
	delete from engine_button where type = 'PAGE_ENGINE';
	
页面模板 总控，页面模板按钮总控，sql脚本设置。
# 把页面模板的按钮 插入到总控
	insert into engine_button(`id`,`create_account`,`create_time`,`modify_account`,`modify_time`, `button_desc`,`code`,`effective`, `name`,`is_system`,`type`,`icon`)
	select id, 'hefan', '2021-12-03', 'hefan',modify_time,remark,code,1,button_name,0,'PAGE_ENGINE',icon_code from mdm_function_sub_button;

备注：这里可能出了问题，因为有 页面模板功能数据不存在总控功能中，需要排查此处sql。
	#     把页面模板 插入到总控功能
	INSERT INTO `engine_competence` (`id`, `create_account`, `create_time`, `modify_account`, `modify_time`, `code`, `comment`, `icon`, `sort_index`, `tstatus`, `type`, `view_item`, `parent_id`)
	select fs.id, 'hefan', '2021-12-03', 'hefan', fs.modify_time, fs.code,fs.function_name,'',0,'009','PAGE_ENGINE',0,co.id
	from mdm_function_sub fs join engine_competence co on fs.parent_code = co.code ;

	# 把页面模板的功能按钮关系 插入到总控
	INSERT INTO `engine_button_competence_mapping` (`button_id`, `competence_id`)
	select eb.id,ec.id from mdm_function_sub_button fsb 
			join  engine_button eb on eb.code = fsb.code
			join mdm_function_sub fs on fs.parent_code = fsb.parent_code and fs.function_code = fsb.function_code                                                
			join engine_competence ec on ec.code = fs.code;
		

-- end ---
		
		
PAGE_ENGINE default
;





