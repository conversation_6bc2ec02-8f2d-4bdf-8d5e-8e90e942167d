package com.biz.crm.mdm.business.material.unit.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.material.unit.dto.MaterialUnitDto;
import com.biz.crm.mdm.business.material.unit.entity.MaterialUnit;
import com.biz.crm.mdm.business.material.unit.service.MaterialUnitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * 物料单位controller
 */
@Api(tags = "物料单位")
@RestController
@RequestMapping("v1/materialUnit/materialUnit")
public class MaterialUnitController {

  @Autowired(required = false)
  private MaterialUnitService materialUnitService;

  @ApiOperation(value = "分页查询列表")
  @GetMapping("/findByConditions")
  public Result<Page<MaterialUnit>> findByConditions(@PageableDefault(50) Pageable pageable,
                                                     MaterialUnitDto dto) {
    return Result.ok(materialUnitService.findByConditions(pageable, dto));
  }

  @ApiOperation(value = "批量删除")
  @DeleteMapping("/deleteBatch")
  public Result<?> delete(@RequestParam List<String> ids) {
    materialUnitService.deleteBatch(ids);
    return Result.ok("删除成功");
  }

  @ApiOperation(value = "批量启用")
  @PatchMapping("/enableBatch")
  public Result<?> enable(@RequestBody List<String> ids) {
    materialUnitService.enableBatch(ids);
    return Result.ok("启用成功");
  }

  @ApiOperation(value = "批量禁用")
  @PatchMapping("/disableBatch")
  public Result<?> disable(@RequestBody List<String> ids) {
    materialUnitService.disableBatch(ids);
    return Result.ok("禁用成功");
  }


  @ApiOperation(value = "根据主键id查询")
  @GetMapping("/findById")
  public Result<MaterialUnit> findById(@RequestParam String id) {
    return Result.ok(this.materialUnitService.findById(id));
  }

  @ApiOperation(value = "新增")
  @PostMapping("")
  public Result<MaterialUnit> create(@RequestBody MaterialUnit indexConfig) {
    return Result.ok(this.materialUnitService.create(indexConfig));
  }

  @ApiOperation(value = "更新")
  @PatchMapping("")
  public Result<MaterialUnit> update(@RequestBody MaterialUnit indexConfig) {
    return Result.ok(this.materialUnitService.update(indexConfig));
  }

}
