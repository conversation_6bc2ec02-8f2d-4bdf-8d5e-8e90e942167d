package com.biz.crm.mdm.business.material.unit.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.material.unit.dto.MaterialUnitTypeDto;
import com.biz.crm.mdm.business.material.unit.entity.MaterialUnitType;
import com.biz.crm.mdm.business.material.unit.mapper.MaterialUnitTypeMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;

/**
 * 物料单位类型 的 数据库访问类 {@link MaterialUnitType}
 * <AUTHOR>
 */
@Component
public class MaterialUnitTypeRepository extends ServiceImpl<MaterialUnitTypeMapper, MaterialUnitType> {

  /**
   * 分页条件查询物料单位类型
   *
   * @param pageable 分页信息
   * @param dto      查询筛选条件
   * @return 分页数据
   */
  public Page<MaterialUnitType> findByConditions(Page<MaterialUnitType> pageable, MaterialUnitTypeDto dto) {
    dto.setTenantCode(TenantUtils.getTenantCode()); // 过滤多租户
    return this.baseMapper.findByConditions(pageable, dto);
  }

  /**
   * 条件查询物料单位类型(包含下级单位列表)
   *
   * @param dto      查询筛选条件
   * @return 数据
   */
  public List<MaterialUnitType> findDetailsByConditions(MaterialUnitTypeDto dto) {
    dto.setTenantCode(TenantUtils.getTenantCode()); // 过滤多租户
    return this.baseMapper.findDetailsByConditions(dto);
  }

  /**
   * 根据id查询物料单位类别详情(包含物料单位类别下的物料单位)
   * @param id 主键
   * @return 物料单位实体
   */
  public MaterialUnitType findDetailById(String id) {
    return this.baseMapper.findDetailById(id, TenantUtils.getTenantCode());
  }

  /**
   * 根据物料单位类别编码查询物料单位类别详情(包含物料单位类别下的物料单位)
   * @param unitTypeCode 主键
   * @return 物料单位实体
   */
  public MaterialUnitType findDetailByUnitTypeCode(String unitTypeCode) {
    return this.baseMapper.findDetailByUnitTypeCode(unitTypeCode, TenantUtils.getTenantCode());
  }

  /**
   * 根据物料单位类别编码查询物料单位类别
   * @param unitTypeCode 单位类别编码
   * @return 物料单位类别
   */
  public MaterialUnitType findByUnitTypeCode(String unitTypeCode) {
    QueryWrapper<MaterialUnitType> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("unit_type_code",unitTypeCode);
    queryWrapper.eq("tenant_code",TenantUtils.getTenantCode());   //新增租户编号
    return this.baseMapper.selectOne(queryWrapper);
  }


  /**
   * 根据主键集合，修改 enable_status
   *
   * @param enable
   * @param ids
   */
  public void updateEnableStatusByIdIn(EnableStatusEnum enable, List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return;
    }
    UpdateWrapper<MaterialUnitType> updateWrapper = new UpdateWrapper<>();
    updateWrapper.set("enable_status", enable.getCode());
    updateWrapper.in("id", ids);
    updateWrapper.eq("tenant_code",TenantUtils.getTenantCode());   //新增租户编号
    this.update(updateWrapper);
  }

  /**
   * 根据主键集合，修改 del_flag
   *
   * @param delFlagStatusEnum 删除标记
   * @param ids               主键id集合
   */
  public void updateDelStatusByIdIn(DelFlagStatusEnum delFlagStatusEnum, List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return;
    }
    UpdateWrapper<MaterialUnitType> updateWrapper = new UpdateWrapper<>();
    updateWrapper.set("del_flag", delFlagStatusEnum.getCode());
    updateWrapper.in("id", ids);
    updateWrapper.eq("tenant_code",TenantUtils.getTenantCode());
    this.update(updateWrapper);
  }


  public List<MaterialUnitType> findAllByCodes(Set<String> codeList) {
    if (CollectionUtils.isEmpty(codeList)) {
      return Lists.newArrayList();
    }
    return this.lambdaQuery()
            .eq(MaterialUnitType::getTenantCode, TenantUtils.getTenantCode())
            .in(MaterialUnitType::getUnitTypeCode, codeList)
            .list();
  }

  public void saveBatchXml(List<MaterialUnitType> entityList) {
    if (CollectionUtil.isEmpty(entityList)) {
      return;
    }
    Lists.partition(entityList, CommonConstant.MAX_PAGE_SIZE).forEach(list -> {
      this.baseMapper.insertBatchSomeColumn(list);
    });

  }

  public void updateBatchXml(List<MaterialUnitType> updateList) {
    if (CollectionUtil.isEmpty(updateList)) {
      return;
    }
    Lists.partition(updateList, CommonConstant.MAX_PAGE_SIZE).forEach(this::updateBatchById);
  }

  public void delByCodes(Set<String> codeList) {
    if (CollectionUtils.isEmpty(codeList)) {
      return;
    }
    this.lambdaUpdate()
            .in(MaterialUnitType::getUnitTypeCode, codeList)
            .remove();
  }

}
