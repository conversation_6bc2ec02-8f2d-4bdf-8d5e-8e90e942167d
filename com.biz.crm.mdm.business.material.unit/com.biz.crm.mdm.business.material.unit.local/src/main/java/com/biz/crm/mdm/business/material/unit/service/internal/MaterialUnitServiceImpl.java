package com.biz.crm.mdm.business.material.unit.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.material.unit.constant.MaterialUnitConstant;
import com.biz.crm.mdm.business.material.unit.dto.MaterialUnitDto;
import com.biz.crm.mdm.business.material.unit.entity.MaterialUnit;
import com.biz.crm.mdm.business.material.unit.entity.MaterialUnitType;
import com.biz.crm.mdm.business.material.unit.event.MaterialUnitChangeListener;
import com.biz.crm.mdm.business.material.unit.repository.MaterialUnitRepository;
import com.biz.crm.mdm.business.material.unit.repository.MaterialUnitTypeRepository;
import com.biz.crm.mdm.business.material.unit.service.MaterialUnitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * 物料单位service实现
 */
@Service
public class MaterialUnitServiceImpl implements MaterialUnitService {

  @Autowired(required = false)
  private MaterialUnitRepository materialUnitRepository;

  @Autowired(required = false)
  private MaterialUnitTypeRepository materialUnitTypeRepository;

  @Autowired(required = false)
  private LoginUserService loginUserService;

  @Override
  public Page<MaterialUnit> findByConditions(Pageable pageable, MaterialUnitDto dto) {
    pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
    dto = Optional.ofNullable(dto).orElse(new MaterialUnitDto());
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    Page<MaterialUnit> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return materialUnitRepository.findByConditions(page, dto);
  }

  @Override
  @Transactional
  public void enableBatch(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "待修正的数据主键不能为空");
    materialUnitRepository.updateEnableStatusByIdIn(EnableStatusEnum.ENABLE, ids);
  }

  @Override
  @Transactional
  public void disableBatch(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "待修正的数据主键不能为空");
    materialUnitRepository.updateEnableStatusByIdIn(EnableStatusEnum.DISABLE, ids);
  }

  @Override
  @Transactional
  public void deleteBatch(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "待修正的数据主键不能为空");
    materialUnitRepository.updateDelStatusByIdIn(DelFlagStatusEnum.DELETE, ids);
  }

  @Override
  public void deleteByUnitTypeCode(String unitTypeCode,List<String> unitCodes) {
    Validate.notNull(unitTypeCode, "待修正的物料单位类别不能为空");
    materialUnitRepository.updateDelStatusByUnitType(DelFlagStatusEnum.DELETE, unitTypeCode,unitCodes);
  }

  @Override
  public MaterialUnit findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    //重构查询方法
    return materialUnitRepository.findByIdAndTenantCode(id,TenantUtils.getTenantCode());
  }

  @Override
  public List<MaterialUnit> findByUnitTypeCode(String unitTypeCode) {
    return this.materialUnitRepository.findByUnitTypeCode(unitTypeCode);
  }

  @Override
  @Transactional
  public MaterialUnit create(MaterialUnit materialUnit) {
    Validate.notNull(materialUnit, "创建登录配置实体不能为空！");
    this.createValidate(materialUnit);
    //设置基本数据
    materialUnit.setCreateTime(new Date());
    if (StringUtils.isBlank(materialUnit.getEnableStatus())) {
      materialUnit.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    }
    materialUnit.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    materialUnit.setTenantCode(TenantUtils.getTenantCode());
    materialUnit.setCreateAccount(loginUserService.getLoginAccountName());
    this.materialUnitRepository.saveOrUpdate(materialUnit);
    //如果扩展配置不为空，添加配置扩展
    return materialUnit;
  }

  @Override
  @Transactional
  public MaterialUnit update(MaterialUnit materialUnit) {
    Validate.notNull(materialUnit, "更新登录配置实体不能为空！");
    Validate.notBlank(materialUnit.getId(), "登录登录配置ID不能为空！");
    this.createValidate(materialUnit);
    materialUnit.setModifyTime(new Date());
    materialUnit.setModifyAccount(loginUserService.getLoginAccountName());
    //新增租户编号
    materialUnit.setTenantCode(TenantUtils.getTenantCode());
    this.materialUnitRepository.saveOrUpdate(materialUnit);
    return materialUnit;
  }

  /**
   * 创建/编辑 数据校验
   *
   * @param materialUnit 物料单位实体
   */
  private void createValidate(MaterialUnit materialUnit) {
    Validate.notBlank(materialUnit.getUnitName(), "物料单位名称不能为空");
    Validate.notBlank(materialUnit.getUnitTypeCode(), "物料单位类别编码不能为空");
    Validate.notNull(materialUnit.getConvertScale(), "物料单位转换系数不能为空");
    Validate.notBlank(materialUnit.getStandardUnitFlag(), "物料单位标准单位标记不能为空");
    Validate.isTrue(materialUnit.getConvertScale().compareTo(new BigDecimal(BigInteger.ZERO)) > 0, "物料单位转换系数不能小于0");
    Validate.notNull(materialUnit.getMeasureConvertScale(), "EA单位转换系数不能为空");
    Validate.isTrue(materialUnit.getMeasureConvertScale().compareTo(new BigDecimal(BigInteger.ZERO)) > 0, "EA单位转换系数不能小于0");
    //校验物料单位类型
    MaterialUnitType materialUnitType = materialUnitTypeRepository.findByUnitTypeCode(materialUnit.getUnitTypeCode());
    Validate.notNull(materialUnitType, "物料单位类别不存在！");
  }
}
