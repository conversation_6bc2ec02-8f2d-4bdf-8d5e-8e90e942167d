<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.mdm.business.material.unit.mapper.MaterialUnitMapper">

  <resultMap id="materialUnitMap" type="com.biz.crm.mdm.business.material.unit.entity.MaterialUnit">
  </resultMap>

  <sql id="materialUnit">
    unit.id as id,
    unit.unit_code as unitCode ,
    unit.unit_name as unitName ,
    unit.unit_type_code as unitTypeCode ,
    unit.convert_scale as convertScale,
    unit.measure_convert_scale as measureConvertScale,
    unit.standard_unit_flag as standardUnitFlag,
    unit.standard_unit_code as standardUnitCode,
    unit.tenant_code as tenantCode,
    unit.del_flag as delFlag,
    unit.enable_status as enableStatus,
    unit.create_account as createAccount,
    unit.create_time as createTime,
    unit.modify_account as modifyAccount,
    unit.modify_time as modifyTime,
    unit.remark as remark
  </sql>


  <select id="findByConditions" resultMap="materialUnitMap">
    SELECT
    <include refid="materialUnit"/>
    FROM mdm_material_unit unit
    <where>
    	tenant_code = #{dto.tenantCode}
      <if test="dto.delFlag != null and dto.delFlag !=''">
        and unit.del_flag =#{dto.delFlag}
      </if>
      <if test="dto.enableStatus != null and dto.enableStatus !=''">
        and unit.enable_status =#{dto.enableStatus}
      </if>
      <if test="dto.unitTypeCode != null and dto.unitTypeCode !=''">
        and unit.unit_type_code =#{dto.unitTypeCode}
      </if>
    </where>
    ORDER BY unit.create_time DESC
  </select>

</mapper>
