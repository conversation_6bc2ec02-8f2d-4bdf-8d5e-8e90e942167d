package com.biz.crm.mdm.business.material.unit.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.material.unit.dto.MaterialUnitDto;
import com.biz.crm.mdm.business.material.unit.entity.MaterialUnit;
import com.biz.crm.mdm.business.material.unit.mapper.MaterialUnitMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;

/**
 * 物料单位 的 数据库访问类 {@link MaterialUnit}
 *
 * <AUTHOR>
 */
@Component
public class MaterialUnitRepository extends ServiceImpl<MaterialUnitMapper, MaterialUnit> {

  /**
   * 分页条件查询物料单位
   *
   * @param pageable 分页信息
   * @param dto 查询筛选条件
   * @return 分页数据
   */
  public Page<MaterialUnit> findByConditions(Page<MaterialUnit> pageable, MaterialUnitDto dto) {
    dto.setTenantCode(TenantUtils.getTenantCode()); // 过滤多租户
    return this.baseMapper.findByConditions(pageable, dto);
  }

  /**
   * 根据物料单位编码查询物料单位
   *
   * @param unitCode 物料单位编码
   * @return 物料单位
   */
  public MaterialUnit findByUnitCode(String unitCode) {
    return this.lambdaQuery().eq(MaterialUnit::getUnitCode, unitCode)
        .eq(MaterialUnit::getTenantCode, TenantUtils.getTenantCode())
        .one();
  }

  /**
   * 根据物料单位类别编码查询物料单位
   *
   * @param unitTypeCode 物料单位类别编码
   * @return 物料单位
   */
  public List<MaterialUnit> findByUnitTypeCode(String unitTypeCode) {
    return this.lambdaQuery().eq(MaterialUnit::getUnitTypeCode, unitTypeCode)
        .eq(MaterialUnit::getTenantCode, TenantUtils.getTenantCode())
        .eq(MaterialUnit::getDelFlag,DelFlagStatusEnum.NORMAL.getCode())
        .list();
  }

  public List<MaterialUnit> findByUnitTypeCodes(Set<String> unitTypeCodes) {
    if (CollectionUtil.isEmpty(unitTypeCodes)){
      return Lists.newArrayList();
    }
    return this.lambdaQuery()
            .in(MaterialUnit::getUnitTypeCode, unitTypeCodes)
            .eq(MaterialUnit::getTenantCode, TenantUtils.getTenantCode())
            .eq(MaterialUnit::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .list();
  }


  /**
   * 根据主键集合，修改 enable_status
   *
   * @param enable
   * @param ids
   */
  public void updateEnableStatusByIdIn(EnableStatusEnum enable, List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return;
    }
    this.lambdaUpdate().in(MaterialUnit::getId, ids)
        .set(MaterialUnit::getEnableStatus, enable.getCode())
        .eq(MaterialUnit::getTenantCode, TenantUtils.getTenantCode()) // 过滤多租户
        .update();
  }

  /**
   * 根据主键集合，修改 del_flag
   *
   * @param delFlagStatusEnum 删除标记
   * @param ids 主键id集合
   */
  public void updateDelStatusByIdIn(DelFlagStatusEnum delFlagStatusEnum, List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return;
    }
    this.lambdaUpdate().in(MaterialUnit::getId, ids)
        .set(MaterialUnit::getDelFlag, delFlagStatusEnum.getCode())
        .eq(MaterialUnit::getTenantCode, TenantUtils.getTenantCode()) // 过滤多租户
        .update();
  }

  /**
   * 根据单位类别编码，修改 del_flag
   *
   * @param delFlagStatusEnum 删除标记
   * @param unitTypeCode 单位类别编码
   */
  public void updateDelStatusByUnitType(DelFlagStatusEnum delFlagStatusEnum, String unitTypeCode,
      List<String> unitCodes) {
    this.lambdaUpdate().eq(MaterialUnit::getUnitTypeCode, unitTypeCode)
        .notIn(MaterialUnit::getUnitCode, unitCodes)
        .set(MaterialUnit::getDelFlag, delFlagStatusEnum.getCode())
        .eq(MaterialUnit::getTenantCode, TenantUtils.getTenantCode())
        .update();
  }

  /**
   * 重构查询方法
   * @param id
   * @param tenantCode
   * @return
   */
  public MaterialUnit findByIdAndTenantCode(String id, String tenantCode) {
    if (StringUtil.isEmpty(id)
            || StringUtil.isEmpty(tenantCode)) {
      return null;
    }
    return this.lambdaQuery()
        .eq(MaterialUnit::getTenantCode,tenantCode)
        .eq(MaterialUnit::getId,id)
        .one();
  }


  public List<MaterialUnit> findAllByOnlyKeys(List<String> onlyKeys) {
    if (CollectionUtil.isEmpty(onlyKeys)) {
      return Lists.newArrayList();
    }
    return this.lambdaQuery()
            .eq(MaterialUnit::getTenantCode, TenantUtils.getTenantCode())
            .in(MaterialUnit::getOnlyKey, onlyKeys)
            .list();
  }


  public void saveBatchXml(List<MaterialUnit> entityList) {
    if (CollectionUtil.isEmpty(entityList)) {
      return;
    }
    Lists.partition(entityList, CommonConstant.MAX_PAGE_SIZE).forEach(list -> {
      this.baseMapper.insertBatchSomeColumn(list);
    });

  }
  public void updateBatchXml(List<MaterialUnit> entityList) {
    if (CollectionUtil.isEmpty(entityList)) {
      return;
    }
    Lists.partition(entityList, CommonConstant.MAX_PAGE_SIZE).forEach(this::updateBatchById);

  }

  public void delByCodes(Set<String> codeList) {
    if (CollectionUtils.isEmpty(codeList)) {
      return;
    }
    this.lambdaUpdate()
            .in(MaterialUnit::getUnitTypeCode, codeList)
            .remove();
  }
}
