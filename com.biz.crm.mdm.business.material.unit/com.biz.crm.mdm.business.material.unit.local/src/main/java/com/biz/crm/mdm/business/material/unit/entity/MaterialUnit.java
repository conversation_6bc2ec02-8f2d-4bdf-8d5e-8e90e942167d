package com.biz.crm.mdm.business.material.unit.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/10/26 16:24
 * 物料单位实体类
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MaterialUnit", description = "物料单位实体类")
@Entity
@org.hibernate.annotations.Table(appliesTo = "mdm_material_unit", comment = "物料单位实体类")
@TableName("mdm_material_unit")
@Table(name = "mdm_material_unit", indexes = {
        @Index(name = "mdm_material_unit_uk1", columnList = "only_key", unique = true),
        @Index(name = "mdm_material_unit_idx1", columnList = "unit_type_code"),
        @Index(name = "mdm_material_unit_idx2", columnList = "tenant_code, del_flag, unit_type_code"),
})
public class MaterialUnit extends TenantFlagOpEntity {

    private static final long serialVersionUID = 8599246341788262253L;

    @ApiModelProperty("物料编码/物料单位类型编码")
    @Column(name = "unit_type_code", columnDefinition = "VARCHAR(32) COMMENT '物料编码/物料单位类型编码'")
    private String unitTypeCode;

    @ApiModelProperty("物料单位编码")
    @Column(name = "unit_code", columnDefinition = "VARCHAR(32) COMMENT '物料单位编码'")
    private String unitCode;

    @ApiModelProperty("物料单位名称")
    @Column(name = "unit_name", columnDefinition = "VARCHAR(128) COMMENT '物料名称/物料单位名称'")
    private String unitName;

    @ApiModelProperty("物料单位换算系数")
    @Column(name = "convert_scale", columnDefinition = "DECIMAL(20,4) COMMENT '换算系数'")
    private BigDecimal convertScale;

    @ApiModelProperty("EA单位换算系数")
    @Column(name = "measure_convert_scale", columnDefinition = "DECIMAL(20,4) COMMENT 'EA单位换算系数'")
    private BigDecimal measureConvertScale;

    @ApiModelProperty("标准物料单位编码")
    @Column(name = "standard_unit_code", columnDefinition = "VARCHAR(32) COMMENT '标准物料单位编码'")
    private String standardUnitCode;

    @ApiModelProperty("是否标准单位标准(Y/N)")
    @Column(name = "standard_unit_flag", columnDefinition = "VARCHAR(4) COMMENT '是否标准单位标准(Y/N)'")
    private String standardUnitFlag;

    @ApiModelProperty("MD5(物料编码+标准物料单位编码+物料单位编码)")
    @Column(name = "only_key", nullable = false, columnDefinition = "varchar(32) COMMENT 'MD5(物料编码+标准物料单位编码+物料单位编码)'")
    private String onlyKey;

}
