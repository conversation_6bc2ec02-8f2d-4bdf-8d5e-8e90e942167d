package com.biz.crm.mdm.business.material.unit.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.material.unit.service.MaterialUnitVoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * 物料单位controller
 */
@Api(tags = "物料单位VO(仅TEST使用)")
@RestController
@RequestMapping("v1/materialUnit/materialUnitVo")
public class MaterialUnitVoController {

  @Autowired(required = false)
  private MaterialUnitVoService materialUnitVoService;

  /**
   * 转换当前物料单位到标准单位数量
   */
  @ApiOperation(value = "转换当前物料单位到标准单位数量")
  @GetMapping("/convertStandUnit")
  public Result<BigDecimal> convertStandUnit(@RequestParam String unitCode) {
    return Result.ok(materialUnitVoService.findScaleByStandUnit(unitCode));
  }

  /**
   * 转换当前物料单位到目标单位数量
   */
  @ApiOperation(value = "转换当前物料单位到目标单位数量")
  @GetMapping("/convertGoalUnit")
  public Result<BigDecimal> convertGoalUnit(@RequestParam String fromUnitCode,String goalUnitCode,int scale,RoundingMode roundingMode) {
    return Result.ok(materialUnitVoService.findScaleByGoalUnit(fromUnitCode, goalUnitCode, scale, roundingMode));
  }
}
