package com.biz.crm.mdm.business.material.unit.service.internal;

import com.biz.crm.mdm.business.material.unit.dto.MaterialUnitTypeDto;
import com.biz.crm.mdm.business.material.unit.entity.MaterialUnit;
import com.biz.crm.mdm.business.material.unit.entity.MaterialUnitType;
import com.biz.crm.mdm.business.material.unit.repository.MaterialUnitRepository;
import com.biz.crm.mdm.business.material.unit.repository.MaterialUnitTypeRepository;
import com.biz.crm.mdm.business.material.unit.service.MaterialUnitTypeVoService;
import com.biz.crm.mdm.business.material.unit.service.MaterialUnitVoService;
import com.biz.crm.mdm.business.material.unit.vo.MaterialUnitTypeVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * 物料单位类别vo service实现
 */
@Service
public class MaterialUnitTypeVoServiceImpl implements MaterialUnitTypeVoService {

  @Autowired(required = false)
  private MaterialUnitTypeRepository materialUnitTypeRepository;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public List<MaterialUnitTypeVo> findMaterialUnitTypeByConditions(MaterialUnitTypeDto materialUnitTypeDto) {
    if (ObjectUtils.isEmpty(materialUnitTypeDto)) {
      return null;
    }
    List<MaterialUnitType> materialUnitTypes = materialUnitTypeRepository.findDetailsByConditions(materialUnitTypeDto);
    if (!CollectionUtils.isEmpty(materialUnitTypes)) {
      return (List<MaterialUnitTypeVo>) nebulaToolkitService.copyCollectionByWhiteList(materialUnitTypes, MaterialUnitType.class, MaterialUnitTypeVo.class, HashSet.class, ArrayList.class, "materialUnits");
    }
    return null;
  }
}
