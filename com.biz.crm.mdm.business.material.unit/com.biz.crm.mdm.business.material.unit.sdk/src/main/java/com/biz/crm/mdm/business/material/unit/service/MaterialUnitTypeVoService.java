package com.biz.crm.mdm.business.material.unit.service;

import com.biz.crm.mdm.business.material.unit.dto.MaterialUnitTypeDto;
import com.biz.crm.mdm.business.material.unit.vo.MaterialUnitTypeVo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/4 16:01
 * 物料单位类别VO service
 * 提供物料单位类别条件查询列表
 */
public interface MaterialUnitTypeVoService {

  /**
   * 条件查询物料单位类型(包含下级物料单位列表)
   * @param materialUnitTypeDto 查询条件
   * @return 物料单位类型列表
   */
  List<MaterialUnitTypeVo> findMaterialUnitTypeByConditions(MaterialUnitTypeDto materialUnitTypeDto);
}
