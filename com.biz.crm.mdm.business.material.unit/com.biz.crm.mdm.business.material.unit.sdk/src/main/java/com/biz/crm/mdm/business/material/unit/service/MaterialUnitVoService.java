package com.biz.crm.mdm.business.material.unit.service;

import com.biz.crm.mdm.business.material.unit.vo.MaterialUnitVo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/11/4 16:01
 * 物料单位VO service
 * 提供物料单位与标准单位转换，物料单位之间转换接口
 */
public interface MaterialUnitVoService {

    /**
     * 获取当前单位与标准单位的转换系数
     *
     * @param unitCode 物料单位编码
     * @return 转换系数
     */
    BigDecimal findScaleByStandUnit(String unitCode);

    /**
     * 获取源物料单位与目标单位的转换系数
     *
     * @param fromUnitCode 源物料单位编码
     * @param goalUnitCode 目标物料单位编码
     * @param decimalScale 转换时小数位数,默认2位小数
     * @param roundingMode 转换时小数保留模式，默认为 RoundingMode.HALF_UP
     * @return 转换系数
     */
    BigDecimal findScaleByGoalUnit(String fromUnitCode, String goalUnitCode, int decimalScale, RoundingMode roundingMode);

    /**
     * 根据物料编码获取单位集合
     *
     * @param materialCodeSet 物料编码
     * @return java.util.List<com.biz.crm.mdm.business.material.unit.vo.MaterialUnitVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/6/7 21:42
     */
    List<MaterialUnitVo> findByMaterialList(Set<String> materialCodeSet);
}
