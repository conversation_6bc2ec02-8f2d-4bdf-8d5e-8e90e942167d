package com.biz.crm.mdm.business.position.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.TreeRuleCodeStrategy;
import com.biz.crm.business.common.sdk.service.TreeRuleCodeStrategyHolder;
import com.biz.crm.mdm.business.position.level.sdk.service.PositionLevelVoService;
import com.biz.crm.mdm.business.position.level.sdk.vo.PositionLevelVo;
import com.biz.crm.mdm.business.position.local.entity.PositionEntity;
import com.biz.crm.mdm.business.position.local.entity.PositionOrgEntity;
import com.biz.crm.mdm.business.position.local.entity.PositionRoleEntity;
import com.biz.crm.mdm.business.position.local.repository.PositionOrgRepository;
import com.biz.crm.mdm.business.position.local.repository.PositionRepository;
import com.biz.crm.mdm.business.position.local.service.PositionRoleService;
import com.biz.crm.mdm.business.position.local.service.PositionService;
import com.biz.crm.mdm.business.position.sdk.constant.PositionConstant;
import com.biz.crm.mdm.business.position.sdk.dto.PositionDto;
import com.biz.crm.mdm.business.position.sdk.event.PositionEventListener;
import com.biz.crm.mdm.business.position.sdk.service.PositionOrgVoService;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionOrgVo;
import com.biz.crm.mdm.business.position.sdk.vo.PositionRelationVo;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 职位VO接口实现
 *
 * <AUTHOR>
 * @date 2021/10/13
 */
@Service
public class PositionVoServiceImpl implements PositionVoService {

  @Autowired(required = false)
  @Lazy
  private List<PositionEventListener> listeners;
  @Autowired(required = false)
  private PositionService positionService;
  @Autowired(required = false)
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;
  @Autowired(required = false)
  private PositionRoleService positionRoleService;
  @Autowired(required = false)
  private PositionRepository positionRepository;
  @Autowired(required = false)
  private TreeRuleCodeStrategyHolder treeRuleCodeStrategyHolder;
  @Autowired(required = false)
  private PositionLevelVoService positionLevelVoService;
  @Autowired(required = false)
  private PositionOrgRepository positionOrgRepository;

  @Override
  public List<PositionVo> findByIdsOrCodes(List<String> ids, List<String> positionCodes) {
    if (CollectionUtils.isEmpty(positionCodes) && CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<PositionEntity> list = this.positionService.findByIdsOrCodes(ids, positionCodes);
    if (CollectionUtils.isEmpty(list)) {
      return Lists.newArrayList();
    }
    return this.convertEntityToVo(list);
  }

  @Override
  public List<PositionVo> findByLotPositionCodes(List<String> positionCodes) {
    return this.findByIdsOrCodes(Lists.newArrayList(), positionCodes);
  }

  @Override
  public List<PositionVo> findDetailsByIdsOrCodes(List<String> ids, List<String> positionCodes) {
    if (CollectionUtils.isEmpty(positionCodes) && CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    List<PositionVo> list = this.findByIdsOrCodes(ids, positionCodes);
    if (!CollectionUtils.isEmpty(list)) {
      buildRelationData(list);
    }
    return list;
  }

  @Override
  public List<PositionVo> findAllChildrenByCode(String positionCode) {
    if (StringUtils.isBlank(positionCode)) {
      return Lists.newArrayList();
    }
    List<PositionEntity> entities = this.positionService.findByIdsOrCodes(null, Lists.newArrayList(positionCode));
    if (CollectionUtils.isEmpty(entities)) {
      return Lists.newArrayList();
    }
    List<PositionEntity> childrenList = this.positionService.findAllChildrenByRuleCode(entities.get(0).getRuleCode());
    if (CollectionUtils.isEmpty(childrenList)) {
      return Lists.newArrayList();
    }
    return Lists.newArrayList(this.nebulaToolkitService.copyCollectionByWhiteList(childrenList, PositionEntity.class
        , PositionVo.class, HashSet.class, ArrayList.class));
  }

  @Override
  public List<PositionVo> findByParentCode(String positionCode) {
    if (StringUtils.isBlank(positionCode)) {
      return Lists.newArrayList();
    }
    List<PositionEntity> entities = this.positionRepository.findByParentCode(positionCode, TenantUtils.getTenantCode());
    if (CollectionUtils.isEmpty(entities)) {
      return Lists.newArrayList();
    }
    return Lists.newArrayList(this.nebulaToolkitService.copyCollectionByWhiteList(entities, PositionEntity.class
        , PositionVo.class, HashSet.class, ArrayList.class));
  }

  @Override
  public PositionVo create(PositionDto dto) {
    PositionEntity entity = this.positionService.create(dto);
    if (entity == null) {
      return null;
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(entity, PositionVo.class, HashSet.class, ArrayList.class);
  }

  @Override
  public PositionVo update(PositionDto dto) {
    PositionEntity entity = this.positionService.update(dto);
    if (entity == null) {
      return null;
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(entity, PositionVo.class, HashSet.class, ArrayList.class);
  }

  @Override
  public PositionVo findByPositionCode(String positionCode) {
    if (StringUtil.isEmpty(positionCode)) {
      return null;
    }
    PositionEntity entity = this.positionService.findByPositionCode(positionCode);
    if (entity == null) {
      return null;
    }
    return this.nebulaToolkitService.copyObjectByWhiteList(entity, PositionVo.class, HashSet.class, ArrayList.class);
  }

  @Override
  public List<PositionVo> findAllParentByRoleCodes(List<String> roleCodes) {
    if (CollectionUtils.isEmpty(roleCodes)) {
      return Lists.newLinkedList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    List<PositionEntity> entities = this.positionRepository.findByRoleCodes(roleCodes, tenantCode);
    if (CollectionUtils.isEmpty(entities)) {
      return Lists.newLinkedList();
    }
    List<String> ruleCodes = entities.stream().map(PositionEntity::getRuleCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
    TreeRuleCodeStrategy treeRuleCodeStrategy = this.treeRuleCodeStrategyHolder.getStrategy(null);
    Set<String> parentRuleCodes = treeRuleCodeStrategy.findParentRuleCodeByRuleCodes(PositionConstant.POSITION_RULE_CODE_LENGTH, ruleCodes);
    List<PositionEntity> parentEntities = this.positionRepository
        .findByRuleCodesAndEnableStatus(Lists.newArrayList(parentRuleCodes), null, TenantUtils.getTenantCode());
    if (CollectionUtils.isEmpty(parentEntities)) {
      return Lists.newLinkedList();
    }
    return (List<PositionVo>) this.nebulaToolkitService.copyCollectionByWhiteList(parentEntities, PositionEntity.class
        , PositionVo.class, HashSet.class, ArrayList.class);
  }

  @Override
  public List<PositionVo> findPositionsByRoleCodes(List<String> roleCodes) {
    if (CollectionUtils.isEmpty(roleCodes)) {
      return Lists.newLinkedList();
    }
    String tenantCode = TenantUtils.getTenantCode();
    List<PositionEntity> entities = this.positionRepository.findByRoleCodes(roleCodes, tenantCode);
    if (CollectionUtils.isEmpty(entities)) {
      return Lists.newLinkedList();
    }
    return (List<PositionVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entities, PositionEntity.class
        , PositionVo.class, HashSet.class, ArrayList.class);
  }

  @Override
  public Set<String> findPositionCodesByPositionLevelCodes(List<String> positionLevelCodes) {
    if (CollectionUtils.isEmpty(positionLevelCodes)) {
      return new HashSet<>();
    }
    List<PositionEntity> byPositionLevelCodes = this.positionRepository.findByPositionLevelCodes(positionLevelCodes, TenantUtils.getTenantCode());
    if (CollectionUtils.isEmpty(byPositionLevelCodes)) {
      return new HashSet<>();
    }
    return byPositionLevelCodes.stream().map(PositionEntity::getPositionCode).collect(Collectors.toSet());
  }

  @Override
  public Set<String> findRolesByPositionCodes(List<String> positionCodes) {
    if (CollectionUtils.isEmpty(positionCodes)) {
      return new HashSet<>(0);
    }
    List<PositionRoleEntity> byPositionCodes = this.positionRoleService.findByPositionCodes(positionCodes);
    if (CollectionUtils.isEmpty(byPositionCodes)) {
      return new HashSet<>(0);
    }
    return byPositionCodes.stream().map(PositionRoleEntity::getRoleCode).collect(Collectors.toSet());
  }

  /**
   * 通过职位编码查询全部上级
   *
   * @param positionCode
   * @return
   */
  @Override
  public List<PositionVo> findAllParentByPositionCode(String positionCode) {
    return this.findParent(positionCode, true);
  }

  /**
   * 通过职位编码查询直属上级
   *
   * @param positionCode
   * @return
   */
  @Override
  public List<PositionVo> findParentByPositionCode(String positionCode) {
    return this.findParent(positionCode, false);
  }

  /**
   * 通过职位编码   isAll为true 代表查询全部上级  isAll为false  代表查询直属上级
   *
   * @param positionCode
   * @param isAll
   * @return
   */
  private List<PositionVo> findParent(String positionCode, Boolean isAll) {
    if (StringUtils.isBlank(positionCode) || ObjectUtils.isEmpty(isAll)) {
      return new ArrayList<>();
    }
    List<String> allRuleCodeList = new ArrayList<>();
    PositionEntity byPositionCode = this.positionService.findByPositionCode(positionCode);
    if (ObjectUtils.isEmpty(byPositionCode)){
      return new ArrayList<>();
    }
    String ruleCode = byPositionCode.getRuleCode();
    int length = ruleCode.length();
    int positionRuleCodeLength = PositionConstant.POSITION_RULE_CODE_LENGTH;
    if (isAll){
      //获取分割次数
      int count = length / positionRuleCodeLength;
      for (int i = 1; i <= count; i++) {
        String substring = ruleCode.substring(0, (positionRuleCodeLength * i));
        allRuleCodeList.add(substring);
      }
    }else {
      String substring = ruleCode.substring(0, length - positionRuleCodeLength);
      allRuleCodeList.add(substring);
    }
    List<PositionEntity> byRuleCodesAndEnableStatus = this.positionRepository.findByRuleCodesAndEnableStatus(allRuleCodeList, EnableStatusEnum.ENABLE.getCode(), TenantUtils.getTenantCode());
    if (CollectionUtils.isEmpty(byRuleCodesAndEnableStatus)){
      return new ArrayList<>(0);
    }
    return (List<PositionVo>) this.nebulaToolkitService.copyCollectionByWhiteList(byRuleCodesAndEnableStatus,PositionEntity.class,PositionVo.class,HashSet.class,ArrayList.class);
  }

  /**
   * 职位实体列表转VO列表
   *
   * @param entities 职位实体列表
   * @return 职位VO列表
   */
  private List<PositionVo> convertEntityToVo(List<PositionEntity> entities) {
    List<PositionVo> list = Lists.newArrayList(this.nebulaToolkitService.copyCollectionByWhiteList(entities, PositionEntity.class
        , PositionVo.class, HashSet.class, ArrayList.class));
    //补充职位级别名称
    List<String> positionCodeList = list.stream().map(PositionVo::getPositionCode).distinct().collect(Collectors.toList());
    List<PositionLevelVo> positionLevelVos = positionLevelVoService.findByIdsOrCodes(Lists.newLinkedList(), list.stream().map(PositionVo::getPositionLevelCode).collect(Collectors.toList()));
    if(ObjectUtils.isNotEmpty(positionLevelVos)){
      Map<String, String> positionLevelCodes = positionLevelVos.stream().collect(Collectors.toMap(PositionLevelVo::getPositionLevelCode, PositionLevelVo::getPositionLevelName, (k1, k2)->k2));
      list.forEach(positionVo -> {
        positionVo.setPositionLevelName(StringUtils.isNotEmpty(positionVo.getPositionLevelCode()) ? positionLevelCodes.get(positionVo.getPositionLevelCode()) : null);
      });
    }

    List<PositionRoleEntity> positionRoles = this.positionRoleService.findByPositionCodes(positionCodeList);
    if (!CollectionUtils.isEmpty(positionRoles)) {
      Map<String, List<String>> positionRoleMap = positionRoles.stream().collect(Collectors
          .groupingBy(PositionRoleEntity::getPositionCode, Collectors.mapping(PositionRoleEntity::getRoleCode, Collectors.toList())));
      list.forEach(positionVo -> {
        positionVo.setRoleList(positionRoleMap.get(positionVo.getPositionCode()));
      });
    }
    return list;
  }

  /**
   * 封装职位上级模块关联数据
   *
   * @param list 待封装职位列表
   */
  private void buildRelationData(List<PositionVo> list) {
      if (CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(listeners)) {
          return;
      }
      List<String> positionCodes = list.stream().map(PositionVo::getPositionCode).collect(Collectors.toList());
      List<PositionRelationVo> relationList = Lists.newArrayList();
      for (PositionEventListener positionEventListener : listeners) {
          List<PositionRelationVo> dataList = positionEventListener.onRequestByPositionCodes(positionCodes);
          if (CollectionUtils.isEmpty(dataList)) {
              continue;
          }
          relationList.addAll(dataList);
      }

      if (!CollectionUtils.isEmpty(relationList)) {
          Map<String, List<PositionRelationVo>> relationMap = relationList.stream().collect(Collectors
                  .groupingBy(PositionRelationVo::getPositionCode));
          list.forEach(positionVo -> {
              positionVo.setRelationData(relationMap.get(positionVo.getPositionCode()));
          });
      }

      List<PositionOrgEntity> positionOrgEntityList = positionOrgRepository.findByPositionCodes(positionCodes, TenantUtils.getTenantCode());
      if (CollectionUtil.isNotEmpty(positionOrgEntityList)) {
          List<PositionOrgVo> whiteList = (List<PositionOrgVo>) this.nebulaToolkitService.copyCollectionByWhiteList(positionOrgEntityList, PositionOrgEntity.class
                  , PositionOrgVo.class, HashSet.class, ArrayList.class);
          Map<String, List<PositionOrgVo>> positionOrgMap = whiteList.stream().collect(Collectors
                  .groupingBy(PositionOrgVo::getPositionCode));
          list.forEach(positionVo -> {
              positionVo.setPositionOrgList(positionOrgMap.get(positionVo.getPositionCode()));
          });
      }

  }
}
