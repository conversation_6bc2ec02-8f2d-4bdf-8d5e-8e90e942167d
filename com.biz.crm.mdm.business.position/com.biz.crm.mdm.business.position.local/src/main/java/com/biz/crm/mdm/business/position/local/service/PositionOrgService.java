package com.biz.crm.mdm.business.position.local.service;

import com.biz.crm.mdm.business.position.local.entity.PositionOrgEntity;
import com.biz.crm.mdm.business.position.sdk.dto.PositionOrgDto;

import java.util.List;

/**
 * 职位-角色关联表接口
 *
 * <AUTHOR>
 * @date 2021/10/12
 */
public interface PositionOrgService {

  /**
   * 通过职位编码获取职位角色列表
   *
   * @param positionCode 职位编码
   * @return 职位角色列表
   */
  List<PositionOrgEntity> findByPositionCode(String positionCode);

  /**
   * 通过职位编码集合获取职位角色列表
   *
   * @param positionCodes 职位编码集合
   * @return 职位角色列表
   */
  List<PositionOrgEntity> findByPositionCodes(List<String> positionCodes);

  /**
   * 批量移除职位关联的角色
   *
   * @param positionCodes 职位编码集合
   */
  void unbindByPositionCodes(List<String> positionCodes);

  /**
   * 添加职位关联的角色
   *
   * @param positionCode 职位编码
   * @param orgCodes    角色编码集合
   */
  void bindByPositionCodeAndOrgCodes(String positionCode, List<String> orgCodes);

  /**
   * 通过角色编码集合获取职位角色关联实体列表
   *
   * @param orgCodes 角色编码集合
   * @return 职位角色关联实体列表
   */
  List<PositionOrgEntity> findByOrgCodes(List<String> orgCodes);

  /**
   * 通过职位编码集合和职位编码绑定职位角色关联关系
   *
   * @param positionCodes 职位编码集合
   * @param orgCode    角色编码集合
   */
  void bindByPositionCodesAndOrgCode(List<String> positionCodes, String orgCode);

  /**
   * 通过职位编码集合和职位编码解绑职位角色关联关系
   *
   * @param positionCodes 职位编码集合
   * @param orgCode    角色编码集合
   */
  void unbindByPositionCodesAndOrgCode(List<String> positionCodes, String orgCode);

  void bindAndOrgCodes(String positionCode, List<PositionOrgDto> positionOrgList);
}
