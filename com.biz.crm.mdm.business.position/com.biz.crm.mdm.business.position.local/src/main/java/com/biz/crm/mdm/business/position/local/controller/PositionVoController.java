package com.biz.crm.mdm.business.position.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * 职位管理: PositionVo: 职位信息
 *
 * <AUTHOR>
 * @date 2021/10/22
 */
@Slf4j
@RestController
@RequestMapping("/v1/position/position")
@Api(tags = "职位管理: PositionVo: 职位信息")
public class PositionVoController {

  @Autowired(required = false)
  private PositionVoService positionVoService;

  /**
   * 详情(编辑页面使用),通过主键进行数据的查询
   *
   * @param id 职位ID
   * @return 职位详情
   */
  @ApiOperation(value = "详情(编辑页面使用),通过主键进行数据的查询")
  @GetMapping("/findDetailsById")
  public Result<PositionVo> findDetailsById(@RequestParam(value = "id") @ApiParam(name = "id", value = "主键ID") String id) {
    try {
      List<PositionVo> list = positionVoService.findDetailsByIdsOrCodes(Lists.newArrayList(id), null);
      if (CollectionUtils.isEmpty(list)) {
        return Result.ok((PositionVo) null);
      }
      return Result.ok(list.get(0));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 详情(编辑页面使用),通过职位编码进行数据的查询
   *
   * @param positionCode 职位编码
   * @return 详情(编辑页面使用)
   */
  @ApiOperation(value = "详情(编辑页面使用),通过职位编码进行数据的查询")
  @GetMapping("/findDetailsByPositionCode")
  public Result<PositionVo> findDetailsByPositionCode(@RequestParam(value = "positionCode") @ApiParam(name = "positionCode", value = "职位编码") String positionCode) {
    try {
      List<PositionVo> list = positionVoService.findDetailsByIdsOrCodes(null, Lists.newArrayList(positionCode));
      if (CollectionUtils.isEmpty(list)) {
        return Result.ok((PositionVo) null);
      }
      return Result.ok(list.get(0));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据角色编码集合查询角色编码关联的职位及其所有上级职位(包含角色关联职位自身)
   *
   * @param roleCodes 角色编码集合
   * @return 职位及其所有上级职位
   */
  @ApiOperation(value = "根据角色编码集合查询角色编码关联的职位及其所有上级职位(包含角色关联职位自身)")
  @GetMapping("/findAllParentByRoleCodes")
  public Result<List<PositionVo>> findAllParentByRoleCodes(@RequestParam(value = "roleCodes") @ApiParam(name = "roleCodes", value = "角色编码集合") List<String> roleCodes) {
    try {
      return Result.ok(positionVoService.findAllParentByRoleCodes(roleCodes));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @ApiOperation(value = "根据角色编码集合查询角色编码关联的职位")
  @GetMapping("/findPositionsByRoleCodes")
  public Result<List<PositionVo>> findPositionsByRoleCodes(@RequestParam(value = "roleCodes") List<String> roleCodes) {
    try {
      return Result.ok(positionVoService.findPositionsByRoleCodes(roleCodes));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据职位编码查询该职位的所有下级职位(包含自身)
   *
   * @param positionCode 职位编码
   * @return 所有下级职位
   */
  @ApiOperation(value = "根据职位编码查询该职位的所有下级职位(包含自身)")
  @GetMapping("/findAllChildrenByCode")
  public Result<List<PositionVo>> findAllChildrenByCode(@RequestParam(value = "positionCode") @ApiParam(name = "positionCode", value = "职位编码") String positionCode) {
    try {
      return Result.ok(positionVoService.findAllChildrenByCode(positionCode));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据职位编码查询该职位的直属下级
   *
   * @param positionCode 职位编码
   * @return 直属下级职位
   */
  @ApiOperation(value = "根据职位编码查询该职位的直属下级")
  @GetMapping("/findByParentCode")
  public Result<List<PositionVo>> findByParentCode(@RequestParam(value = "positionCode") @ApiParam(name = "positionCode", value = "职位编码") String positionCode) {
    try {
      return Result.ok(positionVoService.findByParentCode(positionCode));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据职位id或者code集合查询职位列表
   *
   * @param ids           职位ID集合
   * @param positionCodes 职位编码集合
   * @return 职位列表
   */
  @ApiOperation(value = "根据职位id或者code集合查询职位列表")
  @GetMapping("/findByIdsOrCodes")
  public Result<List<PositionVo>> findByIdsOrCodes(
      @RequestParam(value = "ids", required = false) @ApiParam(name = "ids", value = "职位ID集合",
          required = false) List<String> ids,
      @RequestParam(value = "positionCodes", required = false) @ApiParam(name = "positionCodes", value = "职位编码集合",
          required = false) List<String> positionCodes) {
    try {
      return Result.ok(positionVoService.findByIdsOrCodes(ids, positionCodes));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据大量职位code查询职位列表
   *
   * @param positionCodes 职位编码集合
   * @return 职位列表
   */
  @ApiOperation(value = "根据大量职位code查询职位列表")
  @PostMapping("/findByLotPositionCodes")
  public Result<List<PositionVo>> findByLotPositionCodes(@RequestBody List<String> positionCodes) {
    try {
      return Result.ok(positionVoService.findByLotPositionCodes(positionCodes));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据职位编码查询角色编码集合
   *
   * @param positionCodes 职位编码集合
   * @return 角色编码集合
   */
  @ApiOperation(value = "根据职位编码查询角色编码集合")
  @GetMapping("/findRolesByPositionCodes")
  public Result<Set<String>> findRolesByPositionCodes(@RequestParam(value = "positionCodes") @ApiParam(name = "positionCodes", value = "职位编码集合") List<String> positionCodes) {
    try {
      return Result.ok(positionVoService.findRolesByPositionCodes(positionCodes));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据职位编码查询职位信息
   *
   * @param positionCode 职位编码
   * @return 角色编码集合
   */
  @ApiOperation(value = "根据职位编码查询职位信息")
  @GetMapping("/findByPositionCode")
  public Result<PositionVo> findByPositionCode(@RequestParam(value = "positionCode",required = false)
                                                 @ApiParam(name = "positionCode", value = "职位编码") String positionCode) {
    try {
      return Result.ok(positionVoService.findByPositionCode(positionCode));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

}
