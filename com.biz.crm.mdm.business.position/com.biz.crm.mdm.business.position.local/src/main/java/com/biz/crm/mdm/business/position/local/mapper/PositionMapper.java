package com.biz.crm.mdm.business.position.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.mdm.business.position.local.entity.PositionEntity;
import com.biz.crm.mdm.business.position.sdk.vo.PositionLevelCountVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 职位的mybatis-plus接口类 {@link PositionEntity}
 *
 * <AUTHOR>
 * @date 2021/9/29
 */
public interface PositionMapper extends BaseMapper<PositionEntity> {

    /**
     * 更具降维编码集合查询所有子级职位列表
     *
     * @param ruleCodes    降维编码集合
     * @param enableStatus 启用状态
     * @param tenantCode   租户编码
     * @return 所有子级职位列表
     */
    List<PositionEntity> findAllChildrenByRuleCodes(@Param("ruleCodes") List<String> ruleCodes, @Param("enableStatus") String enableStatus,
                                                    @Param("tenantCode") String tenantCode);

    /**
     * 将parentCode不为空但找不到对应上级的数据,设置parentCode为null
     *
     * @param tenantCode 租户编码
     */
    void updateOrphanParentCodeNull(@Param("tenantCode") String tenantCode);

    /**
     * 通过角色编码集合获取职位信息
     *
     * @param roleCodes  角色编码集合
     * @param tenantCode 租户编码
     * @return 职位实体集合
     */
    List<PositionEntity> findByRoleCodes(@Param("roleCodes") List<String> roleCodes, @Param("tenantCode") String tenantCode);

    /**
     * 根据编码规则获取职位信息
     *
     * @param ruleCodes
     * @param enableStatus
     * @param tenantCode
     * @return
     */
    List<PositionEntity> findByRuleCodesAndEnableStatus(@Param("roleCodes") List<String> ruleCodes, @Param("enableStatus") String enableStatus,
                                                        @Param("tenantCode") String tenantCode);

    /**
     * 根据ID或编码获取职位
     *
     * @param ids
     * @param positionCodes
     * @param tenantCode
     * @return
     */
    List<PositionEntity> findByIdsOrCodes(@Param("ids") List<String> ids, @Param("positionCodes") List<String> positionCodes, @Param("tenantCode") String tenantCode);

    /**
     * 根据上级编码查询职位信息
     *
     * @param parentCodes
     * @param tenantCode
     * @return
     */
    List<PositionEntity> findByParentCodes(@Param("parentCodes") List<String> parentCodes, @Param("tenantCode") String tenantCode);


    /**
     * 根据ID或编码获取职位
     *
     * @param id
     * @param positionCode
     * @param tenantCode
     * @return
     */
    PositionEntity findByIdOrPositionCode(@Param("id") String id, @Param("positionCode") String positionCode, @Param("tenantCode") String tenantCode);

    /**
     * 根据职位级别编码查询职位信息
     *
     * @param positionLevelCodes
     * @param tenantCode
     * @return
     */
    List<PositionEntity> findByPositionLevelCodes(@Param("positionLevelCodes") List<String> positionLevelCodes, @Param("tenantCode") String tenantCode);

    /**
     * 根据职位名称获取职位信息
     *
     * @param positionName
     * @param tenantCode
     * @return
     */
    List<PositionEntity> findByPositionNameLike(@Param("positionName") String positionName, @Param("tenantCode") String tenantCode);

    /**
     * 统计已有职位数量
     *
     * @param positionLevelCodeList
     * @return
     */
    List<PositionLevelCountVo> getCountByPositionLevelCodes(@Param("positionLevelCodeList") List<String> positionLevelCodeList);
}
