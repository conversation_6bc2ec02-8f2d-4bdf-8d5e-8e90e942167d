package com.biz.crm.mdm.business.position.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.dto.TreeDto;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.business.common.sdk.service.TreeRuleCodeStrategy;
import com.biz.crm.business.common.sdk.service.TreeRuleCodeStrategyHolder;
import com.biz.crm.mdm.business.position.level.sdk.service.PositionLevelVoService;
import com.biz.crm.mdm.business.position.level.sdk.vo.PositionLevelVo;
import com.biz.crm.mdm.business.position.local.entity.PositionEntity;
import com.biz.crm.mdm.business.position.local.repository.PositionRepository;
import com.biz.crm.mdm.business.position.local.service.PositionOrgService;
import com.biz.crm.mdm.business.position.local.service.PositionRoleService;
import com.biz.crm.mdm.business.position.local.service.PositionService;
import com.biz.crm.mdm.business.position.sdk.constant.PositionConstant;
import com.biz.crm.mdm.business.position.sdk.dto.PositionDto;
import com.biz.crm.mdm.business.position.sdk.dto.PositionLogDto;
import com.biz.crm.mdm.business.position.sdk.dto.RebindParentPositionDto;
import com.biz.crm.mdm.business.position.sdk.event.PositionEventListener;
import com.biz.crm.mdm.business.position.sdk.event.PositionLogEventListener;
import com.biz.crm.mdm.business.position.sdk.vo.PositionEventVo;
import com.biz.crm.mdm.business.position.sdk.vo.PositionLevelCountVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 职位表接口实现
 *
 * <AUTHOR>
 * @date 2021/10/12
 */
@Service
public class PositionServiceImpl implements PositionService {

  @Autowired(required = false)
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;
  @Autowired(required = false)
  private PositionLevelVoService positionLevelVoService;
  @Autowired(required = false)
  private PositionRepository positionRepository;
  @Autowired(required = false)
  private GenerateCodeService generateCodeService;
  @Autowired(required = false)
  private TreeRuleCodeStrategyHolder treeRuleCodeStrategyHolder;
  @Autowired(required = false)
  private PositionRoleService positionRoleService;
  @Autowired(required = false)
  @Lazy
  private List<PositionEventListener> listeners;
  @Autowired(required = false)
  private NebulaNetEventClient nebulaNetEventClient;
  @Autowired(required = false)
  private PositionOrgService positionOrgService;

  @Override
  @Transactional
  public PositionEntity create(PositionDto dto) {
    this.createValidation(dto);
    PositionEntity entity = this.nebulaToolkitService.copyObjectByWhiteList(dto, PositionEntity.class, HashSet.class, ArrayList.class);
    //自动填充职位名称
    if (StringUtils.isEmpty(entity.getPositionName())) {
      List<PositionLevelVo> positionLevels = this.positionLevelVoService.findByIdsOrCodes(null, Lists.newArrayList(dto.getPositionLevelCode()));
      Validate.isTrue(!CollectionUtils.isEmpty(positionLevels), "无效的职位级别");
      PositionLevelVo positionLevel = positionLevels.get(0);
      List<PositionEntity> entities = this.positionRepository.findByPositionNameLike(positionLevel.getPositionLevelName(), dto.getTenantCode());
      Set<String> positionNameSet = !CollectionUtils.isEmpty(entities) ?
          entities.stream().map(PositionEntity::getPositionName).collect(Collectors.toSet()) : null;
      entity.setPositionName(this.positionLevelVoService.createPositionNameByPositionLevelCode(dto.getPositionLevelCode(), positionNameSet, dto.getTenantCode()));
    }
    if (StringUtils.isEmpty(entity.getPositionCode())) {
      entity.setPositionCode(this.generateCodeService.generateCode(PositionConstant.POSITION_CODE));
    }
    PositionEntity position = this.positionRepository.findByPositionCode(entity.getPositionCode(), dto.getTenantCode());
    Validate.isTrue(Objects.isNull(position), "职位编码已经存在");
    int levelNum = 1;
    PositionEntity parentPosition = null;
    if (StringUtils.isNotEmpty(dto.getParentCode())) {
      parentPosition = this.positionRepository.findByPositionCode(dto.getParentCode(), dto.getTenantCode());
      Validate.notNull(parentPosition, "上级职位不存在");
      Validate.isTrue(EnableStatusEnum.ENABLE.getCode().equals(parentPosition.getEnableStatus()), "上级职位被禁用");
      if (StringUtil.isNotEmpty(entity.getParentCode())){
        Validate.isTrue(!entity.getParentCode().equals(entity.getPositionCode()), "上级职位不能是自己");
      }
      levelNum = parentPosition.getLevelNum() + 1;
    }
    entity.setRuleCode(this.createRuleCodeByParentPosition(parentPosition));
    entity.setLevelNum(levelNum);
    entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    //新增租户编号
    entity.setTenantCode(TenantUtils.getTenantCode());
    this.positionRepository.save(entity);
    //绑定角色关系
    this.positionRoleService.bindByPositionCodeAndRoleCodes(entity.getPositionCode(), dto.getRoleCodeList());
    positionOrgService.bindAndOrgCodes(entity.getPositionCode(), dto.getPositionOrgList());
    //新增职位事件通知
    if (!CollectionUtils.isEmpty(listeners)) {
      PositionEventVo vo = this.nebulaToolkitService.copyObjectByBlankList(dto, PositionEventVo.class, HashSet.class, ArrayList.class);
      vo.setPositionCode(entity.getPositionCode());
      listeners.forEach(positionEventListener -> {
        positionEventListener.onCreate(vo);
      });
    }
    PositionLogDto positionLogDto = new PositionLogDto();
    positionLogDto.setNewest(this.nebulaToolkitService.copyObjectByWhiteList(entity, PositionDto.class, HashSet.class, ArrayList.class));
    SerializableBiConsumer<PositionLogEventListener, PositionLogDto> onCreate =
        PositionLogEventListener::onCreate;
    this.nebulaNetEventClient.publish(positionLogDto, PositionLogEventListener.class, onCreate);
    return entity;
  }

  @Override
  @Transactional
  public PositionEntity update(PositionDto dto) {
    this.updateValidation(dto);
    PositionEntity entity = this.positionRepository.findByIdAndTenantCode(dto.getId(),TenantUtils.getTenantCode());
    Validate.notNull(entity, "职位不存在");
    Validate.isTrue(entity.getPositionCode().equals(dto.getPositionCode()), "职位编码不能修改");

    String entityRuleCode = entity.getRuleCode();
    //父级职位是否发生变化(前后父级编码都为空或者null,则视为没有变动,否则比较编码是否相等判定是否变化)
    boolean parentChange = (StringUtils.isNotBlank(dto.getParentCode()) || StringUtils.isNotBlank(entity.getParentCode()))
        && !Objects.equals(dto.getParentCode(), entity.getParentCode());
    int levelNum = 1;
    PositionEntity parentPosition = null;
    //只有父级编码变动时才进行父级职位状态校验
    if (StringUtils.isNotBlank(dto.getParentCode()) && parentChange) {
      Validate.isTrue(!dto.getParentCode().equals(entity.getPositionCode()), "上级职位不能为当前职位");
      parentPosition = this.positionRepository.findByPositionCode(dto.getParentCode(), dto.getTenantCode());
      Validate.notNull(parentPosition, "上级职位不存在");
      Validate.isTrue(EnableStatusEnum.ENABLE.getCode().equals(parentPosition.getEnableStatus()), "上级职位被禁用");
      String parentRuleCode = parentPosition.getRuleCode();
      Validate.notBlank(parentRuleCode, "上级职位ruleCode不能为空");
      Validate.isTrue(!parentRuleCode.startsWith(entityRuleCode), "上级职位不能是当前职位的下级职位");
      levelNum = parentPosition.getLevelNum() + 1;
    }
    PositionEntity updateEntity = this.nebulaToolkitService.copyObjectByWhiteList(dto, PositionEntity.class, HashSet.class, ArrayList.class);
    updateEntity.setParentCode(Optional.ofNullable(dto.getParentCode()).orElse(""));
    //重构修改方法
    this.positionRepository.updateById(updateEntity);
    if (parentChange) {
      //更新当前及下级降维编码
      String ruleCode = this.createRuleCodeByParentPosition(parentPosition);
      updateRuleCodeAllChildren(entity.getPositionCode(), ruleCode, levelNum);
    }
    //重新绑定角色关系
    this.positionRoleService.unbindByPositionCodes(Lists.newArrayList(dto.getPositionCode()));
    if (!CollectionUtils.isEmpty(dto.getRoleCodeList())) {
      this.positionRoleService.bindByPositionCodeAndRoleCodes(dto.getPositionCode(), dto.getRoleCodeList());
    }
    positionOrgService.bindAndOrgCodes(entity.getPositionCode(), dto.getPositionOrgList());
    //更新职位事件通知
    if (!CollectionUtils.isEmpty(listeners)) {
      PositionEventVo oldVo = this.nebulaToolkitService.copyObjectByBlankList(entity, PositionEventVo.class, HashSet.class, ArrayList.class);
      PositionEventVo newVo = this.nebulaToolkitService.copyObjectByBlankList(dto, PositionEventVo.class, HashSet.class, ArrayList.class);
      listeners.forEach(positionEventListener -> {
        positionEventListener.onUpdate(oldVo, newVo);
      });
    }
    //日志处理
    PositionLogDto positionLogDto = new PositionLogDto();
    positionLogDto.setOriginal(this.nebulaToolkitService.copyObjectByWhiteList(entity, PositionDto.class, HashSet.class, ArrayList.class));
    positionLogDto.setNewest(dto);
    SerializableBiConsumer<PositionLogEventListener, PositionLogDto> onUpdate =
        PositionLogEventListener::onUpdate;
    this.nebulaNetEventClient.publish(positionLogDto, PositionLogEventListener.class, onUpdate);
    return updateEntity;
  }

  @Override
  @Transactional
  public void deleteBatch(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "缺失id");
    List<PositionEntity> entities = this.positionRepository.listByIds(ids);
    Validate.isTrue(!CollectionUtils.isEmpty(entities) && entities.size() == ids.size(), "数据删除个数不匹配");
    String tenantCode = TenantUtils.getTenantCode();
    Map<String, PositionEntity> entityMap = entities.stream().collect(Collectors.toMap(PositionEntity::getPositionCode, t -> t));
    List<PositionEntity> childrenPositions = this.positionRepository.findByParentCodes(Lists.newArrayList(entityMap.keySet()), tenantCode);
    if (!CollectionUtils.isEmpty(childrenPositions)) {
      throw new IllegalArgumentException("职位[" + entityMap.get(childrenPositions.get(0).getParentCode()).getPositionName()
          + "]存在下级，不能删除");
    }
    this.positionRepository.updateDelFlagByIds(ids);
    //解绑职位关联的角色
    this.positionRoleService.unbindByPositionCodes(Lists.newArrayList(entityMap.keySet()));
    //删除职位事件通知
    if (!CollectionUtils.isEmpty(listeners)) {
      List<PositionEventVo> voList = Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(entities, PositionEntity.class
          , PositionEventVo.class, HashSet.class, ArrayList.class));
      listeners.forEach(positionEventListener -> {
        positionEventListener.onDelete(voList);
      });
    }
  }

  /**
   * 批量启用职位
   * 1.校验数据有效性
   * 2.根据职位的ruleCode,分离出所有上级职位的ruleCode,并根据分离的ruleCode查询出所有上级职位
   * 3.判断上级职位是否已经被禁用
   * 4.更新传入职位及其下级职位的启用状态为启用
   * 5.发布职位启用事件通知
   *
   * @param ids 职位id集合
   */
  @Override
  @Transactional
  public void enableBatch(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "缺失id");
    List<PositionEntity> entities = this.positionRepository.listByIds(ids);
    Validate.isTrue(!CollectionUtils.isEmpty(entities) && entities.size() == ids.size(), "数据启用个数不匹配");
    //校验上级职位是否被禁用
    List<String> ruleCodes = entities.stream().map(PositionEntity::getRuleCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
    Validate.notNull(this.treeRuleCodeStrategyHolder, "系统未配置降维码策略控制器");
    TreeRuleCodeStrategy treeRuleCodeStrategy = this.treeRuleCodeStrategyHolder.getStrategy(null);
    Validate.notNull(treeRuleCodeStrategy, "系统未配置降维码策略");
    Set<String> parentRuleCodes = treeRuleCodeStrategy.findParentRuleCodeByRuleCodesExcludeAnySelf(PositionConstant.POSITION_RULE_CODE_LENGTH, ruleCodes);
    if (!CollectionUtils.isEmpty(parentRuleCodes)) {
      List<PositionEntity> parentDisableList = this.positionRepository.findByRuleCodesAndEnableStatus(Lists.newArrayList(parentRuleCodes), EnableStatusEnum.DISABLE.getCode(), TenantUtils.getTenantCode());
      if (!CollectionUtils.isEmpty(parentDisableList)) {
        throw new IllegalArgumentException("存在未启用的上级职位[" + parentDisableList.get(0).getPositionName() + "],不能启用当前职位");
      }
    }
    this.positionRepository.updateEnableStatusByIds(ids, EnableStatusEnum.ENABLE);
    //启用职位事件通知
    if (!CollectionUtils.isEmpty(listeners)) {
      List<PositionEventVo> voList = Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(entities, PositionEntity.class
          , PositionEventVo.class, HashSet.class, ArrayList.class));
      listeners.forEach(positionEventListener -> {
        positionEventListener.onEnable(voList);
      });
    }
    //日志处理
    List<PositionEntity> byIdsOrCodes = this.positionRepository.findByIdsOrCodes(ids, null, TenantUtils.getTenantCode());
    for (PositionEntity byIdsOrCode : byIdsOrCodes) {
      PositionLogDto positionLogDto = new PositionLogDto();
      PositionDto oldDto = new PositionDto();
      PositionDto newDto = new PositionDto();
      oldDto.setId(byIdsOrCode.getId());
      oldDto.setEnableStatus(byIdsOrCode.getEnableStatus());
      newDto.setId(byIdsOrCode.getId());
      newDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
      positionLogDto.setOriginal(oldDto);
      positionLogDto.setNewest(newDto);
      SerializableBiConsumer<PositionLogEventListener, PositionLogDto> onUpdate =
          PositionLogEventListener::onUpdate;
      this.nebulaNetEventClient.publish(positionLogDto, PositionLogEventListener.class, onUpdate);
    }
  }

  /**
   * 批量禁用职位
   * 1.校验数据有效性
   * 2.根据职位的ruleCode,查询出所有的下级职位
   * 3.更新传入职位及其下级职位的启用状态为禁用
   * 4.发布职位禁用事件通知
   *
   * @param ids 职位id集合
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void disableBatch(List<String> ids) {
    Validate.isTrue(!CollectionUtils.isEmpty(ids), "缺失id");
    List<PositionEntity> entities = this.positionRepository.listByIds(ids);
    Validate.isTrue(!CollectionUtils.isEmpty(entities) && entities.size() == ids.size(), "数据禁用个数不匹配");
    List<String> ruleCodes = entities.stream().map(PositionEntity::getRuleCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
    //加载所有当前职位和其所有子级职位列表
    List<PositionEntity> disableEntities = this.positionRepository.findAllChildrenByRuleCodes(ruleCodes, EnableStatusEnum.ENABLE.getCode(), TenantUtils.getTenantCode());
    if (CollectionUtils.isEmpty(disableEntities)) {
      return;
    }
    disableEntities.forEach(positionEntity -> {
      positionEntity.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
    });
    //重构修改方法
    this.positionRepository.updateBatchByIdAndTenantCode(disableEntities,TenantUtils.getTenantCode());
    //禁用职位事件通知
    if (!CollectionUtils.isEmpty(listeners)) {
      List<PositionEventVo> voList = Lists.newArrayList(nebulaToolkitService.copyCollectionByWhiteList(disableEntities, PositionEntity.class
          , PositionEventVo.class, HashSet.class, ArrayList.class));
      listeners.forEach(positionEventListener -> {
        positionEventListener.onDisable(voList);
      });
    }
    //日志处理
    List<PositionEntity> byIdsOrCodes = this.positionRepository.findByIdsOrCodes(ids, null, TenantUtils.getTenantCode());
    for (PositionEntity byIdsOrCode : byIdsOrCodes) {
      PositionLogDto positionLogDto = new PositionLogDto();
      PositionDto oldDto = new PositionDto();
      PositionDto newDto = new PositionDto();
      oldDto.setId(byIdsOrCode.getId());
      oldDto.setEnableStatus(byIdsOrCode.getEnableStatus());
      newDto.setId(byIdsOrCode.getId());
      newDto.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
      positionLogDto.setOriginal(oldDto);
      positionLogDto.setNewest(newDto);
      SerializableBiConsumer<PositionLogEventListener, PositionLogDto> onUpdate =
          PositionLogEventListener::onUpdate;
      this.nebulaNetEventClient.publish(positionLogDto, PositionLogEventListener.class, onUpdate);
    }
  }

  @Override
  public List<PositionEntity> findByPositionLevelCodes(List<String> positionLevelCodes) {
    if (CollectionUtils.isEmpty(positionLevelCodes)) {
      return Lists.newArrayList();
    }
    return positionRepository.findByPositionLevelCodes(positionLevelCodes, TenantUtils.getTenantCode());
  }

  @Override
  public List<PositionEntity> findByIdsOrCodes(List<String> ids, List<String> positionCodes) {
    if (CollectionUtils.isEmpty(positionCodes) && CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    return this.positionRepository.findByIdsOrCodes(ids, positionCodes, TenantUtils.getTenantCode());
  }

  @Override
  public void updateRuleCode() {
    String tenantCode = TenantUtils.getTenantCode();
    //1.将parentCode不为空但找不到对应上级的数据，设置parentCode为null
    this.positionRepository.updateOrphanParentCodeNull(tenantCode);
    //2.查找所有parentCode为空的数据（相当于第一层数据）
    List<PositionEntity> list = this.positionRepository.findByParentCodeIsNull(tenantCode);
    if (CollectionUtils.isEmpty(list)) {
      return;
    }
    //3.递归设置其ruleCode和其所有子级的ruleCode
    for (int i = 0; i < list.size(); i++) {
      //递归调用
      Validate.notNull(this.treeRuleCodeStrategyHolder, "系统未配置降维码策略控制器");
      TreeRuleCodeStrategy treeRuleCodeStrategy = this.treeRuleCodeStrategyHolder.getStrategy(null);
      Validate.notNull(treeRuleCodeStrategy, "系统未配置降维码策略");
      updateRuleCodeAllChildren(list.get(i).getPositionCode(),
          treeRuleCodeStrategy.generateByNum(PositionConstant.POSITION_RULE_CODE_LENGTH, i + 1),
          1);
    }
  }

  @Override
  public List<PositionEntity> findAllChildrenByRuleCode(String ruleCode) {
    if (StringUtils.isBlank(ruleCode)) {
      return Lists.newArrayList();
    }
    return this.positionRepository.findAllChildrenByRuleCode(ruleCode, TenantUtils.getTenantCode());
  }

  @Override
  public List<PositionEntity> findByRoleCodes(List<String> roleCodes, String tenantCode) {
    if (CollectionUtils.isEmpty(roleCodes) || StringUtils.isBlank(tenantCode)) {
      return Lists.newArrayList();
    }
    return this.positionRepository.findByRoleCodes(roleCodes, tenantCode);
  }

  @Override
  public PositionEntity findByPositionCode(String positionCode) {
    return this.positionRepository.findByPositionCode(positionCode, TenantUtils.getTenantCode());
  }

  /**
   * 重新绑定父级职位
   * 1.校验职位列表和父级职位数据有效性
   * 2.根据上级职位ruleCode,解析出上级职位的所有上级职位的ruleCode,然后根据所有上级职位ruleCode,查询出所有上级职位
   * 3.校验所有上级职位是否存在传入下级职位的下级职位
   * 4.持久化传入下级职位
   * 5.重置所有传入下级职位的ruleCode(包括他们的下级)
   *
   * @param dto 请求参数dto
   */
  @Override
  @Transactional
  public void rebindParentPosition(RebindParentPositionDto dto) {
    Validate.isTrue(!CollectionUtils.isEmpty(dto.getUnderlingPositionCodeList()), "缺失下级职位");
    Validate.notBlank(dto.getPositionCode(), "缺失上级职位");
    String tenantCode = TenantUtils.getTenantCode();
    List<PositionEntity> positionEntities = this.positionRepository.findByIdsOrCodes(null, dto.getUnderlingPositionCodeList(), tenantCode);
    Validate.isTrue(!CollectionUtils.isEmpty(positionEntities)
        && positionEntities.size() == dto.getUnderlingPositionCodeList().size(), "数据重绑个数不匹配");
    PositionEntity parentPosition = this.positionRepository.findByPositionCode(dto.getPositionCode(), tenantCode);
    Validate.notNull(parentPosition, "上级职位不存在");
    Validate.isTrue(!dto.getUnderlingPositionCodeList().contains(parentPosition.getPositionCode())
        , String.format("所选职位[%s]不能是自身上级职位", parentPosition.getPositionName()));
    //查询所选上级职位的全部上级
    TreeRuleCodeStrategy strategy = this.treeRuleCodeStrategyHolder.getStrategy(null);
    Set<String> ruleCodes = strategy.findParentRuleCodeByRuleCode(PositionConstant.POSITION_RULE_CODE_LENGTH, parentPosition.getRuleCode());
    List<PositionEntity> allParentPositions = this.positionRepository.findByRuleCodesAndEnableStatus(Lists.newArrayList(ruleCodes), null, tenantCode);
    allParentPositions.forEach(positionEntity -> {
      Validate.isTrue(!dto.getUnderlingPositionCodeList().contains(positionEntity.getPositionCode())
          , String.format("所选职位[%s]是[%s]的下级职位，不能选择作为上级", parentPosition.getPositionName(), positionEntity.getPositionName()));
    });
    //更新职位规则编码和父级职位信息
    Integer levelNum = parentPosition.getLevelNum() + 1;
    positionEntities.forEach(positionEntity -> {
      String ruleCode = this.createRuleCodeByParentPosition(parentPosition);
      positionEntity.setRuleCode(ruleCode);
      positionEntity.setLevelNum(levelNum);
      positionEntity.setParentCode(parentPosition.getPositionCode());
      //重构修改方法
      this.positionRepository.updateById(positionEntity);
      updateRuleCodeAllChildren(positionEntity.getPositionCode(), ruleCode, levelNum);
    });
  }

  /**
   * 更新职位及职位下级降维编码和层级
   *
   * @param positionCode 当前职位编码
   * @param ruleCode     规则code
   * @param levelNum     层级等级
   */
  private void updateRuleCodeAllChildren(String positionCode, String ruleCode, Integer levelNum) {
    //更新当前职位规则code和职位等级
    this.positionRepository.updateRuleCodeAndLevelNumByPositionCode(positionCode, ruleCode, levelNum, TenantUtils.getTenantCode());
    //查询下一层
    List<PositionEntity> list = positionRepository.findByParentCode(positionCode, TenantUtils.getTenantCode());
    //无下级职位,结束递归
    if (CollectionUtils.isEmpty(list)) {
      return;
    }
    //遍历下级
    for (int i = 0; i < list.size(); i++) {
      //递归调用
      PositionEntity childrenPosition = list.get(i);
      Validate.notNull(this.treeRuleCodeStrategyHolder, "系统未配置降维码策略控制器");
      TreeRuleCodeStrategy treeRuleCodeStrategy = this.treeRuleCodeStrategyHolder.getStrategy(null);
      Validate.notNull(treeRuleCodeStrategy, "系统未配置降维码策略");
      updateRuleCodeAllChildren(childrenPosition.getPositionCode(),
          ruleCode + treeRuleCodeStrategy.generateByNum(PositionConstant.POSITION_RULE_CODE_LENGTH, i + 1)
          , (levelNum + 1));
    }
  }

  /**
   * 根据父职位生成一个降维编码
   *
   * @param parentPosition 父职位
   * @return 降维编码
   */
  private String createRuleCodeByParentPosition(PositionEntity parentPosition) {
    String tenantCode = TenantUtils.getTenantCode();
    List<PositionEntity> children;
    String parentRuleCode = null;
    List<TreeDto> childrenDto = Lists.newArrayList();
    if (parentPosition != null) {
      children = this.positionRepository.findByParentCode(parentPosition.getPositionCode(), tenantCode);
      parentRuleCode = parentPosition.getRuleCode();
    } else {
      children = this.positionRepository.findByParentCodeIsNull(tenantCode);
    }
    if (!CollectionUtils.isEmpty(children)) {
      childrenDto = Lists.newArrayList(this.nebulaToolkitService.copyCollectionByWhiteList(children, PositionEntity.class
          , TreeDto.class, HashSet.class, ArrayList.class));
    }
    Validate.notNull(this.treeRuleCodeStrategyHolder, "系统未配置降维码策略控制器");
    TreeRuleCodeStrategy treeRuleCodeStrategy = this.treeRuleCodeStrategyHolder.getStrategy(null);
    Validate.notNull(treeRuleCodeStrategy, "系统未配置降维码策略");
    return treeRuleCodeStrategy.generate(PositionConstant.POSITION_RULE_CODE_LENGTH, parentRuleCode, childrenDto);
  }

  /**
   * 在创建position模型对象之前，检查对象各属性的正确性，其主键属性必须没有值
   *
   * @param dto 检查对象
   */
  private void createValidation(PositionDto dto) {
    Validate.notNull(dto, "进行当前操作时，信息对象必须传入!");
    dto.setTenantCode(TenantUtils.getTenantCode());
    dto.setId(null);
    Validate.notBlank(dto.getPositionLevelCode(), "缺失职位级别编码");
    Validate.isTrue(StringUtils.isBlank(dto.getPositionName()) || dto.getPositionName().length() < 128
        , "职位名称，在进行添加时填入值超过了限定长度(128)，请检查!");
    Validate.isTrue(StringUtils.isBlank(dto.getPositionCode()) || dto.getPositionCode().length() < 64
        , "职位编码，在进行添加时填入值超过了限定长度(64)，请检查!");
  }

  /**
   * 在修改positionLevel模型对象之前，检查对象各属性的正确性，其主键属性必须没有值
   *
   * @param dto 检查对象
   */
  private void updateValidation(PositionDto dto) {
    Validate.notNull(dto, "进行当前操作时，信息对象必须传入!");
    dto.setTenantCode(TenantUtils.getTenantCode());
    Validate.notBlank(dto.getId(), "修改信息时，id不能为空！");
    Validate.notBlank(dto.getPositionCode(), "缺失职位编码");
    Validate.notBlank(dto.getPositionLevelCode(), "缺失职位级别编码");
    Validate.isTrue(StringUtils.isBlank(dto.getPositionName()) || dto.getPositionName().length() < 128
        , "职位名称，在进行编辑时填入值超过了限定长度(128)，请检查!");
  }

  @Override
  public Map<String, Integer> getCountByPositionLevelCodes(List<String> positionLevelCodeList) {
      if (CollectionUtil.isEmpty(positionLevelCodeList)) {
          return Maps.newHashMap();
      }
      List<PositionLevelCountVo> countVoList = positionRepository.getCountByPositionLevelCodes(positionLevelCodeList);
      Map<String, Integer> map = Maps.newHashMap();
      if (CollectionUtil.isNotEmpty(countVoList)) {
          map.putAll(countVoList.stream().collect(Collectors.toMap(PositionLevelCountVo::getPositionLevelCode,
                  PositionLevelCountVo::getMaxNum, (n, o) -> n)));
      }
      positionLevelCodeList.forEach(positionLevelCode -> {
          map.putIfAbsent(positionLevelCode, 0);
      });
      return map;
  }
}
