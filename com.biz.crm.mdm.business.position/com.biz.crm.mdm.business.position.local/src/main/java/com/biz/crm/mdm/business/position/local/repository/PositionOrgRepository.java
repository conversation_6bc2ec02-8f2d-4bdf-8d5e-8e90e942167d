package com.biz.crm.mdm.business.position.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.position.local.entity.PositionOrgEntity;
import com.biz.crm.mdm.business.position.local.mapper.PositionOrgMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 职位-角色关联表的数据库访问类 {@link PositionOrgEntity}
 *
 * <AUTHOR>
 * @date 2021/9/28
 */
@Component
public class PositionOrgRepository extends ServiceImpl<PositionOrgMapper, PositionOrgEntity> {

  /**
   * 通过职位编码获取职位角色列表
   *
   * @param positionCode 职位编码
   * @param tenantCode   租户编码
   * @return 职位角色列表
   */
  public List<PositionOrgEntity> findByPositionCode(String positionCode, String tenantCode) {
    return this.lambdaQuery()
            .eq(PositionOrgEntity::getTenantCode, tenantCode)
            .eq(PositionOrgEntity::getPositionCode, positionCode)
            .list();
  }

  /**
   * 通过职位编码集合获取职位角色列表
   *
   * @param positionCodes 职位编码集合
   * @param tenantCode    租户编码
   * @return 职位角色列表
   */
  public List<PositionOrgEntity> findByPositionCodes(List<String> positionCodes, String tenantCode) {
    return this.lambdaQuery()
            .eq(PositionOrgEntity::getTenantCode, tenantCode)
            .in(PositionOrgEntity::getPositionCode, positionCodes)
            .list();
  }

  /**
   * 通过职位编码集合删除角色关联关系
   *
   * @param positionCodes 职位编码集合
   * @param tenantCode    租户编码
   */
  public void deleteByPositionCodes(List<String> positionCodes, String tenantCode) {
    this.lambdaUpdate()
            .eq(PositionOrgEntity::getTenantCode, tenantCode)
            .in(PositionOrgEntity::getPositionCode, positionCodes)
            .remove();
  }

  /**
   * 通过职位编码和角色编码集合获取职位角色关联实体列表
   *
   * @param positionCode 职位编码
   * @param roleCodes    角色编码集合
   * @param tenantCode   租户编码
   * @return 职位角色关联列表
   */
  public List<PositionOrgEntity> findByPositionCodeAndOrgCodes(String positionCode, List<String> roleCodes
          , String tenantCode) {
    return super.lambdaQuery()
            .eq(PositionOrgEntity::getTenantCode, tenantCode)
            .eq(PositionOrgEntity::getPositionCode, positionCode)
            .in(PositionOrgEntity::getOrgCode, roleCodes)
            .list();
  }

  /**
   * 通过角色编码集合获取职位角色关联实体列表
   *
   * @param roleCodes  角色编码集合
   * @param tenantCode 租户编码
   * @return 职位角色关联列表
   */
  public List<PositionOrgEntity> findByOrgCodes(List<String> roleCodes, String tenantCode) {
    return super.lambdaQuery()
            .eq(PositionOrgEntity::getTenantCode, tenantCode)
            .in(PositionOrgEntity::getOrgCode, roleCodes)
            .list();
  }

  /**
   * 通过职位编码集合和角色编码获取职位角色关联列表
   *
   * @param positionCodes 职位编码集合
   * @param roleCode      角色编码
   * @param tenantCode    租户编码
   * @return 职位角色关联列表
   */
  public List<PositionOrgEntity> findByPositionCodesAndOrgCode(List<String> positionCodes, String roleCode, String tenantCode) {
    return this.lambdaQuery()
            .eq(PositionOrgEntity::getTenantCode, tenantCode)
            .in(PositionOrgEntity::getPositionCode, positionCodes)
            .eq(PositionOrgEntity::getOrgCode, roleCode)
            .list();
  }

  /**
   * 通过id和租户编号删除
   * @param positionOrgIds
   * @param tenantCode
   */
  public void removeByIdsAndTenantCode(List<String> positionOrgIds, String tenantCode) {
    this.lambdaUpdate()
        .eq(PositionOrgEntity::getTenantCode,tenantCode)
        .in(PositionOrgEntity::getId,positionOrgIds)
        .remove();
  }
}
