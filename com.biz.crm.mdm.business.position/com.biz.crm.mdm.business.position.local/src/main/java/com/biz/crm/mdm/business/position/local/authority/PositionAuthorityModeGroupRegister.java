package com.biz.crm.mdm.business.position.local.authority;

import com.bizunited.nebula.mars.sdk.register.SelectAuthorityModeGroupRegister;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * @Author: heguanyun
 * @Date: 2022/4/7 17:27
 * description:
 */
@Component
public class PositionAuthorityModeGroupRegister implements SelectAuthorityModeGroupRegister {
    @Override
    public String groupCode() {
        return "position_scope";
    }

    @Override
    public String groupName() {
        return "按照职位维度授权";
    }

    @Override
    public Set<String> viewFieldNames() {
        return Sets.newHashSet("positionCode", "positionCodes", "createPostCode", "createPosCode",
                "position_code", "position_codes", "create_post_code", "create_pos_code");
    }

    @Override
    public Set<String> repositoryFieldNames() {
        return Sets.newHashSet("position_code", "position_codes", "create_post_code", "create_pos_code");
    }
}
