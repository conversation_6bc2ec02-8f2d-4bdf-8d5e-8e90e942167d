package com.biz.crm.mdm.business.position.local.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantOpEntity;
import com.biz.crm.business.common.local.entity.UuidOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * 职位组织实体类
 *
 * <AUTHOR>
 * @version v1.0.0
 * @date 2024/10/22 9:20
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PositionOrgEntity", description = "职位组织实体")
@Entity
@TableName("mdm_position_org")
@Table(name = "mdm_position_org", indexes = {
        @Index(name = "mdm_position_org_uq1", columnList = "position_code,org_code", unique = true),
        @Index(name = "mdm_position_org_idx1", columnList = "position_code"),
        @Index(name = "mdm_position_org_idx2", columnList = "org_code"),
})
@org.hibernate.annotations.Table(appliesTo = "mdm_position_org", comment = "职位组织关系表")
public class PositionOrgEntity extends TenantOpEntity {

    private static final long serialVersionUID = 1069865750124446073L;

    @ApiModelProperty("职位编码")
    @Column(name = "position_code", length = 32, columnDefinition = "VARCHAR(32) NOT NULL COMMENT '职位编码'")
    private String positionCode;

    @ApiModelProperty("组织编码")
    @Column(name = "org_code", length = 32, columnDefinition = "VARCHAR(32) NOT NULL COMMENT '组织编码'")
    private String orgCode;

}
