package com.biz.crm.mdm.business.position.feign.service.internal;

import com.biz.crm.mdm.business.position.feign.feign.PositionVoFeign;
import com.biz.crm.mdm.business.position.sdk.dto.PositionDto;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * 职位基本信息sdk实现
 *
 * <AUTHOR>
 */
@Service
public class PositionVoServiceImpl implements PositionVoService {

  @Autowired(required = false)
  private PositionVoFeign positionVoFeign;

  @Override
  public List<PositionVo> findByIdsOrCodes(List<String> ids, List<String> positionCodes) {
    return this.positionVoFeign.findByIdsOrCodes(ids, positionCodes).checkFeignResult();
  }

  @Override
  public List<PositionVo> findByLotPositionCodes(List<String> positionCodes) {
    return this.positionVoFeign.findByLotPositionCodes(positionCodes).checkFeignResult();
  }

  @Override
  public List<PositionVo> findDetailsByIdsOrCodes(List<String> ids, List<String> positionCodes) {
    throw new UnsupportedOperationException();
  }

  @Override
  public List<PositionVo> findAllChildrenByCode(String positionCode) {
    return this.positionVoFeign.findAllChildrenByCode(positionCode).checkFeignResult();
  }

  @Override
  public List<PositionVo> findByParentCode(String positionCode) {
    return this.positionVoFeign.findByParentCode(positionCode).checkFeignResult();
  }

  @Override
  public PositionVo create(PositionDto dto) {
    throw new UnsupportedOperationException();
  }

  @Override
  public PositionVo update(PositionDto dto) {
    throw new UnsupportedOperationException();
  }

  @Override
  public PositionVo findByPositionCode(String positionCode) {
    if (StringUtil.isEmpty(positionCode)){
      return null;
    }
    return this.positionVoFeign.findByPositionCode(positionCode).checkFeignResult();
  }

  @Override
  public List<PositionVo> findAllParentByRoleCodes(List<String> roleCodes) {
    throw new UnsupportedOperationException();
  }

  @Override
  public List<PositionVo> findPositionsByRoleCodes(List<String> roleCodes) {
    return this.positionVoFeign.findPositionsByRoleCodes(roleCodes).checkFeignResult();
  }

  @Override
  public Set<String> findPositionCodesByPositionLevelCodes(List<String> positionLevelCodes) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Set<String> findRolesByPositionCodes(List<String> positionCodes) {
    return this.positionVoFeign.findRolesByPositionCodes(positionCodes).checkFeignResult();
  }

  @Override
  public List<PositionVo> findAllParentByPositionCode(String positionCode) {
    throw new UnsupportedOperationException();
  }

  @Override
  public List<PositionVo> findParentByPositionCode(String positionCode) {
    throw new UnsupportedOperationException();
  }
}
