package com.biz.crm.mdm.business.position.feign.service.internal;

import com.biz.crm.mdm.business.position.feign.feign.PositionOrgVoFeign;
import com.biz.crm.mdm.business.position.sdk.service.PositionOrgVoService;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 职位组织关联表接口实现
 *
 * <AUTHOR>
 * @date 2021/10/13
 */
@Service
public class PositionOrgVoServiceImpl implements PositionOrgVoService {

    @Autowired(required = false)
    private PositionOrgVoFeign positionOrgVoFeign;

    @Override
    public Map<String, List<String>> findOrgsByPositionCodes(List<String> positionCodes) {
        if (CollectionUtils.isEmpty(positionCodes)) {
            return Collections.emptyMap();
        }
        return positionOrgVoFeign.findOrgsByPositionCodes(positionCodes).checkFeignResult();
    }

    @Override
    public Set<String> findOrgsByPositionCode(String positionCode) {
        if (StringUtil.isEmpty(positionCode)) {
            return Collections.emptySet();
        }
        return positionOrgVoFeign.findOrgsByPositionCode(positionCode).checkFeignResult();
    }
}
