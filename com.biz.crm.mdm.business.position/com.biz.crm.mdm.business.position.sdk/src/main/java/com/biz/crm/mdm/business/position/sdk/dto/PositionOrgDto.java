package com.biz.crm.mdm.business.position.sdk.dto;


import com.biz.crm.business.common.local.entity.TenantOpEntity;
import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.biz.crm.business.common.sdk.dto.UuidOpDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 职位组织实体类
 *
 * <AUTHOR>
 * @version v1.0.0
 * @date 2024/10/22 9:20
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
public class PositionOrgDto extends TenantFlagOpDto {


    private static final long serialVersionUID = 7395116298075258496L;

    @ApiModelProperty("职位编码")
    private String positionCode;

    @ApiModelProperty("组织编码")
    private String orgCode;

}
