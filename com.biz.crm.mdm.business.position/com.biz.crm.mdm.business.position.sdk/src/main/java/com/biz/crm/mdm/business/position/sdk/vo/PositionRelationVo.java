package com.biz.crm.mdm.business.position.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 职位关联数据VO
 *
 * <AUTHOR>
 * @date 2021/10/14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "职位关联数据VO")
public class PositionRelationVo {

  /**
   * 职位编码
   */
  @ApiModelProperty("职位编码")
  private String positionCode;
  /**
   * 关联数据key
   */
  @ApiModelProperty("关联数据key")
  private String relationKey;
  /**
   * 关联数据Name
   */
  @ApiModelProperty("关联数据Name")
  private String relationName;
  /**
   * 关联数据
   */
  @ApiModelProperty("关联数据")
  private List<AbstractRelationView> relationData;
}
