package com.biz.crm.mdm.business.position.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 解绑职位角色Dto
 *
 * <AUTHOR>
 * @date 2021/11/11
 */
@Data
@ApiModel(value = "UnbindPositionRoleDto", description = "解绑职位角色Dto")
public class UnbindPositionRoleDto {

  /**
   * 角色编码
   */
  @ApiModelProperty("角色编码")
  private String roleCode;

  /**
   * 职位编码集合
   */
  @ApiModelProperty("职位编码集合")
  private List<String> positionCodeList;

}