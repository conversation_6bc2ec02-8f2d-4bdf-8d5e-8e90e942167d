package com.biz.crm.mdm.business.position.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 职位关联数据dto(职位模块关联的上级数据)
 *
 * <AUTHOR>
 * @date 2021/10/14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "职位关联数据dto(职位模块关联的上级数据)")
public class RelationDataDto {

  /**
   * 关联上级模块数据唯一的业务code编码
   */
  @ApiModelProperty("关联上级模块数据唯一的业务code编码")
  private String code;

}
