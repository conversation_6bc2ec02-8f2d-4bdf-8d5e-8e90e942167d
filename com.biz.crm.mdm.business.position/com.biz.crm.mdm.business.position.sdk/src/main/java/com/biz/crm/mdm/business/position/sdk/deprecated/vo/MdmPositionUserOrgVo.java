package com.biz.crm.mdm.business.position.sdk.deprecated.vo;

import com.biz.crm.business.common.sdk.deprecated.vo.CrmTreeTenVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 职位返回vo
 *
 * <AUTHOR>
 * @date 2020-11-17 20:23:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "以职位维度的职位及关联用户、组织、上级信息")
@Deprecated
public class MdmPositionUserOrgVo extends CrmTreeTenVo {

    @ApiModelProperty("职位编码")
    private String positionCode;

    @ApiModelProperty("职位名称")
    private String positionName;

    @ApiModelProperty("关联组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("职位级别编码")
    private String positionLevelCode;

    @ApiModelProperty("职位级别名称")
    private String positionLevelName;

    @ApiModelProperty("是否主职位1是0否")
    private String primaryFlag;

    @ApiModelProperty("是否当前职位1是0否")
    private String currentFlag;

    @ApiModelProperty("用户登录名")
    private String userName;

    @ApiModelProperty("用户姓名")
    private String fullName;

    @ApiModelProperty("用户电话")
    private String userPhone;

    @ApiModelProperty("用户工号")
    private String jobCode;

    @Deprecated
    @ApiModelProperty("用户编码")
    private String userCode;

    @ApiModelProperty("用户的启用状态")
    private String userEnableStatus;

    @ApiModelProperty("上级职位编码")
    private String parentCode;

    @ApiModelProperty("上级职位名称")
    private String parentName;

    @ApiModelProperty("上级用户编码")
    private String parentUserName;

    @ApiModelProperty("上级用户名称")
    private String parentFullName;

    @ApiModelProperty("上级用户编码")
    private String parentUserCode;

    @ApiModelProperty("上级组织编码")
    private String parentOrgCode;

    @ApiModelProperty("上级组织名称")
    private String parentOrgName;

    @ApiModelProperty("用户头像url")
    private String userHeadUrl;

}
