package com.biz.crm.mdm.business.position.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 职位dto
 *
 * <AUTHOR>
 * @date 2021/10/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "职位dto", description = "职位")
public class PositionDto extends TenantDto {

    /**
     * 职位编码
     */
    @ApiModelProperty("职位编码")
    private String positionCode;
    /**
     * 职位名称
     */
    @ApiModelProperty("职位名称")
    private String positionName;
    /**
     * 上级职位编码
     */
    @ApiModelProperty("上级职位编码")
    private String parentCode;
    /**
     * 职位级别编码
     */
    @ApiModelProperty("职位级别编码")
    private String positionLevelCode;

    /**
     * 职位级别名称
     */
    @ApiModelProperty("职位级别名称")
    private String positionLevelName;

    /**
     * 启禁用
     */
    @ApiModelProperty("启禁用")
    private String enableStatus;

    @ApiModelProperty("删除")
    private String delFlag;

    @ApiModelProperty("规则code")
    private String ruleCode;

    @ApiModelProperty("层级等级查询用")
    private Integer levelNum;

    /**
     * 角色编码集合，新增职位需要关联角色的时候传这个
     */
    @ApiModelProperty("角色编码集合，新增职位需要关联角色的时候传这个")
    private List<String> roleCodeList;

    /**
     * 关联上级模块数据信息(如关联的组织)
     */
    @ApiModelProperty("关联上级模块数据信息(如关联的组织)")
    private List<PositionRelationDto> relationData;

    @ApiModelProperty("关联的组织")
    private List<PositionOrgDto> positionOrgList;
}