package com.biz.crm.mdm.business.position.sdk.service;

import com.biz.crm.mdm.business.position.sdk.dto.PositionDto;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;

import java.util.List;
import java.util.Set;

/**
 * 职位VO接口
 *
 * <AUTHOR>
 * @date 2021/10/13
 */
public interface PositionVoService {

  /**
   * 根据职位id或者code查询职位列表
   *
   * @param ids           职位ID集合
   * @param positionCodes 职位编码集合
   * @return 职位列表
   */
  List<PositionVo> findByIdsOrCodes(List<String> ids, List<String> positionCodes);

  /**
   * 根据大量职位code查询职位列表
   *
   * @param positionCodes 职位编码集合
   * @return 职位列表
   */
  List<PositionVo> findByLotPositionCodes(List<String> positionCodes);

  /**
   * 根据职位id或者code查询职位详情(包含上级模块关联数据)
   *
   * @param ids           职位ID集合
   * @param positionCodes 职位编码集合
   * @return 职位详情
   */
  List<PositionVo> findDetailsByIdsOrCodes(List<String> ids, List<String> positionCodes);

  /**
   * 根据职位编码查询该职位的所有下级职位(包含自身)
   *
   * @param positionCode 职位编码
   * @return 所有下级职位
   */
  List<PositionVo> findAllChildrenByCode(String positionCode);

  /**
   * 根据职位编码查询该职位的直属下级
   *
   * @param positionCode 职位编码
   * @return 直属下级职位
   */
  List<PositionVo> findByParentCode(String positionCode);


  /**
   * 创建职位信息
   *
   * @param dto 职位信息
   */
  PositionVo create(PositionDto dto);

  /**
   * 创建职位信息
   *
   * @param dto 职位信息
   */
  PositionVo update(PositionDto dto);

  /**
   * 根据职位编码查询职位信息
   *
   * @param positionCode 职位编码
   */
  PositionVo findByPositionCode(String positionCode);

  /**
   * 根据角色编码集合查询角色编码关联的职位及其所有上级职位(包含角色关联职位自身)
   *
   * @param roleCodes 角色编码集合
   * @return 职位及其所有上级职位
   */
  List<PositionVo> findAllParentByRoleCodes(List<String> roleCodes);

  /**
   * 根据角色编码集合查询角色编码关联的职位
   *
   * @param roleCodes 角色编码集合
   * @return 职位
   */
  List<PositionVo> findPositionsByRoleCodes(List<String> roleCodes);

  /**
   * 根据职位级别编码集合查询职位编码集合
   *
   * @param positionLevelCodes 职位级别编码
   * @return 职位编码集合
   */
  Set<String> findPositionCodesByPositionLevelCodes(List<String> positionLevelCodes);

  /**
   * 根据职位编码查询角色编码集合
   *
   * @param positionCodes 职位编码集合
   * @return 角色编码集合
   */
  Set<String> findRolesByPositionCodes(List<String> positionCodes);

  /**
   * 查询当前职位编码的全部上级
   * @param positionCode
   * @return
   */
  List<PositionVo> findAllParentByPositionCode(String positionCode);

  /**
   * 查询当前职位编码的直属上级
   * @param positionCode
   * @return
   */
  List<PositionVo> findParentByPositionCode(String positionCode);
}
