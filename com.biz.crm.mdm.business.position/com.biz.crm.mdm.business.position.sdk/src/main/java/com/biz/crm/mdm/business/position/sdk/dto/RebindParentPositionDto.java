package com.biz.crm.mdm.business.position.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 重新绑定上级职位Dto
 *
 * <AUTHOR>
 * @date 2021/11/30
 */
@Data
@ApiModel(value = "RebindParentPositionDto", description = "重新绑定上级职位Dto")
public class RebindParentPositionDto {

  /**
   * 下级职位编码集合
   */
  @ApiModelProperty("下级职位编码集合")
  private List<String> underlingPositionCodeList;

  /**
   * 上级职位编码
   */
  @ApiModelProperty("上级职位编码")
  private String positionCode;
}
