package com.biz.crm.mdm.business.position.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantTreeFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 职位VO
 *
 * <AUTHOR>
 * @date 2021/10/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "职位表")
public class PositionVo extends TenantTreeFlagOpVo {

    private static final long serialVersionUID = 7281844844093831468L;

    @ApiModelProperty("职位编码")
    private String positionCode;

    @ApiModelProperty("职位名称")
    private String positionName;

    @ApiModelProperty("上级职位编码")
    private String parentCode;

    @ApiModelProperty("职位级别编码")
    private String positionLevelCode;

    @ApiModelProperty("职位级别名称")
    private String positionLevelName;

    @ApiModelProperty("用户编码")
    private String userCode;

    @ApiModelProperty("用户名")
    private String fullName;

    @ApiModelProperty("登录账号")
    private String userName;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("分部id公司")
    private String subCompanyId;

    @ApiModelProperty("部门id")
    private String departmentId;

    @ApiModelProperty("用户电话")
    private String userPhone;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("关联角色编码")
    private List<String> roleList;

    @ApiModelProperty("关联数据")
    private List<PositionRelationVo> relationData;

    @ApiModelProperty("关联的组织")
    private List<PositionOrgVo> positionOrgList;
}