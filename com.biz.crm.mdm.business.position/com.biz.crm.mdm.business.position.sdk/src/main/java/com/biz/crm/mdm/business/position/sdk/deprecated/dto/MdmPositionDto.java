package com.biz.crm.mdm.business.position.sdk.deprecated.dto;

import com.biz.crm.business.common.sdk.deprecated.vo.CrmTreeTenVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 职位表请求dto
 *
 * <AUTHOR>
 * @date 2021/10/23
 */
@Data
@Deprecated
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MdmPositionDto", description = "职位表请求dto")
public class MdmPositionDto extends CrmTreeTenVo {

  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty("职位名称")
  private String positionName;

  @ApiModelProperty("关联组织编码")
  private String orgCode;

  @ApiModelProperty("上级职位编码")
  private String parentCode;

  @ApiModelProperty("职位级别编码")
  private String positionLevelCode;

  @ApiModelProperty("是否主职位 0否1是")
  private String primaryFlag;

  @ApiModelProperty("是否当前职位 0否1是")
  private String currentFlag;

  @ApiModelProperty("角色编码集合，新增职位需要关联角色的时候传这个")
  private List<String> roleCodeList;

  @ApiModelProperty("流程角色编码集合，新增职位需要关联流程角色的时候传这个")
  private List<String> bpmRoleCodeList;

}