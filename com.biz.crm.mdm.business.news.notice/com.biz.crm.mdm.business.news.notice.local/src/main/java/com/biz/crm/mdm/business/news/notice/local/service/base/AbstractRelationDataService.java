package com.biz.crm.mdm.business.news.notice.local.service.base;

import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.news.notice.sdk.dto.base.AbstractCustomerPageDto;
import com.biz.crm.mdm.business.news.notice.sdk.enums.ScopeType;
import com.biz.crm.mdm.business.news.notice.sdk.vo.base.ScopeVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 抽象关联数据服务类
 *
 * <AUTHOR>
 * @date 2022/5/13
 */
public abstract class AbstractRelationDataService {

  @Autowired(required = false)
  private OrgVoService orgVoService;
  @Autowired(required = false)
  private CustomerVoService customerVoService;
  @Autowired(required = false)
  private LoginUserService loginUserService;

  /**
   * 封装客户-分页查询dto相关参数信息
   * @param dto 客户-分页查询dto
   */
  protected void buildCustomerPageDto(AbstractCustomerPageDto dto) {
    AbstractCrmUserIdentity loginDetails = loginUserService.getAbstractLoginUser();
    if (StringUtils.isBlank(loginDetails.getAccount())) {
      return;
    }
    dto.setUserName(loginDetails.getAccount());
    // 获取组织code
    Object orgCodeObject = loginDetails.invokeFieldValue("orgCode");
    if(orgCodeObject == null) {
      return;
    }
    
    //获取用户相关组织信息
    List<OrgVo> orgList = this.orgVoService.findAllParentByOrgCodes(Lists.newArrayList(orgCodeObject.toString()));
    if (!CollectionUtils.isEmpty(orgList)) {
      List<String> collect = orgList.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
      collect.add(orgCodeObject.toString());
      dto.setOrgCodeList(collect);
    }
  }

  /**
   * 完善范围信息数据
   * @param scopeList 范围信息集合
   */
  protected void perfectScopeInfo(List<ScopeVo> scopeList) {
    if (CollectionUtils.isEmpty(scopeList)) {
      return;
    }
    List<String> orgCodes = Lists.newArrayList();
    List<String> customerCodes = Lists.newArrayList();
    scopeList.forEach(scopeVo -> {
      if (ScopeType.ORG.name().equals(scopeVo.getScopeType())) {
        orgCodes.add(scopeVo.getScopeCode());
      }
      if (ScopeType.CUSTOMER.name().equals(scopeVo.getScopeType())) {
        customerCodes.add(scopeVo.getScopeCode());
      }
    });
    Map<String, OrgVo> orgMap = this.buildOrgMap(orgCodes);
    Map<String, CustomerVo> customerMap = this.buildCustomerMap(customerCodes);
    scopeList.forEach(scopeVo -> {
      if (ScopeType.ORG.name().equals(scopeVo.getScopeType())) {
        OrgVo orgVo = orgMap.get(scopeVo.getScopeCode());
        scopeVo.setScopeName(orgVo != null ? orgVo.getOrgName() : null);
      }
      if (ScopeType.CUSTOMER.name().equals(scopeVo.getScopeType())) {
        CustomerVo customerVo = customerMap.get(scopeVo.getScopeCode());
        scopeVo.setScopeName(customerVo != null ? customerVo.getCustomerName() : null);
      }
    });
  }

  /**
   * 封装企业组织数据映射(key:企业组织编码,value:企业组织信息)
   * @param orgCodes 企业组织编码集合
   * @return 企业组织数据映射
   */
  private Map<String,OrgVo> buildOrgMap(List<String> orgCodes) {
    Map<String,OrgVo> resultMap = Maps.newHashMap();
    if (CollectionUtils.isEmpty(orgCodes)) {
      return resultMap;
    }
    List<OrgVo> orgVoList = orgVoService.findByOrgCodes(orgCodes);
    if (CollectionUtils.isEmpty(orgVoList)) {
      return resultMap;
    }
    resultMap = orgVoList.stream().collect(Collectors.toMap(OrgVo::getOrgCode, t -> t,(key1, key2) -> key2));
    return resultMap;
  }

  /**
   * 封装客户信息数据映射(key:客户编码,value:客户信息)
   * @param customerCodes 客户编码集合
   * @return 客户数据映射
   */
  private Map<String,CustomerVo> buildCustomerMap(List<String> customerCodes) {
    Map<String,CustomerVo> resultMap = Maps.newHashMap();
    if (CollectionUtils.isEmpty(customerCodes)) {
      return resultMap;
    }
    List<CustomerVo> orgVoList = this.customerVoService.findByCustomerCodes(customerCodes);
    if (CollectionUtils.isEmpty(orgVoList)) {
      return resultMap;
    }
    resultMap = orgVoList.stream().collect(Collectors.toMap(CustomerVo::getCustomerCode,t -> t,(key1,key2) -> key2));
    return resultMap;
  }
}
