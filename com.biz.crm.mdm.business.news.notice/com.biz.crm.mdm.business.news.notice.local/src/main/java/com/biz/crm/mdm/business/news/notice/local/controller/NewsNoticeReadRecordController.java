package com.biz.crm.mdm.business.news.notice.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.news.notice.local.entity.NewsNoticeReadRecordEntity;
import com.biz.crm.mdm.business.news.notice.local.service.NewsNoticeReadRecordService;
import com.biz.crm.mdm.business.news.notice.sdk.dto.NewsNoticeReadRecordDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 消息公告: NewsNoticeReadRecord: 公告阅读记录
 *
 * <AUTHOR>
 * @date 2022/5/13
 */
@Slf4j
@RestController
@RequestMapping(value = {"/v1/newsNoticeReadRecord/newsNoticeReadRecord"})
@Api(tags = "消息公告: NewsNoticeReadRecord: 公告阅读记录")
public class NewsNoticeReadRecordController {

  @Autowired(required = false)
  private NewsNoticeReadRecordService newsNoticeReadRecordService;

  @ApiOperation(value = "创建")
  @PostMapping(value = "")
  public Result<List<NewsNoticeReadRecordEntity>> create(@RequestBody NewsNoticeReadRecordDto dto) {
    try {
      return Result.ok(this.newsNoticeReadRecordService.create(dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  /**
   * 全部已读接口
   * @return
   */
  @ApiOperation(value = "全部已读接口")
  @GetMapping(value = "all")
  public Result<?> all() {
    try {
      this.newsNoticeReadRecordService.all();
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
