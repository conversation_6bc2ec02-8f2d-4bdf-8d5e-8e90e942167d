package com.biz.crm.mdm.business.news.notice.local.repository;

import java.util.List;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.news.notice.local.entity.NewsNoticeReadRecordEntity;
import com.biz.crm.mdm.business.news.notice.local.mapper.NewsNoticeReadRecordMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

/**
 * 公告阅读记录表的数据库访问类 {@link NewsNoticeReadRecordEntity}
 *
 * <AUTHOR>
 * @date 2022/5/12
 */
@Component
public class NewsNoticeReadRecordRepository extends ServiceImpl<NewsNoticeReadRecordMapper, NewsNoticeReadRecordEntity> {
  /**
   * 通过公告ID集合和客户编码查询公告阅读记录
   *
   * @param noticeIds    公告ID集合
   * @param userName 客户编码
   * @return 公告阅读记录集合
   */
  public List<NewsNoticeReadRecordEntity> findByNewsNoticeIdsAndUserName(List<String> noticeIds, String userName) {
    return this.lambdaQuery()
        .in(NewsNoticeReadRecordEntity::getNoticeId, noticeIds)
        .eq(NewsNoticeReadRecordEntity::getUserName, userName)
        .eq(NewsNoticeReadRecordEntity::getTenantCode, TenantUtils.getTenantCode()) // 增加租户控制
        .select(NewsNoticeReadRecordEntity::getNoticeId, NewsNoticeReadRecordEntity::getUserName)
        .list();
  }
}
