package com.biz.crm.mdm.business.news.notice.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.FileEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * 公告文件实体类
 *
 * <AUTHOR>
 * @date 2022/5/12
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "NewsNoticeFileEntity", description = "公告文件实体类")
@Entity
@TableName("mdm_news_notice_file")
@Table(name = "mdm_news_notice_file", indexes = {
    @Index(name = "mdm_news_notice_file_index1", columnList = "notice_id"),
    @Index(name = "mdm_news_notice_file_index2", columnList = "tenant_code"),
})
@org.hibernate.annotations.Table(appliesTo = "mdm_news_notice_file", comment = "公告文件表")
public class NewsNoticeFileEntity extends FileEntity {

  private static final long serialVersionUID = 5636349164148962136L;
  /**
   * 公告id
   */
  @ApiModelProperty("公告id")
  @Column(name = "notice_id", length = 64, columnDefinition = "VARCHAR(64) COMMENT '公告id'")
  private String noticeId;

  /**
   * 文件显示顺序
   */
  @ApiModelProperty("文件显示顺序")
  @Column(name = "sort_num", length = 5, columnDefinition = "int(5) COMMENT '文件显示顺序'")
  private Integer sortNum;
}
