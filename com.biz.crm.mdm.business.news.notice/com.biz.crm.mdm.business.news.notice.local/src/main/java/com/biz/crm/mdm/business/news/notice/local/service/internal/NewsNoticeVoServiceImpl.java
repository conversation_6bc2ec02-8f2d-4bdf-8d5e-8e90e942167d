package com.biz.crm.mdm.business.news.notice.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.common.message.sdk.service.SystemMessageVoService;
import com.biz.crm.mdm.business.news.notice.local.entity.NewsNoticeEntity;
import com.biz.crm.mdm.business.news.notice.local.entity.NewsNoticeFileEntity;
import com.biz.crm.mdm.business.news.notice.local.entity.NewsNoticeReadRecordEntity;
import com.biz.crm.mdm.business.news.notice.local.entity.NewsNoticeScopeEntity;
import com.biz.crm.mdm.business.news.notice.local.model.NewsNoticeModelVo;
import com.biz.crm.mdm.business.news.notice.local.model.PageResult;
import com.biz.crm.mdm.business.news.notice.local.repository.*;
import com.biz.crm.mdm.business.news.notice.local.service.base.AbstractRelationDataService;
import com.biz.crm.mdm.business.news.notice.sdk.dto.NewsNoticeCustomerPageDto;
import com.biz.crm.mdm.business.news.notice.sdk.dto.NewsNoticeDto;
import com.biz.crm.mdm.business.news.notice.sdk.dto.NewsNoticePageDto;
import com.biz.crm.mdm.business.news.notice.sdk.enums.ScopeType;
import com.biz.crm.mdm.business.news.notice.sdk.service.NewsNoticeVoService;
import com.biz.crm.mdm.business.news.notice.sdk.vo.NewsNoticeVo;
import com.biz.crm.mdm.business.news.notice.sdk.vo.SystemMessageNoticeVo;
import com.biz.crm.mdm.business.news.notice.sdk.vo.base.NewsFileVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgPositionVoService;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgPositionVo;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.user.sdk.service.UserPositionVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserPositionVo;
import com.biz.crm.workflow.sdk.service.TodoOverruledVoService;
import com.biz.crm.workflow.sdk.vo.TodoOverruledVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 公告VO服务接口实现类
 *
 * <AUTHOR>
 * @date 2022/5/13
 */
@Service
@Slf4j
public class NewsNoticeVoServiceImpl extends AbstractRelationDataService implements NewsNoticeVoService {

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;
  @Autowired(required = false)
  private NewsNoticeRepository newsNoticeRepository;
  @Autowired(required = false)
  private NewsNoticeModelRepository newsNoticeModelRepository;
  @Autowired(required = false)
  private NewsNoticeReadRecordRepository newsNoticeReadRecordRepository;
  @Autowired(required = false)
  private LoginUserService loginUserService;
  @Autowired(required = false)
  private NewsNoticeFileRepository newsNoticeFileRepository;
  @Autowired(required = false)
  private NewsNoticeScopeRepository newsNoticeScopeRepository;
  @Autowired(required = false)
  private UserPositionVoService userPositionVoService;
  @Autowired(required = false)
  private OrgPositionVoService orgPositionVoService;
  @Autowired(required = false)
  private OrgVoService orgVoService;

  @Autowired(required = false)
  private SystemMessageVoService systemMessageVoService;

  @Autowired(required = false)
  private TodoOverruledVoService todoOverruledVoService;

  @Override
  public Page<NewsNoticeVo> findByConditions(Pageable pageable, NewsNoticePageDto dto) {
    pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
    dto = ObjectUtils.defaultIfNull(dto, new NewsNoticePageDto());
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<NewsNoticeModelVo> modelPage = this.newsNoticeModelRepository.findByConditions(pageable, dto);
    Page<NewsNoticeVo> pageResult = new Page<>(modelPage.getCurrent(), modelPage.getSize(), modelPage.getTotal());
    if (CollectionUtils.isEmpty(modelPage.getRecords())) {
      return pageResult;
    }
    List<NewsNoticeVo> list = (List<NewsNoticeVo>) this.nebulaToolkitService.copyCollectionByBlankList(modelPage.getRecords()
        , NewsNoticeModelVo.class, NewsNoticeVo.class, LinkedHashSet.class, ArrayList.class);
    pageResult.setRecords(list);
    // 组织下的总人数 包括下级组织
    Set<String> orgCodeSet = list.stream()
        .map(NewsNoticeVo::getPublishOrgCode)
        .collect(Collectors.toSet());
    // 查询组织及其下级组织编码
    List<OrgVo> allChildren = orgVoService.findAllChildrenByOrgCodes(Lists.newArrayList(orgCodeSet));
    Set<String> childrenCodeSet = allChildren.stream()
        .map(OrgVo::getOrgCode)
        .collect(Collectors.toSet());
    // 组织和下级组织的映射
    List<OrgVo> orgVoList = allChildren.stream()
        .filter(vo -> orgCodeSet.contains(vo.getOrgCode()))
        .collect(Collectors.toList());
    Map<String, List<String>> orgToChildMap = new HashMap<>(orgVoList.size());
    for (OrgVo orgVo : orgVoList) {
      List<String> orgCodeList = Lists.newLinkedList();
      String ruleCode = orgVo.getRuleCode();
      for (OrgVo allChild : allChildren) {
        String childRuleCode = allChild.getRuleCode();
        boolean isStart = childRuleCode.startsWith(ruleCode);
        if (!isStart) {
          continue;
        }
        orgCodeList.add(allChild.getOrgCode());
      }
      orgToChildMap.put(orgVo.getOrgCode(), orgCodeList);
    }
    // 组织绑定了多个岗位
    List<OrgPositionVo> orgPositionVos = orgPositionVoService.findByOrgCodes(Lists.newArrayList(childrenCodeSet));
    Map<String, List<OrgPositionVo>> orgToPositionMap;
    Set<String> positionCodeSet;
    if (!CollectionUtils.isEmpty(orgPositionVos)) {
      orgToPositionMap = orgPositionVos.stream()
          .collect(Collectors.groupingBy(OrgPositionVo::getOrgCode));
      positionCodeSet = orgPositionVos.stream()
          .map(OrgPositionVo::getPositionCode)
          .collect(Collectors.toSet());
    } else {
      orgToPositionMap = Maps.newHashMap();
      positionCodeSet = Sets.newHashSet();
    }
    // 人员绑定了多个岗位
    List<UserPositionVo> userPositionVos;
    if (!CollectionUtils.isEmpty(positionCodeSet)) {
      userPositionVos = userPositionVoService.findByPositionCodeList(Lists.newArrayList(positionCodeSet));
    } else {
      userPositionVos = Lists.newArrayList();
    }
    Map<String, List<UserPositionVo>> positionToUserMap;
    if (!CollectionUtils.isEmpty(userPositionVos)) {
      positionToUserMap = userPositionVos.stream()
          .collect(Collectors.groupingBy(UserPositionVo::getPositionCode));
    } else {
      positionToUserMap = new HashMap<>(0);
    }
    // key：分页结果的组织，value：人数
    Map<String, Integer> map = new HashMap<>(orgCodeSet.size());
    for (Map.Entry<String, List<String>> entry : orgToChildMap.entrySet()) {
      String orgCode = entry.getKey();
      Set<String> userSet = Sets.newHashSet();
      // 组织下的子组织
      List<String> childOrgCodeList = entry.getValue();
      if (CollectionUtils.isEmpty(childOrgCodeList)) {
        continue;
      }
      for (String childOrgCode : childOrgCodeList) {
        // 绑定的岗位
        List<OrgPositionVo> orgPositionVoList = orgToPositionMap.get(childOrgCode);
        if (CollectionUtils.isEmpty(orgPositionVoList)) {
          continue;
        }
        for (OrgPositionVo orgPositionVo : orgPositionVoList) {
          String positionCode = orgPositionVo.getPositionCode();
          // 岗位的人，还得去重
          List<UserPositionVo> userPositionVoList = positionToUserMap.get(positionCode);
          if (CollectionUtils.isEmpty(userPositionVoList)) {
            continue;
          }
          Set<String> userNameSet = userPositionVoList.stream().map(UserPositionVo::getUserName).collect(Collectors.toSet());
          userSet.addAll(userNameSet);
        }
      }
      map.put(orgCode, userSet.size());
    }
    // 人数填充
    for (NewsNoticeVo newsNoticeVo : list) {
      Integer integer = map.get(newsNoticeVo.getPublishOrgCode());
      newsNoticeVo.setTotalNum(integer);
    }
    return pageResult;
  }

  /**
   * 客户-公告分页列表
   * 1.根据当前登录客户信息,封装请求参数dto中相应的查询参数
   * 2.分页查询出客户公告分页列表
   * 3.封装公告已读标志
   *
   * @param pageable 分页信息
   * @param dto      请求参数dto
   * @return Page<NoticeVo> 公告分页列表
   */
  @Override
  public Page<NewsNoticeVo> findByNewsNoticeCustomerPageDto(Pageable pageable, NewsNoticeCustomerPageDto dto) {
    pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
    dto = ObjectUtils.defaultIfNull(dto, new NewsNoticeCustomerPageDto());
    dto.setTenantCode(TenantUtils.getTenantCode());
    if (StringUtils.isBlank(dto.getBusinessCode())) {
      this.buildCustomerPageDto(dto);
    }
    //查询当前登录的发布部门名称
    FacturerUserDetails loginDetails = loginUserService.getLoginDetails(FacturerUserDetails.class);
    dto.setOrgCodeList(Arrays.asList(loginDetails.getOrgCode()));
    Page<NewsNoticeModelVo> entityPage = this.newsNoticeModelRepository.findByNewsNoticeCustomerPageDto(pageable, dto);
//    List<UserPositionVo> userlist = this.userPositionVoService.findAllUser(TenantUtils.getTenantCode());
//    List<String> alluser = userlist.stream().map(UserPositionVo::getUserName).distinct().collect(Collectors.toList());
    Page<NewsNoticeVo> pageResult = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
    if (CollectionUtils.isEmpty(entityPage.getRecords())) {
      return pageResult;
    }
    //转换并封装公告相关信息
    List<NewsNoticeVo> list = Lists.newArrayList(this.nebulaToolkitService
        .copyCollectionByBlankList(entityPage.getRecords(), NewsNoticeModelVo.class, NewsNoticeVo.class, LinkedHashSet.class, ArrayList.class));
    List<String> noticeIds = list.stream().map(NewsNoticeVo::getId).collect(Collectors.toList());
    List<NewsNoticeReadRecordEntity> recordEntities = new ArrayList<>();
    if (StringUtils.isNoneBlank(dto.getUserName())) {
      recordEntities = this.newsNoticeReadRecordRepository.findByNewsNoticeIdsAndUserName(noticeIds, dto.getUserName());
    } else {
      recordEntities = this.newsNoticeReadRecordRepository.findByNewsNoticeIdsAndUserName(noticeIds, loginUserService.getLoginAccountName());
    }
    Map<String, NewsNoticeReadRecordEntity> recordEntityMap = Maps.newHashMap();
    if (!CollectionUtils.isEmpty(recordEntities)) {
      recordEntityMap = recordEntities.stream().collect(Collectors.toMap(NewsNoticeReadRecordEntity::getNoticeId, t -> t, (key1, key2) -> key2));
    }
    for (NewsNoticeVo noticeVo : list) {
      noticeVo.setHaveRead(Objects.nonNull(recordEntityMap.get(noticeVo.getId())));
    }
    //-------------------设置所有的未读-------------------
    //设置所有未阅读的消息通知数量
    List<NewsNoticeModelVo> newsNoticeModelVos = newsNoticeModelRepository.findAllByNewsNoticeCustomer(dto);
    //转换并封装公告相关信息
    List<NewsNoticeVo> newsNoticeVos = Lists.newArrayList(this.nebulaToolkitService
        .copyCollectionByBlankList(newsNoticeModelVos, NewsNoticeModelVo.class, NewsNoticeVo.class, LinkedHashSet.class, ArrayList.class));
    List<String> ids = newsNoticeVos.stream().map(NewsNoticeVo::getId).collect(Collectors.toList());
    List<NewsNoticeReadRecordEntity> newsNoticeReadRecordEntities = new ArrayList<>();
    if (StringUtils.isNoneBlank(dto.getUserName())) {
      newsNoticeReadRecordEntities = this.newsNoticeReadRecordRepository.findByNewsNoticeIdsAndUserName(ids, dto.getUserName());
    } else {
      newsNoticeReadRecordEntities = this.newsNoticeReadRecordRepository.findByNewsNoticeIdsAndUserName(ids, loginUserService.getLoginAccountName());
    }
    Map<String, NewsNoticeReadRecordEntity> finalRecordEntityMap = Maps.newHashMap();
    if (!CollectionUtils.isEmpty(recordEntities)) {
      finalRecordEntityMap = newsNoticeReadRecordEntities.stream().collect(Collectors.toMap(NewsNoticeReadRecordEntity::getNoticeId, t -> t, (key1, key2) -> key2));
    }
    /*Map<String, NewsNoticeReadRecordEntity> finalRecordEntityMap = recordEntityMap;*/
    Integer unreadCount = 0;
    for (NewsNoticeModelVo noticeModelVo : newsNoticeModelVos) {
      if (!Objects.nonNull(finalRecordEntityMap.get(noticeModelVo.getId()))) {
        unreadCount++;
      }
    }
    for (NewsNoticeVo noticeVo : list) {
      if (noticeVo.getTotalNum() == null || noticeVo.getTotalNum() == 0) {
        noticeVo.setTotalNum(userPositionVoService.countAllUser(TenantUtils.getTenantCode()));
      }
    }
    //补充附件
    if (!CollectionUtils.isEmpty(list)) {
      List<String> idList = list.stream().map(NewsNoticeVo::getId).collect(Collectors.toList());
      List<NewsNoticeFileEntity> fileList = newsNoticeFileRepository.findByNoticeIds(idList);
      if (!CollectionUtils.isEmpty(fileList)) {
        Map<String, List<NewsNoticeFileEntity>> map = fileList.stream().collect(Collectors.groupingBy(NewsNoticeFileEntity::getNoticeId));
        for (NewsNoticeVo vo : list) {
          String id = vo.getId();
          List<NewsNoticeFileEntity> newsNoticeFileEntities = map.get(id);
          if (CollectionUtils.isEmpty(newsNoticeFileEntities)) {
            continue;
          }
          List<NewsFileVo> fileVos = new ArrayList<>();
          newsNoticeFileEntities.forEach(k -> {
            NewsFileVo fileVo = new NewsFileVo();
            fileVo.setSortNum(k.getSortNum());
            fileVo.setOriginalFileName(k.getOriginalFileName());
            fileVo.setFileCode(k.getFileCode());
            fileVos.add(fileVo);
          });
          vo.setFileList(fileVos);
        }
      }
    }
    pageResult.setRecords(list);
    PageResult pr = new PageResult();
    BeanUtils.copyProperties(pageResult, pr);
    pr.setUnreadTotal(unreadCount);
    /*return pageResult;*/
    return pr;//加上未读的总数
  }

  @Override
  public NewsNoticeVo findById(String id) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    NewsNoticeEntity entity = this.newsNoticeRepository.findById(id);
    if (Objects.isNull(entity)) {
      return null;
    }
    NewsNoticeVo noticeVo = this.nebulaToolkitService.copyObjectByBlankList(entity, NewsNoticeVo.class, LinkedHashSet.class, ArrayList.class);
    //补充附件信息
    List<NewsFileVo> fileList = noticeVo.getFileList();
    if (ObjectUtils.isNotEmpty(fileList)) {
      fileList.forEach(k -> {
        NewsNoticeFileEntity fileEntity = newsNoticeFileRepository.getById(k.getId());
        k.setOriginalFileName(fileEntity.getOriginalFileName());
        k.setSortNum(fileEntity.getSortNum());
        k.setTenantCode(fileEntity.getTenantCode());
      });
    }
    this.perfectScopeInfo(noticeVo.getScopeList());
    return noticeVo;
  }

  @Override
  @Transactional
  public void create(List<NewsNoticeDto> dtoList) {
    this.createValidation(dtoList);
    AbstractCrmUserIdentity loginDetails = loginUserService.getAbstractLoginUser();
    // 确认组织机构信息
    Object orgCodeObject = loginDetails.invokeFieldValue("orgCode");
    Object orgNameObject = loginDetails.invokeFieldValue("orgName");

    List<NewsNoticeEntity> noticeEntities = dtoList.stream().map(dto -> {
      NewsNoticeEntity entity = this.nebulaToolkitService.copyObjectByBlankList(dto, NewsNoticeEntity.class, LinkedHashSet.class, ArrayList.class);
      entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
      entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
      entity.setPublishOrgCode(orgCodeObject == null?null:orgCodeObject.toString());
      entity.setPublishOrgName(orgNameObject == null?null:orgNameObject.toString());
      //新增租户编号
      entity.setTenantCode(TenantUtils.getTenantCode());
      return entity;
    }).collect(Collectors.toList());
    this.newsNoticeRepository.saveBatch(noticeEntities);
    this.saveExtInfo(noticeEntities);

  }

  /**
   * 根据登录人查询所有消息
   * 2023-09-15 - 新需求 ： 需要一键将所有消息标记为已读，不懂以前的范围逻辑，故使用
   * findByNewsNoticeCustomerPageDto接口的逻辑，只是将分页去掉。
   */
  @Override
  public List<NewsNoticeVo> findAllByCurrentUser() {
    NewsNoticeCustomerPageDto dto = new NewsNoticeCustomerPageDto();
    dto.setTenantCode(TenantUtils.getTenantCode());
    if (StringUtils.isBlank(dto.getBusinessCode())) {
      this.buildCustomerPageDto(dto);
    }
    //查询当前登录的发布部门名称
    FacturerUserDetails loginDetails = loginUserService.getLoginDetails(FacturerUserDetails.class);
    dto.setOrgCodeList(Arrays.asList(loginDetails.getOrgCode()));
    List<NewsNoticeModelVo> list = this.newsNoticeModelRepository.findAllByNewsNoticeCustomer(dto);
    if (CollectionUtils.isEmpty(list)) {
      return new ArrayList<>();
    }
    return (List<NewsNoticeVo>) this.nebulaToolkitService.copyCollectionByBlankList(list, NewsNoticeModelVo.class, NewsNoticeVo.class, HashSet.class, ArrayList.class);

  }

  /**
   * 查询当前用户未读消息
   *
   * @return
   */
  @Override
  public SystemMessageNoticeVo findHomeNoRead() {
    SystemMessageNoticeVo systemMessageNoticeVo = new SystemMessageNoticeVo();

    //系统消息调common
    Integer systemMessageTotalVo = systemMessageVoService.findHomeNotReadCount();
    systemMessageNoticeVo.setSystemMessageTotal(ObjectUtils.isNotEmpty(systemMessageTotalVo) ? systemMessageTotalVo : 0);
    //通知公告调 newsNotice
    Pageable pageable = PageRequest.of(1,1);
    NewsNoticeCustomerPageDto dto = new NewsNoticeCustomerPageDto();
    String accountName = loginUserService.getLoginAccountName();
    dto.setBusinessCode(accountName);
    systemMessageNoticeVo.setBussinessCode(accountName);
    Page<NewsNoticeVo> res = this.findByNewsNoticeCustomerPageDto(pageable, dto);
    PageResult pageResult = new PageResult();
    BeanUtils.copyProperties(res, pageResult);
    systemMessageNoticeVo.setNewsNoticeTotal(ObjectUtils.isNotEmpty(pageResult.getUnreadTotal()) ? pageResult.getUnreadTotal() : 0);
    // 待处理 已处理 工作流的
    TodoOverruledVo todoOverruledVo = todoOverruledVoService.todoOverruledCount();
    if(ObjectUtils.isNotEmpty(todoOverruledVo)){
      systemMessageNoticeVo.setPendingMessageTotal(ObjectUtils.isNotEmpty(todoOverruledVo.getPendingMessageTotal()) ? todoOverruledVo.getPendingMessageTotal() : 0);
      systemMessageNoticeVo.setRejectedMessageTotal(ObjectUtils.isNotEmpty(todoOverruledVo.getRejectedMessageTotal()) ? todoOverruledVo.getRejectedMessageTotal() : 0);
    }

    return systemMessageNoticeVo;
  }

  /**
   * 保存扩展信息
   *
   * @param noticeEntities 公告实体列表
   */
  private void saveExtInfo(List<NewsNoticeEntity> noticeEntities) {
    List<NewsNoticeScopeEntity> noticeScopeEntities = Lists.newArrayList();
    List<NewsNoticeFileEntity> noticeFileEntities = Lists.newArrayList();
    noticeEntities.forEach(noticeEntity -> {
      if (!CollectionUtils.isEmpty(noticeEntity.getScopeList())) {
        noticeEntity.getScopeList().forEach(noticeScopeEntity -> {
          //新增租户编号
          noticeScopeEntity.setTenantCode(TenantUtils.getTenantCode());
          noticeScopeEntity.setNoticeId(noticeEntity.getId());
        });
        noticeScopeEntities.addAll(noticeEntity.getScopeList());
      }
      if (!CollectionUtils.isEmpty(noticeEntity.getFileList())) {
        noticeEntity.getFileList().forEach(noticeFileEntity -> {
          noticeFileEntity.setTenantCode(noticeEntity.getTenantCode());
          noticeFileEntity.setNoticeId(noticeEntity.getId());
        });
        noticeFileEntities.addAll(noticeEntity.getFileList());
      }
    });
    if (!CollectionUtils.isEmpty(noticeScopeEntities)) {
      this.newsNoticeScopeRepository.saveBatch(noticeScopeEntities);
    }
    if (!CollectionUtils.isEmpty(noticeFileEntities)) {
      this.newsNoticeFileRepository.saveBatch(noticeFileEntities);
    }
  }

  /**
   * 在创建newsnotice模型对象之前，检查对象各属性的正确性，其主键属性必须没有值
   *
   * @param dtoList 检查对象
   */
  private void createValidation(List<NewsNoticeDto> dtoList) {
    Validate.isTrue(!CollectionUtils.isEmpty(dtoList), "进行当前操作时，信息对象必须传入!");
    dtoList.forEach(dto -> {
      dto.setId(null);
      dto.setTenantCode(TenantUtils.getTenantCode());
      Validate.notBlank(dto.getTitle(), "缺失标题");
      Validate.notBlank(dto.getType(), "缺失类型");
      Validate.notNull(dto.getStartTime(), "缺失生效开始时间");
      Validate.notNull(dto.getEndTime(), "缺失生效结束时间");
      Validate.isTrue(dto.getStartTime().before(dto.getEndTime()), "生效开始时间必须小于生效结束时间");
      if (!CollectionUtils.isEmpty(dto.getScopeList())) {
        dto.getScopeList().forEach(noticeScopeDto -> {
          List<String> scopeTypeList = Arrays.stream(ScopeType.values()).map(ScopeType::name).collect(Collectors.toList());
          Validate.isTrue(scopeTypeList.contains(noticeScopeDto.getScopeType()), "不支持的范围类型!");
        });
      }
      Validate.isTrue(dto.getTitle().length() < 128, "标题，在进行编辑时填入值超过了限定长度(128)，请检查!");
    });
  }
}
