package com.biz.crm.mdm.business.news.notice.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;


/**
 * 公告范围实体类
 *
 * <AUTHOR>
 * @date 2022/5/12
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "NewsNoticeScopeEntity", description = "公告范围实体类")
@Entity
@TableName("mdm_news_notice_scope")
@Table(name = "mdm_news_notice_scope", indexes = {
    @Index(name = "mdm_news_notice_scope_index1", columnList = "notice_id"),
    @Index(name = "mdm_news_notice_scope_index2", columnList = "tenant_code"),
    @Index(name = "mdm_news_notice_scope_index3", columnList = "scope_code"),
})
@org.hibernate.annotations.Table(appliesTo = "mdm_news_notice_scope", comment = "公告范围表")
public class NewsNoticeScopeEntity extends TenantEntity {

  private static final long serialVersionUID = 472450903966651456L;
  /**
   * 公告id
   */
  @ApiModelProperty("公告id")
  @Column(name = "notice_id", length = 64, columnDefinition = "VARCHAR(64) COMMENT '公告id'")
  private String noticeId;

  /**
   * 范围编码
   */
  @ApiModelProperty("范围编码")
  @Column(name = "scope_code", length = 64, columnDefinition = "VARCHAR(64) COMMENT '范围编码'")
  private String scopeCode;

  /**
   * 是否包含(true: 包含,false:不包含)
   */
  @ApiModelProperty("是否包含(true: 包含,false:不包含)")
  @Column(name = "contain_flag", columnDefinition = "int(1) COMMENT '是否包含(true: 包含,false:不包含)'")
  private Boolean containFlag;

  /**
   * 范围类型(ORG:组织范围,CHANNEL:渠道范围,CUSTOMER:客户范围)
   */
  @ApiModelProperty("范围类型(ORG:组织范围,CHANNEL:渠道范围,CUSTOMER:客户范围)")
  @Column(name = "scope_type", length = 64, columnDefinition = "VARCHAR(64) COMMENT '范围类型(ORG:组织范围,CHANNEL:渠道范围,CUSTOMER:客户范围)'")
  private String scopeType;
}
