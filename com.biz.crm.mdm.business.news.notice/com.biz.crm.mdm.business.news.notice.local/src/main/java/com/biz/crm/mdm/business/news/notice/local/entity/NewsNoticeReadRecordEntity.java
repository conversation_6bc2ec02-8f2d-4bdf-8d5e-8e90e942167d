package com.biz.crm.mdm.business.news.notice.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;


/**
 * 公告阅读记录实体类
 *
 * <AUTHOR>
 * @date 2022/5/12
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "NewsNoticeReadRecordEntity", description = "公告阅读记录实体类")
@Entity
@TableName("mdm_news_notice_read_record")
@Table(name = "mdm_news_notice_read_record", indexes = {
    @Index(name = "mdm_news_notice_index1", columnList = "notice_id,user_name", unique = true),
    @Index(name = "mdm_news_notice_index2", columnList = "tenant_code"),
})
@org.hibernate.annotations.Table(appliesTo = "mdm_news_notice_read_record", comment = "公告阅读记录表")
public class NewsNoticeReadRecordEntity extends TenantOpEntity {

  private static final long serialVersionUID = -5873542408393242254L;
  /**
   * 公告id
   */
  @ApiModelProperty("公告id")
  @Column(name = "notice_id", length = 64, columnDefinition = "VARCHAR(64) COMMENT '公告id'")
  private String noticeId;

  /**
   * 用户名
   */
  @ApiModelProperty("用户名")
  @Column(name = "user_name", length = 64, columnDefinition = "VARCHAR(64) COMMENT '用户名'")
  private String userName;
}
