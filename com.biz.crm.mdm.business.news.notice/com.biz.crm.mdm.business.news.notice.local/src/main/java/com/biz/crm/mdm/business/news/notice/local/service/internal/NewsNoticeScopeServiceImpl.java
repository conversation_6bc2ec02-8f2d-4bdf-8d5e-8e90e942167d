package com.biz.crm.mdm.business.news.notice.local.service.internal;

import com.biz.crm.mdm.business.news.notice.local.entity.NewsNoticeScopeEntity;
import com.biz.crm.mdm.business.news.notice.local.repository.NewsNoticeScopeRepository;
import com.biz.crm.mdm.business.news.notice.local.service.NewsNoticeScopeService;
import com.biz.crm.mdm.business.news.notice.sdk.dto.base.ScopeDto;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 公告范围表服务实现类
 *
 * <AUTHOR>
 * @date 2022/5/13
 */
@Service("noticeScopeService")
public class NewsNoticeScopeServiceImpl implements NewsNoticeScopeService {

  @Autowired(required = false)
  private NewsNoticeScopeRepository newsNoticeScopeRepository;
  @Autowired(required = false)
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;

  @Override
  @Transactional
  public void update(List<ScopeDto> dtoList, String noticeId) {
    Validate.notBlank(noticeId, "公告ID不能为空");
    this.newsNoticeScopeRepository.deleteByNoticeId(noticeId);
    if (CollectionUtils.isEmpty(dtoList)) {
      return;
    }
    String tenantCode = TenantUtils.getTenantCode();
    //过滤无效的数据
    List<NewsNoticeScopeEntity> entities = dtoList.stream()
        .filter(dto -> Objects.nonNull(dto) && StringUtils.isNotEmpty(dto.getScopeCode()) && StringUtils.isNotEmpty(dto.getScopeType()))
        .map(dto -> {
          NewsNoticeScopeEntity entity = this.nebulaToolkitService.copyObjectByWhiteList(dto, NewsNoticeScopeEntity.class, HashSet.class, ArrayList.class);
          entity.setNoticeId(noticeId);
          entity.setTenantCode(tenantCode);
          return entity;
        }).collect(Collectors.toList());
    if (CollectionUtils.isEmpty(entities)) {
      return;
    }
    this.newsNoticeScopeRepository.saveBatch(entities);
  }
}
