package com.biz.crm.mdm.business.news.notice.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.news.notice.sdk.dto.NewsNoticeCustomerPageDto;
import com.biz.crm.mdm.business.news.notice.sdk.dto.NewsNoticePageDto;
import com.biz.crm.mdm.business.news.notice.sdk.service.NewsNoticeVoService;
import com.biz.crm.mdm.business.news.notice.sdk.vo.NewsNoticeVo;
import com.biz.crm.mdm.business.news.notice.sdk.vo.SystemMessageNoticeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 消息公告: NewsNoticeVo: 公告管理
 *
 * <AUTHOR>
 * @date 2022/5/13
 */
@Slf4j
@RestController
@RequestMapping("/v1/newsnotice/newsnotice")
@Api(tags = "消息公告: NewsNoticeVo: 公告管理")
public class NewsNoticeVoController {

  @Autowired(required = false)
  private NewsNoticeVoService newsNoticeVoService;

  /**
   * 公告分页查询
   * @param pageable 分页信息
   * @param dto 参数Dto
   * @return 公告分页数据
   */
  @ApiOperation(value = "公告分页查询")
  @GetMapping("/findByConditions")
  public Result<Page<NewsNoticeVo>> findByConditions(@PageableDefault(50) Pageable pageable,
                                                     @ApiParam(name = "NewsNoticePageDto", value = "分页Dto") NewsNoticePageDto dto) {
    try {
      return Result.ok(this.newsNoticeVoService.findByConditions(pageable, dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 客户-公告分页查询
   *
   * @param pageable 分页信息
   * @param dto      参数Dto
   * @return 公告分页数据
   */
  @ApiOperation(value = "客户-公告分页查询")
  @GetMapping("/findByNewsNoticeCustomerPageDto")
  public Result<Page<NewsNoticeVo>> findByNewsNoticeCustomerPageDto(@PageableDefault(50) Pageable pageable,
                                                            @ApiParam(name = "NewsNoticeCustomerPageDto", value = "分页Dto") NewsNoticeCustomerPageDto dto) {
    try {
      return Result.ok(this.newsNoticeVoService.findByNewsNoticeCustomerPageDto(pageable, dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 通过ID获取公告信息
   *
   * @param id 公告ID
   * @return 操作结果
   */
  @ApiOperation(value = "通过ID获取公告信息")
  @GetMapping("/findById")
  public Result<NewsNoticeVo> findById(@RequestParam(value = "id", required = false) @ApiParam(name = "id", value = "主键ID") String id) {
    try {
      return Result.ok(this.newsNoticeVoService.findById(id));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 获取当前用户未读消息
   * @return
   */
  @ApiOperation(value = "未读消息查询")
  @GetMapping("/findHomeNoRead")
  public Result<SystemMessageNoticeVo> findHomeNoRead() {
    try {
      return Result.ok(this.newsNoticeVoService.findHomeNoRead());
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
