package com.biz.crm.mdm.business.news.notice.local.service;

import com.biz.crm.mdm.business.news.notice.local.entity.NewsNoticeReadRecordEntity;
import com.biz.crm.mdm.business.news.notice.sdk.dto.NewsNoticeReadRecordDto;

import java.util.List;

/**
 * 公告阅读记录表服务接口
 *
 * <AUTHOR>
 * @date 2022/5/12
 */
public interface NewsNoticeReadRecordService {

  /**
   * 创建公告阅读记录
   *
   * @param dto 参数dto
   * @return 创建的公告阅读记录
   */
  List<NewsNoticeReadRecordEntity> create(NewsNoticeReadRecordDto dto);

  /**
   * 根据当前登录人 进行全部已读
   */
  void all();
}
