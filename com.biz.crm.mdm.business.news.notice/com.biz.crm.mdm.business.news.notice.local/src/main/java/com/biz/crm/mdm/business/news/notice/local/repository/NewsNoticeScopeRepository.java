package com.biz.crm.mdm.business.news.notice.local.repository;

import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.news.notice.local.entity.NewsNoticeScopeEntity;
import com.biz.crm.mdm.business.news.notice.local.mapper.NewsNoticeChannelScopeMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

/**
 * 公告渠道范围表的数据库访问类 {@link NewsNoticeScopeEntity}
 *
 * <AUTHOR>
 * @date 2022/5/12
 */
@Component
public class NewsNoticeScopeRepository extends ServiceImpl<NewsNoticeChannelScopeMapper, NewsNoticeScopeEntity> {

  /**
   * 通过公告ID删除公告范围信息
   *
   * @param noticeId 公告ID
   */
  public void deleteByNoticeId(String noticeId) {
    this.lambdaUpdate()
        .eq(NewsNoticeScopeEntity::getNoticeId, noticeId)
        .eq(NewsNoticeScopeEntity::getTenantCode, TenantUtils.getTenantCode()) // 增加租户控制
        .remove();
  }
}
