package com.biz.crm.mdm.business.news.notice.sdk.dto.base;

import com.biz.crm.business.common.sdk.dto.FileDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件信息Dto
 *
 * <AUTHOR>
 * @date 2022/5/12
 */
@Data
@ApiModel(value = "FileDto", description = "文件信息Dto")
public class NewsFileDto extends FileDto {

  /**
   * 公告id
   */
  @ApiModelProperty("公告id")
  private String noticeId;

  /**
   * 文件显示顺序
   */
  @ApiModelProperty(value = "文件显示顺序")
  private Integer sortNum;
}
