package com.biz.crm.mdm.business.news.notice.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 公告类型
 * <AUTHOR>
 * @date 2022/5/12
 */
@Getter
@AllArgsConstructor
public enum NewsNoticeType {
  INFORM("0", "0", "通知公告", "0"),

  POLICY("1", "1", "政策", "1"),

  FILE("2", "2", "文件", "2"),
  ;

  /** 系统key */
  private String key;

  /** 字典编码 */
  private String dictCode;

  /** 字典值 */
  private String value;

  /** 字典排序 */
  private String order;
}
