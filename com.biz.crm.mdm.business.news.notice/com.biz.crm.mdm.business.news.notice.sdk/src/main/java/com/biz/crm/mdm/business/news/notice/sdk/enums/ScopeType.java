package com.biz.crm.mdm.business.news.notice.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 范围类型
 *
 * <AUTHOR>
 * @date 2022/5/12
 */
@Getter
@AllArgsConstructor
public enum ScopeType {

  /**
   * 范围类型取值
   */
  ORG("org","org", "组织范围","0"),
  CHANNEL("channel","channel", "渠道范围","1"),
  CUSTOMER("customer","customer", "客户范围","2");

  /** 系统key */
  private String key;

  /** 字典编码 */
  private String dictCode;

  /** 字典值 */
  private String value;

  /** 字典排序 */
  private String order;
}
