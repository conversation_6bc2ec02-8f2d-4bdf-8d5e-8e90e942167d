package com.biz.crm.mdm.business.news.notice.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-02-23 10:03
 * @description：新消息提示推送
 */
@Data
@ApiModel(value = "SystemMessageNoticeVo", description = "新消息提示推送")
public class SystemMessageNoticeVo {

  /**
   * 消息公告未读数量
   */
  @ApiModelProperty("消息公告未读数量")
  private Integer newsNoticeTotal;
  /**
   * 系统通知未读数量
   */
  @ApiModelProperty("系统通知未读数量")
  private Integer systemMessageTotal;
  /**
   * 待处理消息未读数量
   */
  @ApiModelProperty("待处理消息未读数量")
  private Integer pendingMessageTotal;
  /**
   * 被驳回消息未读数量
   */
  @ApiModelProperty("被驳回消息未读数量")
  private Integer rejectedMessageTotal;

  /** 执行账号 */
  private String bussinessCode;

  /**
   * 执行类型
   */
  private String bussinessType;

}
