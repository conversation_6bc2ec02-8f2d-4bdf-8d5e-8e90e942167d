package com.biz.crm.mdm.business.news.notice.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import com.biz.crm.mdm.business.news.notice.sdk.dto.base.NewsFileDto;
import com.biz.crm.mdm.business.news.notice.sdk.dto.base.ScopeDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import java.util.List;


/**
 * 公告信息Dto
 *
 * <AUTHOR>
 * @date 2022/5/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "NoticeDto", description = "公告信息Dto")
public class NewsNoticeDto extends TenantDto {

  /**
   * 标题
   */
  @ApiModelProperty(value = "标题")
  private String title;
  /**
   * 类型
   */
  @ApiModelProperty(value = "类型")
  private String type;
  /**
   * 生效开始时间
   */
  @ApiModelProperty("生效开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date startTime;
  /**
   * 生效结束时间
   */
  @ApiModelProperty("生效结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;
  /**
   * 公告内容
   */
  @ApiModelProperty(value = "公告内容")
  private String content;
  /**
   * 发布部门code
   */
  @ApiModelProperty("发布部门code")
  private String publishOrgCode;
  /**
   * 发布部门名称
   */
  @ApiModelProperty("发布部门名称")
  private String publishOrgName;
  /**
   * 文件信息
   */
  @ApiModelProperty(value = "文件信息")
  private List<NewsFileDto> fileList;
  /**
   * 范围信息
   */
  @ApiModelProperty(value = "范围信息")
  private List<ScopeDto> scopeList;

}
