package com.biz.crm.mdm.business.news.notice.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.news.notice.sdk.dto.NewsNoticeCustomerPageDto;
import com.biz.crm.mdm.business.news.notice.sdk.dto.NewsNoticeDto;
import com.biz.crm.mdm.business.news.notice.sdk.dto.NewsNoticePageDto;
import com.biz.crm.mdm.business.news.notice.sdk.vo.NewsNoticeVo;
import com.biz.crm.mdm.business.news.notice.sdk.vo.SystemMessageNoticeVo;
import org.springframework.data.domain.Pageable;

import java.util.List;


/**
 * 公告VO服务接口类
 *
 * <AUTHOR>
 * @date 2022/5/12
 */
public interface NewsNoticeVoService {

  /**
   * 公告分页列表
   *
   * @param dto      请求参数dto
   * @param pageable 分页信息
   * @return Page<NoticeVo> 公告分页信息
   */
  Page<NewsNoticeVo> findByConditions(Pageable pageable, NewsNoticePageDto dto);


  /**
   * 客户-公告分页列表
   *
   * @param dto      请求参数dto
   * @param pageable 分页信息
   * @return Page<NoticeVo> 公告分页列表
   */
  Page<NewsNoticeVo> findByNewsNoticeCustomerPageDto(Pageable pageable, NewsNoticeCustomerPageDto dto);

  /**
   * 通过ID获取公告详细信息
   *
   * @param id 收货地址ID
   * @return 公告信息
   */
  NewsNoticeVo findById(String id);

  /**
   * 公告批量创建
   *
   * @param dtoList 参数dto
   */
  void create(List<NewsNoticeDto> dtoList);

  /**
   * 查询当前用户未读消息
   * @return
   */
  SystemMessageNoticeVo findHomeNoRead();

  /**
   * 根据登录人信息 查询全部公告
   */
  List<NewsNoticeVo> findAllByCurrentUser();
}
