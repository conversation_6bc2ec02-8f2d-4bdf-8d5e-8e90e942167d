package com.biz.crm.mdm.business.news.notice.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;

/**
 * 公告事件Vo
 *
 * <AUTHOR>
 * @date 2022/5/12
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "NewsNoticeEventVo", description = "公告事件Vo")
public class NewsNoticeEventVo extends TenantVo {

  private static final long serialVersionUID = 5147725408899262938L;
  /**
   * 标题
   */
  @ApiModelProperty(value = "标题")
  private String title;
  /**
   * 类型
   */
  @ApiModelProperty(value = "类型")
  private String type;
  /**
   * 生效开始时间
   */
  @ApiModelProperty("生效开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date startTime;
  /**
   * 生效结束时间
   */
  @ApiModelProperty("生效结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;
  /**
   * 公告内容
   */
  @ApiModelProperty(value = "公告内容")
  private String content;
  /**
   * 发布部门code
   */
  @ApiModelProperty(value = "发布部门code")
  private String publishOrgCode;
}
