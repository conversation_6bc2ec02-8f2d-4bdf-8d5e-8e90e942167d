<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.biz.crm.mdm</groupId>
		<artifactId>crm-mdm</artifactId>
		<version>202405</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.biz.crm.mdm.boot</groupId>
	<artifactId>mdm-boot</artifactId>
	<packaging>pom</packaging>

	<properties>
		<maven.install.skip>true</maven.install.skip>
		<maven.deploy.skip>true</maven.deploy.skip>
	</properties>

	<modules>
		<module>com.biz.crm.mdm.admin.web</module>
	</modules>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.biz-united.nebula.rbac</groupId>
				<artifactId>rbac-local-starter</artifactId>
				<version>${nebula.version}</version>
			</dependency>
			<dependency>
				<groupId>com.biz-united.nebula.security</groupId>
				<artifactId>security-local-starter</artifactId>
				<version>${nebula.version}</version>
			</dependency>
			<dependency>
				<groupId>com.biz-united.nebula.europa</groupId>
				<artifactId>europa-local-starter</artifactId>
				<version>${nebula.version}</version>
			</dependency>
			<dependency>
				<groupId>com.biz-united.nebula.europa.database</groupId>
				<artifactId>europa-database-local-starter</artifactId>
				<version>${nebula.version}</version>
			</dependency>
			<dependency>
				<groupId>com.biz-united.nebula.competence</groupId>
				<artifactId>competence-local-starter</artifactId>
				<version>${nebula.version}</version>
			</dependency>
			<dependency>
				<groupId>com.biz-united.nebula.competence.tacit</groupId>
				<artifactId>competence-tacit-starter</artifactId>
				<version>${nebula.version}</version>
			</dependency>
			<dependency>
				<groupId>com.biz-united.nebula.mars</groupId>
				<artifactId>mars-local-starter</artifactId>
				<version>${nebula.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
</project>
