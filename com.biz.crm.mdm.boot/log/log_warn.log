2025-06-24 14:44:21.917 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[crm-mdm] & group[dev]
2025-06-24 14:44:24.931 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[crm-mdm.yaml] & group[dev]
2025-06-24 14:45:07.243 [main] WARN  o.s.w.context.support.GenericWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'nebulaSecurityAuthenticationSuccessHandler': Unsatisfied dependency expressed through field 'authenticatedEventListeners'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'AuthenticatedEventListenerImpl': Unsatisfied dependency expressed through field 'loginLogVoService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginLogVoServiceImpl': Unsatisfied dependency expressed through field 'loginLogRepository'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginLogRepository': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginLogMapper' defined in file [/Users/<USER>/IdeaProject/std/crm-mdm/com.biz.crm.mdm.business.login.log/com.biz.crm.mdm.business.login.log.local/target/classes/com/biz/crm/mdm/business/login/log/local/mapper/LoginLogMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration': Bean instantiation via constructor failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$31a3b88b]: Constructor threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mybatisInterceptor': Unsatisfied dependency expressed through field 'repositoryInterceptorStrategies'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'defaultMybatisBaseFieldEnhanceInterceptor': Unsatisfied dependency expressed through field 'loginUserService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'defaultLoginUserServiceImpl': Unsatisfied dependency expressed through field 'authenticationRefreshStrategies'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dynamicTaskAuthenticationRefreshStrategy': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dictDataVoServiceImpl': Unsatisfied dependency expressed through field 'dictDataRepository'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dictDataRepository': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dictDataMapper' defined in file [/Users/<USER>/IdeaProject/std/crm-mdm/com.biz.crm.mdm.business.dictionary/com.biz.crm.mdm.business.dictionary.local/target/classes/com/biz/crm/mdm/business/dictionary/local/mapper/DictDataMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'sqlSessionFactory': Requested bean is currently in creation: Is there an unresolvable circular reference?
2025-06-24 14:45:07.350 [Thread-9] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-24 14:45:07.352 [Thread-9] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-24 14:47:11.457 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[crm-mdm] & group[dev]
2025-06-24 14:47:14.465 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[crm-mdm.yaml] & group[dev]
2025-06-24 14:47:58.328 [main] WARN  o.s.w.context.support.GenericWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'nebulaSecurityAuthenticationSuccessHandler': Unsatisfied dependency expressed through field 'authenticatedEventListeners'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'AuthenticatedEventListenerImpl': Unsatisfied dependency expressed through field 'loginLogVoService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginLogVoServiceImpl': Unsatisfied dependency expressed through field 'loginLogRepository'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginLogRepository': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginLogMapper' defined in file [/Users/<USER>/IdeaProject/std/crm-mdm/com.biz.crm.mdm.business.login.log/com.biz.crm.mdm.business.login.log.local/target/classes/com/biz/crm/mdm/business/login/log/local/mapper/LoginLogMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration': Bean instantiation via constructor failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$cba22ab0]: Constructor threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mybatisInterceptor': Unsatisfied dependency expressed through field 'repositoryInterceptorStrategies'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'defaultMybatisBaseFieldEnhanceInterceptor': Unsatisfied dependency expressed through field 'loginUserService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'defaultLoginUserServiceImpl': Unsatisfied dependency expressed through field 'authenticationRefreshStrategies'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dynamicTaskAuthenticationRefreshStrategy': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dictDataVoServiceImpl': Unsatisfied dependency expressed through field 'dictDataRepository'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dictDataRepository': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dictDataMapper' defined in file [/Users/<USER>/IdeaProject/std/crm-mdm/com.biz.crm.mdm.business.dictionary/com.biz.crm.mdm.business.dictionary.local/target/classes/com/biz/crm/mdm/business/dictionary/local/mapper/DictDataMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'sqlSessionFactory': Requested bean is currently in creation: Is there an unresolvable circular reference?
2025-06-24 14:48:02.041 [Thread-9] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-24 14:48:02.042 [Thread-9] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-24 14:49:39.016 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[crm-mdm] & group[dev]
2025-06-24 14:49:42.027 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[crm-mdm.yaml] & group[dev]
2025-06-24 14:50:26.754 [main] WARN  o.s.w.context.support.GenericWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'nebulaSecurityAuthenticationSuccessHandler': Unsatisfied dependency expressed through field 'authenticatedEventListeners'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'AuthenticatedEventListenerImpl': Unsatisfied dependency expressed through field 'loginLogVoService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginLogVoServiceImpl': Unsatisfied dependency expressed through field 'loginLogRepository'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginLogRepository': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginLogMapper' defined in file [/Users/<USER>/IdeaProject/std/crm-mdm/com.biz.crm.mdm.business.login.log/com.biz.crm.mdm.business.login.log.local/target/classes/com/biz/crm/mdm/business/login/log/local/mapper/LoginLogMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration': Bean instantiation via constructor failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$cba22ab0]: Constructor threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mybatisInterceptor': Unsatisfied dependency expressed through field 'repositoryInterceptorStrategies'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'defaultMybatisBaseFieldEnhanceInterceptor': Unsatisfied dependency expressed through field 'loginUserService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'defaultLoginUserServiceImpl': Unsatisfied dependency expressed through field 'authenticationRefreshStrategies'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dynamicTaskAuthenticationRefreshStrategy': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dictDataVoServiceImpl': Unsatisfied dependency expressed through field 'dictDataRepository'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dictDataRepository': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dictDataMapper' defined in file [/Users/<USER>/IdeaProject/std/crm-mdm/com.biz.crm.mdm.business.dictionary/com.biz.crm.mdm.business.dictionary.local/target/classes/com/biz/crm/mdm/business/dictionary/local/mapper/DictDataMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'sqlSessionFactory': Requested bean is currently in creation: Is there an unresolvable circular reference?
2025-06-24 14:50:29.550 [Thread-9] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-24 14:50:29.551 [Thread-9] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-24 14:52:40.669 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[crm-mdm] & group[dev]
2025-06-24 14:52:43.680 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[crm-mdm.yaml] & group[dev]
2025-06-24 14:53:26.699 [main] WARN  o.s.w.context.support.GenericWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'nebulaSecurityAuthenticationSuccessHandler': Unsatisfied dependency expressed through field 'authenticatedEventListeners'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'AuthenticatedEventListenerImpl': Unsatisfied dependency expressed through field 'loginLogVoService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginLogVoServiceImpl': Unsatisfied dependency expressed through field 'loginLogRepository'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginLogRepository': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginLogMapper' defined in file [/Users/<USER>/IdeaProject/std/crm-mdm/com.biz.crm.mdm.business.login.log/com.biz.crm.mdm.business.login.log.local/target/classes/com/biz/crm/mdm/business/login/log/local/mapper/LoginLogMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration': Bean instantiation via constructor failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$7a873968]: Constructor threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mybatisInterceptor': Unsatisfied dependency expressed through field 'repositoryInterceptorStrategies'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'defaultMybatisBaseFieldEnhanceInterceptor': Unsatisfied dependency expressed through field 'loginUserService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'defaultLoginUserServiceImpl': Unsatisfied dependency expressed through field 'authenticationRefreshStrategies'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dynamicTaskAuthenticationRefreshStrategy': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dictDataVoServiceImpl': Unsatisfied dependency expressed through field 'dictDataRepository'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dictDataRepository': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dictDataMapper' defined in file [/Users/<USER>/IdeaProject/std/crm-mdm/com.biz.crm.mdm.business.dictionary/com.biz.crm.mdm.business.dictionary.local/target/classes/com/biz/crm/mdm/business/dictionary/local/mapper/DictDataMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'sqlSessionFactory': Requested bean is currently in creation: Is there an unresolvable circular reference?
2025-06-24 14:53:26.814 [Thread-9] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-24 14:53:26.815 [Thread-9] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-24 14:56:42.752 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[crm-mdm] & group[dev]
2025-06-24 14:56:45.764 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[crm-mdm.yaml] & group[dev]
2025-06-24 14:57:33.655 [main] WARN  o.s.w.context.support.GenericWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'nebulaSecurityAuthenticationSuccessHandler': Unsatisfied dependency expressed through field 'authenticatedEventListeners'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'AuthenticatedEventListenerImpl': Unsatisfied dependency expressed through field 'loginLogVoService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginLogVoServiceImpl': Unsatisfied dependency expressed through field 'loginLogRepository'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginLogRepository': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginLogMapper' defined in file [/Users/<USER>/IdeaProject/std/crm-mdm/com.biz.crm.mdm.business.login.log/com.biz.crm.mdm.business.login.log.local/target/classes/com/biz/crm/mdm/business/login/log/local/mapper/LoginLogMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration': Bean instantiation via constructor failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$a4e973cb]: Constructor threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'mybatisInterceptor': Unsatisfied dependency expressed through field 'repositoryInterceptorStrategies'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'defaultMybatisBaseFieldEnhanceInterceptor': Unsatisfied dependency expressed through field 'loginUserService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'defaultLoginUserServiceImpl': Unsatisfied dependency expressed through field 'authenticationRefreshStrategies'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dynamicTaskAuthenticationRefreshStrategy': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dictDataVoServiceImpl': Unsatisfied dependency expressed through field 'dictDataRepository'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dictDataRepository': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dictDataMapper' defined in file [/Users/<USER>/IdeaProject/std/crm-mdm/com.biz.crm.mdm.business.dictionary/com.biz.crm.mdm.business.dictionary.local/target/classes/com/biz/crm/mdm/business/dictionary/local/mapper/DictDataMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'sqlSessionFactory': Requested bean is currently in creation: Is there an unresolvable circular reference?
2025-06-24 14:57:34.788 [Thread-9] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-24 14:57:34.791 [Thread-9] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
