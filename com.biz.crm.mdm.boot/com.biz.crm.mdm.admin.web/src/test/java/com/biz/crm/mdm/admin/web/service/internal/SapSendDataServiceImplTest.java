package com.biz.crm.mdm.admin.web.service.internal;
import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.mdm.admin.web.ApplicationBootstrap;
import com.biz.crm.mdm.admin.web.service.SapSendDataService;
import com.biz.crm.mdm.business.inquiry.sdk.service.InquiryVoService;
import com.biz.crm.mdm.business.inquiry.sdk.vo.InquiryVo;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialDto;
import com.biz.crm.mdm.business.org.local.service.OrgService;
import com.biz.crm.mdm.business.price.sdk.dto.FindPriceDto;
import com.biz.crm.mdm.business.price.sdk.enums.FindPriceUserTypeEnum;
import com.biz.crm.mdm.business.product.local.service.MaterialSapDataService;
import com.biz.crm.mdm.business.product.local.service.internal.MaterialSapDataServiceImpl;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaCus;
import com.biz.crm.mdm.business.terminal.local.repository.TerminalRelaCusRepository;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * @Author: haiyang
 * @Date: 2024-11-19 15:35
 * @Desc:
 */
@Slf4j
@SpringBootTest(classes = ApplicationBootstrap.class)
@WebAppConfiguration
@RunWith(SpringRunner.class)
public class SapSendDataServiceImplTest {

    @Autowired
    private SapSendDataService sapSendDataService;


    @Autowired
    private TerminalRelaCusRepository terminalRelaCusRepository;

    @Autowired
    MaterialSapDataServiceImpl materialSapDataService;

    @Autowired
    private InquiryVoService inquiryVoService;


//    @Before
//    public void setup() {
////        AuthUserDetailVo dto = new AuthUserDetailVo();
////        dto.setAccount("c011964");
////        dto.setUsername("haiyang");
////        dto.setRealName("test");
//        TestingAuthenticationToken auth = new TestingAuthenticationToken("testUser", "testPassword", "ROLE_USER");
//        SecurityContextHolder.getContext().setAuthentication(auth);
//    }

    @Test
    public void sapSendCustomerSupplierDeliveryInfo() {
        String jsonStr = "{\"CTRL\":{\"SYSID\":\"SAP\",\"REVID\":\"STD\",\"FUNID\":\"ZSTDINF001\",\"INFID\":\"\",\"UNAME\":\"C010123\",\"DATUM\":\"2024-11-19\",\"UZEIT\":\"15:16:55\",\"KEYID\":\"00********\",\"TABIX\":0,\"MSGTY\":\"\",\"MSAGE\":\"\",\"METHOD\":\"\",\"PAGE_SIZE\":0,\"PAGE_NO\":0,\"MD5\":\"\"},\"DATA\":[{\"ZBUTYPE\":\"C\",\"PARTNER\":\"********\",\"NAME1\":\"安徽君禄商贸有限公司\",\"NAME2\":\"\",\"BU_SORT2\":\"\",\"BUGROUP\":\"Z002\",\"XBLCK\":\"\",\"SPERR\":\"\",\"NAME_CO\":\"蔡大涛\",\"STREET\":\"安徽省合肥市肥东县龙塘浙商城食品城16栋121-122号\",\"REGIO\":\"110\",\"REGIO_T\":\"安徽省\",\"ORT01\":\"合肥市\",\"ORT02\":\"肥东县\",\"TELF1\":\"***********\",\"AUFSD\":\"\",\"KATR3\":\"1\",\"KATR5\":\"8\",\"ZXQYQ\":\"\",\"ZKHQD\":\"\",\"VTEXT\":\"经销商\",\"LIQUID_DAT\":\"2024-09-12\",\"RISK_CLASS\":\"B\",\"VBUND\":\"\",\"NOT_RELEASED\":\"\",\"TAXNUMXL\":\"\",\"ERDAT\":\"2023-09-25\",\"IT_BUKRS\":[{\"PARTNER\":\"********\",\"BUKRS\":\"6000\",\"VBUND\":\"********\",\"AKONT\":\"**********\",\"NODEL\":\"\",\"SPERR\":\"\"}],\"IT_BANKS\":[{\"PARTNER\":\"00********\",\"BANKL\":\"************\",\"BANKN\":\"6228480669232252975\",\"BKREF\":\"5\",\"BANKA\":\"中国农业银行股份有限公司合肥繁华支行\",\"KOINH\":\"李群\",\"BKVFM\":**************,\"BKVTO\":**************,\"KOVON\":\"2023-10-07\",\"KOBIS\":\"9999-12-31\",\"BKVID\":\"0002\"},{\"PARTNER\":\"00********\",\"BANKL\":\"************\",\"BANKN\":\"*****************\",\"BKREF\":\"\",\"BANKA\":\"中国农业银行股份有限公司合肥龙塘支行\",\"KOINH\":\"安徽君禄商贸有限公司\",\"BKVFM\":**************,\"BKVTO\":**************,\"KOVON\":\"2023-09-25\",\"KOBIS\":\"9999-12-31\",\"BKVID\":\"0001\"}],\"IT_KNVV\":[{\"PARTNER\":\"********\",\"KUNNR\":\"00********\",\"VKORG\":\"6000\",\"VKORGT\":\"杭州生物销售组织\",\"SPART\":\"00\",\"SPART_T\":\"通用产品组\",\"VTWEG\":\"00\",\"VTWEG_T\":\"通用分销渠道\",\"KONDA\":\"01\",\"KONDA_T\":\"大宗买主\",\"VKBUR\":\"Y075\",\"VKBURT\":\"低温新零售运营部\",\"VKGRP\":\"W12\",\"VKGRPT\":\"南京区域低温业务组\",\"AUFSD\":\"10\",\"KVGR3\":\"\",\"KVGR5\":\"A01\",\"KVGR5T\":\"合同到期冻结\",\"PERNR\":990179,\"RUFNM\":\"c010888\",\"NACHN\":\"付\",\"VORNA\":\"启文\",\"KVGR3T\":\"非寄售\"}],\"IT_KNVP\":[{\"PARTNER\":\"********\",\"KUNNR\":\"********\",\"VKORG\":\"6000\",\"VKORGT\":\"杭州生物销售组织\",\"VTWEG\":\"00\",\"VTWEG_T\":\"通用分销渠道\",\"SPART\":\"00\",\"SPART_T\":\"通用产品组\",\"PARVW\":\"SE\",\"PARZA\":0,\"KUNN2\":\"\",\"LIFNR\":\"\",\"PERNR\":990179,\"RUFNM\":\"c010888\"},{\"PARTNER\":\"********\",\"KUNNR\":\"********\",\"VKORG\":\"6000\",\"VKORGT\":\"杭州生物销售组织\",\"VTWEG\":\"00\",\"VTWEG_T\":\"通用分销渠道\",\"SPART\":\"00\",\"SPART_T\":\"通用产品组\",\"PARVW\":\"SH\",\"PARZA\":0,\"KUNN2\":\"********\",\"LIFNR\":\"\",\"PERNR\":0,\"RUFNM\":\"\"},{\"PARTNER\":\"********\",\"KUNNR\":\"********\",\"VKORG\":\"6000\",\"VKORGT\":\"杭州生物销售组织\",\"VTWEG\":\"00\",\"VTWEG_T\":\"通用分销渠道\",\"SPART\":\"00\",\"SPART_T\":\"通用产品组\",\"PARVW\":\"BP\",\"PARZA\":0,\"KUNN2\":\"********\",\"LIFNR\":\"\",\"PERNR\":0,\"RUFNM\":\"\"},{\"PARTNER\":\"********\",\"KUNNR\":\"********\",\"VKORG\":\"6000\",\"VKORGT\":\"杭州生物销售组织\",\"VTWEG\":\"00\",\"VTWEG_T\":\"通用分销渠道\",\"SPART\":\"00\",\"SPART_T\":\"通用产品组\",\"PARVW\":\"PY\",\"PARZA\":0,\"KUNN2\":\"********\",\"LIFNR\":\"\",\"PERNR\":0,\"RUFNM\":\"\"},{\"PARTNER\":\"********\",\"KUNNR\":\"********\",\"VKORG\":\"6000\",\"VKORGT\":\"杭州生物销售组织\",\"VTWEG\":\"00\",\"VTWEG_T\":\"通用分销渠道\",\"SPART\":\"00\",\"SPART_T\":\"通用产品组\",\"PARVW\":\"SP\",\"PARZA\":0,\"KUNN2\":\"********\",\"LIFNR\":\"\",\"PERNR\":0,\"RUFNM\":\"\"},{\"PARTNER\":\"********\",\"KUNNR\":\"********\",\"VKORG\":\"6000\",\"VKORGT\":\"杭州生物销售组织\",\"VTWEG\":\"00\",\"VTWEG_T\":\"通用分销渠道\",\"SPART\":\"00\",\"SPART_T\":\"通用产品组\",\"PARVW\":\"SH\",\"PARZA\":1,\"KUNN2\":\"50005243\",\"LIFNR\":\"\",\"PERNR\":0,\"RUFNM\":\"\"}]}]}";
        JSONObject json = JSONObject.parseObject(jsonStr);
        sapSendDataService.sapSendCustomerSupplierDeliveryInfo(json);
    }


    @Test
    public void testSearch() {
        List<TerminalRelaCus> list = terminalRelaCusRepository.findByTerminalCustomers(Lists.newArrayList(), "泽林");
        log.info("list: {}", JSON.toJSONString(list));
    }

    @Test
    public void testSearch2() {
        List<MaterialDto> dtoList = Lists.newArrayList();
        MaterialDto dto = new MaterialDto();
        dto.setId(UuidCrmUtil.general());
        dto.setMaterialCode("110201001376");
        dto.setUnitTypeCode("110201001376");
        dto.setMaterialName("200钻草莓酸10入-6提");
        dto.setSimpleName("200钻草莓酸10入-6提");
        dto.setFourLevelCode("************");
        dto.setFourLevelName("200草莓酸");
        dto.setConversionValue("6");
        dto.setMaterialType("ZERT");
        dto.setStandardUnit("CS");
//        dto.setMaterialGroupCode(vo.getMATKL());
        dto.setMaterialGroupName("酸牛奶-利乐钻200g");
        dto.setFinanceIndex("");
        dto.setBarCode("6978178910435");
        dto.setGrossWeight("15.16");
        dto.setNetWeight("12");
        dto.setWeightUnit("kg");
        dto.setCapacity("0.05258");
        dto.setLongMetre("0.552");
        dto.setWideMetre("0.378");
        dto.setHighMetre("0.252");

        dto.setProductSmallClassCode("CPXL20250225003");
        dto.setProductSmallClassName("200钻草莓酸10入");
        dto.setProductPhaseCode("YN4067");
        dto.setProductPhaseName("200草莓酸");


        dtoList.add(dto);

//        materialSapDataService.saveOrUpdateMaterialDtoForProductPhase(dtoList, null);
    }

    @Test
    public void testPrice() {
        FindPriceDto findPriceDto = new FindPriceDto();
        findPriceDto.setUserType(FindPriceUserTypeEnum.CUSTOMER.getDictCode());
        findPriceDto.setUserCode("1000675360000000");
        findPriceDto.setPriceTypeCode(Sets.newHashSet("DRQ001"));
        findPriceDto.setProductCodeSet(Sets.newHashSet("110201001157"));
        Map<String, InquiryVo> price = inquiryVoService.findPrice((JSONObject) JSONObject.toJSON(findPriceDto));
        log.info("price: {}", JSON.toJSONString(price));
    }
}