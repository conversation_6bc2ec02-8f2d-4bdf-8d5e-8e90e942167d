server:
  port: 31201
spring:
  profiles:
    active: dev
  cloud:
    config:
      #本地配置文件允许覆盖远程配置文件
      override-none: false
    nacos:
      server-addr: mse-a9abc970-nacos-ans.mse.aliyuncs.com:8848
      config:
        server-addr: ${spring.cloud.nacos.server-addr} #Nacos作为配置中心地址
        file-extension: yaml #指定yaml格式的配置
        group: ${spring.profiles.active}
        namespace: f5ab2dcd-e6a5-4a26-b4f4-dd2cb68ad119
        access-key: LTAI5tRSzjqre5uzrt7mYHKa
        secret-key: ******************************
