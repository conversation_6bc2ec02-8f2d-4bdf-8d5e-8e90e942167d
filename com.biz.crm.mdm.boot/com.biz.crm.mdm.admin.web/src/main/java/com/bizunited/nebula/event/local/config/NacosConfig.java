package com.bizunited.nebula.event.local.config;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;


/**
 * 覆盖产品组方法
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2023-06-15 17:57
 */
@Configuration
@Slf4j
public class NacosConfig {

    @Autowired(required = false)
    private NacosDiscoveryProperties nacosDiscoveryProperties;

    @Bean
    @ConditionalOnMissingBean
    public ConfigService getConfigService() throws NacosException {
        Properties properties = new Properties();
        log.info("=====>    nacos加载 start    <=====");
        properties.put("serverAddr", nacosDiscoveryProperties.getServerAddr());
        log.info("=====>    nacos加载 地址[{}]    <=====", nacosDiscoveryProperties.getServerAddr());
        if (StringUtils.isNotBlank(nacosDiscoveryProperties.getNamespace())) {
            properties.put("namespace", nacosDiscoveryProperties.getNamespace());
            log.info("=====>    nacos加载 namespace[{}]    <=====", nacosDiscoveryProperties.getNamespace());
        }
        if (StringUtils.isNotBlank(nacosDiscoveryProperties.getGroup())) {
            properties.put("group", nacosDiscoveryProperties.getGroup());
            log.info("=====>    nacos加载 group[{}]    <=====", nacosDiscoveryProperties.getGroup());
        }
        if (StringUtils.isNotBlank(nacosDiscoveryProperties.getClusterName())) {
            properties.put("clusterName", nacosDiscoveryProperties.getClusterName());
            log.info("=====>    nacos加载 clusterName[{}]    <=====", nacosDiscoveryProperties.getClusterName());
        }
        if (StringUtils.isNotBlank(nacosDiscoveryProperties.getAccessKey())) {
            properties.put("accessKey", nacosDiscoveryProperties.getAccessKey());
            log.info("=====>    nacos加载 有accessKey    <=====");
        }
        if (StringUtils.isNotBlank(nacosDiscoveryProperties.getSecretKey())) {
            properties.put("secretKey", nacosDiscoveryProperties.getSecretKey());
            log.info("=====>    nacos加载 有secretKey    <=====");
        }
        if (StringUtils.isNotBlank(nacosDiscoveryProperties.getUsername())) {
            properties.put("username", nacosDiscoveryProperties.getUsername());
            log.info("=====>    nacos加载 有账号    <=====");
        }
        if (StringUtils.isNotBlank(nacosDiscoveryProperties.getPassword())) {
            properties.put("password", nacosDiscoveryProperties.getPassword());
            log.info("=====>    nacos加载 有密码    <=====");
        }

        log.info("=====>    nacos加载 end    <=====");
        return NacosFactory.createConfigService(properties);
    }
}
