package com.biz.crm.mdm.admin.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 功能迁移文件生成dto
 * @date 2023/12/27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "功能迁移文件生成dto", description = "功能迁移文件生成dto")
public class CompetenceTransferFileDto {

  /**
   * 功能菜单编码
   */
  @ApiModelProperty("功能菜单编码")
  private String competenceCode;

  /**
   * 列表编码集合
   */
  @ApiModelProperty("列表编码集合")
  private Set<String> functionCodes;

  /**
   * 数据字典类型集合
   */
  @ApiModelProperty("数据字典类型集合")
  private Set<String> dictTypeCodes;
}
