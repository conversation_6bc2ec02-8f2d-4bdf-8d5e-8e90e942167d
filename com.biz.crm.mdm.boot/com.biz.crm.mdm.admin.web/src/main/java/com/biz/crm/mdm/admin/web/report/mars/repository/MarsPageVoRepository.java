package com.biz.crm.mdm.admin.web.report.mars.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.mars.dto.MarsPageDto;
import com.biz.crm.mdm.admin.web.report.mars.mapper.MarsPageVoMapper;
import com.biz.crm.mdm.admin.web.report.mars.vo.MarsPageVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 数据视图分页Repository
 * @date 2023/11/21
 */
@Component
public class MarsPageVoRepository {

  @Autowired
  private MarsPageVoMapper marsPageVoMapper;

  /**
   * 数据权限分页列表
   *
   * @param dto      请求参数dto
   * @param pageable 分页信息
   * @return Page<PositionPageVo> 数据权限分页列表
   */
  public Page<MarsPageVo> findByMarsPageDto(Pageable pageable, MarsPageDto dto) {
    Page<MarsPageVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return this.marsPageVoMapper.findByMarsPageDto(page, dto);
  }
}
