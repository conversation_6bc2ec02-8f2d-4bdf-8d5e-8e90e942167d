package com.biz.crm.mdm.admin.web.notifier;

import com.biz.crm.mdm.business.user.sdk.constant.UserConstant;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.security.sdk.config.SimpleSecurityProperties;
import com.bizunited.nebula.security.sdk.event.AuthenticatedEventListener;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import com.bizunited.nebula.security.sdk.loginform.LoginDetails;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 描述：</br>登录失败的安全保障功能
 *
 * <AUTHOR>
 * @date 2022/10/17
 */
@Slf4j
@Component
public class LoginFailedAuthenticatedEventListener implements AuthenticatedEventListener {

  /**
   * 锁定时间
   */
  @Value("${security.login.lockTime:10m}")
  private Duration lockTime;

  @Autowired(required = false)
  private RedisMutexService redisMutexService;

  @Autowired(required = false)
  private StringRedisTemplate stringRedisTemplate;

  @Autowired(required = false)
  private SimpleSecurityProperties simpleSecurityProperties;

  @Override
  public void onAuthenticationSuccess(UserIdentity userIdentity, Authentication authentication) {
    if (StringUtils.isBlank(userIdentity.getAccount())) {
      return;
    }
    Integer type = userIdentity.getLoginType();
    if (type == null) {
      type = this.simpleSecurityProperties.getDefaultLoginType();
    }
    String account = StringUtils.join(type, "_", userIdentity.getAccount());
    String key = String.format(UserConstant.LOGIN_LOCK_KEY, TenantUtils.getTenantCode(), account);
    String redisKey = String.format(UserConstant.LOGIN_FAILED_KEY, TenantUtils.getTenantCode(), account);
    try {
      this.redisMutexService.lock(key);
      String loginFailedTimeStr = this.stringRedisTemplate.opsForValue().get(redisKey);
      if (StringUtils.isNotBlank(loginFailedTimeStr)) {
        this.stringRedisTemplate.delete(redisKey);
      }
    } catch (Exception ex) {
      log.error(ex.getMessage(), ex);
      throw new RuntimeException(ex.getMessage(), ex);
    } finally {
      if (this.redisMutexService.islock(key)) {
        this.redisMutexService.unlock(key);
      }
    }
  }

  @Override
  public void onAuthenticationFailed(AuthenticationException exception) {

  }

  @Override
  public void onAuthenticationFailed(LoginDetails loginDetails, AuthenticationException e) {
    /*
     * 1、检查登录账号
     * 2、通过账号获取登录错误次数数据
     */

    // 通过登录错误传递错误的account
    if (StringUtils.isBlank(loginDetails.getAccount())) {
      return;
    }
    Integer type = loginDetails.getType();
    if (type == null) {
      type = this.simpleSecurityProperties.getDefaultLoginType();
    }
    String account = StringUtils.join(type, "_", loginDetails.getAccount());
    String key = String.format(UserConstant.LOGIN_LOCK_KEY, TenantUtils.getTenantCode(), account);
    String redisKey = String.format(UserConstant.LOGIN_FAILED_KEY, TenantUtils.getTenantCode(), account);
    try {
      this.redisMutexService.lock(key);
      String loginFailedTimeStr = this.stringRedisTemplate.opsForValue().get(redisKey);
      if (StringUtils.isBlank(loginFailedTimeStr)) {
        loginFailedTimeStr = "1";
      } else {
        loginFailedTimeStr = Integer.toString(Integer.parseInt(loginFailedTimeStr) + 1);
      }
      this.stringRedisTemplate.opsForValue().set(redisKey, loginFailedTimeStr, lockTime.getSeconds(), TimeUnit.SECONDS);
    } catch (Exception ex) {
      log.error(ex.getMessage(), ex);
      throw new RuntimeException(ex.getMessage(), ex);
    } finally {
      if (this.redisMutexService.islock(key)) {
        this.redisMutexService.unlock(key);
      }
    }
  }
}
