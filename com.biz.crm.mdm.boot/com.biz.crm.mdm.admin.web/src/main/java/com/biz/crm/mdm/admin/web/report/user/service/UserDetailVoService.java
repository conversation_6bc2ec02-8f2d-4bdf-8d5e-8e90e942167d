package com.biz.crm.mdm.admin.web.report.user.service;

import com.biz.crm.mdm.admin.web.report.user.dto.UserDetailDto;
import com.biz.crm.mdm.admin.web.report.user.vo.UserDetailVo;

import java.util.List;

/**
 * 用户详细信息服务接口
 *
 * <AUTHOR>
 * @since 2021-11-04 17:38:46
 */
public interface UserDetailVoService {

  /**
   * 分页条件查询
   *
   * @param dto
   * @return
   */
  List<UserDetailVo> findByUserName(UserDetailDto dto);

  /**
   * 查询当前登录用户信息
   *
   * @return
   */
  UserDetailVo findDetailByLoginUser();

}
