package com.biz.crm.mdm.admin.web.imports.costcenter.service;

import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.admin.web.imports.costcenter.model.CostCenterOrgImportVo;
import com.biz.crm.mdm.admin.web.imports.customer.model.CustomerCrmImportVo;
import com.biz.crm.mdm.business.cost.local.entity.MdmCostCenter;
import com.biz.crm.mdm.business.cost.local.entity.MdmCostCenterOrg;
import com.biz.crm.mdm.business.cost.local.repository.MdmCostCenterOrgRepository;
import com.biz.crm.mdm.business.cost.local.repository.MdmCostCenterRepository;
import com.biz.crm.mdm.business.cost.local.service.MdmCostCenterService;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/30 11:48
 **/
@Component
public class CostCenterOrgImportProcess implements ImportProcess<CostCenterOrgImportVo> {

    @Resource
    private MdmCostCenterOrgRepository costCenterOrgRepository;

    @Resource
    private MdmCostCenterRepository mdmCostCenterRepository;

    @Resource
    private OrgVoService orgVoService;

    @Resource
    private MdmCostCenterService mdmCostCenterService;


    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, CostCenterOrgImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        List<CostCenterOrgImportVo> dataList = Lists.newArrayList();

        for (Map.Entry<Integer, CostCenterOrgImportVo> entry : data.entrySet()) {
            CostCenterOrgImportVo importVo = entry.getValue();
            importVo.setIndex(entry.getKey());
            dataList.add(importVo);
        }
        List<String> orgCodes = dataList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getOrgCode())).map(x -> x.getOrgCode()).distinct().collect(Collectors.toList());
        List<String> costCenterCodes = dataList.stream().filter(x -> ObjectUtils.isNotEmpty(x.getCostCenterCode())).map(x -> x.getCostCenterCode()).distinct().collect(Collectors.toList());

        List<MdmCostCenter> mdmCostCenters = mdmCostCenterService.findByCodes(costCenterCodes);
        Map<String, MdmCostCenter> costCenterMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(mdmCostCenters)) {
            costCenterMap = mdmCostCenters.stream().collect(Collectors.toMap(x -> x.getCostCenterCode(), Function.identity()));
        }
        List<OrgVo> orgVoList = orgVoService.findByOrgCodes(orgCodes);
        Map<String, OrgVo> orgMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(orgVoList)) {
            orgMap = orgVoList.stream().collect(Collectors.toMap(x -> x.getOrgCode(), Function.identity()));
        }

        Map<Integer, String> errMap = Maps.newHashMap();
        for (CostCenterOrgImportVo importVo : dataList) {
            StringJoiner errMsg = new StringJoiner(";");
            if (ObjectUtils.isEmpty(importVo.getCostCenterCode())) {
                errMsg.add("成本中心编码不能为空");
            } else {
                if (!costCenterMap.containsKey(importVo.getCostCenterCode())) {
                    errMsg.add("成本中心编码错误");
                }
            }
            if (ObjectUtils.isEmpty(importVo.getOrgCode())) {
                errMsg.add("组织编码不能为空");
            } else {
                if (!orgMap.containsKey(importVo.getOrgCode())) {
                    errMsg.add("组织编码错误");
                }
            }
            if (ObjectUtils.isNotEmpty(errMsg.toString())) {
                errMap.put(importVo.getIndex(), errMsg.toString());
            }
        }
        Map<String, List<CostCenterOrgImportVo>> costCenterOrgMap = dataList.stream().collect(Collectors.groupingBy(x -> x.getCostCenterCode()));
        for (Map.Entry<String, List<CostCenterOrgImportVo>> entry : costCenterOrgMap.entrySet()) {
            if (entry.getValue().size() > 1) {
                for (CostCenterOrgImportVo importVo : entry.getValue()) {
                    StringJoiner errMsg = new StringJoiner(";");
                    if (errMap.containsKey(importVo.getIndex())) {
                        errMsg.add(errMap.get(importVo.getIndex()));
                    }
                    errMsg.add("成本中心只能对应一个组织");
                    errMap.put(importVo.getIndex(), errMsg.toString());
                }
            }
        }
        if (ObjectUtils.isEmpty(errMap) || errMap.isEmpty()) {
            //删除成本中心对应的组织关系
            costCenterOrgRepository.deleteByCostCenterCodes(costCenterCodes);
            List<MdmCostCenterOrg> costCenterOrgList = Lists.newArrayList();
            for (CostCenterOrgImportVo importVo : dataList) {
                MdmCostCenterOrg mdmCostCenterOrg = new MdmCostCenterOrg();
                mdmCostCenterOrg.setCostCenterCode(importVo.getCostCenterCode());
                mdmCostCenterOrg.setOrgCode(importVo.getOrgCode());
                mdmCostCenterOrg.setTenantCode(TenantUtils.getTenantCode());
                costCenterOrgList.add(mdmCostCenterOrg);
            }
            costCenterOrgRepository.saveBatch(costCenterOrgList);
        }
        return errMap;
    }

    @Override
    public Class<CostCenterOrgImportVo> findCrmExcelVoClass() {
        return CostCenterOrgImportVo.class;
    }

    @Override
    public String getTemplateCode() {
        return "MDM_COST_CENTER_ORG_IMPORT";
    }

    @Override
    public String getTemplateName() {
        return "成本中心组织导入";
    }
}
