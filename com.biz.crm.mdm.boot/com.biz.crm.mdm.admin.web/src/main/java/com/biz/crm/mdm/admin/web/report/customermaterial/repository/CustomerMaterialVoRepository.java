package com.biz.crm.mdm.admin.web.report.customermaterial.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.customermaterial.mapper.CustomerMaterialVoMapper;
import com.biz.crm.mdm.admin.web.report.customermaterial.vo.CustomerMaterialVo;
import com.biz.crm.mdm.business.customer.material.sdk.dto.CustomerMaterialDto;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 客户物料VO 数据操作类
 */
@Component
public class CustomerMaterialVoRepository {

  @Autowired(required = false)
  private CustomerMaterialVoMapper customerMaterialVoMapper;

  /**
   * 分页条件查询列表
   * @param page 分页参数
   * @param dto 客户物料查询参数
   * @return 客户物料实体
   */
  public Page<CustomerMaterialVo> findByConditions(Page<CustomerMaterialVo> page,
                                                   CustomerMaterialDto dto) {
    dto.setTenantCode(TenantUtils.getTenantCode());
    return this.customerMaterialVoMapper.findByConditions(page,dto);
  }
}
