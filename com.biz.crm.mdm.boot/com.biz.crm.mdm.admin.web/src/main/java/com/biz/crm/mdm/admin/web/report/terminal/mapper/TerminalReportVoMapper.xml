<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.admin.web.report.terminal.mapper.TerminalReportVoMapper">

    <sql id="terminalPageSelectSql">
        a.id,
    a.enable_status,
    a.terminal_code,
    a.terminal_name,
    a.terminal_type,
    a.channel,
    a.license_person_name,
    a.license_register_number,
    a.license_firm_name,
    a.terminal_address,
    a.shop_image_path,
    a.province_code,
    a.province_name,
    a.city_code,
    a.city_name,
    a.district_code,
    a.district_name,
    a.longitude,
    a.latitude
    </sql>

    <sql id="baseTableSql">
        mdm_terminal
    a
    left join mdm_terminal_r_org b on a.tenant_code=b.tenant_code and a.terminal_code =
    b.terminal_code
    left join mdm_terminal_r_customer_org c on a.tenant_code=c.tenant_code and a.terminal_code =
    c.terminal_code
    left join mdm_org b1 on b1.tenant_code=b.tenant_code and b1.org_code = b.org_code
    left join mdm_customer_org c1 on c.tenant_code=c1.tenant_code and c.org_code = c1.customer_org_code
    </sql>

    <select id="findByConditions"
            resultType="com.biz.crm.mdm.admin.web.report.terminal.vo.TerminalReportVo">
        select
        a.id,
        a.terminal_code,
        a.terminal_name,
        a.terminal_type,
        a.channel,
        c.org_name,
        a.license_person_name,
        a.license_register_number,
        a.license_firm_name,
        a.enable_status,
        a.province_name,
        a.city_name,
        a.district_name,
        a.process_number,
        a.longitude
        from mdm_terminal a
        left join mdm_terminal_r_org b
        on a.tenant_code = b.tenant_code and a.terminal_code = b.terminal_code
        left join mdm_org c on c.tenant_code = b.tenant_code and b.org_code = c.org_code
        where 1 = 1
        <if test="dto.tenantCode!=null and dto.tenantCode!=''">
            and a.tenant_code = #{dto.tenantCode}
        </if>
        <if test="dto.delFlag!=null and dto.delFlag!=''">
            and a.del_flag = #{dto.delFlag}
        </if>
        <if test="dto.terminalCode!=null and dto.terminalCode!=''">
            <bind name="terminalCodeLike" value="'%'+dto.terminalCode+'%'"/>
            and a.terminal_code like #{terminalCodeLike}
        </if>
        <if test="dto.terminalName!=null and dto.terminalName!=''">
            <bind name="terminalNameLike" value="'%'+dto.terminalName+'%'"/>
            and a.terminal_name like #{terminalNameLike}
        </if>
        <if test="dto.terminalType!=null and dto.terminalType!=''">
            and a.terminal_type = #{dto.terminalType}
        </if>
        <if test="dto.channel!=null and dto.channel!=''">
            and a.channel = #{dto.channel}
        </if>
        <if test="dto.enableStatus!=null and dto.enableStatus!=''">
            and a.enable_status = #{dto.enableStatus}
        </if>
        <if test="dto.provinceName!=null and dto.provinceName!=''">
            <bind name="provinceNameLike" value="'%'+dto.provinceName+'%'"/>
            and a.province_name like #{provinceNameLike}
        </if>
        <if test="dto.cityName!=null and dto.cityName!=''">
            <bind name="cityNameLike" value="'%'+dto.cityName+'%'"/>
            and a.city_name like #{cityNameLike}
        </if>
        <if test="dto.districtName!=null and dto.districtName!=''">
            <bind name="districtNameLike" value="'%'+dto.districtName+'%'"/>
            and a.district_name like #{districtNameLike}
        </if>
        <if test="dto.orgCode!=null and dto.orgCode!=''">
            and c.org_code = #{dto.orgCode}
        </if>
        order by a.create_time desc
    </select>

    <select id="findByRelaCurTerminalDto"
            resultType="com.biz.crm.mdm.admin.web.report.terminal.vo.TerminalReportVo">
        select
        <include refid="terminalPageSelectSql"></include>
        from
        mdm_terminal a
        where
        1=1
        <if test="dto.tenantCode != null and dto.tenantCode != ''">
            and a.tenant_code=#{dto.tenantCode}
        </if>
        <if test="dto.terminalType != null and dto.terminalType != ''">
            and a.terminal_type=#{dto.terminalType}
        </if>
        <if test="dto.terminalCode != null and dto.terminalCode != ''">
            <bind name="likeTerminalCode" value="'%' + dto.terminalCode + '%'"/>
            and a.terminal_code like #{likeTerminalCode}
        </if>
        <if test="dto.terminalName != null and dto.terminalName != ''">
            <bind name="likeTerminalName" value="'%' + dto.terminalName + '%'"/>
            and a.terminal_name like #{likeTerminalName}
        </if>
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and a.enable_status=#{dto.enableStatus}
        </if>
        <if test="dto.delFlag != null and dto.delFlag != ''">
            and a.del_flag=#{dto.delFlag}
        </if>
        <if test="dto.customerCode!=null and dto.customerCode!=''">
            and exists (
            select 1 from mdm_terminal_rela_cus d
            where a.terminal_code = d.terminal_code
            and d.cus_code = #{dto.customerCode}
            )
        </if>
        <if test="dto.userName!=null and dto.userName!=''">
            and exists(
            select 1
            from mdm_terminal_rela_business d
            where a.terminal_code = d.terminal_code
            and a.tenant_code = d.tenant_code
            and d.position_code in (
            select c.position_code
            from mdm_user_position c
            where c.user_name = #{dto.userName}
            )
            )
        </if>
        order by
        <if test="dto.selectedCode != null and dto.selectedCode.size > 0">
            CASE
            <foreach collection="dto.selectedCode" item="item" index="index">
                WHEN a.terminal_code = #{item} THEN ${index}
            </foreach>
            ELSE -1 END desc,
        </if>
        a.create_time desc,a.id desc
    </select>

    <select id="findByNoRelaCurTerminalDto"
            resultType="com.biz.crm.mdm.admin.web.report.terminal.vo.TerminalReportVo">
        select
        <include refid="terminalPageSelectSql"></include>
        from
        mdm_terminal a
        where 1=1
        <if test="dto.tenantCode != null and dto.tenantCode != ''">
            and a.tenant_code=#{dto.tenantCode}
        </if>
        <if test="dto.terminalType != null and dto.terminalType != ''">
            and a.terminal_type=#{dto.terminalType}
        </if>
        <if test="dto.terminalCode != null and dto.terminalCode != ''">
            <bind name="likeTerminalCode" value="'%' + dto.terminalCode + '%'"/>
            and a.terminal_code like #{likeTerminalCode}
        </if>
        <if test="dto.terminalName != null and dto.terminalName != ''">
            <bind name="likeTerminalName" value="'%' + dto.terminalName + '%'"/>
            and a.terminal_name like #{likeTerminalName}
        </if>
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and a.enable_status=#{dto.enableStatus}
        </if>
        <if test="dto.delFlag != null and dto.delFlag != ''">
            and a.del_flag=#{dto.delFlag}
        </if>
        <if test="dto.customerCode!=null and dto.customerCode!=''">
            and not exists(
            select 1 from mdm_terminal_rela_cus d1
            where a.terminal_code = d1.terminal_code
            and d1.cus_code = #{dto.customerCode}
            )
        </if>
        <if test="dto.userName!=null and dto.userName!=''">
            and not exists(
            select 1
            from mdm_terminal_rela_business d
            where a.terminal_code = d.terminal_code
            and a.tenant_code = d.tenant_code
            and d.position_code in (
            select c.position_code
            from mdm_user_position c
            where c.user_name = #{dto.userName}
            )
            )
        </if>
        order by a.create_time desc, a.id desc
    </select>

    <select id="findByNoRelaTerminalDto"
            resultType="com.biz.crm.mdm.admin.web.report.terminal.vo.TerminalReportVo">
        select
        <include refid="terminalPageSelectSql"></include>
        from
        mdm_terminal a
        where
        1=1
        <if test="dto.delFlag != null and dto.delFlag != ''">
            and a.del_flag=#{dto.delFlag}
        </if>
        <if test="dto.tenantCode != null and dto.tenantCode != ''">
            and a.tenant_code=#{dto.tenantCode}
        </if>
        <if test="dto.terminalType != null and dto.terminalType != ''">
            and a.terminal_type=#{dto.terminalType}
        </if>
        <if test="dto.terminalCode != null and dto.terminalCode != ''">
            <bind name="likeTerminalCode" value="'%' + dto.terminalCode + '%'"/>
            and a.terminal_code like #{likeTerminalCode}
        </if>
        <if test="dto.terminalName != null and dto.terminalName != ''">
            <bind name="likeTerminalName" value="'%' + dto.terminalName + '%'"/>
            and a.terminal_name like #{likeTerminalName}
        </if>
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and a.enable_status=#{dto.enableStatus}
        </if>
        <if test="dto.customerCode!=null and dto.customerCode!=''">
            and not exists (
            select 1 from mdm_terminal_rela_cus d
            where a.terminal_code = d.terminal_code
            and d.cus_code > ''
            )
        </if>

        <if test="dto.userName!=null and dto.userName!=''">
            and not exists(
            select 1
            from mdm_terminal_rela_business d
            where a.terminal_code = d.terminal_code
            and a.tenant_code = d.tenant_code
            and d.position_code in (
            select c.position_code
            from mdm_user_position c
            where c.user_name > ''
            )
            )
        </if>
        order by a.create_time desc,a.id desc
    </select>

    <select id="findByTerminalCodes"
            resultType="com.biz.crm.mdm.admin.web.report.terminal.vo.TerminalReportVo">
        SELECT
        b.terminal_code,
        b.position_code,
        c.position_name
        from mdm_terminal_supply b
        LEFT JOIN mdm_position c on b.position_code = c.position_code AND b.tenant_code = c.tenant_code
        where c.del_flag=#{delCode} AND b.tenant_code=#{tenantCode}
        and b.terminal_code in
        (<foreach collection="list" item="item" separator=",">#{item}</foreach>)
    </select>

    <select id="findTerminalRelaOrgListByTerminalCodes"
            resultType="com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaOrg">
        select a.terminal_code,a.org_code,b.org_name from mdm_terminal_r_org a
        left join mdm_org b on a.tenant_code=b.tenant_code and a.org_code = b.org_code
        where a.tenant_code=#{tenantCode}
        and a.terminal_code in(<foreach collection="list" item="item" separator=",">#{item}</foreach>)
    </select>
    <select id="findTerminalRelaCustomerOrgListByTerminalCodes"
            resultType="com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaCustomerOrg">
        select a.terminal_code,a.org_code,b.customer_org_name as org_name from mdm_terminal_r_customer_org a
        left join mdm_customer_org b on a.tenant_code=b.tenant_code and a.org_code = b.customer_org_code
        where a.tenant_code=#{tenantCode}
        and a.terminal_code in(<foreach collection="list" item="item" separator=",">#{item}</foreach>)
    </select>
    <select id="findChildrenPageByTerminalClientDto"
            resultType="com.biz.crm.mdm.business.terminal.sdk.vo.TerminalClientVo">
        SELECT
        e.*
        FROM
        mdm_user_position mup
        LEFT JOIN mdm_position b ON ( mup.position_code = b.position_code AND mup.tenant_code = b.tenant_code )
        LEFT JOIN mdm_position c ON ( c.rule_code LIKE concat( b.rule_code, '%' ) AND c.tenant_code = b.tenant_code )
        LEFT JOIN mdm_terminal_supply d ON ( d.position_code = c.position_code AND c.tenant_code = d.tenant_code )
        LEFT JOIN mdm_terminal e ON ( e.terminal_code = d.terminal_code AND e.tenant_code = d.tenant_code )
        WHERE mup.user_name = #{dto.userName}
        AND e.tenant_code = #{dto.tenantCode}
        AND e.del_flag = '${@<EMAIL>()}'
        AND e.enable_status = '${@<EMAIL>()}'
        <if test="dto.terminalCode != null and dto.terminalCode != ''">
            <bind name="likeTerminalCode" value="'%' + dto.terminalCode + '%'"/>
            and e.terminal_code like #{likeTerminalCode}
        </if>
        <if test="dto.terminalName != null and dto.terminalName != ''">
            <bind name="likeTerminalName" value="'%' + dto.terminalName + '%'"/>
            and e.terminal_name like #{likeTerminalName}
        </if>
        <if test="dto.keyWord != null and dto.keyWord != ''">
            <bind name="likekeyWord" value="'%' + dto.keyWord + '%'"/>
            and e.terminal_name like #{likekeyWord} or e.terminal_code like #{likekeyWord}
        </if>
        <if test="dto.terminalType != null and dto.terminalType != ''">
            and e.terminal_type = #{dto.terminalType}
        </if>
        GROUP BY
        e.id
        ORDER BY
        e.create_time DESC
    </select>

    <select id="findPageByTerminalClientDto" resultType="com.biz.crm.mdm.business.terminal.sdk.vo.TerminalClientVo">
        SELECT
        a.id,
        a.terminal_code,
        a.terminal_name,
        a.terminal_type,
        a.channel,
        a.terminal_address,
        a.shop_image_path,
        a.province_code,
        a.province_name,
        a.city_code,
        a.city_name,
        a.district_code,
        a.district_name,
        a.longitude,
        a.latitude
        FROM mdm_terminal a
        <where>
            a.tenant_code = #{dto.tenantCode}
            AND a.del_flag = '${@<EMAIL>()}'
            AND a.enable_status = '${@<EMAIL>()}'
            <if test="dto.terminalCode != null and dto.terminalCode != ''">
                <bind name="likeTerminalCode" value="'%' + dto.terminalCode + '%'"/>
                and a.terminal_code like #{likeTerminalCode}
            </if>
            <if test="dto.terminalName != null and dto.terminalName != ''">
                <bind name="likeTerminalName" value="'%' + dto.terminalName + '%'"/>
                and a.terminal_name like #{likeTerminalName}
            </if>
            <if test="dto.keyWord != null and dto.keyWord != ''">
                <bind name="likekeyWord" value="'%' + dto.keyWord + '%'"/>
                and a.terminal_name like #{likekeyWord} or a.terminal_code like #{likekeyWord}
            </if>
            <if test="dto.terminalType != null and dto.terminalType != ''">
                and a.terminal_type = #{dto.terminalType}
            </if>
            <if test="dto.terminalCodes != null">
                and a.terminal_code in(<foreach collection="dto.terminalCodes" item="item" separator=",">
                #{item}</foreach>)
            </if>
        </where>
        ORDER BY
        a.create_time DESC
    </select>
</mapper>