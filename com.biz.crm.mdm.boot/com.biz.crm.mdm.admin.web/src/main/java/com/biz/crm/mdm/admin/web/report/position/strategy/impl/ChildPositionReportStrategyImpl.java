package com.biz.crm.mdm.admin.web.report.position.strategy.impl;

import com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant;
import com.biz.crm.mdm.admin.web.report.position.dto.ChildPositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.dto.PositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.strategy.PositionReportStrategy;
import com.biz.crm.mdm.admin.web.report.position.strategy.helper.PositionReportStrategyHelper;
import com.biz.crm.mdm.admin.web.report.position.vo.PositionPageVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 职位关联下属职位分页列表报表数据策略实现
 *
 * <AUTHOR>
 * @date 2022/3/29
 */
@Component
public class ChildPositionReportStrategyImpl implements PositionReportStrategy<ChildPositionPageDto> {

  @Autowired(required = false)
  private PositionReportStrategyHelper positionReportStrategyHelper;

  @Override
  public String getPageSource() {
    return PositionReportConstant.POSITION_PAGE_SOURCE_CHILD_POSITION_LIST;
  }

  @Override
  public Boolean onRequest(ChildPositionPageDto dto) {
    return StringUtils.isNotBlank(dto.getPositionCode());
  }

  @Override
  public void onReturn(List<PositionPageVo> voList) {
    this.positionReportStrategyHelper.buildUnionName(voList);
  }
}
