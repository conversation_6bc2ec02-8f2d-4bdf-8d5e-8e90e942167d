package com.biz.crm.mdm.admin.web.imports.org.service;

import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.admin.web.imports.org.model.OrgImportVo;
import com.biz.crm.mdm.business.org.local.entity.Org;
import com.biz.crm.mdm.business.org.local.repository.OrgRepository;
import com.biz.crm.mdm.business.org.local.service.OrgService;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

import com.bizunited.nebula.common.util.tenant.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * org导入过程
 *
 * <AUTHOR>
 * @date 2022/05/24
 */
@Component
@Slf4j
public class OrgImportProcess  implements ImportProcess<OrgImportVo> {

  @Autowired(required = false)
  private OrgService orgService;
  @Autowired(required = false)
  private OrgRepository orgRepository;

  @Override
  @Transactional
  public Map<Integer, String> execute(
      LinkedHashMap<Integer, OrgImportVo> data,
      TaskGlobalParamsVo paramsVo,
      Map<String, Object> params) {
    final Optional<OrgImportVo> first = data.values().stream().findFirst();
    if (!first.isPresent()) {
      return null;
    }
    OrgImportVo orgImportVo = first.get();
    Org org = new Org();
    org.setOrgCode(orgImportVo.getOrgCode());
    org.setOrgName(orgImportVo.getOrgName());
    org.setParentCode(orgImportVo.getParentCode());
    org.setTenantCode(paramsVo.getTenantCode());
    orgService.create(org);
    return null;
  }

  /**
   * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存)
   * 新逻辑需同时实现接口 tryVerify tryConfirm
   *
   * @return
   */
  @Override
  public boolean importBeforeValidationFlag() {
    return true;
  }

  /**
   * 数据校验
   *
   * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
   * @param paramsVo 任务公共参数
   * @param params   导入任务自定义参数
   * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
   */
  @Override
  public Map<Integer, String> tryVerify(LinkedHashMap<Integer, OrgImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
    final Optional<OrgImportVo> first = data.values().stream().findFirst();
    if (!first.isPresent()) {
      return null;
    }
    OrgImportVo orgImportVo = first.get();
    Validate.isTrue(StringUtils.isNotEmpty(orgImportVo.getOrgCode()), "企业组织编码必填!");
    Validate.isTrue(StringUtils.isNotEmpty(orgImportVo.getOrgName()), "企业组织名称必填!");
    //当前组织编码
    Org entity = this.orgRepository.findByOrgCode(orgImportVo.getOrgCode(), TenantUtils.getTenantCode());
    Validate.isTrue(ObjectUtils.isEmpty(entity), "当前企业组织编码已存在");
    //上级编码
    String parentCode = orgImportVo.getParentCode();
    if(StringUtils.isNotEmpty(parentCode)){
      Org org = orgService.findByOrgCode(parentCode);
      Validate.isTrue(ObjectUtils.isNotEmpty(org), "上级编码已存在!");
    }
    return null;
  }

  /**
   * 数据存储
   *
   * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
   * @param paramsVo 任务公共参数
   * @param params   导入任务自定义参数
   * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
   */
  @Override
  public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, OrgImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
    final Optional<OrgImportVo> first = data.values().stream().findFirst();
    if (!first.isPresent()) {
      return null;
    }
    OrgImportVo orgImportVo = first.get();
    Org org = new Org();
    org.setOrgCode(orgImportVo.getOrgCode());
    org.setOrgName(orgImportVo.getOrgName());
    org.setParentCode(orgImportVo.getParentCode());
    org.setTenantCode(paramsVo.getTenantCode());
    orgService.create(org);
    return null;
  }


  @Override
  public Class findCrmExcelVoClass() {
    return OrgImportVo.class;
  }

  @Override
  public String getTemplateCode() {
    return "MDM_ORG_IMPORT";
  }

  @Override
  public String getTemplateName() {
    return "企业组织导入 ";
  }
}
