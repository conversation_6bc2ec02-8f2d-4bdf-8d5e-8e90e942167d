package com.biz.crm.mdm.admin.web.report.customermaterial.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.customermaterial.vo.CustomerMaterialVo;
import com.biz.crm.mdm.business.customer.material.sdk.dto.CustomerMaterialDto;

/**
 * <AUTHOR>
 * 客户物料VO mapper
 */
public interface CustomerMaterialVoMapper {

  /**
   * 分页条件查询列表
   * @param page 分页参数
   * @param dto 客户物料查询参数
   * @return 客户物料实体
   */
  Page<CustomerMaterialVo> findByConditions(Page<CustomerMaterialVo> page,
                                            CustomerMaterialDto dto);
}
