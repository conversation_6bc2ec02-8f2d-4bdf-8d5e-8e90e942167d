package com.biz.crm.mdm.admin.web.strategy.internal;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.enums.LockStateEnum;
import com.biz.crm.business.common.sdk.enums.LoginFromTypeEnum;
import com.biz.crm.common.sms.sdk.service.ValiditySmsCodeService;
import com.biz.crm.mdm.admin.web.strategy.LoginUserManageStrategy;
import com.biz.crm.mdm.business.customer.user.sdk.dto.CustomerUserResetPasswordDto;
import com.biz.crm.mdm.business.customer.user.sdk.service.CustomerUserVoService;
import com.biz.crm.mdm.business.customer.user.sdk.vo.CustomerUserVo;
import com.biz.crm.mdm.business.user.sdk.enums.UserTypeEnum;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;

/**
 * DMS小程序业务系统登录账号信息管理策略实现
 *
 * <AUTHOR>
 */
@Service
public class DmsMiniLoginUserManageStrategyImpl implements LoginUserManageStrategy {

  @Autowired(required = false)
  private ValiditySmsCodeService validitySmsCodeService;

  @Autowired(required = false)
  private CustomerUserVoService customerUserVoService;

  @Override
  public LoginFromTypeEnum loginFromTypeEnum() {
    return LoginFromTypeEnum.DMS_MINI;
  }

  @Override
  public void updatePasswordByPhoneAndVerificationCode(String phone, String password, String verificationCode) {
    // 根据设备端类型+登录手机号检查用户有效性
//    Boolean isAvailable = this.validitySmsCodeService.isAvailableVerificationCode(phone, verificationCode);
//    Validate.isTrue(isAvailable, "验证码过期或不正确，请重新获取！");
    // 根据手机号和短信验证码修改密码
    CustomerUserVo customerUserVo = this.customerUserVoService.findDetailsByUserPhone(phone);
    Validate.notNull(customerUserVo, "用户不存在或已删除！");
    // 修改密码
    CustomerUserResetPasswordDto customerUserResetPasswordDto = new CustomerUserResetPasswordDto();
    customerUserResetPasswordDto.setIds(Collections.singletonList(customerUserVo.getId()));
    customerUserResetPasswordDto.setPassword(password);
    this.customerUserVoService.updatePasswordByIds(customerUserResetPasswordDto);
  }

  @Override
  public void verificationByPhone(String phone) {
    // 根据手机号检查账号有效性
    CustomerUserVo customerUserVo = this.customerUserVoService.findDetailsByUserPhone(phone);
    Validate.notNull(customerUserVo, "当前系统不存在此手机号，请先注册！");
    Validate.notBlank(customerUserVo.getUserType(), "未知用户类型，请联系管理员！");
    Validate.isTrue(UserTypeEnum.CUSTOMER.getCode().equals(customerUserVo.getUserType()), "只有客户用户才能登录当前系统！");
    Validate.isTrue(EnableStatusEnum.ENABLE.getCode().equals(customerUserVo.getEnableStatus()), "当前用户已禁用！");
    Validate.isTrue(LockStateEnum.UNLOCK.getCode().equals(customerUserVo.getLockState()), "当前用户已锁定！");
    Validate.isTrue(DelFlagStatusEnum.NORMAL.getCode().equals(customerUserVo.getDelFlag()), "此手机号已删除！");
    Date now = new Date();
    if (customerUserVo.getStartTime() != null) {
      Validate.isTrue(now.compareTo(customerUserVo.getStartTime()) >= 0, "用户未生效");
    }
    if (customerUserVo.getEndTime() != null) {
      Validate.isTrue(now.compareTo(customerUserVo.getEndTime()) <= 0, "用户已失效");
    }
    // TODO 账号关联信息是否变更
  }
}
