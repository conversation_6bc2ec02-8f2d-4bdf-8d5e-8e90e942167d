package com.biz.crm.mdm.admin.web.imports.customer.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 终端导入模型
 *
 * <AUTHOR>
 * @date 2022/5/13
 */
@Data
@CrmExcelImport
public class CustomerCrmImportVo extends CrmExcelVo {


  /**
   * 客户编码
   */
  @CrmExcelColumn("客户编码")
  private String customerCode;

  /**
   * 客户名称
   */
  @CrmExcelColumn("客户名称")
  private String customerName;

  /**
   * 渠道
   */
  @CrmExcelColumn("渠道")
  private String channelName;

  /**
   * 渠道编码
   */
  private String channel;

  /**
   * 客户组织编码
   */
  @CrmExcelColumn("客户组织编码")
  private String customerOrgCode;

  /**
   * 客户组织编码
   */
  private String customerOrgName;

  /**
   * 客户类型
   */
  @CrmExcelColumn("客户类型")
  private String customerTypeName;

  /**
   * 客戶类型编码
   */
  private String customerType;

  /**
   * 组织编码
   */
  @CrmExcelColumn("组织编码")
  private String orgCode;

  /**
   * 组织编码
   */
  private String orgName;


  /** 省 */
  private String provinceCode;

  /** 省 */
  @CrmExcelColumn("省")
  private String provinceName;

  /** 市 */
  private String cityCode;

  /** 市 */
  @CrmExcelColumn("市")
  private String cityName;

  /** 区 */
  private String districtCode;

  /** 区 */
  @CrmExcelColumn("区")
  private String districtName;

  /**
   * 注册地址
   */
  @CrmExcelColumn("注册地址")
  private String registeredAddress;

  /**
   * 客户联系方式
   */
  @CrmExcelColumn("客户联系方式")
  private String customerContact;

  /**
   * 客户法人代表
   */
  @CrmExcelColumn("客户法人代表")
  private String legalRepresentative;

  /**
   * 经度
   */
  @CrmExcelColumn("经度")
  private BigDecimal longitude;

  /**
   * 纬度
   */
  @CrmExcelColumn("纬度")
  private BigDecimal latitude;

  /**
   * 联系人姓名
   */
  @CrmExcelColumn("联系人姓名")
  private String contactName;

  /**
   * 联系人电话
   */
  @CrmExcelColumn("联系人电话")
  private String contactPhone;
}
