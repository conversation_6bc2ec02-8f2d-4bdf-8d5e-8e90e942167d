package com.biz.crm.mdm.admin.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.inquiry.sdk.service.InquiryVoService;
import com.biz.crm.mdm.business.inquiry.sdk.vo.InquiryVo;
import com.biz.crm.mdm.business.price.sdk.dto.FindPriceDto;
import com.biz.crm.mdm.business.price.sdk.enums.FindPriceUserTypeEnum;
import com.biz.crm.mdm.business.product.sdk.dto.ProductPaginationDto;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/19 18:27
 */
@RestController
@RequestMapping("/v1/productPriceController")
@Api(tags = "商品价格")
public class ProductPriceController {

    @Autowired
    private ProductVoService productVoService;

    @Autowired(required = false)
    private InquiryVoService priceModelVoService;

    @ApiOperation(value = "查询分页列表")
    @GetMapping(value = {"/findByConditions"})
    public Result<Page<ProductVo>> findByConditions(@PageableDefault(50) Pageable pageable, ProductPaginationDto paginationDto) {
        try {
            paginationDto = Optional.ofNullable(paginationDto).orElse(new ProductPaginationDto());
            paginationDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
            Page<ProductVo> result = this.productVoService.findByConditions(pageable, paginationDto);
            if (ObjectUtils.isNotEmpty(paginationDto.getCustomerCode()) && CollectionUtils.isNotEmpty(result.getRecords())) {
                Set<String> productCodeSet = result.getRecords().stream().map(ProductVo::getProductCode).collect(Collectors.toSet());
                FindPriceDto findPriceDto = new FindPriceDto();
                findPriceDto.setUserType(FindPriceUserTypeEnum.CUSTOMER.getDictCode());
                findPriceDto.setUserCode(paginationDto.getCustomerCode());
                findPriceDto.setProductCodeSet(productCodeSet);
                Map<String, InquiryVo> priceMap = this.priceModelVoService.findPrice((JSONObject) JSONObject.toJSON(findPriceDto));
                for (ProductVo record : result.getRecords()) {
                    if (priceMap.containsKey(record.getProductCode())) {
                        InquiryVo inquiryVo = priceMap.get(record.getProductCode());
                        record.setSalesPrice(inquiryVo.getPrice());
                    }
                }
            }
            //特殊处理
            return Result.ok(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}
