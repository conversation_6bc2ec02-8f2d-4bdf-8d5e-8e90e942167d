package com.biz.crm.mdm.admin.web.report.mars.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.admin.web.report.mars.dto.MarsPageDto;
import com.biz.crm.mdm.admin.web.report.mars.service.MarsPageVoService;
import com.biz.crm.mdm.admin.web.report.mars.vo.MarsPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据权限管理: MarsPageVo: 数据权限列表相关
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Slf4j
@RestController
@RequestMapping("/v1/mars/marsReport")
@Api(tags = "数据权限管理: MarsPageVo: 数据权限列表相关")
public class MarsReportController {

  @Autowired
  private MarsPageVoService marsPageVoService;

  /**
   * 数据权限分页列表(报表查询)
   *
   * @param pageable 分页参数
   * @param dto      参数dto
   * @return 数据权限列表
   */
  @ApiOperation(value = "数据权限分页列表(报表查询)")
  @GetMapping("/findByMarsPageDto")
  public Result<Page<MarsPageVo>> findByMarsPageDto(@PageableDefault(50) Pageable pageable,
                                                            @ApiParam(name = "MarsPageDto", value = "分页Dto") MarsPageDto dto) {
    try {
      return Result.ok(this.marsPageVoService.findByMarsPageDto(pageable, dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
