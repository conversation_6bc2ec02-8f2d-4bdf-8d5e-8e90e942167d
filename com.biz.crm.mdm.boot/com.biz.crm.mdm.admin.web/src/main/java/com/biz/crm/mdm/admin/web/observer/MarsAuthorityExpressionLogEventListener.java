package com.biz.crm.mdm.admin.web.observer;

import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.mars.sdk.event.MarsAuthorityExpressionEventListener;
import com.bizunited.nebula.mars.sdk.vo.MarsAuthorityExpressionVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 数据权限连接表达式事件操作日志监听实现
 * @date 2023/10/19
 */
@Component
@Slf4j
public class MarsAuthorityExpressionLogEventListener implements
    MarsAuthorityExpressionEventListener {

  @Autowired(required = false)
  private CrmBusinessLogVoService crmBusinessLogVoService;

  @Override
  public void onCreated(MarsAuthorityExpressionVo marsAuthorityExpression) {
    try {
      CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
      crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
      crmBusinessLogDto.setOnlyKey(marsAuthorityExpression.getMarsAuthorityId());
      crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
      crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
      crmBusinessLogDto.setNewObject(marsAuthorityExpression);
      crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    } catch (Exception e) {
      log.error("数据权限连接表达式创建操作日志保存失败", e);
    }
  }

  @Override
  public void onUpdate(MarsAuthorityExpressionVo oldMarsAuthorityExpression,
      MarsAuthorityExpressionVo newMarsAuthorityExpression) {
    try {
      CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
      crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
      crmBusinessLogDto.setOnlyKey(newMarsAuthorityExpression.getMarsAuthorityId());
      crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
      crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
      crmBusinessLogDto.setOldObject(oldMarsAuthorityExpression);
      crmBusinessLogDto.setNewObject(newMarsAuthorityExpression);
      crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    } catch (Exception e) {
      log.error("数据权限连接表达式更新操作日志保存失败", e);
    }
  }
}
