package com.biz.crm.mdm.admin.web.dataview.product;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * mdm产品管理数据视图
 */
@Component
public class ProductDataviewRegister implements DataviewRegister {
  @Override
  public String code() {
    return "mdm_product_dataview";
  }

  @Override
  public String desc() {
    return "MDM产品管理数据视图";
  }

  @Override
  public String buildSql() {
    return "select a.*,  " +
            "    b.product_level_name,  " +
            "    b.rule_code    " +
            "    from mdm_product a  " +
            "    left join mdm_product_level b on a.tenant_code=b.tenant_code and a.product_level_code = b.product_level_code " +
            "    where a.del_flag = '" + DelFlagStatusEnum.NORMAL.getCode() + "' " +
            "    and a.tenant_code = :tenantCode " +
            "    AND b.rule_code like concat (:ruleCode , '%') ";
  }
}
