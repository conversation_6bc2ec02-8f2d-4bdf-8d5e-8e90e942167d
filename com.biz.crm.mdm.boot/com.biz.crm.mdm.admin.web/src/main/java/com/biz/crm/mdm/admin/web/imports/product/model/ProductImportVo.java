package com.biz.crm.mdm.admin.web.imports.product.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
@CrmExcelImport
public class ProductImportVo extends CrmExcelVo {

  /** 商品ID */
  private String id;

  /** 商品编码 */
  @CrmExcelColumn("商品编码")
  private String productCode;

  /** 商品名称 */
  @CrmExcelColumn("商品名称")
  private String productName;

  /** 商品类型 */
  private String productType;

  /** 产品层级编码 */
  @CrmExcelColumn("产品层级编码")
  private String productLevelCode;

  /** 上下架状态 */
  @CrmExcelColumn("上下架状态")
  private String isShelf;

  /** 开始时间 */
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @CrmExcelColumn("开始时间(yyyy-MM-dd)")
  private Date beginDateTime;

  /** 结束时间 */
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @CrmExcelColumn("结束时间(yyyy-MM-dd)")
  private Date endDateTime;

  /** 规格 */
  @CrmExcelColumn("规格")
  private String spec;

  /** 销售单位 */
  @CrmExcelColumn("销售单位")
  private String saleUnit;

  /** 物料编码 */
  @CrmExcelColumn("物料编码")
  private String materialCode;

  /** 物料名称 */
  @CrmExcelColumn("物料名称")
  private String materialName;

  /** 物料数量 */
  @CrmExcelColumn("数量")
  private BigDecimal count;

  @CrmExcelColumn("商品介绍")
  private String introductionText;
}
