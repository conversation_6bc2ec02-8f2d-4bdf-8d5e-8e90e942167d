package com.biz.crm.mdm.admin.web.imports.material.service;

import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.admin.web.imports.material.model.MaterialImportVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.material.local.entity.MaterialEntity;
import com.biz.crm.mdm.business.material.local.repository.MaterialRepository;
import com.biz.crm.mdm.business.material.sdk.dto.MaterialDto;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2022/5/24
 */
@Component
public class MaterialImportProcess implements ImportProcess<MaterialImportVo> {

  @Autowired(required = false) private DictDataVoService dictDataVoService;

  @Autowired(required = false) private NebulaToolkitService nebulaToolkitService;

  @Autowired(required = false) private MaterialVoService materialVoService;

  @Autowired(required = false) private MaterialRepository materialRepository;

  /** 物料类型 */
  private static final String DICT_MATERIAL_TYPE = "material_type";

  @Override
  @Transactional
  public Map<Integer, String> execute(
      LinkedHashMap<Integer, MaterialImportVo> data,
      TaskGlobalParamsVo paramsVo,
      Map<String, Object> params) {
    final Optional<MaterialImportVo> first = data.values().stream().findFirst();
    if (!first.isPresent()) {
      return null;
    }
    MaterialImportVo vo = first.get();
    this.validate(vo);

    final List<MaterialEntity> list =
        this.materialRepository.findDetailByMaterialCodes(
            paramsVo.getTenantCode(), Collections.singleton(vo.getMaterialCode()));
    if (CollectionUtils.isEmpty(list)) {
      MaterialDto materialDto =
          this.nebulaToolkitService.copyObjectByBlankList(
              vo, MaterialDto.class, HashSet.class, ArrayList.class);
      this.materialVoService.save(materialDto);
    }
    return null;
  }

  private void validate(MaterialImportVo vo) {
    Boolean f =
        StringUtils.isNoneBlank(
            vo.getMaterialName());
    Validate.isTrue(f, "物料名称必填");
    vo.setMaterialType(this.findDictCode(vo.getMaterialType(), DICT_MATERIAL_TYPE));
    Validate.notBlank(vo.getMaterialType(), "物料类型对应的数据字典值不存在");
  }

  @Override
  public Class findCrmExcelVoClass() {
    return MaterialImportVo.class;
  }

  @Override
  public String getTemplateCode() {
    return "MDM_MATERIAL_IMPORT";
  }

  @Override
  public String getTemplateName() {
    return "mdm物料信息导入";
  }

  @Override
  public String getBusinessCode() {
    return "MDM_MATERIAL_IMPORT";
  }

  @Override
  public String getBusinessName() {
    return "MDM物料信息导入";
  }

  /**
   * 获取字典对应的code值
   *
   * @param dictValue
   * @param typeCode
   * @return
   */
  private String findDictCode(String dictValue, String typeCode) {
    Map<String, List<DictDataVo>> map =
        this.dictDataVoService.findByDictTypeCodeList(
            Lists.newArrayList(DICT_MATERIAL_TYPE));
    final Optional<DictDataVo> first =
        map.get(typeCode).stream().filter(a -> dictValue.equals(a.getDictValue())).findFirst();
    if (first.isPresent()) {
      return first.get().getDictCode();
    }
    return null;
  }
}
