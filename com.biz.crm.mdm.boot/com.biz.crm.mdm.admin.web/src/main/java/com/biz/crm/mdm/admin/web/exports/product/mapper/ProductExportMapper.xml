<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.admin.web.exports.product.mapper.ProductExportMapper">
    <select id="getProductExportTotal" resultType="java.lang.Integer">
        select count(*)
        from mdm_product
        where
            tenant_code=#{dto.tenantCode}
           and   del_flag='${@<EMAIL>()}'
          <if test="dto.productCode != null and dto.productCode != ''">
            <bind name="likeProductCode" value="'%' + dto.productCode + '%'"/>
            and product_code like #{likeProductCode}
        </if>
        <if test="dto.productName != null and dto.productName != ''">
            <bind name="likeProductName" value="'%' + dto.productName + '%'"/>
            and product_name like #{likeProductName}
        </if>
        <if test="dto.isShelf != null and dto.isShelf != ''">
            and is_shelf=#{dto.isShelf}
        </if>
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and enable_status=#{dto.enableStatus}
        </if>
        order by product_code asc
    </select>

    <select id="findProductExportData" resultType="com.biz.crm.mdm.admin.web.exports.product.model.ProductExportVo">
        select
        a.product_type,
        a.product_code,
        a.product_name,
        a.product_level_code,
        a.is_shelf,
        a.begin_date_time,
        a.end_date_time,
        a.sale_unit,
        a.spec,
        a.enable_status,
        a.maintenance_picture,
        a.maintenance_introduction
        from mdm_product a
        <where>
        a.tenant_code=#{dto.tenantCode}
            <if test="dto.productCode != null and dto.productCode != ''">
                <bind name="likeProductCode" value="'%' + dto.productCode + '%'"/>
                and product_code like #{likeProductCode}
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                <bind name="likeProductName" value="'%' + dto.productName + '%'"/>
                and product_name like #{likeProductName}
            </if>
            <if test="dto.isShelf != null and dto.isShelf != ''">
                and is_shelf=#{dto.isShelf}
            </if>
            <if test="dto.enableStatus != null and dto.enableStatus != ''">
                and enable_status=#{dto.enableStatus}
            </if>
        </where>
        order by product_code asc
        limit #{dto.offset},#{dto.limit}
    </select>
</mapper>
