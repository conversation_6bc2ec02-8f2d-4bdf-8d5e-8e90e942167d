package com.biz.crm.mdm.admin.web.strategy.internal;

import com.biz.crm.common.log.sdk.strategy.CrmBusinessLogStrategy;
import com.biz.crm.common.log.sdk.vo.FieldTransformVo;
import com.biz.crm.mdm.business.table.sdk.service.ColumnConfigVoService;
import com.biz.crm.mdm.business.table.sdk.vo.ColumnConfigVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/05/23
 */
@Service
public class CrmBusinessLogStrategyServiceImpl implements CrmBusinessLogStrategy {

  @Autowired(required = false)
  private ColumnConfigVoService columnConfigVoService;

  @Override
  public String getTransformationName() {
    return "页面引擎获取";
  }

  @Override
  public String getTransformationCode() {
    return "engine";
  }

  @Override
  public List<FieldTransformVo> handleTransformation(String parentCode) {
    // 在页面模板里查询
    Collection<ColumnConfigVo> columnConfigVos = columnConfigVoService.findByParentCode(parentCode);
    if (CollectionUtils.isEmpty(columnConfigVos)) {
      return null;
    }
    List<FieldTransformVo> fieldTransformVos = new ArrayList<>(columnConfigVos.size());
    for (ColumnConfigVo columnConfigVo : columnConfigVos) {
      FieldTransformVo vo = new FieldTransformVo();
      vo.setFieldCode(columnConfigVo.getField());
      vo.setFieldName(columnConfigVo.getTitle());
      fieldTransformVos.add(vo);
    }
    return fieldTransformVos;
  }

}
