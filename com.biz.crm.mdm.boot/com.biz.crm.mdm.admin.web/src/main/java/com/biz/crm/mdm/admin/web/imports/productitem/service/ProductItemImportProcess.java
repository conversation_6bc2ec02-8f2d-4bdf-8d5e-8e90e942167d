package com.biz.crm.mdm.admin.web.imports.productitem.service;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.admin.web.imports.productitem.model.ProductItemImportVo;
import com.biz.crm.mdm.business.product.level.local.entity.ProductItem;
import com.biz.crm.mdm.business.product.level.local.service.ProductItemVoService;
import com.biz.crm.mdm.business.product.level.sdk.dto.ProductItemDto;
import com.biz.crm.mdm.business.product.level.sdk.enums.ProductLevelEnum;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductItemVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @ClassName ProductItemImportProcess
 * @Description 分类字典导入
 * <AUTHOR>
 * @Date 2024/11/19 16:40
 * @Version 1.0
 */
@Component
@Slf4j
public class ProductItemImportProcess implements ImportProcess<ProductItemImportVo> {

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;
    @Autowired
    private ProductItemVoService productItemVoService;

    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, ProductItemImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        log.info("data-->" + JSONObject.toJSONString(data));
        final Optional<ProductItemImportVo> first = data.values().stream().findFirst();
        if (!first.isPresent()) {
            return null;
        }
        ProductItemImportVo vo = first.get();
        this.validate(vo, params);
        this.execute(vo, paramsVo, params);
        return null;
    }
    private void execute(ProductItemImportVo vo,TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        List<ProductItemVo> productItemList = productItemVoService.selectByProductItemCode(vo.getBusinessType());
        Validate.notNull(productItemList, "未查询到业态");
        ProductItemDto dto = this.nebulaToolkitService.copyObjectByBlankList(vo, ProductItemDto.class, HashSet.class, ArrayList.class);
        ProductLevelEnum productLevelEnum = ProductLevelEnum.levelDescToEnum(dto.getProductItemType());
        dto.setProductItemType(productLevelEnum.getCode());
        productItemVoService.importCreate(dto);
    }

    private void validate(ProductItemImportVo vo, Map<String, Object> params) {
        Validate.isTrue(StringUtils.isNotEmpty(vo.getProductItemName()), "分类名称必填!");
        Validate.isTrue(StringUtils.isNotEmpty(vo.getBusinessType()), "所属业态必填!");
        Validate.isTrue(StringUtils.isNotEmpty(vo.getProductItemType()), "所属层级分类必填!");
        Validate.isTrue(!StringUtils.equals(ProductLevelEnum.BRAND.getLevelDesc(), vo.getProductItemType()), "所属层级分类不能为零级");
        log.info("所属层级分类-->" + vo.getProductItemType());
        // 校验一下所属层级 是不是合法
        ProductLevelEnum productLevelEnum = ProductLevelEnum.levelDescToEnum(vo.getProductItemType());
        Validate.isTrue(productLevelEnum != null, "所属层级分类不合法!");

    }

    @Override
    public Class<ProductItemImportVo> findCrmExcelVoClass() {
        return ProductItemImportVo.class;
    }

    @Override
    public String getTemplateCode() {
        return "MDM_PRODUCT_ITEM_IMPORT";
    }

    @Override
    public String getTemplateName() {
        return "MDM分类字典导入";
    }

    @Override
    public String getBusinessCode() {
        return "MDM_PRODUCT_ITEM_IMPORT";
    }

    @Override
    public String getBusinessName() {
        return "MDM分类字典导入";
    }
}
