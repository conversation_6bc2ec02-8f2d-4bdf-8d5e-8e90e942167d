package com.biz.crm.mdm.admin.web.login.decision;

import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.enums.LoginFromTypeEnum;
import com.biz.crm.mdm.admin.web.login.DefaultPerfectLoginUserDetails;
import com.biz.crm.mdm.admin.web.login.form.LoginFormDetails;
import com.biz.crm.mdm.admin.web.login.wx.utilities.SfaWxOpenIdUtil;
import com.biz.crm.mdm.admin.web.strategy.PasswordOverdueStrategy;
import com.biz.crm.mdm.business.customer.user.local.entity.CustomerUser;
import com.biz.crm.mdm.business.customer.user.local.service.CustomerUserService;
import com.biz.crm.mdm.business.user.local.repository.UserRepository;
import com.biz.crm.mdm.business.user.sdk.constant.UserConstant;
import com.biz.crm.mdm.business.user.sdk.service.UserValidityCheckService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.security.sdk.config.SimpleSecurityProperties;
import com.bizunited.nebula.security.sdk.login.AuthenticationDecisionStrategy;
import com.bizunited.nebula.security.sdk.login.DecisionTypes;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import com.bizunited.nebula.security.sdk.loginform.LoginDetails;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.ehcache.core.util.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 典型的账户名+密码方式策略实现
 *
 * <AUTHOR>
 */
@Component
public class DefaultAccountAndPasswordStrategy extends DefaultPerfectLoginUserDetails implements AuthenticationDecisionStrategy {

    @Autowired(required = false)
    private UserValidityCheckService userValidityCheckService;

    @Autowired(required = false)
    private List<PasswordOverdueStrategy> passwordOverdueStrategies;

    @Autowired(required = false)
    private StringRedisTemplate stringRedisTemplate;

    @Autowired(required = false)
    private SimpleSecurityProperties simpleSecurityProperties;

    @Autowired(required = false)
    private CustomerUserService customerUserService;

    @Autowired(required = false)
    private SfaWxOpenIdUtil sfaWxOpenIdUtil;

    /**
     * 触发错误次数
     */
    @Value("${security.login.failedTimes:5}")
    private int failedTimes;

    /**
     * 密码过期验证策略
     */
    @Value("${password.overdue.overdueStrategy:notAllowLogin}")
    private String overdueStrategy;

    @Value("${spring.profiles.active:dev}")
    private String profiles;

    @Override
    public Integer type() {
        return DecisionTypes.ACCOUNTANDPASSWORD.getCode();
    }

    @Override
    public UserIdentity decide(LoginDetails loginFormDetails, Integer type) {
        /*
         * 处理过程如下：
         * 1、首先通过登录账号查询当前用户并检查用户有效性
         * 2、检查登录密码是否正确
         * 3、验证调用密码过期策略进行检查
         * 4、创建企业用户，并进行企业用户的登录信息完善（用户基本信息）
         * 5、最后完善登录信息中的岗位组织等信息
         */
        LoginFormDetails loginUserDetails = (LoginFormDetails) loginFormDetails;
        Validate.notBlank(loginUserDetails.getAccount(), "登录账号不能为空！");
        Validate.notBlank(loginUserDetails.getPassword(), "登录密码不能为空！");
        UserVo userVo = null;
        Integer appType = Objects.nonNull(loginUserDetails.getAppType()) ? loginUserDetails.getAppType() : 9999;
        LoginFromTypeEnum loginFromTypeEnum = LoginFromTypeEnum.getEnumByAppType(appType);
        if (LoginFromTypeEnum.DMS_WEB.equals(loginFromTypeEnum)
                || LoginFromTypeEnum.DMS_MINI.equals(loginFromTypeEnum)) {
            userVo = new UserVo();
            CustomerUser customerUser = customerUserService.findByUserName(loginUserDetails.getAccount());
            customerUserService.userValidityCheck(customerUser);
            userVo.setUserType(customerUser.getUserType());
            userVo.setTenantCode(customerUser.getTenantCode());
            userVo.setUserName(customerUser.getUserName());
            userVo.setUserPhone(customerUser.getUserPhone());
            userVo.setFullName(customerUser.getFullName());
            userVo.setUserCode(customerUser.getUserCode());
            userVo.setUserPassword(customerUser.getUserPassword());
        } else {
            userVo = this.userValidityCheckService.verificationManageByAccountFromLogin(loginUserDetails.getAccount());
        }
        // 2、密码校验
        String passwordEncryption = DigestUtils.md5DigestAsHex(loginUserDetails.getPassword().getBytes(StandardCharsets.UTF_8));
        int surplusTimes = this.getAuthenticationFailedTimes(loginFormDetails);
        String errorMsg;
        if (surplusTimes > 0) {
//      errorMsg = "登录密码错误！您还有" + this.getAuthenticationFailedTimes(loginFormDetails) + "次尝试机会";
            errorMsg = "账号或密码错误！";
        } else {
            errorMsg = "账号或密码错误！您的账号已被锁定，请联系管理员";
        }
        String replacePass = super.adminLogin(profiles, this.type(), userVo);
        Validate.isTrue(passwordEncryption.equals(userVo.getUserPassword()) || StringUtils.equals(passwordEncryption, replacePass), errorMsg);
        // 3、如果不是小程序 则需要进行校验
        if (!LoginFromTypeEnum.SFA_MINI.equals(loginFromTypeEnum)){
            PasswordOverdueStrategy passwordOverdueStrategy = this.getPasswordOverdueStrategy(overdueStrategy);
            if (ObjectUtils.isNotEmpty(passwordOverdueStrategy)) {
                passwordOverdueStrategy.handle(loginUserDetails.getAccount(), null);
            }
        }
        // 4、
        FacturerUserDetails mdmUser = new FacturerUserDetails();
        mdmUser.setLoginType(DecisionTypes.ACCOUNTANDPASSWORD.getCode());
        super.perfectLoginUserDetails(userVo, mdmUser);
        mdmUser.setAccount(loginUserDetails.getAccount());
        // 5、
        super.perfectLoginPostAndOrg(mdmUser);
        //6.
        //SFA openId验证
        if (Objects.nonNull(mdmUser.getRoleCodes())) {
            String[] roleCodes = mdmUser.getRoleCodes();
            ArrayList<String> arrayList = Lists.newArrayList(roleCodes);
            //不是超级管理员或经销商用户
            if (!(arrayList.contains(UserConstant.ADMIN) || arrayList.contains(UserConstant.SFA_DEALER_ROLE_CODE))) {
                if (LoginFromTypeEnum.SFA_MINI.equals(loginFromTypeEnum)) {
                    this.sfaWxOpenIdUtil.openIdCheckOrInitialize(userVo, loginUserDetails.getCode());
                }
            }
        }
        return mdmUser;
    }


    /**
     * 获取密码控制策略
     *
     * @return 指定的密码控制策略
     */
    private PasswordOverdueStrategy getPasswordOverdueStrategy(String overdueStrategy) {
        if (CollectionUtils.isEmpty(passwordOverdueStrategies) || StringUtils.isBlank(overdueStrategy)) {
            return null;
        }
        for (PasswordOverdueStrategy strategy : passwordOverdueStrategies) {
            if (strategy.name().equals(overdueStrategy)) {
                return strategy;
            }
        }
        return null;
    }


    /**
     * 获取当前登录账号剩余可失败次数
     *
     * @param loginDetails
     * @return
     */
    public Integer getAuthenticationFailedTimes(LoginDetails loginDetails) {
        /*
         * 1、检查登录账号
         * 2、通过账号获取登录错误次数数据
         */
        // 通过登录错误传递错误的account
        Integer type = this.simpleSecurityProperties.getDefaultLoginType();
        String account = StringUtils.join(type, "_", loginDetails.getAccount());
        String redisKey = String.format(UserConstant.LOGIN_FAILED_KEY, TenantUtils.getTenantCode(), account);
        String loginFailedTimeStr = this.stringRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isBlank(loginFailedTimeStr)) {
            loginFailedTimeStr = "1";
        } else {
            loginFailedTimeStr = Integer.toString(Integer.parseInt(loginFailedTimeStr) + 1);
        }
        return failedTimes - Integer.parseInt(loginFailedTimeStr);
    }


}
