package com.biz.crm.mdm.admin.web.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.admin.web.service.CustomerTerminalBusinessService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 终端业务员绑定关系
 *
 * @Author: yangrui
 * @Date: 2024-11-19 16:44
 */
@Slf4j
@RestController
@RequestMapping(value = {"/v1/customer/terminal"})
@RequiredArgsConstructor
public class CustomerTerminalController {

    private final CustomerTerminalBusinessService customerTerminalBusinessService;

    @ApiOperation(value = "定时维护终端业务员绑定关系")
    @GetMapping("/updateTerminalBusinessRelation")
    public Result<?> updateTerminalBusinessRelation() {
        try {
            customerTerminalBusinessService.updateCustomerTerminalBusinessRelation();
            return Result.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
