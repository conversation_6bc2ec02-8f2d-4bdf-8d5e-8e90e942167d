package com.biz.crm.mdm.admin.web.report.terminal.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.terminal.dto.NoRelaCurTerminalDto;
import com.biz.crm.mdm.admin.web.report.terminal.dto.NoRelaTerminalDto;
import com.biz.crm.mdm.admin.web.report.terminal.dto.RelaCurTerminalDto;
import com.biz.crm.mdm.admin.web.report.terminal.dto.TerminalReportPaginationDto;
import com.biz.crm.mdm.admin.web.report.terminal.vo.TerminalReportVo;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaCustomerOrg;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaOrg;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalClientDto;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalClientVo;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2021/11/5
 */
public interface TerminalReportVoMapper {

  /**
   * 终端企业组织关联分页查询分页-无供货关系
   *
   * @param page
   * @param dto
   * @return
   */
  Page<TerminalReportVo> findByConditions(
      Page<TerminalReportVo> page, @Param("dto") TerminalReportPaginationDto dto);

  /**
   * 终端企业组织关联分页查询分页-有供货关系[我关联的]
   *
   * @param page
   * @param dto
   * @return
   */
  Page<TerminalReportVo> findByRelaCurTerminalDto(
      Page<TerminalReportVo> page, @Param("dto") RelaCurTerminalDto dto);

  /**
   * 终端企业组织关联分页查询分页-有供货关系[有关联的不是我]
   *
   * @param page
   * @param dto
   * @return
   */
  Page<TerminalReportVo> findByNoRelaCurTerminalDto(
      Page<TerminalReportVo> page, @Param("dto") NoRelaCurTerminalDto dto);

  /**
   * 终端企业组织关联分页查询分页-有供货关系[未关联任何的]
   *
   * @param page
   * @param dto
   * @return
   */
  Page<TerminalReportVo> findByNoRelaTerminalDto(
      Page<TerminalReportVo> page, @Param("dto") NoRelaTerminalDto dto);

  /**
   * 通过终端编码查询
   *
   * @param terminalCodes
   * @param tenantCode
   * @param delCode
   * @return
   */
  List<TerminalReportVo> findByTerminalCodes(
      @Param("list") List<String> terminalCodes,
      @Param("tenantCode") String tenantCode,
      @Param("delCode") String delCode);

  /**
   * 根据终端编码集合获取终端关联组织信息
   *
   * @param terminalCodeSet
   * @param tenantCode
   * @return
   */
  List<TerminalRelaOrg> findTerminalRelaOrgListByTerminalCodes(
      @Param("list") Set<String> terminalCodeSet, @Param("tenantCode") String tenantCode);

  /**
   * 根据终端编码集合获取终端关联客户组织信息
   *
   * @param terminalCodeSet
   * @param tenantCode
   * @return
   */
  List<TerminalRelaCustomerOrg> findTerminalRelaCustomerOrgListByTerminalCodes(
      @Param("list") Set<String> terminalCodeSet, @Param("tenantCode") String tenantCode);

  /**
   * 查询当前用户及其下属所关联的终端信息分页列表
   * <p>
   * 用户账号和租户必传
   *
   * @param page 分页信息
   * @param dto  查询对象
   * @return Page<TerminalClientVo> 查询当前用户及其下属所关联的终端信息分页列表
   */
  Page<TerminalClientVo> findChildrenPageByTerminalClientDto(Page<TerminalClientVo> page, @Param("dto") TerminalClientDto dto);

  /**
   * 根据条件分页查询数据
   *
   * @param page
   * @param dto
   * @return
   */
  Page<TerminalClientVo> findPageByTerminalClientDto(Page<TerminalClientVo> page, @Param("dto") TerminalClientDto dto);
}
