package com.biz.crm.mdm.admin.web.dataview.channel.org;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * mdm渠道组织数据视图
 * <AUTHOR>
 */
@Component
public class ChannelOrgDataviewRegister implements DataviewRegister {
  @Override
  public String code() {
    return "mdm_channel_org_dataview";
  }

  @Override
  public String desc() {
    return "MDM渠道组织数据视图";
  }

  @Override
  public String buildSql() {
    return "select a.*,b.channel_org_name as parent_name " +
            "    from mdm_channel_org a " +
            "    left join mdm_channel_org b on a.parent_code=b.channel_org_code " +
            "    WHERE 1=1 " +
            "    AND a.del_flag = '" + DelFlagStatusEnum.NORMAL.getCode() + "' " +
            "    AND a.tenant_code = :tenantCode " +
            "    AND a.rule_code like concat (:ruleCode , '%') ";
  }
}
