package com.biz.crm.mdm.admin.web.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.dto.SfaSendTaskDto;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.mdm.admin.web.dto.PublicAccountSendTaskMsgDto;
import com.biz.crm.mdm.admin.web.helper.SendPublicAccountMsgHelper;
import com.biz.crm.mdm.business.user.local.entity.UserRelWeChatEntity;
import com.biz.crm.mdm.business.user.local.repository.UserRelWeChatRepository;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/31 12:31
 **/
@Service
@RefreshScope
public class PublicAccountSendMsgServiceImpl {

    @Value("${wx.public.account.task.template_id}")
    private String tempalteId;

    @Value("${wx.public.account.task.path}")
    private String path;

    @Value("${wx.applet.sfa.appId}")
    private String miniAppid;

    @Resource
    private SendPublicAccountMsgHelper sendPublicAccountMsgHelper;

    @Resource
    private UserRelWeChatRepository userRelWeChatRepository;


    /**
     * 发送公众号消息
     *
     * @param dto
     * @param appid
     * @param appSecret
     */
    public void sendSfaTaskToUserName(SfaSendTaskDto dto, String appid, String appSecret) {
        List<UserRelWeChatEntity> relWeChatEntities = userRelWeChatRepository.findByUserNames(TenantUtils.getTenantCode(), dto.getReceiveAccount());
        if (CollectionUtils.isNotEmpty(relWeChatEntities)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("appid", miniAppid);
            jsonObject.put("pagepath", path);
            PublicAccountSendTaskMsgDto.TaskMsgTime taskMsgTime = new PublicAccountSendTaskMsgDto.TaskMsgTime();
            taskMsgTime.setColor("#173177");
            taskMsgTime.setValue(DateUtil.format(dto.getSendTime(), DateUtil.DEFAULT_DATE_ALL_PATTERN));
            PublicAccountSendTaskMsgDto.TaskMsgThing taskMsgThing = new PublicAccountSendTaskMsgDto.TaskMsgThing();
            taskMsgThing.setColor("#173177");
            taskMsgThing.setValue(dto.getTaskTitle());
            PublicAccountSendTaskMsgDto.TaskMsgData taskMsgData = new PublicAccountSendTaskMsgDto.TaskMsgData();
            taskMsgData.setThing11(taskMsgThing);
            taskMsgData.setTime5(taskMsgTime);
            for (UserRelWeChatEntity relWeChatEntity : relWeChatEntities) {
                PublicAccountSendTaskMsgDto taskMsgDto = new PublicAccountSendTaskMsgDto();
                taskMsgDto.setTouser(relWeChatEntity.getOpenId());
                taskMsgDto.setTemplate_id(tempalteId);
                taskMsgDto.setMiniprogram(jsonObject);
                taskMsgDto.setData(taskMsgData);
                sendPublicAccountMsgHelper.sendTaskMsgToTemplate(taskMsgDto, appid, appSecret);
            }
        }
    }


}
