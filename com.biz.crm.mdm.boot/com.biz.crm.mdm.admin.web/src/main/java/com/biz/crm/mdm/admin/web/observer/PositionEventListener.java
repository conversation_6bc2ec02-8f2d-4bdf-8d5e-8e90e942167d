package com.biz.crm.mdm.admin.web.observer;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.position.dto.PositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.service.PositionPageVoService;
import com.biz.crm.mdm.admin.web.report.position.vo.PositionPageVo;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.workflow.sdk.dto.PositionInfoDto;
import com.biz.crm.workflow.sdk.listener.PositionInfoListener;
import com.biz.crm.workflow.sdk.vo.PositionVo;
import com.biz.crm.workflow.sdk.vo.response.PositionInfoResponse;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * mdm为工作流提供职位相关基础数据
 *
 * <AUTHOR>
 * @date 2022-04-01 15:18:12
 */
@Component
public class PositionEventListener implements PositionInfoListener {

  @Autowired(required = false)
  private PositionVoService positionVoService;

  @Autowired(required = false)
  private PositionPageVoService positionPageVoService;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public PositionInfoResponse onFindAllParentByRoleCodes(PositionInfoDto dto) {
    PositionInfoResponse positionInfoResponse = new PositionInfoResponse();
    List<com.biz.crm.mdm.business.position.sdk.vo.PositionVo> positionVos = this.positionVoService.findPositionsByRoleCodes(dto.getRoleCodes());
    if (CollectionUtils.isNotEmpty(positionVos)) {
      List<PositionVo> voList = (List<PositionVo>) this.nebulaToolkitService.copyCollectionByWhiteList(positionVos, com.biz.crm.mdm.business.position.sdk.vo.PositionVo.class, PositionVo.class, HashSet.class, ArrayList.class);
      positionInfoResponse.setPositionVos(voList);
    }
    return positionInfoResponse;
  }

  @Override
  public PositionInfoResponse onFindByIdsOrCodes(PositionInfoDto dto) {
    PositionInfoResponse positionInfoResponse = new PositionInfoResponse();
    List<com.biz.crm.mdm.business.position.sdk.vo.PositionVo> positionVos = this.positionVoService.findByIdsOrCodes(dto.getIds(), dto.getPositionCodes());
    if (CollectionUtils.isNotEmpty(positionVos)) {
      List<PositionVo> voList = Lists.newArrayList();
      positionVos.forEach(vo -> {
        PositionVo positionVo = this.nebulaToolkitService.copyObjectByWhiteList(vo, PositionVo.class, HashSet.class, ArrayList.class);
        positionVo.setRoleList(vo.getRoleList());
        voList.add(positionVo);
      });
      positionInfoResponse.setPositionVos(voList);
    }
    return positionInfoResponse;
  }

  @Override
  public PositionInfoResponse onFindByConditions(PositionInfoDto dto) {
    //1.构造职位报表请求参数，并获取职位报表数据
    Pageable pageable = PageRequest.of(dto.getPage(), dto.getSize());
    PositionPageDto pageDto = new PositionPageDto();
    pageDto.setOrgName(dto.getOrgName());
    pageDto.setUserName(dto.getUserName());
    pageDto.setPositionName(dto.getPositionName());
    Page<PositionPageVo> page = this.positionPageVoService.findByConditions(pageable, pageDto);
    //2.构造返回分页实体，并组装数据
    Page<PositionVo> positionVoPage = new Page<>();
    positionVoPage.setSize(page.getSize());
    positionVoPage.setCurrent(page.getCurrent());
    positionVoPage.setTotal(page.getTotal());
    //3.转换数据
    if (ObjectUtils.isNotEmpty(page) && CollectionUtils.isNotEmpty(page.getRecords())) {
      List<PositionVo> voList = (List<PositionVo>) this.nebulaToolkitService.copyCollectionByWhiteList(page.getRecords(), PositionPageVo.class, PositionVo.class, HashSet.class, ArrayList.class);
      positionVoPage.setRecords(voList);
    }
    PositionInfoResponse positionInfoResponse = new PositionInfoResponse();
    positionInfoResponse.setPositionVoPage(positionVoPage);
    return positionInfoResponse;
  }
}
