package com.biz.crm.mdm.admin.web.service.internal;

import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.bizunited.nebula.common.service.redis.RedisMutexService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 描述：</br>业务规则生成方法
 *
 * <AUTHOR>
 * @date 2022/7/27
 */
@Service
public class DefaultGenerateCodeServiceImpl implements GenerateCodeService {

    /**
     * code生成规则
     */
    private final static String KEY_FORMAT = "GenerateCode:%s:%s:code:index:%s";

    @Autowired(required = false)
    private RedisMutexService redisMutexService;

    @Autowired(required = false)
    private RedissonClient redissonClient;

    @Value("${spring.application.name}")
    private String applicationName;

    @Override
    public List<String> generateCode(String ruleCode, int number, Integer mixStrLen) {
        return this.generateCodeAll(ruleCode, DateUtil.DEFAULT_YEAR_MONTH_DAY_NO_CH, number, mixStrLen, 2, TimeUnit.DAYS);
    }

    @Override
    public List<String> generateCodeYearMonth(String ruleCode, int number, Integer mixStrLen) {
        return this.generateCodeAll(ruleCode, DateUtil.DEFAULT_YEAR_MONTH_NO_CH, number, mixStrLen, 32, TimeUnit.DAYS);
    }

    @Override
    public List<String> generateCodeYear(String ruleCode, int number, Integer mixStrLen) {
        return this.generateCodeAll(ruleCode, "yyyy", number, mixStrLen, 370, TimeUnit.DAYS);
    }

    @Override
    public List<String> generateCodeNotDate(String ruleCode, int number, Integer mixStrLen) {
        return this.generateCodeAll(ruleCode, "", number, mixStrLen, -1, TimeUnit.DAYS);
    }

    private List<String> generateCodeAll(String ruleCode, String dateFormat, int number, Integer mixStrLen, long expire, TimeUnit unit) {
        String nowTimeStr = "";
        if (StringUtil.isNotEmpty(dateFormat)) {
            nowTimeStr = DateFormatUtils.format(new Date(), dateFormat);
        }
        List<String> codes = Lists.newArrayList();
        //编码规则redis key默认加时间戳，保证每天重置
        String key = String.format(KEY_FORMAT, applicationName, ruleCode + nowTimeStr, TenantUtils.getTenantCode());

        long firstIndex = this.getAndAdd(key, 1, expire, unit, number);

        char[] fillChars = new char[mixStrLen];
        Arrays.fill(fillChars, 0, mixStrLen, '0');
        DecimalFormat format = new DecimalFormat(new String(fillChars));

        for (int i = 0; i < number; i++) {
            String index = format.format(firstIndex + i);
            codes.add(StringUtils.join(ruleCode + nowTimeStr, index));
        }
        return codes;
    }

    public long getAndAdd(String code, long min, long expire, TimeUnit unit, long addValue) {
        Validate.notNull(code, "code必须传入");
        Validate.notNull(unit, "时间单位必须传入");
        long currentMin = Math.max(min, 0L);

        String lockCode = StringUtils.join("_async_", code);
        long currentValue = 0L;

        try {
            redisMutexService.lock(lockCode);
            RAtomicLong atomicLong = this.redissonClient.getAtomicLong(code);
            currentValue = atomicLong.getAndAdd(addValue);
            if (currentValue < currentMin) {
                atomicLong.set(currentMin + addValue);
                currentValue = currentMin;
            }
            //过期时间大于0 才有意义
            if (expire > 0) {
                atomicLong.expire(expire, unit);
            }
        } finally {
            redisMutexService.unlock(lockCode);
        }

        return currentValue;
    }

}
