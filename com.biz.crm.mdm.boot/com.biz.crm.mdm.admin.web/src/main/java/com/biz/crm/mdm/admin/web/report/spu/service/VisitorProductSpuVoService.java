package com.biz.crm.mdm.admin.web.report.spu.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.spu.dto.VisitorProductSpuPaginationDto;
import com.biz.crm.mdm.business.product.spu.sdk.vo.ProductSpuVo;
import org.springframework.data.domain.Pageable;

/**
 * spu查询服务接口类
 *
 * <AUTHOR>
 * @date 2022/9/9
 */
public interface VisitorProductSpuVoService {

  /**
   * 可购清单spu分页信息接口-内存分页
   *
   * @param pageable
   * @param dto
   * @return
   */
  Page<ProductSpuVo> findByConditions(Pageable pageable, VisitorProductSpuPaginationDto dto);
}
