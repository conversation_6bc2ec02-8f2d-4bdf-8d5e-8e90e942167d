package com.biz.crm.mdm.admin.web.report.positionlevel.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 职位级别分页返回vo
 *
 * <AUTHOR>
 * @date 2021/10/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "职位级别分页返回vo")
public class PositionLevelPageVo extends TenantFlagOpVo {

  private static final long serialVersionUID = 7916968783503599346L;

  /**
   * 职位编码
   */
  @ApiModelProperty("职位编码")
  private String positionCode;
  /**
   * 职位名称
   */
  @ApiModelProperty("职位名称")
  private String positionName;
  /**
   * 关联组织编码
   */
  @ApiModelProperty("关联组织编码")
  private String orgCode;
  /**
   * 关联组织名称
   */
  @ApiModelProperty("关联组织名称")
  private String orgName;
  /**
   * 职位级别编码
   */
  @ApiModelProperty("职位级别编码")
  private String positionLevelCode;
  /**
   * 职位级别名称
   */
  @ApiModelProperty("职位级别名称")
  private String positionLevelName;

  @ApiModelProperty("编码/名称")
  private String unionName;
}