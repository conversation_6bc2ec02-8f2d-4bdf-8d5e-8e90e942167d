package com.biz.crm.mdm.admin.web.report.spu.dto;

import lombok.Data;

import java.util.List;

/**
 * spu查询dto
 *
 * <AUTHOR>
 * @date 2022/9/9
 */
@Data
public class VisitorProductSpuDto {
  /** 租户编码 */
  private String tenantCode;
  /** 删除标记 */
  private String delFlag;
  /** 上架标记 */
  private String isShelf;
  /** 查询spu标签 */
  private List<String> spuTagList;
  /** 商品编码in查询 */
  private List<String> productCodeList;
  /** 商品层级ruleCode */
  private String ruleCode;
  /** 商品编码或名称精确查询 */
  private String keyword;
  /** 商品编码或名称like查询 */
  private String likeKeyword;
}
