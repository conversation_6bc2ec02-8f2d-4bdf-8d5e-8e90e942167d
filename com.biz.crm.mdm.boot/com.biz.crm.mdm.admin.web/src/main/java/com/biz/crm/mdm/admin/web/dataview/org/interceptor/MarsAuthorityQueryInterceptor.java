package com.biz.crm.mdm.admin.web.dataview.org.interceptor;

import com.bizunited.nebula.europa.database.sdk.strategy.ExternalQueryInterceptor;
import com.bizunited.nebula.europa.sdk.context.execute.ExecuteContent;
import com.bizunited.nebula.europa.sdk.context.matedata.MetaData;
import com.bizunited.nebula.europa.sdk.vo.EuropaInfoVo;

import com.bizunited.nebula.mars.local.entity.MarsAuthorityEntity;
import com.bizunited.nebula.mars.local.vo.MarsAuthorityQueryVo;
import com.bizunited.nebula.mars.sdk.register.SelectAuthorityModeRegister;
import com.bizunited.nebula.mars.sdk.register.SelectScopeRegister;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;


import javax.persistence.EntityManager;
import java.util.*;

/**
 * <AUTHOR>
 * @title MarsAuthorityQueryInterceptor
 * @date 2023/1/3 11:58
 * @description 数据权限数据视图参数补充
 */
@Component
public class MarsAuthorityQueryInterceptor implements ExternalQueryInterceptor {

  @Autowired(required = false)
  @Lazy
  private List<SelectScopeRegister> selectScopeRegisters;

  @Autowired(required = false)
  @Lazy
  private Set<SelectAuthorityModeRegister> selectAuthorityModeRegisters;

  @Override
  public String code() {
    return "mdm_mars_auth_query_interceptor";
  }

  @Override
  public String name() {
    return "MDM数据权限数据视图参数补充";
  }

  @Override
  public List<Object[]> process(EntityManager entityManager, MetaData metaData, EuropaInfoVo europaInfoVo, ExecuteContent executeContent, String... strings) {
    List<Map<String, Object>> results = executeContent.getResults();
    if(CollectionUtils.isEmpty(results)){
      return null;
    }
    if (0 == results.parallelStream().filter(row -> row != null).count()) {
      return org.apache.commons.compress.utils.Lists.newArrayList();
    }
    List<MarsAuthorityEntity> marsAuthorityList = Lists.newArrayList();
    List<MarsAuthorityQueryVo>marsAuthorityQueryVos=Lists.newArrayList();
    results.forEach(result -> {
      MarsAuthorityQueryVo marsAuthorityQueryVo = new MarsAuthorityQueryVo();
      MarsAuthorityEntity marsAuthorityEntity = new MarsAuthorityEntity();
      //空数据校验
      String scopeKey = (String) result.get("scope_key");
      if(StringUtils.isNotEmpty(scopeKey)){
        marsAuthorityQueryVo.setScopeKey(scopeKey);
      }
      String selectModeKey = (String) result.get("select_mode_key");
      if(StringUtils.isNotEmpty(selectModeKey)){
        marsAuthorityQueryVo.setSelectModeKeys( new String [] {selectModeKey});
      }
      BeanUtils.copyProperties(marsAuthorityQueryVo, marsAuthorityEntity);
      marsAuthorityList.add(marsAuthorityEntity);
      marsAuthorityQueryVos.add(marsAuthorityQueryVo);
    });
    this.validate(marsAuthorityQueryVos);

    List<Object[]> externalContents = Lists.newArrayList();
    marsAuthorityQueryVos.stream().forEach(marsAuthorityQueryVo -> {
      results.stream().forEach(result->{
      List<Object>itemList = Lists.newArrayList();
      for (String externalFileName : strings){
        if (StringUtils.equals(externalFileName,"scopeName")) {
          if (StringUtils.isNotEmpty(marsAuthorityQueryVo.getScopeName())){
            itemList.add(marsAuthorityQueryVo.getScopeName());
          }else{
            itemList.add("");
          }
      }
        if (StringUtils.equals(externalFileName,"selectModeNames")){
          if (!ObjectUtils.isEmpty(marsAuthorityQueryVo.getSelectModeNames())){
            itemList.add(marsAuthorityQueryVo.getSelectModeNames());
          }else{
            itemList.add("");
          }
        }
      }
      externalContents.add(itemList.toArray(new Object[]{}));
      });
    });
    return externalContents;
  }

  /**
   * 数据处理
   * @param marsAuthorityList
   * @return
   */
  private List<MarsAuthorityQueryVo> validate(List<MarsAuthorityQueryVo> marsAuthorityList) {
    if (marsAuthorityList != null && !marsAuthorityList.isEmpty()) {
      Iterator marsAuthority = marsAuthorityList.iterator();

      while (true) {
        MarsAuthorityQueryVo marsAuthorityQueryVo;
        String[] selectModeKeys;
        do {
          do {
            if (!marsAuthority.hasNext()) {
              return Lists.newArrayList(marsAuthorityList);
            }
            marsAuthorityQueryVo = (MarsAuthorityQueryVo) marsAuthority.next();
            String scopeKey = marsAuthorityQueryVo.getScopeKey();
            selectModeKeys = marsAuthorityQueryVo.getSelectModeKeys();
            if (StringUtils.isNotBlank(scopeKey) && !CollectionUtils.isEmpty(this.selectScopeRegisters)) {
              //根据scopeKey查询特定的SelectScopeRegister注册器
              SelectScopeRegister registerByScopeKey = this.findRegisterByScopeKey(scopeKey);
              if (registerByScopeKey == null) {
                marsAuthorityQueryVo.setScopeName("检查并删除：未实现：" + scopeKey);
              } else {
                if (StringUtils.equals(registerByScopeKey.scopeKey(), scopeKey)) {
                  marsAuthorityQueryVo.setScopeName(registerByScopeKey.selectName());
                }
              }
            }
          } while (selectModeKeys == null);
        } while (CollectionUtils.isEmpty(this.selectAuthorityModeRegisters));

        List<String> selectModeNames = Lists.newArrayList();
        for (int i = 0; i < selectModeKeys.length; i++){
          SelectAuthorityModeRegister registerByModeKey = this.findRegisterByModeKey(selectModeKeys[i]);
          if (StringUtils.equalsAny(registerByModeKey.modeKey(), selectModeKeys)){
            selectModeNames.add(registerByModeKey.modeName());
          }
        }
        marsAuthorityQueryVo.setSelectModeNames(selectModeNames.toArray(new String[0]));
      }
    } else {
      return new ArrayList<>();
    }
  }

  /**
   *  按照scopeKey查询特定的SelectScopeRegister注册器
   * @param scopeKey
   * @return
   */
  private SelectScopeRegister findRegisterByScopeKey(String scopeKey) {
    if(CollectionUtils.isEmpty(this.selectScopeRegisters) || StringUtils.isBlank(scopeKey)) {
      return null;
    }
    for (SelectScopeRegister selectScopeRegister : selectScopeRegisters) {
      String currentScopeKey = selectScopeRegister.scopeKey();
      if(StringUtils.equals(currentScopeKey, scopeKey)) {
        return selectScopeRegister;
      }
    }
    return null;
  }


  /**
   * 根据modeKey查找到指定的注册器
   * @param modeKey
   * @return
   */
  private SelectAuthorityModeRegister findRegisterByModeKey(String modeKey) {
    if(StringUtils.isBlank(modeKey) || CollectionUtils.isEmpty(this.selectAuthorityModeRegisters)) {
      return null;
    }
    for (SelectAuthorityModeRegister selectAuthorityModeRegister : selectAuthorityModeRegisters) {
      String currentModeKey = selectAuthorityModeRegister.modeKey();
      if(StringUtils.equals(currentModeKey, modeKey)) {
        return selectAuthorityModeRegister;
      }
    }
    return null;
  }
}
