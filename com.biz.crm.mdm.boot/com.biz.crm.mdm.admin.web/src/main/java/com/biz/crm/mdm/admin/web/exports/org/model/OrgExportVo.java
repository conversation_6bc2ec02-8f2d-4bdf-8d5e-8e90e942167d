package com.biz.crm.mdm.admin.web.exports.org.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelExport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * org导出
 *
 * <AUTHOR>
 * @date 2022/05/24
 */
@Setter
@Getter
@ToString
@CrmExcelExport
public class OrgExportVo extends CrmExcelVo {

  /**
   * 组织编码
   */
  @CrmExcelColumn("组织编码")
  private String orgCode;

  /**
   * 组织名称
   */
  @CrmExcelColumn("组织名称")
  private String orgName;

  /**
   * 组织层级--名称（orgType）
   */
  @CrmExcelColumn("组织层级")
  private String levelName;

  /**
   * 上级组织
   */
  @CrmExcelColumn("上级组织")
  private String parentName;

  /**
   * 启用状态
   */
  @CrmExcelColumn("启用状态")
  private String enableStatus;

  /**
   * 创建人
   */
  @CrmExcelColumn("创建人")
  private String createName;

  /**
   * 创建时间
   */
  @CrmExcelColumn("创建时间")
  private String createTime;

}
