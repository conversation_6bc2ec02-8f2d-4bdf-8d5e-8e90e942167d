package com.biz.crm.mdm.admin.web.report.position.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.admin.web.report.position.dto.ChildPositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.dto.HasRelateCurrentRolePositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.dto.LevelPositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.dto.NotRelateAnyRolePositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.dto.NotRelateCurrentRolePositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.dto.OrgPositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.dto.ParentPositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.dto.PositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.dto.SelectPositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.dto.UserSelectParentPositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.dto.UserSelectPositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.service.PositionPageVoService;
import com.biz.crm.mdm.admin.web.report.position.vo.PositionPageVo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 职位管理: PositionPageVo: 职位列表相关
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Slf4j
@RestController
@RequestMapping("/v1/position/position")
@Api(tags = "职位管理: PositionPageVo: 职位列表相关")
public class PositionPageVoController {

  @Autowired(required = false)
  private PositionPageVoService positionPageVoService;

  /**
   * 职位分页列表
   *
   * @param pageable 分页参数
   * @param dto      参数dto
   * @return 职位列表
   */
  @ApiOperation(value = "职位分页列表")
  @GetMapping("/findByConditions")
  public Result<Page<PositionPageVo>> findByConditions(@PageableDefault(50) Pageable pageable,
                                                       @ApiParam(name = "PositionPageDto", value = "分页Dto") PositionPageDto dto) {
    try {
      return Result.ok(this.positionPageVoService.findByConditions(pageable, dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 职位关联下属职位分页列表
   *
   * @param pageable 分页参数
   * @param dto      参数dto
   * @return 职位列表
   */
  @ApiOperation(value = "职位关联下属职位分页列表")
  @GetMapping("/findByChildPositionPageDto")
  public Result<Page<PositionPageVo>> findByChildPositionPageDto(@PageableDefault(50) Pageable pageable,
                                                                 @ApiParam(name = "ChildPositionPageDto", value = "分页Dto") ChildPositionPageDto dto) {
    try {
      return Result.ok(this.positionPageVoService.findByConditions(pageable, dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 职位级别关联职位分页列表
   *
   * @param pageable 分页参数
   * @param dto      参数dto
   * @return 职位列表
   */
  @ApiOperation(value = "职位级别关联职位分页列表")
  @GetMapping("/findByLevelPositionPageDto")
  public Result<Page<PositionPageVo>> findByLevelPositionPageDto(@PageableDefault(50) Pageable pageable,
                                                                 @ApiParam(name = "LevelPositionPageDto", value = "分页Dto") LevelPositionPageDto dto) {
    try {
      return Result.ok(this.positionPageVoService.findByConditions(pageable, dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 组织关联职位分页列表
   *
   * @param pageable 分页参数
   * @param dto      参数dto
   * @return 职位列表
   */
  @ApiOperation(value = "组织关联职位分页列表")
  @GetMapping("/findByOrgPositionPageDto")
  public Result<Page<PositionPageVo>> findByOrgPositionPageDto(@PageableDefault(50) Pageable pageable,
                                                               @ApiParam(name = "OrgPositionPageDto", value = "分页Dto") OrgPositionPageDto dto) {
    try {
      return Result.ok(this.positionPageVoService.findByConditions(pageable, dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 职位下拉框分页列表
   *
   * @param pageable 分页参数
   * @param dto      参数dto
   * @return 职位列表
   */
  @ApiOperation(value = "职位下拉框分页列表")
  @GetMapping("/findBySelectPositionPageDto")
  public Result<Page<PositionPageVo>> findBySelectPositionPageDto(@PageableDefault(50) Pageable pageable,
                                                                  @ApiParam(name = "SelectPositionPageDto", value = "分页Dto") SelectPositionPageDto dto) {
    try {
      List<String> selectedCodes = Lists.newArrayList();
      if (StringUtil.isNotEmpty(dto.getSelectedCode())){
        selectedCodes.add(dto.getSelectedCode());
      }
      if (CollectionUtil.isNotEmpty(dto.getSelectedCodes())){
        selectedCodes.addAll(dto.getSelectedCodes());
      }
      dto.setSelectedCodes(selectedCodes);
      return Result.ok(this.positionPageVoService.findByConditions(pageable, dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 未关联当前角色的职位分页列表
   *
   * @param pageable 分页参数
   * @param dto      参数dto
   * @return 职位列表
   */
  @ApiOperation(value = "未关联当前角色的职位分页列表")
  @GetMapping("/findByNotRelateCurrentRolePositionPageDto")
  public Result<Page<PositionPageVo>> findByConditions(@PageableDefault(50) Pageable pageable,
                                                       @ApiParam(name = "NotRelateCurrentRolePositionPageDto", value = "分页Dto") NotRelateCurrentRolePositionPageDto dto) {
    try {
      return Result.ok(this.positionPageVoService.findByConditions(pageable, dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 未关联任何角色的职位分页列表
   *
   * @param pageable 分页参数
   * @param dto      参数dto
   * @return 职位列表
   */
  @ApiOperation(value = "未关联任何角色的职位分页列表")
  @GetMapping("/findByNotRelateAnyRolePositionPageDto")
  public Result<Page<PositionPageVo>> findByNotRelateAnyRolePositionPageDto(@PageableDefault(50) Pageable pageable,
                                                                            @ApiParam(name = "NotRelateAnyRolePositionPageDto", value = "分页Dto") NotRelateAnyRolePositionPageDto dto) {
    try {
      return Result.ok(this.positionPageVoService.findByConditions(pageable, dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 已关联当前角色的职位分页列表
   *
   * @param pageable 分页参数
   * @param dto      参数dto
   * @return 职位列表
   */
  @ApiOperation(value = "已关联当前角色的职位分页列表")
  @GetMapping("/findByHasRelateCurrentRolePositionPageDto")
  public Result<Page<PositionPageVo>> findByHasRelateCurrentRolePositionPageDto(@PageableDefault(50) Pageable pageable,
                                                                                @ApiParam(name = "HasRelateCurrentRolePositionPageDto", value = "分页Dto") HasRelateCurrentRolePositionPageDto dto) {
    try {
      return Result.ok(this.positionPageVoService.findByConditions(pageable, dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 用户职位下拉框分页列表
   *
   * @param pageable 分页参数
   * @param dto      参数dto
   * @return 职位列表
   */
  @ApiOperation(value = "用户职位下拉框分页列表")
  @GetMapping("/findByUserSelectPositionPageDto")
  public Result<Page<PositionPageVo>> findByUserSelectPositionPageDto(@PageableDefault(50) Pageable pageable,
                                                                      @ApiParam(name = "UserSelectPositionPageDto", value = "分页Dto") UserSelectPositionPageDto dto) {
    try {
      return Result.ok(this.positionPageVoService.findByConditions(pageable, dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 用户父级职位下拉框分页列表
   *
   * @param pageable 分页参数
   * @param dto      参数dto
   * @return 职位列表
   */
  @ApiOperation(value = "用户父级职位下拉框分页列表")
  @GetMapping("/findByUserSelectParentPositionPageDto")
  public Result<Page<PositionPageVo>> findByUserSelectParentPositionPageDto(@PageableDefault(50) Pageable pageable,
                                                                            @ApiParam(name = "UserSelectParentPositionPageDto", value = "分页Dto") UserSelectParentPositionPageDto dto) {
    try {
      List<String> selectedCodes = Lists.newArrayList();
      if (StringUtil.isNotEmpty(dto.getSelectedCode())){
        selectedCodes.add(dto.getSelectedCode());
      }
      if (CollectionUtil.isNotEmpty(dto.getSelectedCodes())){
        selectedCodes.addAll(dto.getSelectedCodes());
      }
      dto.setSelectedCodes(selectedCodes);
      return Result.ok(this.positionPageVoService.findByConditions(pageable, dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 上级职位分页列表
   *
   * @param pageable 分页参数
   * @param dto      参数dto
   * @return 职位列表
   */
  @ApiOperation(value = "上级职位分页列表")
  @GetMapping("/findByParentPositionPageDto")
  public Result<Page<PositionPageVo>> findByParentPositionPageDto(@PageableDefault(50) Pageable pageable,
                                                                  @ApiParam(name = "ParentPositionPageDto", value = "分页Dto") ParentPositionPageDto dto) {
    try {
      return Result.ok(this.positionPageVoService.findByConditions(pageable, dto));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
