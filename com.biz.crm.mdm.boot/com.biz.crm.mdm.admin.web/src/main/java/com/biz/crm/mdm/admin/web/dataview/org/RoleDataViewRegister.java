package com.biz.crm.mdm.admin.web.dataview.org;

import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @title RoleDataViewRegister
 * @date 2023/1/3 11:26
 * @description 角色管理数据视图
 */
@Component
public class RoleDataViewRegister implements DataviewRegister {
  @Override
  public String code() {
    return "mdm_role_management_dataview";
  }

  @Override
  public String desc() {
    return "MDM角色管理数据视图";
  }

  @Override
  public String buildSql() {
    return "select  * from engine_role where 1 = 1 and tenant_code=:tenantCode";
  }
}
