<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.mdm.admin.web.exports.customer.mapper.CustomerExportsMapper">


  <select id="getExportTotal" resultType="java.lang.Integer">
    select count(*)
    from
    mdm_customer a
    LEFT JOIN mdm_customer_r_org n ON (a.customer_code = n.customer_code AND a.tenant_code = n.tenant_code)
    <where>
    a.tenant_code=#{dto.tenantCode}
    <if test="dto.customerCode!=null and dto.customerCode!=''">
      and a.customer_code =#{dto.customerCode}
    </if>
    <if test="dto.customerName!=null and dto.customerName!=''">
      <bind name="likeCustomerName" value="'%' + dto.customerName + '%'"/>
      and a.customer_name like #{likeCustomerName}
    </if>
    <if test="dto.customerType!=null and dto.customerType!=''">
      and a.customer_type =#{dto.customerType}
    </if>
    <if test="dto.lockState!=null and dto.lockState!=''">
      and a.lock_state =#{dto.lockState}
    </if>
    <if test="dto.enableStatus!=null and dto.enableStatus!=''">
      and a.enable_status =#{dto.enableStatus}
    </if>
    </where>
  </select>

  <select id="findData"
          resultType="com.biz.crm.mdm.admin.web.exports.customer.model.CustomerCrmExportVo">
    select a.*,n.org_code
    from
    mdm_customer a
    LEFT JOIN mdm_customer_r_org n ON (a.customer_code = n.customer_code AND a.tenant_code = n.tenant_code)
    <where>
    a.tenant_code=#{dto.tenantCode}
    <if test="dto.customerCode!=null and dto.customerCode!=''">
      and a.customer_code =#{dto.customerCode}
    </if>
    <if test="dto.customerName!=null and dto.customerName!=''">
      <bind name="likeCustomerName" value="'%' + dto.customerName + '%'"/>
      and a.customer_name like #{likeCustomerName}
    </if>
    <if test="dto.customerType!=null and dto.customerType!=''">
      and a.customer_type =#{dto.customerType}
    </if>
    <if test="dto.lockState!=null and dto.lockState!=''">
      and a.lock_state =#{dto.lockState}
    </if>
    <if test="dto.enableStatus!=null and dto.enableStatus!=''">
      and a.enable_status =#{dto.enableStatus}
    </if>
    </where>
    order by a.create_time desc
    limit #{dto.offset},#{dto.limit}
  </select>
</mapper>