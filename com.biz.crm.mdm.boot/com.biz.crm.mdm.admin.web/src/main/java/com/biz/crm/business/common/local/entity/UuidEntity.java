package com.biz.crm.business.common.local.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

/**
 * 覆盖产品组
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/22 14:02
 */
@Data
@MappedSuperclass
public class UuidEntity {

    @Id
    @GeneratedValue(generator = "uuid")
    @TableId(type = IdType.ASSIGN_UUID)
    @GenericGenerator(name = "uuid", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", nullable = false, columnDefinition = "VARCHAR(32) COMMENT '主键'")
    private String id;
}
