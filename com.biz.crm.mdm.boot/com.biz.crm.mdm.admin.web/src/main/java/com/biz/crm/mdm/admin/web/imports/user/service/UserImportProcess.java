package com.biz.crm.mdm.admin.web.imports.user.service;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.base.util.NumberDealUtil;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.admin.web.imports.user.model.UserCrmImportVo;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.org.local.entity.OrgPosition;
import com.biz.crm.mdm.business.org.local.repository.OrgPositionRepository;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.position.level.sdk.service.PositionLevelVoService;
import com.biz.crm.mdm.business.position.level.sdk.vo.PositionLevelVo;
import com.biz.crm.mdm.business.position.local.entity.PositionEntity;
import com.biz.crm.mdm.business.position.local.entity.PositionRoleEntity;
import com.biz.crm.mdm.business.position.local.repository.PositionRepository;
import com.biz.crm.mdm.business.position.local.repository.PositionRoleRepository;
import com.biz.crm.mdm.business.position.local.service.PositionService;
import com.biz.crm.mdm.business.position.sdk.constant.PositionConstant;
import com.biz.crm.mdm.business.user.local.entity.UserEntity;
import com.biz.crm.mdm.business.user.local.entity.UserPositionEntity;
import com.biz.crm.mdm.business.user.local.repository.UserPositionRepository;
import com.biz.crm.mdm.business.user.local.repository.UserRepository;
import com.biz.crm.mdm.business.user.sdk.constant.UserConstant;
import com.biz.crm.mdm.business.user.sdk.enums.UserEmployeeStatusEnum;
import com.biz.crm.mdm.business.user.sdk.enums.UserEmployeeTypeEnum;
import com.biz.crm.mdm.business.user.sdk.enums.UserTypeEnum;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2023年09月08日 09:47:00
 */
@Component
public class UserImportProcess implements ImportProcess<UserCrmImportVo> {

    @Autowired(required = false)
    private UserVoService userVoService;

    @Autowired(required = false)
    private OrgVoService orgVoService;

    @Autowired(required = false)
    private PositionService positionService;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private GenerateCodeService generateCodeService;

    @Autowired(required = false)
    private PositionLevelVoService positionLevelVoService;

    @Autowired(required = false)
    private PositionRepository positionRepository;

    @Autowired(required = false)
    private UserRepository userRepository;

    @Autowired(required = false)
    private UserPositionRepository userPositionRepository;

    @Autowired(required = false)
    private PositionRoleRepository positionRoleRepository;

    @Autowired(required = false)
    private OrgPositionRepository orgPositionRepository;


    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, UserCrmImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = Maps.newHashMap();
        Map<String, Map<String, String>> dictMap = this.dictDataVoService.findReverseMapByDictTypeCodeList(
                Lists.newArrayList(DictConstant.IDENTITY_TYPE, DictConstant.RESIGNATION_SITUATION, DictConstant.GENDER));
        //基础验证和设置基础信息
        this.baseValidation(errMap, data, dictMap);
        if (CollectionUtil.isEmpty(errMap)) {
            //组织 职位 职位级别
            this.settingInfo(data);
        }
        return errMap;
    }

    /**
     * 构建关联信息
     *
     * @param data
     */
    private void settingInfo(LinkedHashMap<Integer, UserCrmImportVo> data) {
        if (CollectionUtil.isEmpty(data)) {
            return;
        }
        List<String> positionCodeList = generateCodeService.generateCode(PositionConstant.POSITION_CODE, data.size());
        List<String> userCodeList = generateCodeService.generateCode(UserConstant.USER_CODE, data.size());
        Map<String, List<UserCrmImportVo>> userImportList = data.values().stream()
                .filter(k -> StringUtil.isNotEmpty(k.getUserTypeImport()))
                .collect(Collectors.groupingBy(UserCrmImportVo::getUserTypeImport));
        List<UserEntity> userEntityList = Lists.newArrayList();
        List<PositionEntity> positionEntityList = Lists.newArrayList();
        List<OrgPosition> orgPositionList = Lists.newArrayList();
        List<UserPositionEntity> userPositionEntityList = Lists.newArrayList();
        List<PositionRoleEntity> positionRoleEntityList = Lists.newArrayList();
        AtomicInteger positionIndex = new AtomicInteger(0);
        AtomicInteger userCodeIndex = new AtomicInteger(0);
        userImportList.forEach((userType, list) -> {
            if (CollectionUtil.isEmpty(list)) {
                return;
            }
            AtomicInteger userIndex = new AtomicInteger(0);
            List<String> userNameList = generateCodeService.generateCodeNotDate(userType, list.size(), 6);
            list.forEach(item -> {
                String positionCode = positionCodeList.get(positionIndex.getAndIncrement());
                String useName = userNameList.get(userIndex.getAndIncrement());
                if (CollectionUtil.isNotEmpty(item.getRoleList())) {
                    item.getRoleList().forEach(role -> {
                        PositionRoleEntity positionRoleEntity = new PositionRoleEntity();
                        positionRoleEntity.setPositionCode(positionCode);
                        positionRoleEntity.setRoleCode(role);
                        positionRoleEntityList.add(positionRoleEntity);
                    });
                }
                UserPositionEntity userPositionEntity = new UserPositionEntity();
                userPositionEntity.setPrimaryFlag(true);
                userPositionEntity.setCurrentFlag(true);
                userPositionEntity.setUserName(useName);
                userPositionEntity.setPositionCode(positionCode);

                OrgPosition orgPosition = new OrgPosition();
                orgPosition.setOrgCode(item.getOrgCode());
                orgPosition.setPositionCode(positionCode);
                UserEntity userEntity = this.nebulaToolkitService.copyObjectByBlankList(item, UserEntity.class, HashSet.class, ArrayList.class);
                userEntity.setUserCode(userCodeList.get(userCodeIndex.getAndIncrement()));
                userEntity.setUserName(useName);

                PositionEntity positionEntity = new PositionEntity();
                positionEntity.setRuleCode(positionCode);
                positionEntity.setPositionCode(positionCode);
                positionEntity.setParentCode(item.getParentCode());
                positionEntity.setPositionLevelCode(item.getPositionLevelCode());
                positionEntity.setPositionName(item.getPositionName());

                userEntityList.add(userEntity);
                positionEntityList.add(positionEntity);
                orgPositionList.add(orgPosition);
                userPositionEntityList.add(userPositionEntity);
            });
        });
        positionRepository.saveBatch(positionEntityList);
        if (CollectionUtil.isNotEmpty(positionRoleEntityList)){
            positionRoleRepository.saveBatch(positionRoleEntityList);
        }
        userRepository.saveBatch(userEntityList);
        userPositionRepository.saveBatch(userPositionEntityList);
        orgPositionRepository.saveBatch(orgPositionList);
        positionService.updateRuleCode();
    }

    /**
     * 基础验证和设置基础信息
     *
     * @param errMap
     * @param data
     * @param dictMap
     */
    private void baseValidation(Map<Integer, String> errMap, LinkedHashMap<Integer, UserCrmImportVo> data,
                                Map<String, Map<String, String>> dictMap) {
        Assert.notEmpty(data, "导入的数据不能为空!");
        String startTime = DateUtil.dateStrNowAll();
        List<String> positionLevelCodeList = data.values().stream().map(UserCrmImportVo::getPositionLevelCode)
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<PositionLevelVo> positionLevelVoList = positionLevelVoService.findByIdsOrCodes(null, positionLevelCodeList);
        Map<String, Integer> positionLevelMap = positionService.getCountByPositionLevelCodes(positionLevelCodeList);
        Map<String, PositionLevelVo> positionLevelVoMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(positionLevelVoList)) {
            positionLevelVoMap.putAll(positionLevelVoList.stream()
                    .filter(k -> StringUtil.isNotEmpty(k.getPositionLevelCode()))
                    .collect(Collectors.toMap(PositionLevelVo::getPositionLevelCode, v -> v, (n, o) -> n)));
        }

        List<String> userPhoneList = data.values().stream().map(UserCrmImportVo::getUserPhone)
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<UserEntity> userEntityList = userRepository.findByUserPhoneList(userPhoneList);
        Map<String, UserEntity> userEntityMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(userEntityList)) {
            userEntityMap.putAll(userEntityList.stream()
                    .filter(k -> StringUtil.isNotEmpty(k.getUserPhone()))
                    .collect(Collectors.toMap(UserEntity::getUserPhone, v -> v, (n, o) -> n)));
        }
        List<String> identityCardNumberList = data.values().stream().map(UserCrmImportVo::getIdentityCardNumber)
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<UserEntity> identityCardNumberEntityList = userRepository.findByIdentityCardNumberList(identityCardNumberList);
        Map<String, UserEntity> identityCardNumberMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(identityCardNumberEntityList)) {
            identityCardNumberMap.putAll(identityCardNumberEntityList.stream()
                    .filter(k -> StringUtil.isNotEmpty(k.getIdentityCardNumber()))
                    .collect(Collectors.toMap(UserEntity::getIdentityCardNumber, v -> v, (n, o) -> n)));
        }

        List<String> userNameList = data.values().stream().map(UserCrmImportVo::getParentUserName)
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, String> primaryPositionMap = userVoService.findPrimaryPositionByUserNames(userNameList);

        List<String> orgCodeList = data.values().stream().map(UserCrmImportVo::getOrgCode)
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, OrgVo> orgMap = orgVoService.findOrgVoByOrgCodes(orgCodeList);
        String business = "业代";
        String guideBuy = "导购";
        Map<String, Integer> userPhoneValMap = Maps.newHashMap();
        Map<String, Integer> identityCardNumberValMap = Maps.newHashMap();
        data.forEach((rowNum, item) -> {
            this.validateIsTrue(StringUtil.isNotEmpty(item.getFullName()), "用户姓名必填||");
            this.validateIsTrue(StringUtil.isNotEmpty(item.getUserTypeImportStr()), "用户类型必填||");
            String positionLevelCode = item.getPositionLevelCode();
            this.validateIsTrue(StringUtil.isNotEmpty(positionLevelCode), "职位级别编码必填||");
            if (StringUtil.isNotEmpty(positionLevelCode)) {
                PositionLevelVo positionLevelVo = positionLevelVoMap.get(positionLevelCode);
                this.validateIsTrue(Objects.nonNull(positionLevelVo), "职位级别编码不存在||");
                if (Objects.nonNull(positionLevelVo)) {
                    String positionLevelName = positionLevelVo.getPositionLevelName();
                    item.setPositionLevelName(positionLevelName);
                    Integer index = positionLevelMap.getOrDefault(positionLevelCode, 0) + 1;
                    item.setPositionName(StringUtils.stripToEmpty(positionLevelName) + "_" + index);
                    item.setRoleList(positionLevelVo.getRoleList());
                    positionLevelMap.put(positionLevelCode, index);
                }

            }
            this.validateIsTrue(StringUtil.isNotEmpty(item.getParentUserName()), "上级用户编码必填||");
            if (StringUtil.isNotEmpty(item.getParentUserName())) {
                String name = primaryPositionMap.get(item.getParentUserName());
                this.validateIsTrue(StringUtil.isNotEmpty(name), "上级用户不存在或上级用户无主职位||");
                item.setParentCode(name);
            }
            this.validateIsTrue(StringUtil.isNotEmpty(item.getOrgCode()), "营销组织编码必填||");
            if (StringUtil.isNotEmpty(item.getOrgCode())) {
                this.validateIsTrue(orgMap.containsKey(item.getOrgCode()), "营销组织编码不存在||");
            }
            this.validateIsTrue(StringUtil.isNotEmpty(item.getUserPhone()), "联系电话必填||");
            if (StringUtil.isNotEmpty(item.getUserPhone())) {
                this.validateIsTrue(item.getUserPhone().startsWith("1"), "联系电话不合法,需为手机号||");
                this.validateIsTrue(item.getUserPhone().length() == 11, "联系电话不合法,需为11位手机号||");
                this.validateIsTrue(!userEntityMap.containsKey(item.getUserPhone()), "联系电话已存在||");
                this.validateIsTrue(!userPhoneValMap.containsKey(item.getUserPhone()), "联系电话已存在第["
                        + userPhoneValMap.get(item.getUserPhone()) + "]行||");
                if (!userPhoneValMap.containsKey(item.getUserPhone())) {
                    userPhoneValMap.put(item.getUserPhone(), rowNum);
                }
            }
            if (StringUtil.isNotEmpty(item.getIdentityCardNumber())) {
                this.validateIsTrue(!identityCardNumberMap.containsKey(item.getIdentityCardNumber()), "身份证号已存在||");
                this.validateIsTrue(!identityCardNumberValMap.containsKey(item.getIdentityCardNumber()), "身份证号已存在第["
                        + identityCardNumberValMap.get(item.getIdentityCardNumber()) + "]行||");
                if (!identityCardNumberValMap.containsKey(item.getIdentityCardNumber())) {
                    identityCardNumberValMap.put(item.getIdentityCardNumber(), rowNum);
                }
            }
            String errorMsg = NumberDealUtil.validateNumberStrAndSetErrorMsg(item.getEmploymentAgeStr(), "入职年龄", false, item::setEmploymentAge, Integer.class);
            this.validateIsTrue(StringUtil.isEmpty(errorMsg), errorMsg);
            errorMsg = NumberDealUtil.validateNumberStrAndSetErrorMsg(item.getServiceYearsStr(), "服务年限", false, item::setServiceYears, Integer.class);
            this.validateIsTrue(StringUtil.isEmpty(errorMsg), errorMsg);
            errorMsg = NumberDealUtil.validateNumberStrAndSetErrorMsg(item.getBasicWagesStr(), "基本工资", false, item::setBasicWages, BigDecimal.class);
            this.validateIsTrue(StringUtil.isEmpty(errorMsg), errorMsg);
            if (StringUtil.isNotEmpty(item.getUserTypeImportStr())) {
                this.validateIsTrue(StringUtils.equalsAny(item.getUserTypeImportStr(), business, guideBuy), "用户类型不合法||");
                if (StringUtils.equals(item.getUserTypeImportStr(), business)) {
                    item.setUserTypeImport("Y");
                } else if (StringUtils.equals(item.getUserTypeImportStr(), guideBuy)) {
                    item.setUserTypeImport("D");
                }
            }
            if (StringUtil.isNotEmpty(item.getResignationSituationStr())) {
                String dictValue = this.findDictCode(dictMap.get(DictConstant.RESIGNATION_SITUATION), item.getResignationSituationStr());
                this.validateIsTrue(StringUtil.isNotEmpty(dictValue), "离职情况不合法,数据字典[resignation_situation]");
                item.setResignationSituation(dictValue);
            }
            if (StringUtil.isNotEmpty(item.getIdentityTypeStr())) {
                String dictValue = this.findDictCode(dictMap.get(DictConstant.IDENTITY_TYPE), item.getIdentityTypeStr());
                this.validateIsTrue(StringUtil.isNotEmpty(dictValue), "户口不合法,数据字典[identity_type]");
                item.setIdentityType(dictValue);
            }
//            if (StringUtil.isNotEmpty(item.getEmploymentSource())) {
//                String dictValue = this.findDictCode(dictMap.get(DictConstant.EMPLOYMENT_SOURCE), item.getEmploymentSource());
//                this.validateIsTrue(StringUtil.isNotEmpty(dictValue), "顾问来源不合法,数据字典[employment_source]");
//                item.setEmploymentSource(dictValue);
//            }
            if (StringUtil.isNotEmpty(item.getGenderStr())) {
                String dictValue = this.findDictCode(dictMap.get(DictConstant.GENDER), item.getGenderStr());
                this.validateIsTrue(StringUtil.isNotEmpty(dictValue), "性别不合法,数据字典[gender]");
                item.setGender(dictValue);
            }

            //入职日期
            if (StringUtil.isNotEmpty(item.getEmploymentTime())) {
                try {
                    Date date = DateUtil.parseDate(item.getEmploymentTime(), DateUtil.DEFAULT_YEAR_MONTH_DAY);
                    this.validateIsTrue(DateUtil.format(date, DateUtil.DEFAULT_YEAR_MONTH_DAY).equals(item.getEmploymentTime()),
                            "入职日期格式不正确[yyyy-MM-dd]");
                } catch (Exception e) {
                    this.validateIsTrue(false, "入职日期格式不正确[yyyy-MM-dd]");
                }
            }
            //离职日期
            if (StringUtil.isNotEmpty(item.getResignationTime())) {
                try {
                    Date date = DateUtil.parseDate(item.getResignationTime(), DateUtil.DEFAULT_YEAR_MONTH_DAY);
                    this.validateIsTrue(DateUtil.format(date, DateUtil.DEFAULT_YEAR_MONTH_DAY).equals(item.getResignationTime()),
                            "离职日期格式不正确[yyyy-MM-dd]");
                } catch (Exception e) {
                    this.validateIsTrue(false, "离职日期格式不正确[yyyy-MM-dd]");
                }
            }

            //设置默认值
            item.setUserType(UserTypeEnum.USER.getCode());
            item.setEmployeeType(UserEmployeeTypeEnum.OUT_STAFF.getCode());
            item.setEmployeeStatus(UserEmployeeStatusEnum.STATUS_1.getCode());
            item.setStartTime(startTime);
            item.setEndTime(DateUtil.MAX_END_TIME);
            item.setUserPassword(UserConstant.DEFAULT_PASSWORD);
            item.setForceChangePassword(true);
            item.setLockState(EnableStatusEnum.ENABLE.getCode());

            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        });
    }

    @Override
    public Class<UserCrmImportVo> findCrmExcelVoClass() {
        return UserCrmImportVo.class;
    }

    @Override
    public String getTemplateCode() {
        return "mdm_user_import";
    }

    @Override
    public String getTemplateName() {
        return "企业用户导入";
    }

    /**
     * 获取字典对应的code值
     *
     * @param typeCode
     * @return
     */
    private String findDictCode(Map<String, String> map, String typeCode) {
        if (StringUtil.isEmpty(typeCode)
                || CollectionUtil.isEmpty(map)) {
            return null;
        }
        return map.get(typeCode);
    }

    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }
}
