package com.biz.crm.mdm.admin.web.imports.org.service;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.admin.web.imports.org.model.CostCenterImportVo;
import com.biz.crm.mdm.business.cost.local.entity.MdmCostCenter;
import com.biz.crm.mdm.business.cost.local.entity.MdmCostCenterOrg;
import com.biz.crm.mdm.business.cost.local.repository.MdmCostCenterOrgRepository;
import com.biz.crm.mdm.business.cost.local.repository.MdmCostCenterRepository;
import com.biz.crm.mdm.business.org.local.entity.Org;
import com.biz.crm.mdm.business.org.local.repository.OrgRepository;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 成本中心和组织关系导入
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/6/18 21:57
 */
@Component
public class CostCenterImportProcess implements ImportProcess<CostCenterImportVo> {


    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private OrgRepository orgRepository;

    @Autowired(required = false)
    private MdmCostCenterRepository mdmCostCenterRepository;

    @Autowired(required = false)
    private MdmCostCenterOrgRepository mdmCostCenterOrgRepository;


    @Override
    public boolean importBeforeValidationFlag() {
        //开启新导入逻辑
        return true;
    }

    /**
     * 数据校验
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, CostCenterImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        if (CollectionUtil.isEmpty(data)) {
            throw new IllegalArgumentException("导入数据不能为空!");
        }
        Map<Integer, String> errorMsgMap = Maps.newHashMap();
        Set<String> repeatValSet = Sets.newHashSet();
        List<String> costCenterCodeList = data.values().stream().filter(k -> StringUtil.isNotEmpty(k.getCostCenterCode()))
                .map(CostCenterImportVo::getCostCenterCode).distinct().collect(Collectors.toList());
        List<String> orgCodeList = data.values().stream().filter(k -> StringUtil.isNotEmpty(k.getOrgCode()))
                .map(CostCenterImportVo::getOrgCode).distinct().collect(Collectors.toList());
        Map<String, Org> orgMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(orgCodeList)) {
            List<Org> list = this.orgRepository.findByOrgCodes(orgCodeList, paramsVo.getTenantCode());
            if (CollectionUtil.isNotEmpty(list)) {
                orgMap.putAll(list.stream().filter(k -> StringUtil.isNotEmpty(k.getOrgCode()))
                        .collect(Collectors.toMap(Org::getOrgCode, v -> v, (n, o) -> n)));
            }
        }
        Map<String, MdmCostCenter> materialEntityMap = Maps.newHashMap();
        Set<String> costCenterOrgDataSet = Sets.newHashSet();
        if (CollectionUtil.isNotEmpty(costCenterCodeList)) {
            List<MdmCostCenter> list = this.mdmCostCenterRepository.findAllByCodes(costCenterCodeList);
            List<MdmCostCenterOrg> costCenterOrgs = this.mdmCostCenterOrgRepository.findByCostCenterCodes(costCenterCodeList);
            if (CollectionUtil.isNotEmpty(list)) {
                materialEntityMap.putAll(list.stream().filter(k -> StringUtil.isNotEmpty(k.getCostCenterCode()))
                        .collect(Collectors.toMap(MdmCostCenter::getCostCenterCode, v -> v, (n, o) -> n)));
            }
            if (CollectionUtil.isNotEmpty(costCenterOrgs)) {
                costCenterOrgDataSet.addAll(costCenterOrgs.stream()
                        .filter(k -> StringUtil.isNotEmpty(k.getCostCenterCode()))
                        .filter(k -> StringUtil.isNotEmpty(k.getOrgCode()))
                        .map(v -> v.getCostCenterCode() + v.getOrgCode())
                        .collect(Collectors.toSet()));
            }
        }
        data.forEach((index, item) -> {
            StringBuilder errorMsg = new StringBuilder();
            String orgCode = item.getOrgCode();
            if (StringUtil.isEmpty(orgCode)) {
                errorMsg.append("营销组织编码不能为空!||");
            } else {
                if (CollectionUtil.isNotEmpty(orgMap)) {
                    Org org = orgMap.get(orgCode);
                    if (Objects.isNull(org)) {
                        errorMsg.append("营销组织编码在营销组织主数中不存在!||");
                    } else {
                        if (DelFlagStatusEnum.NORMAL.getCode().equals(org.getDelFlag())) {
                            errorMsg.append("营销组织编码已被逻辑删除!||");
                        }
                    }
                }
            }

            String costCenterCode = item.getCostCenterCode();
            if (StringUtil.isEmpty(costCenterCode)) {
                errorMsg.append("成本中心编码不能为空!||");
            } else {
                if (CollectionUtil.isNotEmpty(materialEntityMap)) {
                    MdmCostCenter costCenter = materialEntityMap.get(costCenterCode);
                    if (Objects.isNull(costCenter)) {
                        errorMsg.append("成本中心编码在成本中心主数中不存在!||");
                    } else {
                        if (DelFlagStatusEnum.NORMAL.getCode().equals(costCenter.getDelFlag())) {
                            errorMsg.append("成本中心编码已被逻辑删除!||");
                        }
                    }
                }
            }
            if (StringUtil.isNotEmpty(costCenterCode)
                    && StringUtil.isNotEmpty(orgCode)) {
                if (repeatValSet.contains(costCenterCode + orgCode)) {
                    errorMsg.append("成本中心编码和组织重复!||");
                } else {
                    repeatValSet.add(costCenterCode + orgCode);
                }
                if (costCenterOrgDataSet.contains(costCenterCode + orgCode)) {
                    errorMsg.append("已存在成本中心编码和组织关系!||");
                }
            }

            if (StringUtil.isNotEmpty(errorMsg.toString())) {
                errorMsgMap.put(index, errorMsg.toString());
            }

        });
        //只做数据校验
        return errorMsgMap;
    }

    /**
     * 数据存储
     *
     * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
     * @param paramsVo 任务公共参数
     * @param params   导入任务自定义参数
     * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, CostCenterImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        if (CollectionUtil.isEmpty(data)) {
            throw new IllegalArgumentException("导入数据不能为空!");
        }
        List<MdmCostCenterOrg> entityList = (List<MdmCostCenterOrg>) this.nebulaToolkitService.copyCollectionByWhiteList(data.values(), CostCenterImportVo.class,
                MdmCostCenterOrg.class, HashSet.class, ArrayList.class);
        String tenantCode = TenantUtils.getTenantCode();
        entityList.forEach(item -> {
            item.setTenantCode(tenantCode);
        });
        mdmCostCenterOrgRepository.saveBatchXml(entityList);
        return null;
    }

    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, CostCenterImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        //旧逻辑就不再实现
        return null;
    }


    @Override
    public Class findCrmExcelVoClass() {
        return CostCenterImportVo.class;
    }

    @Override
    public String getTemplateCode() {
        return "MDM_COST_CENTRE_ORG_IMPORT";
    }

    @Override
    public String getTemplateName() {
        return "MDM成本中心与营销组织关系导入";
    }

    @Override
    public String getBusinessCode() {
        return "MDM_COST_CENTRE_ORG_IMPORT";
    }

    @Override
    public String getBusinessName() {
        return "MDM成本中心与营销组织关系导入";
    }


}
