package com.biz.crm.mdm.admin.web.report.mars.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.mars.dto.MarsPageDto;
import com.biz.crm.mdm.admin.web.report.mars.repository.MarsPageVoRepository;
import com.biz.crm.mdm.admin.web.report.mars.service.MarsPageVoService;
import com.biz.crm.mdm.admin.web.report.mars.vo.MarsPageVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.competence.local.entity.CompetenceEntity;
import com.bizunited.nebula.competence.local.repository.CompetenceRepository;
import com.bizunited.nebula.mars.sdk.register.SelectAuthorityModeGroupRegister;
import com.bizunited.nebula.mars.sdk.register.SelectAuthorityModeRegister;
import com.bizunited.nebula.mars.sdk.register.SelectScopeRegister;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 数据视图分页服务接口实现类
 * @date 2023/11/21
 */
@Slf4j
@Service
public class MarsPageVoServiceImpl implements MarsPageVoService {

  @Autowired
  private MarsPageVoRepository marsPageVoRepository;
  @Autowired
  private CompetenceRepository competenceRepository;
  @Autowired(required = false)
  private List<SelectScopeRegister> selectScopeRegisters;
  @Autowired(required = false)
  private List<SelectAuthorityModeRegister> selectAuthorityModeRegisters;
  @Autowired(required = false)
  private List<SelectAuthorityModeGroupRegister> selectAuthorityModeGroupRegisters;
  @Override
  @Transactional
  public Page<MarsPageVo> findByMarsPageDto(Pageable pageable, MarsPageDto dto) {
    pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
    dto = Optional.ofNullable(dto).orElse(new MarsPageDto());
    dto.setTenantCode(TenantUtils.getTenantCode());
    Page<MarsPageVo> resultPage = this.marsPageVoRepository.findByMarsPageDto(pageable, dto);
    if (CollectionUtils.isNotEmpty(resultPage.getRecords())) {
      Set<String> competenceCodes = resultPage.getRecords().stream().map(MarsPageVo::getCompetenceCode)
          .collect(Collectors.toSet());
      List<CompetenceEntity> competenceEntities = this.competenceRepository.findByCodes(
          Lists.newArrayList(competenceCodes));
      competenceEntities = ObjectUtils.defaultIfNull(competenceEntities, Lists.newArrayList());
      Map<String, CompetenceEntity> competenceEntityMap = competenceEntities.stream()
          .collect(Collectors.toMap(CompetenceEntity::getCode, t -> t, (a, b) -> a));
      resultPage.getRecords().forEach(marsPageVo -> {
        marsPageVo.setCompetencePath(this.buildCompetencePath(competenceEntityMap.get(marsPageVo.getCompetenceCode())));
      });
      this.buildModeInfo(resultPage.getRecords());
      this.buildGroupInfo(resultPage.getRecords());
      this.buildScopeInfo(resultPage.getRecords());
    }
    return resultPage;
  }

  /**
   * 构建数据权限方式信息
   *
   * @param marsList 数据权限列表
   */
  private void buildModeInfo(List<MarsPageVo> marsList) {
    Map<String, SelectAuthorityModeRegister> modeMap = CollectionUtils.isNotEmpty(selectAuthorityModeRegisters) ? selectAuthorityModeRegisters.stream()
        .collect(Collectors.toMap(SelectAuthorityModeRegister::modeKey, t -> t, (a, b) -> a)) : Maps.newHashMap();
    marsList.forEach(marsPageVo -> {
      SelectAuthorityModeRegister modeRegister = modeMap.get(marsPageVo.getSelectModeKey());
      SelectAuthorityModeRegister defaultModeRegister = modeMap.get(marsPageVo.getDefaultSelectModeKey());
      if (Objects.nonNull(modeRegister)) {
        if ("defaultIdSelectAuthorityModeGroupRegisterForAnyone".equals(modeRegister.groupCode())) {
          marsPageVo.setSelectModeName(null);
        } else {
          marsPageVo.setSelectModeName(modeRegister.modeName());
        }
      }
      if (Objects.nonNull(defaultModeRegister) && "defaultIdSelectAuthorityModeGroupRegisterForAnyone".equals(defaultModeRegister.groupCode())) {
          marsPageVo.setDefaultSelectModeName(defaultModeRegister.modeName());
      } else {
        marsPageVo.setDefaultSelectModeName("按授权维度选择");
      }
    });
  }
  /**
   * 构建数据权限分组信息
   *
   * @param marsList 数据权限列表
   */
  private void buildGroupInfo(List<MarsPageVo> marsList) {
    Map<String, SelectAuthorityModeGroupRegister> groupMap = CollectionUtils.isNotEmpty(selectAuthorityModeGroupRegisters) ? selectAuthorityModeGroupRegisters.stream()
        .collect(Collectors.toMap(SelectAuthorityModeGroupRegister::groupCode, t -> t, (a, b) -> a)) : Maps.newHashMap();
    marsList.forEach(marsPageVo -> {
      SelectAuthorityModeGroupRegister groupRegister = groupMap.get(marsPageVo.getSelectModeGroupCode());
      if (Objects.nonNull(groupRegister)) {
        if ("defaultIdSelectAuthorityModeGroupRegisterForAnyone".equals(groupRegister.groupCode())) {
          marsPageVo.setSelectModeGroupName("全部数据");
        } else {
          marsPageVo.setSelectModeGroupName(groupRegister.groupName());
        }
      }
    });
  }
  /**
   * 构建数据权限范围信息
   *
   * @param marsList 数据权限列表
   */
  private void buildScopeInfo(List<MarsPageVo> marsList) {
    Map<String, SelectScopeRegister> scopeMap = CollectionUtils.isNotEmpty(selectScopeRegisters) ? selectScopeRegisters.stream()
        .collect(Collectors.toMap(SelectScopeRegister::scopeKey, t -> t, (a, b) -> a)) : Maps.newHashMap();
    marsList.forEach(marsPageVo -> {
      SelectScopeRegister selectScopeRegister = scopeMap.get(marsPageVo.getScopeKey());
      if (Objects.nonNull(selectScopeRegister)) {
        marsPageVo.setScopeName(selectScopeRegister.selectName());
      }
    });
  }
  /**
   * 构建菜单路径
   *
   * @param competenceEntity 菜单
   * @return 菜单路径
   */
  private String buildCompetencePath(CompetenceEntity competenceEntity) {
    if (Objects.isNull(competenceEntity)) {
      return "";
    }
    String resultPath = competenceEntity.getComment();
    if (Objects.nonNull(competenceEntity.getParent())) {
      resultPath = String.format("%s-%s", buildCompetencePath(competenceEntity.getParent()), resultPath);
    }
    return resultPath;
  }
}
