package com.biz.crm.mdm.admin.web.dataview.destination;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * MDM送达方数据视图
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/24 17:17
 */
@Component
public class DestinationDataViewRegister implements DataviewRegister {
    @Override
    public String code() {
        return "mdm_destination_data_view";
    }

    @Override
    public String desc() {
        return "MDM送达方数据视图";
    }

    @Override
    public String buildSql() {
        return "select t.*,  " +
                "    group_concat( mc.customer_code) customer_codes , " +
                "    group_concat( mc.customer_name) customer_names  " +
                "    from mdm_destination t  " +
                "    left join mdm_customer_destination mcd on t.destination_code = mcd.destination_code " +
                "    left join mdm_customer mc on mc.customer_code = mcd.customer_code " +
                "   and mc.del_flag = '" + DelFlagStatusEnum.NORMAL.getCode() + "'" +
                "    where t.del_flag = '" + DelFlagStatusEnum.NORMAL.getCode() + "' " +
                "    and t.tenant_code = :tenantCode "+
                "    group by t.id ";
    }
}
