package com.biz.crm.mdm.admin.web.imports.price.service;

import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.admin.web.imports.price.modle.PriceProCusCrmImportVo;
import com.biz.crm.mdm.business.customer.local.entity.CustomerEntity;
import com.biz.crm.mdm.business.customer.local.service.CustomerService;
import com.biz.crm.mdm.business.price.local.entity.Price;
import com.biz.crm.mdm.business.price.local.entity.PriceDimension;
import com.biz.crm.mdm.business.price.local.entity.PriceType;
import com.biz.crm.mdm.business.price.local.service.PriceService;
import com.biz.crm.mdm.business.price.local.service.PriceTypeService;
import com.biz.crm.mdm.business.price.sdk.constant.PriceConstant;
import com.biz.crm.mdm.business.price.sdk.enums.PriceDimensionEnum;
import com.biz.crm.mdm.business.price.sdk.register.PriceDimensionRegister;
import com.biz.crm.mdm.business.product.local.entity.Product;
import com.biz.crm.mdm.business.product.local.service.ProductService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 价格维护导入 维度：客户商品
 * <AUTHOR>
 * @describe:
 * @createTime 2022年05月24日 14:30:00
 */
@Component
@Slf4j
public class PriceProCusImportProcess  implements ImportProcess<PriceProCusCrmImportVo> {

  @Autowired(required = false)
  private PriceService priceService;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @Autowired(required = false)
  private List<PriceDimensionRegister> priceDimensionRegisters;

  @Autowired(required = false)
  private ProductService productService;

  @Autowired(required = false)
  private CustomerService customerService;

  @Autowired(required = false)
  private PriceTypeService priceTypeService;

  /**
   * 校验
   * @param vo
   */
  private void validate(PriceProCusCrmImportVo vo) {
    boolean f = StringUtils.isNoneBlank(vo.getTypeCode(), vo.getRelateCode(), vo.getProductCode());
    Validate.isTrue(f,"价格类型编码、客户编码、商品编码不能为空");
    Validate.notNull(vo.getPrice(),"价格不能为空");
    Validate.notNull(vo.getBeginTime(),"开始时间不能为空");
    Validate.notNull(vo.getEndTime(),"结束时间不能为空");
    Validate.isTrue(vo.getBeginTime().compareTo(vo.getEndTime())<0,"开始时间必须大于结束时间");
    //校验价格类型
    PriceType detailByTypeCode = priceTypeService.findDetailByTypeCode(vo.getTypeCode());
    Validate.notNull(detailByTypeCode,"尚无当前价格类型编码");
    //设置价格维度编码
    HashMap<Integer, PriceDimensionEnum> map = new HashMap<>();
    for (PriceDimensionRegister priceDimensionRegister : priceDimensionRegisters) {
      String code = priceDimensionRegister.getCode();
      if (PriceDimensionEnum.PRODUCT.getDictCode().equals(code)) {
        map.put(priceDimensionRegister.sort(), PriceDimensionEnum.PRODUCT);
      }
      if (PriceDimensionEnum.CUSTOMER.getDictCode().equals(code)) {
        map.put(priceDimensionRegister.sort(), PriceDimensionEnum.CUSTOMER);
      }
    }
    Validate.notEmpty(map, "未查询到价格维度");
    List<Integer> collect = map.keySet().stream().sorted().collect(Collectors.toList());
    String typeCode = vo.getTypeCode();
    String typeName = "";
    //拼接
    for (Integer integer : collect) {
      PriceDimensionEnum priceDimensionEnum = map.get(integer);
      typeCode = typeCode.concat(PriceConstant.SEPARATOR).concat(priceDimensionEnum.getDictCode());
      if (StringUtils.isBlank(typeName)){
        typeName=typeName.concat(priceDimensionEnum.getValue());
      }else {
        typeName = typeName.concat(PriceConstant.NAME_SEPARATOR).concat(priceDimensionEnum.getValue());
      }
    }
    //设置价格维度编码
    vo.setTypeDetailCode(typeCode);
    vo.setTypeDetailName(typeName);
  }

  @Override
  @Transactional
  public Map<Integer, String> execute(
      LinkedHashMap<Integer, PriceProCusCrmImportVo> data,
      TaskGlobalParamsVo paramsVo,
      Map<String, Object> params) {
    final Optional<PriceProCusCrmImportVo> first = data.values().stream().findFirst();
    if (!first.isPresent()) {
      return null;
    }
    PriceProCusCrmImportVo vo = first.get();
    this.validate(vo);

    Price price =
        this.nebulaToolkitService.copyObjectByWhiteList(
            vo, Price.class, HashSet.class, ArrayList.class);
    // 设置价格维度
    List<PriceDimension> dimensionList = new ArrayList<>();
    PriceDimension product = new PriceDimension();
    product.setDimensionCode(PriceDimensionEnum.PRODUCT.getDictCode());
    product.setRelateCode(vo.getProductCode());
    // 追加商品信息
    addProductMsg(product);
    PriceDimension customer = new PriceDimension();
    customer.setDimensionCode(PriceDimensionEnum.CUSTOMER.getDictCode());
    customer.setRelateCode(vo.getRelateCode());
    // 添加客户信息
    addCustomerMsg(customer);
    dimensionList.add(product);
    dimensionList.add(customer);
    price.setDimensionList(dimensionList);
    this.priceService.create(price);
    return null;
  }

  /**
   * 添加商品信息
   * @param product
   */
  private void addProductMsg(PriceDimension product) {
    Product byProductCode = this.productService.findByProductCode(product.getRelateCode());
    Validate.notNull(byProductCode,"["+product.getRelateCode()+"未查询到商品信息");
    product.setRelateName(byProductCode.getProductName());
  }

  /**
   * 添加终端信息
   * @param
   */
  private void addCustomerMsg(PriceDimension customer) {
    List<String> list =new ArrayList<>();
    list.add(customer.getRelateCode());
    List<CustomerEntity> byCustomerCodes = this.customerService.findByCustomerCodes(list);
    Validate.notEmpty(byCustomerCodes,"["+customer.getRelateCode()+"未查询终端信息");
    Validate.notNull(byCustomerCodes.get(0),"["+customer.getRelateCode()+"未查询到终端信息");
    customer.setRelateName(byCustomerCodes.get(0).getCustomerName());
  }

  @Override
  public Class findCrmExcelVoClass() {
    return PriceProCusCrmImportVo.class;
  }

  @Override
  public String getBusinessCode() {
    return "MDM_PRICE_IMPORT";
  }

  @Override
  public String getBusinessName() {
    return "MDM价格维护导入";
  }

  @Override
  public String getTemplateCode() {
    return "MDM_PRICE_PRODUCT_CUSTOMER_IMPORT";
  }

  @Override
  public String getTemplateName() {
    return "MDM价格维护导入，客户商品维度";
  }
}
