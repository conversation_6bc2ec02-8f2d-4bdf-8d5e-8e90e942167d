package com.biz.crm.mdm.admin.web.report.position.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.position.dto.AbstractPositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.mapper.PositionReportMapper;
import com.biz.crm.mdm.admin.web.report.position.vo.PositionPageVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

/**
 * 职位复杂报表数据库访问类
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Component
public class PositionReportRepository {

  @Autowired(required = false)
  private PositionReportMapper positionReportMapper;

  /**
   * 职位分页列表
   *
   * @param dto      请求参数dto
   * @param pageable 分页信息
   * @return Page<PositionPageVo> 职位分页列表
   */
  public Page<PositionPageVo> findByConditions(Pageable pageable, AbstractPositionPageDto dto) {
    Page<PositionPageVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return this.positionReportMapper.findByConditions(page, dto);
  }
}
