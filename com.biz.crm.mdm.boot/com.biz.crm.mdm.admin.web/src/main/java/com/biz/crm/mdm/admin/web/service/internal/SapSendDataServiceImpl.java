package com.biz.crm.mdm.admin.web.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.base.constant.ryytn.RyConstant;
import com.biz.crm.business.common.base.eunm.ExternalSystemEnum;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.admin.web.dto.*;
import com.biz.crm.mdm.admin.web.enums.SapCustomerTypeEnum;
import com.biz.crm.mdm.admin.web.service.SapSendDataService;
import com.biz.crm.mdm.business.customer.local.service.CustomerSapDataService;
import com.biz.crm.mdm.business.customer.local.service.CustomerService;
import com.biz.crm.mdm.business.customer.sdk.dto.*;
import com.biz.crm.mdm.business.customer.sdk.enums.CustomerActiveStateEnum;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.destination.local.service.MdmDestinationSapDataService;
import com.biz.crm.mdm.business.destination.sdk.dto.MdmDestinationDto;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.price.local.entity.Price;
import com.biz.crm.mdm.business.price.local.entity.PriceDimension;
import com.biz.crm.mdm.business.price.local.repository.PriceRepository;
import com.biz.crm.mdm.business.price.local.service.PriceService;
import com.biz.crm.mdm.business.price.sdk.dto.PriceMilkCostDto;
import com.biz.crm.mdm.business.price.sdk.enums.PriceDimensionEnum;
import com.biz.crm.mdm.business.price.sdk.enums.SapPriceDimensionEnum;
import com.biz.crm.mdm.business.price.sdk.service.PriceMilkCostVoService;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.mdm.business.supplier.local.service.MdmSupplierSapDataService;
import com.biz.crm.mdm.business.supplier.sdk.dto.MdmSupplierBankDto;
import com.biz.crm.mdm.business.supplier.sdk.dto.MdmSupplierDto;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * SAP推送数据  实现
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/23 23:00
 */
@Service
@Slf4j
public class SapSendDataServiceImpl implements SapSendDataService {

    @Autowired(required = false)
    private CustomerSapDataService customerSapDataService;

    @Autowired(required = false)
    private MdmSupplierSapDataService mdmSupplierSapDataService;

    @Autowired(required = false)
    private MdmDestinationSapDataService mdmDestinationSapDataService;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Autowired(required = false)
    private PriceMilkCostVoService priceMilkCostVoService;

    @Autowired(required = false)
    private CustomerService customerService;

    @Autowired(required = false)
    private ProductVoService productVoService;

    @Autowired(required = false)
    private PriceService priceService;

    @Autowired(required = false)
    private OrgVoService orgVoService;

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private PriceRepository priceRepository;

    /**
     * ZBUTYPE	业务类型	C 客户 V 供应商
     * 1、C客户
     * Z001、Z002、Z005、Z007、Z008、Z009放客户
     * Z003放送达方
     * Z006放供应商
     * 2、V全放供应商
     * 供应商需要银行信息，其他不需要
     * <p>
     * BUGROUP	合作伙伴分组
     * Z001 Z1 集团内部关联客商（法人）
     * Z002 Z2 集团外部业务客商
     * Z003 Z3 送达方
     * Z005 Z4 集团外部财务专用客商
     * Z006 Z5 集团员工
     * Z007 Z6 物流承运商
     * Z008 Z7 一次性客商
     * Z009 Z8 旺店通店铺
     */
    //SE销售员;SH送达方;SP售达方;BP收票方;PY付款方
    private final static String PY_PARVW = "PY";
    private final static String SH_PARVW = "SH";

    /**
     * SAP推送数据
     *
     * @param json
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/23 23:01
     */
    @Override
    public void sapSendCustomerSupplierDeliveryInfo(JSONObject json) {
        JSONArray jsonArray = this.baseVerify(json);
        List<SapSendCustomerSupplierDeliveryDto> sapDtoList = JSONArray.parseArray(JSON.toJSONString(jsonArray), SapSendCustomerSupplierDeliveryDto.class);
        if (CollectionUtil.isEmpty(sapDtoList)) {
            return;
        }
        AbstractCrmUserIdentity crmUserIdentity = loginUserService.getAbstractLoginUser();
        List<CustomerDto> customerDtoList = Lists.newArrayList();
        List<MdmSupplierDto> supplierDtoList = Lists.newArrayList();
        List<MdmDestinationDto> destinationDtoList = Lists.newArrayList();
        sapDtoList.forEach(item -> {
            if (SapCustomerTypeEnum.V.getZbuType().equals(item.getZBUTYPE())) {
                List<MdmSupplierDto> mdmSupplierDtoList = this.buildSupplierInfo(item, crmUserIdentity);
                if (CollectionUtil.isNotEmpty(mdmSupplierDtoList)) {
                    supplierDtoList.addAll(mdmSupplierDtoList);
                }
            } else if (SapCustomerTypeEnum.Z001.getZbuType().equals(item.getZBUTYPE())) {
                SapCustomerTypeEnum typeEnum = SapCustomerTypeEnum.getByBusinessKey(item.getZBUTYPE(), item.getBUGROUP());
                if (Objects.isNull(typeEnum)) {
                    return;
                }
                switch (typeEnum) {
                    case Z001:
                    case Z002:
                    case Z005:
                    case Z007:
                    case Z008:
                    case Z009:
                        List<CustomerDto> sapCustomerList = this.buildCustomerInfo(item, crmUserIdentity);
                        if (CollectionUtil.isNotEmpty(sapCustomerList)) {
                            customerDtoList.addAll(sapCustomerList);
                        }
                        break;
                    case Z003:
                        List<MdmDestinationDto> sapDestinationList = this.buildDestinationInfo(item, crmUserIdentity);
                        if (CollectionUtil.isNotEmpty(sapDestinationList)) {
                            destinationDtoList.addAll(sapDestinationList);
                        }
                        break;
                    case Z006:
                        List<MdmSupplierDto> sapSupplierInfo = this.buildSupplierInfo(item, crmUserIdentity);
                        if (CollectionUtil.isNotEmpty(sapSupplierInfo)) {
                            supplierDtoList.addAll(sapSupplierInfo);
                        }
                        break;
                    default:
                        break;
                }
            }

        });

        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(DictConstant.MDM_CUSTOMER_FREEZE_REASON);
        Map<String, String> freezeReasonMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(dictDataVos)) {
            freezeReasonMap = dictDataVos.stream().collect(Collectors.toMap(x -> x.getDictCode(), l->l.getDictValue()));
        }

        if (CollectionUtil.isNotEmpty(customerDtoList)) {
            customerSapDataService.saveOrUpdateSapData(customerDtoList,freezeReasonMap);
        }
        if (CollectionUtil.isNotEmpty(supplierDtoList)) {
            mdmSupplierSapDataService.saveOrUpdateSapData(supplierDtoList);
        }
        if (CollectionUtil.isNotEmpty(destinationDtoList)) {
            mdmDestinationSapDataService.saveOrUpdateSapData(destinationDtoList);
        }
    }

    /**
     * SAP推送的数据,基础验证
     *
     * @param jsonObject
     */
    private JSONArray baseVerify(JSONObject jsonObject) {
        JSONArray jsonArray = jsonObject.getJSONArray(RyConstant.SAP_DETAIL_DATA);
        Assert.notEmpty(jsonArray, "推送的数据未获取到[" + RyConstant.SAP_DETAIL_DATA + "]明细!");
        Assert.isTrue(jsonArray.size() <= CommonConstant.MAX_DATA_SIZE, "单次推送明细最多[" + CommonConstant.MAX_DATA_SIZE + "]");
        return jsonArray;
    }


    /**
     * 构建客户信息
     *
     * @param item
     * @return
     */
    private List<CustomerDto> buildCustomerInfo(SapSendCustomerSupplierDeliveryDto item, AbstractCrmUserIdentity crmUserIdentity) {
        if (Objects.isNull(item)
                || CollectionUtil.isEmpty(item.getIT_KNVV())) {
            return Lists.newArrayList();
        }
        Map<String, List<CustomerDockingDto>> dockMap = Maps.newHashMap();
        Map<String, List<CustomerDestinationDto>> destinationDtoMap = Maps.newHashMap();
        Map<String, CustomerDto> payerCodeMap = this.buildPayerCodeInfo(item, dockMap, destinationDtoMap, crmUserIdentity);
        List<CustomerDto> customerDtoList = Lists.newArrayList();
        for (SapSendCustomerSupplierDeliveryOrgDto sapDto : item.getIT_KNVV()) {
            CustomerDto dto = new CustomerDto();
            this.setBaseInfo(item, dto, crmUserIdentity);
            dto.setCustomerName(item.getNAME1());
            dto.setCustomerType(item.getBUGROUP());
            dto.setProvinceCode(item.getREGIO());
            dto.setProvinceName(item.getREGIO_T());
            dto.setCityName(item.getORT01());
            dto.setDistrictName(item.getORT02());
            dto.setCooperateType(item.getKATR3());
            dto.setHzlx(item.getKATR3());
            if (StringUtil.isEmpty(item.getKATR3())) {
                log.error("这里在设置为空了-客户的合作类型");
                dto.setCooperateType(null);
                dto.setHzlx(null);
            }
            if (StringUtil.isNotEmpty(item.getKATR5())) {
                dto.setChannelType(item.getKATR5());
            }
            if (StringUtil.isNotEmpty(item.getKATR6())){
                dto.setChannelDepartmentCode(item.getKATR6());
            }
            Set<String> sapCustomerChannelCodeSet = Sets.newHashSet();
            Set<String> sapCustomerChannelNameSet = Sets.newHashSet();
            if (StringUtil.isNotEmpty(item.getZKHQD())) {
                for (String codeAndName : item.getZKHQD().split(",")) {
                    if (StringUtil.isEmpty(codeAndName)) {
                        continue;
                    }
                    String[] channels = codeAndName.split("/");
                    for (int i = 0; i < channels.length; i++) {
                        if (i == 0) {
                            sapCustomerChannelCodeSet.add(channels[i]);
                        } else if (i == 1) {
                            sapCustomerChannelNameSet.add(channels[i]);
                        }
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(sapCustomerChannelCodeSet)) {
                dto.setSapCustomerChannelCode(String.join(",", sapCustomerChannelCodeSet));
            }
            if (CollectionUtil.isNotEmpty(sapCustomerChannelNameSet)) {
                dto.setSapCustomerChannelName(String.join(",", sapCustomerChannelNameSet));
            }
            dto.setSearchName(item.getBUSORT2());
            dto.setContractDate(item.getLIQUID_DAT());
            dto.setExpirationDateRequest(item.getZXQYQ());
            dto.setRegisteredAddress(item.getSTREET());
            dto.setRiskClass(item.getRISK_CLASS());
            String sapCreateStr = item.getERDAT();
            if (StringUtil.isNotEmpty(sapCreateStr)) {
                try {
                    if (sapCreateStr.length() > DateUtil.DEFAULT_YEAR_MONTH_DAY.length()) {
                        sapCreateStr = sapCreateStr.substring(0, DateUtil.DEFAULT_YEAR_MONTH_DAY.length() - 1);
                    }
                    Date sapCreateTime = DateUtil.parseDate(sapCreateStr, DateUtil.DEFAULT_YEAR_MONTH_DAY);
                    sapCreateTime = DateUtil.dateAddMonth(sapCreateTime, 3);
                    if (new Date().compareTo(sapCreateTime) <= 0) {
                        dto.setActiveState(CustomerActiveStateEnum.T1.getCode());
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);

                }
            }
            //客户编码+公司代码+产品组编码+分销渠道编码
            dto.setErpCode(sapDto.getPARTNER());
            dto.setCompanyCode(sapDto.getVKORG());
            dto.setCompanyName(sapDto.getVKORGT());
            dto.setProductGroupCode(sapDto.getSPART());
            dto.setProductGroupName(sapDto.getSPART_T());
            dto.setChannelCode(sapDto.getVTWEG());
            dto.setChannelName(sapDto.getVTWEG_T());
            dto.setPriceGroup(sapDto.getKONDA());
            dto.setPriceGroupName(sapDto.getKONDA_T());
            dto.setOrgCode(sapDto.getVKGRP());
            dto.setFromType(ExternalSystemEnum.SAP.getCode());
            BooleanEnum booleanEnum = BooleanEnum.TRUE;
            if (StringUtil.isEmpty(sapDto.getKVGR3())) {
                booleanEnum = BooleanEnum.FALSE;
            }
            dto.setConsignSale(booleanEnum.getCapital());
            dto.setFreezeReason(sapDto.getKVGR5());
            if (StringUtil.isNotEmpty(sapDto.getAUFSD())
                    || StringUtil.isNotEmpty(item.getAUFSD())) {
                dto.setSapStatus(EnableStatusEnum.DISABLE.getCode());
            } else {
                dto.setSapStatus(EnableStatusEnum.ENABLE.getCode());
            }

            if (StringUtil.isEmpty(dto.getErpCode())
                    || StringUtil.isEmpty(dto.getCompanyCode())
                    || StringUtil.isEmpty(dto.getProductGroupCode())
                    || StringUtil.isEmpty(dto.getChannelCode())) {
                log.warn("客户 客户数据 关键信息为空{} ", JSON.toJSONString(dto));
                log.warn("客户 客户数据 关键信息为空{} {} ", JSON.toJSONString(item), JSON.toJSONString(sapDto));
                continue;
            }
            String customerCode = dto.getErpCode() + dto.getCompanyCode() + dto.getProductGroupCode() + dto.getChannelCode();
            dto.setCustomerCode(customerCode);
            CustomerDto payerCodeDto = payerCodeMap.get(customerCode);
            if (Objects.nonNull(payerCodeDto)) {
                dto.setPayerErpCode(payerCodeDto.getPayerErpCode());
                dto.setPayerCode(payerCodeDto.getPayerCode());
            }
            dto.setProcessStatus(ProcessStatusEnum.PASS.getDictCode());
            //构建组织信息
            this.buildOrg(dto, sapDto.getVKGRP());
            //构建地址信息
            this.buildAddress(dto, item, crmUserIdentity);
            //构建联系人
            this.buildContactList(dto, item, crmUserIdentity);
            dto.setDockingList(dockMap.getOrDefault(customerCode, Lists.newArrayList()));
            dto.setDestinationList(destinationDtoMap.getOrDefault(customerCode, Lists.newArrayList()));
            customerDtoList.add(dto);
        }

        return customerDtoList;
    }

    /**
     * 构建联系人
     *
     * @param dto
     * @param item
     * @param crmUserIdentity
     */
    private void buildContactList(CustomerDto dto, SapSendCustomerSupplierDeliveryDto item, AbstractCrmUserIdentity crmUserIdentity) {
        if (StringUtil.isNotEmpty(item.getNAME_CO())) {
            CustomerContactDto contactDto = new CustomerContactDto();
            contactDto.setTenantCode(TenantUtils.getTenantCode());
            contactDto.setId(dto.getCustomerCode());
            contactDto.setCustomerCode(dto.getCustomerCode());
            contactDto.setContactName(item.getNAME_CO());
            contactDto.setContactPhone(item.getTELF1());
            List<CustomerContactDto> contactDtoList = Lists.newLinkedList();
            contactDtoList.add(contactDto);
            dto.setContactList(contactDtoList);
        }
    }

    /**
     * 构建客户收货地址
     *
     * @param dto
     * @param item
     * @param crmUserIdentity
     */
    private void buildAddress(CustomerDto dto, SapSendCustomerSupplierDeliveryDto item, AbstractCrmUserIdentity crmUserIdentity) {
        if (StringUtil.isNotEmpty(item.getNAME_CO())) {
            CustomerAddressDto addressDto = new CustomerAddressDto();
            this.setBaseInfo(item, addressDto, crmUserIdentity);
            addressDto.setCustomerCode(dto.getCustomerCode());
            addressDto.setContactName(item.getNAME_CO());
            addressDto.setConsigneeName(item.getNAME_CO());
            addressDto.setContactPhone(item.getTELF1());
            addressDto.setDataSource(ExternalSystemEnum.SAP.getCode());
            List<CustomerAddressDto> addressDtoList = Lists.newLinkedList();
            addressDtoList.add(addressDto);
            dto.setCustomerAddressVos(addressDtoList);
        }
    }

    /**
     * 构建客户组织信息
     *
     * @param dto
     * @param orgCode
     */
    private void buildOrg(CustomerDto dto, String orgCode) {
        if (StringUtil.isNotEmpty(orgCode)) {
            CustomerRelateOrgDto orgDto = new CustomerRelateOrgDto();
            orgDto.setId(UuidCrmUtil.general());
            orgDto.setTenantCode(TenantUtils.getTenantCode());
            orgDto.setCustomerCode(dto.getCustomerCode());
            orgDto.setOrgCode(orgCode);
            List<CustomerRelateOrgDto> orgDtoList = Lists.newLinkedList();
            orgDtoList.add(orgDto);
            dto.setOrgList(orgDtoList);
        }
    }

    /**
     * 构建客户 责任业务员 相关信息
     *
     * @param item
     * @return
     */
    private Map<String, CustomerDto> buildPayerCodeInfo(SapSendCustomerSupplierDeliveryDto item,
                                                        Map<String, List<CustomerDockingDto>> dockMap,
                                                        Map<String, List<CustomerDestinationDto>> destinationDtoMap,
                                                        AbstractCrmUserIdentity crmUserIdentity) {
        Map<String, CustomerDto> payerCodeMap = Maps.newConcurrentMap();
        List<SapSendCustomerSupplierDeliverySalesDto> salesDtoList = item.getIT_KNVP();
        if (CollectionUtil.isEmpty(salesDtoList)) {
            return payerCodeMap;
        }
        SapCustomerTypeEnum typeEnum = SapCustomerTypeEnum.getByBusinessKey(item.getZBUTYPE(), item.getBUGROUP());
        salesDtoList.forEach(sapDto -> {
            CustomerDto dto = new CustomerDto();
            dto.setErpCode(sapDto.getPARTNER());
            dto.setCompanyCode(sapDto.getVKORG());
            dto.setCompanyName(sapDto.getVKORGT());
            dto.setProductGroupCode(sapDto.getSPART());
            dto.setProductGroupName(sapDto.getSPART_T());
            dto.setChannelCode(sapDto.getVTWEG());
            dto.setChannelName(sapDto.getVTWEG_T());
            dto.setPriceGroup(sapDto.getKONDA());
            dto.setPriceGroupName(sapDto.getKONDA_T());
            if (StringUtil.isEmpty(dto.getErpCode())
                    || StringUtil.isEmpty(dto.getCompanyCode())
                    || StringUtil.isEmpty(dto.getProductGroupCode())
                    || StringUtil.isEmpty(dto.getChannelCode())) {
                log.warn("客户 客户伙伴 关键信息为空{} ", JSON.toJSONString(dto));
                log.warn("客户 客户伙伴 关键信息为空{} ", JSON.toJSONString(sapDto));
                return;
            }

            String customerCode = dto.getErpCode() + dto.getCompanyCode() + dto.getProductGroupCode() + dto.getChannelCode();
            //PY付款方
            if (PY_PARVW.equals(sapDto.getPARVW())) {
                if (SapCustomerTypeEnum.Z002.equals(typeEnum)
                        || SapCustomerTypeEnum.Z009.equals(typeEnum)) {
                    String payerCode = sapDto.getKUNN2() + dto.getCompanyCode() + dto.getProductGroupCode() + dto.getChannelCode();
                    dto.setPayerErpCode(sapDto.getKUNN2());
                    dto.setPayerCode(payerCode);
                    payerCodeMap.put(customerCode, dto);
                }
            }

            //SH送达方
            if (SH_PARVW.equals(sapDto.getPARVW())) {
                List<CustomerDestinationDto> destinationDtoList = destinationDtoMap.getOrDefault(customerCode, Lists.newArrayList());
                CustomerDestinationDto destinationDto = new CustomerDestinationDto();
                destinationDto.setTenantCode(TenantUtils.getTenantCode());
                destinationDto.setCustomerCode(customerCode);
                String destinationCode = sapDto.getKUNN2() + dto.getCompanyCode() + dto.getProductGroupCode() + dto.getChannelCode();
                destinationDto.setDestinationErpCode(sapDto.getKUNN2());
                destinationDto.setDestinationCode(destinationCode);
                destinationDtoList.add(destinationDto);
                destinationDtoMap.put(customerCode, destinationDtoList);
            }

            dto.setCustomerCode(customerCode);
            if (StringUtil.isNotEmpty(sapDto.getRUFNM())) {
                List<CustomerDockingDto> dockingDtoList = dockMap.getOrDefault(customerCode, Lists.newArrayList());
                CustomerDockingDto dockingDto = new CustomerDockingDto();
                this.setBaseInfo(dockingDto, crmUserIdentity);
                dockingDto.setUserName(sapDto.getRUFNM());
                dockingDto.setCustomerCode(customerCode);
                dockingDto.setId(customerCode + "_" + StringUtils.stripToEmpty(sapDto.getRUFNM()));
                dockingDtoList.add(dockingDto);
                dockMap.put(customerCode, dockingDtoList);
            }
        });
        return payerCodeMap;
    }

    /**
     * 构建送达方
     *
     * @param item
     * @return
     */
    private List<MdmDestinationDto> buildDestinationInfo(SapSendCustomerSupplierDeliveryDto item, AbstractCrmUserIdentity crmUserIdentity) {
        if (Objects.isNull(item)
                || CollectionUtil.isEmpty(item.getIT_KNVV())) {
            return Lists.newArrayList();
        }
        List<MdmDestinationDto> destinationDtoList = Lists.newArrayList();
        item.getIT_KNVV().forEach(sapDto -> {
            MdmDestinationDto dto = new MdmDestinationDto();
            this.setBaseInfo(item, dto, crmUserIdentity);
            //送达方编码+公司代码+产品组编码+分销渠道编码
            dto.setErpCode(item.getPARTNER());
            dto.setDestinationName(item.getNAME1());
            dto.setDestinationType(item.getKATR3());

            dto.setCompanyCode(sapDto.getVKORG());
            dto.setCompanyName(sapDto.getVKORGT());
            dto.setProductGroupCode(sapDto.getSPART());
            dto.setProductGroupName(sapDto.getSPART_T());
            dto.setChannelCode(sapDto.getVTWEG());

            if (StringUtil.isEmpty(dto.getErpCode())
                    || StringUtil.isEmpty(dto.getCompanyCode())
                    || StringUtil.isEmpty(dto.getProductGroupCode())
                    || StringUtil.isEmpty(dto.getChannelCode())) {
                log.warn("送达方关键信息为空{} ", JSON.toJSONString(dto));
                log.warn("送达方关键信息为空{} {} ", JSON.toJSONString(item), JSON.toJSONString(sapDto));
                return;
            }
            String destinationCode = dto.getErpCode() + dto.getCompanyCode() + dto.getProductGroupCode() + dto.getChannelCode();
            dto.setDestinationCode(destinationCode);
            if (CollectionUtil.isNotEmpty(item.getIT_KNVP())) {
                item.getIT_KNVP().forEach(customerVo -> {
                    String customerErpCode = customerVo.getKUNN2();
                    if (StringUtil.isNotEmpty(customerErpCode)) {
                        String customerCode = customerErpCode + dto.getCompanyCode() + dto.getProductGroupCode() + dto.getChannelCode();
                        dto.setCustomerErpCode(customerErpCode);
                        dto.setCustomerCode(customerCode);
                        dto.setCustomerName(dto.getDestinationName());
                    }
                });
            }

            dto.setProvinceCode(item.getREGIO());
            dto.setProvinceName(item.getREGIO_T());
            dto.setCityName(item.getORT01());
            dto.setDistrictName(item.getORT02());

            dto.setSyncUpdateTime(new Date());
            dto.setDataSource(ExternalSystemEnum.SAP.getCode());
            dto.setConsigneeAddress(item.getSTREET());
            dto.setConsigneeName(item.getNAME_CO());
            dto.setConsigneePhone(item.getTELF1());

            if (StringUtil.isNotEmpty(sapDto.getAUFSD())) {
                dto.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
            }
            destinationDtoList.add(dto);

        });

        return destinationDtoList;
    }

    /**
     * 构建供应商
     *
     * @param item
     * @return
     */
    private List<MdmSupplierDto> buildSupplierInfo(SapSendCustomerSupplierDeliveryDto item, AbstractCrmUserIdentity crmUserIdentity) {
        if (Objects.isNull(item)
                || CollectionUtil.isEmpty(item.getIT_BUKRS())) {
            return Lists.newArrayList();
        }
        List<MdmSupplierDto> dtoList = Lists.newArrayList();
        item.getIT_BUKRS().forEach(sapDto -> {
            MdmSupplierDto dto = new MdmSupplierDto();
            dto.setSupplierSapCode(item.getPARTNER());
            dto.setCompanyCode(sapDto.getBUKRS());
            if (StringUtil.isEmpty(dto.getSupplierSapCode())
                    || StringUtil.isEmpty(dto.getCompanyCode())) {
                return;
            }
            dto.setSupplierCode(dto.getSupplierSapCode() + dto.getCompanyCode());
            this.setBaseInfo(item, dto, crmUserIdentity);
            if (StringUtil.isNotEmpty(sapDto.getSPERR())) {
                dto.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
            }
            dto.setSupplierName(item.getNAME1());
            dto.setEmployeeAccount(item.getNAME2());
            dto.setAddress(item.getSTREET());
            dto.setSupplierType(item.getBUGROUP());
            dto.setDataSource(ExternalSystemEnum.SAP.getCode());

            String supplierCode = dto.getSupplierCode();
            List<MdmSupplierBankDto> bankList = Lists.newArrayList();
            if (CollectionUtil.isNotEmpty(item.getIT_BANKS())) {
                item.getIT_BANKS().forEach(bank -> {
                    MdmSupplierBankDto bankDto = new MdmSupplierBankDto();
                    bankDto.setId(supplierCode + "_" + StringUtils.stripToEmpty(bank.getBKVID()));
                    bankDto.setBankCardNumber(bank.getBANKN());
                    bankDto.setBankCardNumberRemark(bank.getBKREF());
                    bankDto.setBankName(bank.getBANKA());
                    bankDto.setBankPayeeName(bank.getKOINH());
                    bankDto.setInterbankNumber(bank.getBANKL());
                    bankDto.setSupplierCode(supplierCode);
                    bankList.add(bankDto);
                });
            }
            dto.setBankList(bankList);
            dtoList.add(dto);
        });

        return dtoList;
    }

    /**
     * 设置基础信息
     *
     * @param dto
     */
    private void setBaseInfo(SapSendCustomerSupplierDeliveryDto item, TenantFlagOpDto dto, AbstractCrmUserIdentity crmUserIdentity) {
        this.setBaseInfo(dto, crmUserIdentity);
        //1、销售总体冻结 || 集中冻结 || 中心记账冻结，冻结则该客户编码的数据全部为禁用状态
        //2、销售选定冻结，则只针对该条数据冻结
        EnableStatusEnum enableStatusEnum = EnableStatusEnum.ENABLE;
        if (StringUtil.isNotEmpty(item.getXBLCK())
                || StringUtil.isNotEmpty(item.getSPERR())
                || StringUtil.isNotEmpty(item.getAUFSD())) {
            enableStatusEnum = EnableStatusEnum.DISABLE;
        }
        dto.setEnableStatus(enableStatusEnum.getCode());
    }

    /**
     * 设置基础信息
     *
     * @param dto
     */
    private void setBaseInfo(TenantFlagOpDto dto, AbstractCrmUserIdentity crmUserIdentity) {
        Date date = new Date();
        dto.setId(UuidCrmUtil.general());
        dto.setTenantCode(TenantUtils.getTenantCode());

        dto.setCreateAccount(crmUserIdentity.getUsername());
        dto.setCreateName(crmUserIdentity.getRealName());
        dto.setCreateTime(date);
        dto.setModifyAccount(crmUserIdentity.getUsername());
        dto.setModifyName(crmUserIdentity.getRealName());
        dto.setModifyTime(date);

        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());

    }

    /**
     * 设置基础信息
     *
     * @param entity
     */
    private void setBaseInfo(TenantFlagOpEntity entity, AbstractCrmUserIdentity crmUserIdentity) {
        Date date = new Date();
        entity.setId(UuidCrmUtil.general());
        entity.setTenantCode(TenantUtils.getTenantCode());

        entity.setCreateAccount(crmUserIdentity.getUsername());
        entity.setCreateName(crmUserIdentity.getRealName());
        entity.setCreateTime(date);
        entity.setModifyAccount(crmUserIdentity.getUsername());
        entity.setModifyName(crmUserIdentity.getRealName());
        entity.setModifyTime(date);

        entity.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        entity.setEnableStatus(EnableStatusEnum.ENABLE.getCode());

    }

    /**
     * SAP推送的价格数据
     *
     * @param json
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/2 22:32
     */
    @Override
    public void sapSendPriceInfo(JSONObject json) {
        JSONArray jsonArray = this.baseVerify(json);
        List<SapSendPriceDto> sapDtoList = JSONArray.parseArray(JSON.toJSONString(jsonArray), SapSendPriceDto.class);
        if (CollectionUtil.isEmpty(sapDtoList)) {
            return;
        }
        AbstractCrmUserIdentity crmUserIdentity = loginUserService.getAbstractLoginUser();

        List<Price> priceList = Lists.newArrayList();
        String tenantCode = TenantUtils.getTenantCode();
        List<String> companyCodes = sapDtoList.stream().filter(k -> StringUtil.isNotEmpty(k.getVKORG()))
                .map(SapSendPriceDto::getVKORG).distinct().collect(Collectors.toList());
        List<String> erpCodes = sapDtoList.stream().filter(k -> StringUtil.isNotEmpty(k.getKUNNR()))
                .map(SapSendPriceDto::getKUNNR).distinct().collect(Collectors.toList());
        List<CustomerVo> customerVoList = customerService.findByErpCodesAndCompanyCodes(companyCodes, erpCodes);
        Map<String, CustomerVo> customerVoMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(customerVoList)) {
            customerVoMap.putAll(customerVoList.stream().collect(Collectors.toMap(v -> v.getErpCode() + v.getCompanyCode(), v -> v, (n, o) -> n)));
        }
        List<String> productCodes = sapDtoList.stream().filter(k -> StringUtil.isNotEmpty(k.getMATNR()))
                .map(SapSendPriceDto::getMATNR).distinct().collect(Collectors.toList());
        List<ProductVo> productVoList = productVoService.findDetailsByIdsOrProductCodes(null, productCodes);
        Map<String, ProductVo> productVoMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(productVoList)) {
            productVoMap.putAll(productVoList.stream().collect(Collectors.toMap(ProductVo::getProductCode, v -> v, (n, o) -> n)));
        }
        List<String> orgCodeList = Lists.newArrayList();
        sapDtoList.forEach(item -> {
            if (StringUtil.isNotEmpty(item.getVKGRP())) {
                orgCodeList.add(item.getVKGRP());
            }
            if (StringUtil.isNotEmpty(item.getVKBUR())) {
                orgCodeList.add(item.getVKBUR());
            }
        });
        List<OrgVo> orgVoList = orgVoService.findByOrgCodes(orgCodeList);
        Map<String, OrgVo> orgVoMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(orgVoList)) {
            orgVoMap.putAll(orgVoList.stream().collect(Collectors.toMap(OrgVo::getOrgCode, v -> v, (n, o) -> n)));
        }
        List<String> dictTypeCodeList = Lists.newArrayList();
        dictTypeCodeList.add(DictConstant.MDM_PRICE_ORDER_FACTORY);
        dictTypeCodeList.add(DictConstant.PRICE_GROUP);
        dictTypeCodeList.add(DictConstant.MDM_COMPANY);
        dictTypeCodeList.add(DictConstant.MDM_SAP_CHANNEL);
        Map<String, Map<String, String>> allMap = this.dictDataVoService.findMapByDictTypeCodeList(dictTypeCodeList);

        Map<String, String> priceOrderFactoryMap = allMap.getOrDefault(DictConstant.MDM_PRICE_ORDER_FACTORY, Maps.newHashMap());
        Map<String, String> priceGroupMap = allMap.getOrDefault(DictConstant.PRICE_GROUP, Maps.newHashMap());
        Map<String, String> companyMap = allMap.getOrDefault(DictConstant.MDM_COMPANY, Maps.newHashMap());
        Map<String, String> channelMap = allMap.getOrDefault(DictConstant.MDM_SAP_CHANNEL, Maps.newHashMap());
        Set<String> disableSapPriceCodes = Sets.newHashSet();
        sapDtoList.forEach(sapDto -> {
            if (StringUtil.isNotEmpty(sapDto.getMATNR())) {
                sapDto.setProductName(productVoMap.getOrDefault(sapDto.getMATNR(), new ProductVo()).getProductName());
            }
            if (StringUtil.isNotEmpty(sapDto.getKUNNR())) {
                CustomerVo customerVo = customerVoMap.getOrDefault(sapDto.getKUNNR() + sapDto.getVKORG(), new CustomerVo());
                sapDto.setCustomerCode(customerVo.getCustomerCode());
                sapDto.setCustomerName(customerVo.getCustomerName());
                sapDto.setOrgCode(customerVo.getOrgCode());
                sapDto.setContractCustomer(customerVo.getContractCustomer());
                sapDto.setCompanyName(companyMap.getOrDefault(customerVo.getCompanyCode(), ""));
            }
            if (StringUtil.isNotEmpty(sapDto.getVKGRP())) {
                sapDto.setOrgGroupName(orgVoMap.getOrDefault(sapDto.getVKGRP(), new OrgVo()).getOrgName());
            }
            if (StringUtil.isNotEmpty(sapDto.getVKBUR())) {
                sapDto.setOrgDepartmentName(orgVoMap.getOrDefault(sapDto.getVKBUR(), new OrgVo()).getOrgName());
            }
            Price price = new Price();
            this.setBaseInfo(price, crmUserIdentity);
            this.buildPrice(price, sapDto, tenantCode, priceOrderFactoryMap, priceGroupMap, companyMap, channelMap, disableSapPriceCodes);
            if (StringUtil.isEmpty(price.getTypeDetailCode())
                    || StringUtil.isEmpty(price.getSapPriceCode())
                    || Objects.isNull(price.getBeginTime())
                    || Objects.isNull(price.getEndTime())
                    || CollectionUtil.isEmpty(price.getDimensionList())) {
                return;
            }
            priceList.add(price);
        });
        List<Price> saveList = Lists.newArrayList();
        List<Price> updateList = Lists.newArrayList();
        this.saveOrUpdateListForSapData(priceList, saveList, updateList);
        priceService.saveOrUpdateListForSapData(saveList, updateList, disableSapPriceCodes);
    }

    public void saveOrUpdateListForSapData(List<Price> priceList, List<Price> saveList, List<Price> updateList) {

        Map<String, List<Price>> priceMap = this.verifySelfRepetition(priceList);
        if (CollectionUtil.isEmpty(priceMap)) {
            return;
        }
        Map<String, List<Price>> oldMap = this.getOldPriceBySapPriceCodes(priceMap.keySet());
        Map<String, List<Price>> oldRelateMap = this.getOldPriceByRelateCodeJoins(priceMap.keySet(), priceMap.keySet());
        Map<String, Price> updateMap = Maps.newHashMap();
        AtomicBoolean isOperate = new AtomicBoolean(false);
        Set<String> isRepeatSet = Sets.newHashSet();
        priceMap.forEach((sapPriceCode, list) -> {
            if (CollectionUtil.isEmpty(list)) {
                return;
            }
            List<Price> oldPriceList = oldMap.get(sapPriceCode);
            list.forEach(newPrice -> {
                String relateCodeJoin = newPrice.getRelateCodeJoin();
                if (!isRepeatSet.contains(relateCodeJoin)) {
                    verifyRelateCodeJoins(newPrice, oldRelateMap.get(relateCodeJoin), updateMap);
                    isRepeatSet.add(relateCodeJoin);
                }
                if (CollectionUtil.isNotEmpty(oldPriceList)) {
                    isOperate.set(false);
                    verifySapPriceCodes(newPrice, oldPriceList, isOperate, updateMap, saveList);

                } else {
                    saveList.add(newPrice);
                }
            });
        });

        if (CollectionUtil.isNotEmpty(updateMap)) {
            updateList.addAll(updateMap.values());
        }
    }

    /**
     * 验证只剩重复
     *
     * @param priceList
     * @return
     */
    private Map<String, List<Price>> verifySelfRepetition(List<Price> priceList) {
        if (CollectionUtil.isEmpty(priceList)) {
            return Maps.newHashMap();
        }
        List<Price> prices = Lists.newArrayList();
        Map<String, List<Price>> priceMap = priceList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getTypeDetailCode()))
                .filter(k -> StringUtil.isNotEmpty(k.getSapPriceCode()))
                .filter(k -> Objects.nonNull(k.getBeginTime()))
                .filter(k -> Objects.nonNull(k.getEndTime()))
                .collect(Collectors.groupingBy(Price::getTypeDetailCode));
        priceMap.forEach((typeDetailCode, list) -> {
            if (CollectionUtil.isEmpty(list)) {
                return;
            }
            if (list.size() > 1) {
                list.forEach(oldPrice -> {
                    list.forEach(newPrice -> {
                        if (EnableStatusEnum.ENABLE.getCode().equals(oldPrice.getEnableStatus())
                                && EnableStatusEnum.ENABLE.getCode().equals(newPrice.getEnableStatus())
                                && oldPrice.getRelateCodeJoin().equals(newPrice.getRelateCodeJoin())) {
                            if (newPrice.getBeginTime().compareTo(oldPrice.getBeginTime()) == 0
                                    && newPrice.getEndTime().compareTo(oldPrice.getEndTime()) == 0
                                    && newPrice.getSapPriceCode().equals(oldPrice.getSapPriceCode())) {
                                //自己跳过不处理
                            } else if (newPrice.getBeginTime().compareTo(oldPrice.getBeginTime()) <= 0
                                    && newPrice.getEndTime().compareTo(oldPrice.getBeginTime()) >= 0) {
                                if (newPrice.getSapPriceCodeLong().compareTo(oldPrice.getSapPriceCodeLong()) >= 0) {
                                    oldPrice.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
                                } else {
                                    newPrice.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
                                }
                            } else if (newPrice.getBeginTime().compareTo(oldPrice.getEndTime()) <= 0
                                    && newPrice.getEndTime().compareTo(oldPrice.getEndTime()) >= 0) {
                                if (newPrice.getSapPriceCodeLong().compareTo(oldPrice.getSapPriceCodeLong()) >= 0) {
                                    oldPrice.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
                                } else {
                                    newPrice.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
                                }
                            } else if (newPrice.getBeginTime().compareTo(oldPrice.getBeginTime()) <= 0
                                    && newPrice.getEndTime().compareTo(oldPrice.getEndTime()) >= 0) {
                                if (newPrice.getSapPriceCodeLong().compareTo(oldPrice.getSapPriceCodeLong()) >= 0) {
                                    oldPrice.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
                                } else {
                                    newPrice.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
                                }
                            } else if (newPrice.getBeginTime().compareTo(oldPrice.getBeginTime()) >= 0
                                    && newPrice.getEndTime().compareTo(oldPrice.getEndTime()) <= 0) {
                                if (newPrice.getSapPriceCodeLong().compareTo(oldPrice.getSapPriceCodeLong()) >= 0) {
                                    oldPrice.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
                                } else {
                                    newPrice.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
                                }
                            }
                        }
                    });
                });
            }
            prices.addAll(list);
        });
        priceList = null;
        return Lists.newArrayList(prices).stream()
                .filter(k -> StringUtil.isNotEmpty(k.getTypeDetailCode()))
                .filter(k -> StringUtil.isNotEmpty(k.getSapPriceCode()))
                .filter(k -> Objects.nonNull(k.getBeginTime()))
                .filter(k -> Objects.nonNull(k.getEndTime()))
                .collect(Collectors.groupingBy(Price::getSapPriceCode));
    }

    /**
     * 判断SAP定价记录号重复时,时间是否有交集
     *
     * @param newPrice
     * @param oldPriceList
     * @param isOperate
     * @param updateMap
     * @param saveList
     */
    private void verifySapPriceCodes(Price newPrice, List<Price> oldPriceList, AtomicBoolean isOperate, Map<String, Price> updateMap, List<Price> saveList) {
        oldPriceList.forEach(oldPrice -> {
            if (newPrice.getBeginTime().compareTo(oldPrice.getBeginTime()) == 0
                    && newPrice.getEndTime().compareTo(oldPrice.getEndTime()) == 0) {
                String priceCode = oldPrice.getPriceCode();
                if (!isOperate.get()) {
                    newPrice.setId(oldPrice.getId());
                    newPrice.setPriceCode(priceCode);
                    newPrice.setCreateName(oldPrice.getCreateName());
                    newPrice.setCreateAccount(oldPrice.getCreateAccount());
                    newPrice.setCreateTime(oldPrice.getCreateTime());
                    updateMap.put(priceCode, newPrice);
                    isOperate.set(true);
                } else {
                    oldPrice.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
                    updateMap.put(priceCode, oldPrice);
                }
            } else if (newPrice.getBeginTime().compareTo(oldPrice.getBeginTime()) <= 0
                    && newPrice.getEndTime().compareTo(oldPrice.getBeginTime()) >= 0) {
                oldPrice.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
                updateMap.put(oldPrice.getPriceCode(), oldPrice);
                if (!isOperate.get()) {
                    saveList.add(newPrice);
                    isOperate.set(true);
                }
            } else if (newPrice.getBeginTime().compareTo(oldPrice.getEndTime()) <= 0
                    && newPrice.getEndTime().compareTo(oldPrice.getEndTime()) >= 0) {
                oldPrice.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
                updateMap.put(oldPrice.getPriceCode(), oldPrice);
                if (!isOperate.get()) {
                    saveList.add(newPrice);
                    isOperate.set(true);
                }
            } else if (newPrice.getBeginTime().compareTo(oldPrice.getBeginTime()) <= 0
                    && newPrice.getEndTime().compareTo(oldPrice.getEndTime()) >= 0) {
                oldPrice.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
                updateMap.put(oldPrice.getPriceCode(), oldPrice);
                if (!isOperate.get()) {
                    saveList.add(newPrice);
                    isOperate.set(true);
                }
            } else if (newPrice.getBeginTime().compareTo(oldPrice.getBeginTime()) >= 0
                    && newPrice.getEndTime().compareTo(oldPrice.getEndTime()) <= 0) {
                oldPrice.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
                updateMap.put(oldPrice.getPriceCode(), oldPrice);
                if (!isOperate.get()) {
                    saveList.add(newPrice);
                    isOperate.set(true);
                }
            }
        });
    }

    /**
     * 判断价格维度重复时,时间是否有交集
     *
     * @param newPrice
     * @param oldPriceList
     * @param updateMap
     */
    private void verifyRelateCodeJoins(Price newPrice, List<Price> oldPriceList, Map<String, Price> updateMap) {
        if (CollectionUtil.isEmpty(oldPriceList)) {
            return;
        }
        oldPriceList.forEach(oldPrice -> {
            if (newPrice.getBeginTime().compareTo(oldPrice.getBeginTime()) <= 0
                    && newPrice.getEndTime().compareTo(oldPrice.getBeginTime()) >= 0) {
                oldPrice.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
                updateMap.put(oldPrice.getPriceCode(), oldPrice);
            } else if (newPrice.getBeginTime().compareTo(oldPrice.getEndTime()) <= 0
                    && newPrice.getEndTime().compareTo(oldPrice.getEndTime()) >= 0) {
                oldPrice.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
                updateMap.put(oldPrice.getPriceCode(), oldPrice);
            } else if (newPrice.getBeginTime().compareTo(oldPrice.getBeginTime()) <= 0
                    && newPrice.getEndTime().compareTo(oldPrice.getEndTime()) >= 0) {
                oldPrice.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
                updateMap.put(oldPrice.getPriceCode(), oldPrice);
            } else if (newPrice.getBeginTime().compareTo(oldPrice.getBeginTime()) >= 0
                    && newPrice.getEndTime().compareTo(oldPrice.getEndTime()) <= 0) {
                oldPrice.setEnableStatus(EnableStatusEnum.DISABLE.getCode());
                updateMap.put(oldPrice.getPriceCode(), oldPrice);
            }
        });
    }

    /**
     * 根据SAP价格编码  查询历史数据
     *
     * @param sapPriceCodeSet
     * @return
     */
    private Map<String, List<Price>> getOldPriceBySapPriceCodes(Set<String> sapPriceCodeSet) {
        if (CollectionUtil.isEmpty(sapPriceCodeSet)) {
            return Maps.newHashMap();
        }
        List<Price> oldList = priceRepository.findBySapPriceCodes(sapPriceCodeSet);
        Map<String, List<Price>> oldMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(oldList)) {
            oldMap.putAll(oldList.stream()
                    .filter(k -> StringUtil.isNotEmpty(k.getSapPriceCode()))
                    .filter(k -> Objects.nonNull(k.getBeginTime()))
                    .filter(k -> Objects.nonNull(k.getEndTime()))
                    .collect(Collectors.groupingBy(Price::getSapPriceCode)));
        }
        return oldMap;
    }

    /**
     * 根据 价格维度  查询历史数据
     *
     * @param relateCodeSet
     * @return
     */
    private Map<String, List<Price>> getOldPriceByRelateCodeJoins(Set<String> relateCodeSet, Set<String> sapPriceCodeSet) {
        if (CollectionUtil.isEmpty(relateCodeSet)) {
            return Maps.newHashMap();
        }
        List<Price> oldList = priceRepository.getOldPriceByRelateCodeJoins(relateCodeSet, sapPriceCodeSet);
        Map<String, List<Price>> oldMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(oldList)) {
            oldMap.putAll(oldList.stream()
                    .filter(k -> StringUtil.isNotEmpty(k.getPriceCode()))
                    .filter(k -> Objects.nonNull(k.getBeginTime()))
                    .filter(k -> Objects.nonNull(k.getEndTime()))
                    .filter(k -> StringUtil.isNotEmpty(k.getRelateCodeJoin()))
                    .collect(Collectors.groupingBy(Price::getRelateCodeJoin)));
        }
        return oldMap;
    }

    /**
     * 构建价格数据
     *
     * @param price
     * @param sapDto
     */
    private void buildPrice(Price price, SapSendPriceDto sapDto, String tenantCode,
                            Map<String, String> priceOrderFactoryMap,
                            Map<String, String> priceGroupMap,
                            Map<String, String> companyMap,
                            Map<String, String> channelMap,
                            Set<String> disableSapPriceCodes) {
        if (Objects.isNull(price)
                || Objects.isNull(sapDto)) {
            return;
        }
        String sapPriceCode = sapDto.getKNUMH();
        SapPriceDimensionEnum priceDimensionEnum = SapPriceDimensionEnum.getByTableKey(sapDto.getTABLE());
        if (Objects.isNull(priceDimensionEnum)) {
            if (StringUtil.isNotEmpty(sapDto.getLOEVM_KO())
                    && StringUtil.isNotEmpty(sapPriceCode)) {
                disableSapPriceCodes.add(sapPriceCode);
            }
            log.warn("SAP推送的价格数据异常,未获取到价格维度信息1{}", JSONObject.toJSONString(sapDto));
            return;
        }

        List<PriceDimension> dimensionList = this.buildPriceDimension(price, priceDimensionEnum, sapDto, priceOrderFactoryMap, priceGroupMap, companyMap, channelMap);
        if (CollectionUtil.isEmpty(dimensionList)) {
            log.warn("SAP推送的价格数据异常,未获取到价格维度信息2{}", JSONObject.toJSONString(sapDto));
            return;
        }
        dimensionList.forEach(priceDimension -> {
            priceDimension.setSapPriceCode(sapPriceCode);
            priceDimension.setTypeCode(priceDimensionEnum.getTypeCode());
            priceDimension.setTypeDetailCode(priceDimensionEnum.getTypeDetailCode());
            priceDimension.setTenantCode(tenantCode);
        });

        price.setTypeCode(priceDimensionEnum.getTypeCode());
        price.setTypeDetailCode(priceDimensionEnum.getTypeDetailCode());
        price.setSapPriceCode(sapPriceCode);
        price.setSapPriceCodeLong(Long.parseLong(sapPriceCode));
        price.setCompanyCode(sapDto.getVKORG());
        price.setPrice(sapDto.getKBETR());
        price.setDataSource(ExternalSystemEnum.SAP.getCode());
        try {
            price.setBeginTime(DateUtil.parse(sapDto.getDATAB() + DateUtil.DAY_EARLIEST_TIME, DateUtil.DEFAULT_DATE_ALL_PATTERN));
        } catch (Exception e) {
            log.error("SAP推送的价格数据异常,价格开始日期异常{}", JSONObject.toJSONString(sapDto));
            log.error(e.getMessage(), e);

        }
        try {
            price.setEndTime(DateUtil.parse(sapDto.getDATBI() + DateUtil.DAY_LATEST_TIME, DateUtil.DEFAULT_DATE_ALL_PATTERN));
        } catch (Exception e) {
            log.error("SAP推送的价格数据异常,价格截止日期异常{}", JSONObject.toJSONString(sapDto));
            log.error(e.getMessage(), e);
        }
        EnableStatusEnum enableStatusEnum = EnableStatusEnum.ENABLE;
        if (StringUtil.isNotEmpty(sapDto.getLOEVM_KO())) {
            enableStatusEnum = EnableStatusEnum.DISABLE;
        }
        price.setSapStatus(enableStatusEnum.getCode());
        price.setEnableStatus(enableStatusEnum.getCode());
        price.setDimensionList(dimensionList);
    }

    /**
     * 构建价格维度数据
     *
     * @param priceDimensionEnum
     * @param sapDto
     * @return
     */
    private List<PriceDimension> buildPriceDimension(Price price,
                                                     SapPriceDimensionEnum priceDimensionEnum,
                                                     SapSendPriceDto sapDto,
                                                     Map<String, String> priceOrderFactoryMap,
                                                     Map<String, String> priceGroupMap,
                                                     Map<String, String> companyMap,
                                                     Map<String, String> channelMap) {
        List<PriceDimension> priceDimensionList = Lists.newArrayList();
        if (Objects.isNull(priceDimensionEnum)) {
            return priceDimensionList;
        }
        String typeDetailCode = priceDimensionEnum.getTypeDetailCode();
        PriceDimension product = new PriceDimension();
        product.setDimensionCode(PriceDimensionEnum.PRODUCT.getKey());
        product.setRelateCode(sapDto.getMATNR());
        product.setRelateName(sapDto.getProductName());

        //销售组织
        PriceDimension orgInstitution = new PriceDimension();
        orgInstitution.setDimensionCode(PriceDimensionEnum.ORG_INSTITUTION.getKey());
        orgInstitution.setRelateCode(sapDto.getVKORG());
        orgInstitution.setRelateName(companyMap.getOrDefault(orgInstitution.getRelateCode(), ""));

        //渠道
        PriceDimension channel = new PriceDimension();
        channel.setDimensionCode(PriceDimensionEnum.CHANNEL.getKey());
        channel.setRelateCode(sapDto.getVTWEG());
        channel.setRelateName(channelMap.getOrDefault(channel.getRelateCode(), ""));

        switch (priceDimensionEnum) {
            case A032:
                //格组
                PriceDimension priceGroup = new PriceDimension();
                priceGroup.setDimensionCode(PriceDimensionEnum.PRICE_GROUP.getKey());
                priceGroup.setRelateCode(sapDto.getKONDA());
                priceGroup.setRelateName(priceGroupMap.getOrDefault(priceGroup.getRelateCode(), ""));
                price.setRelateCodeJoin(String.join("-", typeDetailCode, priceGroup.getRelateCode(), product.getRelateCode()
                        , orgInstitution.getRelateCode(), channel.getRelateCode()));
                priceDimensionList.add(priceGroup);

                //销售组织
                priceDimensionList.add(orgInstitution);
                //渠道
                priceDimensionList.add(channel);

                priceDimensionList.add(product);
                break;
            case A005:
                //客户
                PriceDimension customer = new PriceDimension();
                customer.setDimensionCode(PriceDimensionEnum.CUSTOMER.getKey());
                customer.setRelateCode(sapDto.getCustomerCode());
                customer.setRelateSapCode(sapDto.getKUNNR());
                customer.setOrgCode(sapDto.getOrgCode());
                customer.setRelateName(sapDto.getCustomerName());
                customer.setCompanyName(sapDto.getCompanyName());
                customer.setContractCustomer(sapDto.getContractCustomer());
                price.setRelateCodeJoin(String.join("-", typeDetailCode, customer.getRelateCode(), product.getRelateCode()
                        , orgInstitution.getRelateCode(), channel.getRelateCode()));
                priceDimensionList.add(customer);


                //销售组织
                priceDimensionList.add(orgInstitution);
                //渠道
                priceDimensionList.add(channel);

                priceDimensionList.add(product);
                break;
            case A901:
                //A901销售组
                PriceDimension orgGroup = new PriceDimension();
                orgGroup.setDimensionCode(PriceDimensionEnum.ORG_GROUP.getKey());
                orgGroup.setRelateCode(sapDto.getVKGRP());
                orgGroup.setRelateName(sapDto.getOrgGroupName());
                price.setRelateCodeJoin(String.join("-", typeDetailCode, orgGroup.getRelateCode(), product.getRelateCode()
                        , orgInstitution.getRelateCode(), channel.getRelateCode()));
                priceDimensionList.add(orgGroup);

                //销售组织
                priceDimensionList.add(orgInstitution);
                //渠道
                priceDimensionList.add(channel);

                priceDimensionList.add(product);
                break;
            case A902:
                //销售部门
                PriceDimension orgDepartment = new PriceDimension();
                orgDepartment.setDimensionCode(PriceDimensionEnum.ORG_DEPARTMENT.getKey());
                orgDepartment.setRelateCode(sapDto.getVKBUR());
                orgDepartment.setRelateName(sapDto.getOrgDepartmentName());
                price.setRelateCodeJoin(String.join("-", typeDetailCode, orgDepartment.getRelateCode(), product.getRelateCode()
                        , orgInstitution.getRelateCode(), channel.getRelateCode()));
                priceDimensionList.add(orgDepartment);

                //销售组织
                priceDimensionList.add(orgInstitution);
                //渠道
                priceDimensionList.add(channel);

                priceDimensionList.add(product);
                break;
            case A004:
                price.setRelateCodeJoin(String.join("-", typeDetailCode, product.getRelateCode()
                        , orgInstitution.getRelateCode(), channel.getRelateCode()));

                //销售组织
                priceDimensionList.add(orgInstitution);
                //渠道
                priceDimensionList.add(channel);

                priceDimensionList.add(product);
                break;
            case A055:

                PriceDimension orderFactory = new PriceDimension();
                orderFactory.setDimensionCode(PriceDimensionEnum.ORDER_FACTORY.getKey());
                orderFactory.setRelateCode(sapDto.getWERKS());
                orderFactory.setRelateName(priceOrderFactoryMap.getOrDefault(orderFactory.getRelateCode(), ""));
                priceDimensionList.add(orderFactory);
                //订单/工厂
                price.setRelateCodeJoin(String.join("-", typeDetailCode,
                        orderFactory.getRelateCode(), product.getRelateCode(), orgInstitution.getRelateCode()));

                //销售组织
                priceDimensionList.add(orgInstitution);


                priceDimensionList.add(product);
                break;
            default:
                break;
        }
        return priceDimensionList;
    }

    /**
     * SAP推送的奶卡成本价
     *
     * @param json
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/2 22:32
     */
    @Override
    public void sapSendMilkCostPriceInfo(JSONObject json) {
        JSONArray jsonArray = this.baseVerify(json);
        List<SapSendMilkCostPriceDto> sapDtoList = JSONArray.parseArray(JSON.toJSONString(jsonArray), SapSendMilkCostPriceDto.class);
        if (CollectionUtil.isEmpty(sapDtoList)) {
            return;
        }
        AbstractCrmUserIdentity crmUserIdentity = loginUserService.getAbstractLoginUser();
        List<PriceMilkCostDto> dtoList = Lists.newArrayList();
        BigDecimal hundred = BigDecimal.valueOf(100);
        sapDtoList.forEach(sapDto -> {
            PriceMilkCostDto dto = new PriceMilkCostDto();
            this.setBaseInfo(dto, crmUserIdentity);
            this.buildPriceMilkCostDto(dto, sapDto);
            if (Objects.nonNull(sapDto.getTAX())) {
                BigDecimal taxRate = sapDto.getTAX().setScale(2, BigDecimal.ROUND_HALF_UP);
                dto.setTaxRate(taxRate);
                dto.setTaxRateStr(taxRate.multiply(hundred)
                        .setScale(0, BigDecimal.ROUND_HALF_UP).toString() + "%");
            }
            dtoList.add(dto);
        });
        priceMilkCostVoService.saveOrUpdateBatchDto(dtoList);
    }

    /**
     * 构建奶卡成本价dto
     *
     * @param dto
     * @param sapDto
     */
    private void buildPriceMilkCostDto(PriceMilkCostDto dto, SapSendMilkCostPriceDto sapDto) {
        if (Objects.isNull(dto)
                || Objects.isNull(sapDto)) {
            return;
        }
        dto.setCompanyCode(sapDto.getMANDT());
        dto.setYears(sapDto.getGJAHR());
        try {
            dto.setMonth(String.format("%02d", Integer.valueOf(sapDto.getMONAT())));
            dto.setYearMonthLy(dto.getYears() + "-" + dto.getMonth());
        } catch (Exception e) {
            log.error("SAP奶卡成本价月转换错误");
            log.error(e.getMessage(), e);
        }
        dto.setMaterialCode(sapDto.getMATNR());
        dto.setOnlyKey(StringUtils.stripToEmpty(dto.getCompanyCode()) + "_" + StringUtils.stripToEmpty(dto.getMaterialCode()) + "_" + StringUtils.stripToEmpty(dto.getYearMonthLy()));
        dto.setMaterialName(sapDto.getMAKTX());
        dto.setMilkCardQuantity(sapDto.getZTS());
        dto.setCostPrice(sapDto.getKOSTN());
        EnableStatusEnum enableStatusEnum = EnableStatusEnum.ENABLE;
        if (StringUtil.isNotEmpty(sapDto.getZSTATU())) {
            enableStatusEnum = EnableStatusEnum.DISABLE;
        }
        dto.setEnableStatus(enableStatusEnum.getCode());
    }

}
