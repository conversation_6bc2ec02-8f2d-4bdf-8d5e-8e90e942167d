package com.biz.crm.mdm.admin.web.imports.price.modle;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 价格维护 客户商品维度 导入模板
 *
 * <AUTHOR>
 * @describe:
 * @createTime 2022年05月24日 14:09:00
 */
@Data
@CrmExcelImport
public class PriceProCusCrmImportVo extends CrmExcelVo {


  /**
   * 价格类型编码
   */
  @CrmExcelColumn("价格类型编码")
  private String typeCode;

  /**
   * 客户编码
   */
  @CrmExcelColumn("客户编码")
  private String relateCode;

  /**
   *客户名称
   */
  private String relateName;

  /**
   * 维度编码
   */
  private String typeDetailCode;

  /**
   * 维度名称
   */
  private String typeDetailName;

  /**
   * 商品编码
   */
  @CrmExcelColumn("商品编码")
  private String productCode;

  /**
   * 价格
   */
  @CrmExcelColumn("价格")
  private BigDecimal price;

  /**
   * 开始时间
   */
  @CrmExcelColumn("开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date beginTime;

  /**
   * 结束时间
   */
  @CrmExcelColumn("结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;
}
