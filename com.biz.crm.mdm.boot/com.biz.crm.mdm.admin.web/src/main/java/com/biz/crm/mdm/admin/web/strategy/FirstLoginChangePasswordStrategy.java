package com.biz.crm.mdm.admin.web.strategy;


import com.alibaba.fastjson.JSONObject;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import org.springframework.core.Ordered;

/**
 * 登录限制策略
 * 提供登录人第一次登陆是否强制修改密码策略
 *
 * <AUTHOR>
 */
public interface FirstLoginChangePasswordStrategy extends Ordered {

  /**
   * 处理登录限制逻辑
   */
  void handle(UserIdentity userIdentity, JSONObject jsonObject);
}
