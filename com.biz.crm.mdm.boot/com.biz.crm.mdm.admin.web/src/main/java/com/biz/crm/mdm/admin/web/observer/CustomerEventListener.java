package com.biz.crm.mdm.admin.web.observer;

import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerContactVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.org.local.entity.Org;
import com.biz.crm.mdm.business.org.local.service.OrgService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.workflow.sdk.dto.CustomerInfoDto;
import com.biz.crm.workflow.sdk.listener.CustomerInfoListener;
import com.biz.crm.workflow.sdk.vo.response.CustomerInfoResponse;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: mdm为工作流提供组织相关基础数据
 * @author: rentao
 * @date: 2022/8/11 15:28
 */
@Component
public class CustomerEventListener implements CustomerInfoListener {

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @Autowired(required = false)
  private CustomerVoService customerVoService;

  @Override
  public CustomerInfoResponse findByCustomerCodes(CustomerInfoDto customerInfoDto) {
    if (Objects.nonNull(customerInfoDto)) {
      List<String> customerCodes = customerInfoDto.getCustomerCodes();
      List<CustomerVo> customerVos = this.customerVoService.findByCustomerCodes(customerCodes);
      List<com.biz.crm.workflow.sdk.vo.CustomerVo> customerVoArrayList = Lists.newArrayList();
      if (CollectionUtils.isNotEmpty(customerVos)) {
        customerVoArrayList =
            (List<com.biz.crm.workflow.sdk.vo.CustomerVo>)
                this.nebulaToolkitService.copyCollectionByWhiteList(
                    customerVos,
                    CustomerVo.class,
                    com.biz.crm.workflow.sdk.vo.CustomerVo.class,
                    HashSet.class,
                    ArrayList.class);
      }
      Map<String, CustomerVo> customerVoMap =
          customerVos.stream()
              .collect(Collectors.toMap(CustomerVo::getCustomerCode, t -> t, (key1, key2) -> key1));
      customerVoArrayList.forEach(
          customerVo -> {
            CustomerVo customer = customerVoMap.get(customerVo.getCustomerCode());
            // 联系人
            List<CustomerContactVo> contactList = customer.getContactList();
            List<com.biz.crm.workflow.sdk.vo.CustomerContactVo> customerContactVos =
                (List<com.biz.crm.workflow.sdk.vo.CustomerContactVo>)
                    this.nebulaToolkitService.copyCollectionByWhiteList(
                        contactList,
                        CustomerContactVo.class,
                        com.biz.crm.workflow.sdk.vo.CustomerContactVo.class,
                        HashSet.class,
                        ArrayList.class);
            customerVo.setContactList(customerContactVos);
          });
      CustomerInfoResponse customerInfoResponse = new CustomerInfoResponse();
      customerInfoResponse.setCustomerVos(customerVoArrayList);
      return customerInfoResponse;
    }
    return null;
  }
}
