package com.biz.crm.mdm.admin.web.report.spu.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.spu.model.AllowSaleListSpuModelVo;
import com.biz.crm.mdm.admin.web.report.spu.repository.AllowSaleListSpuVoRepository;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.price.sdk.dto.FindPriceDto;
import com.biz.crm.mdm.business.price.sdk.enums.FindPriceUserTypeEnum;
import com.biz.crm.mdm.business.price.sdk.service.PriceModelVoService;
import com.biz.crm.mdm.business.price.sdk.vo.PriceModelVo;
import com.biz.crm.mdm.business.product.level.sdk.service.ProductLevelVoSdkService;
import com.biz.crm.mdm.business.product.level.sdk.vo.ProductLevelVo;
import com.biz.crm.mdm.business.product.spu.local.entity.ProductSpu;
import com.biz.crm.mdm.business.product.spu.local.entity.ProductSpuMedia;
import com.biz.crm.mdm.business.product.spu.local.entity.ProductSpuRelateSku;
import com.biz.crm.mdm.business.product.spu.local.repository.ProductSpuMediaRepository;
import com.biz.crm.mdm.business.product.spu.local.repository.ProductSpuRelateSkuRepository;
import com.biz.crm.mdm.business.product.spu.local.repository.ProductSpuRepository;
import com.biz.crm.mdm.business.product.spu.sdk.dto.AllowSaleListSpuPaginationDto;
import com.biz.crm.mdm.business.product.spu.sdk.enums.MediaTypeEnum;
import com.biz.crm.mdm.business.product.spu.sdk.service.AllowSaleListSpuVoService;
import com.biz.crm.mdm.business.product.spu.sdk.vo.AllowSaleListSpuVo;
import com.biz.crm.mdm.business.product.spu.sdk.vo.ProductSpuMediaVo;
import com.biz.crm.mdm.business.product.spu.sdk.vo.ProductSpuRelateSkuVo;
import com.biz.crm.mdm.business.product.spu.sdk.vo.ProductSpuVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 可购清单spu分页信息SDK接口实现
 *
 * <AUTHOR>
 * @date 2021/12/6
 */
@Slf4j
@Service
public class AllowSaleListSpuVoServiceImpl implements AllowSaleListSpuVoService {

  @Autowired(required = false)
  private AllowSaleListSpuVoRepository allowSaleListSpuVoRepository;

  @Autowired(required = false)
  private ProductLevelVoSdkService productLevelVoSdkService;

  @Autowired(required = false)
  private ProductSpuRepository productSpuRepository;

  @Autowired(required = false)
  private ProductSpuRelateSkuRepository productSpuRelateSkuRepository;

  @Autowired(required = false)
  private ProductSpuMediaRepository productSpuMediaRepository;

  @Autowired(required = false)
  private CustomerVoService customerVoService;

  @Autowired(required = false)
  private PriceModelVoService priceModelVoService;

  @Autowired(required = false)
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public Page<AllowSaleListSpuVo> onRequestByAllowSaleListSpuPaginationDto(
      AllowSaleListSpuPaginationDto dto) {
    // 1. 根据keyword+productLevelCode+productCodeList筛选符合的商品编码集合信息
    // 2. 根据1获取的商品编码集合+keyword+spuTagList+pageSize+page获取spu编码+sort排序分页信息
    // 3. 根据2获取数据去重排序，取得符合条件的分页记录数据
    // 4. 根据spu编码获取spu详情，返回分页数据
    if (CollectionUtils.isEmpty(dto.getProductCodeList())) {
      return null;
    }
    Set<String> productCodeSet =
        this.findProductCodeSet(
            StringUtils.EMPTY, dto.getProductLevelCode(), dto.getProductCodeList());

    if (CollectionUtils.isEmpty(productCodeSet)) {
      return null;
    }
    dto.setPage(Integer.max(1, Optional.ofNullable(dto.getPage()).orElse(0)));
    dto.setPageSize(Integer.max(0, Optional.ofNullable(dto.getPageSize()).orElse(0)));
    if (dto.getPageSize() == 0) {
      return null;
    }
    List<AllowSaleListSpuModelVo> list = Lists.newArrayList();
    for (List<String> item : Lists.partition(Lists.newArrayList(productCodeSet), 2000)) {
      List<AllowSaleListSpuModelVo> curList =
          this.allowSaleListSpuVoRepository.findAllowSaleListSpuModelVoList(
              dto.getKeyword(), dto.getSpuTagList(), item);
      if (CollectionUtils.isNotEmpty(curList)) {
        list.addAll(curList);
      }
    }

    if (CollectionUtils.isEmpty(list)) {
      return null;
    }

    // sort降序 去重
    List<String> filterList =
        list.stream()
            .sorted(Comparator.comparing(AllowSaleListSpuModelVo::getSort).reversed())
            .map(AllowSaleListSpuModelVo::getSpuCode)
            .distinct()
            .collect(Collectors.toList());

    Integer total = filterList.size();
    Integer maxPage = total / dto.getPageSize() + (total % dto.getPageSize() > 0 ? 1 : 0);
    dto.setPage(Integer.min(maxPage, dto.getPage()));

    List<String> spuCodeList =
        filterList.stream()
            .skip((dto.getPage() - 1) * dto.getPageSize())
            .limit(dto.getPageSize())
            .collect(Collectors.toList());

    Page<AllowSaleListSpuVo> page = new Page<>();
    page.setTotal(total);
    page.setSize(dto.getPageSize());

    if (CollectionUtils.isNotEmpty(spuCodeList)) {
      List<ProductSpuVo> spuVoList = this.findProductSpu(dto, spuCodeList, productCodeSet);
      if (!CollectionUtils.isEmpty(spuVoList)) {
        Map<String, List<BigDecimal>> map =
            spuVoList.stream()
                .filter(
                    a ->
                        StringUtils.isNoneBlank(a.getSpuCode())
                            && !CollectionUtils.isEmpty(a.getPriceShowList()))
                .collect(
                    Collectors.toMap(
                        ProductSpuVo::getSpuCode, ProductSpuVo::getPriceShowList, (a, b) -> a));
        List<AllowSaleListSpuVo> records =
            (List<AllowSaleListSpuVo>)
                this.nebulaToolkitService.copyCollectionByBlankList(
                    spuVoList,
                    ProductSpuVo.class,
                    AllowSaleListSpuVo.class,
                    HashSet.class,
                    ArrayList.class,
                    "priceShowList");
        for (AllowSaleListSpuVo item : records) {
          item.setPriceShowList(map.get(item.getSpuCode()));
        }
        page.setRecords(records);
      }
    }
    return page;
  }

  /**
   * 精准查询
   *
   * @param dto
   * @return
   */
  @Override
  public Page<AllowSaleListSpuVo> onRequestByAllowSaleListSpuPrecisePaginationDto(AllowSaleListSpuPaginationDto dto) {
    // 1. 根据keyword+productLevelCode+productCodeList筛选符合的商品编码集合信息
    // 2. 根据1获取的商品编码集合+keyword+spuTagList+pageSize+page获取spu编码+sort排序分页信息
    // 3. 根据2获取数据去重排序，取得符合条件的分页记录数据
    // 4. 根据spu编码获取spu详情，返回分页数据
    if (CollectionUtils.isEmpty(dto.getProductCodeList())) {
      return null;
    }
    Set<String> productCodeSet =
        this.findProductCodeSet(
            StringUtils.EMPTY, dto.getProductLevelCode(), dto.getProductCodeList());

    if (CollectionUtils.isEmpty(productCodeSet)) {
      return null;
    }
    dto.setPage(Integer.max(1, Optional.ofNullable(dto.getPage()).orElse(0)));
    dto.setPageSize(Integer.max(0, Optional.ofNullable(dto.getPageSize()).orElse(0)));
    if (dto.getPageSize() == 0) {
      return null;
    }
    List<AllowSaleListSpuModelVo> list = Lists.newArrayList();
    for (List<String> item : Lists.partition(Lists.newArrayList(productCodeSet), 500)) {
      List<AllowSaleListSpuModelVo> curList =
          this.allowSaleListSpuVoRepository.findPreciseAllowSaleListSpuModelVoList(
              dto.getKeyword(), dto.getSpuTagList(), item);
      if (CollectionUtils.isNotEmpty(curList)) {
        list.addAll(curList);
      }
    }

    if (CollectionUtils.isEmpty(list)) {
      return null;
    }

    // sort降序 去重
    List<String> filterList =
        list.stream()
            .sorted(Comparator.comparing(AllowSaleListSpuModelVo::getSort).reversed())
            .map(AllowSaleListSpuModelVo::getSpuCode)
            .distinct()
            .collect(Collectors.toList());

    Integer total = filterList.size();
    Integer maxPage = total / dto.getPageSize() + (total % dto.getPageSize() > 0 ? 1 : 0);
    dto.setPage(Integer.min(maxPage, dto.getPage()));

    List<String> spuCodeList =
        filterList.stream()
            .skip((dto.getPage() - 1) * dto.getPageSize())
            .limit(dto.getPageSize())
            .collect(Collectors.toList());

    Page<AllowSaleListSpuVo> page = new Page<>();
    page.setTotal(total);
    page.setSize(dto.getPageSize());

    if (CollectionUtils.isNotEmpty(spuCodeList)) {
      List<ProductSpuVo> spuVoList = this.findProductSpu(dto, spuCodeList, productCodeSet);
      if (!CollectionUtils.isEmpty(spuVoList)) {
        Map<String, List<BigDecimal>> map =
            spuVoList.stream()
                .filter(
                    a ->
                        StringUtils.isNoneBlank(a.getSpuCode())
                            && !CollectionUtils.isEmpty(a.getPriceShowList()))
                .collect(
                    Collectors.toMap(
                        ProductSpuVo::getSpuCode, ProductSpuVo::getPriceShowList, (a, b) -> a));
        List<AllowSaleListSpuVo> records =
            (List<AllowSaleListSpuVo>)
                this.nebulaToolkitService.copyCollectionByBlankList(
                    spuVoList,
                    ProductSpuVo.class,
                    AllowSaleListSpuVo.class,
                    HashSet.class,
                    ArrayList.class,
                    "priceShowList");
        for (AllowSaleListSpuVo item : records) {
          item.setPriceShowList(map.get(item.getSpuCode()));
        }
        page.setRecords(records);
      }
    }
    return page;
  }

  /**
   * 获取spu信息、spu对应sku询价、促销标记
   *
   * @param dto
   * @param spuCodeList
   * @return
   */
  private List<ProductSpuVo> findProductSpu(
      AllowSaleListSpuPaginationDto dto, List<String> spuCodeList, Set<String> productCodeSet) {
    if (Objects.isNull(dto)
        || CollectionUtils.isEmpty(spuCodeList)
        || CollectionUtils.isEmpty(productCodeSet)) {
      log.warn("dto、spuCodeList、productCodeSet参数为空");
      return Lists.newLinkedList();
    }
    List<ProductSpu> spuList = this.productSpuRepository.findBySpuCodes(spuCodeList);
    if (CollectionUtils.isEmpty(spuList)) {
      log.warn("根据[{}]获取到的spuList为空", spuList);
      return Lists.newLinkedList();
    }
    // 获取客户信息
    List<CustomerVo> customerVoList =
        this.customerVoService.findByCustomerCodes(Lists.newArrayList(dto.getBusinessCode()));
    if (CollectionUtils.isEmpty(customerVoList)) {
      log.warn("根据[{}]获取到的客户信息为空", dto.getBusinessCode());
      return Lists.newLinkedList();
    }
    CustomerVo customerVo = customerVoList.stream().findFirst().get();

    Map<String, ProductSpu> mapSpu =
        spuList.stream()
            .filter(a -> StringUtils.isNotBlank(a.getSpuCode()))
            .collect(Collectors.toMap(ProductSpu::getSpuCode, Function.identity(), (a, b) -> a));
    List<ProductSpuRelateSku> skuList1 =
        this.productSpuRelateSkuRepository.findBySpuCodes(spuCodeList);
    if (CollectionUtils.isEmpty(skuList1)) {
      log.error("根据[{}]获取spu关联上架的sku信息为空", spuCodeList);
      return Lists.newLinkedList();
    }
    List<ProductSpuRelateSku> skuList =
        skuList1.stream()
            .filter(a -> productCodeSet.contains(a.getProductCode()))
            .collect(Collectors.toList());
    if (CollectionUtils.isEmpty(skuList)) {
      log.error("根据[{}]获取spu关联上架的sku信息为空", spuCodeList);
      return Lists.newLinkedList();
    }
    List<ProductSpuMedia> mediaList =
        this.productSpuMediaRepository.findBySpuCodesAndType(
            spuCodeList, MediaTypeEnum.PICTURE.getCode());
    Map<String, List<ProductSpuMedia>> mapMedia =
        mediaList.stream()
            .filter(a -> StringUtils.isNotBlank(a.getSpuCode()))
            .collect(Collectors.groupingBy(ProductSpuMedia::getSpuCode));
    Set<String> skuCodeSet =
        skuList.stream()
            .filter(a -> StringUtils.isNotBlank(a.getProductCode()))
            .map(ProductSpuRelateSku::getProductCode)
            .collect(Collectors.toSet());
    if (CollectionUtils.isEmpty(skuCodeSet)) {
      log.error("skuCodeSet为空");
      return Lists.newLinkedList();
    }
    // k-spuCode,v-商品编码集合
    Map<String, List<String>> mapSpuSku =
        skuList.stream()
            .filter(a -> StringUtils.isNoneBlank(a.getSpuCode(), a.getProductCode()))
            .collect(
                Collectors.groupingBy(
                    ProductSpuRelateSku::getSpuCode,
                    Collectors.mapping(ProductSpuRelateSku::getProductCode, Collectors.toList())));
    // 询价
    Map<String, PriceModelVo> priceModelVoMap = this.findPriceModel(skuCodeSet, customerVo);
    // TODO 促销标记
    return this.initSpu(spuCodeList, mapSpu, mapMedia, mapSpuSku, priceModelVoMap);
  }

  /**
   * 设置spu扩展信息、spu信息、图片信息、商品价格
   *
   * @param spuCodeList
   * @param mapSpu
   * @param mapMedia
   * @param mapSpuSku
   * @param priceModelVoMap
   * @return
   */
  private List<ProductSpuVo> initSpu(
      List<String> spuCodeList,
      Map<String, ProductSpu> mapSpu,
      Map<String, List<ProductSpuMedia>> mapMedia,
      Map<String, List<String>> mapSpuSku,
      Map<String, PriceModelVo> priceModelVoMap) {
    if (CollectionUtils.isEmpty(spuCodeList) || mapSpu.isEmpty() || mapSpuSku.isEmpty()) {
      log.error("initSpu的spuCodeList、mapSpu、mapSpuSku为空");
      return Lists.newLinkedList();
    }
    List<ProductSpuVo> list = Lists.newLinkedList();
    for (String spuCode : spuCodeList) {
      ProductSpuVo spuVo = new ProductSpuVo();
      ProductSpu spu = mapSpu.getOrDefault(spuCode, new ProductSpu());
      spuVo.setSpuCode(spuCode);
      spuVo.setSpuName(spu.getSpuName());
      spuVo.setIsShelf(spu.getIsShelf());
      spuVo.setBeginDateTime(spu.getBeginDateTime());
      spuVo.setEndDateTime(spu.getEndDateTime());
      spuVo.setSort(spu.getSort());
      List<ProductSpuMedia> mediaList = mapMedia.get(spuCode);
      if (CollectionUtils.isNotEmpty(mediaList)) {
        spuVo.setPictureList(
            (List<ProductSpuMediaVo>)
                this.nebulaToolkitService.copyCollectionByBlankList(
                    mediaList,
                    ProductSpuMedia.class,
                    ProductSpuMediaVo.class,
                    HashSet.class,
                    ArrayList.class));
      }
      List<String> productCodeList = mapSpuSku.get(spuCode);
      if (CollectionUtils.isNotEmpty(productCodeList)) {
        List<ProductSpuRelateSkuVo> productList = Lists.newLinkedList();
        for (String productCode : productCodeList) {
          PriceModelVo priceModelVo = priceModelVoMap.getOrDefault(productCode, new PriceModelVo());
          ProductSpuRelateSkuVo skuVo = new ProductSpuRelateSkuVo();
          skuVo.setProductCode(productCode);
          skuVo.setPrice(priceModelVo.getPrice());
          productList.add(skuVo);
        }
        spuVo.setProductList(productList);
        spuVo.setPriceShowList(this.findPriceShow(productList));
      }
      list.add(spuVo);
    }
    return list;
  }

  /**
   * 价格展示
   *
   * @param productList
   * @return
   */
  private List<BigDecimal> findPriceShow(List<ProductSpuRelateSkuVo> productList) {
    if (CollectionUtils.isEmpty(productList)) {
      return Lists.newLinkedList();
    }
    return productList.stream()
        .filter(a -> Objects.nonNull(a.getPrice()))
        .map(ProductSpuRelateSkuVo::getPrice)
        .distinct()
        .sorted()
        .collect(Collectors.toList());
  }

  /**
   * 询价
   *
   * @param skuCodeSet
   * @param customerVo
   * @return
   */
  private Map<String, PriceModelVo> findPriceModel(Set<String> skuCodeSet, CustomerVo customerVo) {
    if (CollectionUtils.isEmpty(skuCodeSet)
        || Objects.isNull(customerVo)
        || StringUtils.isBlank(customerVo.getCustomerCode())) {
      return Maps.newHashMap();
    }
    FindPriceDto dto = new FindPriceDto();
    dto.setUserCode(customerVo.getCustomerCode());
    dto.setUserType(FindPriceUserTypeEnum.CUSTOMER.getDictCode());
    dto.setProductCodeSet(skuCodeSet);
    return this.priceModelVoService.findPrice(dto);
  }

  /**
   * 根据keyword+productLevelCode+productCodeList筛选符合的商品编码集合信息
   *
   * @param keyword
   * @param productLevelCode
   * @param productCodeList
   * @return
   */
  private Set<String> findProductCodeSet(
      String keyword, String productLevelCode, List<String> productCodeList) {
    Set<String> set = Sets.newHashSet();

    String ruleCode = StringUtils.EMPTY;
    if (StringUtils.isNotBlank(productLevelCode)) {
      ProductLevelVo productLevelVo =
          this.productLevelVoSdkService.findDetailsByCode(productLevelCode);
      if (Objects.nonNull(productCodeList)) {
        ruleCode = productLevelVo.getRuleCode();
      }
    }

    for (List<String> item : Lists.partition(productCodeList, 2000)) {
      Set<String> curSet =
          this.allowSaleListSpuVoRepository.findProductCodeSet(keyword, ruleCode, item);
      if (CollectionUtils.isNotEmpty(curSet)) {
        set.addAll(curSet);
      }
    }
    return set;
  }
}
