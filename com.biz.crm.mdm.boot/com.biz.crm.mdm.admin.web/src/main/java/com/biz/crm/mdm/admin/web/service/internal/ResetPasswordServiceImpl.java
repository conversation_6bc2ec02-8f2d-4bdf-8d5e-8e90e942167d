package com.biz.crm.mdm.admin.web.service.internal;

import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.enums.LoginFromTypeEnum;

import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.admin.web.dto.ResetPasswordDto;
import com.biz.crm.mdm.admin.web.service.ResetPasswordService;
import com.biz.crm.mdm.admin.web.strategy.LoginUserManageStrategy;
import com.biz.crm.mdm.business.user.sdk.dto.UserChangePasswordDto;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 重置密码服务实现
 *
 * <AUTHOR>
 */
@Service
public class ResetPasswordServiceImpl implements ResetPasswordService {

  @Autowired(required = false)
  private List<LoginUserManageStrategy> loginFromTypeStrategies;

  @Autowired(required = false)
  private LoginUserService loginUserService;

  @Autowired(required = false)
  private UserVoService userVoService;

  @Override
  public void updatePasswordByPhoneAndVerificationCode(Integer appType, String phone, String password, String verificationCode) {
    // 检查业务系统类型
    LoginFromTypeEnum loginFromTypeEnum = LoginFromTypeEnum.getEnumByAppType(appType);
    Validate.notNull(loginFromTypeEnum, "业务系统类型未知！");
    // 根据手机号和短信验证码修改密码
    LoginUserManageStrategy strategy = this.loginFromTypeStrategies.stream().filter(a -> loginFromTypeEnum.equals(a.loginFromTypeEnum())).findFirst().orElse(null);
    Validate.notNull(strategy, "未匹配到当前标识业务系统类型【%s】的校验策略！", loginFromTypeEnum.getDesc());
    strategy.updatePasswordByPhoneAndVerificationCode(phone, password, verificationCode);
  }

  @Override
  public void updatePasswordInLoginStatus(ResetPasswordDto resetPasswordDto) {
    FacturerUserDetails loginDetails = this.loginUserService.getLoginDetails(FacturerUserDetails.class);
    Validate.notNull(loginDetails, "请您先登录！");
    Validate.notBlank(resetPasswordDto.getOriginalPassword(), "原密码不能为空！");
    Validate.notBlank(resetPasswordDto.getNewPassword(), "新密码不能为空！");
    Validate.notBlank(resetPasswordDto.getConfirmPassword(), "确认密码不能为空！");
    Validate.isTrue(resetPasswordDto.getNewPassword().equals(resetPasswordDto.getConfirmPassword()), "新密码与确认密码不一致，请重新输入！");
    // 修改密码
    UserChangePasswordDto userChangePasswordDto = new UserChangePasswordDto();
    userChangePasswordDto.setUserName(loginDetails.getAccount());
    userChangePasswordDto.setNewPassword(resetPasswordDto.getNewPassword());
    userChangePasswordDto.setOldPassword(resetPasswordDto.getOriginalPassword());
    this.userVoService.updatePasswordByUserName(userChangePasswordDto);
  }
}
