package com.biz.crm.mdm.admin.web.dataview.product;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * MDM奶卡成本价数据视图
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/6/18 19:00
 */
@Component
public class PriceMilkCostDataViewRegister implements DataviewRegister {
    @Override
    public String code() {
        return "mdm_price_milk_cost_data_view";
    }

    @Override
    public String desc() {
        return "MDM奶卡成本价数据视图";
    }

    @Override
    public String buildSql() {
        return "select a.* " +
                "   from mdm_price_milk_cost a   " +
                "   where a.del_flag = '" + DelFlagStatusEnum.NORMAL.getCode() + "' " +
                "   and a.tenant_code = :tenantCode ";
    }
}
