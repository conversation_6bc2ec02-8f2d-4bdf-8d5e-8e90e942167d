package com.biz.crm.mdm.admin.web.report.position.dto;

import com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 已关联当前角色的职位分页列表查询条件Dto
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "已关联当前角色的职位分页列表查询条件Dto", description = "已关联当前角色的职位分页列表查询条件Dto")
public class HasRelateCurrentRolePositionPageDto extends AbstractPositionPageDto {

  /**
   * 角色编码
   */
  @ApiModelProperty("角色编码")
  private String roleCode;

  /**
   * 用户姓名
   */
  @ApiModelProperty("用户姓名")
  private String fullName;

  /**
   * 职位名称
   */
  @ApiModelProperty("职位名称")
  private String positionName;

  /**
   * 分页来源
   */
  @ApiModelProperty(value = "分页来源",hidden = true)
  private String pageSource = PositionReportConstant.POSITION_PAGE_SOURCE_HAS_RELATE_CURRENT_ROLE_POSITION_LIST;
}
