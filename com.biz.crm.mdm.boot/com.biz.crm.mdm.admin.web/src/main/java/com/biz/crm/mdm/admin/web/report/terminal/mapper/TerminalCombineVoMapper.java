package com.biz.crm.mdm.admin.web.report.terminal.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.terminal.dto.TerminalCombinePageDto;
import com.biz.crm.mdm.admin.web.report.terminal.vo.TerminalCombineVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 终端组合信息VO(mapper)
 * @date 2024/02/29
 */
public interface TerminalCombineVoMapper {

  /**
   * 终端组合信息分页条件查询
   *
   * @param page 分页信息
   * @param dto 查询条件参数dto
   * @return 分页数据
   */
  Page<TerminalCombineVo> findByConditions(Page<TerminalCombinePageDto> page, @Param("dto")TerminalCombinePageDto dto);
}
