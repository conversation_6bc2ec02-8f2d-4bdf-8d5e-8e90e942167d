package com.biz.crm.mdm.admin.web.report.positionlevel.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.positionlevel.dto.PositionLevelPageDto;
import com.biz.crm.mdm.admin.web.report.positionlevel.vo.PositionLevelPageVo;
import org.apache.ibatis.annotations.Param;

/**
 * 职位级别分页Vo mybatis-plus接口类
 *
 * <AUTHOR>
 * @date 2022/6/21
 */
public interface PositionLevelPageVoMapper {

  /**
   * 职位级别分页列表
   *
   * @param dto  请求参数dto
   * @param page 分页信息
   * @return 职位级别分页列表
   */
  Page<PositionLevelPageVo> findByPositionLevelPageDto(@Param("page") Page<PositionLevelPageVo> page, @Param("dto") PositionLevelPageDto dto);
}
