package com.biz.crm.mdm.admin.web.dataview.productspu;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @describe:MDM商城展示管理数据视图
 * @createTime 2022年09月13日 17:03:00
 */
@Component
public class ProductSpuDataviewRegister implements DataviewRegister {
  @Override
  public String code() {
    return "mdm_product_spu_dataview";
  }

  @Override
  public String desc() {
    return "MDM商城展示管理数据视图";
  }

  @Override
  public String buildSql() {
    return "select mps.*,mpk.product_quantity from mdm_product_spu  mps left join (select spu_code ,count(*)as product_quantity  from mdm_product_spu_relate_sku group by spu_code) mpk on mps.spu_code = mpk.spu_code " +
            " where mps.del_flag = '" + DelFlagStatusEnum.NORMAL.getCode() + "' and mps.tenant_code= :tenantCode";
  }


}
