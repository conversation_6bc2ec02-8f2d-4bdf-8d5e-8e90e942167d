package com.biz.crm.mdm.admin.web.report.user.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户关联信息查询dto
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel
public class UserRelationDto extends TenantFlagOpDto {

  /**
   * 用户ID
   */
  @ApiModelProperty("用户ID")
  private String userId;

  /**
   * 帐户
   */
  @ApiModelProperty("账户")
  private String userName;

}
