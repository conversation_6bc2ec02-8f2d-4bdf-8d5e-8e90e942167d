package com.biz.crm.mdm.admin.web.report.terminal.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.admin.web.report.terminal.dto.TerminalCombinePageDto;
import com.biz.crm.mdm.admin.web.report.terminal.service.TerminalCombineVoService;
import com.biz.crm.mdm.admin.web.report.terminal.vo.TerminalCombineVo;
import com.biz.crm.mdm.admin.web.service.OrgInitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description MDM管理后台BOOT：TerminalCombineVo: 终端组合信息
 * @date 2024/02/29
 */
@Slf4j
@Api(tags = "MDM管理后台BOOT：TerminalCombineVo: 终端组合信息")
@RestController
@RequestMapping(value = {"/v1/adminWeb/terminalCombine"})
public class TerminalCombineVoController {

  @Autowired
  private TerminalCombineVoService terminalCombineVoService;

  @Autowired
  private OrgInitService orgInitService;

  /**
   * 终端组合信息分页条件查询
   *
   * @param pageable 分页信息
   * @param dto      查询条件参数dto
   * @return 分页数据
   */
  @ApiOperation(value = "终端组合信息分页条件查询")
  @GetMapping(value = {"/findByConditions"})
  public Result<Page<TerminalCombineVo>> findByConditions(@PageableDefault(50) Pageable pageable, TerminalCombinePageDto dto) {
    try {
      Page<TerminalCombineVo> result = this.terminalCombineVoService.findByConditions(pageable, dto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  @GetMapping(value = {"/initOrg"})
  public Result<?> initOrg() {
    orgInitService.orgInit();
    return Result.ok();
  }
}
