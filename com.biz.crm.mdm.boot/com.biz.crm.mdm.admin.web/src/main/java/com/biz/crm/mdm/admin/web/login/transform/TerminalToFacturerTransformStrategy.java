package com.biz.crm.mdm.admin.web.login.transform;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.mdm.admin.web.login.DefaultPerfectLoginUserDetails;
import com.biz.crm.mdm.business.user.sdk.service.UserValidityCheckService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.bizunited.nebula.security.sdk.config.SimpleSecurityProperties;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import com.bizunited.nebula.security.sdk.transform.IdentityTransformStrategy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * CRM系统厂商管理平台（MDM+DMS+CPS+SFA+TPM）boot，在收到外部子系统的调用请求后（例如EMS经销商电商），将终端用户转换为厂商用户的身份转换器
 *
 * <AUTHOR>
 */
@Component("TerminalToFacturerTransformStrategy")
public class TerminalToFacturerTransformStrategy extends
    DefaultPerfectLoginUserDetails implements IdentityTransformStrategy {

  @Autowired
  private SimpleSecurityProperties simpleSecurityProperties;
  @Autowired
  private UserValidityCheckService userValidityCheckService;
  
  /**
   * 日志
   */
  private static final Logger LOGGER = LoggerFactory
      .getLogger(TerminalToFacturerTransformStrategy.class);


  @Override
  public boolean matched(String sourceIdentityType) {
    //consumer-消费者  terminal-终端  user_guide- 导购
    return StringUtils.equals("terminal", sourceIdentityType);
  }

  @Override
  public UserIdentity transform(String sourceIdentityType, String sourceTenantCode,
      String sourceAccount, JSONObject userObject) {
    FacturerUserDetails mdmUser = new FacturerUserDetails();
    // TODO 目前使用配置文件中，默认的管理员账号和管理员角色，作为调用者的身份
    String account = this.simpleSecurityProperties.getIndependencyUser();
    Integer type = this.simpleSecurityProperties.getDefaultLoginType();
    String[] independencyRoles = this.simpleSecurityProperties.getIndependencyRoles();

    // 转换成管理员用户
    UserVo userVo = this.userValidityCheckService.verificationManageByAccount(account);
    mdmUser.setLoginType(type);
    mdmUser.setTenantCode(sourceTenantCode);
    mdmUser.setRoleCodes(independencyRoles);
    super.perfectLoginUserDetails(userVo, mdmUser);
    super.perfectLoginPostAndOrg(mdmUser);
    return mdmUser;
  }

  @Override
  public int getOrder() {
    // 默认转换策略，最后一个执行
    return Integer.MAX_VALUE;
  }

}
