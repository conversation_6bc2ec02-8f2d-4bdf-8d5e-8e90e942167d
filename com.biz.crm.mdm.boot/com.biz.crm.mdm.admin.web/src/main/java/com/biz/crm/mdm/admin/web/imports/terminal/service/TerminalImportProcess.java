package com.biz.crm.mdm.admin.web.imports.terminal.service;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.admin.web.imports.terminal.model.TerminalCrmImportVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.region.sdk.constant.RegionConstant;
import com.biz.crm.mdm.business.region.sdk.service.RegionVoService;
import com.biz.crm.mdm.business.terminal.local.entity.Terminal;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalContact;
import com.biz.crm.mdm.business.terminal.local.service.TerminalContactService;
import com.biz.crm.mdm.business.terminal.local.service.TerminalRelaCustomerOrgService;
import com.biz.crm.mdm.business.terminal.local.service.TerminalRelaOrgService;
import com.biz.crm.mdm.business.terminal.local.service.TerminalService;
import com.biz.crm.workflow.sdk.enums.ProcessStatusEnum;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 终端信息导入
 *
 * <AUTHOR>
 * @date 2022/5/13
 */
@Component
@Slf4j
public class TerminalImportProcess implements ImportProcess<TerminalCrmImportVo> {

  @Autowired(required = false) private DictDataVoService dictDataVoService;

  @Autowired(required = false) private RegionVoService regionVoService;

  @Autowired(required = false) private TerminalService terminalService;

  @Autowired(required = false) private TerminalRelaOrgService terminalRelaOrgService;

  @Autowired(required = false) private TerminalRelaCustomerOrgService terminalRelaCustomerOrgService;

  @Autowired(required = false) private TerminalContactService terminalContactService;

  @Autowired(required = false) private NebulaToolkitService nebulaToolkitService;

  /** 终端类型 */
  private static final String DICT_TERMINAL_TYPE = "terminal_type";
  /** 渠道 */
  private static final String DICT_CHANNEL = "channel";

  @Override
  @Transactional
  public Map<Integer, String> execute(
      LinkedHashMap<Integer, TerminalCrmImportVo> data,
      TaskGlobalParamsVo paramsVo,
      Map<String, Object> params) {
    final Optional<TerminalCrmImportVo> first = data.values().stream().findFirst();
    if (!first.isPresent()) {
      return null;
    }
    TerminalCrmImportVo vo = first.get();
    this.validate(vo, params);
    this.execute(vo, paramsVo, params);
    return null;
  }

  /**
   * 是否开启先校验后导入的模式 默认false: false执行旧逻辑 true执行新逻辑(先校验再保存)
   * 新逻辑需同时实现接口 tryVerify tryConfirm
   *
   * @return
   */
  @Override
  public boolean importBeforeValidationFlag() {
    return true;
  }

  /**
   * 数据校验
   *
   * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
   * @param paramsVo 任务公共参数
   * @param params   导入任务自定义参数
   * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
   */
  @Override
  public Map<Integer, String> tryVerify(LinkedHashMap<Integer, TerminalCrmImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
    final Optional<TerminalCrmImportVo> first = data.values().stream().findFirst();
    if (!first.isPresent()) {
      return null;
    }
    TerminalCrmImportVo vo = first.get();
    this.validate(vo, params);
    return null;
  }

  /**
   * 数据存储
   *
   * @param data     待处理的数据集合，k-流水号，v-excel解析后的对象
   * @param paramsVo 任务公共参数
   * @param params   导入任务自定义参数
   * @return k-对应data的k，v-对应data的k对应的v处理异常描述信息，会回写到错误文件
   */
  @Override
  public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, TerminalCrmImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
    final Optional<TerminalCrmImportVo> first = data.values().stream().findFirst();
    if (!first.isPresent()) {
      return null;
    }
    TerminalCrmImportVo vo = first.get();
    this.execute(vo, paramsVo, params);
    return null;
  }

  private void execute(
      TerminalCrmImportVo vo, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
    final List<Terminal> list =
        this.terminalService.findByTerminalCodes(Lists.newArrayList(vo.getTerminalCode()));
    Validate.isTrue(CollectionUtils.isEmpty(list), "当前终端编码已重复");
    if (CollectionUtils.isEmpty(list)) {
      Terminal terminal =
          this.nebulaToolkitService.copyObjectByBlankList(
              vo, Terminal.class, HashSet.class, ArrayList.class);
      terminal.setTenantCode(paramsVo.getTenantCode());
      terminal.setCreateAccount(paramsVo.getCreateAccount());
      terminal.setCreateName(paramsVo.getCreateAccountName());
      terminal.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
      terminal.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
      terminal.setProcessStatus(ProcessStatusEnum.PASS.getDictCode());
      this.terminalService.create(terminal);
      this.terminalRelaOrgService.create(
          vo.getOrgCode(), vo.getTerminalCode(), paramsVo.getTenantCode());
      this.terminalRelaCustomerOrgService.create(
          vo.getCustomerOrgCode(), vo.getTerminalCode(), paramsVo.getTenantCode());
    }
    if (StringUtils.isNotBlank(vo.getContactName())) {
      TerminalContact contact = new TerminalContact();
      contact.setTerminalCode(vo.getTerminalCode());
      contact.setContactName(vo.getContactName());
      contact.setContactPhone(vo.getContactPhone());
      contact.setTenantCode(paramsVo.getTenantCode());
      contact.setContactMain(false);
      this.terminalContactService.create(contact);
    }
  }

  private void validate(TerminalCrmImportVo vo, Map<String, Object> params) {
    Boolean f =
        StringUtils.isNoneBlank(
            vo.getTerminalCode(),
            vo.getTerminalName(),
            vo.getTerminalType(),
            vo.getOrgCode(),
            vo.getCustomerOrgCode(),
            vo.getChannel());
    Validate.isTrue(f, "终端编码、名称、类型、组织、客户组织、渠道必填");
    vo.setTerminalType(this.findDictCode(vo.getTerminalType(), DICT_TERMINAL_TYPE));
    Validate.notBlank(vo.getTerminalType(), "终端类型对应的数据字典值不存在");
    vo.setChannel(this.findDictCode(vo.getChannel(), DICT_CHANNEL));
    Validate.notBlank(vo.getChannel(), "渠道对应的数据字典值不存在");

    String provinceName = vo.getProvinceName();
    String cityName = vo.getCityName();
    String districtName = vo.getDistrictName();

    if (StringUtils.isNotBlank(provinceName)) {
      String regionCode =
          this.regionVoService.findRegionCode(provinceName, RegionConstant.PROVINCE_PARENT_CODE);
      vo.setProvinceCode(regionCode);
      Validate.notBlank(vo.getProvinceCode(), "省份信息异常");
    }

    if (StringUtils.isNotBlank(cityName)) {
      String regionCode = this.regionVoService.findRegionCode(cityName, vo.getProvinceCode());
      vo.setCityCode(regionCode);
      Validate.notBlank(vo.getCityCode(), "市信息异常");
    }

    if (StringUtils.isNotBlank(districtName)) {
      String regionCode = this.regionVoService.findRegionCode(districtName, vo.getCityCode());
      vo.setDistrictCode(regionCode);
      Validate.notBlank(vo.getDistrictCode(), "区信息异常");
    }
  }

  @Override
  public Class findCrmExcelVoClass() {
    return TerminalCrmImportVo.class;
  }

  @Override
  public String getTemplateCode() {
    return "MDM_TERMINAL_IMPORT";
  }

  @Override
  public String getTemplateName() {
    return "MDM终端信息导入";
  }

  /**
   * 获取字典对应的code值
   *
   * @param dictValue
   * @param typeCode
   * @return
   */
  private String findDictCode(String dictValue, String typeCode) {
    Map<String, List<DictDataVo>> map =
        this.dictDataVoService.findByDictTypeCodeList(
            Lists.newArrayList(DICT_TERMINAL_TYPE, DICT_CHANNEL));
    final Optional<DictDataVo> first =
        map.get(typeCode).stream().filter(a -> dictValue.equals(a.getDictValue())).findFirst();
    if (first.isPresent()) {
      return first.get().getDictCode();
    }
    return null;
  }
}
