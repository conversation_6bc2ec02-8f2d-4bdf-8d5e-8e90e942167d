package com.biz.crm.mdm.admin.web.dataview.user;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @describe: 企业用户数据视图
 * @createTime 2022年09月13日 16:17:00
 */
@Component
public class UserDataviewRegister implements DataviewRegister {
  @Override
  public String code() {
    return "mdm_user_dataview";
  }

  @Override
  public String desc() {
    return "MDM企业用户数据视图";
  }

@Override
public String buildSql() {
  return "SELECT DISTINCT " +
          "u.id, " +
          "u.user_code , " +
          "u.user_name , " +
          "u.full_name, " +
          "u.user_type, " +
          "u.JOB_CODE,"+
          "u.user_phone, " +
          "o.org_name, " +
          "o.org_code, " +
          "p.position_code, " +
          "p.position_name, " +
          "p.position_level_code, " +
          "pl.position_level_name, " +
//      "u.email, " +
//      "u.gender, " +
//      "u.end_time, " +
//      "u.last_login_time, " +
          "u.create_time, " +
          "u.start_time, " +
          "u.lock_state, " +
          "u.enable_status " +
          "FROM mdm_user u " +
          "LEFT JOIN mdm_user_position t1 ON t1.tenant_code = u.tenant_code AND t1.user_name = u.user_name AND t1.primary_flag='1' " +
          "LEFT JOIN mdm_position p ON p.tenant_code = t1.tenant_code AND p.position_code = t1.position_code " +
          "LEFT JOIN mdm_position_level pl ON p.tenant_code = pl.tenant_code AND p.position_level_code = pl.position_level_code " +
          "LEFT JOIN mdm_org_position op ON op.tenant_code = p.tenant_code AND op.position_code = p.position_code " +
          "LEFT JOIN mdm_org o ON o.tenant_code = op.tenant_code AND o.org_code = op.org_code " +
          "WHERE u.del_flag= '" + DelFlagStatusEnum.NORMAL.getCode() + "' and u.user_type in ('u', 'user_guide') and u.tenant_code= :tenantCode and o.rule_code like concat(:ruleCode,'%')";
  }
}
