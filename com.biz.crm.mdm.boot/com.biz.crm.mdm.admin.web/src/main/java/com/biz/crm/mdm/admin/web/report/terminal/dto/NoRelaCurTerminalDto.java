package com.biz.crm.mdm.admin.web.report.terminal.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 非常关键
 * <p></p>
 * 企业用户查询时必传userName参数
 * <p>
 * 客户用户查询时必传customerUserName
 * </p>
 * 客户查询时必传customerCode
 *
 * <AUTHOR>
 * @date 2021/11/17
 */
@Getter
@Setter
@ToString
@ApiModel(value = "NoRelaCurTerminalDto", description = "有供货关系类终端report分页查询dto-未关联了当前客户，客户用户，企业用户")
public class NoRelaCurTerminalDto extends TerminalSupplyReportPaginationDto {

  /**
   * 客户编码
   */
  @ApiModelProperty(value = "客户编码")
  private String customerCode;

  /**
   * 客户用户账号信息
   */
  @ApiModelProperty(value = "客户用户账号信息")
  private String customerUserName;

  /**
   * 用来获取positionCodeList进行查询
   */
  @ApiModelProperty(value = "企业用户账号信息")
  private String userName;

  /**
   * 删除标记
   */
  @ApiModelProperty(value = "删除标记")
  private String delFlag;
}
