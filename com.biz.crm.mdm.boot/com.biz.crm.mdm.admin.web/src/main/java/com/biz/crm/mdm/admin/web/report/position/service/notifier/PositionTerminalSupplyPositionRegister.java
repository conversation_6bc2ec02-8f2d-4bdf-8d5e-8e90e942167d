package com.biz.crm.mdm.admin.web.report.position.service.notifier;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.position.dto.SelectPositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.service.PositionPageVoService;
import com.biz.crm.mdm.admin.web.report.position.vo.PositionPageVo;
import com.biz.crm.mdm.business.terminal.sdk.event.TerminalSupplyPositionRegister;
import com.biz.crm.mdm.business.terminal.sdk.vo.SupplyPositionPageVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

/**
 * 实现岗位编码获取岗位对应的扩展信息
 *
 * <AUTHOR>
 * @date 2022/3/17
 */
@Component
public class PositionTerminalSupplyPositionRegister implements TerminalSupplyPositionRegister {

  @Autowired(required = false) private PositionPageVoService positionPageVoService;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public List<SupplyPositionPageVo> onRequestByPositionCodes(Set<String> positionCodeSet) {
    if (CollectionUtils.isEmpty(positionCodeSet)) {
      return Lists.newLinkedList();
    }
    Pageable pageable = PageRequest.of(1, positionCodeSet.size());
    SelectPositionPageDto dto = new SelectPositionPageDto();
    dto.setSelectedCodes(Lists.newArrayList(positionCodeSet));
    Page<PositionPageVo> page = this.positionPageVoService.findByConditions(pageable, dto);
    if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getRecords())) {
      return Lists.newLinkedList();
    }
    return (List<SupplyPositionPageVo>)
        this.nebulaToolkitService.copyCollectionByBlankList(
            page.getRecords(),
            PositionPageVo.class,
            SupplyPositionPageVo.class,
            HashSet.class,
            ArrayList.class);
  }
}
