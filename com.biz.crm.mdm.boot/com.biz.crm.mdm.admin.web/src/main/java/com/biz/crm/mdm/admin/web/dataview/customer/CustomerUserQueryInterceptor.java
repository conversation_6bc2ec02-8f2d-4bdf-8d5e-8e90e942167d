package com.biz.crm.mdm.admin.web.dataview.customer;

import com.biz.crm.mdm.business.customer.user.local.entity.CustomerUser;
import com.biz.crm.mdm.business.customer.user.local.entity.CustomerUserRelaCustomer;
import com.biz.crm.mdm.business.customer.user.local.service.CustomerUserRelaCustomerService;
import com.bizunited.nebula.europa.database.sdk.strategy.ExternalQueryInterceptor;
import com.bizunited.nebula.europa.sdk.context.execute.ExecuteContent;
import com.bizunited.nebula.europa.sdk.context.matedata.MetaData;
import com.bizunited.nebula.europa.sdk.vo.EuropaInfoVo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.persistence.EntityManager;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * mdm客户用户数据视图补充
 */
@Component
public class CustomerUserQueryInterceptor implements ExternalQueryInterceptor {

  @Autowired
  private CustomerUserRelaCustomerService customerUserRelaCustomerService;

  private static final String USER_CODE = "user_code";

  @Override
  public String code() {
    return "mdm_customerUser_queryInterceptor";
  }

  @Override
  public String name() {
    return "mdm客户用户-关联客户编码和名称补充";
  }

  @Override
  public List<Object[]> process(EntityManager entityManager, MetaData metaData, EuropaInfoVo europaInfoVo, ExecuteContent executeContent, String... strings) {
    List<Map<String, Object>> resutls = executeContent.getResults();
    if (CollectionUtils.isEmpty(resutls)) {
      return null;
    }
    if (0 == resutls.parallelStream().filter(row -> row != null).count()) {
      return Lists.newLinkedList();
    }
    List<String> userCodes = Lists.newArrayList();
    resutls.forEach(map -> {
      if(map.containsKey(USER_CODE)){
        userCodes.add((String) map.get(USER_CODE));
      }
    });
    List<CustomerUserRelaCustomer> cusUserRelaList =
            customerUserRelaCustomerService.findByUserCodes(userCodes);
    Map<String, List<CustomerUserRelaCustomer>> cusUserRelaMapByCode =
            cusUserRelaList.stream()
                    .collect(Collectors.groupingBy(CustomerUserRelaCustomer::getUserCode));
    // 组装关联客户信息
    List<Object[]> externalContents = Lists.newArrayList();
    for (int index = 0; index < resutls.size(); index++) {
      List<Object> itemList = Lists.newArrayList();
      String userCode = (String) resutls.get(index).get(USER_CODE);
      if(StringUtils.isBlank(userCode)){
        continue;
      }
      List<CustomerUserRelaCustomer> relaCustomers = cusUserRelaMapByCode.get(userCode);
      if(CollectionUtils.isEmpty(relaCustomers)){
        continue;
      }
      List<String> relationCodes = relaCustomers.stream().map(CustomerUserRelaCustomer::getCustomerCode).collect(Collectors.toList());
      List<String> relationNames = relaCustomers.stream().map(CustomerUserRelaCustomer::getCustomerName).collect(Collectors.toList());
      for (String externalFieldName : strings) {
        if (StringUtils.equals(externalFieldName, "customer_code")) {
          itemList.add(StringUtils.join(relationCodes,","));
        } else {
          itemList.add(StringUtils.join(relationNames,","));
        }
      }
      externalContents.add(itemList.toArray(new Object[]{}));
    }
    return externalContents;
  }
}
