package com.biz.crm.mdm.admin.web.service;

import com.biz.crm.mdm.admin.web.dto.GenerateJwtByUserAccountDto;
import com.biz.crm.mdm.admin.web.vo.GenerateJwtByUserAccountVo;

/**
 * <AUTHOR>
 * @date 2023-04-18 11:14
 * @description：通过用户基本信息生成对应jwt
 */
public interface GenerateJwtByUserAccountService {

  /**
   * 通过用户基本信息生成对应jwt
   * @param dto
   * @return
   */
  GenerateJwtByUserAccountVo create(GenerateJwtByUserAccountDto dto);
}
