package com.biz.crm.mdm.admin.web.report.customermaterial.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.customermaterial.service.CustomerMaterialVoService;
import com.biz.crm.mdm.admin.web.report.customermaterial.vo.CustomerMaterialVo;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.customer.material.sdk.dto.CustomerMaterialDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * 客户物料VO 接口
 */
@Api(tags = "客户物料: CustomerMaterialVo: 客户物料查询分页")
@RestController
@RequestMapping(value = {"/v1/customermaterial/report"})
public class CustomerMaterialVoController {

  @Autowired(required = false)
  private CustomerMaterialVoService customerMaterialVoService;

  @ApiOperation(value = "分页查询")
  @GetMapping("/findByConditions")
  public Result<Page<CustomerMaterialVo>> findByConditions(@PageableDefault(50) Pageable pageable,
                                                           CustomerMaterialDto customerMaterialDto) {
    return Result.ok(this.customerMaterialVoService.findByConditions(pageable, customerMaterialDto));
  }
}
