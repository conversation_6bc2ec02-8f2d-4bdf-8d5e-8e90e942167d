package com.biz.crm.mdm.admin.web.report.user.service;

import com.biz.crm.mdm.admin.web.report.user.dto.UserRelationDto;
import com.biz.crm.mdm.admin.web.report.user.vo.UserRelationVo;

import java.util.List;

/**
 * 用户关联信息服务接口
 *
 * <AUTHOR>
 * @since 2021-11-04 17:38:46
 */
public interface UserRelationVoService {

  /**
   * 分页条件查询
   *
   * @param dto
   * @return
   */
  List<UserRelationVo> findByConditions(UserRelationDto dto);

}
