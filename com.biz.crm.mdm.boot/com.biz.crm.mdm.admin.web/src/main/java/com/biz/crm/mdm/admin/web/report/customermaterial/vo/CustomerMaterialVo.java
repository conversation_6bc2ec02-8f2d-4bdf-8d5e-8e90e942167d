package com.biz.crm.mdm.admin.web.report.customermaterial.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 客户物料实体VO
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CustomerMaterialVo", description = "客户物料实体VO")
public class CustomerMaterialVo extends TenantFlagOpVo {

  private static final long serialVersionUID = -8309283044725871182L;

  /**
   * 客户组织编码
   */
  @ApiModelProperty("客户组织编码")
  private String customerOrgCode;

  /**
   * 客户组织名称
   */
  @ApiModelProperty("客户组织名称")
  private String customerOrgName;

  /**
   * 物料编码
   */
  @ApiModelProperty("物料编码")
  private String materialCode;

  /**
   * 物料名称
   */
  @ApiModelProperty("物料名称")
  private String materialName;

  /**
   * 条形码
   */
  @ApiModelProperty("条形码")
  private String barCode;

}
