package com.biz.crm.mdm.admin.web.report.position.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.position.dto.PositionCombinePageDto;
import com.biz.crm.mdm.admin.web.report.position.vo.PositionCombineVo;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @description 职位组合信息VO服务接口类
 * @date 2024/02/29
 */
public interface PositionCombineVoService {

  /**
   * 职位组合信息分页条件查询
   *
   * @param pageable 分页信息
   * @param dto 查询条件参数dto
   * @return 分页数据
   */
  Page<PositionCombineVo> findByConditions(Pageable pageable, PositionCombinePageDto dto);
}
