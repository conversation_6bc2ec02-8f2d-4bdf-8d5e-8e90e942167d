package com.biz.crm.mdm.admin.web.report.product.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.product.dto.ProductReportPaginationDto;
import com.biz.crm.mdm.admin.web.report.product.vo.ProductReportVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2021/11/4
 */
public interface ProductReportVoMapper {

  /**
   * 分页信息
   *
   * @param page
   * @param dto
   * @return
   */
  Page<ProductReportVo> findByConditions(Page<ProductReportVo> page, @Param("dto") ProductReportPaginationDto dto);
}
