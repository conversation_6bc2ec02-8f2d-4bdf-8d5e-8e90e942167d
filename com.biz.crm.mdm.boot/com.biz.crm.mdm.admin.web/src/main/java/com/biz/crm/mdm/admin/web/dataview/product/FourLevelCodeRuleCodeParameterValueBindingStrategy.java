package com.biz.crm.mdm.admin.web.dataview.product;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.biz.crm.mdm.business.product.level.local.repository.ProductLevelSapRepository;
import com.bizunited.nebula.europa.database.sdk.context.execute.DatabaseExecuteExternalRequest;
import com.bizunited.nebula.europa.database.sdk.strategy.ParameterValueBindingStrategy;
import com.bizunited.nebula.europa.sdk.context.execute.ExecuteParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @title ProductRuleCodeParameterValueBindingStrategy
 * @date 2023/2/1 10:39
 * @description 【通用】产品层级ruleCode规则编码取值
 */
@Component
public class FourLevelCodeRuleCodeParameterValueBindingStrategy implements ParameterValueBindingStrategy {

  @Autowired
  private ProductLevelSapRepository productLevelSapRepository;

  @Override
  public String getBindType() {
    return "General_Material_FourLevelCode_RuleCode";
  }

  @Override
  public String getBindTypeCnName() {
    return "【物料管理专用】SAP产品层级ruleCode规则编码取值";
  }

  @Override
  public Boolean getOutside() {
    return false;
  }

  @Override
  public Class<?>[] matchedJavaClasses() {
    return new Class[]{CharSequence.class};
  }

  @Override
  public Object bindingFieldValue(ExecuteParameter executeParameter, DatabaseExecuteExternalRequest databaseExecuteExternalRequest) {
    String productLevelCode = (String) databaseExecuteExternalRequest.getAttribute("fourLevelCode");
    if (StringUtils.isEmpty(productLevelCode)){
      return null;
    }
    String ruleCode = productLevelSapRepository.findRuleCodeByProductLevelCode(productLevelCode);
    if (StringUtils.isEmpty(ruleCode)) {
        return null;
    }
    return ruleCode;
  }
}
