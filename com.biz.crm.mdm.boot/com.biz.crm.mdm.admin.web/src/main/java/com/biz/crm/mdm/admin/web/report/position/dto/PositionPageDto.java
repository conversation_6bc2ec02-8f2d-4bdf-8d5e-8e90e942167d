package com.biz.crm.mdm.admin.web.report.position.dto;

import com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 职位列表查询条件Dto
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "职位列表查询条件Dto", description = "职位列表查询条件Dto")
public class PositionPageDto extends AbstractPositionPageDto {

  /**
   * 职位编码
   */
  @ApiModelProperty("职位编码")
  private String positionCode;

  /**
   * 职位名称
   */
  @ApiModelProperty("职位名称")
  private String positionName;

  /**
   * 上级职位所属组织名称
   */
  @ApiModelProperty("上级职位所属组织名称")
  private String parentOrgName;

  /**
   * 用户账号
   */
  @ApiModelProperty("用户账号")
  private String userName;

  /**
   * 用户姓名
   */
  @ApiModelProperty("用户姓名")
  private String fullName;
  /**
   * 启用状态
   */
  @ApiModelProperty("启用状态")
  private String enableStatus;

  /**
   * 所属组织编码
   */
  @ApiModelProperty("所属组织编码")
  private String orgCode;

  /**
   * 所属组织编码
   */
  @ApiModelProperty("所属组织编码")
  private String orgName;

  /**
   * 查询所有下级组织编码(查询这个组织及下级组织的数据)
   */
  @ApiModelProperty("组织编码(查询这个组织及下级组织的数据)")
  private String allUnderOrgCode;

  /**
   * 职位级别编码
   */
  @ApiModelProperty("职位级别编码")
  private String positionLevelCode;

  /**
   * 职位级别名称
   */
  @ApiModelProperty("职位级别名称 ")
  private String positionLevelName;

  /**
   * 是否是主职位
   */
  @ApiModelProperty("是否是主职位")
  private Boolean primaryFlag;

  /**
   * 分页来源
   */
  @ApiModelProperty(value = "分页来源",hidden = true)
  private String pageSource = PositionReportConstant.POSITION_PAGE_SOURCE_POSITION_LIST;
}
