package com.biz.crm.mdm.admin.web.report.user.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.admin.web.report.user.dto.UserRelationDto;
import com.biz.crm.mdm.admin.web.report.user.service.UserRelationVoService;
import com.biz.crm.mdm.admin.web.report.user.vo.UserRelationVo;
import com.bizunited.nebula.security.sdk.login.UserIdentity;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户关联信息控制层
 *
 * <AUTHOR>
 * @since 2021-11-04 16:47:03
 */
@Slf4j
@Api(tags = "MDM管理后台BOOT：UserRelationVo: 用户关联信息")
@RestController
@RequestMapping(value = {"/v1/adminWeb/userRelation"})
public class UserRelationVoController {

  @Autowired(required = false)
  private UserRelationVoService userRelationVoService;

  @Autowired(required = false)
  private LoginUserService loginUserService;

  /**
   * 查询用户关联信息
   *
   * @param dto 用户关系参数对象
   * @return Result<List<UserRelationVo>>
   */
  @ApiOperation(value = "查询用户关联信息")
  @GetMapping(value = {"/findByConditions"})
  public Result<List<UserRelationVo>> findByConditions(UserRelationDto dto) {
    try {
      List<UserRelationVo> result = this.userRelationVoService.findByConditions(dto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }


  /**
   * 根据登录信息查询用户关系
   *
   * @return Result<List<UserRelationVo>>
   */
  @ApiOperation(value = "根据登录信息查询用户关系")
  @GetMapping(value = {"/findByLoginUserDetails"})
  public Result<List<UserRelationVo>> findByLoginUserDetails() {
    try {
      UserIdentity loginDetails = loginUserService.getLoginUser();
      if (loginDetails == null) {
        return Result.error("请先登录");
      }
      UserRelationDto dto = new UserRelationDto();
      dto.setUserName(loginDetails.getAccount());
      List<UserRelationVo> result = this.userRelationVoService.findByConditions(dto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }



}
