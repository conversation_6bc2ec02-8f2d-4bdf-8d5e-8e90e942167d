<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.mdm.admin.web.report.position.mapper.PositionReportMapper">

  <select id="findByConditions" resultType="com.biz.crm.mdm.admin.web.report.position.vo.PositionPageVo">
    <if test="dto.pageSource != null and dto.pageSource == @com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant@POSITION_PAGE_SOURCE_POSITION_LIST">
      <include refid="positionPage"/>
    </if>

    <if test="dto.pageSource != null and dto.pageSource == @com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant@POSITION_PAGE_SOURCE_CHILD_POSITION_LIST">
      <include refid="childPositionPage"/>
    </if>

    <if test="dto.pageSource != null and dto.pageSource == @com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant@POSITION_PAGE_SOURCE_LEVEL_POSITION_LIST">
      <include refid="levelPositionPage"/>
    </if>

    <if test="dto.pageSource != null and dto.pageSource == @com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant@POSITION_PAGE_SOURCE_ORG_POSITION_LIST">
      <include refid="orgPositionPage"/>
    </if>

    <if test="dto.pageSource != null and dto.pageSource == @com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant@POSITION_PAGE_SOURCE_SELECT_POSITION_LIST">
      <include refid="selectPositionPage"/>
    </if>

    <if test="dto.pageSource != null and dto.pageSource == @com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant@POSITION_PAGE_SOURCE_HAS_RELATE_CURRENT_ROLE_POSITION_LIST">
      <include refid="hasRelateCurrentRolePositionPage"/>
    </if>

    <if test="dto.pageSource != null and dto.pageSource == @com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant@POSITION_PAGE_SOURCE_NOT_RELATE_CURRENT_ROLE_POSITION_LIST">
      <include refid="notRelateCurrentRolePositionPage"/>
    </if>

    <if test="dto.pageSource != null and dto.pageSource == @com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant@POSITION_PAGE_SOURCE_NOT_RELATE_ANY_ROLE_POSITION_LIST">
      <include refid="notRelateAnyRolePositionPage"/>
    </if>

    <if test="dto.pageSource != null and dto.pageSource == @com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant@POSITION_PAGE_SOURCE_USER_SELECT_POSITION_LIST">
      <include refid="userSelectPositionPage"/>
    </if>

    <if test="dto.pageSource != null and dto.pageSource == @com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant@POSITION_PAGE_SOURCE_USER_SELECT_PARENT_POSITION_LIST">
      <include refid="userSelectParentPositionPage"/>
    </if>

    <if test="dto.pageSource != null and dto.pageSource == @com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant@POSITION_PAGE_SOURCE_PARENT_POSITION_LIST">
      <include refid="parentPositionPage"/>
    </if>
  </select>
  <sql id="positionPage">
    SELECT temp.* FROM (
    SELECT
      a.id,
      MAX(a.position_code) position_code,
      MAX(a.position_name) position_name,
      MAX(a.enable_status) enable_status,
      MAX(a.create_time) create_time,
      MAX(i.position_level_name) position_level_name,
      MAX(i.position_level_code) position_level_code,
      MAX(c.org_name) org_name,
      MAX(c.org_code) org_code,
      MAX(f.position_name) parent_name,
      MAX(h.org_name) parent_org_name,
      MAX(j.primary_flag) primary_flag,
      MAX(k.full_name) full_name,
      MAX(k.user_name) user_name
    FROM
    mdm_position a
    LEFT JOIN mdm_org_position b ON (a.position_code = b.position_code AND a.tenant_code = b.tenant_code)
    LEFT JOIN mdm_org c ON (c.org_code = b.org_code AND c.tenant_code = b.tenant_code)
    LEFT JOIN mdm_position_role d ON (d.position_code = a.position_code AND d.tenant_code = a.tenant_code)
    LEFT JOIN mdm_position f ON (f.position_code = a.parent_code AND f.tenant_code = a.tenant_code)
    LEFT JOIN mdm_org_position g ON (g.position_code = f.position_code AND g.tenant_code = f.tenant_code)
    LEFT JOIN mdm_org h ON ( h.org_code = g.org_code AND h.tenant_code = g.tenant_code)
    LEFT JOIN mdm_position_level i ON ( i.position_level_code = a.position_level_code AND i.tenant_code = a.tenant_code)
    LEFT JOIN mdm_user_position j ON (j.position_code = a.position_code AND j.tenant_code = a.tenant_code)
    LEFT JOIN mdm_user k ON (k.user_name = j.user_name AND k.tenant_code = j.tenant_code)
    WHERE
    a.tenant_code = #{dto.tenantCode}
    AND a.del_flag = '${@<EMAIL>()}'
    <if test="dto.positionCode !=null and dto.positionCode != '' ">
      <bind name="likePositionCode" value="'%' + dto.positionCode + '%'"/>
      AND a.position_code like #{likePositionCode}
    </if>
    <if test="dto.positionName !=null and dto.positionName != '' ">
      <bind name="likePositionName" value="'%' + dto.positionName + '%'"/>
      AND a.position_name like #{likePositionName}
    </if>
    <if test="dto.enableStatus !=null and dto.enableStatus != '' ">
      AND a.enable_status = #{dto.enableStatus}
    </if>
    <if test="dto.positionLevelCode !=null and dto.positionLevelCode != '' ">
      AND i.position_level_code = #{dto.positionLevelCode}
    </if>
    <if test="dto.positionLevelName !=null and dto.positionLevelName != '' ">
      AND i.position_level_name = #{dto.positionLevelName}
    </if>
    <if test="dto.primaryFlag !=null ">
      AND j.primary_flag = #{dto.primaryFlag}
    </if>
    <if test="dto.orgCode !=null and dto.orgCode != '' ">
      AND c.org_code = #{dto.orgCode}
    </if>
    <if test="dto.orgName !=null and dto.orgName != '' ">
      <bind name="likeOrgName" value="'%' + dto.orgName + '%'"/>
      AND c.org_name like #{likeOrgName}
    </if>
    <if test="dto.parentOrgName !=null and dto.parentOrgName != '' ">
      <bind name="likeParentOrgName" value="'%' + dto.parentOrgName + '%'"/>
      and h.org_name like #{likeParentOrgName}
    </if>
    <if test="dto.fullName !=null and dto.fullName != '' ">
      <bind name="likeFullName" value="'%' + dto.fullName + '%'"/>
      AND k.full_name like #{likeFullName}
    </if>
    <if test="dto.userName !=null and dto.userName != '' ">
      <bind name="likeUserName" value="'%' + dto.userName + '%'"/>
      AND k.user_name like #{likeUserName}
    </if>
    <if test="dto.allUnderOrgCode !=null and dto.allUnderOrgCode != '' ">
      AND c.org_code IN (
      SELECT
      c2.org_code
      FROM
      mdm_org c1
      LEFT JOIN mdm_org c2 ON (c2.rule_code LIKE concat(c1.rule_code,'%') AND c1.tenant_code = c2.tenant_code)
      WHERE
      c1.org_code = #{dto.allUnderOrgCode}
      )
    </if>
    GROUP BY a.id) temp
    ORDER BY temp.create_time desc
  </sql>
  <sql id="childPositionPage">
    SELECT
      a.id,
      a.position_code,
      a.position_name,
      i.position_level_code position_level_code,
      i.position_level_name position_level_name,
      c.org_code org_code,
      c.org_name org_name,
      k.full_name full_name,
      k.user_name user_name
    FROM
    mdm_position a
    LEFT JOIN mdm_org_position b ON (a.position_code = b.position_code AND a.tenant_code = b.tenant_code)
    LEFT JOIN mdm_org c ON (c.org_code = b.org_code AND c.tenant_code = b.tenant_code)
    LEFT JOIN mdm_position_level i ON ( i.position_level_code = a.position_level_code AND i.tenant_code = a.tenant_code)
    LEFT JOIN mdm_user_position j ON (j.position_code = a.position_code AND j.tenant_code = a.tenant_code)
    LEFT JOIN mdm_user k ON (k.user_name = j.user_name AND k.tenant_code = j.tenant_code)
    WHERE
    a.tenant_code = #{dto.tenantCode}
    AND a.del_flag = '${@<EMAIL>()}'
    AND a.parent_code = #{dto.positionCode}
    <if test="dto.underlingPositionCode !=null and dto.underlingPositionCode != '' ">
      <bind name="likeUnderlingPositionCode" value="'%' + dto.underlingPositionCode + '%'"/>
      AND a.position_code like #{likeUnderlingPositionCode}
    </if>
    <if test="dto.underlingOrgName != null and dto.underlingOrgName != ''">
      <bind name="likeUnderlingOrgName" value="'%' + dto.underlingOrgName + '%'"/>
      AND c.org_name like #{likeUnderlingOrgName}
    </if>
  </sql>
  <sql id="levelPositionPage">
    SELECT
      a.id,
      a.position_code,
      a.position_name,
      a.enable_status,
      c.org_name org_name,
      c.org_code org_code,
      k.full_name full_name,
      k.user_name user_name,
      k.user_code user_code
    FROM
    mdm_position a
    LEFT JOIN mdm_org_position b ON (a.position_code = b.position_code AND a.tenant_code = b.tenant_code)
    LEFT JOIN mdm_org c ON (c.org_code = b.org_code AND c.tenant_code = b.tenant_code)
    LEFT JOIN mdm_user_position j ON (j.position_code = a.position_code AND j.tenant_code = a.tenant_code)
    LEFT JOIN mdm_user k ON (k.user_name = j.user_name AND k.tenant_code = j.tenant_code)
    WHERE
    a.tenant_code = #{dto.tenantCode}
    AND a.del_flag = '${@<EMAIL>()}'
    AND a.position_level_code = #{dto.thisPositionLevelCode}
    <if test="dto.orgName != null and dto.orgName != ''">
      <bind name="likeOrgName" value="'%' + dto.orgName + '%'"/>
      AND c.org_name like #{likeOrgName}
    </if>
    <if test="dto.fullName !=null and dto.fullName != '' ">
      <bind name="likeFullName" value="'%' + dto.fullName + '%'"/>
      AND k.full_name like #{likeFullName}
    </if>
  </sql>
  <sql id="orgPositionPage">
    SELECT
      a.id,
      d.position_code,
      d.position_name,
      h.position_level_name,
      b.org_name,
      e.position_name parent_name,
      g.org_name parent_org_name,
      g.org_code parent_org_code,
      b.org_code,
      k.full_name full_name,
      k.user_code user_code,
      k.user_name user_name,
      k1.full_name parent_full_name,
      k1.user_name parent_user_name
    FROM
    mdm_org a
    LEFT JOIN mdm_org b ON (b.rule_code LIKE concat(a.rule_code,'%') AND b.tenant_code = a.tenant_code)
    LEFT JOIN mdm_org_position c ON (c.org_code = b.org_code AND c.tenant_code = b.tenant_code)
    LEFT JOIN mdm_position d ON (d.position_code = c.position_code AND d.tenant_code = c.tenant_code)
    LEFT JOIN mdm_position e ON (e.position_code = d.parent_code AND e.tenant_code = d.tenant_code)
    LEFT JOIN mdm_org_position f ON (f.position_code = e.position_code AND f.tenant_code = e.tenant_code)
    LEFT JOIN mdm_org g ON ( g.org_code = f.org_code AND g.tenant_code = f.tenant_code)
    LEFT JOIN mdm_position_level h ON ( h.position_level_code = d.position_level_code AND h.tenant_code = d.tenant_code)
    LEFT JOIN mdm_user_position j ON (j.position_code = d.position_code AND j.tenant_code = d.tenant_code)
    LEFT JOIN mdm_user k ON (k.user_name = j.user_name AND k.tenant_code = j.tenant_code)
    LEFT JOIN mdm_user_position j1 ON (j1.position_code = e.position_code AND j1.tenant_code = e.tenant_code)
    LEFT JOIN mdm_user k1 ON (k1.user_name = j1.user_name AND k1.tenant_code = j1.tenant_code)
    WHERE
    a.tenant_code = #{dto.tenantCode}
    AND a.del_flag = '${@<EMAIL>()}'
    AND d.id IS NOT NULL
    <if test="dto.thisOrgCode !=null and dto.thisOrgCode != '' ">
      AND a.org_code = #{dto.thisOrgCode}
      <!-- 此处作用是屏蔽下级组织 -->
      AND b.org_code = #{dto.thisOrgCode}
    </if>
    <if test="dto.allUnderThisOrgCode !=null and dto.allUnderThisOrgCode != '' ">
      AND a.org_code = #{dto.allUnderThisOrgCode}
    </if>
    <if test="dto.orgCode != null and dto.orgCode != ''">
      <bind name="orgCodeLike" value="'%' + dto.orgCode + '%'"/>
      AND b.org_code like #{orgCodeLike}
    </if>
    <if test="dto.orgName != null and dto.orgName != ''">
      <bind name="likeOrgName" value="'%' + dto.orgName + '%'"/>
      AND b.org_name like #{likeOrgName}
    </if>
    <if test="dto.positionCode !=null and dto.positionCode != '' ">
      <bind name="positionCodeLike" value="'%' + dto.positionCode + '%'"/>
      AND d.position_code like #{positionCodeLike}
    </if>
    <if test="dto.positionName !=null and dto.positionName != '' ">
      <bind name="positionNameLike" value="'%' + dto.positionName + '%'"/>
      AND d.position_name like #{positionNameLike}
    </if>
    <if test="dto.positionLevelCode !=null and dto.positionLevelCode != '' ">
      AND h.position_level_code = #{dto.positionLevelCode}
    </if>
    <if test="dto.positionLevelName !=null and dto.positionLevelName != '' ">
      <bind name="positionLevelNameLike" value="'%' + dto.positionLevelName + '%'"/>
      AND h.position_level_name = #{dto.positionLevelNameLike}
    </if>
    <if test="dto.userName !=null and dto.userName != '' ">
      <bind name="userNameLike" value="'%' + dto.userName + '%'"/>
      AND k.user_name like #{userNameLike}
    </if>
    <if test="dto.fullName !=null and dto.fullName != '' ">
      <bind name="fullNameLike" value="'%' + dto.fullName + '%'"/>
      AND k.full_name like #{fullNameLike}
    </if>
  </sql>
  <sql id="selectPositionPage">
    SELECT
      a.id,
      a.position_code,
      a.position_name,
      a.enable_status,
      c.org_name org_name,
      c.org_code org_code,
      j.primary_flag,
      k.user_code,
      k.full_name,
      k.user_name,
      k.user_phone
    FROM mdm_position a
    LEFT JOIN mdm_org_position b ON ( a.position_code = b.position_code AND a.tenant_code = b.tenant_code )
    LEFT JOIN mdm_org c ON ( c.org_code = b.org_code AND c.tenant_code = b.tenant_code )
    LEFT JOIN mdm_user_position j ON (j.position_code = a.position_code AND j.tenant_code = a.tenant_code)
    LEFT JOIN mdm_user k ON (k.user_name = j.user_name AND k.tenant_code = j.tenant_code)
    WHERE
    a.tenant_code = #{dto.tenantCode}
    AND a.del_flag = '${@<EMAIL>()}'
    <if test="dto.hasUserName ">
      AND  k.user_name is not null
    </if>
    <if test="dto.enableStatus !=null and dto.enableStatus != '' ">
      AND a.enable_status = #{dto.enableStatus}
      AND k.enable_status = #{dto.enableStatus}
    </if>
    <if test="dto.unionName !=null and dto.unionName != '' ">
      <bind name="likeUnionName" value="'%' + dto.unionName + '%'"/>
      AND (a.position_name like #{likeUnionName} OR c.org_name like #{likeUnionName} OR k.full_name like
      #{likeUnionName})
    </if>
    <if test="dto.fullName !=null and dto.fullName != '' ">
      <bind name="fullNameLike" value="'%' + dto.fullName + '%'"/>
      AND k.full_name like #{fullNameLike}
    </if>
    <if test="dto.userName !=null and dto.userName != '' ">
      <bind name="userNameLike" value="'%' + dto.userName + '%'"/>
      AND k.user_name like #{userNameLike}
    </if>
    <if test="dto.positionName !=null and dto.positionName != '' ">
      <bind name="positionNameLike" value="'%' + dto.positionName + '%'"/>
      AND a.position_name like #{positionNameLike}
    </if>
    <if test="dto.orgName !=null and dto.orgName != '' ">
      <bind name="orgNameLike" value="'%' + dto.orgName + '%'"/>
      AND c.org_name like #{orgNameLike}
    </if>
    <!-- 排除当前组织(不含自身)所有下级组织关联的职位 -->
    <if test="dto.notUnderThisOrgCodeExcludeSelf !=null and dto.notUnderThisOrgCodeExcludeSelf != ''">
      AND NOT EXISTS (
      SELECT * FROM (
      SELECT
      b.org_code,
      b.tenant_code
      FROM
      mdm_org a
      LEFT JOIN mdm_org b ON (b.rule_code LIKE concat(a.rule_code,'%') AND a.tenant_code = b.tenant_code)
      WHERE
      a.tenant_code = #{dto.tenantCode}
      AND a.org_code = #{dto.notUnderThisOrgCodeExcludeSelf}
      <!-- (不含自身组织) -->
      AND a.org_code != b.org_code
      ) tmp
      WHERE
      b.org_code = tmp.org_code AND b.tenant_code = tmp.tenant_code
      )
    </if>
    <!-- 排除当前职位(含自身)及其所有下级职位 -->
    <if test="dto.notUnderThisPositionCode !=null and dto.notUnderThisPositionCode != ''">
      AND NOT EXISTS (
      SELECT * FROM (
      SELECT
      b.position_code,
      b.tenant_code
      FROM
      mdm_position a
      LEFT JOIN mdm_position b ON (b.rule_code LIKE concat(a.rule_code,'%') AND a.tenant_code = b.tenant_code)
      WHERE
      a.tenant_code = #{dto.tenantCode}
      AND a.position_code = #{dto.notUnderThisPositionCode}
      ) tmp
      WHERE
      a.position_code = tmp.position_code AND a.tenant_code = tmp.tenant_code
      )
    </if>
    ORDER BY
    <!-- 当某个业务编辑回显所选下拉职位时，则返回结果集合一定要包含回显职位且排在最前面,此处的意义就在于传入集合必须包含在
     返回的集合中,按照集合的元素索引值加上排序值,不在传入集合中的数据使用当前分页大小{page.szie}保证排在后面-->
    <if test="dto.selectedCodes != null and dto.selectedCodes.size > 0">
      CASE
      <foreach collection="dto.selectedCodes" item="item" index="index">
        WHEN a.position_code = #{item} THEN ${index}
      </foreach>
      ELSE -1 END desc,
    </if>
    a.create_time desc,a.id desc
  </sql>
  <sql id="hasRelateCurrentRolePositionPage">
    SELECT
      a.id,
      a.position_code,
      a.position_name,
      h.position_level_code,
      h.position_level_name,
      c.position_name parent_name
    FROM
    mdm_position a
    LEFT JOIN mdm_position_role b ON (a.position_code = b.position_code AND a.tenant_code = b.tenant_code)
    LEFT JOIN mdm_position c ON (c.position_code = a.parent_code AND c.tenant_code = a.tenant_code)
    LEFT JOIN mdm_user_position d ON (d.position_code = a.position_code AND d.tenant_code = a.tenant_code)
    LEFT JOIN mdm_user e ON (e.user_name = d.user_name AND e.tenant_code = d.tenant_code)
    LEFT JOIN mdm_position_level h ON (h.position_level_code = a.position_level_code AND h.tenant_code = a.tenant_code)
    WHERE
    a.tenant_code = #{dto.tenantCode}
    AND a.del_flag = '${@<EMAIL>()}'
    AND b.role_code = #{dto.roleCode}
    <if test="dto.fullName !=null and dto.fullName != '' ">
      <bind name="likeFullName" value="'%' + dto.fullName + '%'"/>
      AND e.full_name like #{likeFullName}
    </if>
    <if test="dto.positionName !=null and dto.positionName != '' ">
      <bind name="likePositionName" value="'%' + dto.positionName + '%'"/>
      AND a.position_name like #{likePositionName}
    </if>
    GROUP BY
    a.id
  </sql>
  <sql id="notRelateCurrentRolePositionPage">
    SELECT
      a.id,
      a.position_code,
      a.position_name,
      h.position_level_code,
      h.position_level_name,
      c.position_name parent_name
    FROM
    mdm_position a
    LEFT JOIN mdm_position_role b ON (a.position_code = b.position_code AND a.tenant_code = b.tenant_code)
    LEFT JOIN mdm_position c ON (c.position_code = a.parent_code AND c.tenant_code = a.tenant_code)
    LEFT JOIN mdm_user_position d ON (d.position_code = a.position_code AND d.tenant_code = a.tenant_code)
    LEFT JOIN mdm_user e ON (e.user_name = d.user_name AND e.tenant_code = d.tenant_code)
    LEFT JOIN mdm_position_level h ON (h.position_level_code = a.position_level_code AND h.tenant_code = a.tenant_code)
    WHERE
    a.tenant_code = #{dto.tenantCode}
    AND a.del_flag = '${@<EMAIL>()}'
    AND NOT EXISTS (
    SELECT
    *
    FROM
    mdm_position_role j
    WHERE
    a.tenant_code = j.tenant_code
    AND a.position_code = j.position_code
    AND j.role_code = #{dto.roleCode}
    )
    <if test="dto.fullName !=null and dto.fullName != '' ">
      <bind name="likeFullName" value="'%' + dto.fullName + '%'"/>
      AND e.full_name like #{likeFullName}
    </if>
    <if test="dto.positionName !=null and dto.positionName != '' ">
      <bind name="likePositionName" value="'%' + dto.positionName + '%'"/>
      AND a.position_name like #{likePositionName}
    </if>
    GROUP BY
    a.id
  </sql>
  <sql id="notRelateAnyRolePositionPage">
    SELECT
      a.id,
      a.position_code,
      a.position_name,
      h.position_level_code,
      h.position_level_name,
      c.position_name parent_name
    FROM
    mdm_position a
    LEFT JOIN mdm_position_role b ON (a.position_code = b.position_code AND a.tenant_code = b.tenant_code)
    LEFT JOIN mdm_position c ON (c.position_code = a.parent_code AND c.tenant_code = a.tenant_code)
    LEFT JOIN mdm_user_position d ON (d.position_code = a.position_code AND d.tenant_code = a.tenant_code)
    LEFT JOIN mdm_user e ON (e.user_name = d.user_name AND e.tenant_code = d.tenant_code)
    LEFT JOIN mdm_position_level h ON (h.position_level_code = a.position_level_code AND h.tenant_code = a.tenant_code)
    WHERE
    a.tenant_code = #{dto.tenantCode}
    AND a.del_flag = '${@<EMAIL>()}'
    AND b.id IS NULL
    <if test="dto.fullName !=null and dto.fullName != '' ">
      <bind name="likeFullName" value="'%' + dto.fullName + '%'"/>
      AND e.full_name like #{likeFullName}
    </if>
    <if test="dto.positionName !=null and dto.positionName != '' ">
      <bind name="likePositionName" value="'%' + dto.positionName + '%'"/>
      AND a.position_name like #{likePositionName}
    </if>
    GROUP BY
    a.id
  </sql>
  <sql id="userSelectPositionPage">
    SELECT
      a.id,
      a.position_code,
      a.position_name,
      a.enable_status,
      pl.position_level_code,
      pl.position_level_name,
      c.org_name org_name,
      c.org_code org_code,
      k.full_name,
      k.user_name
    FROM
    mdm_position a
    LEFT JOIN mdm_position_level pl ON ( pl.position_level_code = a.position_level_code AND pl.tenant_code =
    a.tenant_code )
    LEFT JOIN mdm_org_position b ON ( a.position_code = b.position_code AND a.tenant_code = b.tenant_code )
    LEFT JOIN mdm_org c ON ( c.org_code = b.org_code AND c.tenant_code = b.tenant_code )
    LEFT JOIN mdm_user_position j ON (j.position_code = a.position_code AND j.tenant_code = a.tenant_code)
    LEFT JOIN mdm_user k ON (k.user_name = j.user_name AND k.tenant_code = j.tenant_code)
    WHERE
    a.tenant_code = #{dto.tenantCode}
    AND a.del_flag = '${@<EMAIL>()}'
    AND (j.user_name IS NULL OR j.primary_flag != 1
    <if test="dto.includeUserName != null and dto.includeUserName != ''">
      OR j.user_name = #{dto.includeUserName}
    </if>
    )
    <if test="dto.enableStatus !=null and dto.enableStatus != '' ">
      AND a.enable_status = #{dto.enableStatus}
    </if>
    <if test="dto.unionName !=null and dto.unionName != '' ">
      <bind name="likeUnionName" value="'%' + dto.unionName + '%'"/>
      AND (a.position_name like #{likeUnionName} OR c.org_name like #{likeUnionName} OR k.full_name like
      #{likeUnionName})
    </if>
    <if test="dto.thisOrgCode !=null and dto.thisOrgCode != '' ">
      AND b.org_code = #{dto.thisOrgCode}
    </if>
    ORDER BY
    <!-- 当某个业务编辑回显所选下拉职位时，则返回结果集合一定要包含回显职位且排在最前面,此处的意义就在于传入集合必须包含在
     返回的集合中,按照集合的元素索引值加上排序值,不在传入集合中的数据使用当前分页大小{page.szie}保证排在后面-->
    <if test="dto.selectedCodes != null and dto.selectedCodes.size > 0">
      CASE
      <foreach collection="dto.selectedCodes" item="item" index="index">
        WHEN a.position_code = #{item} THEN ${index}
      </foreach>
      ELSE #{page.size} END asc,
    </if>
    a.create_time desc
  </sql>
  <sql id="userSelectParentPositionPage">
    SELECT
      a.id,
      a.position_code,
      a.position_name,
      a.enable_status,
      c.org_name org_name,
      c.org_code org_code,
      k.full_name,
      k.user_name
    FROM
    mdm_position a
    LEFT JOIN mdm_org_position b ON ( a.position_code = b.position_code AND a.tenant_code = b.tenant_code )
    LEFT JOIN mdm_org c ON ( c.org_code = b.org_code AND c.tenant_code = b.tenant_code )
    LEFT JOIN mdm_user_position j ON (j.position_code = a.position_code AND j.tenant_code = a.tenant_code)
    LEFT JOIN mdm_user k ON (k.user_name = j.user_name AND k.tenant_code = j.tenant_code)
    WHERE
    a.tenant_code = #{dto.tenantCode}
    AND a.del_flag = '${@<EMAIL>()}'
    <if test="dto.unionName !=null and dto.unionName != '' ">
      <bind name="likeUnionName" value="'%' + dto.unionName + '%'"/>
      AND (a.position_name like #{likeUnionName} OR c.org_name like #{likeUnionName} OR k.full_name like
      #{likeUnionName})
    </if>
    <if test="dto.excludeUserName != null and dto.excludeUserName != ''">
      AND (j.user_name is null OR j.user_name != #{dto.excludeUserName})
    </if>
    <!-- 排除当前组织(不含自身)所有下级组织关联的职位 -->
    <if test="dto.childPositionOrgCode !=null and dto.childPositionOrgCode != ''">
      AND NOT EXISTS (
      SELECT * FROM (
      SELECT
      b.org_code,
      b.tenant_code
      FROM
      mdm_org a
      LEFT JOIN mdm_org b ON (b.rule_code LIKE concat(a.rule_code,'%') AND a.tenant_code = b.tenant_code)
      WHERE
      a.tenant_code = #{dto.tenantCode}
      AND a.org_code = #{dto.childPositionOrgCode}
      <!-- (不含自身组织) -->
      AND a.org_code != b.org_code
      ) tmp
      WHERE
      b.org_code = tmp.org_code AND b.tenant_code = tmp.tenant_code
      )
    </if>
    <!-- 排除当前职位(含自身)及其所有下级职位 -->
    <if test="dto.childPositionCode !=null and dto.childPositionCode != ''">
      AND NOT EXISTS (
      SELECT * FROM (
      SELECT
      b.position_code,
      b.tenant_code
      FROM
      mdm_position a
      LEFT JOIN mdm_position b ON (b.rule_code LIKE concat(a.rule_code,'%') AND a.tenant_code = b.tenant_code)
      WHERE
      a.tenant_code = #{dto.tenantCode}
      AND a.position_code = #{dto.childPositionCode}
      ) tmp
      WHERE
      a.position_code = tmp.position_code AND a.tenant_code = tmp.tenant_code
      )
    </if>
    ORDER BY
    <!-- 当某个业务编辑回显所选下拉职位时，则返回结果集合一定要包含回显职位且排在最前面,此处的意义就在于传入集合必须包含在
     返回的集合中,按照集合的元素索引值加上排序值,不在传入集合中的数据使用当前分页大小{page.szie}保证排在后面-->
    <if test="dto.selectedCodes != null and dto.selectedCodes.size > 0">
      CASE
      <foreach collection="dto.selectedCodes" item="item" index="index">
        WHEN a.position_code = #{item} THEN ${index}
      </foreach>
      ELSE #{page.size} END asc,
    </if>
    a.create_time desc
  </sql>
  <sql id="parentPositionPage">
    SELECT
      a.id,
      a.position_code,
      a.position_name,
      a.enable_status,
      a.position_level_code,
      a.parent_code,
      n.position_name as parentName,
      m.position_level_name,
      c.org_name org_name,
      c.org_code org_code,
      k.full_name,
      k.user_name
    FROM
    mdm_position a
    LEFT JOIN mdm_org_position b ON ( a.position_code = b.position_code AND a.tenant_code = b.tenant_code )
    LEFT JOIN mdm_org c ON ( c.org_code = b.org_code AND c.tenant_code = b.tenant_code )
    LEFT JOIN mdm_user_position j ON (j.position_code = a.position_code AND j.tenant_code = a.tenant_code)
    LEFT JOIN mdm_user k ON (k.user_name = j.user_name AND k.tenant_code = j.tenant_code)
    LEFT JOIN mdm_position_level m ON (m.position_level_code=a.position_level_code AND a.tenant_code = m.tenant_code)
    LEFT JOIN mdm_position n on(a.parent_code = n.position_code AND a.tenant_code =n .tenant_code)
    WHERE
    a.tenant_code = #{dto.tenantCode}
    AND a.del_flag = '${@<EMAIL>()}'
    <if test="dto.fullName !=null and dto.fullName != '' ">
      <bind name="likeFullName" value="'%' + dto.fullName + '%'"/>
      AND k.full_name like #{likeFullName}
    </if>
    <if test="dto.positionName !=null and dto.positionName != '' ">
      <bind name="likePositionName" value="'%' + dto.positionName + '%'"/>
      AND a.position_name like #{likePositionName}
    </if>
    <if test="dto.enableStatus !=null and dto.enableStatus != '' ">
      AND a.enable_status = #{dto.enableStatus}
    </if>
    <if test="dto.positionCode !=null and dto.positionCode != '' ">
      AND a.position_code = #{dto.positionCode}
    </if>
    <if test="dto.orgName !=null and dto.orgName != '' ">
      <bind name="likeOrgName" value="'%' + dto.orgName + '%'"/>
      AND c.org_name like #{likeOrgName}
    </if>
    <if test="dto.userName !=null and dto.userName != '' ">
      <bind name="likeUserName" value="'%' + dto.userName + '%'"/>
      AND k.user_name like #{likeUserName}
    </if>
    <!-- 排除当前职位(含自身)及其所有下级职位 -->
    <if test="dto.excludeChildPositionCodeList !=null and dto.excludeChildPositionCodeList.size > 0">
      AND NOT EXISTS (
      SELECT * FROM (
      SELECT
      b.position_code,
      b.tenant_code
      FROM
      mdm_position a
      LEFT JOIN mdm_position b ON (b.rule_code LIKE concat(a.rule_code,'%') AND a.tenant_code = b.tenant_code)
      WHERE
      a.tenant_code = #{dto.tenantCode}
      AND a.position_code IN
      <foreach item="item" index="index" collection="dto.excludeChildPositionCodeList" open="(" separator="," close=")">
        #{item}
      </foreach>
      ) tmp
      WHERE
      a.position_code = tmp.position_code AND a.tenant_code = tmp.tenant_code
      )
    </if>
    ORDER BY
    a.create_time desc
  </sql>
</mapper>