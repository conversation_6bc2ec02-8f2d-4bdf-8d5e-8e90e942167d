package com.biz.crm.mdm.admin.web.login.refresh;

import java.util.List;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.admin.web.login.DefaultPerfectLoginUserDetails;
import com.biz.crm.mdm.admin.web.service.PositionRoleVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.biz.crm.mdm.business.user.sdk.dto.UserPositionDto;
import com.biz.crm.mdm.business.user.sdk.service.UserPositionVoService;
import com.biz.crm.mdm.business.user.sdk.service.UserValidityCheckService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import com.bizunited.nebula.security.sdk.refresh.AuthenticationRefreshStrategy;
import com.google.common.collect.Sets;

/**
 * 在线用户切换的用户身份刷新策略，来源请参见UserPositionVoController
 * 
 * <AUTHOR>
 */
@Component
public class UserPositionRefreshStrategy extends DefaultPerfectLoginUserDetails
    implements AuthenticationRefreshStrategy {
  @Autowired
  @Lazy
  private UserValidityCheckService userValidityCheckService;
  @Autowired
  @Lazy
  private LoginUserService         loginUserService;
  @Autowired(required = false)
  @Lazy
  private UserPositionVoService    userPositionVoService;
  @Autowired
  @Lazy
  private PositionRoleVoService    positionRoleVoService;
  
  @Override
  public int getOrder() {
    return 10;
  }
  
  @Override
  public boolean matched(Object info) {
    // 只有在UserPositionDto对象存在的情况下，才会调用这个策略
    if (info != null && (info instanceof UserPositionDto)) {
      return true;
    }
    return false;
  }
  
  @Override
  public UserIdentity refresh(Object info) {
    UserPositionDto dto = (UserPositionDto) info;
    // 获取未切换前的当前用户信息
    AbstractCrmUserIdentity crmUserIdentity = this.loginUserService.getAbstractLoginUser();
    if (crmUserIdentity == null) {
      throw new IllegalArgumentException("错误的当前用户信息，请确认");
    }
    String account = crmUserIdentity.getAccount();
    String currentPositionCode = dto.getPositionCode();
    Integer type = crmUserIdentity.getLoginType();
    
    // 更新当前用户的岗位选中情况标记
    this.userPositionVoService.updateCurrentFlagPosition(crmUserIdentity.getAccount(), dto.getPositionCode());
    
    // 重新塑造厂商用户信息
    // 重新刷新角色信息
    String[] roleCodes = new String[] {};
    String tenantCode = TenantUtils.getTenantCode();
    Set<String> roles = this.findByAccountForCurrentPosition(tenantCode, account);
    if (!CollectionUtils.isEmpty(roles)) {
      roleCodes = roles.toArray(new String[] {});
    }
    
    FacturerUserDetails facturer = new FacturerUserDetails();
    UserVo userVo = this.userValidityCheckService.verificationManageByAccount(account);
    facturer.setAccount(account);
    // 厂商用户的基本信息
    facturer.setLoginType(type);
    facturer.setIdentityType("u");
    facturer.setRoleCodes(roleCodes);
    facturer.setTenantCode(tenantCode);
    
    super.perfectLoginUserDetails(userVo, facturer);
    super.perfectLoginPostAndOrg(facturer);
    // 如果记录了新的岗位，则以那个岗位为准
    if (StringUtils.isNotBlank(currentPositionCode)) {
      facturer.setPostCode(currentPositionCode);
    }
    
    
    return facturer;
  }
  
  /**
   * 按照指定的账号查询对应的角色信息
   * 
   * @param tenantCode
   * @param account
   * @return
   */
  private Set<String> findByAccountForCurrentPosition(String tenantCode, String account) {
    List<PositionVo> positionVos = positionRoleVoService.findByAccountForCurrentPosition(tenantCode, account);
    if (CollectionUtils.isEmpty(positionVos)) {
      return Sets.newHashSet();
    }
    Set<String> roleCodes = Sets.newHashSet();
    positionVos.forEach(p -> {
      if (!CollectionUtils.isEmpty(p.getRoleList())) {
        roleCodes.addAll(p.getRoleList());
      }
    });
    return roleCodes;
  }
}
