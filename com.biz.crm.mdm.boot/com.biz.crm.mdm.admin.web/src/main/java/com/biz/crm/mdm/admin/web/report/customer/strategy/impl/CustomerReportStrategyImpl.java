package com.biz.crm.mdm.admin.web.report.customer.strategy.impl;

import com.biz.crm.mdm.admin.web.report.customer.constant.CustomerReportConstant;
import com.biz.crm.mdm.admin.web.report.customer.dto.CustomerPageDto;
import com.biz.crm.mdm.admin.web.report.customer.strategy.CustomerReportStrategy;
import com.biz.crm.mdm.admin.web.report.customer.strategy.helper.CustomerReportStrategyHelper;
import com.biz.crm.mdm.admin.web.report.customer.vo.CustomerPageVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 客户信息分页列表报表数据策略实现
 *
 * <AUTHOR>
 * @date 2022/3/29
 */
@Component
public class CustomerReportStrategyImpl implements CustomerReportStrategy<CustomerPageDto> {

  @Autowired(required = false)
  private CustomerReportStrategyHelper customerReportStrategyHelper;

  @Override
  public String getPageSource() {
    return CustomerReportConstant.CUSTOMER_PAGE_SOURCE_CUSTOMER_LIST;
  }

  @Override
  public Boolean onRequest(CustomerPageDto dto) {
    return true;
  }

  @Override
  public void onReturn(List<CustomerPageVo> voList) {
    this.customerReportStrategyHelper.buildCustomerSelfInfo(voList);
    this.customerReportStrategyHelper.buildTagInfo(voList);
  }
}
