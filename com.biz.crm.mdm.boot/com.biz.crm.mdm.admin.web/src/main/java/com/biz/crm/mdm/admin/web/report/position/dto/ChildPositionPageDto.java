package com.biz.crm.mdm.admin.web.report.position.dto;

import com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 下属职位列表查询条件Dto
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "下属职位列表查询条件Dto", description = "下属职位列表查询条件Dto")
public class ChildPositionPageDto extends AbstractPositionPageDto {

  /**
   * 职位编码
   */
  @ApiModelProperty("职位编码")
  private String positionCode;

  /**
   * 下级职位编码
   */
  @ApiModelProperty("下级职位编码")
  private String underlingPositionCode;

  /**
   * 下级组织名称
   */
  @ApiModelProperty("下级组织名称")
  private String underlingOrgName;

  /**
   * 分页来源
   */
  @ApiModelProperty(value = "分页来源", hidden = true)
  private String pageSource = PositionReportConstant.POSITION_PAGE_SOURCE_CHILD_POSITION_LIST;

}
