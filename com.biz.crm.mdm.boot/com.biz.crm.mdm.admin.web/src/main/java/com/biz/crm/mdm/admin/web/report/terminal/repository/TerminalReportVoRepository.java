package com.biz.crm.mdm.admin.web.report.terminal.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.mdm.admin.web.report.terminal.dto.NoRelaCurTerminalDto;
import com.biz.crm.mdm.admin.web.report.terminal.dto.NoRelaTerminalDto;
import com.biz.crm.mdm.admin.web.report.terminal.dto.RelaCurTerminalDto;
import com.biz.crm.mdm.admin.web.report.terminal.dto.TerminalReportPaginationDto;
import com.biz.crm.mdm.admin.web.report.terminal.mapper.TerminalReportVoMapper;
import com.biz.crm.mdm.admin.web.report.terminal.vo.TerminalReportVo;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaCustomerOrg;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaOrg;
import com.biz.crm.mdm.business.terminal.sdk.dto.TerminalClientDto;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalClientVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/11/5
 */
@Component
@Slf4j
public class TerminalReportVoRepository {

  @Autowired(required = false)
  private TerminalReportVoMapper terminalReportVoMapper;

  private static Cache<String, Page<TerminalReportVo>> cache = null;

  /**
   * 终端企业组织关联查询-无供货关系
   *
   * @param page
   * @param dto
   * @return
   */
  public Page<TerminalReportVo> findByConditions(
      Page<TerminalReportVo> page, TerminalReportPaginationDto dto) {
    dto.setTenantCode(TenantUtils.getTenantCode());
    return terminalReportVoMapper.findByConditions(page, dto);
  }

  /**
   * 终端企业组织关联查询-有供货关系[我关联的]
   *
   * @param page
   * @param dto
   * @return
   */
  public Page<TerminalReportVo> findByRelaCurTerminalDto(Page<TerminalReportVo> page, RelaCurTerminalDto dto) {
    dto.setTenantCode(TenantUtils.getTenantCode());
    // 该语句在压测中，性能表现很差，增加0.3秒缓存，在高并发情况下起作用
    if (cache == null) {
      synchronized (TerminalReportVoRepository.class) {
        while (cache == null) {
          cache = CacheBuilder.newBuilder()
              .expireAfterWrite(300, TimeUnit.MILLISECONDS)
              .initialCapacity(10000)
              .build();
        }
      }
    }
    // 通过分页信息和查询条件，决定他的key(注意：toString是被复写了的)
    String dtoContext = dto.toString();
    long size = page.getSize();
    long current = page.getCurrent();
    Long maxLimit = page.getMaxLimit();
    String key = StringUtils.join("findByRelaCurTerminalDto", "_", dtoContext, "_", size, "_", current, "_", maxLimit);
    try {
      return cache.get(key, () -> terminalReportVoMapper.findByRelaCurTerminalDto(page, dto));
    } catch (ExecutionException e) {
      log.error(e.getMessage(), e);
      return new Page<>();
    }
  }

  /**
   * 终端企业组织关联查询-有供货关系[有关联的不是我]
   *
   * @param page
   * @param dto
   * @return
   */
  public Page<TerminalReportVo> findByNoRelaCurTerminalDto(
      Page<TerminalReportVo> page, NoRelaCurTerminalDto dto) {
    dto.setTenantCode(TenantUtils.getTenantCode());
    // 该语句在压测中，性能表现很差，增加0.3秒缓存，在高并发情况下起作用
    if (cache == null) {
      synchronized (TerminalReportVoRepository.class) {
        while (cache == null) {
          cache = CacheBuilder.newBuilder()
              .expireAfterWrite(300, TimeUnit.MILLISECONDS)
              .initialCapacity(10000)
              .build();
        }
      }
    }
    // 通过分页信息和查询条件，决定他的key(注意：toString是被复写了的)
    String dtoContext = dto.toString();
    long size = page.getSize();
    long current = page.getCurrent();
    Long maxLimit = page.getMaxLimit();
    String key = StringUtils.join("findByNoRelaCurTerminalDto", "_", dtoContext, "_", size, "_", current, "_", maxLimit);
    try {
      return cache.get(key, new Callable<Page<TerminalReportVo>>() {
        @Override
        public Page<TerminalReportVo> call() throws Exception {
          return terminalReportVoMapper.findByNoRelaCurTerminalDto(page, dto);
        }
      });
    } catch (ExecutionException e) {
      log.error(e.getMessage(), e);
      return null;
    }
  }

  /**
   * 终端企业组织关联查询-有供货关系[未关联任何的]
   *
   * @param page
   * @param dto
   * @return
   */
  public Page<TerminalReportVo> findByNoRelaTerminalDto(Page<TerminalReportVo> page, NoRelaTerminalDto dto) {
    dto.setTenantCode(TenantUtils.getTenantCode());
    // 该语句在压测中，性能表现很差，增加0.3秒缓存，在高并发情况下起作用
    if (cache == null) {
      synchronized (TerminalReportVoRepository.class) {
        while (cache == null) {
          cache = CacheBuilder.newBuilder()
              .expireAfterWrite(300, TimeUnit.MILLISECONDS)
              .initialCapacity(10000)
              .build();
        }
      }
    }
    // 通过分页信息和查询条件，决定他的key(注意：toString是被复写了的)
    String dtoContext = dto.toString();
    long size = page.getSize();
    long current = page.getCurrent();
    Long maxLimit = page.getMaxLimit();
    String key = StringUtils.join("findByNoRelaTerminalDto", "_", dtoContext, "_", size, "_", current, "_", maxLimit);
    try {
      return cache.get(key, new Callable<Page<TerminalReportVo>>() {
        @Override
        public Page<TerminalReportVo> call() throws Exception {
          return terminalReportVoMapper.findByNoRelaTerminalDto(page, dto);
        }
      });
    } catch (ExecutionException e) {
      log.error(e.getMessage(), e);
      return null;
    }
  }

  /**
   * 通过编码查询TerminalReportVo
   *
   * @param terminalCodes
   */
  public List<TerminalReportVo> findByTerminalCodes(List<String> terminalCodes) {
    return terminalReportVoMapper.findByTerminalCodes(terminalCodes, TenantUtils.getTenantCode(), DelFlagStatusEnum.NORMAL.getCode());
  }

  /**
   * 根据终端编码集合获取终端关联组织信息
   *
   * @param terminalCodeSet
   * @return
   */
  public List<TerminalRelaOrg> findTerminalRelaOrgListByTerminalCodes(Set<String> terminalCodeSet) {
    return terminalReportVoMapper.findTerminalRelaOrgListByTerminalCodes(terminalCodeSet, TenantUtils.getTenantCode());
  }

  /**
   * 根据终端编码集合获取终端关联客户组织信息
   *
   * @param terminalCodeSet
   * @return
   */
  public List<TerminalRelaCustomerOrg> findTerminalRelaCustomerOrgListByTerminalCodes(
      Set<String> terminalCodeSet) {
    return terminalReportVoMapper.findTerminalRelaCustomerOrgListByTerminalCodes(
        terminalCodeSet, TenantUtils.getTenantCode());
  }

  /**
   * 查询当前用户及其下属所关联的终端信息分页列表
   * <p>
   * 用户账号和租户必传
   *
   * @param pageable 分页信息
   * @param dto      查询对象
   * @return Page<TerminalClientVo> 查询当前用户及其下属所关联的终端信息分页列表
   */
  public Page<TerminalClientVo> findChildrenPageByTerminalClientDto(Pageable pageable, TerminalClientDto dto) {
    Page<TerminalClientVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return this.terminalReportVoMapper.findChildrenPageByTerminalClientDto(page, dto);
  }

  /**
   * 分页查询
   *
   * @param pageable
   * @param dto
   * @return
   */
  public Page<TerminalClientVo> findPageByTerminalClientDto(Pageable pageable, TerminalClientDto dto) {
    Page<TerminalClientVo> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return this.terminalReportVoMapper.findPageByTerminalClientDto(page, dto);
  }
}
