package com.biz.crm.mdm.admin.web.config.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 配置文件常量类
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ConfigConstant {

  /**
   * 数据字典-密码过期时间
   */
  public static final String DICT_PASSWORD_OVERDUE = "password_overdue";

  /**
   * 数据字典-密码过期时间
   */
  public static final String DICT_PASSWORD_OVERDUE_DAYS = "overdue_days";

  /**
   * 数据字典-密码提醒时间
   */
  public static final String DICT_PASSWORD_REMIND_DAYS = "remind_days";

  /**
   * 数据字典-首次强制更新密码
   */
  public static final String DICT_PASSWORD_FORCE_CHANGE_PASSWORD = "force_change_password";

  /**
   * 数据字典-密码即将过期提醒
   */
  public static final String DICT_PASSWORD_CHANGE_REMIND = "password_change_remind";

  /**
   * 数据字典-密码执行策略
   */
  public static final String DICT_PASSWORD_STRATEGY = "password_strategy";

  /**
   * 数据字典-密码执行策略
   */
  public static final String DICT_FIRST_FORCE_CHANGE_PASSWORD = "first_force_change_password";

}
