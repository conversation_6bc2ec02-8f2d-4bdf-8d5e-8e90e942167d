package com.biz.crm.mdm.admin.web.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SAP推送的数据
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/23 22:48
 */
@Data
public class SapSendCustomerSupplierDeliveryCompanyDto implements Serializable {

    private static final long serialVersionUID = 1395046910260555767L;

    @ApiModelProperty("公司代码")
    private String BUKRS;

    @ApiModelProperty("贸易合作伙伴")
    private String VBUND;

    @ApiModelProperty("公司代码层删除冻结")
    private String NODEL;

    @ApiModelProperty("对公司代码过帐冻结")
    private String SPERR;

}
