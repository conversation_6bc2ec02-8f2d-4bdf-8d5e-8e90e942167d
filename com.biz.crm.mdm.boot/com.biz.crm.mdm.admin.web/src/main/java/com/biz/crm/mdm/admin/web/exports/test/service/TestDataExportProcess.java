package com.biz.crm.mdm.admin.web.exports.test.service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.fastjson.JSONArray;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.mdm.admin.web.exports.test.mapper.TestDataMapper;
import com.biz.crm.mdm.admin.web.exports.test.model.TestDataDto;
import com.biz.crm.mdm.admin.web.exports.test.model.TestDataVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

/**
 * 测试数据导出实现
 *
 * <AUTHOR>
 * @date 2022/5/23
 */
@Component
public class TestDataExportProcess implements ExportProcess<TestDataVo> {

  @Autowired(required = false) private TestDataMapper testDataMapper;

  @Override
  public Integer getTotal(Map<String, Object> params) {
    params = this.convertEuropaParam(params);
    String tenantCode = TenantUtils.getTenantCode();
    return this.testDataMapper.getExportTotal(this.findTestDataDto(params));
  }

  @Override
  @Transactional /* 必须加上事务，否则导出的数据视图将会失效 */
  public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
    params = this.convertEuropaParam(params);
    final TestDataDto dto = this.findTestDataDto(params);
    dto.setOffset(this.getPageSize() * vo.getPageNo());
    dto.setLimit(vo.getPageSize());
    // 获取数据的tenantCode必须使用vo里面获取，否则会出现跨租户问题
    String tenantCode = vo.getTenantCode();
    final List<TestDataVo> list = this.testDataMapper.findData(dto);
    return toJSONArray(list);
  }

  @Override
  public String getBusinessCode() {
    return "MDM_TEST_EXPORT";
  }

  @Override
  public String getBusinessName() {
    return "mdm导出测试";
  }

  @Override
  public Integer getPageSize() {
    return 20000;
  }

  /**
   * 获取参数
   *
   * @param params
   * @return
   */
  private TestDataDto findTestDataDto(Map<String, Object> params) {
    final TestDataDto dto = new TestDataDto();
    final Object productCode = params.get("productCode");
    if (Objects.nonNull(productCode)) {
      dto.setProductCode(productCode.toString());
    }
    return dto;
  }

}
