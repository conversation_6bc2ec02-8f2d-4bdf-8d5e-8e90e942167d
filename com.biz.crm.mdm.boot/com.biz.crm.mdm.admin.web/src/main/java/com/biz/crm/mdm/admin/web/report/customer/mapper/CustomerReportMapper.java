package com.biz.crm.mdm.admin.web.report.customer.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.customer.dto.AbstractCustomerPageDto;
import com.biz.crm.mdm.admin.web.report.customer.dto.CustomerPageDto;
import com.biz.crm.mdm.admin.web.report.customer.vo.CustomerAddressPageVo;
import com.biz.crm.mdm.admin.web.report.customer.vo.CustomerPageVo;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerClientDto;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerClientVo;
import org.apache.ibatis.annotations.Param;

/**
 * 客户复杂报表mybatis-plus接口类
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
public interface CustomerReportMapper {

  /**
   * 客户分页列表
   *
   * @param dto  请求参数dto
   * @param page 分页信息
   * @return 客户分页列表
   */
  Page<CustomerPageVo> findByConditions(@Param("page") Page<CustomerPageVo> page, @Param("dto") AbstractCustomerPageDto dto);

  /**
   * 查询当前用户及其下属所关联的客户信息分页列表
   * <p>
   * 用户账号和租户必传
   *
   * @param dto  查询对象
   * @param page 分页信息
   * @return Page<CustomerClientVo> 查询当前用户及其下属所关联的客户信息分页列表
   */
  Page<CustomerClientVo> findChildrenPageByCustomerClientDto(Page<CustomerClientVo> page, @Param("dto") CustomerClientDto dto);

  /**
   * 客户地址分页列表
   *
   * @param dto  请求参数dto
   * @param page 分页信息
   * @return 客户分页列表
   */
  Page<CustomerAddressPageVo> findAddressByConditions(@Param("page") Page<CustomerPageVo> page, @Param("dto") CustomerPageDto dto);
}
