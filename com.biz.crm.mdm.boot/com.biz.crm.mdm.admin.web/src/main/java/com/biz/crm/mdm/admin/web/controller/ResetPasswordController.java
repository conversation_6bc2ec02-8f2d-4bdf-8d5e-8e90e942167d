package com.biz.crm.mdm.admin.web.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.admin.web.dto.ResetPasswordDto;
import com.biz.crm.mdm.admin.web.service.ResetPasswordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 重置密码相关接口
 *
 * <AUTHOR>
 * @date 2022/05/12
 */
@Slf4j
@RestController
@RequestMapping("/v1/resetPassword/resetPassword")
@Api(tags = "重置密码相关接口")
public class ResetPasswordController {

  @Autowired(required = false)
  private ResetPasswordService resetPasswordService;

  @ApiOperation(value = "根据手机号+短信验证码修改密码")
  @PostMapping("/updatePasswordByPhoneAndVerificationCode")
  public Result<?> updatePasswordByPhoneAndVerificationCode(
      @RequestParam(value = "appType") @ApiParam(name = "appType", required = true, value = "业务系统类型") Integer appType,
      @RequestParam(value = "phone") @ApiParam(name = "phone", required = true, value = "手机号") String phone,
      @RequestParam(value = "verificationCode") @ApiParam(name = "verificationCode", required = true, value = "短信验证码") String verificationCode,
      @RequestParam(value = "password") @ApiParam(name = "password", required = true, value = "新密码（密文）") String password) {
    try {
      this.resetPasswordService.updatePasswordByPhoneAndVerificationCode(appType, phone, password, verificationCode);
      return Result.ok("修改成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 在登录状态中更改密码
   * @param dto
   * @return
   */
  @ApiOperation(value = "在登录状态中更改密码")
  @PostMapping("/updatePasswordInLoginStatus")
  public Result<?> updatePasswordInLoginStatus(@RequestBody ResetPasswordDto dto) {
    try {
      this.resetPasswordService.updatePasswordInLoginStatus(dto);
      return Result.ok("修改成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

}
