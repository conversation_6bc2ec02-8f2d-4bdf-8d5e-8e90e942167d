package com.biz.crm.mdm.admin.web.report.customer.dto;

import com.biz.crm.mdm.admin.web.report.customer.constant.CustomerReportConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 客户分页列表查询条件Dto
 *
 * <AUTHOR>
 * @date 2021/11/10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "客户分页列表查询条件Dto", description = "客户分页列表查询条件Dto")
public class CustomerPageDto extends AbstractCustomerPageDto {

  /**
   * 客户编码
   */
  @ApiModelProperty("客户编码")
  private String customerCode;
  /**
   * 客户名称
   */
  @ApiModelProperty("客户名称")
  private String customerName;


  @ApiModelProperty("客户编码/客户名称")
  private String unionName;

  /**
   * 客户类型
   */
  @ApiModelProperty("客户类型")
  private String customerType;
  /**
   * 市名称
   */
  @ApiModelProperty("市名称")
  private String cityName;
  /**
   * 区名称
   */
  @ApiModelProperty("区名称")
  private String districtName;
  /**
   * 省名称
   */
  @ApiModelProperty("省名称")
  private String provinceName;
  /**
   * 企业组织编码
   */
  @ApiModelProperty("企业组织编码")
  private String orgCode;
  /**
   * 客户组织编码
   */
  @ApiModelProperty("客户组织编码")
  private String customerOrgCode;

  /**
   * 启用禁用状态
   */
  @ApiModelProperty("启用状态 传009只查启用，不传查询全部")
  private String enableStatus;

  /**
   * 锁定状态：009正常，003冻结
   */
  @ApiModelProperty("锁定状态：009正常，003冻结")
  private String lockState;

  /**
   * 审批状态
   */
  @ApiModelProperty("审批状态")
  private String processStatus;

  /**
   * 企业组织名称
   */
  @ApiModelProperty("企业组织名称")
  private String orgName;
  
  /**
   * 分页来源
   */
  @ApiModelProperty(value = "分页来源",hidden = true)
  private String pageSource = CustomerReportConstant.CUSTOMER_PAGE_SOURCE_CUSTOMER_LIST;

  /**
   * 用于下拉列表选择,输入回显编码集合，字符串集合，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据
   */
  @ApiModelProperty("用于下拉列表选择,输入回显编码集合，字符串集合，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据")
  private List<String> selectedCodes;
  @ApiModelProperty("用于下拉列表选择,输入回显编码集合，字符串集合，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据")
  private String selectedCode;

  @ApiModelProperty("渠道编码")
  private String channelCode;

  @ApiModelProperty("经度")
  private BigDecimal longitude;

  @ApiModelProperty("纬度")
  private BigDecimal latitude;

  @ApiModelProperty("距离(公里)")
  private BigDecimal distance;

  @ApiModelProperty("距离单位(公里)")
  private String distanceUnit = "km";

  @ApiModelProperty("合同客户;数据字典[yesOrNo]")
  private String contractCustomer;

  @ApiModelProperty("是否用于客户用户绑定")
  private String queryForCustomerUser;
}
