package com.biz.crm.mdm.admin.web.dataview.product;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * MDM商品小类管理数据视图
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/6/18 14:25
 */
@Component
public class ProductSmallClassDataViewRegister implements DataviewRegister {
    @Override
    public String code() {
        return "mdm_product_small_class_data_view";
    }

    @Override
    public String desc() {
        return "MDM商品小类管理数据视图";
    }

    @Override
    public String buildSql() {
        return "select t.*    " +
                "    from mdm_product_small_class t  " +
                "    where t.del_flag = '" + DelFlagStatusEnum.NORMAL.getCode() + "' " +
                "    and t.tenant_code = :tenantCode";
    }
}
