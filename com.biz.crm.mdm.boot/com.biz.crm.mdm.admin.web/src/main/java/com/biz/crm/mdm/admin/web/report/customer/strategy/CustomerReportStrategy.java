package com.biz.crm.mdm.admin.web.report.customer.strategy;

import com.biz.crm.mdm.admin.web.report.customer.dto.AbstractCustomerPageDto;
import com.biz.crm.mdm.admin.web.report.customer.vo.CustomerPageVo;

import java.util.List;

/**
 * 客户信息报表数据策略(客户信息报表接口使用统一的service和mapper接口
 * ,使用此策略实现不同接口请求数据和返回数据封装)
 *
 * <AUTHOR>
 * @date 2022/3/29
 */
public interface CustomerReportStrategy<T extends AbstractCustomerPageDto> {

  /**
   * 获取报表来源
   *
   * @return 报表来源
   */
  String getPageSource();

  /**
   * 当请求报表数据时,封装请求参数数据
   *
   * @param dto 请求参数
   * @return 请求是否有效
   */
  Boolean onRequest(T dto);

  /**
   * 当返回报表数据时,封装报表返回数据
   *
   * @param voList 报表返回数据
   */
  void onReturn(List<CustomerPageVo> voList);
}
