package com.biz.crm.mdm.admin.web.dataview.customer.strategy;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.biz.crm.mdm.business.customer.local.repository.CustomerRepository;
import com.biz.crm.mdm.business.org.local.repository.OrgRepository;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.europa.database.sdk.context.execute.DatabaseExecuteExternalRequest;
import com.bizunited.nebula.europa.database.sdk.strategy.ParameterValueBindingStrategy;
import com.bizunited.nebula.europa.sdk.context.execute.ExecuteParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @title CustomerParameterValueBindingStrategy
 * @date 2022/12/29 19:55
 * @description 客户信息ruleCode规则编码取值
 */
@Component("CustomerParameterValueBindingStrategy")
public class CustomerParameterValueBindingStrategy implements ParameterValueBindingStrategy {
  @Autowired
  private OrgRepository orgRepository;

  @Override
  public String getBindType() {
    return "Customer_RuleCode";
  }

  @Override
  public String getBindTypeCnName() {
    return "客户信息ruleCode规则编码取值";
  }

  @Override
  public Boolean getOutside() {
    return false;
  }

  @Override
  public Class<?>[] matchedJavaClasses() {
    return new Class[]{CharSequence.class};
  }

  @Override
  public Object bindingFieldValue(ExecuteParameter executeParameter, DatabaseExecuteExternalRequest databaseExecuteExternalRequest) {
    String orgCode = (String) databaseExecuteExternalRequest.getAttribute("orgCode");
    if (StringUtils.isBlank(orgCode)){
      return null;
    }
    String ruleCodeByOrgCode = orgRepository.findRuleCodeByOrgCode(orgCode, TenantUtils.getTenantCode());
    if(StringUtils.isBlank(ruleCodeByOrgCode)){
      return null;
    }
    return ruleCodeByOrgCode;
  }
}
