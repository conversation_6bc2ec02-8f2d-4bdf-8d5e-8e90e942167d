package com.biz.crm.mdm.admin.web.login.refresh;

import java.util.List;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.admin.web.exports.entity.FacturerImportExportAuthEntity;
import com.biz.crm.mdm.admin.web.exports.repository.FacturerImportExportAuthRepository;
import com.biz.crm.mdm.admin.web.login.DefaultPerfectLoginUserDetails;
import com.biz.crm.mdm.admin.web.login.refresh.vo.ImportExportTenantInfo;
import com.biz.crm.mdm.admin.web.service.PositionRoleVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.biz.crm.mdm.business.user.sdk.service.UserValidityCheckService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.bizunited.nebula.common.util.tenant.TenantContextHolder;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.common.vo.AbstractTenantInfo;
import com.bizunited.nebula.security.sdk.config.SimpleSecurityProperties;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import com.bizunited.nebula.security.sdk.refresh.AuthenticationRefreshStrategy;
import com.google.common.collect.Sets;

/**
 * 导入导出任务所需要的用户身份初始化动作
 *
 * <AUTHOR> yinwenjie
 * @since 2022-08-28 由银文杰进行修改
 */
@Component
public class ImportExportAuthenticationRefreshStrategy extends DefaultPerfectLoginUserDetails implements AuthenticationRefreshStrategy {

  /** nebula security模块中，关于默认用户身份信息的配置情况 */
  @Autowired 
  private SimpleSecurityProperties simpleSecurityProperties;
  @Autowired
  @Lazy
  private UserValidityCheckService userValidityCheckService;  
  @Autowired(required = false)
  @Lazy
  private FacturerImportExportAuthRepository facturerImportExportAuthRepository;
  @Autowired
  @Lazy
  private PositionRoleVoService positionRoleVoService;
  /**
   * 日志
   */
  private static final Logger LOGGER = LoggerFactory.getLogger(ImportExportAuthenticationRefreshStrategy.class);
  
  @Override
  public int getOrder() {
    return 8;
  }

  @Override
  public boolean matched(Object info) {
    if(info != null && (info instanceof TaskGlobalParamsVo)) {
      return true;
    }
    return false;
  }

  /**
   * 根据导入导出任务公共信息设置登录信息
   *
   * @deprecated 需要在导入导出方面提供相关的支持
   *
   * @param vo 任务参数vo
   */
  @Override
  public UserIdentity refresh(Object info) {
    TaskGlobalParamsVo vo = (TaskGlobalParamsVo)info;
    /*
     * 重建账号主要依据创建导出任务的厂商用户
     * 经销商体系的导出，在10版本中不适用，原因就是这里的用户只是基于厂商用户进行的查询
     * 除非诸如EMS这样的系统，重写ImportExportAuthBean
     * 
     * 但是08版本做了修改
     * 
     * 1、查询当前导入导出任务用户身份关联映射表中，取出用户信息
     * 如果没有，则依据任务的创建者进行用户身份取出
     * 如果没有取到，则从配置上下文中取得管理员账号
     * 2、根据用户账号，进行用户信息的查询，并得到角色信息
     * 3、根据第1步或者第2步的内容，重建用户认证身份
     * 4、无论以上运行情况如何，TenantContextHolder上下文都需要重建
     * */
    
    // 1、=======
    String account = null;
    String userName = "超级管理员";
    String[] roleCodes = new String[] {};
    String tenantCode = null;
    String currentPositionCode = null;
    String taskCode = vo.getTaskCode();
    String appCode = vo.getTenantCode();

    String applicationName = vo.getApplicationName();
    boolean found = false;
    // 先从导入导出任务身份映射信息中寻找
    if(this.facturerImportExportAuthRepository != null) {
      FacturerImportExportAuthEntity currentFacturerImportExportAuth = this.facturerImportExportAuthRepository.findByTaskCodeAndAppCodeAndApplicationName(taskCode, appCode, applicationName);
      if(currentFacturerImportExportAuth != null) {
        account = currentFacturerImportExportAuth.getCreateAccount();
        tenantCode = currentFacturerImportExportAuth.getTenantCode();
        currentPositionCode = currentFacturerImportExportAuth.getPostCode();
        found = true;
      }
    }
    // 如果没有找到就试图基于任务的创建者作为运行者账号
    if(!found && StringUtils.isNotBlank(vo.getCreateAccount())) {
      account = vo.getCreateAccount();
      tenantCode = vo.getTenantCode();
      found = true;
    }
    // 如果条件成立，说明已经通过以上两种方式的某一种找到了用户
    // 那么执行2、=======
    if(found) {
      Set<String> roles = this.findByAccount(tenantCode, account);
      if(!CollectionUtils.isEmpty(roles)) {
        roleCodes = roles.toArray(new String[] {});
      }
      // 设定username
      if (StringUtils.isNotBlank(vo.getCreateAccountName())) {
        userName = vo.getCreateAccountName();
      }
    } 
    // 如果还没有找到，就从配置信息中以管理员账号为运行身份
    else {
      account = this.simpleSecurityProperties.getIndependencyUser();
      roleCodes = this.simpleSecurityProperties.getIndependencyRoles();
      tenantCode = TenantUtils.getTenantCode();
    }
    LOGGER.info(" ========= 导入导出过程，重建用户身份信息 ：" + account);
    // 初始化用户
    if (StringUtils.isNotBlank(tenantCode)) {
      if (TenantUtils.getTenantCode().equals(tenantCode) == false) {
        // 用户登录过程中，发现登录用户的租户信息，重建缓存中的租户信息
        LOGGER.info(" ========= 导入导出过程，重建用户租户信息");
        AbstractTenantInfo tenantInfo = new ImportExportTenantInfo(tenantCode);
        TenantContextHolder.setTenantInfo(tenantInfo);
        LOGGER.info("========= tenantCode = :{}", tenantCode);
      }
    }
    Integer type = this.simpleSecurityProperties.getDefaultLoginType();
    AbstractTenantInfo tenantInfo = TenantContextHolder.getTenantInfo();
    System.out.println(tenantInfo);
    UserVo userVo = this.userValidityCheckService.verificationManageByAccount(account);
    FacturerUserDetails facturer = new FacturerUserDetails();
    facturer.setAccount(account);
    // 厂商用户（超级管理员）
    facturer.setLoginType(type);
    facturer.setIdentityType("u");
    facturer.setRoleCodes(roleCodes);
    facturer.setUsername(userName);
    facturer.setTenantCode(tenantCode);
    super.perfectLoginUserDetails(userVo, facturer);
    super.perfectLoginPostAndOrg(facturer);
    // 如果记录了导入导出时的岗位，则以那个岗位为准
    if(StringUtils.isNotBlank(currentPositionCode)) {
      facturer.setPostCode(currentPositionCode);
    }
    return facturer;
  }
  
  /**
   * 按照指定的账号查询对应的角色信息
   * @param tenantCode
   * @param account
   * @return
   */
  private Set<String> findByAccount(String tenantCode , String account) {
    List<PositionVo> positionVos = positionRoleVoService.findByAccount(tenantCode, account);
    if (CollectionUtils.isEmpty(positionVos)) {
      return Sets.newHashSet();
    }
    Set<String> roleCodes = Sets.newHashSet();
    positionVos.forEach(p->{
      if (!CollectionUtils.isEmpty(p.getRoleList())) {
        roleCodes.addAll(p.getRoleList());
      }
    });
    return roleCodes;
  }
}
