<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.admin.web.report.customermaterial.mapper.CustomerMaterialVoMapper">

  <sql id="customerMaterial">
    cm.id as id,
        cm.customer_org_code as customerOrgCode ,
        cm.material_code as materialCode ,
        cm.bar_code as barCode ,
        cm.tenant_code as tenantCode,
        cm.del_flag as delFlag,
        cm.enable_status as enableStatus,
        cm.create_account as createAccount,
        cm.create_time as createTime,
        cm.create_name as createName,
        cm.modify_account as modifyAccount,
        cm.modify_time as modifyTime,
        cm.modify_name as modifyName,
        cm.remark as remark
  </sql>


  <select id="findByConditions" resultType="com.biz.crm.mdm.admin.web.report.customermaterial.vo.CustomerMaterialVo">
    SELECT
    <include refid="customerMaterial"/>,
    material.material_name as materialName ,
    org.customer_org_name as customerOrgName
    FROM mdm_customer_material cm
    LEFT JOIN mdm_material material on cm.material_code = material.material_code
    LEFT JOIN mdm_customer_org org on cm.customer_org_code = org.customer_org_code
    <where>
        cm.tenant_code=#{dto.tenantCode}
      <if test="dto.delFlag != null and dto.delFlag !=''">
        and cm.del_flag =#{dto.delFlag}
      </if>
      <if test="dto.enableStatus != null and dto.enableStatus !=''">
        and cm.enable_status =#{dto.enableStatus}
      </if>
      <if test="dto.customerOrgCode != null and dto.customerOrgCode !=''">
        and cm.customer_org_code =#{dto.customerOrgCode}
      </if>
      <if test="dto.materialCode != null and dto.materialCode !=''">
        and cm.material_code =#{dto.materialCode}
      </if>
      <if test="dto.barCode != null and dto.barCode !=''">
        and cm.bar_code =#{dto.barCode}
      </if>
      <if test="dto.materialName != null and dto.materialName !=''">
        <bind name="likeMaterialName" value="'%' + dto.materialName + '%'"/>
        AND material.material_name like #{likeMaterialName}
      </if>
      <if test="dto.customerOrgName != null and dto.customerOrgName !=''">
        <bind name="likeCustomerOrgName" value="'%' + dto.customerOrgName + '%'"/>
        AND org.customer_org_name like #{likeCustomerOrgName}
      </if>
    </where>
    ORDER BY
        cm.create_time DESC
  </select>
</mapper>