package com.biz.crm.mdm.admin.web.report.user.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.user.dto.UserCombinePageDto;
import com.biz.crm.mdm.admin.web.report.user.vo.UserCombineVo;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @description 用户组合信息VO服务接口类
 * @date 2024/02/29
 */
public interface UserCombineVoService {

  /**
   * 用户组合信息分页条件查询
   *
   * @param pageable 分页信息
   * @param dto 查询条件参数dto
   * @return 分页数据
   */
  Page<UserCombineVo> findByConditions(Pageable pageable, UserCombinePageDto dto);
}
