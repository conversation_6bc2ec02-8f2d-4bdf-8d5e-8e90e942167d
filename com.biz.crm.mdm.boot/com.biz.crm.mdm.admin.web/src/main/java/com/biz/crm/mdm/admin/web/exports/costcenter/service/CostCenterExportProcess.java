package com.biz.crm.mdm.admin.web.exports.costcenter.service;

import com.alibaba.fastjson.JSONArray;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.mdm.admin.web.exports.costcenter.mapper.CostCenterOrgMapper;
import com.biz.crm.mdm.admin.web.exports.costcenter.model.CostCenterOrgExportVo;
import com.biz.crm.mdm.business.cost.sdk.dto.MdmCostCenterDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/30 13:50
 **/
@Component
public class CostCenterExportProcess implements ExportProcess<CostCenterOrgExportVo> {


    @Resource
    private CostCenterOrgMapper costCenterOrgMapper;


    @Override
    public Integer getTotal(Map<String, Object> params) {
        params = this.convertEuropaParam(params);
        MdmCostCenterDto dto = this.transParam(params);
        return costCenterOrgMapper.findTotal(dto);
    }

    @Override
    public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
        params = this.convertEuropaParam(params);
        MdmCostCenterDto dto = this.transParam(params);
        List<CostCenterOrgExportVo> list = costCenterOrgMapper.findListByCondition(dto);
        return toJSONArray(list);
    }

    @Override
    public String getBusinessCode() {
        return "MDM_COST_CENTER_COST_EXPORT";
    }

    @Override
    public String getBusinessName() {
        return "成本中心组织导出";
    }

    public MdmCostCenterDto transParam(Map<String, Object> map) {
        MdmCostCenterDto dto = new MdmCostCenterDto();
        if (map.containsKey("costCenterCode")) {
            dto.setCostCenterCode(map.get("costCenterCode").toString());
        }
        if (map.containsKey("costCenterName")) {
            dto.setCostCenterName(map.get("costCenterName").toString());
        }
        if (map.containsKey("companyCode")) {
            dto.setCompanyCode(map.get("companyCode").toString());
        }
        if (map.containsKey("enableStatus")) {
            dto.setEnableStatus(map.get("enableStatus").toString());
        }
        return dto;
    }
}
