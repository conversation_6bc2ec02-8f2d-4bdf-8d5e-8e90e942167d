<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.admin.web.report.user.mapper.UserDetailMapper">
  <resultMap id="userDetailVo" type="com.biz.crm.mdm.admin.web.report.user.vo.UserDetailVo"/>

  <!--查询用户详细信息-->
  <select id="findByUserName" resultMap="userDetailVo">
    SELECT
    b10.*,
    b20.org_code,
    b20.org_name,
    b30.position_level_name,
    b40.position_name AS parent_name,
    b42.org_code AS parent_org_code,
    b42.org_name AS parent_org_name,
    b50.primary_flag,
    b50.primary_flag,
    b60.user_name,
    b60.user_name AS account,
    b60.full_name,
    b60.user_code,
    b60.user_phone,
    b60.user_head_url,
    b60.enable_status AS user_enable_status,
    b60.job_code,
    b60.employee_type,
    b80.user_code AS parent_user_code,
    b80.user_name AS parent_user_name,
    b80.full_name AS parent_full_name
    FROM mdm_position b10
    LEFT JOIN mdm_org_position b11 ON b11.tenant_code = b10.tenant_code AND b11.position_code = b10.position_code
    LEFT JOIN mdm_org b20 ON b20.tenant_code = b11.tenant_code AND b20.org_code = b11.org_code
    LEFT JOIN mdm_position_level b30 ON b30.tenant_code = b10.tenant_code AND b10.position_level_code =
    b30.position_level_code
    LEFT JOIN mdm_position b40 ON b40.tenant_code = b10.tenant_code AND b10.parent_code = b40.position_code
    LEFT JOIN mdm_org_position b41 ON b41.tenant_code = b40.tenant_code AND b41.position_code = b40.position_code
    LEFT JOIN mdm_org b42 ON b42.tenant_code = b41.tenant_code AND b42.org_code = b41.org_code
    LEFT JOIN mdm_user_position b50 ON b50.tenant_code = b10.tenant_code AND b10.position_code = b50.position_code
    LEFT JOIN mdm_user b60 ON b60.tenant_code = b50.tenant_code AND b50.user_name = b60.user_name
    LEFT JOIN mdm_user_position b70 ON b70.tenant_code = b40.tenant_code AND b40.position_code = b70.position_code
    LEFT JOIN mdm_user b80 ON b80.tenant_code = b70.tenant_code AND b70.user_name = b80.user_name
    WHERE b60.tenant_code=#{dto.tenantCode} AND b60.del_flag=#{dto.delFlag}
    <if test="dto.userName !=null and dto.userName != '' ">
      AND b60.user_name = #{dto.userName}
    </if>
    <if test="dto.postCode !=null and dto.postCode != '' ">
      AND b10.position_code = #{dto.postCode}
    </if>
    ORDER BY b10.position_code
  </select>

</mapper>
