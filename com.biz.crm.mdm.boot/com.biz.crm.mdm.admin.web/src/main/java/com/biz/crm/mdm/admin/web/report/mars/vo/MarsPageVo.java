package com.biz.crm.mdm.admin.web.report.mars.vo;

import com.biz.crm.business.common.sdk.vo.TenantVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 数据权限分页返回VO
 * @date 2023/11/20
*/
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "数据权限分页返回VO")
public class MarsPageVo extends TenantVo {

  private static final long serialVersionUID = -808132023846047013L;

  /**
   * 数据权限编码
   */
  @ApiModelProperty("数据权限编码")
  private String marsCode;
  /**
   * 菜单路径
   */
  @ApiModelProperty("菜单路径")
  private String competencePath;
  /**
   * 菜单编码
   */
  @ApiModelProperty("菜单编码")
  private String competenceCode;
  /**
   * 菜单名称
   */
  @ApiModelProperty("菜单名称")
  private String competenceName;
  /**
   * 列表编码
   */
  @ApiModelProperty("列表编码")
  private String listCode;
  /**
   * 列表名称
   */
  @ApiModelProperty("列表名称")
  private String listName;
  /**
   * 是否是默认数据权限(1:是,0:不是)
   */
  @ApiModelProperty("是否是默认数据权限(1:是,0:不是)")
  private String defaultScope;
  /**
   * 数据权限范围KEY
   */
  @ApiModelProperty("数据权限范围KEY")
  private String scopeKey;
  /**
   * 数据权限范围名称
   */
  @ApiModelProperty("数据权限范围名称")
  private String scopeName;
  /**
   * 数据权限范围选定业务值编码
   */
  @ApiModelProperty("数据权限范围选定业务值编码")
  private String scopeValues;
  /**
   * 数据权限范围选定业务值名称
   */
  @ApiModelProperty("数据权限范围选定业务值名称")
  private String scopeNames;
  /**
   * 数据权限分组编码
   */
  @ApiModelProperty("数据权限分组编码")
  private String selectModeGroupCode;
  /**
   * 数据权限分组名称
   */
  @ApiModelProperty("数据权限分组名称")
  private String selectModeGroupName;
  /**
   * 数据权限方式key
   */
  @ApiModelProperty("数据权限方式key")
  private String selectModeKey;
  /**
   * 数据权限方式名称
   */
  @ApiModelProperty("数据权限方式名称")
  private String selectModeName;

  /**
   * 默认数据权限方式key
   */
  @ApiModelProperty("默认数据权限方式key")
  private String defaultSelectModeKey;
  /**
   * 默认数据权限方式名称
   */
  @ApiModelProperty("默认数据权限方式名称")
  private String defaultSelectModeName;
  /**
   * 数据权限方式参考值编码
   */
  @ApiModelProperty("数据权限方式参考值编码")
  private String selectModeValues;
  /**
   * 数据权限方式参考值名称
   */
  @ApiModelProperty("数据权限方式参考值名称")
  private String selectModeValueNames;
  /**
   * 数据权限列字段
   */
  @ApiModelProperty("数据权限列字段")
  private String fields;
  /**
   * 数据权限列字段描述
   */
  @ApiModelProperty("数据权限列字段描述")
  private String fieldNames;
}