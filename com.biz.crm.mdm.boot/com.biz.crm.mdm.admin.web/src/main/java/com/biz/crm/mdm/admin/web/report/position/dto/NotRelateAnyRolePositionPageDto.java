package com.biz.crm.mdm.admin.web.report.position.dto;

import com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 未关联任何角色的职位分页列表查询条件Dto
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "未关联任何角色的职位分页列表查询条件Dto", description = "未关联任何角色的职位分页列表查询条件Dto")
public class NotRelateAnyRolePositionPageDto extends AbstractPositionPageDto {

  /**
   * 角色编码
   */
  @ApiModelProperty("角色编码")
  private String roleCode;

  /**
   * 用户姓名
   */
  @ApiModelProperty("用户姓名")
  private String fullName;

  /**
   * 职位名称
   */
  @ApiModelProperty("职位名称")
  private String positionName;

  /**
   * 分页来源
   */
  @ApiModelProperty(value = "分页来源",hidden = true)
  private String pageSource = PositionReportConstant.POSITION_PAGE_SOURCE_NOT_RELATE_ANY_ROLE_POSITION_LIST;
}
