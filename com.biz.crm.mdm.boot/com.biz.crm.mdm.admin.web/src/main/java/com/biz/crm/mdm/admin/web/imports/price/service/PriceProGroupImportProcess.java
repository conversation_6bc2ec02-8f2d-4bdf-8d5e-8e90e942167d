package com.biz.crm.mdm.admin.web.imports.price.service;

import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.admin.web.imports.price.modle.PriceProGroupCrmImportVo;
import com.biz.crm.mdm.business.price.local.entity.Price;
import com.biz.crm.mdm.business.price.local.entity.PriceDimension;
import com.biz.crm.mdm.business.price.local.entity.PriceType;
import com.biz.crm.mdm.business.price.local.service.PriceService;
import com.biz.crm.mdm.business.price.local.service.PriceTypeService;
import com.biz.crm.mdm.business.price.sdk.constant.PriceConstant;
import com.biz.crm.mdm.business.price.sdk.enums.PriceDimensionEnum;
import com.biz.crm.mdm.business.price.sdk.register.PriceDimensionRegister;
import com.biz.crm.mdm.business.price.sdk.service.PriceDimensionContainerService;
import com.biz.crm.mdm.business.price.sdk.vo.PriceDimensionSelectVo;
import com.biz.crm.mdm.business.product.local.entity.Product;
import com.biz.crm.mdm.business.product.local.service.ProductService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 价格维护导入 维度：商品价格组
 *
 * <AUTHOR>
 * @describe:
 * @createTime 2022年05月24日 14:30:00
 */
@Component
@Slf4j
public class PriceProGroupImportProcess implements ImportProcess<PriceProGroupCrmImportVo> {

  @Autowired(required = false)
  private PriceService priceService;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @Autowired(required = false)
  private PriceDimensionContainerService priceDimensionContainerService;

  @Autowired(required = false)
  private List<PriceDimensionRegister> priceDimensionRegisters;

  @Autowired(required = false)
  private ProductService productService;

  @Autowired(required = false)
  private PriceTypeService priceTypeService;


  /**
   * 校验
   *
   * @param vo
   */
  private void validate(PriceProGroupCrmImportVo vo) {
    boolean f = StringUtils.isNoneBlank(vo.getTypeCode(), vo.getRelateName(), vo.getProductCode());
    Validate.isTrue(f, "价格类型编码、价格组名称、商品编码不能为空");
    Validate.notNull(vo.getPrice(), "价格不能为空");
    Validate.notNull(vo.getBeginTime(), "开始时间不能为空");
    Validate.notNull(vo.getEndTime(), "结束时间不能为空");
    Validate.isTrue(vo.getBeginTime().compareTo(vo.getEndTime()) < 0, "开始时间必须大于结束时间");
    //校验价格类型
    PriceType detailByTypeCode = priceTypeService.findDetailByTypeCode(vo.getTypeCode());
    Validate.notNull(detailByTypeCode,"尚无当前价格类型编码");
    //设置价格维度编码
    HashMap<Integer, PriceDimensionEnum> map = new HashMap<>();
    for (PriceDimensionRegister priceDimensionRegister : priceDimensionRegisters) {
      String code = priceDimensionRegister.getCode();
      if (PriceDimensionEnum.PRODUCT.getDictCode().equals(code)) {
        map.put(priceDimensionRegister.sort(), PriceDimensionEnum.PRODUCT);
      }
      if (PriceDimensionEnum.PRICE_GROUP.getDictCode().equals(code)) {
        map.put(priceDimensionRegister.sort(), PriceDimensionEnum.PRICE_GROUP);
      }
    }
    Validate.notEmpty(map, "未查询到价格维度");
    List<Integer> collect = map.keySet().stream().sorted().collect(Collectors.toList());
    String typeCode = vo.getTypeCode();
    String typeName = "";
    //拼接
    for (Integer integer : collect) {
      PriceDimensionEnum priceDimensionEnum = map.get(integer);
      typeCode = typeCode.concat(PriceConstant.SEPARATOR).concat(priceDimensionEnum.getDictCode());
      if (StringUtils.isBlank(typeName)){
        typeName=typeName.concat(priceDimensionEnum.getValue());
      }else {
        typeName = typeName.concat(PriceConstant.NAME_SEPARATOR).concat(priceDimensionEnum.getValue());
      }
    }
    //设置价格维度编码
    vo.setTypeDetailCode(typeCode);
    vo.setTypeDetailName(typeName);
    //匹配价格组
    List<PriceDimensionSelectVo> priceGroup = priceDimensionContainerService.findSelectSourceDataByCodeAndKeyword("price_group", "", "");
    Validate.notEmpty(priceGroup, "未查询到价格组");
    Map<String, String> priceMap = priceGroup.stream().collect(Collectors.toMap(PriceDimensionSelectVo::getName, PriceDimensionSelectVo::getCode, (v1, v2) -> v1));
    //设置价格组编码
    if (priceMap.containsKey(vo.getRelateName())) {
      vo.setRelateCode(priceMap.get(vo.getRelateName()));
    } else {
      Validate.isTrue(false, vo.getRelateName() + ",未查询到当前价格组");
    }
  }

  @Override
  @Transactional
  public Map<Integer, String> execute(
      LinkedHashMap<Integer, PriceProGroupCrmImportVo> data,
      TaskGlobalParamsVo paramsVo,
      Map<String, Object> params) {
    final Optional<PriceProGroupCrmImportVo> first = data.values().stream().findFirst();
    if (!first.isPresent()) {
      return null;
    }
    PriceProGroupCrmImportVo vo = first.get();
    this.validate(vo);
    this.execute(vo, params);
    return null;
  }

  /**
   * 执行
   *
   * @param data
   * @param params
   */
  private void execute(PriceProGroupCrmImportVo vo, Map<String, Object> params) {
    Price price = this.nebulaToolkitService.copyObjectByWhiteList(vo, Price.class, HashSet.class, ArrayList.class);
    //设置价格维度
    List<PriceDimension> dimensionList = new ArrayList<>();
    PriceDimension product = new PriceDimension();
    product.setDimensionCode(PriceDimensionEnum.PRODUCT.getDictCode());
    product.setRelateCode(vo.getProductCode());
    //追加商品信息
    addProductMsg(product);
    PriceDimension group = new PriceDimension();
    group.setDimensionCode(PriceDimensionEnum.PRICE_GROUP.getDictCode());
    group.setRelateName(vo.getRelateName());
    group.setRelateCode(vo.getRelateCode());
    dimensionList.add(product);
    dimensionList.add(group);
    price.setDimensionList(dimensionList);
    this.priceService.create(price);
  }

  /**
   * 添加商品信息
   * @param product
   */
  private void addProductMsg(PriceDimension product) {
    Product byProductCode = this.productService.findByProductCode(product.getRelateCode());
    Validate.notNull(byProductCode,"["+product.getRelateCode()+"未查询到商品信息");
    product.setRelateName(byProductCode.getProductName());
  }

  @Override
  public Class findCrmExcelVoClass() {
    return PriceProGroupCrmImportVo.class;
  }

  @Override
  public String getBusinessCode() {
    return "MDM_PRICE_IMPORT";
  }

  @Override
  public String getBusinessName() {
    return "MDM价格维护导入";
  }

  @Override
  public String getTemplateCode() {
    return "MDM_PRICE_PRODUCT_GROUP_IMPORT";
  }

  @Override
  public String getTemplateName() {
    return "MDM价格维护导入，商品价格组维度";
  }
}
