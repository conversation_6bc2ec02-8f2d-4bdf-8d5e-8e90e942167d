package com.biz.crm.mdm.admin.web.login.form;

import javax.servlet.http.HttpServletRequest;

import com.bizunited.nebula.security.local.loginform.SimpleLoginFormDetails;
import com.bizunited.nebula.security.sdk.loginform.LoginDetails;
import jodd.util.StringUtil;

/**
 * CRM体系统中，由厂商身份的用户使用的各个子系统中，其经典登录所使用的表单，就是通过该类进行描述
 * <AUTHOR>
 */
public class LoginFormDetails extends SimpleLoginFormDetails implements LoginDetails {
  private static final long serialVersionUID = -2029086401040918765L;

  /**
   * 业务系统类型
   * TODO 这个业务系统类型有什么意义？
   */
  private Integer appType;

  /**
   * 小程序获取openId传入的code（SFA小程序）
   */
  private String code;
  
  public LoginFormDetails(HttpServletRequest request) {
    super(request);
    // 业务系统类型
    String appType = request.getParameter("appType");
    if (StringUtil.isNotEmpty(appType)) {
      this.appType = Integer.valueOf(appType);
    }
    String code = request.getParameter("code");
    if (StringUtil.isNotEmpty(code)) {
      this.code = code;
    }
  }
  public Integer getAppType() {
    return appType;
  }

  public void setAppType(Integer appType) {
    this.appType = appType;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }
}
