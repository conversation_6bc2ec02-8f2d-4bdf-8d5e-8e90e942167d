package com.biz.crm.mdm.admin.web.report.user.repository;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.mdm.admin.web.report.user.dto.UserDetailDto;
import com.biz.crm.mdm.admin.web.report.user.mapper.UserDetailMapper;
import com.biz.crm.mdm.admin.web.report.user.vo.UserDetailVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 用户详细信息(repository)
 *
 * <AUTHOR>
 * @since 2021-11-04 16:47:02
 */
@Component
public class UserDetailRepository {

  @Autowired(required = false)
  private UserDetailMapper userDetailMapper;

  /**
   * 分页
   *
   * @param dto
   * @return
   */
  public List<UserDetailVo> findByUserName(UserDetailDto dto) {
    dto = Optional.ofNullable(dto).orElse(new UserDetailDto());
    dto.setTenantCode(TenantUtils.getTenantCode());
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    return this.userDetailMapper.findByUserName(dto);
  }

}
