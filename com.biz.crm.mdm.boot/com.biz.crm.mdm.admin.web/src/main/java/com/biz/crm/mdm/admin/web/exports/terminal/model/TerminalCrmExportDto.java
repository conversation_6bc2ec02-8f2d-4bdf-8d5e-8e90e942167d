package com.biz.crm.mdm.admin.web.exports.terminal.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 终端导出查询参数
 *
 * <AUTHOR>
 * @date 2022/5/24
 */
@Data
public class TerminalCrmExportDto {
  /** 终端编码 */
  @ApiModelProperty("终端编码")
  private String terminalCode;

  /** 终端名称 */
  @ApiModelProperty("终端名称")
  private String terminalName;

  /** 终端类型 */
  @ApiModelProperty("终端类型")
  private String terminalType;

  /** 渠道 */
  @ApiModelProperty("渠道")
  private String channel;

  /** 审批状态 */
  @ApiModelProperty("审批状态")
  private String processStatus;

  /** 启用状态 */
  @ApiModelProperty(value = "启用状态")
  private String enableStatus;

  /** 组织编码 */
  @ApiModelProperty("组织编码")
  private String orgCode;

  /** 省 */
  @ApiModelProperty("省")
  private String provinceName;

  /** 市 */
  @ApiModelProperty("市")
  private String cityName;

  /** 区 */
  @ApiModelProperty("区")
  private String districtName;

  /** 删除标记 */
  @ApiModelProperty(value = "删除标记", hidden = false)
  private String delFlag;

  /** 租户编码 */
  @ApiModelProperty(value = "租户编码", hidden = false)
  private String tenantCode;

  /** 组织rule_code */
  @ApiModelProperty(value = "组织rule_code", hidden = false)
  private String orgRuleCode;

  /** 偏移量 */
  private Integer offset;

  /** limit */
  private Integer limit;
}
