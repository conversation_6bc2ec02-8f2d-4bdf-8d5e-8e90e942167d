package com.biz.crm.mdm.admin.web.dataview.product;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * mdm产品层级数据视图
 */
@Component
public class ProductLevelDataviewRegister implements DataviewRegister {
  @Override
  public String code() {
    return "mdm_productLevel_dataview";
  }

  @Override
  public String desc() {
    return "MDM产品层级数据视图";
  }

  @Override
  public String buildSql() {
    return "select a.*,b.product_level_name as parent_name " +
            "    from mdm_product_level a " +
            "    left join mdm_product_level b on a.parent_code=b.product_level_code AND b.tenant_code = :tenantCode " +
            "    WHERE a.del_flag = '" + DelFlagStatusEnum.NORMAL.getCode() + "' " +
            "    AND a.tenant_code = :tenantCode " +
            "    AND a.rule_code like concat (:ruleCode , '%') ";
  }
}
