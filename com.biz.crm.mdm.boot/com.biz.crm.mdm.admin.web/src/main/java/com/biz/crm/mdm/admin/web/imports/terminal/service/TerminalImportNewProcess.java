package com.biz.crm.mdm.admin.web.imports.terminal.service;

import com.alibaba.excel.util.StringUtils;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.admin.web.imports.terminal.model.TerminalImportNewVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.biz.crm.mdm.business.terminal.sdk.dto.*;
import com.biz.crm.mdm.business.terminal.sdk.service.TerminalVoService;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @className: com.biz.crm.mdm.admin.web.imports.terminal.service.TerminalImportNewProcess
 * @description: 终端导入 新
 * @author: xiaopeng.zhang
 * @create: 2024-07-23 16:24
 */
@Component
@Slf4j
public class TerminalImportNewProcess implements ImportProcess<TerminalImportNewVo> {

    /**
     * 手机号验证
     */
    private static final String PHONE_REGEX = "^1[23456789]\\d{9}$";
    /**
     * 合作类型 字典
     */
    private static String TERMINAL_TYPE = "terminal_type";
    /**
     * 终端渠道 字典
     */
    private static String TERMINAL_CHANNEL = "terminal_channel";
    /**
     * 终端等级 字典
     */
    private static String TERMINAL_LEVEL = "terminal_level";
    /**
     * 字典标签 字典
     */
    private static String TERMINAL_TAG = "terminal_tag";
    /**
     * 终端新增类型 字典
     */
    private static String TERMINAL_SFA_TYPE = "terminal_sfa_type";
    @Autowired(required = false)
    private DictDataVoService dictDataVoService;

    @Autowired(required = false)
    private PositionVoService positionVoService;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private OrgVoService orgVoService;

    @Autowired(required = false)
    private CustomerVoService customerVoService;

    @Autowired(required = false)
    private TerminalVoService terminalVoService;

    /**
     * 手机号验证
     *
     * @param number
     * @return
     */
    public static boolean isValidChineseMobileNumber(String number) {
        if (number == null || number.isEmpty()) {
            return false;
        }
        return number.matches(PHONE_REGEX);
    }

    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    @Override
    public boolean importBeforeValidationFlag() {
        return true;
    }

    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, TerminalImportNewVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, TerminalImportNewVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        Map<Integer, String> errMap = new HashMap<>();
        Validate.notEmpty(data, "导入数据不能为空！");
        //校验编码统计
        List<String> orgCodeList = new ArrayList<>();
        List<String> customerCodeList = new ArrayList<>();
        List<String> positionCodeList = new ArrayList<>();
        for (TerminalImportNewVo vo : data.values()) {
            if (StringUtils.isNotBlank(vo.getOrgCode())) {
                orgCodeList.add(vo.getOrgCode());
            }
            if (StringUtils.isNotBlank(vo.getCusCode())) {
                customerCodeList.add(vo.getCusCode());
            }
            if (StringUtils.isNotBlank(vo.getPositionCode())) {
                positionCodeList.add(vo.getPositionCode());
            }
            if (StringUtils.isNotBlank(vo.getPositionCode2())) {
                positionCodeList.add(vo.getPositionCode2());
            }
            if (StringUtils.isNotBlank(vo.getPositionCode3())) {
                positionCodeList.add(vo.getPositionCode3());
            }
        }

        List<OrgVo> orgVoList = this.orgVoService.findByOrgCodes(orgCodeList);
        List<CustomerVo> customerVoList = this.customerVoService.findByCustomerCodes(customerCodeList);
        List<PositionVo> positionVoList = this.positionVoService.findByLotPositionCodes(positionCodeList);

        //组织map
        Map<String, OrgVo> orgVoMap = orgVoList.stream().collect(Collectors.toMap(OrgVo::getOrgCode, o -> o, (v1, v2) -> v1));
        //客户map
        Map<String, CustomerVo> customerVoMap = customerVoList.stream().collect(Collectors.toMap(CustomerVo::getCustomerCode, o -> o, (v1, v2) -> v1));
        //职位map
        Map<String, PositionVo> positionVoMap = positionVoList.stream().collect(Collectors.toMap(PositionVo::getPositionCode, o -> o, (v1, v2) -> v1));


        //字典反转map
        //终端标签
        Map<String, String> terminalTagMap = this.dictDataVoService.findReverseMapByDictTypeCode(TERMINAL_TAG);
        //合作类型
        Map<String, String> terminalTypeMap = this.dictDataVoService.findReverseMapByDictTypeCode(TERMINAL_TYPE);
        //终端渠道
        Map<String, String> terminalChannelMap = this.dictDataVoService.findReverseMapByDictTypeCode(TERMINAL_CHANNEL);
        //终端等级
        Map<String, String> terminalLevelMap = this.dictDataVoService.findReverseMapByDictTypeCode(TERMINAL_LEVEL);
        //终端新增类型
        Map<String, String> addTypeMap = this.dictDataVoService.findReverseMapByDictTypeCode(TERMINAL_SFA_TYPE);
        //遍历校验 新增数据组建
        List<TerminalDto> dtoList = new ArrayList<>();
        data.forEach((rowNum, vo) -> {
            TerminalDto dto = this.nebulaToolkitService.copyObjectByWhiteList(vo, TerminalDto.class, HashSet.class, ArrayList.class);
            //非空校验
            this.validateIsTrue(StringUtils.isNotBlank(vo.getTerminalName()), "终端名称，不能为空！");
            this.validateIsTrue(StringUtils.isNotBlank(vo.getOrgCode()), "营销组织编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotBlank(vo.getOrgName()), "营销组织名称，不能为空！");
            this.validateIsTrue(StringUtils.isNotBlank(vo.getChannel()), "终端渠道，不能为空！");

            //this.validateIsTrue(StringUtils.isNotBlank(vo.getContactName()), "联系人，不能为空！");
            //this.validateIsTrue(StringUtils.isNotBlank(vo.getContactPhone()), "联系人电话，不能为空！");
            if (StringUtils.isNotBlank(vo.getContactPhone())) {
                this.validateIsTrue(isValidChineseMobileNumber(vo.getContactPhone()), "手机号格式错误，请验证！");
            }

            this.validateIsTrue(StringUtils.isNotBlank(vo.getCusCode()), "客户编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotBlank(vo.getCusName()), "客户名称，不能为空！");

            this.validateIsTrue(StringUtils.isNotBlank(vo.getUserName()), "业务员编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotBlank(vo.getFullName()), "业务员姓名，不能为空！");
            this.validateIsTrue(StringUtils.isNotBlank(vo.getPositionCode()), "职位编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotBlank(vo.getPositionName()), "职位名称，不能为空！");
            this.validateIsTrue(StringUtils.isNotBlank(vo.getAddTypeStr()), "新增类型，不能为空！");

            this.validateIsTrue(StringUtils.isNotBlank(vo.getTerminalAddress()), "终端地址，不能为空！");
            if (StringUtils.isNotBlank(vo.getMonthlyAverageStr())) {
                try {
                    BigDecimal monthlyAverage = new BigDecimal(vo.getMonthlyAverageStr());
                    dto.setMonthlyAverage(monthlyAverage);
                } catch (NumberFormatException e) {
                    log.error(e.getMessage(), e);
                    this.validateIsTrue(false, "常温月均奶容量，填写错误！");
                }
            }
            //字典值验证
            //合作类型
            if (StringUtils.isNotBlank(vo.getTerminalType())) {
                if (terminalTypeMap.containsKey(vo.getTerminalType())) {
                    dto.setTerminalType(terminalTypeMap.get(vo.getTerminalType()));
                } else {
                    this.validateIsTrue(false, String.format("合作类型%s,不存在", vo.getTerminalType()));
                }
            }
            //渠道
            if (StringUtils.isNotBlank(vo.getChannel())) {
                if (terminalChannelMap.containsKey(vo.getChannel())) {
                    dto.setChannel(terminalChannelMap.get(vo.getChannel()));
                } else {
                    this.validateIsTrue(false, String.format("终端渠道%s,不存在", vo.getChannel()));
                }
            }
            //等级
            if (StringUtils.isNotBlank(vo.getTerminalLevel())) {
                if (terminalLevelMap.containsKey(vo.getTerminalLevel())) {
                    dto.setTerminalLevel(terminalLevelMap.get(vo.getTerminalLevel()));
                } else {
                    this.validateIsTrue(false, String.format("终端等级%s,不存在", vo.getTerminalLevel()));
                }
            }
            //标签
            if (StringUtils.isNotBlank(vo.getTag())) {
                if (terminalTagMap.containsKey(vo.getTag())) {
                    TerminalTagDto tagDto = new TerminalTagDto();
                    tagDto.setTagDescription(vo.getTag());
                    tagDto.setTagType(terminalTagMap.get(vo.getTag()));
                    dto.setTagVos(Lists.newArrayList(tagDto));
                } else {
                    this.validateIsTrue(false, String.format("标签%s,不存在", vo.getTag()));
                }
            }
            //新增类型
            if (StringUtils.isNotBlank(vo.getAddTypeStr())) {
                if (addTypeMap.containsKey(vo.getAddTypeStr())) {
                    dto.setAddType(addTypeMap.get(vo.getAddTypeStr()));
                } else {
                    this.validateIsTrue(false, String.format("新增类型%s,不存在", vo.getAddTypeStr()));
                }
            }
            //关联信息查询
            //组织
            if (StringUtils.isNotBlank(vo.getOrgCode())) {
                if (orgVoMap.containsKey(vo.getOrgCode())) {
                    TerminalRelaOrgDto orgDto = new TerminalRelaOrgDto();
                    OrgVo orgVo = orgVoMap.get(vo.getOrgCode());
                    orgDto.setOrgCode(orgVo.getOrgCode());
                    orgDto.setOrgName(orgVo.getOrgName());
                    dto.setOrgList(Lists.newArrayList(orgDto));
                } else {
                    this.validateIsTrue(false, String.format("组织编码%s,不存在", vo.getOrgCode()));
                }
            }
            //客户
            if (StringUtils.isNotBlank(vo.getCusCode())) {
                if (customerVoMap.containsKey(vo.getCusCode())) {
                    TerminalRelaCusDto cusDto = new TerminalRelaCusDto();
                    CustomerVo customerVo = customerVoMap.get(vo.getCusCode());
                    cusDto.setCusCode(customerVo.getCustomerCode());
                    cusDto.setCusName(customerVo.getCustomerName());
                    cusDto.setOrgCode(customerVo.getCustomerOrgCode());
                    cusDto.setOrgName(customerVo.getCustomerOrgName());
                    dto.setRelaCusVos(Lists.newArrayList(cusDto));
                } else {
                    this.validateIsTrue(false, String.format("客户编码%s,不存在", vo.getCusCode()));

                }
            }
            List<TerminalRelaBusinessDto> relaBusinessDtoList = Lists.newArrayList();
            //业务员
            String positionCode = vo.getPositionCode();
            if (StringUtils.isNotBlank(positionCode)) {
                PositionVo positionVo = positionVoMap.get(positionCode);
                if (Objects.nonNull(positionVo)) {
                    if (positionVo.getUserName().equals(vo.getUserName())) {
                        TerminalRelaBusinessDto businessDto = new TerminalRelaBusinessDto();
                        businessDto.setPositionCode(positionVo.getPositionCode());
                        businessDto.setPositionName(positionVo.getPositionName());
                        businessDto.setOrgCode(positionVo.getOrgCode());
                        businessDto.setOrgName(positionVo.getOrgName());
                        businessDto.setFullName(positionVo.getFullName());
                        relaBusinessDtoList.add(businessDto);
                    } else {
                        this.validateIsTrue(false, "业务员1与职位1对应的人员不一致");
                    }
                } else {
                    this.validateIsTrue(false, String.format("职位编码%s,不存在", positionCode));
                }
            }

            String positionCode2 = vo.getPositionCode2();
            if (StringUtils.isNotBlank(positionCode2)) {
                PositionVo positionVo = positionVoMap.get(positionCode2);
                if (Objects.nonNull(positionVo)) {
                    if (positionVo.getUserName().equals(vo.getUserName2())) {
                        TerminalRelaBusinessDto businessDto = new TerminalRelaBusinessDto();
                        businessDto.setPositionCode(positionVo.getPositionCode());
                        businessDto.setPositionName(positionVo.getPositionName());
                        businessDto.setOrgCode(positionVo.getOrgCode());
                        businessDto.setOrgName(positionVo.getOrgName());
                        businessDto.setFullName(positionVo.getFullName());
                        relaBusinessDtoList.add(businessDto);
                    } else {
                        this.validateIsTrue(false, "业务员2与职位2对应的人员不一致");
                    }
                } else {
                    this.validateIsTrue(false, String.format("职位编码%s,不存在", positionCode2));
                }
            }

            String positionCode3 = vo.getPositionCode3();
            if (StringUtils.isNotBlank(positionCode3)) {
                PositionVo positionVo = positionVoMap.get(positionCode3);
                if (Objects.nonNull(positionVo)) {
                    if (positionVo.getUserName().equals(vo.getUserName3())) {
                        TerminalRelaBusinessDto businessDto = new TerminalRelaBusinessDto();
                        businessDto.setPositionCode(positionVo.getPositionCode());
                        businessDto.setPositionName(positionVo.getPositionName());
                        businessDto.setOrgCode(positionVo.getOrgCode());
                        businessDto.setOrgName(positionVo.getOrgName());
                        businessDto.setFullName(positionVo.getFullName());
                        relaBusinessDtoList.add(businessDto);
                    } else {
                        this.validateIsTrue(false, "业务员3与职位3对应的人员不一致");
                    }
                } else {
                    this.validateIsTrue(false, String.format("职位编码%s,不存在", positionCode3));
                }
            }
            dto.setRelaBusinessVos(relaBusinessDtoList);
            //联系人
            if (StringUtils.isNotBlank(vo.getContactName())
                    && StringUtils.isNotBlank(vo.getContactPhone())) {
                TerminalContactDto contactDto = new TerminalContactDto();
                contactDto.setContactName(vo.getContactName());
                contactDto.setContactPhone(vo.getContactPhone());
                contactDto.setContactMain(true);
                dto.setContacts(Lists.newArrayList(contactDto));
            }

            //新增终端记录
            dtoList.add(dto);
            //错误信息
            String errInfo = this.validateGetErrorInfo();
            if (errInfo != null) {
                errMap.put(rowNum, errInfo);
            }
        });
        if (errMap.isEmpty()) {
            this.terminalVoService.importCreate(dtoList);
        }
        return errMap;
    }

    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, TerminalImportNewVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return ImportProcess.super.tryConfirm(data, paramsVo, params);
    }

    @Override
    public Class<TerminalImportNewVo> findCrmExcelVoClass() {
        return TerminalImportNewVo.class;
    }

    @Override
    public String getTemplateCode() {
        return "MDM_TERMINAL_NEW_IMPORT";
    }

    @Override
    public String getTemplateName() {
        return "MDM终端信息导入（新）";
    }

    /**
     * 字符串按英文逗号","分割
     *
     * @param tag
     * @return
     */
    List<String> split(String tag) {
        if (StringUtils.isBlank(tag)) {
            return Lists.newArrayList();
        }
        String[] tags = tag.split(",");
        return Lists.newArrayList(tags);
    }

}
