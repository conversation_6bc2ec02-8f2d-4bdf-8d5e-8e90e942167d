package com.biz.crm.mdm.admin.web.report.position.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.position.dto.AbstractPositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.vo.PositionPageVo;
import org.apache.ibatis.annotations.Param;

/**
 * 职位复杂报表mybatis-plus接口类
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
public interface PositionReportMapper {

  /**
   * 职位分页列表
   *
   * @param dto  请求参数dto
   * @param page 分页信息
   * @return 职位分页列表
   */
  Page<PositionPageVo> findByConditions(@Param("page") Page<PositionPageVo> page, @Param("dto") AbstractPositionPageDto dto);
}
