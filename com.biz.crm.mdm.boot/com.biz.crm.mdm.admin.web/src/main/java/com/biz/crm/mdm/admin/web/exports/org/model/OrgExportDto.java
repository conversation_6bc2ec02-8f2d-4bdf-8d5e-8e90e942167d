package com.biz.crm.mdm.admin.web.exports.org.model;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrgExportDto {

  private String tenantCode;

  private String delFlag;

  /**
   * 组织编码
   */
  private String orgCode;

  /**
   * 组织名称
   */
  private String orgName;

  /**
   * 启用禁用
   */
  private String enableStatus;

  /**
   * 组织层级--名称（orgType）
   */
  private String levelName;

  /**
   * 偏移量
   */
  private Integer offset;

  /**
   * limit
   */
  private Integer limit;

}
