package com.biz.crm.mdm.admin.web.dataview.org;

import com.biz.crm.mdm.business.org.local.repository.OrgRepository;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.europa.database.sdk.context.execute.DatabaseExecuteExternalRequest;
import com.bizunited.nebula.europa.database.sdk.strategy.ParameterValueBindingStrategy;
import com.bizunited.nebula.europa.sdk.context.execute.ExecuteParameter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @describe: 企业组织 ruleCode规则编码取值
 * @createTime 2022年09月16日 14:44:00
 */
@Component("RuleCodeParameterByOrgCodeValueBindingStrategy")
public class RuleCodeParameterByOrgCodeValueBindingStrategy implements ParameterValueBindingStrategy {

  @Autowired
  private OrgRepository orgRepository;


  public RuleCodeParameterByOrgCodeValueBindingStrategy() {
  }

  @Override
  public String getBindType() {
    return "Org_RuleCode_ByOrgCode";
  }

  @Override
  public String getBindTypeCnName() {
    return "企业组织通过OrgCode获得ruleCode规则编码取值";
  }

  @Override
  public Boolean getOutside() {
    return false;
  }

  @Override
  public Class<?>[] matchedJavaClasses() {
    return new Class[]{CharSequence.class};
  }

  @Override
  public Object bindingFieldValue(ExecuteParameter executeParameter, DatabaseExecuteExternalRequest databaseExecuteExternalRequest) {
    String orgCode = (String) databaseExecuteExternalRequest.getAttribute("includeAllChildrenOrgCode");
    if (StringUtils.isBlank(orgCode)){
      return null;
    }
    String ruleCodeByOrgCode = orgRepository.findRuleCodeByOrgCode(orgCode, TenantUtils.getTenantCode());
    if (StringUtils.isBlank(ruleCodeByOrgCode)){
      return null;
    }
    return ruleCodeByOrgCode;
  }
}
