package com.biz.crm.mdm.admin.web.notifier;

import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.login.log.sdk.dto.LoginLogDto;
import com.biz.crm.mdm.business.login.log.sdk.service.LoginLogVoService;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.bizunited.nebula.security.sdk.event.AuthenticatedEventListener;
import com.bizunited.nebula.security.sdk.login.UserIdentity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

/**
 * spring security鉴权后会被触发的事件
 *
 * <AUTHOR>
 */
@Slf4j
@Component("AuthenticatedEventListenerImpl")
public class AuthenticatedEventListenerImpl implements AuthenticatedEventListener {

  @Autowired(required = false)
  private LoginLogVoService loginLogVoService;

  @Autowired
  private UserVoService userVoService;

  @Autowired(required = false)
  private DictDataVoService dictDataVoService;

  @Override
  public void onAuthenticationSuccess(UserIdentity userIdentity, Authentication authentication) {
    // 当登录成功后，创建登录日志
    if (userIdentity instanceof FacturerUserDetails) {
      FacturerUserDetails loginUserDetails = (FacturerUserDetails) userIdentity;
      LoginLogDto loginLogDto = new LoginLogDto();
      loginLogDto.setAppType(1);
      loginLogDto.setLoginType(loginUserDetails.getLoginType() == null?1:loginUserDetails.getLoginType());
      loginLogDto.setUsertype(loginUserDetails.getIdentityType());
      loginLogDto.setAccount(loginUserDetails.getAccount());
      loginLogDto.setFullName(loginUserDetails.getRealName());
      this.loginLogVoService.create(loginLogDto);
      //验证密码修改期限
      this.userVoService.findByUserName(loginUserDetails.getAccount());
    } else {
      log.error("security鉴权成功后，因登录实体类型转换不匹配，导致未创建登录日志！");
    }
  }
  
  @Override
  public void onAuthenticationFailed(AuthenticationException loginDetails) {
    // 当登录失败后，要做的业务逻辑在这里
  }

}
