package com.biz.crm.mdm.admin.web.dataview.product;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * mdm物流成本管理数据视图
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/28 17:24
 */
@Component
public class MaterialCostDataViewRegister implements DataviewRegister {
    @Override
    public String code() {
        return "mdm_material_cost_data_view";
    }

    @Override
    public String desc() {
        return "MDM物流成本管理数据视图";
    }

    @Override
    public String buildSql() {
        return " select a.*  " +
                " from mdm_material_cost a " +
                " where a.del_flag = '" + DelFlagStatusEnum.NORMAL.getCode() + "' " +
                " and a.tenant_code = :tenantCode ";
    }
}
