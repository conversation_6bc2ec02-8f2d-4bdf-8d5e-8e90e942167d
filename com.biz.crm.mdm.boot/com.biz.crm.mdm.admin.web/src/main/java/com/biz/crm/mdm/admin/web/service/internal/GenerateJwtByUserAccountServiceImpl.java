package com.biz.crm.mdm.admin.web.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.identity.FacturerUserDetails;
import com.biz.crm.mdm.admin.web.dto.GenerateJwtByUserAccountDto;
import com.biz.crm.mdm.admin.web.service.GenerateJwtByUserAccountService;
import com.biz.crm.mdm.admin.web.vo.GenerateJwtByUserAccountVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgPositionVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.biz.crm.mdm.business.user.sdk.service.UserPositionVoService;
import com.biz.crm.mdm.business.user.sdk.service.UserValidityCheckService;
import com.biz.crm.mdm.business.user.sdk.vo.UserPositionVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.bizunited.nebula.security.local.utils.JwtUtils;
import com.bizunited.nebula.security.sdk.config.SimpleSecurityProperties;
import com.bizunited.nebula.security.sdk.login.DecisionTypes;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-04-18 11:14
 * @description：通过用户基本信息生成对应jwt
 */
@Service
public class GenerateJwtByUserAccountServiceImpl implements GenerateJwtByUserAccountService {

  @Autowired(required = false)
  private SimpleSecurityProperties simpleSecurityProperties;
  @Autowired(required = false)
  @Lazy
  private UserPositionVoService userPositionVoService;
  @Autowired(required = false)
  private UserValidityCheckService userValidityCheckService;
  @Autowired(required = false)
  @Lazy
  private PositionVoService positionVoService;
  @Autowired(required = false)
  @Lazy
  private OrgPositionVoService orgPositionVoService;

  /**
   * 当前用户身份认证时，所使用的登录方式 （参见LoginDetails中的type属性）
   */
  private Integer defaultLoginType = 1;

  /**
   * 通过用户基本信息生成对应jwt
   * @param dto
   * @return
   */
  @Override
  public GenerateJwtByUserAccountVo create(GenerateJwtByUserAccountDto dto) {
    Validate.isTrue(StringUtils.isNotEmpty(dto.getUserAccount()), "请选择用户账号");
    Validate.isTrue(!ObjectUtils.isEmpty(dto.getTimeToLive()),  "请设置jwt有效时间");
    //根据用户账号查询详情
    UserVo userVo = this.userValidityCheckService.verificationManageByAccount(dto.getUserAccount());
    FacturerUserDetails mdmUser = new FacturerUserDetails();
    mdmUser.setLoginType(DecisionTypes.ACCOUNTANDPASSWORD.getCode());
    perfectLoginUserDetails(userVo, mdmUser);
    mdmUser.setAccount(dto.getUserAccount());
    perfectLoginPostAndOrg(mdmUser);
    GenerateJwtByUserAccountVo vo = new GenerateJwtByUserAccountVo();
    //传参单位:天 转换成 毫秒
    int ttl = dto.getTimeToLive() * 24 * 60 * 60 * 1000;
    String jwtEncodeContent = JwtUtils.encode(mdmUser, ttl, this.simpleSecurityProperties.getSecretKey());
    vo.setJwt(jwtEncodeContent);
    vo.setUserAccount(dto.getUserAccount());
    return vo;
  }

  /**
   * 完善登录信息中的用户基本信息
   *
   * @param currentUser 查询出的当前用户
   * @param loginUserDetails 登录用户信息
   */
  protected void perfectLoginUserDetails(UserVo currentUser, FacturerUserDetails loginUserDetails){
    // u-企业用户，c-客户用户，terminal-终端用户，customer_employee-经销商员工用户
    loginUserDetails.setIdentityType(currentUser.getUserType().trim());
    loginUserDetails.setTenantCode(currentUser.getTenantCode().trim());
    loginUserDetails.setUsername(currentUser.getUserName().trim());
    loginUserDetails.setPhone(currentUser.getUserPhone().trim());
    loginUserDetails.setRealName(currentUser.getFullName().trim());
    loginUserDetails.setAccount(currentUser.getUserName().trim());
  }

  /**
   * 完善登录信息中的岗位组织等信息
   *
   * 一、获取当前职位，优先级顺序：
   *     1、第一优先获取用户职位关系表“是否当前职位”为是的职位
   *     2、第二优先获取用户职位关系表“是否主职位”为是的职位
   * 二、获取当前职位对应组织
   *
   * @param loginUserDetails 登录用户信息
   */
  protected void perfectLoginPostAndOrg(FacturerUserDetails loginUserDetails){
    List<UserPositionVo> userPositionVos = this.userPositionVoService.findByUserName(loginUserDetails.getTenantCode(), loginUserDetails.getAccount());
    if (CollectionUtils.isEmpty(userPositionVos)) {
      return;
    }
    // 一、1、取设定在数据库中的“当前岗位”
    Optional<UserPositionVo> currentFlagVo = userPositionVos.stream()
            .filter(r-> Boolean.TRUE.equals(r.getCurrentFlag()))
            .findFirst();
    if (currentFlagVo.isPresent()) {
      UserPositionVo userPositionVo = currentFlagVo.get();
      loginUserDetails.setPostCode(userPositionVo.getPositionCode());
    } else {
      // 一、2、去当前数据库中的“默认岗位”
      Optional<UserPositionVo> primaryFlagVo = userPositionVos.stream()
              .filter(r-> Boolean.TRUE.equals(r.getPrimaryFlag()))
              .findFirst();
      if (primaryFlagVo.isPresent()) {
        UserPositionVo userPositionVo = primaryFlagVo.get();
        loginUserDetails.setPostCode(userPositionVo.getPositionCode());
      }
    }
    // 完善职位信息
    if (StringUtils.isBlank(loginUserDetails.getPostCode())) {
      return;
    }
    // 当前用户所有岗位编码集合
    loginUserDetails.setPostCodes(userPositionVos.stream().map(UserPositionVo::getPositionCode).distinct().collect(Collectors.toList()));
    // 下面是查当前用户当前岗位对应角色及组织
    List<PositionVo> positionVoList = positionVoService.findByIdsOrCodes(null, Lists.newArrayList(loginUserDetails.getPostCode()));
    if (CollectionUtils.isEmpty(positionVoList)) {
      return;
    }
    PositionVo positionVo = positionVoList.get(0);
    loginUserDetails.setPostName(positionVo.getPositionName());
    if (CollectionUtils.isNotEmpty(positionVo.getRoleList())) {
      loginUserDetails.setRoleCodes(positionVo.getRoleList().toArray(new String[] {}));
    }
    // 二、组织
    OrgVo orgVo = this.orgPositionVoService.findByPositionCode(loginUserDetails.getPostCode());
    if (orgVo == null) {
      return;
    }
    loginUserDetails.setOrgCode(orgVo.getOrgCode());
    loginUserDetails.setOrgName(orgVo.getOrgName());
  }
}
