package com.biz.crm.mdm.admin.web.report.customer.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.customer.repository.CustomerReportRepository;
import com.biz.crm.mdm.business.customer.local.entity.CustomerContactEntity;
import com.biz.crm.mdm.business.customer.local.repository.CustomerContactRepository;
import com.biz.crm.mdm.business.customer.sdk.dto.CustomerClientDto;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerClientVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerClientVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerContactVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 经销商客户信息voService实现类
 *
 * <AUTHOR>
 */
@Service
public class CustomerClientVoServiceImpl implements CustomerClientVoService {

  @Autowired(required = false)
  private CustomerReportRepository customerReportRepository;
  @Autowired(required = false)
  private CustomerContactRepository customerContactRepository;
  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public Page<CustomerClientVo> findChildrenPageByCustomerClientDto(Pageable pageable, CustomerClientDto dto) {
    pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
    if (dto == null || StringUtils.isAnyBlank(dto.getUserName())) {
      return new Page<>();
    }
    if (StringUtils.isBlank(dto.getTenantCode())) {
      dto.setTenantCode(TenantUtils.getTenantCode());
    }
    Page<CustomerClientVo> page = this.customerReportRepository.findChildrenPageByCustomerClientDto(pageable, dto);
    List<CustomerClientVo> records = page.getRecords();
    if (CollectionUtils.isEmpty(records)) {
      return new Page<>();
    }
    List<String> customerCodes = records.stream().map(CustomerClientVo::getCustomerCode).collect(Collectors.toList());
    this.buildContactInfo(customerCodes, records);
    return page;
  }

  /**
   * 封装客户联系人信息
   *
   * @param customerCodes 客户编码集合
   * @param records       待封装客户信息
   */
  private void buildContactInfo(List<String> customerCodes, List<CustomerClientVo> records) {
    List<CustomerContactEntity> contactEntities =
        this.customerContactRepository.findByCustomerCodes(customerCodes, TenantUtils.getTenantCode());
    if (org.springframework.util.CollectionUtils.isEmpty(contactEntities)) {
      return;
    }
    // k-customerCode,v-contactList
    Map<String, List<CustomerContactEntity>> contactMap = Maps.newHashMap();
    contactEntities.forEach(customerContactEntity -> {
      List<CustomerContactEntity> list = contactMap.getOrDefault(customerContactEntity.getCustomerCode(), Lists.newArrayList());
      list.add(customerContactEntity);
      contactMap.put(customerContactEntity.getCustomerCode(), list);
    });
    records.forEach(customerClientVo -> {
      List<CustomerContactEntity> entities = contactMap.get(customerClientVo.getCustomerCode());
      if (org.springframework.util.CollectionUtils.isEmpty(entities)) {
        return;
      }
      customerClientVo.setContactList((List<CustomerContactVo>) this.nebulaToolkitService
          .copyCollectionByBlankList(entities, CustomerContactEntity.class, CustomerContactVo.class, HashSet.class, ArrayList.class));
    });
  }

}
