package com.biz.crm.mdm.admin.web.imports.productitem.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import lombok.Data;

/**
 * @ClassName ProductItemImportVo
 * @Description 分类字典导入
 * <AUTHOR>
 * @Date 2024/11/19 16:36
 * @Version 1.0
 */
@Data
@CrmExcelImport(startRow = 4)
public class ProductItemImportVo extends CrmExcelVo {

    @CrmExcelColumn("分类名称")
    private String productItemName;

    @CrmExcelColumn("所属层级分类")
    private String productItemType;

    @CrmExcelColumn("所属业态编码")
    private String businessType;
}
