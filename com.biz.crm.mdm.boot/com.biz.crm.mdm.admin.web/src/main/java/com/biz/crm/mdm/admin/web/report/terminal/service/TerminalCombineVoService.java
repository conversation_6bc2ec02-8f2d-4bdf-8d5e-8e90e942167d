package com.biz.crm.mdm.admin.web.report.terminal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.terminal.dto.TerminalCombinePageDto;
import com.biz.crm.mdm.admin.web.report.terminal.vo.TerminalCombineVo;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @description 终端组合信息VO服务接口类
 * @date 2024/03/06
 */
public interface TerminalCombineVoService {

  /**
   * 终端组合信息分页条件查询
   *
   * @param pageable 分页信息
   * @param dto 查询条件参数dto
   * @return 分页数据
   */
  Page<TerminalCombineVo> findByConditions(Pageable pageable, TerminalCombinePageDto dto);
}
