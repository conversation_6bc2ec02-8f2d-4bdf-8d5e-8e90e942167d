package com.biz.crm.mdm.admin.web.imports.terminal.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 终端导入模型
 *
 * <AUTHOR>
 * @date 2022/5/13
 */
@Data
@CrmExcelImport
public class TerminalCrmImportVo extends CrmExcelVo {

  /** 终端编码 */
  @CrmExcelColumn("终端编码")
  private String terminalCode;

  /** 终端名称 */
  @CrmExcelColumn("终端名称")
  private String terminalName;

  /** 终端类型 */
  @CrmExcelColumn("终端类型")
  private String terminalType;

  /** 所属组织编码编码 */
  @CrmExcelColumn("所属组织编码编码")
  private String orgCode;

  /** 所属客户组织编码 */
  @CrmExcelColumn("所属客户组织编码")
  private String customerOrgCode;

  /** 渠道 */
  @CrmExcelColumn("渠道")
  private String channel;

  /** 营业执照法人姓名 */
  @CrmExcelColumn("营业执照法人姓名")
  private String licensePersonName;

  /** 营业执照注册号 */
  @CrmExcelColumn("营业执照注册号")
  private String licenseRegisterNumber;

  /** 营业执照企业名称 */
  @CrmExcelColumn("营业执照企业名称")
  private String licenseFirmName;

  /** 终端地址 */
  @CrmExcelColumn("终端地址")
  private String terminalAddress;

  /** 省 */
  private String provinceCode;

  /** 省 */
  @CrmExcelColumn("省")
  private String provinceName;

  /** 市 */
  private String cityCode;

  /** 市 */
  @CrmExcelColumn("市")
  private String cityName;

  /** 区 */
  private String districtCode;

  /** 区 */
  @CrmExcelColumn("区")
  private String districtName;

  /** 经度 */
  @CrmExcelColumn("经度")
  private BigDecimal longitude;

  /** 纬度 */
  @CrmExcelColumn("纬度")
  private BigDecimal latitude;

  /** 联系人姓名 */
  @CrmExcelColumn("联系人姓名")
  private String contactName;

  /** 联系人电话 */
  @CrmExcelColumn("联系人电话")
  private String contactPhone;
}
