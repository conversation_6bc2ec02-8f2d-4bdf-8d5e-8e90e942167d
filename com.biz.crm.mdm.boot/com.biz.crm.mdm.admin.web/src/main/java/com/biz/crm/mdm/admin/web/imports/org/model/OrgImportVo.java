package com.biz.crm.mdm.admin.web.imports.org.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import lombok.Data;

/**
 *
 *
 * <AUTHOR>
 * @date 2022/05/24
 */
@Data
@CrmExcelImport
public class OrgImportVo  extends CrmExcelVo {

  /**
   * 组织编码
   */
  @CrmExcelColumn("组织编码")
  private String orgCode;

  /**
   * 组织名称
   */
  @CrmExcelColumn("组织名称")
  private String orgName;

  /**
   * 上级组织编码
   */
  @CrmExcelColumn("上级组织编码")
  private String parentCode;

}
