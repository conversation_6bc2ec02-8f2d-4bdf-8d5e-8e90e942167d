package com.biz.crm.mdm.admin.web.imports.material.model;

import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelColumn;
import com.biz.crm.common.ie.sdk.excel.annotations.CrmExcelImport;
import com.biz.crm.common.ie.sdk.excel.vo.CrmExcelVo;
import lombok.Data;

import java.math.BigDecimal;

@Data
@CrmExcelImport
public class MaterialImportVo extends CrmExcelVo {

  /**
   * 物料编码
   */
  private String materialCode;

  /**
   * 物料名称
   */
  @CrmExcelColumn("物料名称")
  private String materialName;

  /**
   * 产品层级编码
   */
  @CrmExcelColumn("产品层级编码")
  private String productLevelCode;

  /**
   * 物料类型
   */
  @CrmExcelColumn("物料类型")
  private String materialType;

  /**
   * 规格
   */
  @CrmExcelColumn("规格")
  private String specification;

  /**
   * 销售公司
   */
  @CrmExcelColumn("销售公司")
  private String saleCompany;

  /**
   * 单位体系编码
   */
  @CrmExcelColumn("单位体系编码")
  private String unitTypeCode;

  /**
   * 成本价格
   */
  @CrmExcelColumn("成本价(元)")
  private String costPrice;

  /**
   * 条形码
   */
  @CrmExcelColumn("条形码")
  private String barCode;

  /**
   * AI编码
   */
  @CrmExcelColumn("AI编码")
  private String aiCode;

  /**
   * 标准单位
   */
  @CrmExcelColumn("标准单位(标盒,标箱)")
  private String standardUnit;

  /**
   * 盒码单位转换系数
   */
  @CrmExcelColumn("盒码单位换算系数")
  private BigDecimal boxUnitConversion;

  /**
   * 箱码单位转换系数
   */
  @CrmExcelColumn("箱码单位换算系数")
  private BigDecimal caseUnitConversion;
}
