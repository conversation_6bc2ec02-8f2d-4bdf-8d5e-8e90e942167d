<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.mdm.admin.web.report.customer.mapper.CustomerReportMapper">

    <select id="findByConditions" resultType="com.biz.crm.mdm.admin.web.report.customer.vo.CustomerPageVo">
        <if test="dto.pageSource != null and dto.pageSource == @com.biz.crm.mdm.admin.web.report.customer.constant.CustomerReportConstant@CUSTOMER_PAGE_SOURCE_CUSTOMER_LIST">
            <include refid="customerPage"/>
        </if>

        <if test="dto.pageSource != null and dto.pageSource == @com.biz.crm.mdm.admin.web.report.customer.constant.CustomerReportConstant@CUSTOMER_PAGE_SOURCE_HAS_RELATE_CURRENT_USER_CUSTOMER_LIST">
            <include refid="hasRelateCurrentUserCustomerPage"/>
        </if>

        <if test="dto.pageSource != null and dto.pageSource == @com.biz.crm.mdm.admin.web.report.customer.constant.CustomerReportConstant@CUSTOMER_PAGE_SOURCE_NOT_RELATE_CURRENT_USER_CUSTOMER_LIST">
            <include refid="notRelateCurrentUserCustomerPage"/>
        </if>

        <if test="dto.pageSource != null and dto.pageSource == @com.biz.crm.mdm.admin.web.report.customer.constant.CustomerReportConstant@CUSTOMER_PAGE_SOURCE_NOT_RELATE_ANY_USER_CUSTOMER_LIST">
            <include refid="notRelateAnyUserCustomerPage"/>
        </if>
    </select>
    <sql id="customerPage">
        SELECT temp.* FROM (
        SELECT
        a.id,
        a.process_number,
        a.process_key,
        <choose>
            <when test="dto.longitude != null and dto.latitude != null">
                IF(a.longitude is null or a.latitude is null, ***********,
                ROUND( st_distance_sphere ( point ( a.longitude, a.latitude ),
                point ( #{dto.longitude}, #{dto.latitude} )) / 1000, 2 )) as distance,
            </when>
            <otherwise>
                *********** as distance,
            </otherwise>
        </choose>
        MAX(a.contract_customer) contractCustomer,
        MAX(a.customer_code) customer_code,
        MAX(a.customer_level) customer_level,
        MAX(a.customer_name) customer_name,
        MAX(a.customer_type) customer_type,
        MAX(a.channel_code) channel_code,
        MAX(a.act_approve_status) act_approve_status,
        MAX(a.city_code) city_code,
        MAX(a.create_account) create_account,
        MAX(a.create_name) create_name,
        MAX(a.create_time) create_time,
        MAX(a.customer_org_code) customer_org_code,
        MAX(a.del_flag) del_flag,
        MAX(a.district_code) district_code,
        MAX(a.enable_status) enable_status,
        MAX(a.lock_state) lock_state,
        MAX(a.modify_account) modify_account,
        MAX(a.modify_name) modify_name,
        MAX(a.modify_time) modify_time,
        MAX(a.province_code) province_code,
        MAX(a.remark) remark,
        MAX(a.tenant_code) tenant_code,
        MAX(a.parent_customer_code) parent_customer_code,
        MAX(a.registered_address) registered_address,
        MAX(c.customer_org_name) customer_org_name,
        MAX(pa.customer_name) parentCustomerName
        FROM mdm_customer a
        LEFT JOIN mdm_customer pa ON (a.parent_customer_code = pa.customer_code)
        LEFT JOIN mdm_customer_r_org b ON (a.customer_code = b.customer_code AND a.tenant_code = b.tenant_code)
        LEFT JOIN mdm_customer_org c ON (a.customer_org_code = c.customer_org_code AND c.tenant_code = a.tenant_code)
        WHERE a.tenant_code = #{dto.tenantCode}
        AND a.del_flag = '${@<EMAIL>()}'
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and a.enable_status = #{dto.enableStatus}
        </if>
        <if test="dto.lockState != null and dto.lockState != ''">
            and a.lock_state = #{dto.lockState}
        </if>
        <if test="dto.contractCustomer != null and dto.contractCustomer != ''">
            and a.contract_customer = #{dto.contractCustomer}
        </if>
        <if test="dto.customerCode != null and dto.customerCode != ''">
            <bind name="likeCustomerCode" value="'%' + dto.customerCode + '%'"/>
            and a.customer_code like #{likeCustomerCode}
        </if>
        <if test="dto.customerName != null and dto.customerName != ''">
            <bind name="likeCustomerName" value="'%' + dto.customerName + '%'"/>
            and a.customer_name like #{likeCustomerName}
        </if>
        <if test="dto.unionName != null and dto.unionName != ''">
            <bind name="unionName" value="'%' + dto.unionName + '%'"/>
            and (a.customer_code like #{unionName} or a.customer_name like #{unionName})
        </if>
        <if test="dto.customerType != null and dto.customerType != ''">
            and a.customer_type = #{dto.customerType}
        </if>
        <if test="dto.orgCode != null and dto.orgCode != ''">
            and b.org_code = #{dto.orgCode}
        </if>
        <if test="dto.orgName != null and dto.orgName != ''">
            and b.org_code IN (
            SELECT
            c2.org_code
            FROM mdm_org c1
            WHERE c1.tenant_code=#{dto.tenantCode}
            <bind name="likeOrgName" value="'%' + dto.orgName + '%'"/>
            and c2.org_name like #{likeOrgName}
            )
        </if>
        <if test="dto.customerOrgCode != null and dto.customerOrgCode != ''">
            and a.customer_org_code = #{dto.customerOrgCode}
        </if>
        <if test="dto.channelCode != null and dto.channelCode != ''">
            and a.channel_code = #{dto.channelCode}
        </if>

        <if test="dto.queryForCustomerUser != null and dto.queryForCustomerUser != ''">
            and  (
                a.sap_status = '${@<EMAIL>()}'
                or  (a.sap_status = '${@<EMAIL>()}' and a.freeze_reason != 'A06')
            )
        </if>

        GROUP BY
        a.id ) temp
        where 1=1
        <if test="dto.distance != null">
            and temp.distance <![CDATA[ <= ]]> #{dto.distance}
        </if>
        ORDER BY
        <if test="dto.selectedCodes != null and dto.selectedCodes.size > 0">
            CASE
            <foreach collection="dto.selectedCodes" item="item" index="index">
                WHEN temp.customer_code = #{item} THEN ${index}
            </foreach>
            ELSE #{page.size} END asc,
        </if>
        temp.distance asc,temp.customer_code asc,temp.id asc
    </sql>

    <sql id="hasRelateCurrentUserCustomerPage">
        SELECT
        a.*,c.position_code,c.position_name
        FROM
        mdm_customer a
        LEFT JOIN mdm_customer_docking b ON (a.customer_code = b.customer_code AND a.tenant_code = b.tenant_code)
        LEFT JOIN mdm_position c ON (c.position_code = b.position_code AND c.tenant_code = b.tenant_code)
        LEFT JOIN mdm_user_position d ON (d.position_code = c.position_code AND d.tenant_code = c.tenant_code)
        WHERE a.tenant_code = #{dto.tenantCode}
        AND a.del_flag = '${@<EMAIL>()}'
        AND a.enable_status = '${@<EMAIL>()}'
        AND d.user_name = #{dto.userName}
        <if test="dto.customerCode != null and dto.customerCode != ''">
            <bind name="likeCustomerCode" value="'%' + dto.customerCode + '%'"/>
            and a.customer_code like #{likeCustomerCode}
        </if>
        <if test="dto.customerName != null and dto.customerName != ''">
            <bind name="likeCustomerName" value="'%' + dto.customerName + '%'"/>
            and a.customer_name like #{likeCustomerName}
        </if>
        <if test="dto.customerType != null and dto.customerType != ''">
            and a.customer_type = #{dto.customerType}
        </if>
        <if test="dto.enableStatus != null and dto.enableStatus != ''">
            and a.enable_status = #{dto.enableStatus}
        </if>
        ORDER BY
        a.create_time DESC
    </sql>
    <sql id="notRelateCurrentUserCustomerPage">
        SELECT
        a.*
        FROM
        mdm_customer a
        WHERE a.tenant_code = #{dto.tenantCode}
        AND a.del_flag = '${@<EMAIL>()}'
        AND NOT EXISTS (
        SELECT
        *
        FROM
        mdm_customer_docking j
        LEFT JOIN mdm_user_position k ON (j.position_code = k.position_code AND j.tenant_code = k.tenant_code)
        WHERE
        a.tenant_code = j.tenant_code
        AND a.customer_code = j.customer_code
        AND k.user_name = #{dto.userName}
        )
        <if test="dto.customerCode != null and dto.customerCode != ''">
            <bind name="likeCustomerCode" value="'%' + dto.customerCode + '%'"/>
            and a.customer_code like #{likeCustomerCode}
        </if>
        <if test="dto.customerName != null and dto.customerName != ''">
            <bind name="likeCustomerName" value="'%' + dto.customerName + '%'"/>
            and a.customer_name like #{likeCustomerName}
        </if>
        <if test="dto.customerType != null and dto.customerType != ''">
            and a.customer_type = #{dto.customerType}
        </if>
        GROUP BY
        a.id
        ORDER BY
        a.create_time DESC
    </sql>
    <sql id="notRelateAnyUserCustomerPage">
        SELECT
        a.*
        FROM
        mdm_customer a
        WHERE a.tenant_code = #{dto.tenantCode}
        AND a.del_flag = '${@<EMAIL>()}'
        AND a.enable_status = '${@<EMAIL>()}'
        AND NOT EXISTS (
        SELECT
        *
        FROM
        mdm_customer_docking j
        LEFT JOIN mdm_user_position k ON (j.position_code = k.position_code AND j.tenant_code = k.tenant_code)
        WHERE
        a.tenant_code = j.tenant_code
        AND a.customer_code = j.customer_code
        AND k.id IS NOT NULL
        )
        <if test="dto.customerCode != null and dto.customerCode != ''">
            <bind name="likeCustomerCode" value="'%' + dto.customerCode + '%'"/>
            and a.customer_code like #{likeCustomerCode}
        </if>
        <if test="dto.customerName != null and dto.customerName != ''">
            <bind name="likeCustomerName" value="'%' + dto.customerName + '%'"/>
            and a.customer_name like #{likeCustomerName}
        </if>
        <if test="dto.customerType != null and dto.customerType != ''">
            and a.customer_type = #{dto.customerType}
        </if>
        GROUP BY
        a.id
        ORDER BY
        a.create_time DESC
    </sql>

    <select id="findChildrenPageByCustomerClientDto"
            resultType="com.biz.crm.mdm.business.customer.sdk.vo.CustomerClientVo">
        SELECT
        e.*
        FROM
        ( SELECT mup.position_code AS position_code, mup.tenant_code AS tenant_code FROM mdm_user_position mup WHERE
        mup.user_name = #{dto.userName} ) a
        LEFT JOIN mdm_position b ON ( a.position_code = b.position_code AND a.tenant_code = b.tenant_code )
        LEFT JOIN mdm_position c ON ( c.rule_code LIKE concat( b.rule_code, '%' ) AND c.tenant_code = b.tenant_code )
        LEFT JOIN mdm_customer_docking d ON ( d.position_code = c.position_code AND c.tenant_code = d.tenant_code )
        LEFT JOIN mdm_customer e ON ( e.customer_code = d.customer_code AND e.tenant_code = d.tenant_code )
        WHERE e.tenant_code = #{dto.tenantCode}
        AND e.del_flag = '${@<EMAIL>()}'
        AND e.enable_status = '${@<EMAIL>()}'
        <if test="dto.customerCode != null and dto.customerCode != ''">
            <bind name="likeCustomerCode" value="'%' + dto.customerCode + '%'"/>
            and e.customer_code like #{likeCustomerCode}
        </if>
        <if test="dto.customerName != null and dto.customerName != ''">
            <bind name="likeCustomerName" value="'%' + dto.customerName + '%'"/>
            and e.customer_name like #{likeCustomerName}
        </if>
        <if test="dto.keyWord != null and dto.keyWord != ''">
            <bind name="keyWordLike" value="'%' + dto.keyWord + '%'"/>
            and (e.customer_name like #{keyWordLike} or e.customer_code like #{keyWordLike})
        </if>
        <if test="dto.customerType != null and dto.customerType != ''">
            and e.customer_type = #{dto.customerType}
        </if>
        <if test="dto.excludeCustomerCodes != null and dto.excludeCustomerCodes.size > 0">
            and e.customer_code not in
            <foreach collection="dto.excludeCustomerCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        GROUP BY
        e.id
        ORDER BY
        e.create_time DESC
    </select>

    <select id="findAddressByConditions"
            resultType="com.biz.crm.mdm.admin.web.report.customer.vo.CustomerAddressPageVo">
        select
        c.id as id,
        c.customer_code as customerCode,
        c.customer_name as customerName,
        c.registered_address as address,
        c.longitude,
        c.latitude,
        'customer' as addressType
        from mdm_customer c
        WHERE c.longitude is not null
        and c.latitude is not null
        <if test="dto.customerCode != null and dto.customerCode != ''">
            <bind name="customerCode" value="'%' + dto.customerCode + '%'"/>
            and c.customer_code like #{customerCode}
        </if>
        <if test="dto.customerName != null and dto.customerName != ''">
            <bind name="customerName" value="'%' + dto.customerName + '%'"/>
            and c.customer_name like #{customerName}
        </if>
        union all

        select
        cwa.id as id,
        cwa.customer_code as customerCode,
        mc.customer_name as customerName,
        cwa.warehouse_address as address,
        cwa.longitude,
        cwa.latitude,
        'warehouse' as addressType
        from
        mdm_customer mc
        LEFT JOIN
        mdm_customer_warehouse_address cwa on cwa.customer_code = mc.customer_code
        WHERE cwa.longitude is not null
        and cwa.latitude is not null
        <if test="dto.customerCode != null and dto.customerCode != ''">
            <bind name="customerCode" value="'%' + dto.customerCode + '%'"/>
            and mc.customer_code like #{customerCode}
        </if>
        <if test="dto.customerName != null and dto.customerName != ''">
            <bind name="customerName" value="'%' + dto.customerName + '%'"/>
            and mc.customer_name like #{customerName}
        </if>
    </select>
</mapper>