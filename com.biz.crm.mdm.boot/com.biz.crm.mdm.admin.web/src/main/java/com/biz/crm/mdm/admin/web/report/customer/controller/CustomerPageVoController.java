package com.biz.crm.mdm.admin.web.report.customer.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.admin.web.report.customer.dto.CustomerPageDto;
import com.biz.crm.mdm.admin.web.report.customer.dto.HasRelateCurrentUserCustomerPageDto;
import com.biz.crm.mdm.admin.web.report.customer.dto.NotRelateAnyUserCustomerPageDto;
import com.biz.crm.mdm.admin.web.report.customer.dto.NotRelateCurrentUserCustomerPageDto;
import com.biz.crm.mdm.admin.web.report.customer.service.CustomerPageVoService;
import com.biz.crm.mdm.admin.web.report.customer.vo.CustomerAddressPageVo;
import com.biz.crm.mdm.admin.web.report.customer.vo.CustomerPageVo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 客户信息: CustomerPageVo: 客户管理
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Slf4j
@RestController
@RequestMapping("/v1/customer/customer")
@Api(tags = "客户信息: CustomerPageVo: 客户管理")
public class CustomerPageVoController {

    @Autowired(required = false)
    private CustomerPageVoService customerPageVoService;

    @ApiOperation(value = "客户信息分页列表")
    @GetMapping("/findByConditions")
    public Result<Page<CustomerPageVo>> findByConditions(@PageableDefault(50) Pageable pageable,
                                                         @ApiParam(name = "CustomerSelectDto", value = "分页Dto") CustomerPageDto dto) {
        try {
            if (Objects.nonNull(dto.getDistance())) {
                dto.setDistance(dto.getDistance().divide(BigDecimal.valueOf(1000), 2, BigDecimal.ROUND_HALF_DOWN));
            }
            List<String> selectedCodes = Lists.newArrayList();
            if (StringUtil.isNotEmpty(dto.getSelectedCode())) {
                selectedCodes.add(dto.getSelectedCode());
            }
            if (CollectionUtil.isNotEmpty(dto.getSelectedCodes())) {
                selectedCodes.addAll(dto.getSelectedCodes());
            }
            dto.setSelectedCodes(selectedCodes);
            return Result.ok(this.customerPageVoService.findByConditions(pageable, dto));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "已关联当前企业用户的客户分页列表")
    @GetMapping("/findByHasRelateCurrentUserCustomerPageDto")
    public Result<Page<CustomerPageVo>> findByChildPositionPageDto(@PageableDefault(50) Pageable pageable,
                                                                   @ApiParam(name = "HasRelateCurrentUserCustomerPageDto", value = "分页Dto") HasRelateCurrentUserCustomerPageDto dto) {
        try {
            return Result.ok(this.customerPageVoService.findByConditions(pageable, dto));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "未关联当前企业用户的客户分页列表")
    @GetMapping("/findByNotRelateCurrentUserCustomerPageDto")
    public Result<Page<CustomerPageVo>> findByLevelPositionPageDto(@PageableDefault(50) Pageable pageable,
                                                                   @ApiParam(name = "LevelPositionPageDto", value = "分页Dto") NotRelateCurrentUserCustomerPageDto dto) {
        try {
            return Result.ok(this.customerPageVoService.findByConditions(pageable, dto));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "未关联任何企业用户的客户分页列表")
    @GetMapping("/findByNotRelateAnyUserCustomerPageDto")
    public Result<Page<CustomerPageVo>> findByOrgPositionPageDto(@PageableDefault(50) Pageable pageable,
                                                                 @ApiParam(name = "OrgPositionPageDto", value = "分页Dto") NotRelateAnyUserCustomerPageDto dto) {
        try {
            return Result.ok(this.customerPageVoService.findByConditions(pageable, dto));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "客户地址信息分页列表")
    @GetMapping("/findAddressByConditions")
    public Result<Page<CustomerAddressPageVo>> findAddressByConditions(@PageableDefault(50) Pageable pageable,
                                                                       @ApiParam(name = "CustomerSelectDto", value = "分页Dto") CustomerPageDto dto) {
        try {
            return Result.ok(this.customerPageVoService.findAddressByConditions(pageable, dto));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
