package com.biz.crm.mdm.admin.web.report.customer.dto;

import com.biz.crm.mdm.admin.web.report.customer.constant.CustomerReportConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 未关联当前企业用户的客户分页列表查询条件Dto
 *
 * <AUTHOR>
 * @date 2021/11/10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "未关联当前企业用户的客户分页列表查询条件Dto", description = "未关联当前企业用户的客户分页列表查询条件Dto")
public class NotRelateCurrentUserCustomerPageDto extends AbstractCustomerPageDto {

  /**
   * 用户帐号
   */
  @ApiModelProperty("用户帐号")
  private String userName;
  /**
   * 客户编码
   */
  @ApiModelProperty("客户编码")
  private String customerCode;
  /**
   * 客户名称
   */
  @ApiModelProperty("客户名称")
  private String customerName;
  /**
   * 客户类型
   */
  @ApiModelProperty("客户类型")
  private String customerType;

  /**
   * 锁定状态：009正常，003冻结
   */
  @ApiModelProperty("锁定状态：009正常，003冻结")
  private String lockState;

  /**
   * 分页来源
   */
  @ApiModelProperty(value = "分页来源", hidden = true)
  private String pageSource = CustomerReportConstant.CUSTOMER_PAGE_SOURCE_NOT_RELATE_CURRENT_USER_CUSTOMER_LIST;

}
