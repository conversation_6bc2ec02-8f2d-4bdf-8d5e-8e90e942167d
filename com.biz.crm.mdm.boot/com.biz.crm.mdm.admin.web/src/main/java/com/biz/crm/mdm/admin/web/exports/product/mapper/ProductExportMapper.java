package com.biz.crm.mdm.admin.web.exports.product.mapper;

import com.biz.crm.mdm.admin.web.exports.product.model.ProductExportDto;
import com.biz.crm.mdm.admin.web.exports.product.model.ProductExportVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品导出mapper
 *
 * <AUTHOR>
 * @since 2022-5-24
 */
public interface ProductExportMapper{

  /**
   * 获取匹配的数据总量
   *
   * @param dto
   * @return
   */
  Integer getProductExportTotal(@Param("dto") ProductExportDto dto);

  /**
   * 获取导出数据
   *
   * @param dto
   * @return
   */
  List<ProductExportVo> findProductExportData(@Param("dto") ProductExportDto dto);
}
