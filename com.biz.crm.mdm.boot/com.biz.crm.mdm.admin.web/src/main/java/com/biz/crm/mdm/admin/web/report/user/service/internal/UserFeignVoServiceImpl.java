package com.biz.crm.mdm.admin.web.report.user.service.internal;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.mdm.admin.web.report.user.repository.UserReportRepository;
import com.biz.crm.mdm.admin.web.report.user.vo.UserReportVo;
import com.biz.crm.mdm.business.user.sdk.dto.UserFeignDto;
import com.biz.crm.mdm.business.user.sdk.service.UserFeignVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * userFeign业务实现类
 *
 * <AUTHOR>
 */
@Service
public class UserFeignVoServiceImpl implements UserFeignVoService {

  @Autowired(required = false)
  private UserReportRepository userReportRepository;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public Set<String> findUserNamesByUserDto(UserFeignDto dto) {
    if (Objects.isNull(dto) || StringUtils.isBlank(dto.getUserType())) {
      return new HashSet<>();
    }
    dto.setTenantCode(TenantUtils.getTenantCode());
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    List<UserReportVo> userNamesByUserDto = this.userReportRepository.findByUserFeignDto(dto);
    if (CollectionUtils.isEmpty(userNamesByUserDto)) {
      return new HashSet<>();
    }
    return userNamesByUserDto.stream().map(UserReportVo::getUserName).collect(Collectors.toSet());
  }

  @Override
  public List<UserVo> findByUserNames(List<String> userNames) {
    if (CollectionUtils.isEmpty(userNames)) {
      return new ArrayList<>();
    }
    UserFeignDto userFeignDto = new UserFeignDto();
    userFeignDto.setUserNames(userNames);
    userFeignDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    userFeignDto.setTenantCode(TenantUtils.getTenantCode());
    List<UserReportVo> list = this.userReportRepository.findByUserFeignDto(userFeignDto);
    if (CollectionUtils.isEmpty(list)) {
      return new ArrayList<>();
    }
    return (List<UserVo>) this.nebulaToolkitService.copyCollectionByWhiteList(list, UserReportVo.class, UserVo.class, HashSet.class, ArrayList.class);
  }

  @Override
  public List<UserVo> findByLotUserNames(List<String> userNames) {
    return this.findByUserNames(userNames);
  }

  @Override
  public List<UserVo> findTotalNum(String enableStatus, String ignorePositionLevelCode,String employeeType) {
    return this.userReportRepository.findTotalNum(enableStatus,ignorePositionLevelCode,employeeType);
  }
}
