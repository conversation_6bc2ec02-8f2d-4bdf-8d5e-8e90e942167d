package com.biz.crm.mdm.admin.web.imports.terminal.service;

import com.alibaba.excel.util.StringUtils;
import com.biz.crm.common.ie.sdk.excel.process.ImportProcess;
import com.biz.crm.common.ie.sdk.vo.TaskGlobalParamsVo;
import com.biz.crm.mdm.admin.web.imports.terminal.model.TerminalRelaBusinessImportVo;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.biz.crm.mdm.business.terminal.local.entity.TerminalRelaBusiness;
import com.biz.crm.mdm.business.terminal.local.service.TerminalRelaBusinessService;
import com.biz.crm.mdm.business.terminal.sdk.service.TerminalVoService;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @className: com.biz.crm.mdm.admin.web.imports.terminal.service.TerminalRelaBusinessImportProcess
 * @description: MDM终端信息关联业务员导入
 * @author: xiaopeng.zhang
 * @create: 2024-08-08 9:16
 */
@Component
@Slf4j
public class TerminalRelaBusinessImportProcess implements ImportProcess<TerminalRelaBusinessImportVo> {

    @Autowired(required = false)
    private TerminalVoService terminalVoService;

    @Autowired(required = false)
    private PositionVoService positionVoService;

    @Autowired(required = false)
    private TerminalRelaBusinessService terminalRelaBusinessService;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public Integer getBatchCount() {
        return Integer.MAX_VALUE;
    }

    @Override
    public boolean importBeforeValidationFlag() {
        return true;
    }

    @Override
    public Map<Integer, String> execute(LinkedHashMap<Integer, TerminalRelaBusinessImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return null;
    }

    @Override
    public Map<Integer, String> tryVerify(LinkedHashMap<Integer, TerminalRelaBusinessImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {

        Map<Integer, String> errMap = new HashMap<>();
        Validate.notEmpty(data, "导入数据不能为空！");
        //校验编码统计
        List<String> terminalCodeList = new ArrayList<>();
        List<String> positionCodeList = new ArrayList<>();
        for (TerminalRelaBusinessImportVo vo : data.values()) {
            if (StringUtils.isNotBlank(vo.getTerminalCode())) {
                terminalCodeList.add(vo.getTerminalCode());
            }
            if (StringUtils.isNotBlank(vo.getPositionCode())) {
                positionCodeList.add(vo.getPositionCode());
            }
        }
        //终端数据
        List<TerminalVo> terminalVoList = this.terminalVoService.findDetailsByIdsOrTerminalCodes(null, terminalCodeList);
        Map<String, TerminalVo> terminalVoMap = terminalVoList.stream().collect(Collectors.toMap(item -> item.getTerminalCode(), o -> o, (v1, v2) -> v1));
        //职位数据
        List<PositionVo> positionVoList = this.positionVoService.findByLotPositionCodes(positionCodeList);
        Map<String, PositionVo> positionVoMap = positionVoList.stream().collect(Collectors.toMap(item -> item.getPositionCode(), o -> o, (v1, v2) -> v1));
        //待保存数据集合
        List<TerminalRelaBusiness> list = new ArrayList<>();
        data.forEach((rowNum, vo) -> {
            TerminalRelaBusiness entity = this.nebulaToolkitService.copyObjectByWhiteList(vo, TerminalRelaBusiness.class, HashSet.class, ArrayList.class);
            //基础信息校验
            this.validateIsTrue(StringUtils.isNotBlank(vo.getUserName()), "用户账号，不能为空！");
            this.validateIsTrue(StringUtils.isNotBlank(vo.getFullName()), "用户姓名，不能为空！");
            this.validateIsTrue(StringUtils.isNotBlank(vo.getPositionCode()), "职位编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotBlank(vo.getPositionName()), "职位名称，不能为空！");
            this.validateIsTrue(StringUtils.isNotBlank(vo.getTerminalCode()), "终端编码，不能为空！");
            this.validateIsTrue(StringUtils.isNotBlank(vo.getTerminalName()), "终端名称，不能为空！");
            //终端，职位信息校验
            this.validateIsTrue(terminalVoMap.containsKey(vo.getTerminalCode()), "终端不存在，请检查！");
            this.validateIsTrue(positionVoMap.containsKey(vo.getPositionCode()), "职位不存在，请检查！");
            if (positionVoMap.containsKey(vo.getPositionName())) {
                PositionVo positionVo = positionVoMap.get(vo.getPositionCode());
                this.validateIsTrue(positionVo.getUserName().equals(vo.getUserName()), "用户与职位不一致，请检查！");

            }
            list.add(entity);
        });
        if (errMap.isEmpty()) {
            this.terminalRelaBusinessService.createTerminalRelaBusiness(list);
        }
        return errMap;
    }

    @Override
    public Map<Integer, String> tryConfirm(LinkedHashMap<Integer, TerminalRelaBusinessImportVo> data, TaskGlobalParamsVo paramsVo, Map<String, Object> params) {
        return ImportProcess.super.tryConfirm(data, paramsVo, params);
    }

    @Override
    public Class<TerminalRelaBusinessImportVo> findCrmExcelVoClass() {
        return TerminalRelaBusinessImportVo.class;
    }

    @Override
    public String getTemplateCode() {
        return "MDM_TERMINAL_RELA_BUSINESS_IMPORT";
    }

    @Override
    public String getTemplateName() {
        return "MDM终端信息关联业务员导入";
    }
}
