package com.biz.crm.mdm.admin.web.deprecated.service.impl;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.admin.web.deprecated.mapper.MdmPositionCustomerOrgMapper;
import com.biz.crm.mdm.admin.web.deprecated.service.MdmPositionCustomerOrgService;
import com.biz.crm.business.common.sdk.deprecated.model.PageResult;
import com.biz.crm.mdm.business.customer.org.local.entity.CustomerOrgPosition;
import com.biz.crm.mdm.business.customer.org.sdk.deprecated.dto.MdmPositionCustomerOrgPageReqVo;
import com.biz.crm.mdm.business.customer.org.sdk.deprecated.vo.MdmPositionCustomerOrgRespVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 职位与用户关联表接口实现
 *
 * <AUTHOR>
 * @date 2020-11-03 17:31:28
 */
@Slf4j
@Service
public class MdmPositionCustomerOrgServiceImpl<M extends BaseMapper<T>, T> extends ServiceImpl<MdmPositionCustomerOrgMapper, CustomerOrgPosition> implements MdmPositionCustomerOrgService {

  @Resource
  private MdmPositionCustomerOrgMapper mdmPositionCustomerOrgMapper;

  @Override
  public PageResult<MdmPositionCustomerOrgRespVo> unRelationPage(MdmPositionCustomerOrgPageReqVo mdmPositionCustomerOrgPageReqVo) {
    String customerOrgCode = mdmPositionCustomerOrgPageReqVo.getCustomerOrgCode();
    Validate.isTrue(StringUtils.isNotEmpty(customerOrgCode), "客户组织编码不能为空");
    Page<MdmPositionCustomerOrgRespVo> page = new Page<>(mdmPositionCustomerOrgPageReqVo.getPageNum(), mdmPositionCustomerOrgPageReqVo.getPageSize());
    List<String> positionCodeList = mdmPositionCustomerOrgMapper.findRelationPosition(customerOrgCode, TenantUtils.getTenantCode());
    mdmPositionCustomerOrgPageReqVo.setPositionCodeList(positionCodeList);
    Page<MdmPositionCustomerOrgRespVo> data = mdmPositionCustomerOrgMapper.unRelationPage(page, mdmPositionCustomerOrgPageReqVo, TenantUtils.getTenantCode());
    return PageResult.<MdmPositionCustomerOrgRespVo>builder()
        .data(data.getRecords())
        .count(data.getTotal())
        .build();
  }


  @Override
  public List<MdmPositionCustomerOrgRespVo> relationList(MdmPositionCustomerOrgPageReqVo mdmPositionCustomerOrgPageReqVo) {
    Validate.isTrue(StringUtils.isNotEmpty(mdmPositionCustomerOrgPageReqVo.getCustomerOrgCode()), "客户编码不能为空");
    return mdmPositionCustomerOrgMapper.relationList(mdmPositionCustomerOrgPageReqVo, TenantUtils.getTenantCode());
  }

}
