package com.biz.crm.mdm.admin.web.observer;

import com.biz.crm.common.log.sdk.dto.CrmBusinessLogDto;
import com.biz.crm.common.log.sdk.enums.OperationTypeEunm;
import com.biz.crm.common.log.sdk.service.CrmBusinessLogVoService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.mars.sdk.event.MarsAuthorityEventListener;
import com.bizunited.nebula.mars.sdk.vo.MarsAuthority;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 数据权限事件操作日志监听实现
 * @date 2023/10/19
 */
@Component
@Slf4j
public class MarsAuthorityLogEventListener implements MarsAuthorityEventListener {

  @Autowired(required = false)
  private CrmBusinessLogVoService crmBusinessLogVoService;

  @Override
  public void onCreated(MarsAuthority marsAuthority) {
    try {
      CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
      crmBusinessLogDto.setOperationType(OperationTypeEunm.CREATE.getDictCode());
      crmBusinessLogDto.setOnlyKey(marsAuthority.getId());
      crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
      crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
      crmBusinessLogDto.setNewObject(marsAuthority);
      crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    } catch (Exception e) {
      log.error("数据权限创建操作日志保存失败", e);
    }
  }

  @Override
  public void onUpdate(MarsAuthority oldMarsAuthority, MarsAuthority newMarsAuthority) {
    try {
      CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
      crmBusinessLogDto.setOperationType(OperationTypeEunm.UPDATE.getDictCode());
      crmBusinessLogDto.setOnlyKey(newMarsAuthority.getId());
      crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
      crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
      crmBusinessLogDto.setOldObject(oldMarsAuthority);
      crmBusinessLogDto.setNewObject(newMarsAuthority);
      crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    } catch (Exception e) {
      log.error("数据权限更新操作日志保存失败", e);
    }
  }

  @Override
  public void onDeleted(MarsAuthority marsAuthority) {
    try {
      CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
      crmBusinessLogDto.setOperationType(OperationTypeEunm.DELETE.getDictCode());
      crmBusinessLogDto.setOnlyKey(marsAuthority.getId());
      crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
      crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
      crmBusinessLogDto.setOldObject(marsAuthority);
      crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    } catch (Exception e) {
      log.error("数据权限删除操作日志保存失败", e);
    }
  }

  @Override
  public void onEffective(MarsAuthority oldMarsAuthority, MarsAuthority newMarsAuthority) {
    try {
      CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
      crmBusinessLogDto.setOperationType(OperationTypeEunm.ENABLE.getDictCode());
      crmBusinessLogDto.setOnlyKey(newMarsAuthority.getId());
      crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
      crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
      crmBusinessLogDto.setOldObject(oldMarsAuthority);
      crmBusinessLogDto.setNewObject(newMarsAuthority);
      crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    } catch (Exception e) {
      log.error("数据权限更新操作日志保存失败", e);
    }
  }

  @Override
  public void onInvalid(MarsAuthority oldMarsAuthority, MarsAuthority newMarsAuthority) {
    try {
      CrmBusinessLogDto crmBusinessLogDto = new CrmBusinessLogDto();
      crmBusinessLogDto.setOperationType(OperationTypeEunm.DISABLE.getDictCode());
      crmBusinessLogDto.setOnlyKey(newMarsAuthority.getId());
      crmBusinessLogDto.setAppCode(TenantUtils.getTenantCode());
      crmBusinessLogDto.setTenantCode(TenantUtils.getTenantCode());
      crmBusinessLogDto.setOldObject(oldMarsAuthority);
      crmBusinessLogDto.setNewObject(newMarsAuthority);
      crmBusinessLogVoService.handleSave(crmBusinessLogDto);
    } catch (Exception e) {
      log.error("数据权限更新操作日志保存失败", e);
    }
  }
}
