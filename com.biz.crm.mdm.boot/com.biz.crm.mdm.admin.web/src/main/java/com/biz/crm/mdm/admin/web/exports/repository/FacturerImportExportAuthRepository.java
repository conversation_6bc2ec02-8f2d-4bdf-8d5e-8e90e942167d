package com.biz.crm.mdm.admin.web.exports.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.admin.web.exports.entity.FacturerImportExportAuthEntity;
import com.biz.crm.mdm.admin.web.exports.mapper.FacturerImportExportAuthMapper;

import org.springframework.stereotype.Component;

/**
 * TODO 注释未写
 */
@Component
public class FacturerImportExportAuthRepository extends ServiceImpl<FacturerImportExportAuthMapper, FacturerImportExportAuthEntity> {
  /**
   * 通过导入导出任务的业务编号进行查询
   */
  public FacturerImportExportAuthEntity findByTaskCodeAndAppCodeAndApplicationName(String taskCode , String appCode , String applicationName) {
    return this.lambdaQuery()
        .eq(FacturerImportExportAuthEntity::getTaskCode, taskCode)
        .eq(FacturerImportExportAuthEntity::getTenantCode, appCode)
        .eq(FacturerImportExportAuthEntity::getApplicationName, applicationName)
        .one();
  }
}

