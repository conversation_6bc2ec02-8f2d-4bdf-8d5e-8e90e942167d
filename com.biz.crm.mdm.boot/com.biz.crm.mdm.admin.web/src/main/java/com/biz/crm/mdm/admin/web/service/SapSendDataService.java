package com.biz.crm.mdm.admin.web.service;

import com.alibaba.fastjson.JSONObject;

/**
 * SAP推送数据
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/23 22:59
 */
public interface SapSendDataService {


    /**
     * SAP推送数据
     *
     * @param json
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/23 23:01
     */
    void sapSendCustomerSupplierDeliveryInfo(JSONObject json);

    /**
     * SAP推送的价格数据
     *
     * @param json
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/2 22:32
     */
    void sapSendPriceInfo(JSONObject json);

    /**
     * SAP推送的奶卡成本价
     *
     * @param json
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/2 22:32
     */
    void sapSendMilkCostPriceInfo(JSONObject json);
}