package com.biz.crm.mdm.admin.web.report.position.dto;

import com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 用户父级职位下拉框查询条件Dto
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "用户父级职位下拉框查询条件Dto", description = "用户父级职位下拉框查询条件Dto")
public class UserSelectParentPositionPageDto extends AbstractPositionPageDto {

  /**
   * 组织名称/职位名称/用户名称
   */
  @ApiModelProperty("组织名称/职位名称/用户名称")
  private String unionName;

  /**
   * 回显编码集合，字符串数组，一般用于多选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据
   */
  @ApiModelProperty("回显编码集合，字符串数组，一般用于多选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据")
  private List<String> selectedCodes;

  @ApiModelProperty("回显编码集合，字符串数组，一般用于多选，如果编码对应的数据满足筛选条件，则返回结果集合一定包含这些数据")
  private String selectedCode;

  /**
   * 用户账号(排除这个用户的职位)
   */
  @ApiModelProperty("用户账号(排除这个用户的职位)")
  private String excludeUserName;

  /**
   * 下级职位组织编码(不含这个组织(不含自身)的全部下级组织的职位)
   */
  @ApiModelProperty("下级职位组织编码(不含这个组织(不含自身)的全部下级组织的职位)")
  private String childPositionOrgCode;

  /**
   * 下级职位编码(不含这个职位以及全部下级职位)
   */
  @ApiModelProperty("下级职位编码(不含这个职位以及全部下级职位)")
  private String childPositionCode;

  /**
   * 生效状态(003:停用，009:启用)
   */
  @ApiModelProperty("生效状态(003:停用，009:启用)")
  private String enableStatus;

  /**
   * 分页来源
   */
  @ApiModelProperty(value = "分页来源",hidden = true)
  private String pageSource = PositionReportConstant.POSITION_PAGE_SOURCE_USER_SELECT_PARENT_POSITION_LIST;
}
