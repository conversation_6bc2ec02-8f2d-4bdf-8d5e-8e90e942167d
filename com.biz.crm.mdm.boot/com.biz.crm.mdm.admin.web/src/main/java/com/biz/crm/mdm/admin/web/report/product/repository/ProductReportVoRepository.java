package com.biz.crm.mdm.admin.web.report.product.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.mdm.admin.web.report.product.dto.ProductReportPaginationDto;
import com.biz.crm.mdm.admin.web.report.product.mapper.ProductReportVoMapper;
import com.biz.crm.mdm.admin.web.report.product.vo.ProductReportVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/11/4
 */
@Component
public class ProductReportVoRepository {

  @Autowired(required = false)
  private ProductReportVoMapper productReportVoMapper;

  public Page<ProductReportVo> findByConditions(Page<ProductReportVo> page,ProductReportPaginationDto dto) {
    if (StringUtils.isEmpty(dto.getDelFlag())) {
      dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    }
    if (StringUtils.isEmpty(dto.getTenantCode())) {
      dto.setTenantCode(TenantUtils.getTenantCode());
    }
    return productReportVoMapper.findByConditions(page, dto);
  }
}
