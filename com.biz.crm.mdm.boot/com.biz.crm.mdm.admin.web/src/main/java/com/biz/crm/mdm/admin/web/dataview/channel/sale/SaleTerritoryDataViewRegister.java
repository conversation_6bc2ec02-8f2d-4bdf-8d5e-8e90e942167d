package com.biz.crm.mdm.admin.web.dataview.channel.sale;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @title SaleTerritoryDataViewRegister
 * @date 2023/3/3 12:58
 * @description 销售区域数据视图
 */
@Component
public class SaleTerritoryDataViewRegister implements DataviewRegister {

  @Override
  public String code() {
    return "mdm_sale_territory_dataview";
  }

  @Override
  public String desc() {
    return "MDM销售区域数据视图";
  }

  @Override
  public String buildSql() {
    return "select  "
        + "  a.*,  "
        + "  b.sale_territory_name as parent_name  "
        + "from  "
        + "  mdm_sales_territory a  "
        + "left join mdm_sales_territory b on  "
        + "  a.parent_code = b.sale_territory_code  "
        + "  and b.tenant_code = a.tenant_code  "
        + "where  "
        + "  1 = 1  "
        + "  and a.del_flag = '" + DelFlagStatusEnum.NORMAL.getCode() + "'  "
        + "  and a.tenant_code = :tenantCode  "
        + "  and a.rule_code like concat(:ruleCode,'%')";
  }
}
