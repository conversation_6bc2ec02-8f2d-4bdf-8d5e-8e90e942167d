package com.biz.crm.mdm.admin.web.exports.position.service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import com.alibaba.fastjson.JSONArray;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.mdm.admin.web.exports.position.mapper.PositionExportMapper;
import com.biz.crm.mdm.admin.web.exports.position.model.PositionExportDto;
import com.biz.crm.mdm.admin.web.exports.position.model.PositionExportVo;
import com.biz.crm.mdm.business.position.sdk.service.PositionVoService;
import com.biz.crm.mdm.business.position.sdk.vo.PositionVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.rbac.sdk.service.RoleVoCacheService;
import com.bizunited.nebula.rbac.sdk.vo.RoleVo;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

/**
 * 岗位导出
 *
 * <AUTHOR>
 * @date 2022/05/23
 */
@Component
public class PositionExportProcess implements ExportProcess<PositionExportVo> {

  @Autowired(required = false)
  private PositionExportMapper positionExportMapper;

  @Override
  public Integer getTotal(Map<String, Object> params) {
    params = this.convertEuropaParam(params);
    String tenantCode = TenantUtils.getTenantCode();
    PositionExportDto dto = this.getPositionExportDto(params);
    return positionExportMapper.getTotal(dto);
  }

  /**
   * 得到dto
   *
   * @param params 参数个数
   * @return {@link PositionExportDto}
   */
  private PositionExportDto getPositionExportDto(Map<String, Object> params) {
    params = this.convertEuropaParam(params);
    
    Object positionCode = params.get("positionCode");
    Object positionName = params.get("positionName");
    Object enableStatus = params.get("enableStatus");
    Object positionLevelCode = params.get("positionLevelCode");
    Object positionLevelName = params.get("positionLevelName");
    Object primaryFlag = params.get("primaryFlag");
    Object orgCode = params.get("orgCode");
    Object orgName = params.get("orgName");
    Object parentOrgName = params.get("parentOrgName");
    Object fullName = params.get("fullName");
    Object userName = params.get("userName");
    Object allUnderOrgCode = params.get("allUnderOrgCode");
    String tenantCode = TenantUtils.getTenantCode();
    PositionExportDto dto = new PositionExportDto();
    dto.setTenantCode(tenantCode);
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    dto.setPositionCode(ObjectUtils.isEmpty(positionCode) ? null : positionCode.toString());
    dto.setPositionName(ObjectUtils.isEmpty(positionName) ? null : positionName.toString());
    dto.setEnableStatus(ObjectUtils.isEmpty(enableStatus) ? null : enableStatus.toString());
    dto.setPositionLevelCode(ObjectUtils.isEmpty(positionLevelCode) ? null : positionLevelCode.toString());
    dto.setPositionLevelName(ObjectUtils.isEmpty(positionLevelName) ? null : positionLevelName.toString());
    dto.setPrimaryFlag(ObjectUtils.isEmpty(primaryFlag) ? null : Boolean.valueOf(primaryFlag.toString()));
    dto.setOrgCode(ObjectUtils.isEmpty(orgCode) ? null : orgCode.toString());
    dto.setOrgName(ObjectUtils.isEmpty(orgName) ? null : orgName.toString());
    dto.setParentOrgName(ObjectUtils.isEmpty(parentOrgName) ? null : parentOrgName.toString());
    dto.setFullName(ObjectUtils.isEmpty(fullName) ? null : fullName.toString());
    dto.setUserName(ObjectUtils.isEmpty(userName) ? null : userName.toString());
    dto.setAllUnderOrgCode(ObjectUtils.isEmpty(allUnderOrgCode) ? null : allUnderOrgCode.toString());
    return dto;
  }

  @Override
  @Transactional /* 必须加上事务，否则导出的数据视图将会失效 */
  public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
    params = this.convertEuropaParam(params);
    int offset = this.getPageSize() * vo.getPageNo();
    Integer limit = vo.getPageSize();
    PositionExportDto dto = this.getPositionExportDto(params);
    dto.setOffset(offset);
    dto.setLimit(limit);
    List<PositionExportVo> list = positionExportMapper.getData(dto);
    /**
     * 编码转汉字
     */
    for (PositionExportVo exportVo : list) {
      String enableStatus = exportVo.getEnableStatus();
      String userEnableStatus = exportVo.getUserEnableStatus();
      exportVo.setEnable(EnableStatusEnum.getDesc(enableStatus));
      exportVo.setUserEnable(EnableStatusEnum.getDesc(userEnableStatus));
    }
    this.buildRoleInfo(list);
    return toJSONArray(list);
  }

  @Autowired(required = false)
  private PositionVoService positionVoService;
  @Autowired(required = false)
  private RoleVoCacheService roleVoCacheService;

  /**
   * 把角色名称拼成一串汉字。
   * 在这里抄的：
   * com.biz.crm.mdm.admin.web.report.position.strategy.helper.PositionReportStrategyHelper#buildRoleInfo(java.util.List)
   *
   * @param list 列表
   */
  private void buildRoleInfo(List<PositionExportVo> list) {
    List<String> positionCodes = list.stream().map(PositionExportVo::getPositionCode).collect(Collectors.toList());
    List<PositionVo> positionVos = this.positionVoService.findByIdsOrCodes(null, positionCodes);
    Set<String> roleCodes = Sets.newHashSet();
    positionVos.forEach(positionVo -> {
      if (!CollectionUtils.isEmpty(positionVo.getRoleList())) {
        roleCodes.addAll(positionVo.getRoleList());
      }
    });
    if (CollectionUtils.isEmpty(roleCodes)) {
      return;
    }
    Set<RoleVo> roleVoSet = roleVoCacheService.findByTenantCodeAndRoleCodes(TenantUtils.getTenantCode(), roleCodes);
    if (CollectionUtils.isEmpty(roleCodes)) {
      return;
    }
    Map<String, String> roleNameMap = roleVoSet.stream().collect(Collectors.toMap(RoleVo::getRoleCode, RoleVo::getRoleName, (key1, key2) -> key1));
    Map<String, Set<String>> positionRoleNameMap = Maps.newHashMap();
    positionVos.forEach(positionVo -> {
      if (CollectionUtils.isEmpty(positionVo.getRoleList())) {
        return;
      }
      Set<String> roleNames = positionVo.getRoleList().stream().map(roleNameMap::get)
          .filter(Objects::nonNull).collect(Collectors.toSet());
      positionRoleNameMap.put(positionVo.getPositionCode(), roleNames);
    });
    list.forEach(positionPageVo -> {
      Set<String> roleNames = positionRoleNameMap.get(positionPageVo.getPositionCode());
      positionPageVo.setRoleNameList(CollectionUtils.isEmpty(roleNames) ? null : String.join(",", roleNames));
    });
  }

  @Override
  public Class<PositionExportVo> findCrmExcelVoClass() {
    return PositionExportVo.class;
  }

  @Override
  public String getBusinessCode() {
    return "MDM_POSITION_EXPORT";
  }

  @Override
  public String getBusinessName() {
    return "职位导出";
  }
}
