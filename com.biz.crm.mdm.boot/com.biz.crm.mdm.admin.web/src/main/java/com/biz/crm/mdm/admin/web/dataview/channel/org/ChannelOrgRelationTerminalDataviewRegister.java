package com.biz.crm.mdm.admin.web.dataview.channel.org;

import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * MDM渠道组织关联终端数据视图
 * <AUTHOR>
 */
@Component
public class ChannelOrgRelationTerminalDataviewRegister implements DataviewRegister {
  @Override
  public String code() {
    return "mdm_channel_org_relation_terminal_dataview";
  }

  @Override
  public String desc() {
    return "MDM渠道组织关联终端数据视图";
  }

  @Override
  public String buildSql() {
    return "SELECT \n" +
            "co.channel_org_code,\n" +
            "co.channel_org_name,\n" +
            "co.channel_org_type,\n" +
            "co.channel_org_level,\n" +
            "pco.channel_org_code parent_channel_org_code,\n" +
            "pco.channel_org_name parent_channel_org_name,\n" +
            "mt.terminal_code,\n" +
            "mt.terminal_name,\n" +
            "mt.terminal_type,\n" +
            "mt.terminal_address,\n" +
            "mt.province_name,\n" +
            "mt.city_name,\n" +
            "mt.district_name,\n" +
            "cort.create_name,\n" +
            "cort.create_time,\n" +
            "cort.id\n" +
            "FROM mdm_channel_org_relation_terminal cort\n" +
            "LEFT JOIN mdm_channel_org co ON co.channel_org_code = cort.channel_org_code\n" +
            "LEFT JOIN mdm_channel_org pco ON pco.channel_org_code = co.parent_code\n" +
            "LEFT JOIN mdm_terminal mt ON mt.terminal_code = cort.terminal_code\n" +
            "WHERE cort.tenant_code = :tenantCode " +
            " AND cort.channel_org_rule_code like concat (:ruleCode , '%') ";
  }
}
