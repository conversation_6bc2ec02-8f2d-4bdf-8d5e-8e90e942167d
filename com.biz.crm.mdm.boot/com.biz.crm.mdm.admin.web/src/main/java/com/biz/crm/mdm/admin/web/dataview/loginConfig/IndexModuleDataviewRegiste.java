package com.biz.crm.mdm.admin.web.dataview.loginConfig;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-06-19 17:01
 * @description：
 */
@Component
public class IndexModuleDataviewRegiste implements DataviewRegister {
  @Override
  public String code() {
    return "mdm_index_module_dataview";
  }

  @Override
  public String desc() {
    return "MDM首页组件数据视图";
  }

  @Override
  public String buildSql() {
    return "SELECT " +
        " a.id, " +
        " a.module_code, " +
        " a.module_name, " +
        " a.default_module, " +
        " a.module_type_name, " +
        " a.module_menu_code, " +
        " a.enable_status, " +
        " a.tenant_code, " +
        " a.del_flag, " +
        " a.create_time, " +
        " a.create_account, " +
        " a.create_name, " +
        " a.modify_time, " +
        " a.modify_account, " +
        " a.modify_name  " +
        " FROM mdm_index_module a " +
        " WHERE a.tenant_code = :tenantCode " +
        " AND a.del_flag = '" + DelFlagStatusEnum.NORMAL.getCode() + "' ";
  }
}
