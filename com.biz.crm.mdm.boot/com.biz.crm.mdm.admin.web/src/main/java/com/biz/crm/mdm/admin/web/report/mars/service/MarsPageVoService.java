package com.biz.crm.mdm.admin.web.report.mars.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.mars.dto.MarsPageDto;
import com.biz.crm.mdm.admin.web.report.mars.vo.MarsPageVo;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @description 数据视图分页服务接口类
 * @date 2023/11/21
 */
public interface MarsPageVoService {

  /**
   * 数据权限分页列表
   *
   * @param dto      请求参数dto
   * @param pageable 分页信息
   * @return Page<MarsPageVo> 数据权限分页列表
   */
  Page<MarsPageVo> findByMarsPageDto(Pageable pageable, MarsPageDto dto);
}
