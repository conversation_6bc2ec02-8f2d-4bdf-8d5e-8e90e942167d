<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.admin.web.report.position.mapper.PositionCombineVoMapper">
    <sql id="table_sql">
        mdm_position p
        left join mdm_user_position up on p.tenant_code = up.tenant_code and p.position_code = up.position_code
        left join mdm_org_position op on op.tenant_code = p.tenant_code and op.position_code = p.position_code
        left join mdm_position_role pr on pr.tenant_code = p.tenant_code and pr.position_code = p.position_code
        <if test="dto.parentOrgCodes !=null and dto.parentOrgCodes.size > 0">
            left join mdm_org_position pop on pop.tenant_code = p.tenant_code and pop.position_code = p.parent_code
        </if>
        <if test="dto.orgRuleCode !=null and dto.orgRuleCode != '' ">
            left join mdm_org mo on mo.tenant_code = p.tenant_code and mo.org_code = op.org_code
        </if>
    </sql>
    <sql id="where_sql">
        p.tenant_code = #{dto.tenantCode}
        and p.del_flag = #{dto.delFlag}
        <if test="dto.createBeginTime != null">
            and p.create_time <![CDATA[ >= ]]> #{dto.createBeginTime}
        </if>
        <if test="dto.createEndTime != null">
            and p.create_time <![CDATA[ <= ]]> #{dto.createEndTime}
        </if>
        <if test="dto.enableStatus !=null and dto.enableStatus != '' ">
            and p.enable_status = #{dto.enableStatus}
        </if>
        <if test="dto.positionRuleCode !=null and dto.positionRuleCode != '' ">
            <bind name="positionRuleCodeLike" value="dto.positionRuleCode + '%'"/>
            and p.rule_code = #{positionRuleCodeLike}
        </if>
        <if test="dto.levelNum !=null and dto.levelNum != '' ">
            and p.level_num = #{dto.levelNum}
        </if>
        <if test="dto.positionCode !=null and dto.positionCode != '' ">
            and p.position_code = #{dto.positionCode}
        </if>
        <if test="dto.positionName !=null and dto.positionName != '' ">
            <bind name="positionNameLike" value="'%' + dto.positionName + '%'"/>
            and p.position_name like #{positionNameLike}
        </if>
        <if test="dto.orgRuleCode !=null and dto.orgRuleCode != '' ">
            <bind name="orgRuleCodeLike" value=" dto.orgRuleCode + '%'"/>
            and mo.rule_code like #{orgRuleCodeLike}
        </if>
        <if test="dto.primaryFlag != null">
            and up.primary_flag = #{dto.primaryFlag}
        </if>
        <if test="dto.parentCode !=null and dto.parentCode != '' ">
            and p.parent_code = #{dto.parentCode}
        </if>
        <if test="dto.parentName !=null and dto.parentName != '' ">
            <bind name="parentNameLike" value="'%' + dto.parentName + '%'"/>
            AND EXISTS (
            SELECT
            1
            FROM
            mdm_position mp
            WHERE
            mp.tenant_code = p.tenant_code
            AND mp.position_code = p.parent_code
            AND mp.position_name LIKE #{parentNameLike}
            )
        </if>
        <if test="dto.orgCodes !=null and dto.orgCodes.size > 0">
            and op.org_code in
            <foreach collection="dto.orgCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="dto.parentOrgCodes !=null and dto.parentOrgCodes.size > 0">
            and pop.org_code in
            <foreach collection="dto.parentOrgCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="dto.positionLevelCodes !=null and dto.positionLevelCodes.size > 0">
            and p.position_level_code in
            <foreach collection="dto.positionLevelCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="dto.userNames !=null and dto.userNames.size > 0">
            and up.user_name in
            <foreach collection="dto.userNames" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="dto.roleCode !=null and dto.roleCode != '' ">
            and pr.role_code = #{dto.roleCode}
        </if>
    </sql>
    <select id="page_total_count" resultType="java.lang.Long">
        select
        count(distinct p.id)
        from
        <include refid="table_sql"/>
        where
        <include refid="where_sql"/>
    </select>
    <select id="findByConditions"
            resultType="com.biz.crm.mdm.admin.web.report.position.vo.PositionCombineVo">
        select
        distinct
        p.*,
        up.user_name,
        up.primary_flag
        from
        <include refid="table_sql"/>
        where
        <include refid="where_sql"/>
        order by p.position_code desc,p.id desc
    </select>
</mapper>