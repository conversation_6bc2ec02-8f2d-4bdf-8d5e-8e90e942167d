package com.biz.crm.mdm.admin.web.report.position.strategy.impl;

import com.biz.crm.mdm.admin.web.report.position.constant.PositionReportConstant;
import com.biz.crm.mdm.admin.web.report.position.dto.NotRelateAnyRolePositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.dto.NotRelateCurrentRolePositionPageDto;
import com.biz.crm.mdm.admin.web.report.position.strategy.PositionReportStrategy;
import com.biz.crm.mdm.admin.web.report.position.strategy.helper.PositionReportStrategyHelper;
import com.biz.crm.mdm.admin.web.report.position.vo.PositionPageVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 未关联任何角色的职位分页列表报表数据策略实现
 *
 * <AUTHOR>
 * @date 2022/3/29
 */
@Component
public class NotRelateAnyRolePositionReportStrategyImpl implements PositionReportStrategy<NotRelateAnyRolePositionPageDto> {

  @Autowired(required = false)
  private PositionReportStrategyHelper positionReportStrategyHelper;

  @Override
  public String getPageSource() {
    return PositionReportConstant.POSITION_PAGE_SOURCE_NOT_RELATE_ANY_ROLE_POSITION_LIST;
  }

  @Override
  public Boolean onRequest(NotRelateAnyRolePositionPageDto dto) {
    return true;
  }

  @Override
  public void onReturn(List<PositionPageVo> voList) {
    this.positionReportStrategyHelper.buildUnionName(voList);
    this.positionReportStrategyHelper.buildUserInfo(voList);
    this.positionReportStrategyHelper.buildOrgInfo(voList);
  }
}
