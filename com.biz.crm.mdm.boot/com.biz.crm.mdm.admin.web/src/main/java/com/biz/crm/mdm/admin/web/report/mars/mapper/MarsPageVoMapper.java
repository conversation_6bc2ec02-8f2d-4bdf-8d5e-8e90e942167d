package com.biz.crm.mdm.admin.web.report.mars.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.mars.dto.MarsPageDto;
import com.biz.crm.mdm.admin.web.report.mars.vo.MarsPageVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 数据视图分页Mapper
 * @date 2023/11/21
 */
public interface MarsPageVoMapper {

  /**
   * 数据权限分页列表
   *
   * @param dto  请求参数dto
   * @param page 分页信息
   * @return 数据权限分页列表
   */
  Page<MarsPageVo> findByMarsPageDto(@Param("page") Page<MarsPageVo> page, @Param("dto") MarsPageDto dto);
}
