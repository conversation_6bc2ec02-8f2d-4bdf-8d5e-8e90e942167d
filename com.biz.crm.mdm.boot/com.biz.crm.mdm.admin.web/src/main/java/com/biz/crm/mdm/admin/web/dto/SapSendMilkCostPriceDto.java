package com.biz.crm.mdm.admin.web.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * SAP推送的数据
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/23 22:48
 */
@Data
public class SapSendMilkCostPriceDto implements Serializable {


    private static final long serialVersionUID = -1391725746550308233L;

    @ApiModelProperty("公司编码")
    private String MANDT;

    @ApiModelProperty("年")
    private String GJAHR;

    @ApiModelProperty("月")
    private String MONAT;

    @ApiModelProperty("物料编码")
    private String MATNR;

    @ApiModelProperty("物料名称")
    private String MAKTX;

    @ApiModelProperty("成本价")
    private BigDecimal KOSTN;

    @ApiModelProperty("税率")
    private BigDecimal TAX;

    @ApiModelProperty("奶卡提数")
    private BigDecimal ZTS;

    @ApiModelProperty("启禁状态;空表示启用，X表示删除")
    private String ZSTATU;

}
