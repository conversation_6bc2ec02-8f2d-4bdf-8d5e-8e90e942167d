<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.mdm.admin.web.exports.test.mapper.TestDataMapper">

  <select id="getExportTotal" resultType="java.lang.Integer">
    select count(*)
    from
    ie_test_product a
    left join ie_test_company_product b on a.product_code = b.product_code
    left join ie_test_company c on b.company_code = c.company_code
    left join ie_test_brand d on a.brand_code = d.brand_code
    where 1=1
    <if test="dto.productCode!=null and dto.productCode!=''">
      and a.product_code like concat('%',#{dto.productCode},'%')
    </if>
    order by a.product_code
  </select>

  <select id="findData"
    resultType="com.biz.crm.mdm.admin.web.exports.test.model.TestDataVo">
    select a.id,
    a.brand_code,
    a.product_code,
    a.product_level_code,
    a.product_name,
    a.spec,
    a.unite,
    c.company_code,
    c.company_name,
    c.phone,
    c.user_name,
    d.brand_name
    from
    ie_test_product a
    left join ie_test_company_product b on a.product_code = b.product_code
    left join ie_test_company c on b.company_code = c.company_code
    left join ie_test_brand d on a.brand_code = d.brand_code
    where 1=1
    <if test="dto.productCode!=null and dto.productCode!=''">
      and a.product_code like concat('%',#{dto.productCode},'%')
    </if>
    order by a.product_code
    limit #{dto.offset},#{dto.limit}
  </select>
</mapper>