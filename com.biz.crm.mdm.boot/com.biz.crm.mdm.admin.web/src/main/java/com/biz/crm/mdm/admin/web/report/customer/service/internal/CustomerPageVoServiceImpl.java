package com.biz.crm.mdm.admin.web.report.customer.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.admin.web.report.customer.dto.AbstractCustomerPageDto;
import com.biz.crm.mdm.admin.web.report.customer.dto.CustomerPageDto;
import com.biz.crm.mdm.admin.web.report.customer.repository.CustomerReportRepository;
import com.biz.crm.mdm.admin.web.report.customer.service.CustomerPageVoService;
import com.biz.crm.mdm.admin.web.report.customer.strategy.CustomerReportStrategy;
import com.biz.crm.mdm.admin.web.report.customer.vo.CustomerAddressPageVo;
import com.biz.crm.mdm.admin.web.report.customer.vo.CustomerPageVo;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerTagVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerTagVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 客户分页VO接口实现类
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Service
@Slf4j
public class CustomerPageVoServiceImpl implements CustomerPageVoService {

  @Autowired(required = false)
  private CustomerReportRepository customerReportRepository;

  @Autowired(required = false)
  private CustomerTagVoService customerTagVoService;

  private Map<String, CustomerReportStrategy> strategyMap;
  /**
   * 基于数据库执行的数据视图执行内容缓存（最多500毫秒）
   */
  private static volatile Cache<String, Page<CustomerPageVo>> cache = null;

  public CustomerPageVoServiceImpl(List<CustomerReportStrategy> itemStrategies) {
    if(cache == null) {
      synchronized (CustomerPageVoServiceImpl.class) {
        while(cache == null) {
          cache = CacheBuilder.newBuilder()
                  .initialCapacity(10000)
                  .expireAfterWrite(500, TimeUnit.MILLISECONDS)
                  .maximumSize(100000)
                  .build();
        }
      }
    }
    strategyMap = Maps.newHashMap();
    if (org.springframework.util.CollectionUtils.isEmpty(itemStrategies)) {
      return;
    }
    itemStrategies.forEach(strategy -> {
      CustomerReportStrategy itemStrategy = strategyMap.get(strategy.getPageSource());
      Validate.isTrue(Objects.isNull(itemStrategy), String.format("存在相同的报表数据策略[%s]", strategy.getPageSource()));
      strategyMap.put(strategy.getPageSource(), strategy);
    });
  }

  @Override
  public Page<CustomerPageVo> findByConditions(Pageable pageable, AbstractCustomerPageDto dto) {
    CustomerReportStrategy strategy = strategyMap.get(dto.getPageSource());
    //1.策略不存在获取请求参数封装结果不通过,直接返回null
    if (Objects.isNull(strategy)) {
      log.info("客户信息报表策略不存在....");
      return null;
    }
    if (!strategy.onRequest(dto)) {
      log.info("客户信息报表参数封装校验不通过....");
      return null;
    }
    pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
    dto.setTenantCode(TenantUtils.getTenantCode());
    String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), dto.getPageSource(), pageable.getPageNumber(), pageable.getPageSize());
    Page<CustomerPageVo> resultPage = cache.getIfPresent(cacheKey);
    if (resultPage == null) {
      resultPage = this.customerReportRepository.findByConditions(pageable, dto);
      cache.put(cacheKey, resultPage);
    }
    //2.获取报表数据
    List<CustomerPageVo> records = resultPage.getRecords();
    if (CollectionUtils.isEmpty(records)) {
      new Page<>();
    }
    if (org.springframework.util.CollectionUtils.isEmpty(resultPage.getRecords())) {
      return resultPage;
    }
    //3.数据返回封装
    strategy.onReturn(resultPage.getRecords());
    resultPage.setRecords(records);
    return resultPage;
  }

  @Override
  public Page<CustomerAddressPageVo> findAddressByConditions(Pageable pageable, CustomerPageDto dto) {
    pageable = ObjectUtils.defaultIfNull(pageable, PageRequest.of(1, 50));
    dto.setTenantCode(TenantUtils.getTenantCode());
    return customerReportRepository.findAddressByConditions(pageable,dto);
  }

  /**
   * 构建标签数据
   *
   * @param records
   */
}
