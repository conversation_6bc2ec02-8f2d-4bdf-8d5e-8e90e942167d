package com.biz.crm.mdm.admin.web.dataview.terminal;

import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.bizunited.nebula.europa.database.register.sdk.service.DataviewRegister;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 终端用户数据视图
 */
@Component
public class TerminalUserDataviewRegister implements DataviewRegister {
  @Override
  public String code() {
    return "mdm_terminalUser_dataview";
  }

  @Override
  public String desc() {
    return "MDM终端用户数据视图";
  }

  @Override
  public String buildSql() {
    return "select t.* " +
            "    from mdm_terminal_user t " +
            "    where t.del_flag = '" + DelFlagStatusEnum.NORMAL.getCode() + "'"+
            "    and t.tenant_code = :tenantCode " ;
  }
}
