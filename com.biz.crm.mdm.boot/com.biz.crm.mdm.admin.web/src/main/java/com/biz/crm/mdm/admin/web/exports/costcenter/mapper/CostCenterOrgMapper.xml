<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.mdm.admin.web.exports.costcenter.mapper.CostCenterOrgMapper">

    <select id="findTotal" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        mdm_cost_center a
        LEFT JOIN mdm_cost_center_org b ON a.cost_center_code = b.cost_center_code
        <where>
            a.del_flag = '009'
            <if test="dto.enableStatus != null and dto.enableStatus != ''">
                and a.enable_status = #{dto.enableStatus}
            </if>
            <if test="dto.costCenterCode != null and dto.costCenterCode != ''">
                <bind name="costCenterCode" value="'%' + dto.costCenterCode + '%'"/>
                and a.cost_center_code like #{costCenterCode}
            </if>
            <if test="dto.costCenterName != null and dto.costCenterName != ''">
                <bind name="costCenterName" value="'%' + dto.costCenterName + '%'"/>
                and a.cost_center_name like #{costCenterName}
            </if>
            <if test="dto.companyCode != null and dto.companyCode != ''">
                <bind name="companyCode" value="'%' + dto.companyCode + '%'"/>
                and a.company_code like #{companyCode}
            </if>
        </where>
    </select>

    <select id="findListByCondition" resultType="com.biz.crm.mdm.admin.web.exports.costcenter.model.CostCenterOrgExportVo">
        SELECT
        a.cost_center_code,a.cost_center_name,a.company_code,b.org_code,c.org_name
        FROM
        mdm_cost_center a
        LEFT JOIN mdm_cost_center_org b ON a.cost_center_code = b.cost_center_code
        left join mdm_org c on b.org_code = c.org_code
        <where>
            a.del_flag = '009'
            <if test="dto.enableStatus != null and dto.enableStatus != ''">
                and a.enable_status = #{dto.enableStatus}
            </if>
            <if test="dto.costCenterCode != null and dto.costCenterCode != ''">
                <bind name="costCenterCode" value="'%' + dto.costCenterCode + '%'"/>
                and a.cost_center_code like #{costCenterCode}
            </if>
            <if test="dto.costCenterName != null and dto.costCenterName != ''">
                <bind name="costCenterName" value="'%' + dto.costCenterName + '%'"/>
                and a.cost_center_name like #{costCenterName}
            </if>
            <if test="dto.companyCode != null and dto.companyCode != ''">
                <bind name="companyCode" value="'%' + dto.companyCode + '%'"/>
                and a.company_code like #{companyCode}
            </if>
            order by a.create_time desc
        </where>
    </select>

</mapper>