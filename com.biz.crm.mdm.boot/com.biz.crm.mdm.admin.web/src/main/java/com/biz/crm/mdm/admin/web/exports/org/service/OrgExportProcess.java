package com.biz.crm.mdm.admin.web.exports.org.service;

import com.alibaba.fastjson.JSONArray;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.common.ie.sdk.excel.process.ExportProcess;
import com.biz.crm.common.ie.sdk.vo.ExportTaskProcessVo;
import com.biz.crm.mdm.admin.web.exports.org.mapper.OrgExportMapper;
import com.biz.crm.mdm.admin.web.exports.org.model.OrgExportDto;
import com.biz.crm.mdm.admin.web.exports.org.model.OrgExportVo;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class OrgExportProcess implements ExportProcess<OrgExportVo> {

  @Autowired(required = false)
  private OrgExportMapper orgExportMapper;

  @Autowired(required = false)
  private DictDataVoService dictDataVoService;

  @Override
  public Integer getTotal(Map<String, Object> params) {
    params = this.convertEuropaParam(params);
    String tenantCode = TenantUtils.getTenantCode();
    OrgExportDto dto = this.getOrgExportDto(tenantCode, params);
    return orgExportMapper.getTotal(dto);
  }

  /**
   * 得到组织出口dto
   *
   * @param params 参数个数
   * @return {@link OrgExportDto}
   */
  private OrgExportDto getOrgExportDto(String tenantCode, Map<String, Object> params) {
    Object orgCode = params.get("orgCode");
    Object orgName = params.get("orgName");
    Object enableStatus = params.get("enableStatus");
    Object orgType = params.get("orgType");
    OrgExportDto dto = new OrgExportDto();
    dto.setTenantCode(tenantCode);
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    dto.setOrgCode(ObjectUtils.isEmpty(orgCode) ? null : orgCode.toString());
    dto.setOrgName(ObjectUtils.isEmpty(orgName) ? null : orgName.toString());
    dto.setEnableStatus(ObjectUtils.isEmpty(enableStatus) ? null : enableStatus.toString());
    dto.setLevelName(ObjectUtils.isEmpty(orgType) ? null : orgType.toString());
    return dto;
  }

  @Override
  @Transactional /* 必须加上事务，否则导出的数据视图将会失效 */
  public JSONArray getData(ExportTaskProcessVo vo, Map<String, Object> params) {
    params = this.convertEuropaParam(params);
    String tenantCode = vo.getTenantCode();
    int offset = this.getPageSize() * vo.getPageNo();
    Integer limit = vo.getPageSize();
    OrgExportDto dto = this.getOrgExportDto(tenantCode, params);
    dto.setOffset(offset);
    dto.setLimit(limit);
    List<OrgExportVo> list = orgExportMapper.getData(dto);
    if (CollectionUtils.isEmpty(list)) {
      return null;
    }
    final Map<String, List<DictDataVo>> mapDict =
            this.dictDataVoService.findByDictTypeCodeList(
                    Lists.newArrayList(DictConstant.ENABLE_STATUS, DictConstant.MDM_ORG_TYPE));
    for (OrgExportVo item : list) {
      item.setLevelName(this.findDictValue(mapDict, DictConstant.MDM_ORG_TYPE, item.getLevelName()));
      item.setEnableStatus(this.findDictValue(mapDict, DictConstant.ENABLE_STATUS, item.getEnableStatus()));
    }
    return toJSONArray(list);
  }

  @Override
  public Class<OrgExportVo> findCrmExcelVoClass() {
    return OrgExportVo.class;
  }

  @Override
  public String getBusinessCode() {
    return "MDM_ORG_EXPORT";
  }

  @Override
  public String getBusinessName() {
    return "组织导出";
  }

  /**
   * 获取字典值
   *
   * @param mapDict
   * @param dictTypeCode
   * @param code
   * @return
   */
  private String findDictValue(
          Map<String, List<DictDataVo>> mapDict, String dictTypeCode, String code) {
    if (Objects.isNull(mapDict) || StringUtils.isBlank(dictTypeCode) || StringUtils.isBlank(code)) {
      return StringUtils.EMPTY;
    }
    final List<DictDataVo> vos = mapDict.get(dictTypeCode);
    if (CollectionUtils.isEmpty(vos)) {
      return StringUtils.EMPTY;
    }
    final Optional<String> first =
            vos.stream()
                    .filter(a -> a.getDictCode().equals(code))
                    .map(DictDataVo::getDictValue)
                    .findFirst();
    return first.orElse(StringUtils.EMPTY);
  }
}
