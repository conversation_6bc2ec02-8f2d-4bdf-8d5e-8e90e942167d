package com.biz.crm.mdm.admin.web.service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 业务侧短信服务接口
 *
 * <AUTHOR>
 */
public interface SmsVerificationCodeService {

  /**
   * 生成随机数字验证码
   *
   * @return String
   */
  default String generateVerificationCode() {
    //验证码长度
    int length = 6;
    double random = Math.random();
    return String.format("%0" + length + "d", new BigDecimal(random).multiply(BigDecimal.valueOf(Math.pow(10, length))).setScale(0, RoundingMode.HALF_UP).longValue());
  }

  /**
   * 【不同设备端】手机+验证码登录时获取短信验证码
   *
   * @param appType 设备端类型
   * @param phone 手机号
   */
  void phoneLoginVerificationCode(Integer appType, String phone);

  /**
   * 【不同设备端】找回密码时获取短信验证码
   *
   * @param appType 设备端类型
   * @param phone 手机号
   */
  void retrievePasswordVerificationCode(Integer appType, String phone);
}