package com.biz.crm.mdm.admin.web.report.terminal.dto;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @description 终端组合分页查询dto
 * @date 2024/03/06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel
public class TerminalCombinePageDto extends TenantDto {

  /**
   * 启用状态
   */
  @ApiModelProperty("启用状态")
  private String enableStatus;
  /**
   * 创建开始时间
   */
  @ApiModelProperty("创建开始时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createBeginTime;
  /**
   * 创建结束时间
   */
  @ApiModelProperty("创建结束时间")
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createEndTime;
  /**
   * 终端编码
   */
  @ApiModelProperty("终端编码")
  private String terminalCode;
  /**
   * 终端名称
   */
  @ApiModelProperty("终端名称")
  private String terminalName;
  /**
   * 终端类型
   */
  @ApiModelProperty("终端类型")
  private String terminalType;
  /**
   * 组织编码
   */
  @ApiModelProperty("组织编码")
  private String orgCode;
  /**
   * 所属组织名称
   */
  @ApiModelProperty("组织名称")
  private String orgName;
  /**
   * 组织上下级规则编码
   */
  @ApiModelProperty("组织上下级规则编码")
  private String orgRuleCode;
  /**
   * 组织编码集合
   */
  @ApiModelProperty(value = "组织编码集合", hidden = true)
  private Set<String> orgCodes;
  /**
   * 客户组织编码
   */
  @ApiModelProperty("客户组织编码")
  private String customerOrgCode;
  /**
   * 客户组织名称
   */
  @ApiModelProperty("客户组织名称")
  private String customerOrgName;
  /**
   * 客户组织上下级规则编码
   */
  @ApiModelProperty("客户组织上下级规则编码")
  private String customerOrgRuleCode;
  /**
   * 客户组织编码集合
   */
  @ApiModelProperty(value = "客户组织编码集合", hidden = true)
  private Set<String> customerOrgCodes;
  /**
   * 渠道
   */
  @ApiModelProperty("渠道")
  private String channel;
  /**
   * 营业执照人名称
   */
  @ApiModelProperty("营业执照人名称")
  private String licensePersonName;
  /**
   * 营业执照注册号
   */
  @ApiModelProperty("营业执照注册号")
  private String licenseRegisterNumber;
  /**
   * 营业执照企业名称
   */
  @ApiModelProperty("营业执照企业名称")
  private String licenseFirmName;
  /**
   * 终端地址
   */
  @ApiModelProperty("终端地址")
  private String terminalAddress;
  /**
   * 省编码
   */
  @ApiModelProperty("省编码")
  private String provinceCode;
  /**
   * 市编码
   */
  @ApiModelProperty("市编码")
  private String cityCode;
  /**
   * 区编码
   */
  @ApiModelProperty("区编码")
  private String districtCode;
  /**
   * 省名称
   */
  @ApiModelProperty("省名称")
  private String provinceName;
  /**
   * 市名称
   */
  @ApiModelProperty("市名称")
  private String cityName;
  /**
   * 区名称
   */
  @ApiModelProperty("区名称")
  private String districtName;
  /**
   * 客户分类
   */
  @ApiModelProperty("客户分类")
  private String customerClassification;
  /**
   * 审批状态
   */
  @ApiModelProperty("审批状态")
  private String actApproveStatus;
  /**
   * 合作状态
   */
  @ApiModelProperty("合作状态")
  private String cooperateStatus;
  /**
   * kms客户关联门店id
   */
  @ApiModelProperty("kms客户关联门店id")
  private String storeId;
  /**
   * kms直营体系id
   */
  @ApiModelProperty("kms直营体系id")
  private String directSystemId;
  /**
   * kms售达方id
   */
  @ApiModelProperty("kms售达方id")
  private String sellPartyId;
  /**
   * 高德地图ID
   */
  @ApiModelProperty("高德地图ID")
  private String amapId;
  /**
   * 审批类型
   */
  @ApiModelProperty("审批类型")
  private String approvalType;
  /**
   * 流程实例编码
   */
  @ApiModelProperty("流程实例编码")
  private String processCode;
  /**
   * 数据来源
   */
  @ApiModelProperty("数据来源")
  private String sourceType;
  /**
   * 数据来源信息
   */
  @ApiModelProperty("数据来源信息")
  private String sourceValue;
  /**
   * 终端名称
   */
  @ApiModelProperty("审批流程编码")
  private String processKey;
  /**
   * 终端名称
   */
  @ApiModelProperty("审批单号")
  private String processNumber;
  /**
   * 终端名称
   */
  @ApiModelProperty("审批备注")
  private String processRemark;
  /**
   * 终端名称
   */
  @ApiModelProperty("审批状态")
  private String processStatus;
  /**
   * 是否分利
   */
  @ApiModelProperty("是否分利")
  private Boolean shareBenefits;

  @ApiModelProperty("重复终端编码，英文逗号隔开")
  private String repeatCode;

  @ApiModelProperty("所属系统")
  private String sourceSystem;

  @ApiModelProperty("系统门店编码")
  private String systemTerminalCode;

  @ApiModelProperty("新增类型")
  private String addType;

  @ApiModelProperty("客户编码")
  private String customerCode;
  @ApiModelProperty("客户名称")
  private String customerName;

  @ApiModelProperty("终端编码列表")
  private List<String> terminalCodeList;

  /**
   * 所属中心
   */
  private String orgZeroName;
  /**
   * 大区运营部
   */
  private String orgDivisionName;
  /**
   * 业务部
   */
  private String orgRegionName;
  /**
   * 三级部门
   */
  private String orgAreaName;
}
