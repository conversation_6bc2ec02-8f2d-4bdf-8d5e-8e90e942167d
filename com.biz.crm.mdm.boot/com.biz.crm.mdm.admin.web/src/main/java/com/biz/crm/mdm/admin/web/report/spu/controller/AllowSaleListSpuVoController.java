package com.biz.crm.mdm.admin.web.report.spu.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.product.spu.sdk.dto.AllowSaleListSpuPaginationDto;
import com.biz.crm.mdm.business.product.spu.sdk.service.AllowSaleListSpuVoService;
import com.biz.crm.mdm.business.product.spu.sdk.vo.AllowSaleListSpuVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/**
 * 商品SPU管理: AllowSaleListSpuVo: 商品SPU管理电商可购清单分页数据
 *
 * <AUTHOR>
 * @date 2021/12/6
 */
@Slf4j
@Api(tags = "商品SPU管理: AllowSaleListSpuVo: 商品SPU管理电商可购清单分页数据")
@RestController
@RequestMapping(value = {"/v1/product/spu/report"})
public class AllowSaleListSpuVoController {

  @Autowired(required = false)
  private AllowSaleListSpuVoService allowSaleListSpuVoService;

  @ApiOperation(value = "查询分页列表")
  @PostMapping(value = {"/onRequestByAllowSaleListSpuPaginationDto"})
  public Result<Page<AllowSaleListSpuVo>> onRequestByAllowSaleListSpuPaginationDto(
      @RequestBody AllowSaleListSpuPaginationDto dto) {
    try {
      dto = Optional.ofNullable(dto).orElse(new AllowSaleListSpuPaginationDto());
      Page<AllowSaleListSpuVo> result =
          this.allowSaleListSpuVoService.onRequestByAllowSaleListSpuPaginationDto(dto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 精准搜索
   *
   * @param dto
   * @return
   */
  @ApiOperation(value = "查询分页列表")
  @PostMapping(value = {"/onRequestByAllowSaleListSpuPrecisePaginationDto"})
  public Result<Page<AllowSaleListSpuVo>> onRequestByAllowSaleListSpuPrecisePaginationDto(
      @RequestBody AllowSaleListSpuPaginationDto dto) {
    try {
      dto = Optional.ofNullable(dto).orElse(new AllowSaleListSpuPaginationDto());
      Page<AllowSaleListSpuVo> result =
          this.allowSaleListSpuVoService.onRequestByAllowSaleListSpuPrecisePaginationDto(dto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
