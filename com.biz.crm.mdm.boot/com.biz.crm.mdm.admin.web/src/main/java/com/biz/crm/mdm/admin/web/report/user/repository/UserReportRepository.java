package com.biz.crm.mdm.admin.web.report.user.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.mdm.admin.web.report.user.dto.UserReportDto;
import com.biz.crm.mdm.admin.web.report.user.mapper.UserReportMapper;
import com.biz.crm.mdm.admin.web.report.user.vo.RoleVo;
import com.biz.crm.mdm.admin.web.report.user.vo.UserReportVo;
import com.biz.crm.mdm.business.user.sdk.dto.UserFeignDto;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户报表(repository)
 *
 * <AUTHOR>
 * @since 2021-11-04 16:47:02
 */
@Component
public class UserReportRepository {

  @Autowired(required = false)
  private UserReportMapper userReportMapper;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  /**
   * 分页
   *
   * @param pageable
   * @param dto
   * @return
   */
  public Page<UserReportVo> findChildrenByConditions(Pageable pageable, @Param("dto") UserReportDto dto) {
    pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
    Page<UserReportDto> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    dto = Optional.ofNullable(dto).orElse(new UserReportDto());
    dto.setTenantCode(TenantUtils.getTenantCode());
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    Page<UserReportVo> reportByConditions = this.userReportMapper.findReportChildrenByConditions(page, dto);
    //合并角色
    if (CollectionUtils.isEmpty(reportByConditions.getRecords())) {
      return reportByConditions;
    }
    for (UserReportVo vo : reportByConditions.getRecords()) {
      if (CollectionUtils.isNotEmpty(vo.getRoles())) {
        List<String> codes = vo.getRoles().stream().filter(a -> Objects.nonNull(a) && StringUtils.isNotBlank(a.getRoleCode())).map(RoleVo::getRoleCode).distinct().collect(Collectors.toList());
        List<String> names = vo.getRoles().stream().filter(a -> Objects.nonNull(a) && StringUtils.isNotBlank(a.getRoleName())).map(RoleVo::getRoleName).distinct().collect(Collectors.toList());
        vo.setRuleCode(StringUtils.join(codes));
        vo.setRoleName(StringUtils.join(names));
      }
    }
    return reportByConditions;
  }


  /**
   * 分页
   *
   * @param pageable
   * @param dto
   * @return
   */
  public Page<UserReportVo> findByConditions(Pageable pageable, @Param("dto") UserReportDto dto) {
    pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
    Page<UserReportDto> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    dto = Optional.ofNullable(dto).orElse(new UserReportDto());
    dto.setTenantCode(TenantUtils.getTenantCode());
    dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    Page<UserReportVo> reportByConditions = this.userReportMapper.findReportByConditions(page, dto);
    //合并角色
    if (CollectionUtils.isEmpty(reportByConditions.getRecords())) {
      return reportByConditions;
    }
    for (UserReportVo vo : reportByConditions.getRecords()) {
      if (CollectionUtils.isNotEmpty(vo.getRoles())) {
        List<String> codes = vo.getRoles().stream().filter(a -> Objects.nonNull(a) && StringUtils.isNotBlank(a.getRoleCode())).map(RoleVo::getRoleCode).distinct().collect(Collectors.toList());
        List<String> names = vo.getRoles().stream().filter(a -> Objects.nonNull(a) && StringUtils.isNotBlank(a.getRoleName())).map(RoleVo::getRoleName).distinct().collect(Collectors.toList());
        vo.setRuleCode(StringUtils.join(codes));
        vo.setRoleName(StringUtils.join(names));
      }
    }
    return reportByConditions;
  }

  /**
   * 按照条件查询用户账号集合
   *
   * @param dto 查询集合
   * @return 用户账号信息集合
   */
  public List<UserReportVo> findByUserFeignDto(UserFeignDto dto) {
    return this.userReportMapper.findByUserFeignDto(dto);
  }

  /**
   * 查询用户总数量
   * @param enableStatus 起禁用状态
   * @param ignorePositionLevelCode 排除职位层级编码
   * @return
   */
  public List<UserVo> findTotalNum(String enableStatus, String ignorePositionLevelCode, String employeeType) {
    return this.userReportMapper.findTotalNum(enableStatus,ignorePositionLevelCode,employeeType);
  }
}
