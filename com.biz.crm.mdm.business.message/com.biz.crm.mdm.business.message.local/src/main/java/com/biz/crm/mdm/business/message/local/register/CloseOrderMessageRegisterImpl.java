//package com.biz.crm.mdm.business.message.local.register;
//
//import com.biz.crm.common.message.sdk.enums.MessageHandlerEnum;
//import com.biz.crm.common.message.sdk.enums.MessageTypeEnum;
//import com.biz.crm.common.message.sdk.register.SystemMessageHandlerRegister;
//import com.biz.crm.common.message.sdk.register.SystemMessageRegister;
//import com.biz.crm.common.message.sdk.service.SystemMessageTypeService;
//import com.google.common.collect.Sets;
//import java.util.List;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * 订单关闭消息通知类型注册器实现
// *
// * <AUTHOR>
// * @date 2022/7/5
// */
//@Component
//public class CloseOrderMessageRegisterImpl implements SystemMessageRegister {
//
//  @Autowired(required = false)
//  private SystemMessageTypeService systemMessageTypeService;
//
//  @Override
//  public String getCode() {
//    return MessageTypeEnum.CLOSE_ORDER.getDictCode();
//  }
//
//  @Override
//  public String getName() {
//    return MessageTypeEnum.CLOSE_ORDER.getValue();
//  }
//
//  @Override
//  public String getDesc() {
//    return "关闭订单通知客户关联业务员和客户";
//  }
//
//  @Override
//  public List<SystemMessageHandlerRegister> getHandlerRegisterList() {
//    return this.systemMessageTypeService.findHandlerByCodes(
//        Sets.newHashSet(
//            MessageHandlerEnum.CLOSE_ORDER_USER.getDictCode(),
//            MessageHandlerEnum.CLOSE_ORDER_CUSTOMER.getDictCode()));
//  }
//
//  @Override
//  public int getOrder() {
//    return 0;
//  }
//}
