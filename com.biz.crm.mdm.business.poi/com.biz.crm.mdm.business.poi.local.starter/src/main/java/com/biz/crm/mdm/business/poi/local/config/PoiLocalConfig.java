package com.biz.crm.mdm.business.poi.local.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2021/10/21
 */
@Configuration
@EntityScan(basePackages = "com.biz.crm.mdm.business.poi.local.entity")
@ComponentScan(basePackages = {"com.biz.crm.mdm.business.poi"})
public class PoiLocalConfig {}
