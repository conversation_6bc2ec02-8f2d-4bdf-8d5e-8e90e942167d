package com.biz.crm.mdm.business.poi.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * poi类型返回vo
 *
 * <AUTHOR>
 * @date 2021/10/9
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "poi类型返回vo")
public class PoiTypeVo {

  @ApiModelProperty("分类编码")
  private String typeCode;

  @ApiModelProperty("分类名称")
  private String typeName;

  @ApiModelProperty("分类级别")
  private Integer typeLevel;

  @ApiModelProperty("上级分类编码")
  private String parentCode;

  @ApiModelProperty("分类范围")
  private String typeCategory;

  @ApiModelProperty(value = "是否有子节点,该值为空则无子节点", hidden = true)
  private String childFlag;

  @ApiModelProperty("是否有子节点 1是 0否")
  private Integer hasChildFlag;

  @ApiModelProperty("是否有子节点 true是 false否")
  private Boolean hasChild;
}
