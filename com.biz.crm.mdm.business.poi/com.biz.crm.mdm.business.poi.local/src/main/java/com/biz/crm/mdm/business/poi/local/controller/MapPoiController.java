package com.biz.crm.mdm.business.poi.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.poi.sdk.dto.PoiDto;
import com.biz.crm.mdm.business.poi.sdk.service.MapPoiService;
import com.biz.crm.mdm.business.poi.sdk.vo.PoiVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 高德poi
 *
 * <AUTHOR>
 * @date 2021/10/9
 */
@Slf4j
@RestController
@RequestMapping("/v1/mappoi/mappoi")
@Api(tags = "高德poi")
public class MapPoiController {

  @Autowired(required = false) private MapPoiService mapPoiService;

  /**
   * 查询分页列表
   *
   * @param pageable
   * @param dto
   * @return
   */
  @ApiOperation(value = "查询分页列表", httpMethod = "GET")
  @GetMapping("/findByConditions")
  public Result<Page<PoiVo>> findByConditions(
      @PageableDefault(50) Pageable pageable,
      @ApiParam(name = "poiDto", value = "请求参数") PoiDto dto) {
    try {
      Page<PoiVo> result = this.mapPoiService.findByConditions(pageable, dto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
