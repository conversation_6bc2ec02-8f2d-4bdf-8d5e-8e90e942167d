package com.biz.crm.mdm.business.poi.local.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 高德POI行业分类表
 *
 * <AUTHOR>
 * @date 2021/10/9
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("mdm_amap_poi_type")
@ApiModel(value = "AmapPoiType", description = "高德POI行业分类信息")
@Table(
    name = "mdm_amap_poi_type",
    indexes = {
      @Index(name = "mdm_amap_poi_type_index1", columnList = "type_code"),
      @Index(name = "mdm_amap_poi_type_index2", columnList = "parent_code, type_code")
    })
@org.hibernate.annotations.Table(appliesTo = "mdm_amap_poi_type", comment = "高德POI行业分类信息")
public class AmapPoiType extends TenantFlagOpEntity {
  private static final long serialVersionUID = -4445110019948981139L;

  @ApiModelProperty("分类编码")
  @Column(
      name = "type_code",
      nullable = false,
      unique = true,
      length = 16,
      columnDefinition = "VARCHAR(16) COMMENT '分类编码'")
  private String typeCode;

  @ApiModelProperty("分类名称")
  @Column(
      name = "type_name",
      nullable = false,
      length = 128,
      columnDefinition = "VARCHAR(128) COMMENT '分类名称'")
  private String typeName;

  @ApiModelProperty("分类级别")
  @Column(name = "type_level", columnDefinition = "INT COMMENT '分类级别'")
  private Integer typeLevel;

  @ApiModelProperty("上级分类编码")
  @Column(name = "parent_code", length = 16, columnDefinition = "VARCHAR(16) COMMENT '上级分类编码'")
  private String parentCode;

  @ApiModelProperty("分类范围")
  @Column(name = "type_category", length = 128, columnDefinition = "VARCHAR(128) COMMENT '分类范围'")
  private String typeCategory;
}
