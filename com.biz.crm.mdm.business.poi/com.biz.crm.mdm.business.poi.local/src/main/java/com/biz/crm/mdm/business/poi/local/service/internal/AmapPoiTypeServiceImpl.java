package com.biz.crm.mdm.business.poi.local.service.internal;

import com.biz.crm.mdm.business.poi.local.repository.AmapPoiTypeRepository;
import com.biz.crm.mdm.business.poi.local.service.AmapPoiTypeService;
import com.biz.crm.mdm.business.poi.sdk.vo.PoiTypeVo;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 高德poi分类
 *
 * <AUTHOR>
 * @date 2021/10/9
 */
@Service(value = "amapPoiTypeService")
public class AmapPoiTypeServiceImpl implements AmapPoiTypeService {

  @Autowired(required = false) private AmapPoiTypeRepository amapPoiTypeRepository;

  @Override
  public List<PoiTypeVo> findLazyTree(String parentCode) {
    if (StringUtils.isBlank(parentCode)) {
      return build(amapPoiTypeRepository.findTopLevel());
    } else {
      return build(amapPoiTypeRepository.findListByParentCode(parentCode));
    }
  }

  /**
   * 对集合属性组装，设置子级标识
   *
   * @param list
   * @return
   */
  private List<PoiTypeVo> build(List<PoiTypeVo> list) {
    if (CollectionUtils.isEmpty(list)) {
      return list;
    }
    for (PoiTypeVo item : list) {
      item.setHasChildFlag(StringUtils.isBlank(item.getChildFlag()) ? 0 : 1);
      item.setHasChild(StringUtils.isNotBlank(item.getChildFlag()));
    }
    return list;
  }
}
