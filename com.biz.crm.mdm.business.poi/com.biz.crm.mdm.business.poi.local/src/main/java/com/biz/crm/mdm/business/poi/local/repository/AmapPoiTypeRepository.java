package com.biz.crm.mdm.business.poi.local.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.poi.local.entity.AmapPoiType;
import com.biz.crm.mdm.business.poi.local.mapper.AmapPoiTypeMapper;
import com.biz.crm.mdm.business.poi.sdk.vo.PoiTypeVo;
import java.util.List;

import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/10/9
 */
@Component
public class AmapPoiTypeRepository extends ServiceImpl<AmapPoiTypeMapper, AmapPoiType> {

  /**
   * 获取所有一级poi类型信息
   *
   * @return
   */
  public List<PoiTypeVo> findTopLevel() {
    return baseMapper.findListByParentCode(StringUtils.EMPTY);
  }

  /**
   * 根据parentCode获取poi类型信息
   *
   * @param parentCode
   * @return
   */
  public List<PoiTypeVo> findListByParentCode(String parentCode) {
    return baseMapper.findListByParentCode(parentCode);
  }
}
