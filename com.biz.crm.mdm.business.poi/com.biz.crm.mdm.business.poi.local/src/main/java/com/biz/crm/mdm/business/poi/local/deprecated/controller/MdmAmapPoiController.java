package com.biz.crm.mdm.business.poi.local.deprecated.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.deprecated.model.PageResult;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.poi.sdk.deprecated.dto.MdmAmapPoiReqVo;
import com.biz.crm.mdm.business.poi.sdk.dto.PoiDto;
import com.biz.crm.mdm.business.poi.sdk.service.MapPoiService;
import com.biz.crm.mdm.business.poi.sdk.vo.PoiVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 旧版-高德POI
 *
 * <AUTHOR>
 * @date 2021-05-10 22:44:52
 */
@Slf4j
@RestController
@RequestMapping("/mdmAmapPoiController")
@Api(tags = "MDM-高德地图POI")
@Deprecated
public class MdmAmapPoiController {

  @Autowired(required = false)
  private MapPoiService mapPoiService;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @ApiOperation(value = "查询分页列表")
  @PostMapping("/findAmapPoiPageList")
  public Result<PageResult<PoiVo>> findAmapPoiPageList(
      @RequestBody MdmAmapPoiReqVo reqVo) {
    try {
      reqVo = Optional.ofNullable(reqVo).orElse(new MdmAmapPoiReqVo());
      PoiDto dto = nebulaToolkitService
          .copyObjectByWhiteList(reqVo, PoiDto.class, HashSet.class, ArrayList.class);
      Page<PoiVo> page = mapPoiService.findByConditions(
          PageRequest.of(reqVo.getPageNum(), reqVo.getPageSize()), dto);
      List<PoiVo> list = Lists.newLinkedList();
      if (CollectionUtils.isNotEmpty(page.getRecords())) {
        list = page.getRecords();
      }
      PageResult<PoiVo> pageResult = PageResult.<PoiVo>builder()
          .data(list)
          .count(page.getTotal())
          .build();
      return Result.ok(pageResult);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
