package com.biz.crm.mdm.business.poi.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.mdm.business.poi.local.entity.AmapPoiType;
import com.biz.crm.mdm.business.poi.sdk.vo.PoiTypeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/9
 */
public interface AmapPoiTypeMapper extends BaseMapper<AmapPoiType> {

  /**
   * 根据parentCode获取poi类型信息
   *
   * @param parentCode
   * @return
   */
  List<PoiTypeVo> findListByParentCode(String parentCode);
}
