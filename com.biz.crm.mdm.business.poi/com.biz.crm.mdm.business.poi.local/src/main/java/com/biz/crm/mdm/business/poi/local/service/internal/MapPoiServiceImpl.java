package com.biz.crm.mdm.business.poi.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.poi.local.service.AmapPoiService;
import com.biz.crm.mdm.business.poi.sdk.dto.PoiDto;
import com.biz.crm.mdm.business.poi.sdk.service.MapPoiService;
import com.biz.crm.mdm.business.poi.sdk.vo.PoiVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

/**
 * 高德poi-sdk接口实现
 *
 * <AUTHOR>
 * @date 2021/10/9
 */
@Service("mapPoiService")
public class MapPoiServiceImpl implements MapPoiService {

  @Autowired(required = false) private AmapPoiService amapPoiService;

  /**
   * poi分页信息
   *
   * @param pageable
   * @param dto
   * @return
   */
  @Override
  public Page<PoiVo> findByConditions(Pageable pageable, PoiDto dto) {
    return amapPoiService.findByConditions(pageable, dto);
  }
}
