package com.biz.crm.mdm.business.price.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.price.local.entity.PriceType;
import com.biz.crm.mdm.business.price.local.entity.PriceTypeDetail;
import com.biz.crm.mdm.business.price.local.service.PriceTypeDetailService;
import com.biz.crm.mdm.business.price.local.service.PriceTypeService;
import com.biz.crm.mdm.business.price.sdk.dto.PriceTypePaginationDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 价格类型主信息(PriceType)表控制层
 *
 * <AUTHOR>
 * @date 2021-12-30 17:46:12
 */
@Slf4j
@Api(tags = "价格类型主信息: PriceType: 价格类型主信息")
@RestController
@RequestMapping(value = {"/v1/priceType/priceType"})
public class PriceTypeController {

  @Autowired(required = false) private PriceTypeService priceTypeService;

  @Autowired(required = false) private PriceTypeDetailService priceTypeDetailService;

  /**
   * 查询分页列表
   *
   * @param pageable
   * @param paginationDto
   * @return
   */
  @ApiOperation(value = "查询分页列表")
  @GetMapping(value = {"/findByConditions"})
  public Result<Page<PriceType>> findByConditions(
      @PageableDefault(50) Pageable pageable, PriceTypePaginationDto paginationDto) {
    try {
      Page<PriceType> result = this.priceTypeService.findByConditions(pageable, paginationDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 详情
   *
   * @param id
   * @return
   */
  @ApiOperation(value = "详情")
  @GetMapping(value = {"/findDetailById"})
  public Result<PriceType> findDetailById(@RequestParam("id") String id) {
    try {
      PriceType priceType = this.priceTypeService.findDetailById(id);
      return Result.ok(priceType);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 创建
   *
   * @param priceType
   * @return
   */
  @ApiOperation(value = "创建")
  @PostMapping(value = "")
  public Result<PriceType> create(@RequestBody PriceType priceType) {
    try {
      PriceType current = this.priceTypeService.create(priceType);
      return Result.ok(current);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 创建
   *
   * @param priceType
   * @return
   */
  @ApiOperation(value = "更新")
  @PatchMapping(value = "")
  public Result<PriceType> update(@RequestBody PriceType priceType) {
    try {
      PriceType current = this.priceTypeService.update(priceType);
      return Result.ok(current);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 逻辑删除
   *
   * @param ids
   * @return
   */
  @ApiOperation(value = "逻辑删除")
  @DeleteMapping("/delete")
  public Result<?> delete(@RequestParam("ids") List<String> ids) {
    try {
      this.priceTypeService.updateDelFlagByIds(ids);
      return Result.ok("删除成功");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据价格类型编码获取定价维度下拉
   *
   * @param typeCode
   * @param typeDetailName
   * @return
   */
  @ApiOperation(value = "根据价格类型编码获取定价维度下拉")
  @GetMapping("/findByTypeCodeAndTypeDetailName")
  public Result<List<PriceTypeDetail>> findByTypeCodeAndTypeDetailName(
      @RequestParam("typeCode") String typeCode,
      @RequestParam(name = "typeDetailName", required = false) String typeDetailName) {
    try {
      List<PriceTypeDetail> list =
          this.priceTypeDetailService.findByTypeCodeAndTypeDetailNameLike(typeCode, typeDetailName);
      return Result.ok(list);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
