package com.biz.crm.mdm.business.price.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.biz.crm.business.common.base.util.DateUtil;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.model.AbstractCrmUserIdentity;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.mdm.business.material.sdk.service.MaterialVoService;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialDetailVo;
import com.biz.crm.mdm.business.material.sdk.vo.MaterialVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.price.local.config.PriceProperties;
import com.biz.crm.mdm.business.price.local.entity.PriceType;
import com.biz.crm.mdm.business.price.local.entity.PriceTypeDetail;
import com.biz.crm.mdm.business.price.local.entity.PriceTypeDetailItem;
import com.biz.crm.mdm.business.price.local.repository.PriceModelRepository;
import com.biz.crm.mdm.business.price.local.service.PriceTypeService;
import com.biz.crm.mdm.business.price.sdk.constant.PriceConstant;
import com.biz.crm.mdm.business.price.sdk.dto.*;
import com.biz.crm.mdm.business.price.sdk.enums.FindPriceUserTypeEnum;
import com.biz.crm.mdm.business.price.sdk.enums.PriceDimensionEnum;
import com.biz.crm.mdm.business.price.sdk.register.UserSearchPriceDimensionItemRegister;
import com.biz.crm.mdm.business.price.sdk.service.PriceMilkCostVoService;
import com.biz.crm.mdm.business.price.sdk.service.PriceModelVoService;
import com.biz.crm.mdm.business.price.sdk.strategy.PriceMergeStrategy;
import com.biz.crm.mdm.business.price.sdk.vo.PriceMilkCostVo;
import com.biz.crm.mdm.business.price.sdk.vo.PriceModelVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantContextHolder;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.common.vo.SimpleTenantInfo;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 价格查询sdk接口实现
 *
 * <AUTHOR>
 * @date 2022/1/4
 */
@Service
@Slf4j
public class PriceModelVoServiceImpl implements PriceModelVoService {

    @Autowired(required = false)
    private PriceTypeService priceTypeService;

    @Autowired(required = false)
    private PriceModelRepository priceModelRepository;

    @Autowired(required = false)
    @Lazy
    private List<UserSearchPriceDimensionItemRegister> registers;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private ThreadPoolExecutor priceExecutor;

    @Autowired(required = false)
    private PriceProperties priceProperties;

    @Autowired(required = false)
    private PriceMergeStrategy priceMergeStrategy;

    @Autowired(required = false)
    @Lazy
    private LoginUserService loginUserService;

    @Autowired(required = false)
    private MaterialVoService materialVoService;

    @Autowired(required = false)
    private PriceMilkCostVoService priceMilkCostVoService;

    /**
     * 基于数据库执行的数据视图执行内容缓存（最多500毫秒）
     */
    private static volatile Cache<String, Map<String, PriceModelVo>> priceCache = null;

    public PriceModelVoServiceImpl() {
        if (priceCache == null) {
            synchronized (PriceModelVoServiceImpl.class) {
                while (priceCache == null) {
                    priceCache = CacheBuilder.newBuilder()
                            .initialCapacity(10000)
                            .expireAfterWrite(500, TimeUnit.MILLISECONDS)
                            .maximumSize(100000)
                            .build();
                }
            }
        }
    }

    @Override
    public Map<String, PriceModelVo> handleSearchPrice(SearchPriceDto dto) {
        this.validateData(dto);
        Map<String, PriceModelVo> map = Maps.newHashMap();
        PriceType priceType = this.priceTypeService.findDetailByTypeCode(dto.getPriceTypeCode());
        if (Objects.isNull(priceType)) {
            return map;
        }
        Set<String> dimensionRelateCodeJoinSet = this.findRelateJoinCodeSet(priceType, dto);
        if (CollectionUtils.isEmpty(dimensionRelateCodeJoinSet)) {
            return map;
        }
        PriceModelDto priceModelDto = new PriceModelDto();
        priceModelDto.setTypeCode(dto.getPriceTypeCode());
        priceModelDto.setTenantCode(TenantUtils.getTenantCode());
        priceModelDto.setDimensionCode(dto.getDimensionCode());
        priceModelDto.setRelateCodeSet(dto.getSet());
        priceModelDto.setRelateCodeJoinSet(dimensionRelateCodeJoinSet);
        priceModelDto.setSearchTime(dto.getSearchTime());
        List<PriceModelVo> list = this.priceModelRepository.findByPriceModelDto(priceModelDto);
        if (CollectionUtils.isEmpty(list)) {
            return map;
        }
        log.info("查询数据库(过滤前)的结果为={}", JSON.toJSONString(list));
        //这里对组织询到的价格进行过滤处理 start
        if (dto.getOrgRecursionFlag()) {
            log.info("询价结果={}", JSON.toJSONString(list));
            //取出组织的上下级关系
            Map<String, List<String>> orgRecursionMap = dto.getOrgRecursionMap();
            //按类型明细编码分类
            Map<String, List<PriceModelVo>> collect = list.stream().collect(Collectors.groupingBy(PriceModelVo::getTypeDetailCode));
            //key=明细类型编码  value为对应价格信息
            for (String key : collect.keySet()) {
                if (StringUtils.contains(key, PriceDimensionEnum.ORG.getDictCode())) {

                    //类型对应的价格集合
                    List<PriceModelVo> priceModelVos1 = collect.get(key);
                    //重置生效标示
                    priceModelVos1.forEach(o -> {
                        o.setValidateFlag(false);
                    });

                    //循环组织的上下级关系，如最底层的价格做为数据返回
                    for (String orgKey : orgRecursionMap.keySet()) {
                        List<String> orgCodes = orgRecursionMap.get(orgKey);

                        //依次从组织的从下往上匹配价格
                        LB:
                        for (String orgCode : orgCodes) {
                            for (PriceModelVo vo : priceModelVos1) {
                                String orgCodeFromVo = getOrgCodeFromVo(vo);
                                if (StringUtils.equals(orgCodeFromVo, orgCode)) {
                                    vo.setValidateFlag(true);
                                    break LB;
                                }

                            }
                        }
                    }
                }
            }
            Iterator<PriceModelVo> iterator = list.iterator();
            while (iterator.hasNext()) {
                PriceModelVo vo = iterator.next();
                if (!vo.getValidateFlag()) {
                    iterator.remove();
                }
            }
        }
        log.info("(过滤后)的结果为={}", JSON.toJSONString(list));
        //这里对组织询到的价格进行过滤处理 end
        return list.stream()
                .filter(a -> StringUtils.isNotBlank(a.getRelateCode()))
                .collect(Collectors.toMap(PriceModelVo::getRelateCode, Function.identity(), (a, b) -> a));
    }

    /***
     * 从对象中解析出orgCode字段
     * @return
     */
    private String getOrgCodeFromVo(PriceModelVo vo) {
        String typeDetailCode = vo.getTypeDetailCode();
        //从codeJion中解析中orgCode
        String relateCodeJoin = vo.getRelateCodeJoin();
        String typeCode = vo.getTypeCode();
        if (StringUtils.isEmpty(typeDetailCode) || StringUtils.isEmpty(relateCodeJoin) || StringUtils.isEmpty(typeCode)) {
            log.warn("价格基础数据维护有错,vo={}", JSON.toJSONString(vo));
            return null;
        }

        //替换typeDetailCode  从sale_price-org-product-RE3543543-9385746559484 变成了RE3543543-9385746559484
        relateCodeJoin = relateCodeJoin.replaceAll(typeDetailCode + PriceConstant.SEPARATOR, "");

        //从typeDetailCode中解析中维度 从sale_price-org-product变成了org-product
        String deCode = typeDetailCode.replaceAll(typeCode + PriceConstant.SEPARATOR, "");
        //定位org的关键字的位置
        String[] split = deCode.split(PriceConstant.SEPARATOR);
        int index = 0;
        for (int i = 0; i < split.length; i++) {
            if (StringUtils.equals(split[i], PriceDimensionEnum.ORG.getDictCode())) {
                index = i;
                break;
            }
        }
        String orgCode = null;
        String[] split1 = relateCodeJoin.split(PriceConstant.SEPARATOR);
        if (split1.length >= index + 1) {
            orgCode = split1[index];
        }
        log.info("从Vo={}对象中解析出来的orgCode={}", JSON.toJSONString(vo), orgCode);
        vo.setOrgCodeValue(orgCode);
        return orgCode;
    }

    @Override
    public Map<String, PriceModelVo> findPrice(FindPriceDto dto) {
        this.validateFindPriceData(dto);
        SearchPriceDto searchPriceDto = new SearchPriceDto();
        searchPriceDto.setDimensionCode(dto.getDimensionCode());
        searchPriceDto.setSet(dto.getProductCodeSet());
        searchPriceDto.setSearchTime(dto.getSearchTime());
        UserSearchPriceDimensionItemRegister register = this.findRegister(dto.getUserType());
        if (Objects.nonNull(register)) {
            searchPriceDto.setDimensionItems(register.findSearchPriceDimensionItems(dto));
        }
        // 校验绑定维度和注册器获取的维度是否有重复，重复抛出异常
        Set<String> dimensionSet = Sets.newHashSet(dto.getDimensionCode());
        if (CollectionUtils.isNotEmpty(searchPriceDto.getDimensionItems())) {
            for (SearchPriceDimensionItemDto item : searchPriceDto.getDimensionItems()) {
                Validate.isTrue(dimensionSet.add(item.getDimensionCode()), item.getDimensionCode() + "存在重复的维度查询信息");
            }
        }
        final Set<String> set = dto.getPriceTypeCode();
        List<SearchPriceDto> searchPriceDtoList = Lists.newLinkedList();
        for (String typeCode : set) {
            SearchPriceDto cur = this.nebulaToolkitService.copyObjectByBlankList(searchPriceDto, SearchPriceDto.class, HashSet.class, ArrayList.class);
            cur.setSet(searchPriceDto.getSet());
            cur.setDimensionItems(searchPriceDto.getDimensionItems());
            cur.setOrgRecursionFlag(dto.getOrgRecursionFlag());
            cur.setPriceTypeCode(typeCode);
            searchPriceDtoList.add(cur);
        }

        StringBuilder searchPriceKey = new StringBuilder();
        for (SearchPriceDto priceDto : searchPriceDtoList) {
            searchPriceKey.append(StringUtils.join(priceDto.getPriceTypeCode(), priceDto.getDimensionCode(), priceDto.getDimensionItems(), priceDto.getSet()));
        }
        String cacheKey = StringUtils.join(TenantUtils.getTenantCode(), searchPriceKey.toString());
        Map<String, PriceModelVo> re = priceCache.getIfPresent(cacheKey);
        if (re == null) {
            // 不同FindPriceDto产生的searchPriceDtoList可能是一样的，缓存之
            re = this.findPriceImpl(dto, searchPriceDtoList);
            priceCache.put(cacheKey, re);
        }
        return re;
    }

    @Override
    public Map<String, PriceModelVo> findMaterialUnitPrice(FindPriceDto dto) {
        Map<String, PriceModelVo> priceVoMap = new HashMap<>();
        Validate.notNull(dto, "询价参数不能为空!");
        String orderFactory = dto.getOrderFactory();
        Validate.notEmpty(orderFactory, "工厂不能为空!");
        List<MaterialVo> materialVoList = materialVoService.findDetailByMaterialCodes(dto.getProductCodeSet());
        if (CollectionUtil.isEmpty(materialVoList)) {
            return priceVoMap;
        }
        Set<String> materialGroupCode = Sets.newHashSet("10800", "10801", "10802");
        materialVoList.forEach(materialVo -> {
            if (CollectionUtil.isNotEmpty(materialVo.getDetailList())) {
                MaterialDetailVo materialDetailVo = materialVo.getDetailList().stream()
                        .filter(k -> StringUtil.isNotEmpty(k.getCostLabel()))
                        .filter(k -> Objects.nonNull(k.getCostPrice()))
                        .filter(k -> BooleanEnum.TRUE.getCapital().equals(k.getCostLabel()))
                        .filter(k -> orderFactory.equals(k.getFactoryTypeCode())).findFirst().orElse(null);
                if (Objects.nonNull(materialDetailVo)) {
                    PriceModelVo priceModelVo = priceVoMap.getOrDefault(materialDetailVo.getMaterialCode(), new PriceModelVo());
                    priceModelVo.setMaterialUnitPrice(materialDetailVo.getCostPrice());
                    // 成本价需要除以价格单位
                    Integer priceUnit = null;
                    try {
                        if (StrUtil.isNotBlank(materialDetailVo.getCostPriceUnit())) {
                            priceUnit = Integer.valueOf(materialDetailVo.getCostPriceUnit());
                        }
                        if (Objects.nonNull(priceUnit) && priceUnit > 1
                                && Objects.nonNull(materialDetailVo.getCostPrice())) {
                            // 计算实际单价，保留6位小数
                            BigDecimal actCostPrice = materialDetailVo.getCostPrice()
                                    .divide(new BigDecimal(priceUnit), 6, BigDecimal.ROUND_HALF_DOWN);
                            if (actCostPrice.compareTo(BigDecimal.ZERO) > 0) {
                                priceModelVo.setMaterialUnitPrice(actCostPrice);
                            }
                        }
                    } catch (Exception ignore) {
                    }
                    priceVoMap.put(materialDetailVo.getMaterialCode(), priceModelVo);
                }
            }
        });
        Set<String> productCodeSet = materialVoList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getMaterialGroupCode()))
                .filter(k -> StringUtil.isNotEmpty(k.getMaterialCode()))
                .filter(k -> materialGroupCode.contains(k.getMaterialGroupCode()))
                .map(MaterialVo::getMaterialCode).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(productCodeSet)) {
            return priceVoMap;
        }
        if (Objects.isNull(dto.getSearchTime())) {
            dto.setSearchTime(new Date());
        }
        String yearMonthLy = DateUtil.format(dto.getSearchTime(), DateUtil.DEFAULT_YEAR_MONTH);
        List<PriceMilkCostVo> milkCostVoList = priceMilkCostVoService.findByUpToDateMaterialCodes(yearMonthLy, productCodeSet);
        if (CollectionUtil.isEmpty(milkCostVoList)) {
            return priceVoMap;
        }
        milkCostVoList.forEach(milkCostVo -> {
            PriceModelVo priceModelVo = priceVoMap.getOrDefault(milkCostVo.getMaterialCode(), new PriceModelVo());
            priceModelVo.setMilkPrice(milkCostVo.getCostPrice());
            if (StringUtil.isEmpty(priceModelVo.getRelateCode())) {
                priceModelVo.setDimensionCode(PriceDimensionEnum.PRODUCT.getKey());
                priceModelVo.setRelateCode(milkCostVo.getMaterialCode());
            }
            priceVoMap.put(milkCostVo.getMaterialCode(), priceModelVo);
        });
        return priceVoMap;
    }

    @Override
    public Map<String, PriceModelVo> findPriceNew(FindPriceDto dto) {
        Validate.notNull(dto, "询价参数不能为空!");
        String orderFactory = dto.getOrderFactory();
        Validate.notEmpty(orderFactory, "工厂不能为空!");
        Map<String, PriceModelVo> priceVoMap = this.findPrice(dto);
        List<MaterialVo> materialVoList = materialVoService.findDetailByMaterialCodes(dto.getProductCodeSet());
        if (CollectionUtil.isEmpty(materialVoList)) {
            return priceVoMap;
        }
        //1046200 【新询价接口开发】
        Set<String> materialGroupCode = Sets.newHashSet("10800", "10801", "10802");
        materialVoList.forEach(materialVo -> {
            if (CollectionUtil.isNotEmpty(materialVo.getDetailList())) {
                MaterialDetailVo materialDetailVo = materialVo.getDetailList().stream()
                        .filter(k -> StringUtil.isNotEmpty(k.getCostLabel()))
                        .filter(k -> Objects.nonNull(k.getCostPrice()))
                        .filter(k -> BooleanEnum.TRUE.getCapital().equals(k.getCostLabel()))
                        .filter(k -> orderFactory.equals(k.getFactoryTypeCode())).findFirst().orElse(null);
                if (Objects.nonNull(materialDetailVo)) {
                    PriceModelVo priceModelVo = priceVoMap.getOrDefault(materialDetailVo.getMaterialCode(), new PriceModelVo());
                    priceModelVo.setCostPrice(materialDetailVo.getCostPrice());
                    priceModelVo.setCostPriceUnit(Integer.valueOf(materialDetailVo.getCostPriceUnit()));
                    if (StringUtil.isEmpty(priceModelVo.getRelateCode())) {
                        priceModelVo.setDimensionCode(PriceDimensionEnum.PRODUCT.getKey());
                        priceModelVo.setRelateCode(materialDetailVo.getMaterialCode());
                    }
                    priceVoMap.put(materialDetailVo.getMaterialCode(), priceModelVo);
                }
            }
        });
        Set<String> productCodeSet = materialVoList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getMaterialGroupCode()))
                .filter(k -> StringUtil.isNotEmpty(k.getMaterialCode()))
                .filter(k -> materialGroupCode.contains(k.getMaterialGroupCode()))
                .map(MaterialVo::getMaterialCode).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(productCodeSet)) {
            return priceVoMap;
        }
        if (Objects.isNull(dto.getSearchTime())) {
            return priceVoMap;
        }
        String yearMonthLy = DateUtil.format(dto.getSearchTime(), DateUtil.DEFAULT_YEAR_MONTH);
        List<PriceMilkCostVo> milkCostVoList = priceMilkCostVoService.findByUpToDateMaterialCodes(yearMonthLy, productCodeSet);
        if (CollectionUtil.isEmpty(milkCostVoList)) {
            return priceVoMap;
        }
        milkCostVoList.forEach(milkCostVo -> {
            PriceModelVo priceModelVo = priceVoMap.getOrDefault(milkCostVo.getMaterialCode(), new PriceModelVo());
            priceModelVo.setMilkPrice(milkCostVo.getCostPrice());
            if (StringUtil.isEmpty(priceModelVo.getRelateCode())) {
                priceModelVo.setDimensionCode(PriceDimensionEnum.PRODUCT.getKey());
                priceModelVo.setRelateCode(milkCostVo.getMaterialCode());
            }
            priceVoMap.put(milkCostVo.getMaterialCode(), priceModelVo);
        });
        return priceVoMap;
    }

    /**
     * 询价实现
     *
     * @param dto
     * @param searchPriceDtoList
     * @return
     */
    public Map<String, PriceModelVo> findPriceImpl(FindPriceDto dto, List<SearchPriceDto> searchPriceDtoList) {
        AbstractCrmUserIdentity userIdentity = this.loginUserService.getAbstractLoginUser();
        List<Map<String, PriceModelVo>> list = Lists.newCopyOnWriteArrayList();
        final long l = System.currentTimeMillis();
        final CompletableFuture<?>[] futures =
                searchPriceDtoList.stream()
                        .map(
                                t -> CompletableFuture
                                        .supplyAsync(
                                                () -> {
                                                    this.transferLoginIdentity(userIdentity);
                                                    return this.findPrice(t, TenantUtils.getTenantCode());
                                                }
                                                , priceExecutor
                                        )
                                        .whenComplete(
                                                (r, e) -> {
                                                    if (e == null && !r.isEmpty()) {
                                                        list.add(r);
                                                    }
                                                    if (e != null) {
                                                        e.printStackTrace();
                                                    }
                                                }
                                        )
                                        .exceptionally((e) -> Maps.newHashMap())
                        )
                        .toArray(CompletableFuture[]::new);
        CompletableFuture.allOf(futures).join();
        Map<String, List<PriceModelVo>> map = this.findPriceMap(list);
        if (map.isEmpty()) {
            return Maps.newHashMap();
        }
        if (priceMergeStrategy == null) {
            Map<String, PriceModelVo> re = Maps.newHashMap();
            for (Entry<String, List<PriceModelVo>> item : map.entrySet()) {
                final List<PriceModelVo> value = item.getValue();
                if (!CollectionUtils.isEmpty(value)) {
                    re.put(item.getKey(), value.get(0));
                }
            }
            return re;
        } else {
            return this.priceMergeStrategy.merge(map);
        }
    }

    /**
     * 将身份信息放入线程上下文
     *
     * @param userIdentity 用户身份
     */
    private void transferLoginIdentity(AbstractCrmUserIdentity userIdentity) {
        List<SimpleGrantedAuthority> grantedAuthorities = Lists.newArrayList();
        String[] roleCodes = userIdentity.getRoleCodes();
        if (roleCodes != null && roleCodes.length > 0) {
            for (String roleCode : roleCodes) {
                grantedAuthorities.add(new SimpleGrantedAuthority(roleCode));
            }
        }
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userIdentity.getAccount(), "123456", grantedAuthorities);
        authentication.setDetails(userIdentity);
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }

    @Override
    public Map<String, PriceModelVo> findBusinessPrice(FindBusinessPriceDto dto) {
        this.validateFindPriceWay1Data(dto);
        String dimensionCode = StringUtils.EMPTY;
        if (FindPriceUserTypeEnum.CUSTOMER.getDictCode().equals(dto.getUserType())) {
            dimensionCode = PriceDimensionEnum.CUSTOMER.getDictCode();
        } else if (FindPriceUserTypeEnum.TERMINAL.getDictCode().equals(dto.getUserType())) {
            dimensionCode = PriceDimensionEnum.TERMINAL.getDictCode();
        }
        PriceType priceType = this.priceTypeService.findDetailByTypeCode(dto.getPriceTypeCode());
        if (Objects.isNull(priceType)) {
            return Maps.newHashMap();
        }
        Map<String, List<String>> map = Maps.newHashMap();
        for (FindBusinessPriceItemDto item : dto.getList()) {
            final String businessCode = item.getBusinessCode();
            for (String productCode : item.getProductCodeSet()) {
                final List<String> cur = this.findProductRelateJoinCodeList(dimensionCode, businessCode, productCode, item.getDimensions(), priceType);
                if (CollectionUtils.isNotEmpty(cur)) {
                    map.put(businessCode + PriceConstant.SEPARATOR + productCode, cur);
                }
            }
        }
        if (map.isEmpty()) {
            return Maps.newHashMap();
        }

        Set<String> codeList = Sets.newHashSet();
        for (Entry<String, List<String>> item : map.entrySet()) {
            codeList.addAll(item.getValue());
        }
        List<PriceModelVo> list = this.priceModelRepository.findByPriceModelDtoByRelateJoinCodes(codeList, dto.getSearchTime());
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        Map<String, PriceModelVo> mapPrice =
                list.stream()
                        .filter(a -> StringUtils.isNoneBlank(a.getRelateCodeJoin()))
                        .collect(Collectors.toMap(PriceModelVo::getRelateCodeJoin, Function.identity(), (a, b) -> a));
        Map<String, PriceModelVo> re = Maps.newHashMap();
        for (Entry<String, List<String>> item : map.entrySet()) {
            for (String sub : item.getValue()) {
                final PriceModelVo cur = mapPrice.get(sub);
                if (Objects.nonNull(cur)) {
                    re.put(item.getKey(), cur);
                    break;
                }
            }
        }
        return re;
    }

    /**
     * 询价
     *
     * @param searchPriceDto
     * @param tenantCode
     * @return
     */
    private Map<String, PriceModelVo> findPrice(SearchPriceDto searchPriceDto, String tenantCode) {
        // 设置在独立线程中，可能使用的租户信息
        SimpleTenantInfo simpleTenantInfo = new SimpleTenantInfo(tenantCode);
        TenantContextHolder.setTenantInfo(simpleTenantInfo);
        return this.handleSearchPrice(searchPriceDto);
    }

    @Resource
    private OrgVoService orgVoService;

    /***
     * 递归查询组织的所有上级组织
     * @param item
     * @param dto
     */
    private void findParentOrgCodes(SearchPriceDimensionItemDto item, SearchPriceDto dto) {
        Map<String, List<String>> maps = Maps.newHashMap();
        if (dto.getOrgRecursionFlag() && PriceDimensionEnum.ORG.getDictCode().equals(item.getDimensionCode())) {
            Set<String> relateCodeSet = item.getRelateCodeSet();
            for (String orgCode : relateCodeSet) {
                List<OrgVo> allParentByOrgCode = orgVoService.findAllParentByOrgCode(orgCode);
                if (CollectionUtils.isNotEmpty(allParentByOrgCode)) {
                    List<String> collect = allParentByOrgCode.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());
                    maps.put(orgCode, collect);
                    //填充所有上级组织,去查询价格
                    item.getRelateCodeSet().addAll(collect);
                }

            }
            dto.setOrgRecursionMap(maps);
        } else {
            log.info("当前查询不需要递归查询上级组织的价格");
        }

    }

    /**
     * 根据价格类型配置信息+询价参数获取relateCodeJoin编码集合
     *
     * <p>为了降低查询复杂度，默认各个维度对应的业务编码都有明确的前缀区分
     *
     * @param priceType
     * @param dto
     * @return
     */
    private Set<String> findRelateJoinCodeSet(PriceType priceType, SearchPriceDto dto) {
        Set<String> set = Sets.newHashSet();
        // k-dimensionCode,v-relateCodeSet
        Map<String, Set<String>> map = Maps.newHashMap();
        map.put(dto.getDimensionCode(), dto.getSet());
        if (CollectionUtils.isNotEmpty(dto.getDimensionItems())) {
            for (SearchPriceDimensionItemDto item : dto.getDimensionItems()) {
                findParentOrgCodes(item, dto);
                map.put(item.getDimensionCode(), item.getRelateCodeSet());
            }
        }
        CK:
        for (PriceTypeDetail detail : priceType.getDetailList()) {
            List<Set<String>> list = Lists.newLinkedList();
            for (PriceTypeDetailItem item : detail.getItemList()) {
                Set<String> set1 = map.get(item.getDimensionCode());
                if (CollectionUtils.isNotEmpty(set1)) {
                    list.add(set1);
                } else {
                    continue CK;
                }
            }
            Set<String> relateJoinCode = this.findRelateJoinCode(list);
            if (CollectionUtils.isNotEmpty(relateJoinCode)) {
                for (String item : relateJoinCode) {
                    set.add(detail.getTypeDetailCode() + PriceConstant.SEPARATOR + item);
                }
            }
        }
        return set;
    }

    /**
     * 获取relateCode,排列组合
     *
     * <p>参数 [1,2,3] [a,b] [$]
     *
     * <p>结果 1-a-$,2-a-$,3-a-$,1-b-$,2-b-$,3-b-$
     *
     * @param list
     * @return
     */
    private Set<String> findRelateJoinCode(List<Set<String>> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Sets.newHashSet();
        }
        List<StringBuilder> cur = Lists.newLinkedList();
        for (int i = 0; i < list.size(); i++) {
            Set<String> curSet = list.get(i);
            if (i == 0) {
                for (String s : curSet) {
                    cur.add(new StringBuilder(s));
                }
            } else {
                List<StringBuilder> cur1 = Lists.newLinkedList();
                for (String code : curSet) {
                    for (StringBuilder s : cur) {
                        cur1.add(new StringBuilder(s).append(PriceConstant.SEPARATOR).append(code));
                    }
                }
                cur = cur1;
            }
        }

        HashSet<String> set = Sets.newHashSet();
        for (StringBuilder sb : cur) {
            set.add(sb.toString());
        }
        return set;
    }

    /**
     * 获取业务编码对应商品的询价关联维度code集合，且按照价格维度优先级排序返回
     *
     * @param dimensionCode
     * @param businessCode
     * @param productCode
     * @param dimensions
     * @param priceType
     * @return
     */
    private List<String> findProductRelateJoinCodeList(
            String dimensionCode,
            String businessCode,
            String productCode,
            List<FindBusinessPriceItemDimensionDto> dimensions,
            PriceType priceType) {
        Set<String> re = Sets.newLinkedHashSet();
        final boolean f =
                Objects.nonNull(priceType)
                        && StringUtils.isNoneBlank(dimensionCode, businessCode, productCode);
        Validate.isTrue(f, "询价参数异常");
        Map<String, Set<String>> map = Maps.newHashMap();
        map.put(PriceDimensionEnum.PRODUCT.getDictCode(), Sets.newHashSet(productCode));
        map.put(dimensionCode, Sets.newHashSet(businessCode));
        if (CollectionUtils.isNotEmpty(dimensions)) {
            Set<String> set = Sets.newHashSet(PriceDimensionEnum.PRODUCT.getDictCode(), dimensionCode);
            dimensions.stream()
                    .filter(a -> !set.contains(a.getCode()) && CollectionUtils.isNotEmpty(a.getValues()))
                    .forEach(
                            a -> {
                                map.put(a.getCode(), a.getValues());
                            });
        }
        CK:
        for (PriceTypeDetail detail : priceType.getDetailList()) {
            List<Set<String>> list = Lists.newLinkedList();
            for (PriceTypeDetailItem item : detail.getItemList()) {
                Set<String> set1 = map.get(item.getDimensionCode());
                if (CollectionUtils.isNotEmpty(set1)) {
                    list.add(set1);
                } else {
                    continue CK;
                }
            }
            Set<String> relateJoinCode = this.findRelateJoinCode(list);
            if (CollectionUtils.isNotEmpty(relateJoinCode)) {
                for (String item : relateJoinCode) {
                    re.add(detail.getTypeDetailCode() + PriceConstant.SEPARATOR + item);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(re)) {
            return Lists.newArrayList(re);
        }
        return Lists.newLinkedList();
    }

    /**
     * 获取询价维度配置信息注册器实现
     *
     * @param code
     * @return
     */
    private UserSearchPriceDimensionItemRegister findRegister(String code) {
        if (StringUtils.isBlank(code) || CollectionUtils.isEmpty(registers)) {
            return null;
        }
        return registers.stream().filter(a -> code.equals(a.getCode())).findFirst().orElse(null);
    }

    /**
     * 询价结果整理
     *
     * @param list
     * @return
     */
    private Map<String, List<PriceModelVo>> findPriceMap(List<Map<String, PriceModelVo>> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        Map<String, List<PriceModelVo>> map = Maps.newHashMap();
        for (Map<String, PriceModelVo> item : list) {
            for (Entry<String, PriceModelVo> cur : item.entrySet()) {
                final List<PriceModelVo> curList = map.getOrDefault(cur.getKey(), Lists.newLinkedList());
                curList.add(cur.getValue());
                map.put(cur.getKey(), curList);
            }
        }
        return map;
    }

    /**
     * 查询价格参数校验
     *
     * @param dto
     */
    private void validateData(SearchPriceDto dto) {
        Validate.notNull(dto, "询价参数不能为空");
        dto.setSearchTime(Optional.ofNullable(dto.getSearchTime()).orElse(new Date()));
        Validate.notBlank(dto.getPriceTypeCode(), "价格类型编码不能为空");
        if (StringUtil.isEmpty(dto.getDimensionCode())) {
            dto.setDimensionCode(PriceDimensionEnum.PRODUCT.getKey());
        }
        Validate.isTrue(CollectionUtils.isNotEmpty(dto.getSet()), "绑定价格维度源数据编码集合不能为空");
        if (CollectionUtils.isNotEmpty(dto.getDimensionItems())) {
            Set<String> set =
                    dto.getDimensionItems().stream()
                            .filter(a -> StringUtils.isNotBlank(a.getDimensionCode()))
                            .map(SearchPriceDimensionItemDto::getDimensionCode)
                            .collect(Collectors.toSet());
            Validate.isTrue(CollectionUtils.isNotEmpty(set), "定价维度筛选项对应的维度编码集合不能为空");
            if (set.contains(dto.getDimensionCode())) {
                Validate.isTrue(false, "绑定价格维度不能存在于筛选维度");
            }
        }
    }

    /**
     * 询价参数校验
     *
     * @param dto
     */
    private void validateFindPriceData(FindPriceDto dto) {
        Validate.notNull(dto, "询价参数不能为空");
        dto.setSearchTime(Optional.ofNullable(dto.getSearchTime()).orElse(new Date()));
        if (CollectionUtil.isNotEmpty(dto.getPriceTypeCode())) {
            dto.setPriceTypeCode(dto.getPriceTypeCode().stream().filter(StringUtil::isNotEmpty).collect(Collectors.toSet()));
        }
        if (CollectionUtil.isEmpty(dto.getPriceTypeCode())) {
            dto.setPriceTypeCode(Sets.newHashSet(priceProperties.getSalePriceCode()));
        }
        Validate.notBlank(dto.getUserType(), "询价用户类型不能为空");
        Validate.notBlank(dto.getUserCode(), "询价用户编码不能为空");
        if (CollectionUtil.isNotEmpty(dto.getProductCodeSet())) {
            dto.setProductCodeSet(dto.getProductCodeSet().stream().filter(StringUtil::isNotEmpty).collect(Collectors.toSet()));
        }
        Validate.isTrue(CollectionUtils.isNotEmpty(dto.getProductCodeSet()), "商品编码集合不能为空");
    }

    /**
     * 方式1询价参数校验
     *
     * @param dto
     */
    private void validateFindPriceWay1Data(FindBusinessPriceDto dto) {
        Validate.notNull(dto, "询价参数不能为空");
        Validate.notBlank(dto.getUserType(), "询价用户业务类型不能为空");
        if (StringUtils.isBlank(dto.getPriceTypeCode())) {
            dto.setPriceTypeCode(this.priceProperties.getSalePriceCode());
        }
        if (Objects.isNull(dto.getSearchTime())) {
            dto.setSearchTime(new Date());
        }
        Validate.notEmpty(dto.getList(), "询价维度信息不能为空");
        for (FindBusinessPriceItemDto item : dto.getList()) {
            boolean f =
                    StringUtils.isNotBlank(item.getBusinessCode())
                            && CollectionUtils.isNotEmpty(item.getProductCodeSet());
            Validate.isTrue(f, "业务编码及商品编码不能为空");
        }
    }
}
