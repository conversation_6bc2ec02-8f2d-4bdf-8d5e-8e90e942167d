package com.biz.crm.mdm.business.price.local.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.mapper.BusinessBaseMapper;
import com.biz.crm.mdm.business.price.local.entity.Price;
import com.biz.crm.mdm.business.price.sdk.dto.PricePaginationDto;
import com.biz.crm.mdm.business.price.sdk.vo.PriceVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 价格维护主信息(Price)表数据库访问层
 *
 * <AUTHOR>
 * @date 2021-12-30 17:46:03
 */
public interface PriceMapper extends BusinessBaseMapper<Price> {


  /**
   * 分页列表
   *
   * @param page 分页信息
   * @param dto 分页参数dto
   * @return 分页列表
   */
  Page<PriceVo> findByConditionsNew(Page<Price> page, @Param("dto")  PricePaginationDto dto);

  /**
   * 分页列表
   *
   * @param page 分页信息
   * @param dto 分页参数dto
   * @return 分页列表
   */
  Page<Price> findByConditions(Page<Price> page, @Param("dto") PricePaginationDto dto);

  /**
   * 根据类型明细和产品查询价格
   * @param typeDetailCode 价格类型明细编码
   * @param productCodeSet 产品编码集合
   * @return
   */
  List<PriceVo> findPriceByTypeDetailCodeAndProducts(@Param("typeDetailCode") String typeDetailCode, @Param("productCodeSet") Set<String> productCodeSet);

}
