package com.biz.crm.mdm.business.price.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.mdm.business.price.local.entity.Price;
import com.biz.crm.mdm.business.price.local.entity.PriceType;
import com.biz.crm.mdm.business.price.local.entity.PriceTypeDetail;
import com.biz.crm.mdm.business.price.local.entity.PriceTypeDetailItem;
import com.biz.crm.mdm.business.price.local.event.PriceTypeLogEventListenerImpl;
import com.biz.crm.mdm.business.price.local.repository.PriceTypeRepository;
import com.biz.crm.mdm.business.price.local.service.PriceService;
import com.biz.crm.mdm.business.price.local.service.PriceTypeDetailService;
import com.biz.crm.mdm.business.price.local.service.PriceTypeService;
import com.biz.crm.mdm.business.price.sdk.constant.PriceConstant;
import com.biz.crm.mdm.business.price.sdk.dto.PriceEventDto;
import com.biz.crm.mdm.business.price.sdk.dto.PriceTypeEventDto;
import com.biz.crm.mdm.business.price.sdk.dto.PriceTypePaginationDto;
import com.biz.crm.mdm.business.price.sdk.enums.PriceDimensionEnum;
import com.biz.crm.mdm.business.price.sdk.event.PriceLogEventListener;
import com.biz.crm.mdm.business.price.sdk.event.PriceTypeLogEventListener;
import com.biz.crm.mdm.business.price.sdk.service.PriceDimensionContainerService;
import com.biz.crm.mdm.business.price.sdk.vo.CombineDimensionVo;
import com.biz.crm.mdm.business.price.sdk.vo.PriceTypeVo;
import com.biz.crm.mdm.business.price.sdk.vo.PriceVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.collect.Sets.SetView;

import java.util.*;

import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 价格类型主信息(PriceType)表服务实现类
 *
 * <AUTHOR>
 * @date 2021-12-30 17:46:11
 */
@Service("priceTypeService")
public class PriceTypeServiceImpl implements PriceTypeService {

    @Autowired(required = false)
    private PriceTypeRepository priceTypeRepository;

    @Autowired(required = false)
    private GenerateCodeService generateCodeService;

    @Autowired(required = false)
    private PriceDimensionContainerService priceDimensionContainerService;

    @Autowired(required = false)
    private PriceTypeDetailService priceTypeDetailService;

    @Autowired(required = false)
    private PriceService priceService;

    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Override
    public Page<PriceType> findByConditions(Pageable pageable, PriceTypePaginationDto dto) {
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
        dto = Optional.ofNullable(dto).orElse(new PriceTypePaginationDto());
        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        dto.setTenantCode(TenantUtils.getTenantCode());
        Page<PriceType> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.priceTypeRepository.findByConditions(page, dto);
    }

    @Override
    public PriceType findDetailById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        PriceType priceType = this.priceTypeRepository.findById(id);
        if (Objects.isNull(priceType)) {
            return null;
        }
        List<PriceTypeDetail> detailList =
                this.priceTypeDetailService.findByTypeCodes(Lists.newArrayList(priceType.getTypeCode()));
        priceType.setDetailList(detailList);
        return priceType;
    }

    @Override
    public PriceType findDetailByTypeCode(String typeCode) {
        if (StringUtils.isBlank(typeCode)) {
            return null;
        }
        PriceType priceType = this.priceTypeRepository.findByTypeCode(typeCode);
        if (Objects.isNull(priceType)) {
            return null;
        }
        List<PriceTypeDetail> detailList =
                this.priceTypeDetailService.findByTypeCodes(Lists.newArrayList(priceType.getTypeCode()));
        priceType.setDetailList(detailList);
        return priceType;
    }

    @Override
    @Transactional
    public PriceType create(PriceType priceType) {
        this.createValidation(priceType);
        priceType.setTenantCode(TenantUtils.getTenantCode());
        priceType.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        priceType.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        priceType.setTypeCode(priceType.getTypeCode().toUpperCase());
        if (StringUtils.isBlank(priceType.getTypeCode())) {
            priceType.setTypeCode(
                    this.generateCodeService.generateCode(PriceConstant.PRICE_TYPE_CODE_PREFIX));
        } else {
            Integer count = this.priceTypeRepository.countByTypeCode(priceType.getTypeCode());
            Validate.isTrue(null == count || 1 > count, priceType.getTypeCode() + "价格类型编码已存在");
        }
        this.priceTypeRepository.saveOrUpdate(priceType);
        this.savePriceTypeExtInfo(priceType);
        //保存日志
        PriceTypeVo nowVo = this.nebulaToolkitService.copyObjectByWhiteList(priceType, PriceTypeVo.class, HashSet.class, ArrayList.class);
        PriceTypeEventDto priceTypeEventDto = new PriceTypeEventDto();
        priceTypeEventDto.setNewest(nowVo);
        priceTypeEventDto.setOriginal(null);
        SerializableBiConsumer<PriceTypeLogEventListener, PriceTypeEventDto> onCreate =
                PriceTypeLogEventListener::onCreate;
        this.nebulaNetEventClient.publish(priceTypeEventDto, PriceTypeLogEventListener.class, onCreate);
        return priceType;
    }

    @Override
    @Transactional
    public PriceType update(PriceType priceType) {
        this.updateValidation(priceType);
        String currentId = priceType.getId();
        priceType.setTypeCode(priceType.getTypeCode().toUpperCase());
        PriceType current = this.findDetailById(currentId);
        Validate.notNull(current, "修改信息不存在");
        current.setTypeCode(current.getTypeCode().toUpperCase());
        //新增租户编号
        priceType.setTenantCode(TenantUtils.getTenantCode());
        Validate.isTrue(priceType.getTypeCode().equals(current.getTypeCode()), "价格类型编码不能修改");
        this.priceTypeRepository.saveOrUpdate(priceType);
        this.savePriceTypeExtInfo(priceType);

        //保存日志
        PriceTypeVo oldVo = this.nebulaToolkitService.copyObjectByWhiteList(current, PriceTypeVo.class, HashSet.class, ArrayList.class);
        PriceTypeVo nowVo = this.nebulaToolkitService.copyObjectByWhiteList(priceType, PriceTypeVo.class, HashSet.class, ArrayList.class);
        PriceTypeEventDto priceTypeEventDto = new PriceTypeEventDto();
        priceTypeEventDto.setNewest(nowVo);
        priceTypeEventDto.setOriginal(oldVo);
        SerializableBiConsumer<PriceTypeLogEventListener, PriceTypeEventDto> onUpdate =
                PriceTypeLogEventListener::onUpdate;
        this.nebulaNetEventClient.publish(priceTypeEventDto, PriceTypeLogEventListener.class, onUpdate);

        // 验证历史的
        // k-typeDetailCode,v-typeDetailName
        Map<String, String> oldMapDetailCodeAndName =
                current.getDetailList().stream()
                        .filter(a -> StringUtils.isNotBlank(a.getTypeDetailCode()))
                        .collect(
                                Collectors.toMap(
                                        PriceTypeDetail::getTypeDetailCode,
                                        PriceTypeDetail::getTypeDetailName,
                                        (a, b) -> a));

        Set<String> newDetailCodeSet =
                priceType.getDetailList().stream()
                        .filter(a -> StringUtils.isNotBlank(a.getTypeDetailCode()))
                        .map(PriceTypeDetail::getTypeDetailCode)
                        .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(oldMapDetailCodeAndName.keySet())
                || CollectionUtils.isEmpty(newDetailCodeSet)) {
            Validate.isTrue(false, "定价维度信息异常");
        }
        SetView<String> difference =
                Sets.difference(oldMapDetailCodeAndName.keySet(), newDetailCodeSet);
        if (difference.isEmpty()) {
            return priceType;
        }
        Boolean existsPriceFlag =
                this.priceService.checkExistsPriceByTypeDetailCodes(Sets.newHashSet(difference));
        if (Boolean.TRUE.equals(existsPriceFlag)) {
            List<String> nameList = Lists.newLinkedList();
            difference.stream().forEach(a -> nameList.add(oldMapDetailCodeAndName.get(a)));
            Validate.isTrue(false, StringUtils.join(nameList, ",") + "已存在配置的价格信息，不能编辑对应的维度信息");
        }
        return priceType;
    }

    /**
     * 保存价格类型扩展信息
     *
     * @param priceType
     */
    @Transactional
    public void savePriceTypeExtInfo(PriceType priceType) {
        Validate.notNull(priceType, "价格类型配置信息不能为空");
        List<PriceTypeDetail> detailList = priceType.getDetailList();
        Validate.isTrue(CollectionUtils.isNotEmpty(detailList), "价格类型定价维度配置信息不能为空");
        int idx = 1;
        for (PriceTypeDetail item : detailList) {
            item.setTypeCode(priceType.getTypeCode());
            Validate.notNull(item.getSort(), "价格类型定价维度配置排序不能为空");
            List<PriceTypeDetailItem> itemList = item.getItemList();
            /*Validate.isTrue(CollectionUtils.isNotEmpty(itemList), idx + "价格类型定价维度配置明细信息不能为空");*/
            Validate.isTrue(CollectionUtils.isNotEmpty(itemList), "价格类型定价维度配置明细信息不能为空");
            Set<String> set =
                    itemList.stream()
                            .filter(a -> StringUtils.isNotBlank(a.getDimensionCode()))
                            .map(PriceTypeDetailItem::getDimensionCode)
                            .collect(Collectors.toSet());
            /*Validate.isTrue(CollectionUtils.isNotEmpty(set), idx + "价格类型定价维度配置明细信息不能为空");*/
            Validate.isTrue(CollectionUtils.isNotEmpty(set), "价格类型定价维度配置明细信息不能为空");
            CombineDimensionVo combineDimensionVo =
                    this.priceDimensionContainerService.getCombineDimensionVo(set);
            /*Validate.notNull(combineDimensionVo, idx + "价格类型定价维度配置明细信息异常");*/
            Validate.notNull(combineDimensionVo, "价格类型定价维度配置明细信息异常");
            item.setTypeDetailCode(
                    StringUtils.join(
                            item.getTypeCode(), PriceConstant.SEPARATOR, combineDimensionVo.getDimensionKey()));
            item.setTypeDetailName(combineDimensionVo.getDimensionValue());
            item.setTenantCode(TenantUtils.getTenantCode());
            for (PriceTypeDetailItem sub : itemList) {
                sub.setTypeCode(priceType.getTypeCode());
                sub.setTypeDetailCode(item.getTypeDetailCode());
                sub.setTenantCode(TenantUtils.getTenantCode());
                Validate.notBlank(sub.getDimensionCode(), item.getTypeDetailName() + "存在异常维度配置信息");
            }
        }
        this.priceTypeDetailService.saveBatch(detailList, priceType.getTypeCode());
    }

    @Override
    @Transactional
    public void updateDelFlagByIds(List<String> ids) {
        Validate.isTrue(CollectionUtils.isNotEmpty(ids), "id集合不能为空");
        List<PriceType> priceTypes = priceTypeRepository.findByIdIn(ids);
        List<String> collect = priceTypes.stream().map(PriceType::getTypeCode).collect(Collectors.toList());
        List<Price> prices = priceService.findByTypeCodes(collect);
        Validate.isTrue(CollectionUtils.isEmpty(prices), "价格设置已被使用，请勿删除");
        this.priceTypeRepository.updateDelFlagByIds(ids);
        //保存日志
        List<PriceTypeVo> voList = (List<PriceTypeVo>) this.nebulaToolkitService.copyCollectionByWhiteList(priceTypes, PriceType.class, PriceTypeVo.class, HashSet.class, ArrayList.class);
        if (CollectionUtils.isNotEmpty(voList)) {
            voList.forEach(priceTypeVo -> {
                PriceTypeEventDto priceTypeEventDto = new PriceTypeEventDto();
                priceTypeEventDto.setNewest(null);
                priceTypeEventDto.setOriginal(priceTypeVo);
                SerializableBiConsumer<PriceTypeLogEventListener, PriceTypeEventDto> onDelete = PriceTypeLogEventListener::onDelete;
                this.nebulaNetEventClient.publish(priceTypeEventDto, PriceTypeLogEventListener.class, onDelete);
            });
        }
    }

    private void createValidation(PriceType priceType) {
        priceType.setId(null);
        this.validation(priceType);
    }

    private void updateValidation(PriceType priceType) {
        Validate.notBlank(priceType.getId(), "价格类型id不能为空");
        this.validation(priceType);
    }

    private void validation(PriceType priceType) {
        if (StringUtils.isNotBlank(priceType.getTypeCode())) {
            priceType.setTypeCode(StringUtils.lowerCase(priceType.getTypeCode()));
        }
        Validate.isTrue(
                StringUtils.isNotBlank(priceType.getTypeCode()) && priceType.getTypeCode().length() <= 64,
                "价格类型编码不能为空且不能超过64个字");
        Validate.isTrue(
                StringUtils.isNotBlank(priceType.getTypeName()) && priceType.getTypeName().length() <= 64,
                "价格类型名称不能为空且不能超过64个字");
        if (StringUtils.isNotBlank(priceType.getRemark())) {
            Validate.isTrue(priceType.getRemark().length() <= 400, "备注信息不能超过400个字");
        }
        Validate.isTrue(CollectionUtils.isNotEmpty(priceType.getDetailList()), "定价维度配置信息不能为空");
        for (PriceTypeDetail item : priceType.getDetailList()) {
            Optional<PriceTypeDetailItem> first =
                    item.getItemList().stream()
                            .filter(a -> StringUtils.isBlank(a.getDimensionCode()))
                            .findFirst();
            Validate.isTrue(!first.isPresent(), "定价维度信息不能为空");
            Set<String> typeDetailCodeSet = item.getItemList().stream().map(PriceTypeDetailItem::getDimensionCode).collect(Collectors.toSet());
            Validate.isTrue(typeDetailCodeSet.contains(PriceDimensionEnum.PRODUCT.getDictCode()), "每个定价维度都必含商品维度!");
        }
    }
}
