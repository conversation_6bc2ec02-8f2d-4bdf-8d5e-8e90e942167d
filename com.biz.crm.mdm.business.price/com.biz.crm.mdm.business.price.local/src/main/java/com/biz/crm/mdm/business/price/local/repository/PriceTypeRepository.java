package com.biz.crm.mdm.business.price.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.mdm.business.price.local.entity.PriceType;
import com.biz.crm.mdm.business.price.local.mapper.PriceTypeMapper;
import com.biz.crm.mdm.business.price.sdk.dto.PriceTypePaginationDto;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 价格类型主信息(repository)
 *
 * <AUTHOR>
 * @date 2021-12-30 17:46:11
 */
@Component
public class PriceTypeRepository extends ServiceImpl<PriceTypeMapper, PriceType> {

  /**
   * 分页
   *
   * @param page
   * @param dto
   * @return
   */
  public Page<PriceType> findByConditions(Page<PriceType> page, PriceTypePaginationDto dto) {
    dto.setTenantCode(TenantUtils.getTenantCode());
    return this.baseMapper.findByConditions(page, dto);
  }

  /**
   * 根据id获取详情
   *
   * @param id
   * @return
   */
  public PriceType findById(String id) {
    return this.lambdaQuery()
        .eq(PriceType::getTenantCode, TenantUtils.getTenantCode())
        .eq(PriceType::getId, id)
        .one();
  }

  /**
   * 根据typeCode获取详情
   *
   * @param typeCode
   * @return
   */
  public PriceType findByTypeCode(String typeCode) {
    return this.lambdaQuery()
        .eq(PriceType::getTenantCode, TenantUtils.getTenantCode())
        .eq(PriceType::getTypeCode, typeCode)
        .one();
  }

  /**
   * 逻辑删除
   *
   * @param ids
   */
  public void updateDelFlagByIds(List<String> ids) {
    this.lambdaUpdate()
        .in(PriceType::getId, ids)
        .eq(PriceType::getTenantCode, TenantUtils.getTenantCode())
        .set(PriceType::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
        .update();
  }

  /**
   * 根据编码统计数量
   *
   * @param typeCode
   * @return
   */
  public Integer countByTypeCode(String typeCode) {
    return this.lambdaQuery()
        .eq(PriceType::getTenantCode, TenantUtils.getTenantCode())
        .eq(PriceType::getTypeCode, typeCode)
        .count();
  }

  /**
   * 通过id查询
   * @param ids
   * @return
   */
  public List<PriceType> findByIdIn(List<String> ids) {
    return this.lambdaQuery()
            .eq(PriceType::getTenantCode, TenantUtils.getTenantCode())
            .in(PriceType::getId, ids)
            .list();
  }
}
