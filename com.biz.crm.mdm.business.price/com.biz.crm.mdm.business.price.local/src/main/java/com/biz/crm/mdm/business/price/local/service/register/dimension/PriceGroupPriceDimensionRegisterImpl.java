package com.biz.crm.mdm.business.price.local.service.register.dimension;

import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.price.sdk.enums.PriceDimensionEnum;
import com.biz.crm.mdm.business.price.sdk.register.PriceDimensionRegister;
import com.biz.crm.mdm.business.price.sdk.vo.PriceDimensionSelectVo;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 价格组【数据字典price_group】-价格维度注册器实现
 *
 * <AUTHOR>
 * @date 2022/1/4
 */
@Component
public class PriceGroupPriceDimensionRegisterImpl implements PriceDimensionRegister {

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;


    @Override
    public String getCode() {
        return PriceDimensionEnum.PRICE_GROUP.getDictCode();
    }

    @Override
    public String getDesc() {
        return PriceDimensionEnum.PRICE_GROUP.getValue();
    }

    @Override
    public int sort() {
        return PriceDimensionEnum.PRICE_GROUP.getOrder();
    }

    @Override
    public List<PriceDimensionSelectVo> findSelectVoByKeyword(String keyword, String selectedCode) {
        List<DictDataVo> list = this.dictDataVoService.findTreeByDictTypeCode(DictConstant.PRICE_GROUP);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newLinkedList();
        }
        if (StringUtil.isNotEmpty(keyword)) {
            list = list.stream()
                    .filter(k -> StringUtils.isNotEmpty(k.getDictCode()))
                    .filter(k -> StringUtils.isNotEmpty(k.getDictValue()))
                    .filter(k -> k.getDictCode().contains(keyword)
                            || k.getDictValue().contains(keyword)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list)) {
                return Lists.newLinkedList();
            }
        }
        List<PriceDimensionSelectVo> selectVoList = Lists.newArrayList();
        for (DictDataVo item : list) {
            PriceDimensionSelectVo cur = new PriceDimensionSelectVo();
            cur.setCode(item.getDictCode());
            cur.setName(item.getDictValue());
            selectVoList.add(cur);
        }
        return selectVoList;
    }
}
