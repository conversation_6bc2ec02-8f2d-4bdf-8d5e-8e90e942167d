package com.biz.crm.mdm.business.price.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.price.local.entity.PriceType;
import com.biz.crm.mdm.business.price.sdk.dto.PriceTypePaginationDto;
import org.apache.ibatis.annotations.Param;

/**
 * 价格类型主信息(PriceType)表数据库访问层
 *
 * <AUTHOR>
 * @date 2021-12-30 17:46:10
 */
public interface PriceTypeMapper extends BaseMapper<PriceType> {
  
  /**
   * 分页列表
   *
   * @param page 分页信息
   * @param dto 分页参数dto
   * @return 分页列表
   */
  Page<PriceType> findByConditions(Page<PriceType> page, @Param("dto") PriceTypePaginationDto dto);
  
}
