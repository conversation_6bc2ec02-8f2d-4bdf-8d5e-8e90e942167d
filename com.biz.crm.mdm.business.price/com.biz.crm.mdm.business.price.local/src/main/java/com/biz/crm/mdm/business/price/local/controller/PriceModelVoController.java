package com.biz.crm.mdm.business.price.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.price.sdk.dto.FindBusinessPriceDto;
import com.biz.crm.mdm.business.price.sdk.dto.FindPriceDto;
import com.biz.crm.mdm.business.price.sdk.dto.SearchPriceDto;
import com.biz.crm.mdm.business.price.sdk.service.PriceModelVoService;
import com.biz.crm.mdm.business.price.sdk.vo.PriceModelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 价格维护主信息: PriceVo: 询价
 *
 * <AUTHOR>
 * @date 2021-12-30 17:46:07
 */
@Slf4j
@Api(tags = "价格维护主信息: PriceModelVo: 询价")
@RestController
@RequestMapping(value = {"/v1/price/price"})
public class PriceModelVoController {

    @Autowired(required = false)
    private PriceModelVoService priceModelVoService;

    /**
     * 查询价格
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "查询价格")
    @PostMapping(value = {"/handleSearchPrice"})
    public Result<Map<String, PriceModelVo>> handleSearchPrice(@RequestBody SearchPriceDto dto) {
        try {
            Map<String, PriceModelVo> map = this.priceModelVoService.handleSearchPrice(dto);
            return Result.ok(map);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 询价
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "询价")
    @PostMapping(value = {"/findPrice"})
    public Result<Map<String, PriceModelVo>> findPrice(@RequestBody FindPriceDto dto) {
        try {
            Map<String, PriceModelVo> map = this.priceModelVoService.findPrice(dto);
            return Result.ok(map);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "询价new")
    @PostMapping(value = {"/findPriceNew"})
    public Result<Map<String, PriceModelVo> > findPriceNew(@RequestBody FindPriceDto dto) {
        try {
            return Result.ok(this.priceModelVoService.findPriceNew(dto));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "询成本价")
    @PostMapping(value = {"/findMaterialUnitPrice"})
    public Result<Map<String, PriceModelVo> > findMaterialUnitPrice(@RequestBody FindPriceDto dto) {
        try {
            return Result.ok(this.priceModelVoService.findMaterialUnitPrice(dto));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }



    /**
     * 根据业务编码及商品维度询价
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "根据业务编码及商品维度询价")
    @PostMapping(value = {"/findBusinessPrice"})
    public Result<Map<String, PriceModelVo>> findBusinessPrice(@RequestBody FindBusinessPriceDto dto) {
        try {
            Map<String, PriceModelVo> map = this.priceModelVoService.findBusinessPrice(dto);
            return Result.ok(map);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
