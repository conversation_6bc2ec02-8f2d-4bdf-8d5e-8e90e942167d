package com.biz.crm.mdm.business.price.local.service.register.dimension;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.org.sdk.common.enums.OrgTypeEnum;
import com.biz.crm.mdm.business.org.sdk.dto.OrgPaginationDto;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.price.sdk.enums.PriceDimensionEnum;
import com.biz.crm.mdm.business.price.sdk.register.PriceDimensionRegister;
import com.biz.crm.mdm.business.price.sdk.vo.PriceDimensionSelectVo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 销售部门【大区】-价格维度注册器实现
 *
 * <AUTHOR>
 * @date 2022/1/4
 */
@Component
public class OrgDepartmentDimensionRegisterImpl implements PriceDimensionRegister {

    @Autowired(required = false)
    private OrgVoService orgVoService;

    @Override
    public String getCode() {
        return PriceDimensionEnum.ORG_DEPARTMENT.getDictCode();
    }

    @Override
    public String getDesc() {
        return PriceDimensionEnum.ORG_DEPARTMENT.getValue();
    }

    @Override
    public int sort() {
        return PriceDimensionEnum.ORG_DEPARTMENT.getOrder();
    }

    @Override
    public List<PriceDimensionSelectVo> findSelectVoByKeyword(String keyword,String selectedCode) {
        Pageable pageable = PageRequest.of(1, 20);
        OrgPaginationDto dto = new OrgPaginationDto();
        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        dto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        dto.setOrgType(OrgTypeEnum.DIVISION.getDictCode());
        dto.setKeyword(keyword);
        dto.setSelectedCode(selectedCode);
        Page<OrgVo> orgVoPage = this.orgVoService.findOrgByConditions(pageable, dto);
        if (CollectionUtils.isEmpty(orgVoPage.getRecords())) {
            return Lists.newLinkedList();
        }
        List<PriceDimensionSelectVo> selectVoList = Lists.newArrayList();
        for (OrgVo item : orgVoPage.getRecords()) {
            PriceDimensionSelectVo cur = new PriceDimensionSelectVo();
            cur.setCode(item.getOrgCode());
            cur.setName(item.getOrgName());
            selectVoList.add(cur);
        }
        return selectVoList;
    }
}
