package com.biz.crm.mdm.business.price.local.service.register.item;

import com.biz.crm.mdm.business.price.sdk.dto.FindPriceDto;
import com.biz.crm.mdm.business.price.sdk.dto.SearchPriceDimensionItemDto;
import com.biz.crm.mdm.business.price.sdk.enums.FindPriceUserTypeEnum;
import com.biz.crm.mdm.business.price.sdk.enums.PriceDimensionEnum;
import com.biz.crm.mdm.business.price.sdk.register.UserSearchPriceDimensionItemRegister;
import com.biz.crm.mdm.business.terminal.sdk.service.TerminalVoService;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalRelaOrgVo;
import com.biz.crm.mdm.business.terminal.sdk.vo.TerminalVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 终端询价维度信息注册器实现
 *
 * <AUTHOR>
 * @date 2022/3/15
 */
@Component
public class TerminalPriceDimensionItemRegister implements UserSearchPriceDimensionItemRegister {

    @Autowired(required = false)
    private TerminalVoService terminalVoService;

    @Override
    public String getCode() {
        return FindPriceUserTypeEnum.TERMINAL.getDictCode();
    }

    /**
     * 获取数据 终端编码 渠道 组织
     *
     * @param dto 经销商编码、终端编码等
     * @return
     */
    @Override
    public List<SearchPriceDimensionItemDto> findSearchPriceDimensionItems(FindPriceDto dto) {
        if (Objects.isNull(dto)) {
            return Lists.newArrayList();
        }
        String userCode = dto.getUserCode();
        if (StringUtils.isBlank(userCode)) {
            return Lists.newLinkedList();
        }
        List<TerminalVo> terminalVoList =
                this.terminalVoService.findMainDetailsByTerminalCodes(Lists.newArrayList(userCode));
        if (CollectionUtils.isEmpty(terminalVoList)) {
            return Lists.newLinkedList();
        }
        Optional<TerminalVo> optional = terminalVoList.stream().findFirst();
        if (!optional.isPresent()) {
            return Lists.newLinkedList();
        }
        List<SearchPriceDimensionItemDto> dimensionItems = Lists.newLinkedList();
        SearchPriceDimensionItemDto item = new SearchPriceDimensionItemDto();
        item.setDimensionCode(PriceDimensionEnum.TERMINAL.getDictCode());
        item.setRelateCodeSet(Sets.newHashSet(userCode));
        dimensionItems.add(item);
        TerminalVo terminalVo = optional.get();
        if (CollectionUtils.isNotEmpty(terminalVo.getOrgList())) {
            SearchPriceDimensionItemDto cur = new SearchPriceDimensionItemDto();
            cur.setDimensionCode(PriceDimensionEnum.ORG.getDictCode());
            cur.setRelateCodeSet(
                    terminalVo.getOrgList().stream()
                            .filter(a -> StringUtils.isNotBlank(a.getOrgCode()))
                            .map(TerminalRelaOrgVo::getOrgCode)
                            .collect(Collectors.toSet()));
            dimensionItems.add(cur);
        }
        if (StringUtils.isNotBlank(terminalVo.getChannel())) {
            SearchPriceDimensionItemDto cur = new SearchPriceDimensionItemDto();
            cur.setDimensionCode(PriceDimensionEnum.CHANNEL.getDictCode());
            cur.setRelateCodeSet(Sets.newHashSet(terminalVo.getChannel()));
            dimensionItems.add(cur);
        }
        return dimensionItems;
    }
}
