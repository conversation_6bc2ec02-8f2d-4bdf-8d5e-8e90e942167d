package com.biz.crm.mdm.business.price.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.mdm.business.price.local.entity.PriceDimension;
import com.biz.crm.mdm.business.price.local.repository.PriceDimensionRepository;
import com.biz.crm.mdm.business.price.local.service.PriceDimensionService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * 价格设置维度配置明细(PriceDimension)表服务实现类
 *
 * <AUTHOR>
 * @date 2021-12-30 17:46:09
 */
@Service("priceDimensionService")
public class PriceDimensionServiceImpl implements PriceDimensionService {

    @Autowired(required = false)
    private PriceDimensionRepository priceDimensionRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<PriceDimension> list, String priceCode) {
        Validate.notBlank(priceCode, "价格编码不能为空");
        this.priceDimensionRepository.deleteByPriceCode(priceCode);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<String> set = Sets.newHashSet();
        list.forEach(
                a -> {
                    a.setPriceCode(priceCode);
                    a.setTenantCode(TenantUtils.getTenantCode());
                    Validate.notBlank(a.getTypeCode(), "价格类型编码不能为空");
                    Validate.notBlank(a.getTypeDetailCode(), "定价维度编码不能为空");
                    Validate.notBlank(a.getDimensionCode(), "维度编码不能为空");
                    Validate.notBlank(a.getRelateCode(), "维度对应值编码不能为空");
                    Validate.notBlank(a.getRelateName(), "维度对应值不能为空");
                    Validate.isTrue(set.add(a.getDimensionCode()), a.getDimensionCode() + "维度编码存在重复");
                });
        this.priceDimensionRepository.saveBatch(list);
    }

    /**
     * 批量保存
     *
     * @param list
     * @param priceCodes
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/4 14:53
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<PriceDimension> list, List<String> priceCodes) {
        if (CollectionUtil.isNotEmpty(priceCodes)) {
            this.priceDimensionRepository.deleteByPriceCodes(priceCodes);
        }
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                item.setId(UuidCrmUtil.general());
            });
            this.priceDimensionRepository.saveBatchXml(list);
        }
    }

    @Override
    public List<PriceDimension> findByPriceCodes(List<String> priceCodeList) {
        if (CollectionUtils.isEmpty(priceCodeList)) {
            return Lists.newLinkedList();
        }
        return this.priceDimensionRepository.findByPriceCodes(priceCodeList);
    }
}
