package com.biz.crm.mdm.business.price.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 价格设置维度配置明细(PriceDimension)实体类
 *
 * <AUTHOR>
 * @since 2021-12-30 17:46:07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("mdm_price_dimension")
@Table(name = "mdm_price_dimension", indexes = {
        @Index(name = "mdm_price_dimension_index1", columnList = "price_code"),
        @Index(name = "mdm_price_dimension_index2", columnList = "dimension_code"),
        @Index(name = "mdm_price_dimension_index3", columnList = "relate_code"),
        @Index(name = "mdm_price_dimension_index4", columnList = "type_detail_code"),
        @Index(name = "mdm_price_dimension_index5", columnList = "tenant_code,price_code"),
        @Index(name = "mdm_price_dimension_index6", columnList = "tenant_code,type_detail_code"),
})
@ApiModel(value = "PriceDimension", description = "价格设置维度配置明细")
@org.hibernate.annotations.Table(appliesTo = "mdm_price_dimension", comment = "价格设置维度配置明细")
public class PriceDimension extends TenantEntity {

    private static final long serialVersionUID = -258146128738429740L;

    @ApiModelProperty("价格编码")
    @TableField(value = "price_code")
    @Column(name = "price_code", columnDefinition = "varchar(32) COMMENT '价格编码'")
    private String priceCode;

    @ApiModelProperty("SAP价格编码")
    @Column(name = "sap_price_code", columnDefinition = "varchar(32) COMMENT 'SAP价格编码'")
    private String sapPriceCode;

    @ApiModelProperty("价格类型编码")
    @TableField(value = "type_code")
    @Column(name = "type_code", columnDefinition = "varchar(64) COMMENT '价格类型编码'")
    private String typeCode;

    @ApiModelProperty("定价维度编码")
    @TableField(value = "type_detail_code")
    @Column(name = "type_detail_code", columnDefinition = "varchar(100) COMMENT '定价维度编码'")
    private String typeDetailCode;

    @ApiModelProperty("维度编码")
    @TableField(value = "dimension_code")
    @Column(name = "dimension_code", columnDefinition = "varchar(32) COMMENT '维度编码'")
    private String dimensionCode;

    @ApiModelProperty("维度数据源编码")
    @TableField(value = "relate_code")
    @Column(name = "relate_code", columnDefinition = "varchar(32) COMMENT '维度数据源编码'")
    private String relateCode;

    @ApiModelProperty("维度数据源SAP编码")
    @TableField(value = "relate_sap_code")
    @Column(name = "relate_sap_code", columnDefinition = "varchar(32) COMMENT '维度数据源SAP编码'")
    private String relateSapCode;

    @ApiModelProperty("维度数据源名称（冗余）")
    @TableField(value = "relate_name")
    @Column(name = "relate_name", columnDefinition = "varchar(256) COMMENT '维度数据源名称（冗余）'")
    private String relateName;


    @ApiModelProperty("客户的组织编码")
    @TableField(exist = false)
    @Transient
    private String orgCode;

    @ApiModelProperty("是否合同客户")
    @TableField(exist = false)
    @Transient
    private String contractCustomer;

    @ApiModelProperty("公司名称")
    @TableField(exist = false)
    @Transient
    private String companyName;
}
