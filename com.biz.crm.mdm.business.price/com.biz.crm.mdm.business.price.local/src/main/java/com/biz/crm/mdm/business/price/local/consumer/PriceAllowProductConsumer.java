package com.biz.crm.mdm.business.price.local.consumer;

import com.alibaba.fastjson.JSONArray;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.rocketmq.service.AbstractRocketMqConsumer;
import com.biz.crm.business.common.rocketmq.vo.MqMessageVo;
import com.biz.crm.mdm.business.price.local.repository.PriceRepository;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @describe: mq消费示例
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2022.10.14 10:09
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MqConstant.TOPIC_DMS_ALLOW_PRODUCT_ORDER + "${rocketmq.environment}",
        /**
         * tag
         * 可用 || 监听多个tag： "tag1 || tag2 || tag3"
         * 请把tag  定义在 *** 内 需要统一维护
        */
        selectorExpression = MqConstant.MDM_PRICE_DMS_ALLOW_PRODUCT_CALL_BACK,
        /**
         * 相同分组下 consumer 可自动负载均衡
         * 请把consumerGroup  定义在 *** 内 需要统一维护
        */
        consumerGroup = MqConstant.MDM_PRICE_DMS_ALLOW_PRODUCT_CALL_BACK_GROUP + "${rocketmq.environment}",
        /**
         * 默认集群消费
         * 可以设置 ConsumeMode.ORDERLY 使用广播消费
         * 也可使用集群模式模拟广播模式：
         * 启动多个不同 consumerGroup 的consumer实例
        */
        consumeMode = ConsumeMode.ORDERLY,
        /**
         * 集群消费or广播消费;默认是集群消费
        */
        messageModel = MessageModel.CLUSTERING)
public class PriceAllowProductConsumer extends AbstractRocketMqConsumer {

    @Autowired(required = false)
    private PriceRepository priceRepository;

    @Override
    protected Object handleMessage(MqMessageVo message) {
        log.info("order mq message received  : {}", message);
        if (Objects.isNull(message)
                || StringUtil.isEmpty(message.getMsgBody())) {
            return "消息内容为空!";
        }
        priceRepository.updatePriceAllow(JSONArray.parseArray(message.getMsgBody(), String.class));
        return "顺序消息消费成功.";
    }
}