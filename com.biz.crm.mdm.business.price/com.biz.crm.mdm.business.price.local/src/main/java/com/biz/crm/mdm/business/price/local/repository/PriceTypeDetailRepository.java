package com.biz.crm.mdm.business.price.local.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.price.local.entity.PriceTypeDetail;
import com.biz.crm.mdm.business.price.local.mapper.PriceTypeDetailMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import java.util.List;

import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

/**
 * 价格类型维度信息(repository)
 *
 * <AUTHOR>
 * @date 2021-12-30 17:46:13
 */
@Component
public class PriceTypeDetailRepository extends ServiceImpl<PriceTypeDetailMapper, PriceTypeDetail> {

  public void deleteByTypeCode(String typeCode) {
    LambdaQueryWrapper<PriceTypeDetail> lambdaQuery = Wrappers.lambdaQuery();
    lambdaQuery
        .eq(PriceTypeDetail::getTenantCode, TenantUtils.getTenantCode())
        .in(PriceTypeDetail::getTypeCode, typeCode);
    this.baseMapper.delete(lambdaQuery);
  }

  public List<PriceTypeDetail> findByTypeCodes(List<String> typeCodeList) {
    if(CollectionUtil.isEmpty(typeCodeList)){
      return Lists.newArrayList();
    }
    return this.lambdaQuery()
        .eq(PriceTypeDetail::getTenantCode, TenantUtils.getTenantCode())
        .in(PriceTypeDetail::getTypeCode, typeCodeList)
        .orderByAsc(PriceTypeDetail::getSort)
        .list();
  }

  /**
   * 根据价格类型编码+明细名称模糊查询
   *
   * @param typeCode
   * @param typeDetailName
   * @return
   */
  public List<PriceTypeDetail> findByTypeCodeAndTypeDetailNameLike(
      String typeCode, String typeDetailName) {
    return this.lambdaQuery()
        .eq(PriceTypeDetail::getTenantCode, TenantUtils.getTenantCode())
        .eq(PriceTypeDetail::getTypeCode, typeCode)
        .like(
            StringUtils.isNotBlank(typeDetailName),
            PriceTypeDetail::getTypeDetailName,
            typeDetailName)
        .orderByAsc(PriceTypeDetail::getSort)
        .list();
  }
}
