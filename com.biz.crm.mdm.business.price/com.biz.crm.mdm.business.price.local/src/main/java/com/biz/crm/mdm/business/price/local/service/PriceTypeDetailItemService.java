package com.biz.crm.mdm.business.price.local.service;

import com.biz.crm.mdm.business.price.local.entity.PriceTypeDetailItem;
import java.util.List;

/**
 * 价格类型维度配置明细信息(PriceTypeDetailItem)表服务接口
 *
 * <AUTHOR>
 * @date 2021-12-30 17:46:15
 */
public interface PriceTypeDetailItemService {

  /**
   * 批量保存
   *
   * @param list
   * @param typeCode
   */
  void saveBatch(List<PriceTypeDetailItem> list, String typeCode);

  /**
   * 根据价格编码获取对应的维度配置信息
   *
   * @param typeCodeList
   * @return
   */
  List<PriceTypeDetailItem> findByTypeCodes(List<String> typeCodeList);

  /**
   * 根据typeDetailCode对应的维度配置信息
   *
   * @param typeDetailCodeList
   * @return
   */
  List<PriceTypeDetailItem> findByTypeDetailCodes(List<String> typeDetailCodeList);
}
