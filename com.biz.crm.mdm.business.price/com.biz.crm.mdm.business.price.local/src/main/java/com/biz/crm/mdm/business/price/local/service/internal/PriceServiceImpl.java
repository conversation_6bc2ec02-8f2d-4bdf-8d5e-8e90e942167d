package com.biz.crm.mdm.business.price.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.base.util.UuidCrmUtil;
import com.biz.crm.business.common.rocketmq.constant.MqConstant;
import com.biz.crm.business.common.rocketmq.service.RocketMqProducer;
import com.biz.crm.business.common.rocketmq.util.RocketMqUtil;
import com.biz.crm.business.common.rocketmq.vo.MqMessageVo;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.GenerateCodeService;
import com.biz.crm.mdm.business.dictionary.sdk.constant.DictConstant;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.price.local.entity.*;
import com.biz.crm.mdm.business.price.local.repository.PriceLogRepository;
import com.biz.crm.mdm.business.price.local.repository.PriceRepository;
import com.biz.crm.mdm.business.price.local.repository.PriceTypeRepository;
import com.biz.crm.mdm.business.price.local.service.PriceDimensionService;
import com.biz.crm.mdm.business.price.local.service.PriceService;
import com.biz.crm.mdm.business.price.local.service.PriceTypeDetailItemService;
import com.biz.crm.mdm.business.price.sdk.constant.PriceConstant;
import com.biz.crm.mdm.business.price.sdk.dto.PriceAllowProductDto;
import com.biz.crm.mdm.business.price.sdk.dto.PriceEventDto;
import com.biz.crm.mdm.business.price.sdk.dto.PricePaginationDto;
import com.biz.crm.mdm.business.price.sdk.dto.PriceSearchDimensionDto;
import com.biz.crm.mdm.business.price.sdk.enums.EffectiveStatusEnum;
import com.biz.crm.mdm.business.price.sdk.enums.PriceDimensionEnum;
import com.biz.crm.mdm.business.price.sdk.enums.SapPriceDimensionEnum;
import com.biz.crm.mdm.business.price.sdk.event.PriceLogEventListener;
import com.biz.crm.mdm.business.price.sdk.service.PriceDimensionContainerService;
import com.biz.crm.mdm.business.price.sdk.vo.PriceDimensionDictVo;
import com.biz.crm.mdm.business.price.sdk.vo.PriceDimensionVo;
import com.biz.crm.mdm.business.price.sdk.vo.PriceVo;
import com.biz.crm.mdm.business.product.sdk.dto.ProductQueryDto;
import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.bizunited.nebula.event.sdk.function.SerializableBiConsumer;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 价格维护主信息(Price)表服务实现类
 *
 * <AUTHOR>
 * @date 2021-12-30 17:46:06
 */
@Service("priceService")
@Slf4j
public class PriceServiceImpl implements PriceService {

    @Autowired(required = false)
    private PriceRepository priceRepository;

    @Autowired(required = false)
    private PriceTypeRepository priceTypeRepository;

    @Autowired(required = false)
    private GenerateCodeService generateCodeService;

    @Autowired(required = false)
    private PriceDimensionService priceDimensionService;

    @Autowired(required = false)
    private PriceTypeDetailItemService priceTypeDetailItemService;

    @Autowired(required = false)
    private PriceDimensionContainerService priceDimensionContainerService;

    @Autowired(required = false)
    private PriceLogRepository priceLogRepository;

    @Autowired(required = false)
    private ProductVoService productVoService;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;

    @Autowired(required = false)
    private RocketMqProducer rocketMqProducer;

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;


    /**
     * 价格分页查询
     *
     * @param pageable
     * @param dto
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.biz.crm.mdm.business.price.sdk.vo.PriceVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/23 11:10
     */
    @Override
    public Page<PriceVo> findByConditionsNew(Pageable pageable, PricePaginationDto dto) {
        Page<PriceVo> pageResult = this.priceRepository.findByConditionsNew(pageable, dto);
        if (Objects.isNull(pageResult)) {
            return new Page<>();
        }
        if (CollectionUtil.isEmpty(pageResult.getRecords())) {
            return pageResult;
        }
        Set<String> priceCodeSet = pageResult.getRecords().stream()
                .filter(a -> StringUtils.isNotBlank(a.getPriceCode()))
                .map(PriceVo::getPriceCode)
                .collect(Collectors.toSet());
        List<PriceDimension> list = priceDimensionService.findByPriceCodes(Lists.newArrayList(priceCodeSet));
        Map<String, List<PriceDimensionVo>> map = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(list)) {
            map.putAll(nebulaToolkitService.copyCollectionByWhiteList(list,
                    PriceDimension.class, PriceDimensionVo.class, HashSet.class, ArrayList.class).stream()
                    .filter(a -> StringUtils.isNotBlank(a.getPriceCode()))
                    .collect(Collectors.groupingBy(PriceDimensionVo::getPriceCode)));
        }
        List<String> dictTypeCodeList = Lists.newArrayList();
        dictTypeCodeList.add(DictConstant.MDM_PRICE_ORDER_FACTORY);
        dictTypeCodeList.add(DictConstant.PRICE_GROUP);
        dictTypeCodeList.add(DictConstant.MDM_COMPANY);
        dictTypeCodeList.add(DictConstant.MDM_SAP_CHANNEL);
        Map<String, Map<String, String>> allMap = this.dictDataVoService.findMapByDictTypeCodeList(dictTypeCodeList);

        Map<String, String> priceOrderFactoryMap = allMap.getOrDefault(DictConstant.MDM_PRICE_ORDER_FACTORY, Maps.newHashMap());
        Map<String, String> priceGroupMap = allMap.getOrDefault(DictConstant.PRICE_GROUP, Maps.newHashMap());
        Map<String, String> companyMap = allMap.getOrDefault(DictConstant.MDM_COMPANY, Maps.newHashMap());
        Map<String, String> channelMap = allMap.getOrDefault(DictConstant.MDM_SAP_CHANNEL, Maps.newHashMap());
        for (PriceVo price : pageResult.getRecords()) {
            price.setEffectiveStatus(this.findEffectiveStatus(price.getBeginTime(), price.getEndTime()));
            List<PriceDimensionVo> priceDimensionVos = map.getOrDefault(price.getPriceCode(), Lists.newArrayList());
            price.setDimensionList(priceDimensionVos);
            priceDimensionVos.forEach(dimension -> {
                PriceDimensionEnum dimensionEnum = PriceDimensionEnum.getByKey(dimension.getDimensionCode());
                if (Objects.isNull(dimensionEnum)) {
                    return;
                }
                switch (dimensionEnum) {
                    case PRICE_GROUP:
                        price.setPriceGroupCode(dimension.getRelateCode());
                        price.setPriceGroupName(priceGroupMap.getOrDefault(dimension.getRelateCode(), ""));
                        break;
                    case CUSTOMER:
                        price.setCustomerCode(dimension.getRelateCode());
                        price.setCustomerName(dimension.getRelateName());
                        break;
                    case ORG_GROUP:
                        price.setOrgGroupCode(dimension.getRelateCode());
                        price.setOrgGroupName(dimension.getRelateName());
                        break;
                    case ORG_DEPARTMENT:
                        price.setOrgDepartmentCode(dimension.getRelateCode());
                        price.setOrgDepartmentName(dimension.getRelateName());
                        break;
                    case ORDER_FACTORY:
                        price.setOrderFactoryCode(dimension.getRelateCode());
                        price.setOrderFactoryName(priceOrderFactoryMap.getOrDefault(dimension.getRelateCode(), ""));
                        break;
                    case PRODUCT:
                        price.setProductCode(dimension.getRelateCode());
                        price.setProductName(dimension.getRelateName());
                        break;
                    case ORG_INSTITUTION:
                        price.setCompanyCode(dimension.getRelateCode());
                        price.setCompanyName(companyMap.getOrDefault(dimension.getRelateCode(), ""));
                        break;
                    case CHANNEL:
                        price.setChannelCode(dimension.getRelateCode());
                        price.setChannelName(channelMap.getOrDefault(dimension.getRelateCode(), ""));
                        break;
                    default:
                        break;
                }
            });
        }
        return pageResult;
    }

    @Override
    public Page<Price> findByConditions(Pageable pageable, PricePaginationDto dto) {
        if (!StringUtils.isAllBlank(dto.getProductCode(), dto.getProductName())) {
            final ProductQueryDto queryDto = new ProductQueryDto();
            queryDto.setProductCode(dto.getProductCode());
            queryDto.setProductName(dto.getProductName());
            queryDto.setTenantCode(TenantUtils.getTenantCode());
            final List<ProductVo> productVoList = this.productVoService.findByProductQueryDto(queryDto);
            if (CollectionUtils.isEmpty(productVoList)) {
                return new Page<>();
            }
            dto.setProductCodeSet(
                    productVoList.stream()
                            .map(ProductVo::getProductCode)
                            .limit(500)
                            .collect(Collectors.toSet()));
        }
        Page<Price> pageResult = this.priceRepository.findByConditions(pageable, dto);
        if (Objects.nonNull(pageResult) && CollectionUtils.isNotEmpty(pageResult.getRecords())) {
            Set<String> priceCodeSet =
                    pageResult.getRecords().stream()
                            .filter(a -> StringUtils.isNotBlank(a.getPriceCode()))
                            .map(Price::getPriceCode)
                            .collect(Collectors.toSet());
            List<PriceDimension> list = priceDimensionService.findByPriceCodes(Lists.newArrayList(priceCodeSet));
            Map<String, List<PriceDimension>> map =
                    list.stream()
                            .filter(a -> StringUtils.isNotBlank(a.getPriceCode()))
                            .collect(Collectors.groupingBy(PriceDimension::getPriceCode));
            for (Price price : pageResult.getRecords()) {
                price.setDimensionList(map.get(price.getPriceCode()));
                price.setEffectiveStatus(this.findEffectiveStatus(price.getBeginTime(), price.getEndTime()));
            }
        }
        return pageResult;
    }

    @Override
    public Price findDetailById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        Price price = this.priceRepository.getById(id);
        if (Objects.isNull(price)) {
            return null;
        }
        if (StringUtils.isNotBlank(price.getTypeCode())) {
            PriceType priceType = this.priceTypeRepository.findByTypeCode(price.getTypeCode());
            if (Objects.nonNull(priceType)) {
                price.setTypeName(priceType.getTypeName());
            }
        }
        List<PriceDimension> dimensionList =
                priceDimensionService.findByPriceCodes(Lists.newArrayList(price.getPriceCode()));
        price.setDimensionList(dimensionList);
        return price;
    }

    @Override
    @Transactional
    public Price create(Price price) {
        this.createValidation(price);
        price.setTenantCode(TenantUtils.getTenantCode());
        price.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        price.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        if (StringUtils.isBlank(price.getPriceCode())) {
            price.setPriceCode(this.generateCodeService.generateCode(PriceConstant.PRICE_CODE_PREFIX));
        } else {
            Integer count = this.priceRepository.countByTypeCode(price.getPriceCode());
            Validate.isTrue(null == count || 1 > count, price.getPriceCode() + "价格编码已存在");
        }
        this.saveDimensionInfo(price);
        this.priceRepository.saveOrUpdate(price);
        PriceLog priceLog = nebulaToolkitService.copyObjectByWhiteList(price, PriceLog.class, HashSet.class, ArrayList.class);
        priceLog.setId(null);
        //新增租户编号
        priceLog.setTenantCode(TenantUtils.getTenantCode());
        this.priceLogRepository.save(priceLog);
        // 业务日志创建
        PriceEventDto priceEventDto = new PriceEventDto();
        PriceVo nowVo = this.nebulaToolkitService.copyObjectByWhiteList(price, PriceVo.class, HashSet.class, ArrayList.class);
        priceEventDto.setNewest(nowVo);
        priceEventDto.setOriginal(null);
        SerializableBiConsumer<PriceLogEventListener, PriceEventDto> onCreate =
                PriceLogEventListener::onCreate;
        this.nebulaNetEventClient.publish(priceEventDto, PriceLogEventListener.class, onCreate);

        return price;
    }

    @Override
    @Transactional
    public Price update(Price price) {
        this.updateValidation(price);
        String currentId = price.getId();
        Price current = priceRepository.getById(currentId);
        Validate.notNull(current, "修改信息不存在");
        price.setTenantCode(TenantUtils.getTenantCode());
        Validate.isTrue(price.getPriceCode().equals(current.getPriceCode()), "价格编码不能修改");
        this.saveDimensionInfo(price);
        this.priceRepository.saveOrUpdate(price);
        PriceLog priceLog = nebulaToolkitService.copyObjectByWhiteList(price, PriceLog.class, HashSet.class, ArrayList.class);
        priceLog.setId(null);
        //新增租户编号
        priceLog.setTenantCode(TenantUtils.getTenantCode());
        this.priceLogRepository.save(priceLog);
        // 业务日志更新
        PriceEventDto priceEventDto = new PriceEventDto();
        PriceVo oldVO = this.nebulaToolkitService.copyObjectByWhiteList(current, PriceVo.class, HashSet.class, ArrayList.class);
        PriceVo nowVo = this.nebulaToolkitService.copyObjectByWhiteList(price, PriceVo.class, HashSet.class, ArrayList.class);
        priceEventDto.setNewest(nowVo);
        priceEventDto.setOriginal(oldVO);
        SerializableBiConsumer<PriceLogEventListener, PriceEventDto> onUpdate = PriceLogEventListener::onUpdate;
        this.nebulaNetEventClient.publish(priceEventDto, PriceLogEventListener.class, onUpdate);
        return price;
    }

    /**
     * 保存价格设置维度配置明细
     *
     * @param price
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveDimensionInfo(Price price) {
        Validate.notNull(price, "价格信息不能为空");
        List<PriceTypeDetailItem> priceTypeDetailItemList = priceTypeDetailItemService.findByTypeDetailCodes(Lists.newArrayList(price.getTypeDetailCode()));
        Validate.isTrue(CollectionUtils.isNotEmpty(priceTypeDetailItemList), "定价维度对应的配置不存在");
        Validate.isTrue(CollectionUtils.isNotEmpty(price.getDimensionList()), "定价维度配置不能为空");
        Set<String> set = price.getDimensionList().stream()
                .filter(a -> StringUtils.isNotBlank(a.getDimensionCode()))
                .map(PriceDimension::getDimensionCode).collect(Collectors.toSet());
        for (PriceTypeDetailItem item : priceTypeDetailItemList) {
            if (!set.contains(item.getDimensionCode())) {
                Validate.isTrue(false, item.getDimensionCode() + "对应的维度配置信息缺失");
            }
        }
        // k-dimensionCode,v-relateCode
        Map<String, String> map = price.getDimensionList().stream()
                .collect(Collectors.toMap(PriceDimension::getDimensionCode, PriceDimension::getRelateCode, (a, b) -> a));
        List<PriceDimensionDictVo> dimensionSelect = this.priceDimensionContainerService.findDimensionSelect();
        List<String> joinCodeList = Lists.newArrayList();
        joinCodeList.add(price.getTypeDetailCode());
        for (PriceDimensionDictVo item : dimensionSelect) {
            if (map.containsKey(item.getCode())) {
                joinCodeList.add(map.get(item.getCode()));
            }
        }
        price.setRelateCodeJoin(String.join(PriceConstant.SEPARATOR, joinCodeList));
        for (PriceDimension item : price.getDimensionList()) {
            item.setPriceCode(price.getPriceCode());
            item.setTypeCode(price.getTypeCode());
            item.setTypeDetailCode(price.getTypeDetailCode());
            //新增租户编号
            item.setTenantCode(TenantUtils.getTenantCode());
        }
        this.priceDimensionService.saveBatch(price.getDimensionList(), price.getPriceCode());
    }

    @Override
    @Transactional
    public void updateDelFlagByIds(List<String> ids) {
        Validate.isTrue(CollectionUtils.isNotEmpty(ids), "id集合不能为空");
        List<Price> byIds = this.priceRepository.findByIds(ids);
        Validate.notEmpty(byIds, "未查询到相关信息");
        this.priceRepository.updateDelFlagByIds(ids);
        // 业务日志删除
        for (Price byId : byIds) {
            PriceEventDto priceEventDto = new PriceEventDto();
            PriceVo oldVO =
                    this.nebulaToolkitService.copyObjectByWhiteList(
                            byId, PriceVo.class, HashSet.class, ArrayList.class);
            priceEventDto.setNewest(null);
            priceEventDto.setOriginal(oldVO);
            SerializableBiConsumer<PriceLogEventListener, PriceEventDto> onDelete =
                    PriceLogEventListener::onDelete;
            this.nebulaNetEventClient.publish(priceEventDto, PriceLogEventListener.class, onDelete);
        }
    }

    @Override
    public Boolean checkExistsPriceByTypeDetailCodes(Set<String> typeDetailCodeSet) {
        if (CollectionUtils.isEmpty(typeDetailCodeSet)) {
            return false;
        }
        Integer count = this.priceRepository.countByTypeDetailCodes(typeDetailCodeSet);
        return count != null && count > 0;
    }

    @Override
    public Price findDetailByPriceSearchDimensionDto(PriceSearchDimensionDto dto) {
        if (Objects.isNull(dto)
                || StringUtils.isAnyBlank(dto.getTypeCode(), dto.getTypeDetailCode())
                || Objects.isNull(dto.getMap())) {
            return null;
        }
        List<PriceDimensionDictVo> dimensionSelect =
                this.priceDimensionContainerService.findDimensionSelect();
        if (CollectionUtils.isEmpty(dimensionSelect)) {
            return null;
        }
        List<String> list = Lists.newLinkedList();
        list.add(dto.getTypeDetailCode());
        for (PriceDimensionDictVo item : dimensionSelect) {
            if (dto.getMap().containsKey(item.getCode())) {
                list.add(dto.getMap().get(item.getCode()));
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        String relateCodeJoin = list.stream().collect(Collectors.joining(PriceConstant.SEPARATOR));

        return this.priceRepository.findByTypeCodeAndDetailCodeAndRelateCodeJoin(
                dto.getTypeCode(), dto.getTypeDetailCode(), relateCodeJoin);
    }

    /**
     * 通过编码查询
     *
     * @param typeCodeList
     * @return
     */
    @Override
    public List<Price> findByTypeCodes(List<String> typeCodeList) {
        if (CollectionUtils.isEmpty(typeCodeList)) {
            return Lists.newArrayList();
        }
        return priceRepository.findByTypeCodes(typeCodeList);
    }

    private void createValidation(Price price) {
        price.setId(null);
        this.validation(price);
    }

    private void updateValidation(Price price) {
        this.validation(price);
    }

    private void validation(Price price) {
        Validate.notBlank(price.getTypeCode(), "请选择价格类型信息");
        Validate.notBlank(price.getTypeDetailCode(), "请选择定价维度信息");
        Validate.notNull(price.getBeginTime(), "有效开始时间不能为空");
        Validate.notNull(price.getEndTime(), "有效截止时间不能为空");
        Validate.isTrue(price.getEndTime().compareTo(price.getBeginTime()) > 0, "截止时间需大于开始时间");
        Validate.isTrue(price.getEndTime().compareTo(new Date()) > 0, "截止时间需大于当前时间");
        price.setPrice(Optional.ofNullable(price.getPrice()).orElse(BigDecimal.ZERO));
        Validate.isTrue(price.getPrice().compareTo(BigDecimal.ZERO) > 0, "请录入大于0的价格数据");
        if (StringUtil.isEmpty(price.getDataSource())) {
            price.setDataSource(CommonConstant.CURRENT_SYSTEM);
        }
    }

    /**
     * 获取展示的生效状态
     *
     * @param begin
     * @param end
     * @return
     */
    private String findEffectiveStatus(Date begin, Date end) {
        if (Objects.isNull(begin)
                || Objects.isNull(end)) {
            return EffectiveStatusEnum.DEFAULT.getDictCode();
        }
        Date now = new Date();
        if (begin.compareTo(now) > 0) {
            return EffectiveStatusEnum.DEFAULT.getDictCode();
        }
        if (begin.compareTo(now) <= 0 && end.compareTo(now) > 0) {
            return EffectiveStatusEnum.ACTIVE.getDictCode();
        }
        if (end.compareTo(now) <= 0) {
            return EffectiveStatusEnum.OVERDUE.getDictCode();
        }
        return EffectiveStatusEnum.DEFAULT.getDictCode();
    }

    /**
     * 新增或更新SAP的价格数据
     *
     * @param saveList
     * @param updateList
     * @param disableSapPriceCodes
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/4 14:34
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateListForSapData(List<Price> saveList, List<Price> updateList, Set<String> disableSapPriceCodes) {
        List<PriceDimension> priceDimensionList = Lists.newArrayList();
        Map<String, PriceAllowProductDto> allowProductDtoMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(saveList)) {
            List<String> codeList = this.generateCodeService.generateCode(PriceConstant.PRICE_CODE_PREFIX, saveList.size());
            AtomicInteger index = new AtomicInteger(0);
            saveList.forEach(item -> {
                item.setId(UuidCrmUtil.general());
                String priceCode = codeList.get(index.getAndIncrement());
                item.setPriceCode(priceCode);
                if (CollectionUtil.isNotEmpty(item.getDimensionList())) {
                    item.getDimensionList().forEach(dimension -> {
                        dimension.setPriceCode(priceCode);
                    });
                    priceDimensionList.addAll(item.getDimensionList());
                }
            });
            this.priceBuildAllowRule(saveList, allowProductDtoMap);
            this.priceRepository.saveBatchXml(saveList);
        }
        List<PriceLog> priceLogList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(saveList)) {
            priceLogList.addAll(nebulaToolkitService.copyCollectionByWhiteList(saveList,
                    Price.class, PriceLog.class, HashSet.class, ArrayList.class));
        }
        List<String> priceCodeList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(updateList)) {
            updateList.forEach(item -> {
                String priceCode = item.getPriceCode();
                if (CollectionUtil.isNotEmpty(item.getDimensionList())) {
                    priceCodeList.add(priceCode);
                    if (CollectionUtil.isNotEmpty(item.getDimensionList())) {
                        item.getDimensionList().forEach(dimension -> {
                            dimension.setPriceCode(priceCode);
                        });
                        priceDimensionList.addAll(item.getDimensionList());
                    }
                }
            });
            this.priceBuildAllowRule(updateList, allowProductDtoMap);
            this.priceRepository.updateBatchXml(updateList);
            priceLogList.addAll(nebulaToolkitService.copyCollectionByWhiteList(updateList,
                    Price.class, PriceLog.class, HashSet.class, ArrayList.class));
        }
        this.priceRepository.disableSapPriceCodes(disableSapPriceCodes);
        priceDimensionService.saveBatch(priceDimensionList, priceCodeList);
        if (CollectionUtil.isNotEmpty(priceLogList)) {
            priceLogList.forEach(item -> {
                item.setId(null);
            });
        }
        this.priceLogRepository.saveBatchXml(priceLogList);
        sendPriceToDms(allowProductDtoMap);

    }

    /**
     * 价格自动生成可购规则
     *
     * @param priceList
     * @param allowProductDtoMap
     */
    private void priceBuildAllowRule(List<Price> priceList, Map<String, PriceAllowProductDto> allowProductDtoMap) {
        if (CollectionUtil.isEmpty(priceList)) {
            return;
        }
        priceList.stream()
                .filter(k -> CollectionUtil.isNotEmpty(k.getDimensionList()))
                .forEach(item -> {
                    PriceAllowProductDto allowProductDto = new PriceAllowProductDto();
                    allowProductDto.setPriceCode(item.getPriceCode());
                    item.getDimensionList().forEach(dimension -> {
                        if (SapPriceDimensionEnum.A005.getTypeDetailCode().equals(item.getTypeDetailCode())) {
                            if (PriceDimensionEnum.CUSTOMER.getDictCode().equals(dimension.getDimensionCode())) {
                                allowProductDto.setCustomerCode(dimension.getRelateCode());
                                allowProductDto.setOrgCode(dimension.getOrgCode());
                                allowProductDto.setErpCode(dimension.getRelateSapCode());
                                allowProductDto.setCustomerName(dimension.getRelateName());
                                allowProductDto.setCompanyName(dimension.getCompanyName());
                                allowProductDto.setContractCustomer(dimension.getContractCustomer());
                            } else if (PriceDimensionEnum.PRODUCT.getDictCode().equals(dimension.getDimensionCode())) {
                                allowProductDto.setProductCode(dimension.getRelateCode());
                                allowProductDto.setMaterialCode(dimension.getRelateCode());
                                allowProductDto.setProductName(dimension.getRelateName());
                            }
                        }
                    });
                    if (SapPriceDimensionEnum.A005.getTypeDetailCode().equals(item.getTypeDetailCode())
                            && StringUtil.isNotEmpty(allowProductDto.getCustomerCode())
                            && StringUtil.isNotEmpty(allowProductDto.getProductCode())) {
                        item.setPriceAllow(BooleanEnum.FALSE.getCapital());
                        allowProductDto.setItemKey(String.join(":", PriceDimensionEnum.CUSTOMER.getDictCode(),
                                allowProductDto.getCustomerCode(), allowProductDto.getProductCode()));
                        allowProductDtoMap.put(allowProductDto.getItemKey(), allowProductDto);
                    }
                });
    }

    /**
     * 价格更新可购
     *
     * @param flag
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/8/10 19:47
     */
    @Override
    public void priceUpdateAllowProduct(String flag) {
        Pageable pageable = PageRequest.of(1, 200);
        PricePaginationDto dto = new PricePaginationDto();
        dto.setTypeDetailCode(SapPriceDimensionEnum.A005.getTypeDetailCode());
        dto.setPriceAllow(BooleanEnum.FALSE.getCapital());
        Page<Price> pricePage = null;
        do {
            pricePage = priceRepository.findByConditions(pageable, dto);
            pageable = pageable.next();
            if (Objects.isNull(pricePage)
                    || CollectionUtil.isEmpty(pricePage.getRecords())) {
                return;
            }
            Set<String> priceCodeSet = pricePage.getRecords().stream()
                    .filter(a -> StringUtils.isNotBlank(a.getPriceCode()))
                    .map(Price::getPriceCode)
                    .collect(Collectors.toSet());
            List<PriceDimension> list = priceDimensionService.findByPriceCodes(Lists.newArrayList(priceCodeSet));
            if (CollectionUtil.isEmpty(list)) {
                return;
            }
            Map<String, List<PriceDimension>> map = list.stream()
                    .filter(a -> StringUtils.isNotBlank(a.getPriceCode()))
                    .collect(Collectors.groupingBy(PriceDimension::getPriceCode));
            pricePage.getRecords().forEach(item -> {
                item.setDimensionList(map.get(item.getPriceCode()));
            });
            Map<String, PriceAllowProductDto> allowProductDtoMap = Maps.newHashMap();
            priceBuildAllowRule(pricePage.getRecords(), allowProductDtoMap);
            sendPriceToDms(allowProductDtoMap);
        } while (pricePage.hasNext() && pageable.getPageNumber() < CommonConstant.MAX_LOOP_NUMBER);
    }

    /**
     * 价格生成可够推送DMS
     *
     * @param allowProductDtoMap
     */
    private void sendPriceToDms(Map<String, PriceAllowProductDto> allowProductDtoMap) {
        if (CollectionUtil.isEmpty(allowProductDtoMap)) {
            return;
        }
        List<PriceAllowProductDto> productDtoList = new ArrayList<>(allowProductDtoMap.values());
        Map<String, List<PriceAllowProductDto>> allowProductMap = productDtoList.stream()
                .filter(k -> StringUtil.isNotEmpty(k.getCustomerCode()))
                .collect(Collectors.groupingBy(PriceAllowProductDto::getCustomerCode));
        allowProductMap.forEach((customerCode, list) -> {
            try {
                // 发送消息
                MqMessageVo mqMessageVo = new MqMessageVo();
                mqMessageVo.setTopic(MqConstant.TOPIC_DMS_ALLOW_PRODUCT_ORDER + RocketMqUtil.mqEnvironment());
                mqMessageVo.setTag(MqConstant.MDM_PRICE_DMS_ALLOW_PRODUCT);
                mqMessageVo.setMsgBody(JSON.toJSONString(list));
                mqMessageVo.setMsgNum(list.size());
                this.rocketMqProducer.sendMqOrderMsg(mqMessageVo, customerCode);
                //MQ发送频繁会报错
                Thread.sleep(200);
            } catch (Exception e) {
                log.error("价格生成可购异常[{}]", e.getMessage());
                log.error(e.getMessage(), e);
            }
        });

    }


    /**
     * 根据类型明细和产品查询价格
     * @param typeDetailCode 价格类型明细编码
     * @param productCodeSet 产品编码集合
     * @return
     */
    @Override
    public List<PriceVo> findPriceByTypeDetailCodeAndProducts(String typeDetailCode, Set<String> productCodeSet) {
        if (StringUtil.isEmpty(typeDetailCode) || CollectionUtil.isEmpty(productCodeSet)) {
            return Collections.emptyList();
        }
        return priceRepository.findPriceByTypeDetailCodeAndProducts(typeDetailCode, productCodeSet);
    }
}
