<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.price.local.mapper.PriceMapper">
    <sql id="where_sql">
        where a.del_flag = '${@<EMAIL>()}'
        and a.tenant_code = #{dto.tenantCode}
        <if test="dto.priceCode!=null and dto.priceCode!=''">
            <bind name="likePriceCode" value="'%' + dto.priceCode + '%'"/>
            and a.price_code like #{likePriceCode}
        </if>
        <if test="dto.sapPriceCode!=null and dto.sapPriceCode!=''">
            <bind name="sapPriceCode" value="'%' + dto.sapPriceCode + '%'"/>
            and a.sap_price_code like #{sapPriceCode}
        </if>
        <if test="dto.typeCode!=null and dto.typeCode!=''">
            <bind name="likeTypeCode" value="'%' + dto.typeCode + '%'"/>
            and a.type_code like #{likeTypeCode}
        </if>
        <if test="dto.typeName!=null and dto.typeName!=''">
            <bind name="likeTypeName" value="'%' + dto.typeName + '%'"/>
            and b.type_name like #{likeTypeName}
        </if>
        <if test="dto.typeDetailCode!=null and dto.typeDetailCode!=''">
            <bind name="typeDetailCode" value="'%' + dto.typeDetailCode + '%'"/>
            and a.type_detail_code like #{typeDetailCode}
        </if>
        <if test="dto.typeDetailName!=null and dto.typeDetailName!=''">
            <bind name="typeDetailName" value="'%' + dto.typeDetailName + '%'"/>
            and c.type_detail_name like #{typeDetailName}
        </if>
        <if test="dto.relateCodeJoin!=null and dto.relateCodeJoin!=''">
            <bind name="relateCodeJoin" value="'%' + dto.relateCodeJoin + '%'"/>
            and c.relate_code_join like #{relateCodeJoin}
        </if>
        <if test="dto.sapStatus!=null and dto.sapStatus!=''">
            and a.sap_status = #{dto.sapStatus}
        </if>
        <if test="dto.enableStatus!=null and dto.enableStatus!=''">
            and a.enable_status = #{dto.enableStatus}
        </if>
        <if test="dto.priceAllow!=null and dto.priceAllow!=''">
            and a.price_allow = #{dto.priceAllow}
        </if>
        <if test="dto.productCode!=null and dto.productCode!=''">
            <bind name="productCode" value="'%' + dto.productCode + '%'"/>
            and exists(select 1 from mdm_price_dimension d
            where d.price_code = a.price_code
            and d.dimension_code = 'product'
            and d.relate_code like #{productCode}
            )
        </if>
        <if test="dto.productName!=null and dto.productName!=''">
            <bind name="productName" value="'%' + dto.productName + '%'"/>
            and exists(select 1 from mdm_price_dimension d
            where d.price_code = a.price_code
            and d.dimension_code = 'product'
            and d.relate_name like #{productName}
            )
        </if>
        <if test="dto.channelCode!=null and dto.channelCode!=''">
            and exists(select 1 from mdm_price_dimension d
            where d.price_code = a.price_code
            and d.dimension_code = 'channel'
            and d.relate_code = #{dto.channelCode}
            )
        </if>
        <if test="dto.orderFactoryCode!=null and dto.orderFactoryCode!=''">
            and exists(select 1 from mdm_price_dimension d
            where d.price_code = a.price_code
            and d.dimension_code = 'order_factory'
            and d.relate_code = #{dto.orderFactoryCode}
            )
        </if>
        <if test="dto.priceGroupCode!=null and dto.priceGroupCode!=''">
            and exists(select 1 from mdm_price_dimension d
            where d.price_code = a.price_code
            and d.dimension_code = 'price_group'
            and d.relate_code = #{dto.priceGroupCode}
            )
        </if>
        <if test="dto.companyCode!=null and dto.companyCode!=''">
            and exists(select 1 from mdm_price_dimension d
            where d.price_code = a.price_code
            and d.dimension_code = 'org_institution'
            and d.relate_code = #{dto.companyCode}
            )
        </if>
        <if test="dto.customerCode!=null and dto.customerCode!=''">
            <bind name="customerCode" value="'%' + dto.customerCode + '%'"/>
            and exists(select 1 from mdm_price_dimension d
            where d.price_code = a.price_code
            and d.dimension_code = 'customer'
            and d.relate_code like #{customerCode}
            )
        </if>
        <if test="dto.customerName!=null and dto.customerName!=''">
            <bind name="customerName" value="'%' + dto.customerName + '%'"/>
            and exists(select 1 from mdm_price_dimension d
            where d.price_code = a.price_code
            and d.dimension_code = 'customer'
            and d.relate_name like #{customerName}
            )
        </if>
        <if test="dto.orgGroupCode!=null and dto.orgGroupCode!=''">
            <bind name="orgGroupCode" value="'%' + dto.orgGroupCode + '%'"/>
            and exists(select 1 from mdm_price_dimension d
            where d.price_code = a.price_code
            and d.dimension_code = 'org_group'
            and d.relate_code like #{orgGroupCode}
            )
        </if>
        <if test="dto.orgGroupName!=null and dto.orgGroupName!=''">
            <bind name="orgGroupName" value="'%' + dto.orgGroupName + '%'"/>
            and exists(select 1 from mdm_price_dimension d
            where d.price_code = a.price_code
            and d.dimension_code = 'org_group'
            and d.relate_name like #{orgGroupName}
            )
        </if>
        <if test="dto.orgDepartmentCode!=null and dto.orgDepartmentCode!=''">
            <bind name="orgDepartmentCode" value="'%' + dto.orgDepartmentCode + '%'"/>
            and exists(select 1 from mdm_price_dimension d
            where d.price_code = a.price_code
            and d.dimension_code = 'org_department'
            and d.relate_code like #{orgDepartmentCode}
            )
        </if>
        <if test="dto.orgDepartmentName!=null and dto.orgDepartmentName!=''">
            <bind name="orgDepartmentName" value="'%' + dto.orgDepartmentName + '%'"/>
            and exists(select 1 from mdm_price_dimension d
            where d.price_code = a.price_code
            and d.dimension_code = 'org_department'
            and d.relate_name like #{orgDepartmentName}
            )
        </if>
        <if test="dto.effectiveStatus!=null and dto.effectiveStatus!=''">
            <choose>
                <when test="dto.effectiveStatus == 'active'">
                    and now() between a.begin_time and a.end_time
                </when>
                <when test="dto.effectiveStatus == 'overdue'">
                    and a.end_time &lt;= now()
                </when>
                <otherwise>
                    and a.begin_time &gt;= now()
                </otherwise>
            </choose>
        </if>
        <if test="dto.productCodeSet!=null and dto.productCodeSet.size>0">
            and exists(select 1 from mdm_price_dimension d
            where d.price_code = a.price_code
            and d.dimension_code = 'product'
            and d.relate_code in (
            <foreach collection="dto.productCodeSet" item="item" separator=",">
                #{item}
            </foreach>)
            )
        </if>
        order by a.modify_time desc, a.id desc
    </sql>
    <select id="findByConditionsNew" resultType="com.biz.crm.mdm.business.price.sdk.vo.PriceVo">
        select
        a.*
        ,b.type_name as typeName
        ,c.type_detail_name as typeDetailName
        from mdm_price a
        left join mdm_price_type b on a.type_code = b.type_code
        left join mdm_price_type_detail c on a.type_detail_code = c.type_detail_code
        <include refid="where_sql"/>
    </select>
    <!--分页查询-->
    <select id="findByConditions"
            resultType="com.biz.crm.mdm.business.price.local.entity.Price">
        select a.*,b.type_name as typeName,c.type_detail_name as typeDetailName
        from mdm_price a
        left join mdm_price_type b on a.type_code=b.type_code
        left join mdm_price_type_detail c on a.type_detail_code=c.type_detail_code
        <include refid="where_sql"/>
    </select>
    <select id="findPriceByTypeDetailCodeAndProducts" resultType="com.biz.crm.mdm.business.price.sdk.vo.PriceVo">
        select mp.*
             , mpd.relate_code productCode
             , mpd.relate_name productName
        from mdm_price mp
                 left join mdm_price_dimension mpd on mp.price_code = mpd.price_code
        where mp.type_detail_code = #{typeDetailCode}
        and mpd.dimension_code = 'product'
        and mpd.relate_code in
        <foreach collection="productCodeSet" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
