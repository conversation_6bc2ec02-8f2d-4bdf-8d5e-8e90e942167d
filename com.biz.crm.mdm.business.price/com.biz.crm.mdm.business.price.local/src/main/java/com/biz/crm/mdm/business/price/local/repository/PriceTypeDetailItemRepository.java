package com.biz.crm.mdm.business.price.local.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.price.local.entity.PriceTypeDetailItem;
import com.biz.crm.mdm.business.price.local.mapper.PriceTypeDetailItemMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 * 价格类型维度配置明细信息(repository)
 *
 * <AUTHOR>
 * @date 2021-12-30 17:46:15
 */
@Component
public class PriceTypeDetailItemRepository
    extends ServiceImpl<PriceTypeDetailItemMapper, PriceTypeDetailItem> {

  /**
   * 根据typeCode删除
   *
   * @param typeCode
   */
  public void deleteByTypeCode(String typeCode) {
    LambdaQueryWrapper<PriceTypeDetailItem> lambdaQuery = Wrappers.lambdaQuery();
    lambdaQuery
        .eq(PriceTypeDetailItem::getTenantCode, TenantUtils.getTenantCode())
        .in(PriceTypeDetailItem::getTypeCode, typeCode);
    this.baseMapper.delete(lambdaQuery);
  }

  /**
   * 根据typeCode集合获取明细
   *
   * @param typeCodeList
   * @return
   */
  public List<PriceTypeDetailItem> findByTypeCodes(List<String> typeCodeList) {
    return this.lambdaQuery()
        .eq(PriceTypeDetailItem::getTenantCode, TenantUtils.getTenantCode())
        .in(PriceTypeDetailItem::getTypeCode, typeCodeList)
        .list();
  }

  /**
   * 根据typeDetailCode集合获取数据
   *
   * @param typeDetailCodeList
   * @return
   */
  public List<PriceTypeDetailItem> findByTypeDetailCodes(List<String> typeDetailCodeList) {
    return this.lambdaQuery()
        .eq(PriceTypeDetailItem::getTenantCode, TenantUtils.getTenantCode())
        .in(PriceTypeDetailItem::getTypeDetailCode, typeDetailCodeList)
        .list();
  }
}
