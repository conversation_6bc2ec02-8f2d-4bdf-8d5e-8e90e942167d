package com.biz.crm.mdm.business.price.local.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.price.local.entity.Price;
import com.biz.crm.mdm.business.price.local.mapper.PriceMapper;
import com.biz.crm.mdm.business.price.sdk.dto.PricePaginationDto;
import com.biz.crm.mdm.business.price.sdk.vo.PriceVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 价格维护主信息(repository)
 *
 * <AUTHOR>
 * @date 2021-12-30 17:46:05
 */
@Component
public class PriceRepository extends ServiceImpl<PriceMapper, Price> {

    /**
     * 分页
     *
     * @param pageable
     * @param dto
     * @return
     */
    public Page<PriceVo> findByConditionsNew(Pageable pageable, PricePaginationDto dto) {
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
        dto = Optional.ofNullable(dto).orElse(new PricePaginationDto());
        if (StringUtil.isEmpty(dto.getTenantCode())) {
            dto.setTenantCode(TenantUtils.getTenantCode());
        }
        Page<Price> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.baseMapper.findByConditionsNew(page, dto);
    }

    /**
     * 分页
     *
     * @param pageable
     * @param dto
     * @return
     */
    public Page<Price> findByConditions(Pageable pageable, PricePaginationDto dto) {
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
        dto = Optional.ofNullable(dto).orElse(new PricePaginationDto());
        if (StringUtil.isEmpty(dto.getTenantCode())) {
            dto.setTenantCode(TenantUtils.getTenantCode());
        }
        Page<Price> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.baseMapper.findByConditions(page, dto);
    }

    public List<Price> findByIds(List<String> id) {
        return this.lambdaQuery()
                .eq(Price::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(Price::getTenantCode, TenantUtils.getTenantCode())
                .in(Price::getId, id)
                .list();
    }

    /**
     * 逻辑删除
     *
     * @param ids
     */
    public void updateDelFlagByIds(List<String> ids) {
        this.lambdaUpdate()
                .eq(Price::getTenantCode, TenantUtils.getTenantCode())
                .in(Price::getId, ids)
                .set(Price::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
                .update();
    }

    /**
     * 获取价格维护数量统计
     *
     * @param typeDetailCodeSet
     * @return
     */
    public Integer countByTypeDetailCodes(Set<String> typeDetailCodeSet) {
        return this.lambdaQuery()
                .eq(Price::getTenantCode, TenantUtils.getTenantCode())
                .eq(Price::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .in(Price::getTypeDetailCode, typeDetailCodeSet)
                .count();
    }

    /**
     * 根据价格编码统计数量
     *
     * @param priceCode
     * @return
     */
    public Integer countByTypeCode(String priceCode) {
        return this.lambdaQuery()
                .eq(Price::getTenantCode, TenantUtils.getTenantCode())
                .in(Price::getPriceCode, priceCode)
                .count();
    }

    /**
     * 根据typeCode+typeDetailCode+relateCodeJoin获取最近创建的未删除的价格配置信息
     *
     * @param typeCode
     * @param typeDetailCode
     * @param relateCodeJoin
     * @return
     */
    public Price findByTypeCodeAndDetailCodeAndRelateCodeJoin(
            String typeCode, String typeDetailCode, String relateCodeJoin) {
        List<Price> list =
                this.lambdaQuery()
                        .eq(Price::getTenantCode, TenantUtils.getTenantCode())
                        .eq(Price::getTypeCode, typeCode)
                        .eq(Price::getTypeDetailCode, typeDetailCode)
                        .eq(Price::getRelateCodeJoin, relateCodeJoin)
                        .eq(Price::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                        .orderByDesc(Price::getCreateTime)
                        .list();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Optional<Price> optional = list.stream().findFirst();
        return optional.orElse(null);
    }

    /**
     * 通过价格类型编码集合查询未删除的记录
     *
     * @param typeCodeList
     * @return
     */
    public List<Price> findByTypeCodes(List<String> typeCodeList) {
        return this.lambdaQuery()
                .eq(Price::getTenantCode, TenantUtils.getTenantCode())
                .eq(Price::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .in(Price::getTypeCode, typeCodeList)
                .list();
    }

    public void saveBatchXml(List<Price> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return;
        }
        Lists.partition(entityList, CommonConstant.MAX_PAGE_SIZE).forEach(list -> {
            this.baseMapper.insertBatchSomeColumn(list);
        });
    }


    public void updateBatchXml(List<Price> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return;
        }
        Lists.partition(entityList, CommonConstant.MAX_PAGE_SIZE).forEach(this::updateBatchById);
    }

    public List<Price> findByPriceCodes(Set<String> priceCodes) {
        if (CollectionUtil.isEmpty(priceCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(Price::getPriceCode, priceCodes)
                .list();

    }

    public List<Price> findBySapPriceCodes(Set<String> sapPriceCodes) {
        if (CollectionUtil.isEmpty(sapPriceCodes)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(Price::getSapPriceCode, sapPriceCodes)
                .eq(Price::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .eq(Price::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();

    }

    public List<Price> getOldPriceByRelateCodeJoins(Set<String> relateCodeJoins, Set<String> sapPriceCodeSet) {
        if (CollectionUtil.isEmpty(relateCodeJoins)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(Price::getRelateCodeJoin, relateCodeJoins)
                .notIn(CollectionUtil.isNotEmpty(sapPriceCodeSet), Price::getSapPriceCode, sapPriceCodeSet)
                .eq(Price::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .eq(Price::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .list();

    }

    public void disableSapPriceCodes(Set<String> disableSapPriceCodes) {
        if (CollectionUtil.isEmpty(disableSapPriceCodes)) {
            return;
        }
        this.lambdaUpdate()
                .in(Price::getSapPriceCode, disableSapPriceCodes)
                .set(Price::getEnableStatus, EnableStatusEnum.DISABLE.getCode())
                .set(Price::getSapStatus, EnableStatusEnum.DISABLE.getCode())
                .update();
    }

    public void updatePriceAllow(List<String> priceCodeList) {
        if (CollectionUtil.isEmpty(priceCodeList)) {
            return;
        }
        this.lambdaUpdate()
                .in(Price::getPriceCode, priceCodeList)
                .set(Price::getPriceAllow, BooleanEnum.TRUE.getCapital())
                .update();
    }

    public List<PriceVo> findPriceByTypeDetailCodeAndProducts(String typeDetailCode, Set<String> productCodeSet) {
        if (StringUtil.isEmpty(typeDetailCode) || CollectionUtil.isEmpty(productCodeSet)) {
            return Collections.emptyList();
        }
        return this.baseMapper.findPriceByTypeDetailCodeAndProducts(typeDetailCode, productCodeSet);
    }
}
