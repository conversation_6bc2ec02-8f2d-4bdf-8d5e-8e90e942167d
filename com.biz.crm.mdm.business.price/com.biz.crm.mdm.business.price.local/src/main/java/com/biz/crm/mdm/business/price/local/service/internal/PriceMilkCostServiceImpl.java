package com.biz.crm.mdm.business.price.local.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.mdm.business.price.local.entity.PriceMilkCost;
import com.biz.crm.mdm.business.price.local.repository.PriceMilkCostRepository;
import com.biz.crm.mdm.business.price.sdk.dto.PriceMilkCostDto;
import com.biz.crm.mdm.business.price.sdk.service.PriceMilkCostVoService;
import com.biz.crm.mdm.business.price.sdk.vo.PriceMilkCostVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.event.sdk.service.NebulaNetEventClient;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 奶卡成本价
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/7/2 23:17
 */
@Service
public class PriceMilkCostServiceImpl implements PriceMilkCostVoService {

    @Autowired(required = false)
    private PriceMilkCostRepository priceMilkCostRepository;

    @Autowired(required = false)
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private NebulaNetEventClient nebulaNetEventClient;

    /**
     * 批量保存或更新
     *
     * @param dtoList
     * @return void
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/7/2 23:21
     */
    @Override
    public void saveOrUpdateBatchDto(List<PriceMilkCostDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        List<PriceMilkCost> priceMilkCostArrayList = Lists.newArrayList(this.nebulaToolkitService.copyCollectionByWhiteList(dtoList
                , PriceMilkCostDto.class, PriceMilkCost.class, HashSet.class, ArrayList.class));
        Map<String, PriceMilkCost> priceMilkCostMap = priceMilkCostArrayList.stream().filter(k -> StringUtil.isNotEmpty(k.getOnlyKey()))
                .collect(Collectors.toMap(PriceMilkCost::getOnlyKey, v -> v, (n, o) -> n));
        List<PriceMilkCost> oldList = priceMilkCostRepository.getAllByOnlyKeys(priceMilkCostMap.keySet());
        Map<String, PriceMilkCost> oldMap = oldList.stream().filter(k -> StringUtil.isNotEmpty(k.getOnlyKey()))
                .collect(Collectors.toMap(PriceMilkCost::getOnlyKey, v -> v, (n, o) -> n));
        List<PriceMilkCost> saveList = Lists.newArrayList();
        List<PriceMilkCost> updateList = Lists.newArrayList();
        priceMilkCostMap.forEach((onlyKey, entity) -> {
            PriceMilkCost oldEntity = oldMap.get(onlyKey);
            if (Objects.nonNull(oldEntity)) {
                oldEntity.setMaterialName(entity.getMaterialName());
                oldEntity.setTaxRate(entity.getTaxRate());
                oldEntity.setTaxRateStr(entity.getTaxRateStr());
                oldEntity.setMilkCardQuantity(entity.getMilkCardQuantity());
                oldEntity.setCostPrice(entity.getCostPrice());
                oldEntity.setEnableStatus(entity.getEnableStatus());
                oldEntity.setModifyAccount(entity.getModifyAccount());
                oldEntity.setModifyName(entity.getModifyName());
                oldEntity.setModifyTime(entity.getModifyTime());
                updateList.add(oldEntity);
            } else {
                saveList.add(entity);
            }
        });
        priceMilkCostRepository.saveBatchXml(saveList);
        priceMilkCostRepository.updateBatchXml(updateList);

    }

    @Override
    public List<PriceMilkCostVo> findByMaterialCodes(Set<String> materialCodes) {
        List<PriceMilkCost> entities = this.priceMilkCostRepository.findByMaterialCodes(materialCodes);
        if (CollectionUtil.isEmpty(entities)) {
            return new ArrayList<>(0);
        }
        return (List<PriceMilkCostVo>) this.nebulaToolkitService.copyCollectionByWhiteList(entities, PriceMilkCost.class,
                PriceMilkCostVo.class, HashSet.class, ArrayList.class);
    }

    /**
     * 查询物料编码最新的奶卡成本价
     *
     * @param yearMonthLy
     * @param materialCodes
     * @return java.util.List<com.biz.crm.mdm.business.price.sdk.vo.PriceMilkCostVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/8/12 23:08
     */
    @Override
    public List<PriceMilkCostVo> findByUpToDateMaterialCodes(String yearMonthLy, Set<String> materialCodes) {
        if (StringUtil.isEmpty(yearMonthLy)
                || CollectionUtil.isEmpty(materialCodes)) {
            return Lists.newArrayList();
        }
        return this.priceMilkCostRepository.findByUpToDateMaterialCodes(yearMonthLy, materialCodes);
    }
}
