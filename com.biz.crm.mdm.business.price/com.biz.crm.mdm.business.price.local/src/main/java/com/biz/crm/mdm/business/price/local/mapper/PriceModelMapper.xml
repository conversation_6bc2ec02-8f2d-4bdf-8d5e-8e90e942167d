<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.price.local.mapper.PriceModelMapper">

    <select id="findByPriceModelDto"
            resultType="com.biz.crm.mdm.business.price.sdk.vo.PriceModelVo">
        select
        b.price_code,b.type_code,b.type_detail_code,b.price,a.dimension_code,a.relate_code,c.sort,b.relate_code_join
        from mdm_price_dimension a
        left join mdm_price b on a.tenant_code = b.tenant_code and a.price_code = b.price_code
        left join mdm_price_type_detail c on a.tenant_code = c.tenant_code and a.type_detail_code = c.type_detail_code
        where b.del_flag=#{dto.delFlag}
        and b.enable_status=#{dto.enableStatus}
        and b.tenant_code=#{dto.tenantCode}
        and b.type_code=#{dto.typeCode}
        and a.dimension_code=#{dto.dimensionCode}
        and a.relate_code in(<foreach collection="dto.relateCodeSet" item="item" separator=",">#{item}</foreach>)
        and b.relate_code_join in(<foreach collection="dto.relateCodeJoinSet" item="item" separator=",">
        #{item}</foreach>)
        and #{dto.searchTime} between b.begin_time and b.end_time
        order by c.sort asc,b.price_code desc
    </select>

    <select id="findByPriceModelDtoByRelateJoinCodes"
            resultType="com.biz.crm.mdm.business.price.sdk.vo.PriceModelVo">
        select
        b.price_code,b.type_code,b.type_detail_code,b.price,a.dimension_code,a.relate_code,c.sort,b.relate_code_join
        from mdm_price_dimension a
        left join mdm_price b on a.tenant_code = b.tenant_code and a.price_code = b.price_code
        left join mdm_price_type_detail c on a.tenant_code = b.tenant_code and a.type_detail_code = c.type_detail_code
        where b.del_flag=#{delFlag}
        and b.tenant_code=#{tenantCode}
        and b.relate_code_join in(<foreach collection="list" item="item" separator=",">#{item}</foreach>)
        and #{searchTime} between b.begin_time and b.end_time
        order by a.relate_code asc,c.sort asc,b.create_time desc
    </select>
</mapper>