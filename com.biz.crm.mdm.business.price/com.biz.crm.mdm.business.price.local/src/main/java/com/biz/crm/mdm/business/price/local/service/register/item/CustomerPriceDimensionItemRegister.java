package com.biz.crm.mdm.business.price.local.service.register.item;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerRelateOrgVo;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.org.sdk.common.enums.OrgTypeEnum;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.mdm.business.price.sdk.dto.FindPriceDto;
import com.biz.crm.mdm.business.price.sdk.dto.SearchPriceDimensionItemDto;
import com.biz.crm.mdm.business.price.sdk.enums.FindPriceUserTypeEnum;
import com.biz.crm.mdm.business.price.sdk.enums.PriceDimensionEnum;
import com.biz.crm.mdm.business.price.sdk.register.UserSearchPriceDimensionItemRegister;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 客户询价维度信息注册器实现
 *
 * <AUTHOR>
 * @date 2022/3/15
 */
@Component
public class CustomerPriceDimensionItemRegister implements UserSearchPriceDimensionItemRegister {

    @Autowired(required = false)
    private CustomerVoService customerVoService;

    @Autowired(required = false)
    private OrgVoService orgVoService;

    @Override
    public String getCode() {
        return FindPriceUserTypeEnum.CUSTOMER.getDictCode();
    }

    /**
     * 获取数据 客户编码 渠道 组织 价格组
     *
     * @param dto 经销商编码、终端编码等
     * @return
     */
    @Override
    public List<SearchPriceDimensionItemDto> findSearchPriceDimensionItems(FindPriceDto dto) {
        if (Objects.isNull(dto)) {
            return Lists.newArrayList();
        }
        String userCode = dto.getUserCode();
        if (StringUtils.isBlank(userCode)) {
            return Lists.newLinkedList();
        }
        List<CustomerVo> customerVoList = this.customerVoService.findForPriceByCustomerCodes(Sets.newHashSet(userCode));
        if (CollectionUtils.isEmpty(customerVoList)) {
            return Lists.newLinkedList();
        }
        Optional<CustomerVo> optional = customerVoList.stream().findFirst();
        if (!optional.isPresent()) {
            return Lists.newLinkedList();
        }
        List<SearchPriceDimensionItemDto> dimensionItems = Lists.newLinkedList();
        if (StringUtil.isNotEmpty(dto.getOrderFactory())){
            //2024年8月5日16:15:00 1045684 【DMS后台】公司间价格未询到
            SearchPriceDimensionItemDto orderFactory = new SearchPriceDimensionItemDto();
            orderFactory.setDimensionCode(PriceDimensionEnum.ORDER_FACTORY.getDictCode());
            orderFactory.setRelateCodeSet(Sets.newHashSet(dto.getOrderFactory()));
            dimensionItems.add(orderFactory);
        }

        //2024年7月17日13:00:00  产品说先写死 20 渠道,后续优化
        //2024年9月21日15:59:27  1048174 【营销方案规划】一件代发客户使用TPM系统规划营销方案，大货价格问题
        SearchPriceDimensionItemDto channelItem = new SearchPriceDimensionItemDto();
        channelItem.setDimensionCode(PriceDimensionEnum.CHANNEL.getDictCode());
        String channelCode = dto.getChannelCode();
        if (StringUtils.isEmpty(channelCode)){
            channelCode = "20";
        }
        channelItem.setRelateCodeSet(Sets.newHashSet(channelCode));
        dimensionItems.add(channelItem);

        SearchPriceDimensionItemDto item = new SearchPriceDimensionItemDto();
        item.setDimensionCode(PriceDimensionEnum.CUSTOMER.getDictCode());
        item.setRelateCodeSet(Sets.newHashSet(userCode));
        dimensionItems.add(item);
        CustomerVo customerVo = optional.get();
        if (CollectionUtils.isNotEmpty(customerVo.getOrgList())) {
            List<OrgVo> orgVoList = orgVoService.findByOrgCodes(customerVo.getOrgList().stream()
                    .filter(a -> StringUtils.isNotBlank(a.getOrgCode()))
                    .map(CustomerRelateOrgVo::getOrgCode).distinct().collect(Collectors.toList()));
            if (CollectionUtil.isNotEmpty(orgVoList)) {
                List<String> orgGroupCodeList = orgVoList.stream().filter(k -> OrgTypeEnum.REGION.getDictCode().equals(k.getOrgType()))
                        .map(OrgVo::getOrgCode)
                        .distinct().collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(orgGroupCodeList)) {
                    SearchPriceDimensionItemDto orgDepartment = new SearchPriceDimensionItemDto();
                    orgDepartment.setDimensionCode(PriceDimensionEnum.ORG_GROUP.getDictCode());
                    orgDepartment.setRelateCodeSet(Sets.newHashSet(orgGroupCodeList));
                    dimensionItems.add(orgDepartment);
                }
                List<String> orgDepartmentList = orgVoList.stream().filter(k -> OrgTypeEnum.DIVISION.getDictCode().equals(k.getOrgType()))
                        .map(OrgVo::getOrgCode).distinct().collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(orgDepartmentList)) {
                    SearchPriceDimensionItemDto orgDepartment = new SearchPriceDimensionItemDto();
                    orgDepartment.setDimensionCode(PriceDimensionEnum.ORG_DEPARTMENT.getDictCode());
                    orgDepartment.setRelateCodeSet(Sets.newHashSet(orgDepartmentList));
                    dimensionItems.add(orgDepartment);
                }
            }

        }

        if (StringUtils.isNotBlank(customerVo.getCompanyCode())) {
            SearchPriceDimensionItemDto cur = new SearchPriceDimensionItemDto();
            cur.setDimensionCode(PriceDimensionEnum.ORG_INSTITUTION.getDictCode());
            cur.setRelateCodeSet(Sets.newHashSet(customerVo.getCompanyCode()));
            dimensionItems.add(cur);
            dto.setCompanyCode(customerVo.getCompanyCode());
        }

        if (StringUtils.isNotBlank(customerVo.getPriceGroup())) {
            SearchPriceDimensionItemDto cur = new SearchPriceDimensionItemDto();
            cur.setDimensionCode(PriceDimensionEnum.PRICE_GROUP.getDictCode());
            cur.setRelateCodeSet(Sets.newHashSet(customerVo.getPriceGroup()));
            dimensionItems.add(cur);
        }
        return dimensionItems;
    }
}
