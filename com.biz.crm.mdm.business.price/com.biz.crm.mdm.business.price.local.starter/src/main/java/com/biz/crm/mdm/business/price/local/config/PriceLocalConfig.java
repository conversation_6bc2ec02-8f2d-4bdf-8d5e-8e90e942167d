package com.biz.crm.mdm.business.price.local.config;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 价格管理模块config
 *
 * <AUTHOR>
 * @date 2021/12/29
 */
@Configuration
@EntityScan(basePackages = "com.biz.crm.mdm.business.price.local.entity")
@ComponentScan(basePackages = {"com.biz.crm.mdm.business.price"})
@EnableConfigurationProperties(PriceProperties.class)
public class PriceLocalConfig {

  @Bean
  public ThreadPoolExecutor priceExecutor() {
    return ExecutorBuilder.create()
        .setCorePoolSize(10)
        .setMaxPoolSize(10)
        .setWorkQueue(new LinkedBlockingQueue<>(Integer.MAX_VALUE))
        .setThreadFactory(ThreadFactoryBuilder.create().setNamePrefix("biz-price-task").build())
        .build();
  }
}
