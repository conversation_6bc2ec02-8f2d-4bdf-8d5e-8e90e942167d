package com.biz.crm.mdm.business.price.sdk.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.Set;

/**
 * 查询价格dto
 *
 * <AUTHOR>
 * @date 2022/3/15
 */
@Data
@ApiModel("查询价格dto")
public class FindPriceDto {

    /**
     * 用户类型
     *
     * @see com.biz.crm.mdm.business.price.sdk.enums.FindPriceUserTypeEnum
     */
    @ApiModelProperty("用户类型")
    private String userType;

    @ApiModelProperty("用户编码-经销商编码、终端编码")
    private String userCode;

    @ApiModelProperty("订单工厂")
    private String orderFactory;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("价格组")
    private String priceGroup;

    /**
     * 价格绑定维度 与productCodeSet对应
     *
     * @see com.biz.crm.mdm.business.price.sdk.enums.PriceDimensionEnum
     */
    @ApiModelProperty("价格绑定维度,不传默认是商品维度（product）")
    private String dimensionCode;

    @ApiModelProperty("商品编码集合")
    private Set<String> productCodeSet;

    @ApiModelProperty("价格类型编码，不设默认取配置文件crm.business.price.sale-price-code=sale_price")
    private Set<String> priceTypeCode;

    @ApiModelProperty("询价时间,不传默认服务端当前时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date searchTime;

    @ApiModelProperty("组织纬度的询价是否根据组织递归往上询价  默认false ")
    private Boolean orgRecursionFlag = false;
}
