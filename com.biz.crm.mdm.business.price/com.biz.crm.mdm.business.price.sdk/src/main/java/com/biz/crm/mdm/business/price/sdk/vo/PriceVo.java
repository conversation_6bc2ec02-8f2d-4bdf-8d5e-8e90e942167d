package com.biz.crm.mdm.business.price.sdk.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 价格维护主信息vo
 *
 * <AUTHOR>
 * @date 2021-12-30 17:43:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "价格维护主信息Vo")
public class PriceVo extends TenantFlagOpVo {

    private static final long serialVersionUID = -1167981811572547853L;

    @ApiModelProperty("价格编码")
    private String priceCode;

    @ApiModelProperty("SAP价格编码")
    private String sapPriceCode;

    @ApiModelProperty("SAP状态")
    private String sapStatus;

    @ApiModelProperty("价格类型编码")
    private String typeCode;

    @ApiModelProperty("价格类型名称")
    private String typeName;

    @ApiModelProperty("定价维度编码")
    private String typeDetailCode;

    @ApiModelProperty("定价维度名称")
    private String typeDetailName;

    @ApiModelProperty("价格")
    private BigDecimal price;

    @ApiModelProperty("开始时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("订单工厂编码")
    private String orderFactoryCode;

    @ApiModelProperty("订单工厂名称")
    private String orderFactoryName;

    @ApiModelProperty("价格组编码")
    private String priceGroupCode;

    @ApiModelProperty("价格组名称")
    private String priceGroupName;

    @ApiModelProperty("销售组编码")
    private String orgGroupCode;

    @ApiModelProperty("销售组名称")
    private String orgGroupName;

    @ApiModelProperty("销售部门编码")
    private String orgDepartmentCode;

    @ApiModelProperty("销售部门名称")
    private String orgDepartmentName;

    @ApiModelProperty("公司/销售组织编码")
    private String companyCode;

    @ApiModelProperty("公司/销售组织名称")
    private String companyName;

    @ApiModelProperty("价格维度数据源编码组合字段")
    private String relateCodeJoin;

    @ApiModelProperty("生效状态,EffectiveStatusEnum,"
            + "  DEFAULT(\"default\", \"default\", \"未生效\", \"0\"),\n"
            + "  ACTIVE(\"active\", \"active\", \"生效中\", \"1\"),\n"
            + "  OVERDUE(\"overdue\", \"overdue\", \"已过期\", \"2\")")
    @TableField(exist = false)
    @Transient
    private String effectiveStatus;

    @ApiModelProperty("数据源[数据字典:mdm_data_source]")
    private String dataSource;

    @ApiModelProperty("是否生成可购[数据字典:yesOrNo]")
    private String priceAllow;

    @ApiModelProperty("价格设置维度配置明细")
    private List<PriceDimensionVo> dimensionList;
}
