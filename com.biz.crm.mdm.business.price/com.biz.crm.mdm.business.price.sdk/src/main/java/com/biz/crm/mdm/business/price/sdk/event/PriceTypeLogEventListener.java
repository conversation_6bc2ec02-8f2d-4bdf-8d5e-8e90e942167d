package com.biz.crm.mdm.business.price.sdk.event;

import com.biz.crm.mdm.business.price.sdk.dto.PriceTypeEventDto;
import com.bizunited.nebula.event.sdk.service.NebulaEvent;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/27 16:30
 * @ClassName PriceTypeLogEventListener
 * @Description TODO
 */
public interface PriceTypeLogEventListener extends NebulaEvent {

    /**
     * 创建事件
     */
    default void onCreate(PriceTypeEventDto eventDto) {
    }

    /**
     * 删除事件
     *
     * @param eventDto
     */
    default void onDelete(PriceTypeEventDto eventDto) {
    }

    /**
     * 更新日志
     *
     * @param eventDto
     */
    default void onUpdate(PriceTypeEventDto eventDto) {
    }

    /**
     * 单个字段更新
     *
     * @param eventDto
     */
    default void onSingleUpdate(PriceTypeEventDto eventDto) {
    }
}
