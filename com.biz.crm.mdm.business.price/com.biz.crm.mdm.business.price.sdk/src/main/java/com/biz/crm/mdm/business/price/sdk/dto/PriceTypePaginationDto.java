package com.biz.crm.mdm.business.price.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 价格类型主信息分页查询dto
 *
 * <AUTHOR>
 * @date 2021-12-30 17:43:59
 */
@Data
@ApiModel(value = "PriceTypePaginationDto", description = "价格类型主信息分页查询dto")
public class PriceTypePaginationDto extends TenantFlagOpDto {
  /** 价格类型编码 */
  @ApiModelProperty("价格类型编码")
  private String typeCode;

  /** 价格类型名称 */
  @ApiModelProperty("价格类型名称")
  private String typeName;
}
