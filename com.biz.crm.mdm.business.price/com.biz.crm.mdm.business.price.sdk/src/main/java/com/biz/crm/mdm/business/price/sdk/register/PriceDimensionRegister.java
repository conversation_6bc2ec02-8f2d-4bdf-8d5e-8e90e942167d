package com.biz.crm.mdm.business.price.sdk.register;

import com.biz.crm.mdm.business.price.sdk.vo.PriceDimensionSelectVo;

import java.util.List;

/**
 * 价格维度注册器
 *
 * <AUTHOR>
 * @date 2021/12/30
 */
public interface PriceDimensionRegister {

    /**
     * 价格维度系统编码
     *
     * <p>系统唯一，必须以字母开头且不包含中横线"-"
     *
     * <p>*非常重要:为保证系统稳定，开发初期定义之后不要修改，可能会影响到历史的价格维护数据
     *
     * @return
     */
    String getCode();

    /**
     * 价格维度系统描述
     *
     * <p>系统唯一,不包含中斜杠"/"
     *
     * @return
     */
    String getDesc();

    /**
     * 排序
     *
     * <p>系统唯一
     * <p>*非常重要:为保证系统稳定，开发初期定义之后不要修改，可能会影响到历史的价格维护数据
     *
     * @return
     */
    default int sort() {
        return Integer.MIN_VALUE;
    }

    /**
     * 获取对应的维度对应的数据源下拉
     *
     * <p>如商品、组织、渠道、价格组、终端、客户下拉等,可以根据keyword执行搜索
     *
     * @param keyword
     * @return
     */
    List<PriceDimensionSelectVo> findSelectVoByKeyword(String keyword, String selectedCode);
}
