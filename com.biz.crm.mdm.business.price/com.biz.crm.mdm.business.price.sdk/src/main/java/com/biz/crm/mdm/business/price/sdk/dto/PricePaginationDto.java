package com.biz.crm.mdm.business.price.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * 价格维护主信息分页查询dto
 *
 * <AUTHOR>
 * @date 2021-12-30 17:43:57
 */
@Data
@ApiModel(value = "PricePaginationDto", description = "价格维护主信息分页查询dto")
public class PricePaginationDto extends TenantFlagOpDto {

    @ApiModelProperty("价格编码")
    private String priceCode;

    @ApiModelProperty("SAP价格编码")
    private String sapPriceCode;

    @ApiModelProperty("SAP状态")
    private String sapStatus;

    @ApiModelProperty("价格类型编码")
    private String typeCode;

    @ApiModelProperty("价格类型名称")
    private String typeName;

    @ApiModelProperty("定价维度编码")
    private String typeDetailCode;

    @ApiModelProperty("定价维度名称")
    private String typeDetailName;

    @ApiModelProperty("商品编码")
    private String productCode;

    @ApiModelProperty("商品名称")
    private String productName;

    @ApiModelProperty("商品编码集合")
    private Set<String> productCodeSet;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("订单工厂编码")
    private String orderFactoryCode;

    @ApiModelProperty("价格组编码")
    private String priceGroupCode;

    @ApiModelProperty("销售组编码")
    private String orgGroupCode;

    @ApiModelProperty("销售组名称")
    private String orgGroupName;

    @ApiModelProperty("销售部门编码")
    private String orgDepartmentCode;

    @ApiModelProperty("销售部门名称")
    private String orgDepartmentName;

    @ApiModelProperty("公司/销售组织编码")
    private String companyCode;

    @ApiModelProperty("价格维度数据源编码组合字段")
    private String relateCodeJoin;

    @ApiModelProperty(
            "生效状态,EffectiveStatusEnum,"
                    + "  DEFAULT(\"default\", \"default\", \"未生效\", \"0\"),\n"
                    + "  ACTIVE(\"active\", \"active\", \"生效中\", \"1\"),\n"
                    + "  OVERDUE(\"overdue\", \"overdue\", \"已过期\", \"2\")")
    private String effectiveStatus;

    @ApiModelProperty("数据源[数据字典:mdm_data_source]")
    private String dataSource;

    @ApiModelProperty("是否生成可购[数据字典:yesOrNo]")
    private String priceAllow;
}
