package com.biz.crm.mdm.business.price.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * 价格维度类型枚举
 *
 * <AUTHOR>
 * @date 2022/1/4
 */
@Getter
@AllArgsConstructor
public enum PriceDimensionEnum {
    /**
     * 价格维度类型枚举
     */
    PRICE_GROUP("price_group", "price_group", "价格组", 10),
    CUSTOMER("customer", "customer", "客户", 20),
    ORG_GROUP("org_group", "org_group", "销售组", 30),
    ORG_DEPARTMENT("org_department", "org_department", "销售部门", 40),
    ORDER_FACTORY("order_factory", "order_factory", "订单工厂", 50),
    PRODUCT("product", "product", "物料", 60),
    ORG_INSTITUTION("org_institution", "org_institution", "销售组织", 70),
    CHANNEL("channel", "channel", "分销渠道", 80),
    ORG("org", "org", "组织", 900),
    TERMINAL("terminal", "terminal", "终端", 910),
    ;
    /**
     * 系统key
     */
    private final String key;
    /**
     * 字典编码
     */
    private final String dictCode;
    /**
     * 字典值
     */
    private final String value;
    /**
     * 字典排序
     */
    private final int order;

    public static PriceDimensionEnum getByKey(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        return Arrays.stream(PriceDimensionEnum.values())
                .filter(item -> Objects.equals(item.getKey(), key))
                .findFirst().orElse(null);
    }
}
