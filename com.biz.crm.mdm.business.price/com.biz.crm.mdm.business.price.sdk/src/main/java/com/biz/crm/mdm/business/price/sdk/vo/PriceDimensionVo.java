package com.biz.crm.mdm.business.price.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 价格设置维度配置明细vo
 *
 * <AUTHOR>
 * @date 2021-12-30 17:43:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "价格设置维度配置明细Vo")
public class PriceDimensionVo extends TenantVo {

    private static final long serialVersionUID = 2182432307402712078L;

    @ApiModelProperty("价格编码")
    private String priceCode;

    @ApiModelProperty("SAP价格编码")
    private String sapPriceCode;

    @ApiModelProperty("价格类型编码")
    private String typeCode;

    @ApiModelProperty("定价维度编码")
    private String typeDetailCode;

    @ApiModelProperty("价格维度编码")
    private String dimensionCode;

    @ApiModelProperty("维度数据源编码")
    private String relateCode;

    @ApiModelProperty("维度数据源名称（冗余）")
    private String relateName;
}
