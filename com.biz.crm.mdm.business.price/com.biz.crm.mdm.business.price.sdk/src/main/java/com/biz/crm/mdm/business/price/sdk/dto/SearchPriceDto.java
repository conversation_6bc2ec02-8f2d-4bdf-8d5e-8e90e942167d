package com.biz.crm.mdm.business.price.sdk.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 询价dto
 *
 * <AUTHOR>
 * @date 2022/1/4
 */
@Data
@ApiModel("询价dto")
public class SearchPriceDto {

    @ApiModelProperty("价格类型编码")
    private String priceTypeCode;

    @ApiModelProperty("价格绑定维度,商品维度")
    private String dimensionCode;

    @ApiModelProperty("价格绑定维度源数据编码集合，如商品编码集合")
    private Set<String> set;

    @ApiModelProperty("询价时间,不传默认服务端当前时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date searchTime;

    @ApiModelProperty("定价维度筛选项")
    private List<SearchPriceDimensionItemDto> dimensionItems;


    @ApiModelProperty("组织纬度的询价是否根据组织递归往上询价  默认false ")
    private Boolean orgRecursionFlag = false;

    private Map<String, List<String>> orgRecursionMap;
}
