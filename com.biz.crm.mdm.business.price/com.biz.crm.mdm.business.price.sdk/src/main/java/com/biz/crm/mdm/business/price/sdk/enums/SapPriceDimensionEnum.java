package com.biz.crm.mdm.business.price.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 价格维度类型枚举
 *
 * <AUTHOR>
 * @date 2022/1/4
 */
@Getter
@AllArgsConstructor
public enum SapPriceDimensionEnum {
    /**
     * SAP价格维度类型枚举
     */
    /**
     * DATA结构-A032 价格组/物料
     * DATA结构-A005 客户/物料
     * DATA结构-A901 销售组/物料
     * DATA结构-A902 销售部门/物料
     * DATA结构-A004 物料
     * DATA结构-A055 销售组织/订单工厂/物料（当发货工厂与销售组织不一致，订单需要有两个价格：对外销售价格和公司间价格）6000对6010
     */
    A032("A032", "ZP01", "ZP01-price_group-product-org_institution-channel", "价格组/物料/销售组织/分销渠道", 10),
    A005("A005", "ZP01", "ZP01-customer-product-org_institution-channel", "客户/物料/销售组织/分销渠道", 20),
    A901("A901", "ZP01", "ZP01-org_group-product-org_institution-channel", "销售组/物料/销售组织/分销渠道", 30),
    A902("A902", "ZP01", "ZP01-org_department-product-org_institution-channel", "销售部门/物料/销售组织/分销渠道", 40),
    A004("A901", "ZP01", "ZP01-product-org_institution-channel", "物料/销售组织/分销渠道", 50),
    A055("A055", "ZIV1", "ZIV1-order_factory-product-org_institution", "订单工厂/物料/销售组织", 60),
    ;
    /**
     * SAP价格类型
     */
    private final String tableKey;
    /**
     * 价格类型编码
     */
    private final String typeCode;

    /**
     * 定价维度编码
     */
    private final String typeDetailCode;

    /**
     * 描述
     */
    private final String desc;
    /**
     * 字典排序
     */
    private final int order;

    /**
     * 根据tableKey获取枚举
     *
     * @param tableKey
     * @return
     */
    public static SapPriceDimensionEnum getByTableKey(String tableKey) {
        if (StringUtils.isEmpty(tableKey)) {
            return null;
        }
        return Arrays.stream(SapPriceDimensionEnum.values())
                .filter(item -> Objects.equals(item.getTableKey(), tableKey))
                .findFirst().orElse(null);
    }

    public static Set<String> getTableKeys() {
        return Arrays.stream(SapPriceDimensionEnum.values())
                .map(SapPriceDimensionEnum::getTableKey).collect(Collectors.toSet());
    }
}
