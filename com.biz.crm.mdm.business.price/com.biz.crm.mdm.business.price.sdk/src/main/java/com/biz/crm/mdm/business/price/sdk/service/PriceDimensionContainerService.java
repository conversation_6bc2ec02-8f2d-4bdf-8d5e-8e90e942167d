package com.biz.crm.mdm.business.price.sdk.service;

import com.biz.crm.mdm.business.price.sdk.vo.CombineDimensionVo;
import com.biz.crm.mdm.business.price.sdk.vo.PriceDimensionDictVo;
import com.biz.crm.mdm.business.price.sdk.vo.PriceDimensionSelectVo;
import java.util.List;
import java.util.Set;

/**
 * 价格维度sdk接口
 *
 * <AUTHOR>
 * @date 2021/12/30
 */
public interface PriceDimensionContainerService {

  /**
   * 可选维度下拉
   *
   * @return
   */
  List<PriceDimensionDictVo> findDimensionSelect();

  /**
   * 根据维度编码集合获取维度编码、名称组合信息
   *
   * @param dimensionCodeSet
   * @return k-维度编码中横线组合,v-维度名称斜杠组合
   */
  CombineDimensionVo getCombineDimensionVo(Set<String> dimensionCodeSet);

  /**
   * 根据维度编码和keyword获取对应的维度数据源下拉
   *
   * @param code 维度编码
   * @param keyword 模糊搜索关键字
   * @param selectedCode
   * @return
   */
  List<PriceDimensionSelectVo> findSelectSourceDataByCodeAndKeyword(String code, String keyword, String selectedCode);

  /**
   * 根据定价维度编码获取维度描述
   *
   * @param typeDetailCode
   * @return
   */
  String findDimensionNameByTypeDetailCode(String typeDetailCode);
}
