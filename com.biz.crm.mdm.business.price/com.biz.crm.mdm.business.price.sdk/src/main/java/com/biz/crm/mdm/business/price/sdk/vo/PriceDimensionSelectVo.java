package com.biz.crm.mdm.business.price.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 价格维度对应的下拉数据
 *
 * <p>如商品、组织、渠道、价格组、终端、客户下拉等
 *
 * <AUTHOR>
 * @date 2021/12/30
 */
@Data
@ApiModel("价格维度对应的下拉VO")
public class PriceDimensionSelectVo {

    /**
     * 编码
     */
    @ApiModelProperty("编码")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;
}
