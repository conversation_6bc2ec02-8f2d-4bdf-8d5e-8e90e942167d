package com.biz.crm.mdm.business.price.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 询价model-vo
 *
 * <AUTHOR>
 * @date 2022/1/5
 */
@Data
@ApiModel("询价model-vo")
public class PriceModelVo {

    @ApiModelProperty("价格编码")
    private String priceCode;

    @ApiModelProperty("价格类型编码")
    private String typeCode;

    @ApiModelProperty("定价维度编码")
    private String typeDetailCode;

    @ApiModelProperty("价格")
    private BigDecimal price;

    @ApiModelProperty("成本价格")
    private BigDecimal costPrice;

    @ApiModelProperty("成本价格单位")
    private Integer costPriceUnit;

    @ApiModelProperty("物料成本单价")
    private BigDecimal materialUnitPrice;

    @ApiModelProperty("奶卡价格")
    private BigDecimal milkPrice;

    @ApiModelProperty("价格绑定维度")
    private String dimensionCode;

    @ApiModelProperty("价格绑定维度源数据编码")
    private String relateCode;

    @ApiModelProperty("优先级")
    private Integer sort;

    @ApiModelProperty("维度关联key")
    private String relateCodeJoin;

    @ApiModelProperty("组织编码辅助字段")
    private String orgCodeValue;

    @ApiModelProperty("生效标识的辅助字段")
    private Boolean validateFlag = true;
}
