package com.biz.crm.mdm.business.channel.org.relation.sdk.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 渠道组织关联门店Dto
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel("渠道组织关联门店Dto")
public class ChannelOrgRelationTerminalDto extends AbstractChannelOrgRelationDto {

  /**
   * 终端集合
   */
  @ApiModelProperty("终端集合")
  private List<RelationTerminalDto> terminals;
}
