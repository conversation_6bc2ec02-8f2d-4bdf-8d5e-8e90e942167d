package com.biz.crm.mdm.business.channel.org.relation.sdk.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 渠道组织关联门店PageDto
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel("渠道组织关联门店PageDto")
public class ChannelOrgRelationTerminalPageDto extends AbstractChannelOrgRelationDto {

  /**
   * 终端编码
   */
  @ApiModelProperty("终端编码")
  private String terminalCode;

  /**
   * 终端名称
   */
  @ApiModelProperty("终端名称")
  private String terminalName;
}
