package com.biz.crm.mdm.business.channel.org.relation.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.channel.org.relation.sdk.sdk.model.ChannelOrgRelationModel;
import com.biz.crm.mdm.business.channel.org.relation.sdk.sdk.service.ChannelOrgRelationVoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 渠道组织关联类型模型控制器
 *
 * <AUTHOR>
 */
@Api(tags = "渠道组织关联类型模型：ChannelOrgRelationModel：渠道组织关联类型模型")
@Slf4j
@RestController
@RequestMapping("/v1/channelOrgRelation/channelOrgRelationModel")
public class ChannelOrgRelationModelController {
  @Autowired(required = false)
  private ChannelOrgRelationVoService channelOrgRelationVoService;

  /**
   * 查询所有渠道组织关系模型
   *
   * @return
   */
  @GetMapping("findChannelOrgRelationModel")
  @ApiOperation(value = "查询所有渠道组织关系模型")
  public Result<List<ChannelOrgRelationModel>> findChannelOrgRelationModel() {
    try {
      List<ChannelOrgRelationModel> models = this.channelOrgRelationVoService.findChannelOrgRelationModel();
      return Result.ok(models);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

}
