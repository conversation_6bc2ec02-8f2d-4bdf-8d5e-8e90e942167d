<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.channel.org.relation.local.mapper.ChannelOrgRelationTerminalMapper">

  <select id="findByConditions" resultType="com.biz.crm.mdm.business.channel.org.relation.sdk.sdk.vo.ChannelOrgRelationTerminalVo">
    select
      cort.id,
      cort.tenant_code,
      cort.channel_org_code,
      cort.channel_org_rule_code,
      cort.terminal_code,
      cort.terminal_name,
      cort.create_time,
      cort.create_name
    from mdm_channel_org_relation_terminal cort
    <where>
      cort.tenant_code = #{dto.tenantCode}
      <if test="dto.channelOrgRuleCode != null and dto.channelOrgRuleCode != ''">
        <bind name="rightLikeChannelOrgRuleCode" value="dto.channelOrgRuleCode + '%'"/>
        and cort.channel_org_rule_code like #{rightLikeChannelOrgRuleCode}
      </if>
      <if test="dto.channelOrgCode != null and dto.channelOrgCode != ''">
        and cort.channel_org_code = #{dto.channelOrgCode}
      </if>
      <if test="dto.terminalCode != null and dto.terminalCode != ''">
        <bind name="likeTerminalCode" value="'%' + dto.terminalCode + '%'"/>
        and cort.terminal_code like #{likeTerminalCode}
      </if>
      <if test="dto.terminalName != null and dto.terminalName != ''">
        <bind name="likeTerminalName" value="'%' + dto.terminalName + '%'"/>
        and cort.terminal_name like #{likeTerminalName}
      </if>
    </where>
  </select>

</mapper>
