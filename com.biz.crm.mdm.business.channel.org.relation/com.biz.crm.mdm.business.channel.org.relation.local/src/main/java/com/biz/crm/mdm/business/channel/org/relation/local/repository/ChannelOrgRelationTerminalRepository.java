package com.biz.crm.mdm.business.channel.org.relation.local.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.channel.org.relation.local.entity.ChannelOrgRelationTerminal;
import com.biz.crm.mdm.business.channel.org.relation.local.mapper.ChannelOrgRelationTerminalMapper;
import com.biz.crm.mdm.business.channel.org.relation.sdk.sdk.dto.ChannelOrgRelationTerminalPageDto;
import com.biz.crm.mdm.business.channel.org.relation.sdk.sdk.vo.ChannelOrgRelationTerminalVo;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 渠道组织关联门店实体类
 *
 * <AUTHOR>
 */
@Component
public class ChannelOrgRelationTerminalRepository extends ServiceImpl<ChannelOrgRelationTerminalMapper, ChannelOrgRelationTerminal> {

  /**
   * 分页条件查询
   *
   * @param pageable
   * @param relationTerminalDto
   * @return
   */
  public Page<ChannelOrgRelationTerminalVo> findByConditions(Pageable pageable, ChannelOrgRelationTerminalPageDto relationTerminalDto) {
    Page<ChannelOrgRelationTerminalPageDto> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
    return this.baseMapper.findByConditions(page, relationTerminalDto);
  }

  /**
   * 通过租户编码和渠道组织规则编码查询渠道组织关联数据
   *
   * @param tenantCode
   * @param channelOrgCode
   * @param terminalCodes
   * @return
   */
  public List<ChannelOrgRelationTerminal> findByTenantCodeAndChannelOrgCodeAndTerminalCodes(String tenantCode, String channelOrgCode, List<String> terminalCodes) {
    return this.lambdaQuery()
        .eq(ChannelOrgRelationTerminal::getTenantCode, tenantCode)
        .eq(ChannelOrgRelationTerminal::getChannelOrgCode, channelOrgCode)
        .in(ChannelOrgRelationTerminal::getTerminalCode, terminalCodes)
            .list();
  }

  /**
   * 通过终端编码查询渠道组织关联数据
   *
   * @param tenantCode
   * @param terminalCodes
   * @return
   */
  public List<ChannelOrgRelationTerminal> findByTenantCodeAndTerminalCodes(String tenantCode, List<String> terminalCodes) {
    return this.lambdaQuery()
            .eq(ChannelOrgRelationTerminal::getTenantCode, tenantCode)
            .in(ChannelOrgRelationTerminal::getTerminalCode, terminalCodes)
            .list();
  }

  /**
   * 通过租户编号和渠道组织编码删除
   *
   * @param ids
   */
  public void deleteByIds(List<String> ids) {
    this.lambdaUpdate()
            .in(ChannelOrgRelationTerminal::getId,ids)
            .remove();
  }
}
