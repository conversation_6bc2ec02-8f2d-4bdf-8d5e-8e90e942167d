package com.biz.crm.mdm.business.channel.org.relation.local.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 渠道组织关联关系模块配置
 */
@Configuration
@EntityScan(basePackages = "com.biz.crm.mdm.business.channel.org.relation.local.entity")
@ComponentScan(basePackages = {"com.biz.crm.mdm.business.channel.org.relation"})
public class ChannelOrgRelationLocalConfig {

}