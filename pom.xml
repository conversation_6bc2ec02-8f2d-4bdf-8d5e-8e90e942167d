<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.6.9</version>
	</parent>

	<groupId>com.biz.crm.mdm</groupId>
	<artifactId>crm-mdm</artifactId>
	<packaging>pom</packaging>
	<version>202405</version>
	<modelVersion>4.0.0</modelVersion>
	<description>crm-mdm project for Spring Boot</description>

	<properties>
		<springboot.version>2.6.9</springboot.version>
		<spring.cloud.version>2021.0.3</spring.cloud.version>
		<spring.cloud.netflix.version>2.2.9.RELEASE</spring.cloud.netflix.version>
		<spring.cloud.nacos.discovery.version>2021.0.1.0</spring.cloud.nacos.discovery.version>

		<business.version>202405</business.version>
		<crm.workflow.version>202405</crm.workflow.version>

		<business.common.version>2024.05-DEV-SNAPSHOT</business.common.version>

		<crm.common.version>24.07.09</crm.common.version>
		<nebula.version>24.07.09</nebula.version>

		<mybatis.plus.boot.starter>3.4.2</mybatis.plus.boot.starter>
		<lombok.version>1.18.26</lombok.version>
		<springfox.version>2.10.5</springfox.version>
		<!--修改maven-jar-plugin版本-->
		<maven.jar.plugin.version>3.2.1</maven.jar.plugin.version>
		<maven.compiler.source>8</maven.compiler.source>
		<maven.compiler.target>8</maven.compiler.target>
		<xiaoymin.version>2.0.9</xiaoymin.version>
		<snakeyaml.version>1.33</snakeyaml.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring.cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<!--Lombok -->
			<dependency>
				<groupId>com.github.xiaoymin</groupId>
				<artifactId>knife4j-spring-boot-starter</artifactId>
				<version>${xiaoymin.version}</version>
			</dependency>
			<dependency>
				<groupId>org.projectlombok</groupId>
				<artifactId>lombok</artifactId>
				<version>${lombok.version}</version>
			</dependency>
			<!--     rocketmq start -->
			<dependency>
				<groupId>com.biz.crm.business.common</groupId>
				<artifactId>crm-common-rocketmq</artifactId>
				<version>${business.common.version}</version>
			</dependency>
			<!-- rocketmq end -->
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<!-- business-common -->
		<dependency>
			<groupId>com.biz.crm</groupId>
			<artifactId>crm-business-common</artifactId>
			<version>${crm.common.version}</version>
		</dependency>
		<!-- JUNIT 测试支持 -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- base-common start -->
		<dependency>
			<groupId>com.biz.crm.business.common</groupId>
			<artifactId>crm-base-common</artifactId>
			<version>${business.common.version}</version>
		</dependency>
		<!--     base-common end -->
		<dependency>
			<groupId>com.biz.crm.business.common.auth</groupId>
			<artifactId>auth-sdk</artifactId>
			<version>${business.common.version}</version>
		</dependency>
		<dependency>
			<groupId>com.biz.crm.business.common.log</groupId>
			<artifactId>crm-log-sdk</artifactId>
			<version>${business.common.version}</version>
		</dependency>
	</dependencies>

	<repositories>
		<repository>
			<id>dev.biz-united.com.cn</id>
			<name>ip822-releases</name>
			<url>http://maven.biz-united.com.cn:28080/artifactory/repo</url>
		</repository>
		<repository>
			<id>alimaven</id>
			<name>aliyun maven</name>
			<url>https://maven.aliyun.com/repository/central/</url>
		</repository>
		<repository>
			<id>central</id>
			<name>Central Repository</name>
			<url>https://repo.maven.apache.org/maven2</url>
		</repository>
	</repositories>

	<distributionManagement>
		<repository>
			<id>dev.biz-united.com.cn</id>
			<name>ip822-releases</name>
			<url>http://maven.biz-united.com.cn:28080/artifactory/ext-release-local</url>
		</repository>
		<snapshotRepository>
			<id>dev.biz-united.com.cn</id>
			<name>ip822-snapshots</name>
			<url>http://maven.biz-united.com.cn:28080/artifactory/ext-snapshot-local</url>
		</snapshotRepository>
	</distributionManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>versions-maven-plugin</artifactId>
				<configuration>
					<generateBackupPoms>false</generateBackupPoms>
				</configuration>
			</plugin>
			<!-- 跳过单元测试 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
			<!--修改maven-jar-plugin版本-->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>${maven.jar.plugin.version}</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${maven.compiler.source}</source>
					<target>${maven.compiler.target}</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<executions>
					<execution>
						<id>attach-sources</id>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

	<modules>
		<module>com.biz.crm.mdm.business.user</module>
		<module>com.biz.crm.mdm.business.region</module>
		<module>com.biz.crm.mdm.business.product.level</module>
		<module>com.biz.crm.mdm.business.table</module>
		<module>com.biz.crm.mdm.business.org</module>
		<module>com.biz.crm.mdm.business.material</module>
		<module>com.biz.crm.mdm.business.poi</module>
		<module>com.biz.crm.mdm.business.position.level</module>
		<module>com.biz.crm.mdm.business.position</module>
		<module>com.biz.crm.mdm.business.product</module>
		<module>com.biz.crm.mdm.business.dictionary</module>
		<module>com.biz.crm.mdm.business.terminal</module>
		<module>com.biz.crm.mdm.business.terminal.user</module>
		<module>com.biz.crm.mdm.business.customer.material</module>
		<module>com.biz.crm.mdm.business.customer.user</module>
		<module>com.biz.crm.mdm.business.customer</module>
		<module>com.biz.crm.mdm.business.material.unit</module>
		<module>com.biz.crm.mdm.business.customer.org</module>
		<module>com.biz.crm.mdm.business.channel.org.relation</module>
		<module>com.biz.crm.mdm.business.channel.org</module>
		<module>com.biz.crm.mdm.business.login.config</module>
		<module>com.biz.crm.mdm.business.fiscal.year</module>
		<module>com.biz.crm.mdm.business.warehouse</module>
		<module>com.biz.crm.mdm.business.product.spu</module>
		<module>com.biz.crm.mdm.business.price</module>
		<module>com.biz.crm.mdm.business.login.log</module>
		<module>com.biz.crm.mdm.business.news.notice</module>
		<module>com.biz.crm.mdm.business.message</module>
		<module>com.biz.crm.mdm.boot</module>
		<module>com.biz.crm.mdm.business.inquiry</module>
		<module>com.biz.crm.mdm.business.visitor</module>
		<module>com.biz.crm.mdm.business.customer.channel</module>
		<module>com.biz.crm.mdm.business.terminal.channel</module>
    <module>com.biz.crm.mdm.business.sale.territory</module>
    <module>com.biz.crm.mdm.business.sequese</module>
    <!-- <module>com.biz.crm.mdm.business.ie</module> -->
	</modules>
</project>
