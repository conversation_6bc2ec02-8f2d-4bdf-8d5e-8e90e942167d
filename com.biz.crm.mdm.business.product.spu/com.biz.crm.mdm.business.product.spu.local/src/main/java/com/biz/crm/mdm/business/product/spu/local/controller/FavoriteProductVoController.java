package com.biz.crm.mdm.business.product.spu.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.product.spu.sdk.service.FavoriteProductVoService;
import com.biz.crm.mdm.business.product.spu.sdk.vo.FavoriteProductVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 收藏夹spu
 *
 * <AUTHOR>
 * @date 2022/05/16
 */
@Slf4j
@Api(tags = "商品spu管理: FavoriteProductVO: 收藏夹SpuVo")
@RestController
@RequestMapping(value = {"/v1/productSpu/productSpuRelateSku"})
public class FavoriteProductVoController {

  @Autowired(required = false)
  private FavoriteProductVoService favoriteProductVoService;

  /**
   *
   * @param spuCodeList
   * @return
   */
  @ApiOperation(value = "根据spu编码获的spu和关联的sku信息")
  @GetMapping(value = {"/findDetailBySpuCodes"})
  public Result<List<FavoriteProductVO>> findDetailBySpuCodes(@RequestParam("spuCodeList") List<String> spuCodeList){
    try {
      return Result.ok(favoriteProductVoService.findDetailBySpuCodes(spuCodeList));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
