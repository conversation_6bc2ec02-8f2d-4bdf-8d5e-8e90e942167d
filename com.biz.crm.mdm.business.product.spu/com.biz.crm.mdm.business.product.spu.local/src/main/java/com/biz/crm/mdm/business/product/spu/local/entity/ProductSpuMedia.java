package com.biz.crm.mdm.business.product.spu.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.FileEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品spu图片视频信息(ProductSpuMedia)实体类
 *
 * <AUTHOR>
 * @since 2021-12-02 16:07:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("mdm_product_spu_media")
@Table(
    name = "mdm_product_spu_media",
    indexes = {
      @Index(name = "mdm_product_spu_media1", columnList = "tenant_code"),
      @Index(name = "mdm_product_spu_media2", columnList = "spu_code"),
    })
@ApiModel(value = "ProductSpuMedia", description = "商品spu图片视频信息")
@org.hibernate.annotations.Table(appliesTo = "mdm_product_spu_media", comment = "商品spu图片视频信息")
public class ProductSpuMedia extends FileEntity {

  /** 商品spu编码 */
  @ApiModelProperty("商品spu编码")
  @TableField(value = "spu_code")
  @Column(name = "spu_code", length = 32, columnDefinition = "varchar(32) COMMENT '商品spu编码'")
  private String spuCode;

  /** 文件类型，picture图片，video视频 */
  @ApiModelProperty("文件类型，picture图片，video视频")
  @TableField(value = "type")
  @Column(
      name = "type",
      length = 32,
      columnDefinition = "varchar(32) COMMENT '文件类型，picture图片，video视频'")
  private String type;

  /** 排序 */
  @ApiModelProperty("排序")
  @TableField(value = "sort")
  @Column(name = "sort", columnDefinition = "int COMMENT '排序'")
  private Integer sort;
}
