package com.biz.crm.mdm.business.product.spu.local.service.notifier;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.biz.crm.mdm.business.product.sdk.dto.ProductSingleEventDto;
import com.biz.crm.mdm.business.product.sdk.event.ProductEventListener;
import com.biz.crm.mdm.business.product.spu.local.service.ProductSpuService;
import com.google.common.collect.Sets;
import java.util.Set;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 下架同步事件实现
 *
 * <AUTHOR>
 * @date 2022/5/25
 */
@Component
class ProductSpuEventListenerImpl implements ProductEventListener {

  @Autowired(required = false) private ProductSpuService productSpuService;

  @Override
  @Transactional
  public void onDownShelf(ProductSingleEventDto productSingleEventDto) {
    final JSONObject o = (JSONObject) JSON.toJSON(productSingleEventDto.getNewest());
    Validate.notNull(o,"商品信息不能为空");
    Object o1 = o.get("productCode");
    Validate.notNull(o,"商品编码不能为空");
    Set<String> set = Sets.newHashSet();
    set.add(o1.toString());
    this.productSpuService.updateDownShelfByProductCodes(set);
  }
}
