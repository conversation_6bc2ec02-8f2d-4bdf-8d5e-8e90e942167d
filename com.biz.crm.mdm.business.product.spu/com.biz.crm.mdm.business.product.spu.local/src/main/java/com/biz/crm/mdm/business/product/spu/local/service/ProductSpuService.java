package com.biz.crm.mdm.business.product.spu.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.product.spu.local.entity.ProductSpu;
import com.biz.crm.mdm.business.product.spu.sdk.dto.ProductSpuPaginationDto;
import java.util.List;
import java.util.Set;
import org.springframework.data.domain.Pageable;

/**
 * 商品spu信息(ProductSpu)表服务接口
 *
 * <AUTHOR>
 * @date 2021-12-02 16:06:49
 */
public interface ProductSpuService {

  /**
   * 分页条件查询
   *
   * @param pageable
   * @param dto
   * @return
   */
  Page<ProductSpu> findByConditions(Pageable pageable, ProductSpuPaginationDto dto);

  /**
   * 按id集合查询详情
   *
   * @param idList
   * @return
   */
  List<ProductSpu> findDetailsByIds(List<String> idList);

  /**
   * 按spuCode集合查询详情
   *
   * @param spuCodeList
   * @return
   */
  List<ProductSpu> findDetailsBySpuCodes(List<String> spuCodeList);

  /**
   * 创建
   *
   * @param productSpu
   * @return
   */
  ProductSpu create(ProductSpu productSpu);

  /**
   * 更新
   *
   * @param productSpu
   * @return
   */
  ProductSpu update(ProductSpu productSpu);

  /**
   * 按id集合启用
   *
   * @param ids
   */
  void enableBatch(List<String> ids);

  /**
   * 按id集合禁用
   *
   * @param ids
   */
  void disableBatch(List<String> ids);

  /**
   * 逻辑删除
   *
   * @param ids
   */
  void updateDelFlagByIds(List<String> ids);

  /**
   * 上架
   *
   * @param ids
   */
  void upShelf(List<String> ids);

  /**
   * 下架
   *
   * @param ids
   */
  void downShelf(List<String> ids);

  /**
   * 上架
   *
   * @param spuCodeList
   */
  void upShelfBySpuCodes(List<String> spuCodeList);

  /**
   * 下架
   *
   * @param spuCodeList
   */
  void downShelfBySpuCodes(List<String> spuCodeList);

  /**
   * 根据spu对应的sku的上架状态更新对应spu的上架状态
   *
   * @param productCodeSet
   */
  void updateUpShelfByProductCodes(Set<String> productCodeSet);

  /**
   * 根据spu对应的sku的下架状态更新对应spu的下架状态
   *
   * @param productCodeSet
   */
  void updateDownShelfByProductCodes(Set<String> productCodeSet);
}
