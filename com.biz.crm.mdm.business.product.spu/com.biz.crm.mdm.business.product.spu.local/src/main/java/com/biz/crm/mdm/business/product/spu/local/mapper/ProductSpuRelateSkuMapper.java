package com.biz.crm.mdm.business.product.spu.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.mdm.business.product.spu.local.entity.ProductSpuRelateSku;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

/**
 * 商品spu关联的sku信息(ProductSpuRelateSku)表数据库访问层
 *
 * <AUTHOR>
 * @date 2021-12-02 16:08:18
 */
public interface ProductSpuRelateSkuMapper extends BaseMapper<ProductSpuRelateSku> {

  /**
   * 获取sku商品关联的对应的上下架状态的spu-sku关联集合
   *
   * @param productCodeList
   * @param isShelf
   * @param tenantCode
   * @return
   */
  List<ProductSpuRelateSku> findSupCodesByproductCodesAndIsShelf(
      @Param("list") Set<String> productCodeSet,
      @Param("isShelf") String isShelf,
      @Param("tenantCode") String tenantCode);
}
