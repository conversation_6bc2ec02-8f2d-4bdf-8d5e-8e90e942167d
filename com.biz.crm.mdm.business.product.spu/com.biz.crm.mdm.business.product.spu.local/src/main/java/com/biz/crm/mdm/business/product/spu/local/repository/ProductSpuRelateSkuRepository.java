package com.biz.crm.mdm.business.product.spu.local.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.product.spu.local.entity.ProductSpuRelateSku;
import com.biz.crm.mdm.business.product.spu.local.mapper.ProductSpuRelateSkuMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import java.util.List;
import java.util.Set;
import org.springframework.stereotype.Component;

/**
 * 商品spu关联的sku信息(repository)
 *
 * <AUTHOR>
 * @date 2021-12-02 16:08:19
 */
@Component
public class ProductSpuRelateSkuRepository
    extends ServiceImpl<ProductSpuRelateSkuMapper, ProductSpuRelateSku> {

  /**
   * 根据spu编码集合+上下架状态获取关联sku明细
   *
   * @param spuCodeList
   * @return
   */
  public List<ProductSpuRelateSku> findBySpuCodes(
      List<String> spuCodeList) {
    return this.lambdaQuery()
        .eq(ProductSpuRelateSku::getTenantCode, TenantUtils.getTenantCode())
        .in(ProductSpuRelateSku::getSpuCode, spuCodeList)
        .orderByAsc(ProductSpuRelateSku::getSort)
        .list();
  }

  public void deleteBySpuCodes(List<String> spuCodeList) {
    LambdaQueryWrapper<ProductSpuRelateSku> lambdaQuery = Wrappers.lambdaQuery();
    lambdaQuery
        .eq(ProductSpuRelateSku::getTenantCode, TenantUtils.getTenantCode())
        .in(ProductSpuRelateSku::getSpuCode, spuCodeList);
    this.baseMapper.delete(lambdaQuery);
  }

  /**
   * 获取sku商品关联的对应的上下架状态的spu-sku关联集合
   *
   * @param productCodeSet
   * @param isShelf
   * @return
   */
  public List<ProductSpuRelateSku> findSupCodesByproductCodesAndIsShelf(
      Set<String> productCodeSet, String isShelf) {
    return this.baseMapper.findSupCodesByproductCodesAndIsShelf(
        productCodeSet, isShelf, TenantUtils.getTenantCode());
  }

  /**
   * 根据商品编码获取关联主信息
   *
   * @param productCodeList
   * @return
   */
  public List<ProductSpuRelateSku> findByProductCodes(List<String> productCodeList) {
    return this.lambdaQuery()
        .eq(ProductSpuRelateSku::getTenantCode, TenantUtils.getTenantCode())
        .in(ProductSpuRelateSku::getProductCode, productCodeList)
        .list();
  }
}
