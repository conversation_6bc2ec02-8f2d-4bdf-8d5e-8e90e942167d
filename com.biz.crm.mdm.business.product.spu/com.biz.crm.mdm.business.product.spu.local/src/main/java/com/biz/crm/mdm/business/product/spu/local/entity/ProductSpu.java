package com.biz.crm.mdm.business.product.spu.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Transient;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 商品spu信息(ProductSpu)实体类
 *
 * <AUTHOR>
 * @since 2021-12-02 16:06:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("mdm_product_spu")
@Table(
    name = "mdm_product_spu",
    indexes = {
      @Index(name = "mdm_product_spu1", columnList = "tenant_code, del_flag, is_shelf, sort"),
      @Index(name = "mdm_product_spu2", columnList = "spu_code"),
    })
@ApiModel(value = "ProductSpu", description = "商品spu信息")
@org.hibernate.annotations.Table(appliesTo = "mdm_product_spu", comment = "商品spu信息")
public class ProductSpu extends TenantFlagOpEntity {

  /** 商品spu编码 */
  @ApiModelProperty("商品spu编码")
  @TableField(value = "spu_code")
  @Column(name = "spu_code", length = 32, columnDefinition = "varchar(32) COMMENT '商品spu编码'")
  private String spuCode;

  /** 商品spu名称 */
  @ApiModelProperty("商品spu名称")
  @TableField(value = "spu_name")
  @Column(name = "spu_name", length = 100, columnDefinition = "varchar(100) COMMENT '商品spu名称'")
  private String spuName;

  /** 上下架状态 */
  @ApiModelProperty("上下架状态")
  @TableField(value = "is_shelf")
  @Column(name = "is_shelf", length = 32, columnDefinition = "varchar(32) COMMENT '上下架状态'")
  private String isShelf;

  /** 开始时间 */
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @ApiModelProperty("开始时间")
  @TableField(value = "begin_date_time")
  @Column(name = "begin_date_time", columnDefinition = "datetime COMMENT '开始时间'")
  private Date beginDateTime;

  /** 结束时间 */
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @ApiModelProperty("结束时间")
  @TableField(value = "end_date_time")
  @Column(name = "end_date_time", columnDefinition = "datetime COMMENT '结束时间'")
  private Date endDateTime;

  /** 排序 */
  @ApiModelProperty("排序")
  @TableField(value = "sort")
  @Column(name = "sort", columnDefinition = "int COMMENT '排序'")
  private Integer sort;

  /** 商品标签 */
  @TableField(exist = false)
  @Transient
  @ApiModelProperty("商品标签")
  private List<ProductSpuTag> tagList;

  /** 关联的商品sku明细 */
  @TableField(exist = false)
  @Transient
  @ApiModelProperty("关联的商品sku明细")
  private List<ProductSpuRelateSku> productList;

  /** 关联的商品sku数量 */
  @TableField(exist = false)
  @Transient
  @ApiModelProperty("关联的商品sku数量")
  private Long productQuantity;

  /** 关联的图片明细 */
  @TableField(exist = false)
  @Transient
  @ApiModelProperty("关联的图片明细")
  private List<ProductSpuMedia> pictureList;

  /** 关联的视频明细 */
  @TableField(exist = false)
  @Transient
  @ApiModelProperty("关联的视频明细")
  private List<ProductSpuMedia> videoList;

  /** 商品spu详情介绍 */
  @TableField(exist = false)
  @Transient
  @ApiModelProperty("商品spu详情介绍")
  private ProductSpuIntroduction introduction;
}
