package com.biz.crm.mdm.business.product.spu.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.Transient;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * 商品spu标签信息(ProductSpuTag)实体类
 *
 * <AUTHOR>
 * @since 2021-12-02 17:07:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("mdm_product_spu_tag")
@Table(
    name = "mdm_product_spu_tag",
    indexes = {
      @Index(name = "mdm_product_spu_tag1", columnList = "tenant_code"),
      @Index(name = "mdm_product_spu_tag2", columnList = "tag_code"),
    })
@ApiModel(value = "ProductSpuTag", description = "商品spu标签信息")
@org.hibernate.annotations.Table(appliesTo = "mdm_product_spu_tag", comment = "商品spu标签信息")
public class ProductSpuTag extends TenantFlagOpEntity {

  /**
   * 标签编码
   */
  @ApiModelProperty("标签编码")
  @TableField(value = "tag_code")
  @Column(name = "tag_code", length = 64, columnDefinition = "varchar(64) COMMENT '标签编码'")
  private String tagCode;

  /**
   * 标签名称
   */
  @ApiModelProperty("标签名称")
  @TableField(value = "tag_name")
  @Column(name = "tag_name", length = 255, columnDefinition = "varchar(255) COMMENT '标签名称'")
  private String tagName;

  /**
   * 标签icon
   */
  @ApiModelProperty("标签icon")
  @TableField(value = "tag_url")
  @Column(name = "tag_url", length = 255, columnDefinition = "varchar(255) COMMENT '标签icon'")
  private String tagUrl;

  /**
   * 标签描述
   */
  @ApiModelProperty("标签描述")
  @TableField(value = "tag_description")
  @Column(name = "tag_description", length = 255, columnDefinition = "varchar(255) COMMENT '标签描述'")
  private String tagDescription;

  /** 关联的商品spu数量 */
  @TableField(exist = false)
  @Transient
  @ApiModelProperty("关联的商品spu数量")
  private Long spuQuantity;
}
