package com.biz.crm.mdm.business.product.spu.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.product.spu.local.entity.ProductSpuTagMapping;
import com.biz.crm.mdm.business.product.spu.local.service.ProductSpuTagMappingService;
import com.biz.crm.mdm.business.product.spu.sdk.dto.BindTagDto;
import com.biz.crm.mdm.business.product.spu.sdk.dto.ProductSpuTagMappingPaginationDto;
import com.biz.crm.mdm.business.product.spu.sdk.dto.RebindTagDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商品关联标签管理: ProductSpuTagMapping: 商品关联标签管理
 *
 * <AUTHOR>
 * @date 2022/6/14
 */
@Slf4j
@Api(tags = "商品关联标签管理: ProductSpuTagMapping: 商品关联标签管理")
@RestController
@RequestMapping(value = {"/v1/productSpuTagMapping/productSpuTagMapping"})
public class ProductSpuTagMappingController {

  @Autowired private ProductSpuTagMappingService productSpuTagMappingService;

  /**
   * 多条件分页查询(关联当前标签的)
   *
   * @return
   */
  @ApiOperation(value = "多条件分页查询(关联当前标签的)", notes = "分页参数为page和size，page从0开始，size默认50;")
  @GetMapping("findByConditions")
  public Result<Page<ProductSpuTagMapping>> findByConditions(
      @PageableDefault(50) Pageable pageable,
      @ApiParam(name = "paginationDto", value = "分页Dto")
          ProductSpuTagMappingPaginationDto paginationDto) {
    try {
      Page<ProductSpuTagMapping> result =
          this.productSpuTagMappingService.findByConditions(pageable, paginationDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 多条件分页查询(未关联当前标签的)
   *
   * @return
   */
  @ApiOperation(value = "多条件分页查询(未关联当前标签的)", notes = "分页参数为page和size，page从0开始，size默认50;")
  @GetMapping("findExcludeByConditions")
  public Result<Page<ProductSpuTagMapping>> findExcludeByConditions(
      @PageableDefault(50) Pageable pageable,
      @ApiParam(name = "paginationDto", value = "分页Dto")
          ProductSpuTagMappingPaginationDto paginationDto) {
    try {
      Page<ProductSpuTagMapping> result =
          this.productSpuTagMappingService.findExcludeByConditions(pageable, paginationDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 绑定标签
   *
   * @param dto
   * @return
   */
  @ApiOperation(value = "绑定标签")
  @PostMapping(value = "bindTag")
  public Result<?> bindTag(@RequestBody BindTagDto dto) {
    try {
      this.productSpuTagMappingService.bindTag(dto);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 替换标签
   *
   * @param dto
   * @return
   */
  @ApiOperation(value = "替换标签")
  @PostMapping(value = "rebindTag")
  public Result<?> rebindTag(@RequestBody RebindTagDto dto) {
    try {
      this.productSpuTagMappingService.rebindTag(dto);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 解绑标签
   *
   * @param dto
   * @return
   */
  @ApiOperation(value = "解绑标签")
  @PostMapping(value = "unbindTag")
  public Result<?> unbindTag(@RequestBody BindTagDto dto) {
    try {
      this.productSpuTagMappingService.unbindTag(dto);
      return Result.ok();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
