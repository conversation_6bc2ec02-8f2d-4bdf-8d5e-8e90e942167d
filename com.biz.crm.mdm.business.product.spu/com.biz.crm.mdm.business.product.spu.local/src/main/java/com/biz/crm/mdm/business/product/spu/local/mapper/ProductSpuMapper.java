package com.biz.crm.mdm.business.product.spu.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.product.spu.local.entity.ProductSpu;
import com.biz.crm.mdm.business.product.spu.sdk.dto.ProductSpuPaginationDto;
import org.apache.ibatis.annotations.Param;

/**
 * 商品spu信息(ProductSpu)表数据库访问层
 *
 * <AUTHOR>
 * @date 2021-12-02 16:06:48
 */
public interface ProductSpuMapper extends BaseMapper<ProductSpu> {

  /**
   * 分页列表
   *
   * @param page 分页信息
   * @param dto 分页参数dto
   * @return 分页列表
   */
  Page<ProductSpu> findByConditions(
      Page<ProductSpu> page, @Param("dto") ProductSpuPaginationDto dto);
}
