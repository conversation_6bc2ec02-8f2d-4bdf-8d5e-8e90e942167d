package com.biz.crm.mdm.business.product.spu.local.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.product.spu.local.entity.ProductSpuIntroduction;
import com.biz.crm.mdm.business.product.spu.local.mapper.ProductSpuIntroductionMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 * 商品spu介绍详情(repository)
 *
 * <AUTHOR>
 * @date 2021-12-02 16:07:21
 */
@Component
public class ProductSpuIntroductionRepository
    extends ServiceImpl<ProductSpuIntroductionMapper, ProductSpuIntroduction> {

  public List<ProductSpuIntroduction> findBySpuCodes(List<String> spuCodeList) {
    return this.lambdaQuery()
        .eq(ProductSpuIntroduction::getTenantCode, TenantUtils.getTenantCode())
        .in(ProductSpuIntroduction::getSpuCode, spuCodeList)
        .list();
  }

  public void deleteBySpuCodes(List<String> spuCodeList) {
    LambdaQueryWrapper<ProductSpuIntroduction> lambdaQuery = Wrappers.lambdaQuery();
    lambdaQuery
        .eq(ProductSpuIntroduction::getTenantCode, TenantUtils.getTenantCode())
        .in(ProductSpuIntroduction::getSpuCode, spuCodeList);
    this.baseMapper.delete(lambdaQuery);
  }
}
