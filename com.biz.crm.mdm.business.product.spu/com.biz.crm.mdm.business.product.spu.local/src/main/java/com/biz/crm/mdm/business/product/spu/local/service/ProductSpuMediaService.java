package com.biz.crm.mdm.business.product.spu.local.service;

import com.biz.crm.mdm.business.product.spu.local.entity.ProductSpuMedia;
import java.util.List;

/**
 * 商品spu图片视频信息(ProductSpuMedia)表服务接口
 *
 * <AUTHOR>
 * @date 2021-12-02 16:07:50
 */
public interface ProductSpuMediaService {

  /**
   * 保存spu商品管理的图片 视频信息
   *
   * @param mediaList
   * @param spuCode
   */
  void saveBatch(List<ProductSpuMedia> mediaList, String spuCode);

  /**
   * 根据spuCode集合获取关联的图片视频信息
   *
   * @param spuCodeList
   * @return
   */
  List<ProductSpuMedia> findBySpuCodes(List<String> spuCodeList);
}
