package com.biz.crm.mdm.business.product.spu.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.product.spu.local.entity.ProductSpuTag;
import com.biz.crm.mdm.business.product.spu.sdk.dto.ProductSpuTagPaginationDto;
import org.apache.ibatis.annotations.Param;

/**
 * 商品spu标签信息(ProductSpuTag)表数据库访问层
 *
 * <AUTHOR>
 * @date 2021-12-02 17:07:23
 */
public interface ProductSpuTagMapper extends BaseMapper<ProductSpuTag> {

  /**
   * 分页查询
   *
   * @param page
   * @param dto
   * @return
   */
  Page<ProductSpuTag> findByConditions(Page<ProductSpuTag> page,@Param("dto") ProductSpuTagPaginationDto dto);
}
