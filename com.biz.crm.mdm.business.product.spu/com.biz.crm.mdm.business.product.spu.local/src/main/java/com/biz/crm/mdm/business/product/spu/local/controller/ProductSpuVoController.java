package com.biz.crm.mdm.business.product.spu.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.product.spu.sdk.service.ProductSpuVoService;
import com.biz.crm.mdm.business.product.spu.sdk.vo.ProductSpuVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 商品spu管理
 *
 * <AUTHOR>
 * @date 2021/12/3
 */
@Slf4j
@Api(tags = "商品spu管理: ProductSpuVo: 商品spu管理")
@RestController
@RequestMapping(value = {"/v1/productSpu/productSpu"})
public class ProductSpuVoController {

  @Autowired(required = false) private ProductSpuVoService productSpuVoService;

  /**
   * 根据spuCode集合获取spu主信息
   *
   * @param spuCodeList
   * @return
   */
  @ApiOperation(value = "根据spuCode集合获取spu主信息")
  @PostMapping(value = {"/findBySpuCodes"})
  public Result<List<ProductSpuVo>> findBySpuCodes(@RequestBody List<String> spuCodeList) {
    try {
      List<ProductSpuVo> list = this.productSpuVoService.findBySpuCodes(spuCodeList);
      return Result.ok(list);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据spuCode集合获取spu全量关联明细
   *
   * @param spuCodeList
   * @return
   */
  @ApiOperation(value = "根据spuCode集合获取spu全量关联明细")
  @PostMapping(value = {"/findDetailsBySpuCodes"})
  public Result<List<ProductSpuVo>> findDetailsBySpuCodes(@RequestBody List<String> spuCodeList) {
    try {
      List<ProductSpuVo> list = this.productSpuVoService.findDetailsBySpuCodes(spuCodeList);
      return Result.ok(list);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据商品编码获取关联的spu主信息
   *
   * @param productCodeList
   * @return
   */
  @ApiOperation(value = "根据商品编码获取关联的spu主信息")
  @PostMapping(value = {"/findRelateSpuMapByProductCodes"})
  public Result<Map<String, List<ProductSpuVo>>> findRelateSpuMapByProductCodes(@RequestBody List<String> productCodeList) {
    try {
      Map<String, List<ProductSpuVo>> map =
          this.productSpuVoService.findRelateSpuMapByProductCodes(productCodeList);
      return Result.ok(map);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
