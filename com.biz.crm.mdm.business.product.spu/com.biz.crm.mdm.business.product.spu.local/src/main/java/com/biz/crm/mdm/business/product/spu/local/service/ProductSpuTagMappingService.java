package com.biz.crm.mdm.business.product.spu.local.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.product.spu.local.entity.ProductSpuTag;
import com.biz.crm.mdm.business.product.spu.local.entity.ProductSpuTagMapping;
import com.biz.crm.mdm.business.product.spu.sdk.dto.BindTagDto;
import com.biz.crm.mdm.business.product.spu.sdk.dto.ProductSpuTagMappingPaginationDto;
import com.biz.crm.mdm.business.product.spu.sdk.dto.RebindTagDto;
import java.util.List;
import org.springframework.data.domain.Pageable;

/**
 * 商品关联标签管理service
 *
 * <AUTHOR>
 * @date 2022/6/14
 */
public interface ProductSpuTagMappingService {

  /**
   * 多条件分页查询
   *
   * @param pageable
   * @param paginationDto
   * @return
   */
  Page<ProductSpuTagMapping> findByConditions(
      Pageable pageable, ProductSpuTagMappingPaginationDto paginationDto);

  /**
   * 多条件分页查询(未关联当前标签的)
   *
   * @param pageable
   * @param paginationDto
   * @return
   */
  Page<ProductSpuTagMapping> findExcludeByConditions(
      Pageable pageable, ProductSpuTagMappingPaginationDto paginationDto);

  /**
   * 批量保存spu商品标签
   *
   * @param tagList
   * @param spuCode
   */
  void saveBatch(List<ProductSpuTag> tagList, String spuCode);

  /**
   * 绑定标签
   *
   * @param dto
   */
  void bindTag(BindTagDto dto);

  /**
   * 替换标签
   *
   * @param dto
   */
  void rebindTag(RebindTagDto dto);

  /**
   * 解绑标签
   *
   * @param dto
   */
  void unbindTag(BindTagDto dto);
}
