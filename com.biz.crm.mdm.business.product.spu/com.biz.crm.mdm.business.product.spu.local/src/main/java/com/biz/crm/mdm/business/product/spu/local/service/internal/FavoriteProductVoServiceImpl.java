package com.biz.crm.mdm.business.product.spu.local.service.internal;

import com.biz.crm.mdm.business.product.sdk.service.ProductVoService;
import com.biz.crm.mdm.business.product.sdk.vo.ProductVo;
import com.biz.crm.mdm.business.product.spu.local.entity.ProductSpu;
import com.biz.crm.mdm.business.product.spu.local.entity.ProductSpuMedia;
import com.biz.crm.mdm.business.product.spu.local.entity.ProductSpuRelateSku;
import com.biz.crm.mdm.business.product.spu.local.repository.ProductSpuRelateSkuRepository;
import com.biz.crm.mdm.business.product.spu.local.repository.ProductSpuRepository;
import com.biz.crm.mdm.business.product.spu.local.service.ProductSpuMediaService;
import com.biz.crm.mdm.business.product.spu.sdk.enums.MediaTypeEnum;
import com.biz.crm.mdm.business.product.spu.sdk.service.FavoriteProductVoService;
import com.biz.crm.mdm.business.product.spu.sdk.vo.FavoriteProductVO;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2022/05/16
 */
@Service
public class FavoriteProductVoServiceImpl implements FavoriteProductVoService {

  @Autowired(required = false)
  private ProductSpuRepository productSpuRepository;

  @Autowired(required = false) private ProductSpuRelateSkuRepository productSpuRelateSkuRepository;

  @Autowired(required = false) private ProductSpuMediaService productSpuMediaService;

  @Autowired(required = false)
  private ProductVoService productVoService;

  @Autowired(required = false)
  @Qualifier("nebulaToolkitService")
  private NebulaToolkitService nebulaToolkitService;

  @Override
  public List<FavoriteProductVO> findDetailBySpuCodes(List<String> spuCodeList) {
    if (CollectionUtils.isEmpty(spuCodeList)) {
      return Lists.newLinkedList();
    }
    List<ProductSpu> list = this.productSpuRepository.findBySpuCodes(spuCodeList);
    if (CollectionUtils.isEmpty(list)) {
      return Lists.newLinkedList();
    }
    List<String> existsSpuCodeList =
        list.stream()
            .filter(a -> StringUtils.isNotBlank(a.getSpuCode()))
            .map(ProductSpu::getSpuCode)
            .collect(Collectors.toList());
    if (CollectionUtils.isEmpty(existsSpuCodeList)) {
      return Lists.newLinkedList();
    }
    Map<String, List<FavoriteProductVO>> mapSku = Maps.newHashMap();
    Map<String, List<ProductSpuMedia>> mapMedia = Maps.newHashMap();
    List<FavoriteProductVO> result = Lists.newArrayList();
    List<ProductSpuRelateSku> skuList = this.productSpuRelateSkuRepository.findBySpuCodes(existsSpuCodeList);

    List<ProductSpuMedia> mediaList = this.productSpuMediaService.findBySpuCodes(spuCodeList);
    if (CollectionUtils.isNotEmpty(skuList)) {
      List<String> productCodes = skuList.stream().map(ProductSpuRelateSku::getProductCode).collect(Collectors.toList());
      List<ProductVo> productVos = this.productVoService.findDetailsByIdsOrProductCodes(null, productCodes);
      existsSpuCodeList.forEach(spu-> {
        List<FavoriteProductVO> skus = Lists.newArrayList();
        skuList.forEach(sku->{
          if(spu.equals(sku.getSpuCode())){
            productVos.forEach(product->{
              if(sku.getProductCode().equals(product.getProductCode())){
                FavoriteProductVO productFavoriteVO = this.nebulaToolkitService.copyObjectByBlankList(product, FavoriteProductVO.class, HashSet.class, ArrayList.class);
                skus.add(productFavoriteVO);
              }
            });
          }
        });
        mapSku.put(spu, skus);
      });
    }
    if (CollectionUtils.isNotEmpty(mediaList)) {
      mapMedia =
          mediaList.stream()
              .filter(a -> StringUtils.isNotBlank(a.getSpuCode()))
              .collect(Collectors.groupingBy(ProductSpuMedia::getSpuCode));
    }
    for (ProductSpu item : list) {
      FavoriteProductVO favoriteVO = new FavoriteProductVO();
      favoriteVO.setProductCode(item.getSpuCode());
      favoriteVO.setProductName(item.getSpuName());
      favoriteVO.setSkus(mapSku.get(item.getSpuCode()));
      List<ProductSpuMedia> mediaList1 = mapMedia.get(item.getSpuCode());
      if (CollectionUtils.isNotEmpty(mediaList1)) {
        Map<String, List<ProductSpuMedia>> curMap =
            mediaList1.stream()
                .filter(a -> StringUtils.isNotBlank(a.getType()))
                .collect(Collectors.groupingBy(ProductSpuMedia::getType));
        favoriteVO.setPrimaryPictureUrl(
            CollectionUtils.isNotEmpty(curMap.get(MediaTypeEnum.PICTURE.getCode()))?curMap.get(MediaTypeEnum.PICTURE.getCode()).get(0).getFileCode():null);
      }
      result.add(favoriteVO);
    }
    return result;
  }
}
