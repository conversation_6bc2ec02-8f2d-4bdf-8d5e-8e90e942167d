package com.biz.crm.mdm.business.product.spu.local.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.mdm.business.product.spu.local.entity.ProductSpuMedia;
import com.biz.crm.mdm.business.product.spu.local.mapper.ProductSpuMediaMapper;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 * 商品spu图片视频信息(repository)
 *
 * <AUTHOR>
 * @date 2021-12-02 16:07:49
 */
@Component
public class ProductSpuMediaRepository extends ServiceImpl<ProductSpuMediaMapper, ProductSpuMedia> {

  /**
   * 根据spuCode和类型获取图片视频信息
   *
   * @param spuCodeList
   * @param type
   * @return
   */
  public List<ProductSpuMedia> findBySpuCodesAndType(List<String> spuCodeList, String type) {
    return this.lambdaQuery()
        .eq(ProductSpuMedia::getTenantCode, TenantUtils.getTenantCode())
        .eq(StringUtils.isNotBlank(type), ProductSpuMedia::getType, type)
        .in(ProductSpuMedia::getSpuCode, spuCodeList)
        .orderByAsc(ProductSpuMedia::getSort)
        .list();
  }

  public void deleteBySpuCodes(List<String> spuCodeList) {
    LambdaQueryWrapper<ProductSpuMedia> lambdaQuery = Wrappers.lambdaQuery();
    lambdaQuery
        .eq(ProductSpuMedia::getTenantCode, TenantUtils.getTenantCode())
        .in(ProductSpuMedia::getSpuCode, spuCodeList);
    this.baseMapper.delete(lambdaQuery);
  }
}
