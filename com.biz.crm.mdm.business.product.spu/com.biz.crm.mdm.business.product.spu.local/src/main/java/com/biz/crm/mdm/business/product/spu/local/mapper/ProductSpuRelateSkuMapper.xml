<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.product.spu.local.mapper.ProductSpuRelateSkuMapper">

  <select id="findSupCodesByproductCodesAndIsShelf"
    resultType="com.biz.crm.mdm.business.product.spu.local.entity.ProductSpuRelateSku">
    select c.spu_code,c.product_code
    from mdm_product_spu_relate_sku c
    where exists (
    select 1
    from mdm_product_spu_relate_sku a
    left join mdm_product_spu b on a.spu_code=b.spu_code and a.tenant_code=b.tenant_code
    where b.is_shelf=#{isShelf}
    and a.tenant_code=#{tenantCode}
    and a.product_code in(<foreach collection="list" item="item" separator=",">#{item}</foreach>)
    and c.tenant_code=a.tenant_code
    and c.spu_code=a.spu_code
    )
  </select>
</mapper>
