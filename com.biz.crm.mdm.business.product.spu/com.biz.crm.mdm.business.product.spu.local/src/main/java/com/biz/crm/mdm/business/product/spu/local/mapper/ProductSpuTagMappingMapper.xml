<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.product.spu.local.mapper.ProductSpuTagMappingMapper">

  <select id="findByConditions"
    resultType="com.biz.crm.mdm.business.product.spu.local.entity.ProductSpuTagMapping">
    select a.id,a.tag_code, c.tag_name, a.spu_code, b.spu_name, b.is_shelf
    from mdm_product_spu_tag_mapping a
    left join mdm_product_spu b on a.tenant_code = b.tenant_code and a.spu_code = b.spu_code
    left join mdm_product_spu_tag c
    on a.tenant_code = c.tenant_code and a.tag_code = c.tag_code
    <where>
      c.tenant_code=#{dto.tenantCode}
      <if test="dto.tagCode!=null and dto.tagCode!=''">
        and a.tag_code=#{dto.tagCode}
      </if>
      <if test="dto.delFlag!=null and dto.delFlag!=''">
        and b.del_flag=#{dto.delFlag}
        and c.del_flag=#{dto.delFlag}
      </if>
      <if test="dto.spuCode!=null and dto.spuCode!=''">
        <bind name="spuCode" value="'%'+dto.spuCode+'%'"/>
        and b.spu_code like #{spuCode}
      </if>
      <if test="dto.spuName!=null and dto.spuName!=''">
        <bind name="spuName" value="'%'+dto.spuName+'%'"/>
        and b.spu_name like #{spuName}
      </if>
    </where>
    order by b.spu_code asc
  </select>

  <select id="findExcludeByConditions"
    resultType="com.biz.crm.mdm.business.product.spu.local.entity.ProductSpuTagMapping">
    select a.id,a.spu_code, a.spu_name, a.is_shelf
    from mdm_product_spu a
    <where>
      <if test="dto.tenantCode!=null and dto.tenantCode!=''">
        and a.tenant_code=#{dto.tenantCode}
      </if>
      <if test="dto.tagCode!=null and dto.tagCode!=''">
        and not exists (select 1 from mdm_product_spu_tag_mapping b
            where b.tenant_code=a.tenant_code
            and b.spu_code=a.spu_code
            and b.tag_code=#{dto.tagCode})
      </if>
      <if test="dto.delFlag!=null and dto.delFlag!=''">
        and a.del_flag=#{dto.delFlag}
      </if>
      <if test="dto.spuCode!=null and dto.spuCode!=''">
        <bind name="spuCode" value="'%'+dto.spuCode+'%'"/>
        and a.spu_code like #{spuCode}
      </if>
      <if test="dto.spuName!=null and dto.spuName!=''">
        <bind name="spuName" value="'%'+dto.spuName+'%'"/>
        and a.spu_name like #{spuName}
      </if>
    </where>
    order by a.spu_code asc
  </select>

  <select id="findByProductSpuTagMappingQueryDto"
    resultType="com.biz.crm.mdm.business.product.spu.local.entity.ProductSpuTagMapping">
    select a.id, a.tag_code, b.tag_name, a.spu_code, c.spu_name, c.is_shelf from
    mdm_product_spu_tag_mapping a
    left join mdm_product_spu_tag b on a.tenant_code = b.tenant_code and a.tag_code = b.tag_code
    left join mdm_product_spu c on a.tenant_code = c.tenant_code and a.spu_code = c.spu_code
    <where>
      <if test="dto.tenantCode!=null and dto.tenantCode!=''">
        and a.tenant_code=#{dto.tenantCode}
      </if>
      <if test="dto.delFlag!=null and dto.delFlag!=''">
        and b.del_flag=#{dto.delFlag}
        and c.del_flag=#{dto.delFlag}
      </if>
      <if test="dto.spuCodes!=null and dto.spuCodes.size>0">
        and a.spu_code in(<foreach collection="dto.spuCodes" item="item" separator=",">
        #{item}</foreach>)
      </if>
      <if test="dto.tagCodes!=null and dto.tagCodes.size>0">
        and a.tag_code in(<foreach collection="dto.tagCodes" item="item" separator=",">
        #{item}</foreach>)
      </if>
    </where>
    order by c.spu_code asc
  </select>
</mapper>