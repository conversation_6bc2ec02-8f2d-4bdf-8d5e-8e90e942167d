package com.biz.crm.mdm.business.product.spu.local.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 商品spu管理config
 *
 * <AUTHOR>
 * @date 2021/12/2
 */
@Configuration
@EntityScan(basePackages = "com.biz.crm.mdm.business.product.spu.local")
@ComponentScan(basePackages = {"com.biz.crm.mdm.business.product.spu"})
public class ProductSpuLocalConfig {}
