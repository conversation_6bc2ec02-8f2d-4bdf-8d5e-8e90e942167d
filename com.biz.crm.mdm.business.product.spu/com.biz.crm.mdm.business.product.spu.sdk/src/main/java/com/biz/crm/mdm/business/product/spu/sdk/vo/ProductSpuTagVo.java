package com.biz.crm.mdm.business.product.spu.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 商品spu标签信息vo
 *
 * <AUTHOR>
 * @date 2021-12-02 17:06:54
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "商品spu标签信息Vo")
public class ProductSpuTagVo extends TenantFlagOpVo {

  /**
   * spu商品编码
   */
  @ApiModelProperty("spu商品编码")
  private String spuCode;

  /**
   * 标签编码
   */
  @ApiModelProperty("标签编码")
  private String tagCode;

  /**
   * 标签名称
   */
  @ApiModelProperty("标签名称")
  private String tagName;

  /**
   * 商品展示数量
   */
  @ApiModelProperty("商品展示数量")
  private Integer num;

  /**
   * 标签icon
   */
  @ApiModelProperty("标签icon")
  private String tagUrl;

  /**
   * 标签描述
   */
  @ApiModelProperty("标签描述")
  private String tagDescription;

  /**
   * spu商品集合
   */
  @ApiModelProperty("spu商品集合")
  private List<String> spuCodes;
}
