package com.biz.crm.mdm.business.product.spu.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品spu关联的sku信息vo
 *
 * <AUTHOR>
 * @date 2021-12-02 16:05:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "商品spu关联的sku信息Vo")
public class ProductSpuRelateSkuVo extends TenantOpVo {

  /** 商品spu编码 */
  @ApiModelProperty("商品spu编码")
  private String spuCode;

  /** 商品sku编码 */
  @ApiModelProperty("商品sku编码")
  private String productCode;

  /** 商品sku名称 */
  @ApiModelProperty("商品sku名称")
  private String productName;

  /** 商品sku类型 */
  @ApiModelProperty("商品sku类型")
  private String productType;

  /** 产品层级 */
  @ApiModelProperty("产品层级")
  private String productLevelName;

  /** 规格 */
  @ApiModelProperty("规格")
  private String spec;

  /** 上下架状态 */
  @ApiModelProperty("上下架状态")
  private String isShelf;

  /** 价格 */
  @ApiModelProperty("价格")
  private BigDecimal price;
}
