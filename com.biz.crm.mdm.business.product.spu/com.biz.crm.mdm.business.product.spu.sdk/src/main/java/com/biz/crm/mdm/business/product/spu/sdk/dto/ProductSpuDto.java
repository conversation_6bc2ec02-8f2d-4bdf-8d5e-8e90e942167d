package com.biz.crm.mdm.business.product.spu.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 商品spu信息dto
 *
 * <AUTHOR>
 * @date 2021-12-02 16:04:59
 */
@Data
@ApiModel(value = "ProductSpuDto", description = "商品spu信息dto")
public class ProductSpuDto extends TenantFlagOpDto {

  /** 商品spu编码 */
  @ApiModelProperty("商品spu编码")
  private String spuCode;

  /** 商品spu名称 */
  @ApiModelProperty("商品spu名称")
  private String spuName;

  /** 上下架状态 */
  @ApiModelProperty("上下架状态")
  private String isShelf;

  /** 开始时间 */
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @ApiModelProperty("开始时间")
  private Date beginDateTime;

  /** 结束时间 */
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @ApiModelProperty("结束时间")
  private Date endDateTime;

  /** 排序 */
  @ApiModelProperty("排序")
  private Integer sort;

  /** 商品标签 */
  @ApiModelProperty("商品标签")
  private List<ProductSpuTagDto> tagList;

  /** 关联的商品sku明细 */
  @ApiModelProperty("关联的商品sku明细")
  private List<ProductSpuRelateSkuDto> productList;

  /** 关联的图片明细 */
  @ApiModelProperty("关联的图片明细")
  private List<ProductSpuMediaDto> pictureList;

  /** 关联的视频明细 */
  @ApiModelProperty("关联的视频明细")
  private List<ProductSpuMediaDto> videoList;

  /** 商品spu详情介绍 */
  @ApiModelProperty("商品spu详情介绍")
  private ProductSpuIntroductionDto introduction;
}
