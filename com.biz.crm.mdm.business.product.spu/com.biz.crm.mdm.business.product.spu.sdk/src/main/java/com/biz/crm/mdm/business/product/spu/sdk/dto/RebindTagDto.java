package com.biz.crm.mdm.business.product.spu.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Set;
import lombok.Data;

/**
 * 替换标签dto
 *
 * <AUTHOR>
 * @date 2022/6/14
 */
@Data
@ApiModel(value = "RebindTagDto", description = "替换标签dto")
public class RebindTagDto {

  /** old标签编码 */
  @ApiModelProperty("old标签编码")
  private String oldTagCode;

  /** 标签编码 */
  @ApiModelProperty("标签编码")
  private String tagCode;

  /** spu编码集合 */
  @ApiModelProperty("spu编码集合")
  private Set<String> spuCodeSet;
}
