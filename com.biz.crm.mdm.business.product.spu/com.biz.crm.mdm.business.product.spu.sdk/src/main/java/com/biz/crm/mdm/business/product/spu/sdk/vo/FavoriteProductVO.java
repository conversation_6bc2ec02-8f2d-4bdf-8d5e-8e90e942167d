package com.biz.crm.mdm.business.product.spu.sdk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: heguanyun
 * @Date: 2022/4/6 09:49
 * @description:收藏商品
 */
@Data
@ApiModel(value = "FavoriteProductVO", description = "收藏夹VO")
public class FavoriteProductVO {
    @ApiModelProperty("图片url")
    private String primaryPictureUrl;

    @ApiModelProperty("商品编码")
    private String productCode;

    @ApiModelProperty("商品名称")
    private String productName;

    @ApiModelProperty("价格")
    private BigDecimal price;

    @ApiModelProperty("spu价格区间")
    private List<BigDecimal> priceShowList;

    @ApiModelProperty("库存数量")
    private int stockNum;

    @ApiModelProperty("销售单位")
    private String saleUnit;

    @ApiModelProperty("规格")
    private String spec;

    @ApiModelProperty("基本单位")
    private String baseUnit;

    @ApiModelProperty("sku集合")
    private List<FavoriteProductVO> skus;

    /** 图片信息 */
    @ApiModelProperty("附件信息，图片信息")
    private List<ProductSpuMediaVo> pictureMediaList;

    /** 视频信息 */
    @ApiModelProperty("附件信息，视频信息")
    private List<ProductSpuMediaVo> videoMediaList;

    /** 上下架状态 */
    @ApiModelProperty("上下架状态")
    private String isShelf;

    /** 是否允销 */
    @ApiModelProperty("是否允销")
    private Boolean isAllowsale;

    /** 启禁用状态 */
    @ApiModelProperty("启禁用状态")
    private String enableStatus;
}
