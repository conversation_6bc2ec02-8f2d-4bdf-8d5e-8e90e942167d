package com.biz.crm.mdm.business.product.spu.feign.service.internal;

import com.biz.crm.mdm.business.product.spu.feign.feign.FavoriteProductVoServiceFeign;
import com.biz.crm.mdm.business.product.spu.sdk.service.FavoriteProductVoService;
import com.biz.crm.mdm.business.product.spu.sdk.vo.FavoriteProductVO;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 * @date 2022/05/16
 */
@Service
public class FavoriteProductVoServiceImpl implements FavoriteProductVoService {

  @Autowired(required = false)
  private FavoriteProductVoServiceFeign favoriteProductVoServiceFeign;

  @Override
  public List<FavoriteProductVO> findDetailBySpuCodes(List<String> spuCodeList) {
    spuCodeList = Optional.ofNullable(spuCodeList).orElse(Lists.newLinkedList());
    return this.favoriteProductVoServiceFeign.findDetailBySpuCodes(spuCodeList).checkFeignResult();
  }

}
