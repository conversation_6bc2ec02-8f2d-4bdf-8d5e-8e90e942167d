package com.biz.crm.mdm.business.product.spu.feign.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.mdm.business.product.spu.feign.feign.AllowSaleListSpuVoFeign;
import com.biz.crm.mdm.business.product.spu.sdk.dto.AllowSaleListSpuPaginationDto;
import com.biz.crm.mdm.business.product.spu.sdk.service.AllowSaleListSpuVoService;
import com.biz.crm.mdm.business.product.spu.sdk.vo.AllowSaleListSpuVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 可购清单spu分页查询接口实现
 *
 * <AUTHOR>
 * @date 2021/12/6
 */
@Service
public class AllowSaleListSpuVoServiceImpl implements AllowSaleListSpuVoService {

  @Autowired(required = false) private AllowSaleListSpuVoFeign allowSaleListSpuVoFeign;

  @Override
  public Page<AllowSaleListSpuVo> onRequestByAllowSaleListSpuPaginationDto(
      AllowSaleListSpuPaginationDto dto) {
    return this.allowSaleListSpuVoFeign.onRequestByAllowSaleListSpuPaginationDto(dto).checkFeignResult();
  }

  @Override
  public Page<AllowSaleListSpuVo> onRequestByAllowSaleListSpuPrecisePaginationDto(
      AllowSaleListSpuPaginationDto dto) {
    return this.allowSaleListSpuVoFeign.onRequestByAllowSaleListSpuPrecisePaginationDto(dto).checkFeignResult();
  }
}
