package com.biz.crm.mdm.business.sequese.local.demo.service;

import com.biz.crm.common.sequese.sdk.generator.service.CrmBizSequenceServiceByLong;
import com.biz.crm.common.sequese.sdk.generator.service.aigorithm.CrmSequeseGeneratorByRedis;
import com.biz.crm.mdm.business.sequese.local.demo.config.BusinessSequeseConfig;
import com.biz.crm.mdm.business.sequese.sdk.demo.dto.CrmBizSequenceServiceByDemoDtoRedis;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 演示一个序列化服务（雪花算法，10进制）
 * 
 * <AUTHOR>
 * @version 1.0 Copyright 2023年4月24日 上午10:18:42
 */
@ToString
@Slf4j
@Component
@Order(5)
public class CrmBizSequenceServiceByRedis
    implements CrmBizSequenceServiceByLong<CrmBizSequenceServiceByDemoDtoRedis, CrmSequeseGeneratorByRedis> {
  /** 获取日志对象 */
  @Override
  public org.slf4j.Logger getLogger() {
    return this.log;
  }

  /**
   * 判断当前对象是否可以处理
   *
   * @param obj
   * @return
   */
  @Override
  public boolean match(Object obj) {
    // return CrmBizSequenceServiceByDemoDtoRedis.class.equals(obj.getClass());
    return obj instanceof CrmBizSequenceServiceByDemoDtoRedis;
  }

  @Override
  public String getSeqInfoByBizCode() {
    return "MDM_DEMO_SEQ_REDIS";
  }

  @Override
  public String getSeqInfoByColumnDesc() {
    return "字段描述3";
  }

  @Override
  public String getSeqInfoByModleName() {
    return "Redis算法，10进制";
  }

  @Override
  public String getSubSystem() {
    return BusinessSequeseConfig.subsystem;
  }

  /** 将要使用的序列算法通过Spring注入进来 */
  @Getter
  @Autowired
  CrmSequeseGeneratorByRedis generator;

  /**
   * 字符串格式化说明：https://blog.csdn.net/lonely_fireworks/article/details/7962171
   */
  private static final String SEQ_FORMAT_TEMP = "demo-redis-%08d-%010d";

  @Override
  public String generatorFormat(CrmBizSequenceServiceByDemoDtoRedis dto, Long sequese) {
    int demoId = dto.getDemoId();
    // 进行格式化组装序列字符串
    String nextVal = String.format(SEQ_FORMAT_TEMP, demoId, sequese);
    return nextVal;
  }

}


