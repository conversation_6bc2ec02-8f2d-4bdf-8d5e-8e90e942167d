package com.biz.crm.mdm.business.sequese.local.demo.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import com.biz.crm.common.sequese.sdk.generator.service.CrmBizSequenceServiceByString;
import com.biz.crm.mdm.business.sequese.local.demo.algorithm.CrmSequeseGeneratorByUuid;
import com.biz.crm.mdm.business.sequese.local.demo.config.BusinessSequeseConfig;
import com.biz.crm.mdm.business.sequese.sdk.demo.dto.CrmBizSequenceServiceByDemoDtoRedis;
import com.biz.crm.mdm.business.sequese.sdk.demo.dto.CrmBizSequenceServiceByDemoDtoUuid;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * 演示一个项目组自定义算法，该算法仅用于演示
 * 
 * <AUTHOR>
 * @version 1.0 Copyright 2023年4月24日 上午10:18:42
 * @param <K>
 */
@ToString
@Slf4j
@Component
@Order(20)
public class CrmBizSequenceServiceByUuid
    implements CrmBizSequenceServiceByString<CrmBizSequenceServiceByDemoDtoUuid, CrmSequeseGeneratorByUuid> {
  /**
   * 判断当前对象是否可以处理
   *
   * @param obj
   * @return
   */
  public boolean match(Object obj) {
    // return CrmBizSequenceServiceByDemoDtoUuid.class.equals(obj.getClass());
    return obj instanceof CrmBizSequenceServiceByDemoDtoUuid;
  }

  @Override
  public String getSeqInfoByBizCode() {
    return "MDM_DEMO_SEQ_UUID";
  }

  @Override
  public String getSeqInfoByColumnDesc() {
    return "字段描述4";
  }

  @Override
  public String getSeqInfoByModleName() {
    return "项目组自定义算法";
  }

  /** 获取日志对象 */
  public org.slf4j.Logger getLogger() {
    return this.log;
  }

  /** 将要使用的序列算法通过Spring注入进来 */
  @Getter
  @Autowired
  CrmSequeseGeneratorByUuid generator;

  /**
   * 字符串格式化说明：https://blog.csdn.net/lonely_fireworks/article/details/7962171
   */
  private static final String SEQ_FORMAT_TEMP = "demo-UUID-%08d-%s";

  @Override
  public String getSubSystem() {
    return BusinessSequeseConfig.subsystem;
  }

  @Override
  public String generatorFormat(CrmBizSequenceServiceByDemoDtoUuid dto, String sequese) {
    int demoId = dto.getDemoId();
    // 进行格式化组装序列字符串
    String nextVal = String.format(SEQ_FORMAT_TEMP, demoId, sequese);
    return nextVal;
  }
}


