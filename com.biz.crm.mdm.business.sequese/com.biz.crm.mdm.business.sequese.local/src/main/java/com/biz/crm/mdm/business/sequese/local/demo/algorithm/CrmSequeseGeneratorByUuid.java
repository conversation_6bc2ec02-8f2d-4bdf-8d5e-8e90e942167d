package com.biz.crm.mdm.business.sequese.local.demo.algorithm;

import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Component;
import com.biz.crm.common.sequese.sdk.exceptions.NotSupportMenthodException;
import com.biz.crm.common.sequese.sdk.generator.constant.SequeseConstant;
import com.biz.crm.common.sequese.sdk.generator.service.CrmSequeseGenerator;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import cn.hutool.core.lang.UUID;
import lombok.extern.slf4j.Slf4j;

/**
 * 使用 UUID 生成唯一 id <br/>
 * 
 * <AUTHOR>
 * @version 1.0 Copyright 2023-5-9 16:14:02
 */
@Slf4j
@Component
public class CrmSequeseGeneratorByUuid implements CrmSequeseGenerator<String> {
  @Override
  public void initAlgorithm() {
    // 暂无
  }

  /**
   * 获取下一个值
   */
  @Override
  public String nextVal(String subSystem, String bizCode) {
    UUID randomUUID = UUID.randomUUID();
    String string = randomUUID.toString();
    return string;
  }

  @Override
  public String[] nextValArray(String subSystem, String bizCode, int seqNum) {
    // 数据纠正
    if (seqNum < 1) {
      seqNum = 1;
    }
    // 批量赋值
    String[] batchNextVal = new String[seqNum];
    for (int idx = 0; idx < batchNextVal.length; idx++) {
      UUID randomUUID = UUID.randomUUID();
      batchNextVal[idx] = randomUUID.toString();
    }
    return batchNextVal;
  }

  @Override
  public String currVal(String subSystem, String bizCode) {
    log.warn("当前算法不支持");
    return null;
  }

  /**
   * 返回主键,当前算法对这些属性不敏感
   */
  public String getKey(String subSystem, String bizCode) {
    String key = "";
    return key;
  }

  /**
   * 当前算法规则
   *
   * @return
   */
  @Override
  public String getSequeseRuleCode() {
    return "UUID算法";
  }

}
