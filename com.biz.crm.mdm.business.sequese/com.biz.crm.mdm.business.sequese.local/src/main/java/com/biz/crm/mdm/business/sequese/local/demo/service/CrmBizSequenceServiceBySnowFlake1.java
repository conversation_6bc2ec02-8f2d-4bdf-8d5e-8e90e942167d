package com.biz.crm.mdm.business.sequese.local.demo.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import com.biz.crm.common.sequese.sdk.generator.service.CrmBizSequenceServiceByLong;
import com.biz.crm.common.sequese.sdk.generator.service.aigorithm.CrmSequeseGeneratorBySnowFlake;
import com.biz.crm.mdm.business.sequese.local.demo.config.BusinessSequeseConfig;
import com.biz.crm.mdm.business.sequese.sdk.demo.dto.CrmBizSequenceServiceByDemoDtoRedis;
import com.biz.crm.mdm.business.sequese.sdk.demo.dto.CrmBizSequenceServiceByDemoDtoSnowFlake1;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * 演示一个序列化服务（雪花算法，16进制）
 * 
 * <AUTHOR>
 * @version 1.0 Copyright 2023年4月24日 上午10:18:42
 * @param <K>
 */
@ToString
@Slf4j
@Component
@Order(1)
public class CrmBizSequenceServiceBySnowFlake1
    implements CrmBizSequenceServiceByLong<CrmBizSequenceServiceByDemoDtoSnowFlake1, CrmSequeseGeneratorBySnowFlake> {
  /** 获取日志对象 */
  public org.slf4j.Logger getLogger() {
    return this.log;
  }

  /**
   * 判断当前对象是否可以处理
   *
   * @param obj
   * @return
   */
  public boolean match(Object obj) {
    // return CrmBizSequenceServiceByDemoDtoSnowFlake1.class.equals(obj.getClass());
    return obj instanceof CrmBizSequenceServiceByDemoDtoSnowFlake1;
  }

  @Override
  public String getSeqInfoByBizCode() {
    return "MDM_DEMO_SEQ_SNOWFLAKE_1";
  }

  @Override
  public String getSeqInfoByColumnDesc() {
    return "字段描述";
  }

  @Override
  public String getSeqInfoByModleName() {
    return "雪花算法，16进制";
  }



  @Override
  public String getSubSystem() {
    return BusinessSequeseConfig.subsystem;
  }

  /** 将要使用的序列算法通过Spring注入进来 */
  @Getter
  @Autowired
  CrmSequeseGeneratorBySnowFlake generator;

  /**
   * 字符串格式化说明：https://blog.csdn.net/lonely_fireworks/article/details/7962171
   */
  private static final String SEQ_FORMAT_TEMP = "demo1-%08d-%016x";

  @Override
  public String generatorFormat(CrmBizSequenceServiceByDemoDtoSnowFlake1 dto, Long sequese) {
    int demoId = dto.getDemoId();
    // 进行格式化组装序列字符串
    String nextVal = String.format(SEQ_FORMAT_TEMP, demoId, sequese);
    return nextVal;
  }
}


