package com.biz.crm.mdm.business.sequese.local.demo.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * sequese模块配置
 * 
 * <AUTHOR>
 * @version 1.0 Copyright 2023年4月21日 下午6:40:24
 */
@Configuration
public class BusinessSequeseConfig {
  /** 子系统名字，例如：CRM-MDM，注意，该属性禁止在static类上赋值使用 */
  public static String subsystem;

  @Value("${spring.application.name:}")
  public void setSubsystem(String subsystem) {
    BusinessSequeseConfig.subsystem = subsystem;
  }
}
