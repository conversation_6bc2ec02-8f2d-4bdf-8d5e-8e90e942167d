package com.biz.crm.mdm.business.sequese.local.demo.algorithm;

import java.util.Calendar;
import java.util.Date;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Component;
import com.biz.crm.common.sequese.sdk.generator.constant.SequeseConstant;
import com.biz.crm.common.sequese.sdk.generator.service.CrmSequeseGenerator;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 使用 Redis 的自增原子性来生成唯一 id,每天一个 <br/>
 * 返回数值,逻辑参考DefaultGenerateCodeServiceImpl算法，该算法速度快，但是和数据库依赖
 * 
 * <AUTHOR>
 * @version 1.0 Copyright 2023-5-8 10:20:57
 */
@Slf4j
@Component
public class CrmSequeseGeneratorByRedisYyyyMmDd implements CrmSequeseGenerator<String> {
  /**
   * code生成规则[seq:code:default:CRM-MDM:BIZ_SEQ]
   */
  private final static String KEY_FORMAT = SequeseConstant.REDIS_SEQ_PRE + "code:%s";
  @Autowired
  private RedisTemplate redisTemplate;

  @Override
  public void initAlgorithm() {
    // 暂无
  }

  // 2022123100001
  private static final String SEQ_FORMAT_TEMP = "%s-%010d";

  private String getFormatVal(long andIncrement) {
    String val = String.format(SEQ_FORMAT_TEMP, DateFormatUtils.format(new Date(), "yyyyMMdd"), andIncrement);
    return val;
  }

  /**
   * 获取下一个值
   */
  @Override
  public String nextVal(String subSystem, String bizCode) {
    String redisCounter = getRedisCounter(subSystem, bizCode);
    long andIncrement = this.addAndGet(redisCounter, 1);
    String val = getFormatVal(andIncrement);
    return val;
  }


  @Override
  public String[] nextValArray(String subSystem, String bizCode, int seqNum) {
    // 数据纠正
    if (seqNum < 1) {
      seqNum = 1;
    }
    String redisCounter = getRedisCounter(subSystem, bizCode);
    long lastSeqVal = this.addAndGet(redisCounter, seqNum);

    // 批量赋值
    String[] batchNextVal = new String[seqNum];
    lastSeqVal++;
    for (int idx = 0; idx < batchNextVal.length; idx++) {
      batchNextVal[idx] = getFormatVal(lastSeqVal - seqNum + idx);
    }
    return batchNextVal;
  }

  @Override
  public String currVal(String subSystem, String bizCode) {
    return null;
  }

  /**
   * 返回主键
   */
  public String getKey(String subSystem, String bizCode) {
    String nowTimeStr = DateFormatUtils.format(new Date(), "yyyyMMdd");
    String key = String.format("%s:%s:%s:%s",
        TenantUtils.getTenantCode(), subSystem.toUpperCase(), bizCode, nowTimeStr);
    return key;
  }

  /**
   * 主键-生命周期(次日销毁)
   * 
   * @return
   */
  public Date getKeyExpireAtDate() {
    Calendar calendar = Calendar.getInstance();
    calendar.add(Calendar.DATE, +1); // 添加一天
    return calendar.getTime();
  }

  /**
   * 当前算法规则
   * 
   * @return
   */
  @Override
  public String getSequeseRuleCode() {
    return "REDIS算法";
  }

  private String getRedisCounter(String subSystem, String bizCode) {
    String key = this.getKey(subSystem, bizCode);
    String redisCounter = String.format(KEY_FORMAT, key);
    return redisCounter;
  }

  /**
   * 基于redis进行key上值的递增
   * 
   * @param redisCounter key的完整路径
   * @param stepNum
   * @return
   */
  private Long addAndGet(String redisCounter, int stepNum) {
    RedisAtomicLong entityIdCounter = new RedisAtomicLong(redisCounter, redisTemplate);
    long addAndGet = entityIdCounter.addAndGet(stepNum);
    // 将该对象过期时间进行设置
    Date date = getKeyExpireAtDate();
    if (date != null) {
      if (entityIdCounter.getExpire() == -1) {
        entityIdCounter.expireAt(date);
      }
    }
    return addAndGet;
  }


}
