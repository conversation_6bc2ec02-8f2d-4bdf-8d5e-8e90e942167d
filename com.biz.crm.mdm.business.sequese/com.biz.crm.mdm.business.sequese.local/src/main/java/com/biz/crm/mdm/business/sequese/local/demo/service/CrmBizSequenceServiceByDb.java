package com.biz.crm.mdm.business.sequese.local.demo.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import com.biz.crm.common.sequese.sdk.generator.service.CrmBizSequenceServiceByInteger;
import com.biz.crm.common.sequese.sdk.generator.service.aigorithm.CrmSequeseGeneratorByDb;
import com.biz.crm.mdm.business.sequese.local.demo.config.BusinessSequeseConfig;
import com.biz.crm.mdm.business.sequese.sdk.demo.dto.CrmBizSequenceServiceByDemoDtoDb;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * 演示一个序列化服务 <br/>
 *
 * <pre>
 * 1、原始序列号生成器：实现getGenerator方法，返回CrmSequeseGenerator对象
 * 2、日志对象：getLogger()方法，返回日志对象，方便接口层默认实现打印日志
 * 3、自定义格式化函数：generatorFormat(dto,sequese)，用于对业务对象进行自定义格式化
 * 4、子系统函数：getSubSystem() 用于返回当前归属哪个子系统，使用@Value("${spring.application.name:}")
 * </pre>
 * 
 * <AUTHOR>
 * @version 1.0 Copyright 2023年4月24日 上午10:18:42
 * @param <K>
 */
@ToString
@Slf4j
@Component
@Order(10)
public class CrmBizSequenceServiceByDb
    implements CrmBizSequenceServiceByInteger<CrmBizSequenceServiceByDemoDtoDb, CrmSequeseGeneratorByDb> {
  /** 获取日志对象 */
  public org.slf4j.Logger getLogger() {
    return this.log;
  }

  /**
   * 判断当前对象是否可以处理
   *
   * @param obj
   * @return
   */
  public boolean match(Object obj) {
    return obj instanceof CrmBizSequenceServiceByDemoDtoDb;
  }

  @Override
  public String getSeqInfoByBizCode() {
    return "MDM_DEMO_SEQ_DB";
  }

  @Override
  public String getSeqInfoByColumnDesc() {
    return "字段描述3";
  }

  @Override
  public String getSeqInfoByModleName() {
    return "仿Oracle序列";
  }

  /** 将要使用的序列算法通过Spring注入进来 */
  @Getter
  @Autowired
  CrmSequeseGeneratorByDb generator;

  /**
   * 字符串格式化说明：https://blog.csdn.net/lonely_fireworks/article/details/7962171
   */
  private static final String SEQ_FORMAT_TEMP = "demo3-%010d-%011d";


  public String generatorFormat(CrmBizSequenceServiceByDemoDtoDb dto, Integer sequese) {
    int demoId = dto.getDemoId();
    // 进行格式化组装序列字符串
    String nextVal = String.format(SEQ_FORMAT_TEMP, demoId, sequese);
    return nextVal;
  }

  public String getSubSystem() {
    return BusinessSequeseConfig.subsystem;
  }


}


