package com.biz.crm.mdm.business.sequese.local.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 序列化模块配置
 *
 * <AUTHOR>
 * @date 2023-5-9 09:23:16
 */
@Configuration
@EntityScan(basePackages = "com.biz.crm.mdm.business.sequese.local.entity")
@ComponentScan(basePackages = {"com.biz.crm.mdm.business.sequese"})
public class SequeseConfig {

}