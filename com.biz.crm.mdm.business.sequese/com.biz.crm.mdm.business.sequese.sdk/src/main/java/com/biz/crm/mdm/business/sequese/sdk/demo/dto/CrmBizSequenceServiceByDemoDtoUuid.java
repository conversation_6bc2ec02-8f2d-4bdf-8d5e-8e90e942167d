package com.biz.crm.mdm.business.sequese.sdk.demo.dto;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * demo使用的dto对象
 * 
 * <AUTHOR>
 * @version 1.0 Copyright 2023年4月24日 上午10:28:29
 */
@Data
@NoArgsConstructor
public class CrmBizSequenceServiceByDemoDtoUuid {
  /** 业务ID */
  int demoId = 0;
  /** 业务操作日期 */
  Date createDate = new Date();

  public CrmBizSequenceServiceByDemoDtoUuid(int demoId) {
    this.demoId = demoId;
  }
}
