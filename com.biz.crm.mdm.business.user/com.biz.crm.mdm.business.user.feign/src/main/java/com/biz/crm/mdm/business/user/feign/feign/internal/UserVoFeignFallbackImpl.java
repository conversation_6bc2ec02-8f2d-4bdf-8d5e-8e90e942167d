package com.biz.crm.mdm.business.user.feign.feign.internal;

import com.biz.crm.business.common.base.dto.SfaSendTaskDto;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.user.feign.feign.UserVoFeign;
import com.biz.crm.mdm.business.user.sdk.dto.UserConditionDto;
import com.biz.crm.mdm.business.user.sdk.dto.UserFeignDto;
import com.biz.crm.mdm.business.user.sdk.dto.UserStatisticsDto;
import com.biz.crm.mdm.business.user.sdk.dto.UserUnderDto;
import com.biz.crm.mdm.business.user.sdk.vo.UserUnderVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserWxPublicMsgVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserWxTemplateMsgDto;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户信息feign接口熔断类
 *
 * <AUTHOR>
 * @date 2022/3/2
 */
@Slf4j
@Component
public class UserVoFeignFallbackImpl implements FallbackFactory<UserVoFeign> {
    @Override
    public UserVoFeign create(Throwable throwable) {
        return new UserVoFeign() {
            @Override
            public Result<Set<String>> findUserNamesByCodesAndUserType(List<String> orgCodes, List<String> positionLevelCodes, String userType) {
                throw new UnsupportedOperationException("根据组织编码集合和职位级别集合和用户类型查询编码集合熔断");
            }

            @Override
            public Result<List<UserVo>> findByUserNames(List<String> userNames) {
                throw new UnsupportedOperationException("根据用户账号集合查询熔断");
            }

            @Override
            public Result<List<UserVo>> findByLotUserNames(List<String> userNames) {
                throw new UnsupportedOperationException("按大量用户账号查询集合(包含职位和组织)");
            }

            @Override
            public Result<UserVo> findByUserName(String userName) {
                throw new UnsupportedOperationException("根据用户账号查询熔断");
            }

            @Override
            public Result<UserVo> findDetailsByPhone(String phone) {
                throw new UnsupportedOperationException("按照用户手机号查询用户信息(包含职位和组织)熔断");
            }

            @PostMapping({"/v1/user/user/findUserNamesByUserDto"})
            @Override
            public Result<Set<String>> findUserNamesByUserDto(UserFeignDto dto) {
                throw new UnsupportedOperationException("按照条件查询用户账号集合熔断");
            }

            @Override
            public Result<UserVo> findRelationByUserNameAndOrgCodesOrOrgTypes(String userName, List<String> orgCodes, List<String> orgTypes) {
                throw new UnsupportedOperationException("检查用户账号与组织编码集合或组织类型（层级）集合是否关联熔断");
            }

            @Override
            public Result<Set<String>> findUserNamesByUserConditionDto(UserConditionDto dto) {
                throw new UnsupportedOperationException("根据用户多条件查询用户账号信息熔断");
            }

            @Override
            public Result<Set<String>> findPositionCodesByUserNames(List<String> userNames) {
                throw new UnsupportedOperationException("根据用户账号集合查询职位编码集合熔断");
            }

            @Override
            public Result<Map<String, String>> findPrimaryPositionByUserNames(List<String> userNames) {
                throw new UnsupportedOperationException("根据用户账号集合查询主职位信息熔断");
            }

            /**
             * 查询当前用户下级分页列表
             *
             * @param dto
             * @return
             */
            @Override
            public Result<List<UserUnderVo>> findUnderByConditions(UserUnderDto dto) {
                throw new UnsupportedOperationException("查询当前用户下级分页列表");
            }

            @Override
            public Result<List<UserVo>> findOrgAllChildrenUser(String orgCode) {
                throw new UnsupportedOperationException("查询组织下用户进入熔断");
            }

            @Override
            public Result<List<UserVo>> findTotalNum(UserStatisticsDto build) {
                throw new UnsupportedOperationException("查询用户总量进入熔断");
            }

            @Override
            public Result sendSfaTaskToUserName(SfaSendTaskDto dto) {
                throw new UnsupportedOperationException("发送任务到指定人员进入熔断");
            }

            @Override
            public Result<List<UserVo>> findUserListByOrgCode(String orgCode, String positionLevelCode) {
                throw new UnsupportedOperationException("查询组织下人员信息进入熔断");
            }

            @Override
            public Result<List<UserVo>> findUserListByOrgCodesAndCondition(UserStatisticsDto dto) {
                throw new UnsupportedOperationException("查询组织下人员信息进入熔断");
            }

            @Override
            public Result sendWxPublicMsgToUser(UserWxPublicMsgVo vo) {
                throw new UnsupportedOperationException("发送消息进入熔断!");
            }

            @Override
            public Result sendOrderWaitConfirmTemplateMsg(UserWxTemplateMsgDto vo) {
                throw new UnsupportedOperationException("发送待确认订单消息进入熔断!");
            }

            @Override
            public Result<UserVo> findDetailById(String id) {
                throw new UnsupportedOperationException("根据id查询详情进入熔断!");
            }
        };
    }
}
