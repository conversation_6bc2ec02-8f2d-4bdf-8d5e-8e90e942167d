package com.biz.crm.mdm.business.user.feign.feign.internal;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.user.feign.feign.UserRelWeChatVoFeign;
import com.biz.crm.mdm.business.user.sdk.dto.UserWeChatBindDto;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用户绑定微信feign接口熔断类
 *
 * <AUTHOR>
 * @date 2022/2/18
 */
@Slf4j
@Component
public class UserRelWeChatVoFeignFallbackImpl implements FallbackFactory<UserRelWeChatVoFeign> {
  @Override
  public UserRelWeChatVoFeign create(Throwable throwable) {
    return new UserRelWeChatVoFeign() {
      @Override
      public Result createByUserWeChatBindDto(UserWeChatBindDto dto) {
        throw new UnsupportedOperationException("通过用用户与微信关系熔断");
      }
    };
  }
}
