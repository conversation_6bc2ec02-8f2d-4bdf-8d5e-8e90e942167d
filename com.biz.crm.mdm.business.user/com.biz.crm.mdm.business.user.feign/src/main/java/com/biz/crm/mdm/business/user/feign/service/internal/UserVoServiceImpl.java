package com.biz.crm.mdm.business.user.feign.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.dto.SfaSendTaskDto;
import com.biz.crm.mdm.business.user.feign.feign.UserVoFeign;
import com.biz.crm.mdm.business.user.sdk.dto.*;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserUnderVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserWxPublicMsgVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserWxTemplateMsgDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 用户信息服务接口实现类
 *
 * <AUTHOR>
 * @date 2022/3/2
 */
@Service("FeignUserVoServiceImpl")
public class UserVoServiceImpl implements UserVoService {

    @Autowired(required = false)
    private UserVoFeign userVoFeign;

    @Override
    public Set<String> findUserNamesByCodesAndUserType(List<String> orgCodes, List<String> positionLevelCodes, String userType) {
        return this.userVoFeign.findUserNamesByCodesAndUserType(orgCodes, positionLevelCodes, userType).checkFeignResult();
    }

    @Override
    public UserVo findDetailsByPhone(String phone) {
        return this.userVoFeign.findDetailsByPhone(phone).checkFeignResult();
    }

    @Override
    public UserVo findByPhone(String phone) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Page<UserVo> findByConditions(Pageable pageable, UserPageDto dto) {
        throw new UnsupportedOperationException();
    }

    @Override
    public UserVo findDetailById(String id) {
        return this.userVoFeign.findDetailById(id).checkFeignResult();
    }

    @Override
    public UserVo findByUserName(String userName) {
        return this.userVoFeign.findByUserName(userName).checkFeignResult();
    }

    /**
     * 更新用户头像
     *
     * @param dto
     * @return
     */
    @Override
    public UserVo updateProfilePicture(UserDto dto) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void create(UserDto dto) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void update(UserDto dto) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void enableBatch(List<String> ids) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void disableBatch(List<String> ids) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void updateDelFlagByIds(List<String> ids) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void deleteUserLockByIds(List<String> ids) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void updatePasswordByIds(UserForceChangePasswordDto dto) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void updatePasswordByUserName(UserChangePasswordDto dto) {
        throw new UnsupportedOperationException();
    }

    @Override
    public UserVo findRelationByUserNameAndOrgCodesOrOrgTypes(String userName, List<String> orgCodes, List<String> orgTypes) {
        return this.userVoFeign.findRelationByUserNameAndOrgCodesOrOrgTypes(userName, orgCodes, orgTypes).checkFeignResult();
    }

    @Override
    public Set<String> findUserNamesByUserConditionDto(UserConditionDto dto) {
        return this.userVoFeign.findUserNamesByUserConditionDto(dto).checkFeignResult();
    }

    @Override
    public Set<String> findPositionCodesByUserNames(List<String> userNames) {
        return this.userVoFeign.findPositionCodesByUserNames(userNames).checkFeignResult();
    }

    @Override
    public Map<String, String> findPrimaryPositionByUserNames(List<String> userNames) {
        return this.userVoFeign.findPrimaryPositionByUserNames(userNames).checkFeignResult();
    }

    /**
     * 查询当前用户下级分页列表-根据角色区分
     *
     * @param pageable
     * @param dto
     * @return
     */
    @Override
    public Page<UserUnderVo> findUnderByRole(Pageable pageable, UserUnderDto dto) {
        throw new UnsupportedOperationException();
    }

    /**
     * 查询当前用户下级分页列表
     *
     * @param dto
     * @return
     */
    @Override
    public List<UserUnderVo> findUnderByConditions(UserUnderDto dto) {
        return this.userVoFeign.findUnderByConditions(dto).checkFeignResult();
    }


    @Override
    public List<UserVo> findOrgAllChildrenUser(String orgCode) {
        return this.userVoFeign.findOrgAllChildrenUser(orgCode).checkFeignResult();
    }

    @Override
    public List<UserVo> findByUserNames(List<String> userNames) {
        if (CollectionUtils.isEmpty(userNames)) {
            return new ArrayList<>(0);
        }
        return this.userVoFeign.findByUserNames(userNames).checkFeignResult();
    }

    @Override
    public void sendSfaTaskToUserName(SfaSendTaskDto dto) {
        if (ObjectUtils.isEmpty(dto)) {
            return;
        }
        this.userVoFeign.sendSfaTaskToUserName(dto);
    }

    @Override
    public List<UserVo> findUserListByOrgCode(String orgCode, String positionLevelCode) {
        return userVoFeign.findUserListByOrgCode(orgCode, positionLevelCode).checkFeignResult();
    }

    @Override
    public List<UserVo> findUserListByOrgCodesAndCondition(UserStatisticsDto dto) {
        return userVoFeign.findUserListByOrgCodesAndCondition(dto).checkFeignResult();
    }

    @Override
    public void sendWxPublicMsgToUser(UserWxPublicMsgVo vo) {
        this.userVoFeign.sendWxPublicMsgToUser(vo);
    }

    @Override
    public void sendOrderWaitConfirmTemplateMsg(UserWxTemplateMsgDto dto) {
        this.userVoFeign.sendOrderWaitConfirmTemplateMsg(dto);
    }
}
