package com.biz.crm.mdm.business.user.feign.feign;

import com.biz.crm.business.common.base.dto.SfaSendTaskDto;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.user.feign.feign.internal.UserVoFeignFallbackImpl;
import com.biz.crm.mdm.business.user.sdk.dto.UserConditionDto;
import com.biz.crm.mdm.business.user.sdk.dto.UserFeignDto;
import com.biz.crm.mdm.business.user.sdk.dto.UserStatisticsDto;
import com.biz.crm.mdm.business.user.sdk.dto.UserUnderDto;
import com.biz.crm.mdm.business.user.sdk.vo.UserUnderVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserWxPublicMsgVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserWxTemplateMsgDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户信息feign接口类
 *
 * <AUTHOR>
 * @date 2022/3/2
 */
@FeignClient(

        name = "${mdm.feign-client.name:crm-mdm}",
        path = "crm-mdm",
        fallbackFactory = UserVoFeignFallbackImpl.class
)
public interface UserVoFeign {

    /**
     * 根据组织编码集合和职位级别集合和用户类型查询编码集合
     * 注意orgCodes和positionLevelCodes只允许有一个为空,否则返回空
     *
     * @param orgCodes           组织编码集合
     * @param positionLevelCodes 职位级别编码集合
     * @param userType           用户类型
     * @return 登录用户编码集合
     */
    @ApiOperation(value = "根据组织编码集合和职位级别集合和用户类型查询编码集合")
    @GetMapping(value = {"/v1/user/user/findUserNamesByCodesAndUserType"})
    Result<Set<String>> findUserNamesByCodesAndUserType(@RequestParam(value = "orgCodes", required = false) List<String> orgCodes,
                                                        @RequestParam(value = "positionLevelCodes", required = false) List<String> positionLevelCodes,
                                                        @RequestParam(value = "userType") String userType);

    /**
     * 按用户账号查询集合(包含职位和组织)
     *
     * @param userNames 用户帐号集合
     * @return 用户信息集合
     */
    @ApiOperation(value = "按用户账号查询集合(包含职位和组织)")
    @GetMapping(value = {"/v1/user/user/findByUserNames"})
    Result<List<UserVo>> findByUserNames(@RequestParam(value = "userNames") List<String> userNames);

    /**
     * 按大量用户账号查询集合(包含职位和组织)
     *
     * @param userNames 用户帐号集合
     * @return 用户信息集合
     */
    @ApiOperation(value = "按大量用户账号查询集合(包含职位和组织)")
    @PostMapping(value = {"/v1/user/user/findByLotUserNames"})
    Result<List<UserVo>> findByLotUserNames(@RequestBody List<String> userNames);


    /**
     * 根据用户账号查询详情
     *
     * @param userName 用户账号
     * @return 用户信息
     */
    @ApiOperation(value = "根据用户账号查询详情")
    @GetMapping(value = {"/v1/user/user/findByUserName"})
    Result<UserVo> findByUserName(@RequestParam("userName") String userName);

    /**
     * 按照用户手机号查询用户信息(包含职位和组织)
     *
     * @param phone 用户手机号
     * @return 用户信息
     */
    @ApiOperation(value = "按用户账号查询集合(包含职位和组织)")
    @GetMapping(value = {"/v1/user/user/findDetailsByPhone"})
    Result<UserVo> findDetailsByPhone(@RequestParam(value = "phone") String phone);

    /**
     * 按照条件查询用户账号集合
     *
     * @param dto 查询集合
     * @return 用户账号信息集合
     */
    @ApiOperation(value = "按照条件查询用户账号集合")
    @PostMapping(value = {"/v1/user/user/findUserNamesByUserDto"})
    Result<Set<String>> findUserNamesByUserDto(@RequestBody UserFeignDto dto);

    /**
     * 检查用户账号与组织编码集合或组织类型（层级）集合是否关联
     *
     * @param userName 用户账号
     * @param orgCodes 组织编码集合
     * @param orgTypes 组织类型（层级）集合
     * @return UserVo
     */
    @ApiOperation(value = "检查用户账号与组织编码集合或组织类型（层级）集合是否关联")
    @GetMapping(value = {"/v1/user/user/findRelationByUserNameAndOrgCodesOrOrgTypes"})
    Result<UserVo> findRelationByUserNameAndOrgCodesOrOrgTypes(@RequestParam(value = "userName", required = true) String userName, @RequestParam(value = "orgCodes", required = false) List<String> orgCodes, @RequestParam(value = "orgTypes", required = false) List<String> orgTypes);

    /**
     * 根据用户多条件查询用户账号信息
     *
     * @param dto 参数dto
     * @return 用户账号信息
     */
    @ApiOperation(value = "根据用户多条件查询用户账号信息")
    @GetMapping(value = {"/v1/user/user/findUserNamesByUserConditionDto"})
    Result<Set<String>> findUserNamesByUserConditionDto(@SpringQueryMap UserConditionDto dto);

    /**
     * 根据用户账号集合查询职位编码集合
     *
     * @param userNames 用户账号
     * @return 职位编码
     */
    @ApiOperation(value = "根据用户账号集合查询职位编码集合")
    @GetMapping(value = {"/v1/user/user/findPositionCodesByUserNames"})
    Result<Set<String>> findPositionCodesByUserNames(@RequestParam("userNames") List<String> userNames);

    /**
     * 根据用户账号集合查询主职位信息
     *
     * @param userNames 用户账号
     * @return 职位编码
     */
    @ApiOperation(value = "根据用户账号集合查询主职位信息")
    @PostMapping(value = {"/v1/user/user/findPrimaryPositionByUserNames"})
    Result<Map<String, String>> findPrimaryPositionByUserNames(@RequestBody List<String> userNames);

    /**
     * 查询当前用户下级分页列表
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "查询当前用户下级分页列表")
    @PostMapping(value = {"/v1/user/user/findUnderByConditions"})
    public Result<List<UserUnderVo>> findUnderByConditions(@RequestBody UserUnderDto dto);


    @GetMapping("/v1/user/user/findOrgAllChildrenUser")
    @ApiOperation(value = "查询组织下所有的启用的用户")
    Result<List<UserVo>> findOrgAllChildrenUser(@RequestParam("orgCode") String orgCode);

    @PostMapping("/v1/user/user/findTotalNum")
    @ApiOperation(value = "查询组织下所有的启用的用户")
    Result<List<UserVo>> findTotalNum(@RequestBody UserStatisticsDto dto);

    @PostMapping("/v1/publicAccountSendMsgController/sendSfaTaskToUserName")
    @ApiOperation(value = "sfa任务发派到指定人员")
    Result sendSfaTaskToUserName(@RequestBody SfaSendTaskDto dto);

    @GetMapping("/v1/user/user/findUserListByOrgCode")
    @ApiOperation(value = "通过组织查询下级所有人员")
    Result<List<UserVo>> findUserListByOrgCode(@RequestParam("orgCode") String orgCode,
                                               @RequestParam(required = false, value = "positionLevelCode") String positionLevelCode);

    @ApiOperation(value = "通过组织和其他条件查询下级所有用户")
    @PostMapping("/v1/user/user/findUserListByOrgCodesAndCondition")
    Result<List<UserVo>> findUserListByOrgCodesAndCondition(@RequestBody UserStatisticsDto dto);

    @ApiOperation(value = "发送消息给对应人员到公众号")
    @PostMapping("/v1/user/user/sendWxPublicMsgToUser")
    Result sendWxPublicMsgToUser(@RequestBody UserWxPublicMsgVo vo);

    @ApiOperation(value = "发送待确认订单模版消息")
    @PostMapping("/v1/user/user/sendOrderWaitConfirmTemplateMsg")
    Result sendOrderWaitConfirmTemplateMsg(@RequestBody UserWxTemplateMsgDto vo);

    /**
     * 根据用户id查询详情
     *
     * @param id 用户id
     * @return 用户信息
     */
    @ApiOperation(value = "根据用户账号查询详情")
    @GetMapping(value = {"/v1/user/user/findDetailById"})
    Result<UserVo> findDetailById(@RequestParam("id") String id);

}
