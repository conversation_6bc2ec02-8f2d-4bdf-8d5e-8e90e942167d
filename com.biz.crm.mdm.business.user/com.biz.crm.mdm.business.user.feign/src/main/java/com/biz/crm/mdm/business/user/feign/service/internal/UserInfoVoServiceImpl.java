package com.biz.crm.mdm.business.user.feign.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.biz.crm.mdm.business.user.feign.feign.UserInfoVoFeign;
import com.biz.crm.mdm.business.user.sdk.service.UserInfoVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserInfoVo;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * 用户信息服务接口实现类
 *
 * <AUTHOR>
 * @date 2022/3/2
 */
@Service("FeignUserInfoVoServiceImpl")
public class UserInfoVoServiceImpl implements UserInfoVoService {

  @Autowired(required = false)
  private UserInfoVoFeign userInfoVoFeign;

  @Override
  public List<UserInfoVo> findByPositionCodes(Set<String> positionCodes) {
    if (CollectionUtil.isEmpty(positionCodes)) {
      return Lists.newArrayList();
    }
    return this.userInfoVoFeign.findByPositionCodes(positionCodes).checkFeignResult();
  }

  @Override
  public List<UserInfoVo> findByLotPositionCodes(Set<String> positionCodes) {
    if (CollectionUtil.isEmpty(positionCodes)) {
      return Lists.newArrayList();
    }
    return this.userInfoVoFeign.findByLotPositionCodes(positionCodes).checkFeignResult();
  }

  @Override
  public List<UserInfoVo> findByUserNames(Set<String> userNames) {
    if (CollectionUtil.isEmpty(userNames)) {
      return Lists.newArrayList();
    }
    return this.userInfoVoFeign.findByUserNames(userNames).checkFeignResult();
  }

}
