package com.biz.crm.mdm.business.user.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 用户信息vo
 *
 * <AUTHOR>
 * @since 2021-11-05 15:01:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "用户信息Vo")
public class UserInfoVo extends TenantVo {
  private static final long serialVersionUID = 105992914324272131L;

  @ApiModelProperty("用户账号登录信息")
  private String userName;

  @ApiModelProperty("用户编码")
  private String userCode;

  @ApiModelProperty("密码（经过加密的）")
  private String userPassword;

  @ApiModelProperty("用户类型")
  private String userType;

  @ApiModelProperty("人员姓名")
  private String fullName;

  @ApiModelProperty("性别")
  private String gender;

  @ApiModelProperty("电话")
  private String userPhone;

  @ApiModelProperty("生效时间")
  private String endTime;

  @ApiModelProperty("失效时间")
  private String startTime;

  @ApiModelProperty("邮箱")
  private String email;

  @ApiModelProperty("锁定状态 003/009")
  private String lockState;

  @ApiModelProperty("最后一次登录时间")
  private String lastLoginTime;

  @ApiModelProperty("用户头像url")
  private String userHeadUrl;

  @ApiModelProperty("是否强制修改密码")
  private Boolean forceChangePassword;

  @ApiModelProperty("工号")
  private String jobCode;

  @ApiModelProperty("员工类型")
  private String employeeType;

  @ApiModelProperty("员工状态")
  private String employeeStatus;

  @ApiModelProperty("身份证号码")
  private String identityCardNumber;

  @ApiModelProperty("身份证地址")
  private String identityCardAddress;

  @ApiModelProperty("民族")
  private String nationality;

  @ApiModelProperty("现住址")
  private String currentAddress;

  @ApiModelProperty("政治面貌")
  private String politicalAffiliation;

  @ApiModelProperty("每隔三个月修改密码更新时间")
  private Date updatePasswordDate;

  @ApiModelProperty("设备码")
  private String registrationId;

  @ApiModelProperty("分部id公司")
  private String subCompanyId;

  @ApiModelProperty("部门id")
  private String departmentId;

  @ApiModelProperty("OA人员id")
  private String oaId;

  @ApiModelProperty("销售公司")
  private String saleCompany;


  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty("职位名称")
  private String positionName;

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("组织名称")
  private String orgName;

  @ApiModelProperty("职位是否主职位 1是 0否")
  private Boolean primaryFlag;

  @ApiModelProperty("生效状态 003停用，009启用")
  private String enableStatus;

  @ApiModelProperty("是否删除 003停用，009启用")
  private String delFlag;
}

