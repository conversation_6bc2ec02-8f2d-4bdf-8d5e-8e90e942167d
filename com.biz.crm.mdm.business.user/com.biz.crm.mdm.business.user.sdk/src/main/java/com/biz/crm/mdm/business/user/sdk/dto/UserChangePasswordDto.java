package com.biz.crm.mdm.business.user.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "UserChangePasswordDto", description = "用户修改密码请求DTO")
public class UserChangePasswordDto {

    /**
     * 登录名
     */
    @ApiModelProperty("登录名")
    private String userName;

    /**
     * 旧密码
     */
    @ApiModelProperty("旧密码")
    private String oldPassword;

    /**
     * 新密码
     */
    @ApiModelProperty("新密码")
    private String newPassword;

}
