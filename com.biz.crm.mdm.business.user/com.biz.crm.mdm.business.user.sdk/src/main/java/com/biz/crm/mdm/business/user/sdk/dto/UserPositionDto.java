package com.biz.crm.mdm.business.user.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 职位-用户关联表dto
 *
 * <AUTHOR>
 * @since 2021-11-05 15:01:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "UserPositionDto", description = "职位-用户关联表dto")
public class UserPositionDto extends TenantDto {

  @ApiModelProperty("组织编码")
  private String orgCode;

  @ApiModelProperty("职位级别编码")
  private String positionLevelCode;

  @ApiModelProperty("职位级别名称")
  private String positionLevelName;

  @ApiModelProperty("上级职位编码")
  private String parentCode;

  @ApiModelProperty("职位编码")
  private String positionCode;

  @ApiModelProperty("用户登录名")
  private String userName;

  @ApiModelProperty("是否主职位 1是 0否")
  private Boolean primaryFlag;

  @ApiModelProperty("是否当前职位 1是 0否")
  private Boolean currentFlag;

  @ApiModelProperty("是否新增职位")
  private Boolean operationType;

  @ApiModelProperty("职位关联角色")
  private List<String> roleCodeList;

}

