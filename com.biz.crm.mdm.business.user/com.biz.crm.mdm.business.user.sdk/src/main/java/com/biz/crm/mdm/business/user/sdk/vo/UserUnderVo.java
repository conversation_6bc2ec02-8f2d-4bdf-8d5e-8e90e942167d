package com.biz.crm.mdm.business.user.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantFlagOpVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023-11-22 14:59
 * @description：当前用户关联下属用户信息Vo
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "当前用户关联下属用户信息Vo")
public class UserUnderVo extends TenantFlagOpVo {

  /**
   * 用户编码
   */
  @ApiModelProperty("用户编码")
  private String userCode;

  /**
   * 用户账号
   */
  @ApiModelProperty("用户账号")
  private String userName;

  /**
   * 人员姓名
   */
  @ApiModelProperty("人员姓名")
  private String fullName;

  /**
   * 电话
   */
  @ApiModelProperty("电话")
  private String userPhone;

  /**
   * 用户头像
   */
  @ApiModelProperty("用户头像")
  private String userHeadUrl;

  /**
   * 职位编码
   */
  @ApiModelProperty("职位编码")
  private String positionCode;

  /**
   * 职位名称
   */
  @ApiModelProperty("职位名称")
  private String positionName;

  /**
   * 下级职位编码
   */
  @ApiModelProperty("下级职位编码")
  private String underlingPositionCode;

  /**
   * 下级职位名称
   */
  @ApiModelProperty("下级职位名称")
  private String underlingPositionName;

  /**
   * 下级用户编码
   */
  @ApiModelProperty("下级用户编码")
  private String underlingUserCode;

  /**
   * 下级登录账号
   */
  @ApiModelProperty("下级登录账号")
  private String underlingUserName;

  /**
   * 下级用户姓名
   */
  @ApiModelProperty("下级用户姓名")
  private String underlingFullName;

  /**
   * 下级组织编码
   */
  @ApiModelProperty("下级组织编码")
  private String underlingOrgCode;

  /**
   * 下级组织名称
   */
  @ApiModelProperty("下级组织名称")
  private String underlingOrgName;

  /**
   * 下级职位级别编码
   */
  @ApiModelProperty("下级职位级别编码")
  private String underlingPositionLevelCode;

  /**
   * 下级职位级别名称
   */
  @ApiModelProperty("下级职位级别名称")
  private String underlingPositionLevelName;

  /**
   * 下级用户电话
   */
  @ApiModelProperty("下级用户电话")
  private String underlingUserPhone;

}
