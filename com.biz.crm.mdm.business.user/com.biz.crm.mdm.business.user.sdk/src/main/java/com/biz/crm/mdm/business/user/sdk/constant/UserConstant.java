package com.biz.crm.mdm.business.user.sdk.constant;

/**
 * 用户常量类
 *
 * <AUTHOR>
 */
public class UserConstant {

    /**
     * 用户编码
     */
    public static final String USER_CODE = "YH";
    /**
     * 加密key
     */
    public final static String ENCRYPT_KEY = "1234123412ABCDEF";

    /**
     * 登录错误redis记录key
     */
    public static final String LOGIN_FAILED_KEY = "crm:login:failed:%s:%s";
    /**
     * 登录错误的分布式锁
     */
    public static final String LOGIN_LOCK_KEY = "crm:login:key:%s:%s";

    /**
     * 用户默认性别
     */
    public final static String USER_GENDER = "0";

    /**
     * 拉取用户信息 redis 锁定
     */
    public static final String PULL_USER_INFO = "pull:user_info:";

    /**
     * SFA 固定 推广人员角色  角色编码
     */
    public static final String SFA_EXTENSION_ROLE_CEDE = "JS0007";

    /**
     * SFA 固定 临时人员角色  角色编码
     */
    public static final String SFA_TEMP_ROLE_CEDE = "JS0216";

    /**
     * SFA 固定 业代人员角色  角色编码
     */
    public static final String SFA_YD_ROLE_CEDE = "JS0008";

    /**
     * 经销商用户
     */
    public static final String SFA_DEALER_ROLE_CODE = "JS2010";


    public static final String ADMIN = "ADMIN";

    /**
     * Ry@.2024      7811cfebdced41366ebf4818d1e5c00e
     * Ryytn@.2024   56bdecc790be491f9311f3f0991c5d12
     * Ryytn@2024    c143aca366ba37f774e6116f1430021a
     */
    public static final String DEFAULT_PASSWORD = "c143aca366ba37f774e6116f1430021a";

}
