package com.biz.crm.mdm.business.user.sdk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.dto.SfaSendTaskDto;
import com.biz.crm.mdm.business.user.sdk.dto.*;
import com.biz.crm.mdm.business.user.sdk.vo.UserUnderVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserWxPublicMsgVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserWxTemplateMsgDto;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户表(User)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-04 17:38:46
 */
public interface UserVoService {

    /**
     * 分页条件查询
     *
     * @param pageable
     * @param dto
     * @return
     */
    Page<UserVo> findByConditions(Pageable pageable, UserPageDto dto);

    /**
     * 按id查询详情
     *
     * @param id
     * @return
     */
    UserVo findDetailById(String id);

    /**
     * 按用户账号查询详情
     *
     * @param userName 用户帐号
     * @return
     */
    UserVo findByUserName(String userName);

    /**
     * 更新用户头像
     *
     * @param dto
     * @return
     */
    UserVo updateProfilePicture(UserDto dto);

    /**
     * 创建
     *
     * @param dto
     */
    void create(UserDto dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(UserDto dto);

    /**
     * 按id集合启用
     *
     * @param ids
     */
    void enableBatch(List<String> ids);

    /**
     * 按id集合禁用
     *
     * @param ids
     */
    void disableBatch(List<String> ids);

    /**
     * 逻辑删除
     *
     * @param ids
     */
    void updateDelFlagByIds(List<String> ids);

    /**
     * 解锁
     *
     * @param ids
     */
    void deleteUserLockByIds(List<String> ids);

    /**
     * 强制修改密码
     *
     * @param dto
     */
    void updatePasswordByIds(UserForceChangePasswordDto dto);

    /**
     * 修改密码
     *
     * @param dto
     */
    void updatePasswordByUserName(UserChangePasswordDto dto);

    /**
     * 根据组织编码集合和职位级别集合和用户类型查询编码集合
     * 注意orgCodes和positionLevelCodes只允许有一个为空
     * {@link com.biz.crm.mdm.business.user.sdk.enums.UserTypeEnum}
     *
     * @param orgCodes           组织编码集合
     * @param positionLevelCodes 职位级别编码集合
     * @param userType           用户类型
     * @return 登录用户编码集合
     */
    default Set<String> findUserNamesByCodesAndUserType(List<String> orgCodes, List<String> positionLevelCodes, String userType) {
        return new HashSet<>();
    }

    /**
     * 按照用户手机号查询用户信息(包含职位和组织)
     *
     * @param phone 用户手机号
     * @return 用户信息
     */
    default UserVo findDetailsByPhone(String phone) {
        return null;
    }

    /**
     * 按手机号查询用户（新增有做系统中手机号唯一限制）
     *
     * @param phone 手机号
     * @return
     */
    UserVo findByPhone(String phone);

    /**
     * 检查用户账号与组织类型（层级）是否关联
     *
     * @param userName 用户账号
     * @param orgCodes 组织编码集合
     * @param orgTypes 组织类型（层级）集合
     * @return UserVo
     */
    UserVo findRelationByUserNameAndOrgCodesOrOrgTypes(String userName, List<String> orgCodes, List<String> orgTypes);

    /**
     * 根据用户多条件查询用户账号信息
     *
     * @param dto 参数dto
     * @return 用户账号信息
     */
    Set<String> findUserNamesByUserConditionDto(UserConditionDto dto);

    /**
     * 根据用户账号查询职位信息
     *
     * @param userNames
     * @return 职位编码集合
     */
    Set<String> findPositionCodesByUserNames(List<String> userNames);

    /**
     * 根据用户账号查询主职位信息
     *
     * @param userNames
     * @return 职位编码集合
     */
    Map<String, String> findPrimaryPositionByUserNames(List<String> userNames);

    /**
     * 查询当前用户下级分页列表-根据角色区分
     *
     * @param pageable
     * @param dto
     * @return
     */
    Page<UserUnderVo> findUnderByRole(Pageable pageable, UserUnderDto dto);

    /**
     * 查询当前用户下级分页列表
     *
     * @param dto
     * @return
     */
    List<UserUnderVo> findUnderByConditions(UserUnderDto dto);

    List<UserVo> findOrgAllChildrenUser(String orgCode);

    /**
     * 按用户账号查询详情
     *
     * @param userNames 用户帐号列表
     * @return
     */
    List<UserVo> findByUserNames(List<String> userNames);

    /**
     * 发送任务到指定人员
     *
     * @param dto
     */
    default void sendSfaTaskToUserName(SfaSendTaskDto dto) {

    }


    /**
     * 通过组织查询下级所有人员
     * @param orgCode
     * @param positionLevelCode
     * @return
     */
    List<UserVo> findUserListByOrgCode(String orgCode, String positionLevelCode);

    /**
     * 通过组织和其他条件查询下级所有人员
     * @param dto
     * @return
     */
    List<UserVo> findUserListByOrgCodesAndCondition(@RequestBody UserStatisticsDto dto);

    void sendWxPublicMsgToUser(UserWxPublicMsgVo vo);

    void sendOrderWaitConfirmTemplateMsg(UserWxTemplateMsgDto dto);
}
