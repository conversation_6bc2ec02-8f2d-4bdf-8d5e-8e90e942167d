package com.biz.crm.mdm.business.user.sdk.vo;

import com.biz.crm.business.common.sdk.vo.TenantVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户与微信关联表vo
 *
 * <AUTHOR>
 * @since 2021-11-05 15:04:31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "用户与微信关联表Vo")
public class UserRelWeChatVo extends TenantVo {
  private static final long serialVersionUID = 3324143260377323092L;

  @ApiModelProperty("微信来源模块")
  private String originModule;

  @ApiModelProperty("用户登录名")
  private String userName;

  @ApiModelProperty("微信openId")
  private String openId;

  @ApiModelProperty("当前是否处于登录状态 1是 0否")
  private Boolean loginStatus;

  @ApiModelProperty("微信头像")
  private String headImgUrl;

  @ApiModelProperty("微信昵称")
  private String nickName;

}

