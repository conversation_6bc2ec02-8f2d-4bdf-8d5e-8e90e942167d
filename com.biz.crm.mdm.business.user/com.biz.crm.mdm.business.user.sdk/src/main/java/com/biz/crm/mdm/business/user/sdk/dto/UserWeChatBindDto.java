package com.biz.crm.mdm.business.user.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户与微信绑定dto
 *
 * <AUTHOR>
 * @date 2022/2/18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "UserWeChatBindDto", description = "用户与微信绑定dto")
public class UserWeChatBindDto extends TenantDto {

  @ApiModelProperty("微信来源模块")
  private String originModule;

  @ApiModelProperty("微信openId")
  private String openId;

  @ApiModelProperty("用户登录名")
  private String userName;
}

