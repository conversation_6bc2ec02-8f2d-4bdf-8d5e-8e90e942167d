package com.biz.crm.mdm.business.user.sdk.dto;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

@Data
public class UserStatisticsDto implements Serializable {

    private static final long serialVersionUID = 362498820763181265L;

    private String enableStatus;
    private String ignorePositionLevelCode;
    private String employeeType;

    private List<String> orgCodes;

    private String excludePositionLevelCode;
}
