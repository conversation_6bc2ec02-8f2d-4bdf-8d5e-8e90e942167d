package com.biz.crm.mdm.business.user.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 用户多条件查询dto
 *
 * <AUTHOR>
 * @date 2022/6/25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "UserConditionDto", description = "用户多条件查询dto")
public class UserConditionDto extends TenantFlagOpDto {

  /**
   * 人员姓名
   */
  @ApiModelProperty("人员姓名")
  private String fullName;

}

