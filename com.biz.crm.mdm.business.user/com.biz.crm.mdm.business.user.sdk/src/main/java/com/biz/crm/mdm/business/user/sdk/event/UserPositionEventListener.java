package com.biz.crm.mdm.business.user.sdk.event;

import com.biz.crm.mdm.business.user.sdk.vo.UserPositionVo;
import java.util.List;

/**
 * 职位-用户关联表变更事件通知接口
 *
 * <AUTHOR>
 * @since 2021-11-05 15:01:46
 */
public interface UserPositionEventListener {

  /**
   * 创建时触发
   *
   * @param vo
   */
  default void onCreate(UserPositionVo vo) {
  }

  /**
   * 编辑时触发
   *
   * @param oldVo
   * @param newVo
   */
  default void onUpdate(UserPositionVo oldVo, UserPositionVo newVo) {

  }

  /**
   * 启用时触发
   *
   * @param list
   */
  default void onEnable(List<UserPositionVo> list) {

  }

  /**
   * 禁用时触发
   *
   * @param list
   */
  default void onDisable(List<UserPositionVo> list) {
  }

  /**
   * 删除时触发
   *
   * @param list
   */
  default void onDelete(List<UserPositionVo> list) {

  }
}

