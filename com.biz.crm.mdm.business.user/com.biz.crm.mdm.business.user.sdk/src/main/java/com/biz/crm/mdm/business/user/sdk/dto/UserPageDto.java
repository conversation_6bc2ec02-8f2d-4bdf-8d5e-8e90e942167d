package com.biz.crm.mdm.business.user.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户分页查询dto
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel
public class UserPageDto extends TenantFlagOpDto {

  /**
   * 用户编码
   */
  @ApiModelProperty("用户编码")
  private String userCode;
  /**
   * 账户
   */
  @ApiModelProperty("账户")
  private String userName;

  /**
   * 账户名称
   */
  @ApiModelProperty("账户名称")
  private String fullName;

}
