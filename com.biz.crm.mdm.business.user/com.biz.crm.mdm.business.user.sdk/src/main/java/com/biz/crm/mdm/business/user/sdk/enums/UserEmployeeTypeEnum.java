package com.biz.crm.mdm.business.user.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum UserEmployeeTypeEnum {
    INSIDE("inside", "内部员工"),
    OUT_STAFF("out_staff", "编外人员"),
    ;

    private String code;
    private String desc;

    public static UserEmployeeTypeEnum getEnumByCode(String code) {
        if (code != null) {
            for (UserEmployeeTypeEnum item :
                    UserEmployeeTypeEnum.values()) {
                if (item.getCode().equals(code)) {
                    return item;
                }
            }
        }
        return null;
    }

    public static String getDescByCode(String code) {
        if (code != null) {
            UserEmployeeTypeEnum anEnum = getEnumByCode(code);
            if (anEnum != null) {
                return anEnum.getDesc();
            }
        }
        return null;
    }

    public static boolean exist(String code) {
        if (code != null) {
            for (UserEmployeeTypeEnum item :
                    UserEmployeeTypeEnum.values()) {
                if (item.getCode().equals(code)) {
                    return true;
                }
            }
        }
        return false;
    }

}
