package com.biz.crm.mdm.business.user.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.user.sdk.dto.UserRelWeChatPageDto;
import com.biz.crm.mdm.business.user.sdk.dto.UserRelWeChatDto;
import com.biz.crm.mdm.business.user.sdk.dto.UserWeChatBindDto;
import com.biz.crm.mdm.business.user.sdk.service.UserRelWeChatVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserRelWeChatVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 用户与微信关联表(UserRelWeChat)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-05 15:05:07
 */
@Slf4j
@Api(tags = "企业用户：UserRelWeChatVo：用户与微信关联表")
@RestController
@RequestMapping(value = {"/v1/userRelWeChat/userRelWeChat"})
public class UserRelWeChatVoController {

    @Autowired(required = false)
    private UserRelWeChatVoService userRelWeChatVoService;

    @ApiOperation(value = "查询分页列表")
    @GetMapping(value = {"/findByConditions"})
    public Result<Page<UserRelWeChatVo>> findByConditions(@PageableDefault(50) Pageable pageable, UserRelWeChatPageDto dto) {
        try {
            Page<UserRelWeChatVo> result = this.userRelWeChatVoService.findByConditions(pageable, dto);
            return Result.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "详情")
    @GetMapping(value = {"/findDetailById"})
    public Result<UserRelWeChatVo> findDetailById(@RequestParam("id") String id) {
        try {
            UserRelWeChatVo vo = this.userRelWeChatVoService.findDetailById(id);
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "创建")
    @PostMapping(value = "")
    public Result<?> create(@RequestBody UserRelWeChatDto dto) {
        try {
            this.userRelWeChatVoService.create(dto);
            return Result.ok("创建成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "更新")
    @PatchMapping(value = "")
    public Result<?> update(@RequestBody UserRelWeChatDto dto) {
        try {
            this.userRelWeChatVoService.update(dto);
            return Result.ok("更新成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "查询用户关联的微信列表")
    @GetMapping(value = {"/findByUserRelWeChatDto"})
    public Result<List<UserRelWeChatVo>> findByUserRelWeChatDto(UserRelWeChatDto dto) {
        try {
            List<UserRelWeChatVo> result = this.userRelWeChatVoService.findByUserRelWeChatDto(dto);
            return Result.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "根据用户账号移除")
    @DeleteMapping("/deleteByUserNames")
    public Result<?> deleteByUserNames(@RequestParam("userNames") List<String> userNames) {
        try {
            this.userRelWeChatVoService.deleteByUserNames(userNames);
            return Result.ok("解绑成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过用户与微信绑定dto创建用户与微信关系
     *
     * @param dto 参数dto
     */
    @ApiOperation(value = "通过用户与微信绑定dto创建用户与微信关系")
    @PostMapping("/createByUserWeChatBindDto")
    public Result<?> createByUserWeChatBindDto(@RequestBody UserWeChatBindDto dto) {
        try {
            this.userRelWeChatVoService.createByUserWeChatBindDto(dto);
            return Result.ok("绑定成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
