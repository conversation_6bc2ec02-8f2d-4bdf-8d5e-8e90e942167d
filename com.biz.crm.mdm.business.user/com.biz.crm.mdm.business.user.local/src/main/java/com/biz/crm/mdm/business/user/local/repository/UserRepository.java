package com.biz.crm.mdm.business.user.local.repository;

import java.util.*;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.biz.crm.business.common.base.constant.CommonConstant;
import com.biz.crm.mdm.business.user.sdk.dto.UserStatisticsDto;
import com.biz.crm.mdm.business.user.sdk.dto.UserUnderDto;
import com.biz.crm.mdm.business.user.sdk.vo.UserUnderVo;
import com.google.common.collect.Lists;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.mdm.business.user.local.entity.UserEntity;
import com.biz.crm.mdm.business.user.local.mapper.UserMapper;
import com.biz.crm.mdm.business.user.sdk.dto.UserConditionDto;
import com.biz.crm.mdm.business.user.sdk.dto.UserPageDto;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.bizunited.nebula.common.util.tenant.TenantUtils;

/**
 * 用户表(repository)
 *
 * <AUTHOR>
 * @since 2021-11-04 16:47:02
 */
@Component
public class UserRepository extends ServiceImpl<UserMapper, UserEntity> {

    /**
     * 分页
     *
     * @param pageable
     * @param dto
     * @return
     */
    public Page<UserVo> findByConditions(Pageable pageable, @Param("dto") UserPageDto dto) {
        pageable = Optional.ofNullable(pageable).orElse(PageRequest.of(1, 50));
        Page<UserPageDto> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        dto = Optional.ofNullable(dto).orElse(new UserPageDto());
        dto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
        dto.setTenantCode(TenantUtils.getTenantCode());//设置租户编号信息
        return baseMapper.findByConditions(page, dto);
    }

    /**
     * 根据用户编码获取详情
     *
     * @param tenantCode 租户编码
     * @param userCode   用户编码
     * @return UserEntity
     */
    public UserEntity findByUserCode(@Param("tenantCode") String tenantCode, @Param("userCode") String userCode) {
        LambdaQueryWrapper<UserEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserEntity::getTenantCode, tenantCode);
        wrapper.eq(UserEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode());
        wrapper.eq(UserEntity::getUserCode, userCode);
        return this.baseMapper.selectOne(wrapper);
    }

    /**
     * 根据用户账号获取详情
     *
     * @param tenantCode 租户编码
     * @param userName   用户账号
     * @return UserEntity
     */
    public UserEntity findByUserName(@Param("tenantCode") String tenantCode, @Param("userName") String userName) {
        LambdaQueryWrapper<UserEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserEntity::getTenantCode, tenantCode);
        wrapper.eq(UserEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode());
        wrapper.eq(UserEntity::getUserName, userName);
        return this.baseMapper.selectOne(wrapper);
    }

    /**
     * 根据用户账号获取详情 (包括已删除)
     *
     * @param tenantCode 租户编码
     * @param userName   用户账号
     * @return UserEntity
     */
    public UserEntity findByUserNameCoverDel(@Param("tenantCode") String tenantCode, @Param("userName") String userName) {
        LambdaQueryWrapper<UserEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserEntity::getTenantCode, tenantCode);
        wrapper.eq(UserEntity::getUserName, userName);
        return this.baseMapper.selectOne(wrapper);
    }

    /**
     * 根据用户账号获取详情
     *
     * @param tenantCode 租户编码
     * @param userNames  用户账号集合
     * @return UserEntity
     */
    public List<UserEntity> findByUserNames(@Param("tenantCode") String tenantCode, @Param("userNames") Collection<String> userNames) {
        LambdaQueryWrapper<UserEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserEntity::getTenantCode, tenantCode);
        wrapper.eq(UserEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode());
        wrapper.in(UserEntity::getUserName, userNames);
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 根据用户电话获取详情
     *
     * @param tenantCode 租户编码
     * @param userPhone  电话
     * @return UserEntity
     */
    public List<UserEntity> findByUserPhone(@Param("tenantCode") String tenantCode, @Param("userPhone") String userPhone) {
        LambdaQueryWrapper<UserEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserEntity::getTenantCode, tenantCode);
        wrapper.eq(UserEntity::getUserPhone, userPhone);
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 根据用户邮箱获取详情
     *
     * @param tenantCode 租户编码
     * @param email      邮箱
     * @return UserEntity
     */
    public List<UserEntity> findByEmail(@Param("tenantCode") String tenantCode, @Param("email") String email) {
        LambdaQueryWrapper<UserEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserEntity::getTenantCode, tenantCode);
        wrapper.eq(UserEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode());
        wrapper.eq(UserEntity::getEmail, email);
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 根据用户身份证号码获取详情
     *
     * @param tenantCode         租户编码
     * @param identityCardNumber 身份证号码
     * @return UserEntity
     */
    public List<UserEntity> findByIdentityCardNumber(@Param("tenantCode") String tenantCode, @Param("identityCardNumber") String identityCardNumber) {
        LambdaQueryWrapper<UserEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserEntity::getTenantCode, tenantCode);
        wrapper.eq(UserEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode());
        wrapper.eq(UserEntity::getIdentityCardNumber, identityCardNumber);
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 根据id集合获取详情集合
     *
     * @param ids ID集合
     * @return List<UserEntity>
     */
    public List<UserEntity> findByIds(@Param("ids") List<String> ids) {
        return this.lambdaQuery()
                .eq(UserEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(UserEntity::getTenantCode, TenantUtils.getTenantCode())
                .in(UserEntity::getId, ids)
                .list();
    }

    /**
     * 根据id集合 更新组织启用/禁用状态
     *
     * @param ids    ID集合
     * @param enable 启禁用枚举对象
     */
    public void updateEnableStatusByIds(List<String> ids, EnableStatusEnum enable) {
        this.lambdaUpdate()
                .eq(UserEntity::getTenantCode, TenantUtils.getTenantCode())
                .in(UserEntity::getId, ids)
                .set(UserEntity::getEnableStatus, enable.getCode())
                .update();
    }

    /**
     * 逻辑删除
     *
     * @param ids ID集合
     */
    public void updateDelFlagByIds(List<String> ids) {
        this.lambdaUpdate()
                .eq(UserEntity::getTenantCode, TenantUtils.getTenantCode())
                .in(UserEntity::getId, ids)
                .set(UserEntity::getDelFlag, DelFlagStatusEnum.DELETE.getCode())
                .update();
    }

    /**
     * 强制修改密码
     *
     * @param ids      ID集合
     * @param password 新密码
     */
    public void updatePasswordByIds(List<String> ids, String password) {
        this.lambdaUpdate()
                .eq(UserEntity::getTenantCode, TenantUtils.getTenantCode())
                .in(UserEntity::getId, ids)
                .set(UserEntity::getUserPassword, password)
                .set(UserEntity::getForceChangePassword, Boolean.FALSE)
                .set(UserEntity::getUpdatePasswordDate, new Date())
                .update();
    }

    /**
     * 根据用户账号集合和用户类型查询用户信息
     *
     * @param tenantCode 租户编码
     * @param userNames  用户账号集合
     * @param userType   用户类型
     * @return 用户信息集合
     */
    public List<UserEntity> findByUserNamesAndUserType(String tenantCode, Set<String> userNames, String userType) {
        List<UserEntity> list = this.lambdaQuery().eq(UserEntity::getTenantCode, tenantCode)
                .eq(UserEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .eq(UserEntity::getEnableStatus, EnableStatusEnum.ENABLE.getCode())
                .eq(UserEntity::getUserType, userType)
                .in(UserEntity::getUserName, userNames)
                .list();
        return list;
    }

    /**
     * 根据用户多条件查询用户账号信息
     *
     * @param dto 参数dto
     * @return 用户账号信息
     */
    public Set<String> findUserNamesByUserConditionDto(UserConditionDto dto) {
        dto.setTenantCode(TenantUtils.getTenantCode());
        return this.baseMapper.findUserNamesByUserConditionDto(dto);
    }

    /**
     * 重构修改方法
     *
     * @param entity
     * @param tenantCode
     */
    public void updateByIdAndTenantCode(UserEntity entity, String tenantCode) {
        LambdaUpdateWrapper<UserEntity> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(UserEntity::getTenantCode, tenantCode);
        lambdaUpdateWrapper.in(UserEntity::getId, entity.getId());
        this.baseMapper.update(entity, lambdaUpdateWrapper);
    }

    /**
     * 重构查询方法
     *
     * @param id
     * @param tenantCode
     * @return
     */
    public UserEntity findByIdAndTenantCode(String id, String tenantCode) {
        return this.lambdaQuery()
                .eq(UserEntity::getTenantCode, tenantCode)
                .in(UserEntity::getId, id)
                .one();
    }

    /**
     * 分页查询用户下属列表
     *
     * @param pageable
     * @param dto
     * @return
     */
    public Page<UserUnderVo> findUnderByConditions(Pageable pageable, @Param("dto") UserUnderDto dto) {
        Page<UserUnderDto> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        return this.baseMapper.findUnderByConditions(page, dto);
    }

    public List<UserEntity> findAllByUserNameList(List<String> userNameList) {
        if (CollectionUtil.isEmpty(userNameList)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(UserEntity::getTenantCode, TenantUtils.getTenantCode())
                .in(UserEntity::getUserName, userNameList)
                .list();
    }

    public List<UserEntity> findByUserPhoneList(List<String> userPhoneList) {
        if (CollectionUtil.isEmpty(userPhoneList)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(UserEntity::getTenantCode, TenantUtils.getTenantCode())
                .eq(UserEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .in(UserEntity::getUserPhone, userPhoneList)
                .list();
    }

    public List<UserEntity> findByIdentityCardNumberList(List<String> identityCardNumberList) {
        if (CollectionUtil.isEmpty(identityCardNumberList)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(UserEntity::getTenantCode, TenantUtils.getTenantCode())
                .eq(UserEntity::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
                .in(UserEntity::getIdentityCardNumber, identityCardNumberList)
                .list();
    }

    public void saveBatchXml(List<UserEntity> dataListVoList) {
        if (CollectionUtil.isEmpty(dataListVoList)) {
            return;
        }
        Lists.partition(dataListVoList, CommonConstant.MAX_PAGE_SIZE).forEach(list -> {
            this.baseMapper.insertBatchSomeColumn(list);
        });
    }


    public List<UserVo> findOrgAllChildrenUser(List<String> orgCodes) {
        if (CollectionUtil.isEmpty(orgCodes)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findOrgAllChildrenUser(orgCodes, TenantUtils.getTenantCode());
    }


    public List<UserVo> findOrgAllChildrenUserExcludePositionLevelCode(List<String> orgCodes, String positionLevelCode) {
        if (CollectionUtil.isEmpty(orgCodes)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.findOrgAllChildrenUserExcludePositionLevelCode(orgCodes, positionLevelCode, TenantUtils.getTenantCode());
    }

    public List<UserVo> findUserListByOrgCodesAndCondition(UserStatisticsDto dto){
        return this.baseMapper.findUserListByOrgCodesAndCondition(dto,TenantUtils.getTenantCode());
    }
}
