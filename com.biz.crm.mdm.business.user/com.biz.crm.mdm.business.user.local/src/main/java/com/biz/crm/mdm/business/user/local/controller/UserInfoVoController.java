package com.biz.crm.mdm.business.user.local.controller;

import com.alibaba.fastjson.JSONObject;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.user.sdk.service.UserInfoVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserInfoVo;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Set;

/**
 * 用户信息控制层
 *
 * <AUTHOR>
 * @since 2021-11-05 15:02:58
 */
@Slf4j
@Api(tags = "企业用户：UserInfoVo：用户信息")
@RestController
@RequestMapping(value = {"/v1/userInfo/userInfo"})
public class UserInfoVoController {

  @Autowired(required = false)
  private UserInfoVoService userInfoVoService;

  /**
   * 根据职位集合查询用户信息
   *
   * @param positionCodes 职位集合
   * @return
   */
  @ApiOperation(value = "根据职位集合查询用户信息")
  @GetMapping(value = {"/findByPositionCodes"})
  public Result<List<UserInfoVo>> findByPositionCodes(@RequestParam("positionCodes") Set<String> positionCodes) {
    try {
      List<UserInfoVo> result = this.userInfoVoService.findByPositionCodes(positionCodes);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据大量职位集合查询
   *
   * @param positionCodes 职位集合
   * @return
   */
  @ApiOperation(value = "根据大量职位集合查询")
  @PostMapping(value = {"/findByLotPositionCodes"})
  public Result<List<UserInfoVo>> findByLotPositionCodes(@RequestBody Set<String> positionCodes) {
    try {
      return Result.ok(this.userInfoVoService.findByLotPositionCodes(positionCodes));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据用户账号集合查询
   *
   * @param userNames 用户账号集合
   * @return
   */
  @ApiOperation(value = "根据用户账号集合查询")
  @GetMapping(value = {"/findByUserNames"})
  public Result<List<UserInfoVo>> findByUserNames(@RequestParam("userNames") Set<String> userNames) {
    try {
      List<UserInfoVo> result = this.userInfoVoService.findByUserNames(userNames);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

}
