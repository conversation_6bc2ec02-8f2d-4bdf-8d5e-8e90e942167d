package com.biz.crm.mdm.business.user.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户与微信关联表(UserRelWeChat)实体类
 *
 * <AUTHOR>
 * @since 2021-11-05 15:34:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "mdm_user_rel_we_chat")
@TableName(value = "mdm_user_rel_we_chat")
@ApiModel(value = "UserRelWeChat", description = "用户与微信关联表")
@org.hibernate.annotations.Table(appliesTo = "mdm_user_rel_we_chat", comment = "用户与微信关联表")
public class UserRelWeChatEntity extends TenantEntity {
    private static final long serialVersionUID = 1479396012316994572L;

    @ApiModelProperty("微信来源模块")
    @TableField(value = "origin_module")
    @Column(name = "origin_module", length = 32, columnDefinition = "varchar(32) COMMENT '微信来源模块'")
    private String originModule;

    @ApiModelProperty("用户登录名")
    @TableField(value = "user_name")
    @Column(name = "user_name", length = 32, columnDefinition = "varchar(32) COMMENT '用户登录名'")
    private String userName;

    @ApiModelProperty("微信openId")
    @TableField(value = "open_id")
    @Column(name = "open_id", length = 128, columnDefinition = "varchar(128) COMMENT '微信openId'")
    private String openId;

    @ApiModelProperty("小程序公众号unionId")
    @Column(name = "union_id", columnDefinition = "varchar(128) comment '小程序公众号unionId'")
    private String unionId;

    @ApiModelProperty("公众号openId")
    @Column(name = "public_account_open_id", columnDefinition = "varchar(128) comment '公众号openId'")
    private String publicAccountOpenId;

    @ApiModelProperty("当前是否处于登录状态 1是 0否")
    @TableField(value = "login_status")
    @Column(name = "login_status", length = 1, columnDefinition = "tinyint(1) COMMENT '当前是否处于登录状态 1是 0否'")
    private Boolean loginStatus;

    @ApiModelProperty("微信头像")
    @TableField(value = "head_img_url")
    @Column(name = "head_img_url", length = 500, columnDefinition = "varchar(500) COMMENT '微信头像'")
    private String headImgUrl;

    @ApiModelProperty("微信昵称")
    @TableField(value = "nick_name")
    @Column(name = "nick_name", length = 32, columnDefinition = "varchar(32) COMMENT '微信昵称'")
    private String nickName;

}
