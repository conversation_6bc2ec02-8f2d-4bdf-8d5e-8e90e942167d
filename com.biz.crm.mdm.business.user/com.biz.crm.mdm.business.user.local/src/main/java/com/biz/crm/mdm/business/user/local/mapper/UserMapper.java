package com.biz.crm.mdm.business.user.local.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.base.mapper.BusinessBaseMapper;
import com.biz.crm.mdm.business.user.local.entity.UserEntity;
import com.biz.crm.mdm.business.user.sdk.dto.UserConditionDto;
import com.biz.crm.mdm.business.user.sdk.dto.UserPageDto;
import com.biz.crm.mdm.business.user.sdk.dto.UserStatisticsDto;
import com.biz.crm.mdm.business.user.sdk.dto.UserUnderDto;
import com.biz.crm.mdm.business.user.sdk.vo.UserUnderVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 用户表(User)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-04 16:46:59
 */
public interface UserMapper extends BusinessBaseMapper<UserEntity> {

    /**
     * 分页列表
     *
     * @param page 分页信息
     * @param dto  分页参数dto
     * @return 分页列表
     */
    Page<UserVo> findByConditions(Page<UserPageDto> page, @Param("dto") UserPageDto dto);

    /**
     * 根据用户多条件查询用户账号信息
     *
     * @param dto 参数dto
     * @return 用户账号信息
     */
    Set<String> findUserNamesByUserConditionDto(@Param("dto") UserConditionDto dto);

    /**
     * 查询用户下属列表
     *
     * @param page 分页信息
     * @param dto  分页参数dto
     * @return 分页列表
     */
    Page<UserUnderVo> findUnderByConditions(Page<UserUnderDto> page, @Param("dto") UserUnderDto dto);

    List<UserVo> findOrgAllChildrenUser(@Param("orgCodes") List<String> orgCodes, @Param("tenantCode") String tenantCode);

    List<UserVo> findOrgAllChildrenUserExcludePositionLevelCode(@Param("orgCodes") List<String> orgCodes, @Param("positionLevelCode") String positionLevelCode,
                                                                @Param("tenantCode") String tenantCode);


    List<UserVo> findUserListByOrgCodesAndCondition(@Param("dto") UserStatisticsDto dto,@Param("tenantCode")String tenantCode);

}

