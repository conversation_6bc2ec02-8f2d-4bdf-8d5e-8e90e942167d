package com.biz.crm.mdm.business.user.local.service;

import com.biz.crm.mdm.business.user.sdk.dto.UserMediaDto;
import com.biz.crm.mdm.business.user.sdk.vo.UserMediaVo;

import java.util.List;

/**
 * 用户媒体文件表服务接口
 *
 * @author: huxmld
 * @version: v1.0.0
 * @date: 2024/5/30 17:40
 */
public interface UserMediaService {

    /**
     * 用户媒体文件绑定用户编码
     *
     * @param dtoList  用户媒体文件列表
     * @param userName 用户账号
     */
    void rebindUserName(List<UserMediaDto> dtoList, String userName);

    /**
     * 根据用户账号集合获取媒体文件信息
     *
     * @param userNameList
     * @return java.util.List<com.biz.crm.mdm.business.user.sdk.vo.UserMediaVo>
     * @author: huxmld
     * @version: v1.0.0
     * @date: 2024/5/30 17:52
     */
    List<UserMediaVo> findMediaBysUerNameList(List<String> userNameList);
}
