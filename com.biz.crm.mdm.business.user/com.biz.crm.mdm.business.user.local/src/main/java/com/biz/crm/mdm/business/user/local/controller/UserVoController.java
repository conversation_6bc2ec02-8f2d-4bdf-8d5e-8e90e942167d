package com.biz.crm.mdm.business.user.local.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.user.local.service.UserOaDataService;
import com.biz.crm.mdm.business.user.sdk.dto.*;
import com.biz.crm.mdm.business.user.sdk.service.UserFeignVoService;
import com.biz.crm.mdm.business.user.sdk.service.UserVoService;
import com.biz.crm.mdm.business.user.sdk.vo.UserUnderVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserWxPublicMsgVo;
import com.biz.crm.mdm.business.user.sdk.vo.UserWxTemplateMsgDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户表(User)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-04 16:47:03
 */
@Slf4j
@Api(tags = "企业用户：UserVo: 企业用户管理")
@RestController
@RequestMapping(value = {"/v1/user/user"})
public class UserVoController {

    @Autowired(required = false)
    private UserVoService userVoService;

    @Autowired(required = false)
    private UserFeignVoService userFeignVoService;

    @Autowired(required = false)
    private UserOaDataService userOaDataService;

    /**
     * 查询分页列表
     *
     * @param pageable
     * @param dto
     */
    @ApiOperation(value = "查询分页列表")
    @GetMapping(value = {"/findByConditions"})
    public Result<Page<UserVo>> findByConditions(@PageableDefault(50) Pageable pageable,
                                                 UserPageDto dto) {
        try {
            Page<UserVo> result = this.userVoService.findByConditions(pageable, dto);
            return Result.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据ID查询详情
     *
     * @param id
     */
    @ApiOperation(value = "根据ID查询详情")
    @GetMapping(value = {"/findDetailById"})
    public Result<UserVo> findDetailById(@RequestParam("id") String id) {
        try {
            UserVo vo = this.userVoService.findDetailById(id);
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据用户账号查询详情
     *
     * @param userName
     */
    @ApiOperation(value = "根据用户账号查询详情")
    @GetMapping(value = {"/findByUserName"})
    public Result<UserVo> findByUserName(@RequestParam("userName") String userName) {
        try {
            log.info("根据用户账号查询详情:{}", userName);
            UserVo vo = this.userVoService.findByUserName(userName);
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    /**
     * 更换用户头像
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "更换用户头像")
    @PostMapping(value = "/updateProfilePicture")
    public Result<UserVo> updateProfilePicture(@RequestBody UserDto dto) {
        try {
            UserVo vo = this.userVoService.updateProfilePicture(dto);
            return Result.ok(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 创建
     *
     * @param dto
     */
    @ApiOperation(value = "创建")
    @PostMapping(value = "")
    public Result<?> create(@RequestBody UserDto dto) {
        try {
            this.userVoService.create(dto);
            return Result.ok("创建成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 更新
     *
     * @param dto
     */
    @ApiOperation(value = "更新")
    @PatchMapping(value = "")
    public Result<?> update(@RequestBody UserDto dto) {
        try {
            this.userVoService.update(dto);
            return Result.ok("更新成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 启用
     *
     * @param ids
     */
    @ApiOperation(value = "启用")
    @PatchMapping("/enable")
    public Result<?> enable(@RequestBody List<String> ids) {
        try {
            this.userVoService.enableBatch(ids);
            return Result.ok("启用成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 禁用
     *
     * @param ids
     */
    @ApiOperation(value = "禁用")
    @PatchMapping("/disable")
    public Result<?> disable(@RequestBody List<String> ids) {
        try {
            this.userVoService.disableBatch(ids);
            return Result.ok("禁用成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 逻辑删除
     *
     * @param ids
     */
    @ApiOperation(value = "逻辑删除")
    @DeleteMapping("/delete")
    public Result<?> delete(@RequestParam("ids") List<String> ids) {
        try {
            this.userVoService.updateDelFlagByIds(ids);
            return Result.ok("删除成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 用户解锁
     *
     * @param ids
     */
    @ApiOperation(value = "用户解锁")
    @DeleteMapping("/deleteUserLockByIds")
    public Result<?> deleteUserLockByIds(@RequestParam("ids") List<String> ids) {
        try {
            this.userVoService.deleteUserLockByIds(ids);
            return Result.ok("解锁成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量修改密码
     *
     * @param dto
     */
    @ApiOperation(value = "批量修改密码")
    @PatchMapping("/updatePasswordByIds")
    public Result<?> updatePasswordByIds(@RequestBody UserForceChangePasswordDto dto) {
        try {
            this.userVoService.updatePasswordByIds(dto);
            return Result.ok("修改成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改密码
     *
     * @param dto
     */
    @ApiOperation(value = "修改密码")
    @PatchMapping("/updatePasswordByUserName")
    public Result<?> updatePasswordByUserName(@RequestBody UserChangePasswordDto dto) {
        try {
            this.userVoService.updatePasswordByUserName(dto);
            return Result.ok("修改成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改密码
     *
     * @param dto
     */
    @ApiOperation(value = "修改密码")
    @PostMapping("/updatePasswordByUserName/updateTurnPatch")
    public Result<?> updateTurnPatch(@RequestBody UserChangePasswordDto dto) {
        return this.updatePasswordByUserName(dto);
    }

    /**
     * 根据组织编码集合和职位级别集合和用户类型查询编码集合
     * 注意orgCodes和positionLevelCodes只允许有一个为空,否则返回空
     *
     * @param orgCodes           组织编码集合
     * @param positionLevelCodes 职位级别编码集合
     * @param userType           用户类型
     * @return 登录用户编码集合
     */
    @ApiOperation(value = "根据组织编码集合和职位级别集合和用户类型查询编码集合")
    @GetMapping(value = {"/findUserNamesByCodesAndUserType"})
    public Result<Set<String>> findUserNamesByCodesAndUserType(@RequestParam(value = "orgCodes", required = false) List<String> orgCodes,
                                                               @RequestParam(value = "positionLevelCodes", required = false) List<String> positionLevelCodes,
                                                               @RequestParam(value = "userType") String userType) {
        try {
            Set<String> result = this.userVoService.findUserNamesByCodesAndUserType(orgCodes, positionLevelCodes, userType);
            return Result.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 按用户账号查询集合(包含职位和组织)
     *
     * @param userNames 用户帐号集合
     * @return 用户信息集合
     */
    @ApiOperation(value = "按用户账号查询集合(包含职位和组织)")
    @GetMapping(value = {"/findByUserNames"})
    public Result<List<UserVo>> findByUserNames(@RequestParam(value = "userNames") List<String> userNames) {
        try {
            return Result.ok(this.userFeignVoService.findByUserNames(userNames));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 按大量用户账号查询集合(包含职位和组织)
     *
     * @param userNames 用户帐号集合
     * @return 用户信息集合
     */
    @ApiOperation(value = "按大量用户账号查询集合(包含职位和组织)")
    @PostMapping(value = {"/findByLotUserNames"})
    public Result<List<UserVo>> findByLotUserNames(@RequestBody List<String> userNames) {
        try {
            return Result.ok(this.userFeignVoService.findByLotUserNames(userNames));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 按照用户手机号查询用户信息(包含职位和组织)
     *
     * @param phone 用户手机号
     * @return 用户信息
     */
    @ApiOperation(value = "按用户账号查询集合(包含职位和组织)")
    @GetMapping(value = {"/findDetailsByPhone"})
    public Result<UserVo> findDetailsByPhone(@RequestParam(value = "phone") String phone) {
        try {
            return Result.ok(this.userVoService.findDetailsByPhone(phone));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 按照条件查询用户账号集合
     *
     * @param dto 查询集合
     * @return 用户账号信息集合
     */
    @ApiOperation(value = "按照条件查询用户账号集合")
    @PostMapping(value = {"/findUserNamesByUserDto"})
    public Result<Set<String>> findUserNamesByUserDto(@RequestBody UserFeignDto dto) {
        try {
            return Result.ok(this.userFeignVoService.findUserNamesByUserDto(dto));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 检查用户账号与组织编码集合或组织类型（层级）集合是否关联
     *
     * @param userName 用户账号
     * @param orgCodes 组织编码集合
     * @param orgTypes 组织类型（层级）集合
     * @return UserVo
     */
    @ApiOperation(value = "检查用户账号与组织编码集合或组织类型（层级）集合是否关联")
    @GetMapping(value = {"/findRelationByUserNameAndOrgCodesOrOrgTypes"})
    public Result<UserVo> findRelationByUserNameAndOrgCodesOrOrgTypes(@RequestParam(value = "userName", required = true) String userName, @RequestParam(value = "orgCodes", required = false) List<String> orgCodes, @RequestParam(value = "orgTypes", required = false) List<String> orgTypes) {
        try {
            UserVo userVo = this.userVoService.findRelationByUserNameAndOrgCodesOrOrgTypes(userName, orgCodes, orgTypes);
            return Result.ok(userVo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据用户多条件查询用户账号信息
     *
     * @param dto 参数dto
     * @return 用户账号信息
     */
    @ApiOperation(value = "根据用户多条件查询用户账号信息")
    @GetMapping(value = {"/findUserNamesByUserConditionDto"})
    public Result<Set<String>> findUserNamesByUserConditionDto(UserConditionDto dto) {
        try {
            return Result.ok(this.userVoService.findUserNamesByUserConditionDto(dto));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据用户账号集合查询职位编码集合
     *
     * @param userNames 用户账号
     * @return 职位编码
     */
    @ApiOperation(value = "根据用户账号集合查询职位编码集合")
    @GetMapping(value = {"/findPositionCodesByUserNames"})
    public Result<Set<String>> findPositionCodesByUserNames(@RequestParam("userNames") List<String> userNames) {
        try {
            return Result.ok(this.userVoService.findPositionCodesByUserNames(userNames));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据用户账号集合查询职位编码集合
     *
     * @param userNames 用户账号
     * @return 职位编码
     */
    @ApiOperation(value = "根据用户账号集合查询主职位信息")
    @PostMapping(value = {"/findPrimaryPositionByUserNames"})
    public Result<Map<String, String>> findPrimaryPositionByUserNames(@RequestBody List<String> userNames) {
        try {
            return Result.ok(this.userVoService.findPrimaryPositionByUserNames(userNames));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 查询当前用户下级分页列表-根据角色区分
     *
     * @param pageable
     * @param dto
     */
    @ApiOperation(value = "查询当前用户下级分页列表-根据角色区分")
    @GetMapping(value = {"/findUnderByRole"})
    public Result<Page<UserUnderVo>> findUnderByRole(@PageableDefault(50) Pageable pageable, UserUnderDto dto) {
        try {
            Page<UserUnderVo> result = this.userVoService.findUnderByRole(pageable, dto);
            return Result.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 查询当前用户下级分页列表
     *
     * @param dto
     */
    @ApiOperation(value = "查询当前用户下级分页列表")
    @PostMapping(value = {"/findUnderByConditions"})
    public Result<List<UserUnderVo>> findUnderByConditions(@RequestBody UserUnderDto dto) {
        try {
            List<UserUnderVo> result = this.userVoService.findUnderByConditions(dto);
            return Result.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "拉取OA用户信息", httpMethod = "GET")
    @GetMapping("/pullUserInfo")
    public Result pullUserInfo() {
        try {
            this.userOaDataService.pullUserInfo();
            return Result.ok("拉取OA用户成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("findOrgAllChildrenUser")
    @ApiOperation(value = "查询组织下所有的启用的用户")
    public Result findOrgAllChildrenUser(@RequestParam("orgCode") String orgCode) {
        return Result.ok(userVoService.findOrgAllChildrenUser(orgCode));
    }


    /**
     * 统计员工数量
     *
     * @param dto 统计条件
     * @return
     */
    @ApiOperation(value = "统计员工数量")
    @PostMapping(value = {"/findTotalNum"})
    public Result<List<UserVo>> findTotalNum(@RequestBody UserStatisticsDto dto) {
        try {
            return Result.ok(this.userFeignVoService.findTotalNum(dto.getEnableStatus(),
                    dto.getIgnorePositionLevelCode(), dto.getEmployeeType()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }


    @GetMapping("findUserListByOrgCode")
    @ApiOperation(value = "通过组织查询下级所有人员")
    public Result<List<UserVo>> findUserListByOrgCode(@RequestParam("orgCode") String orgCode,
                                                      @RequestParam(required = false, value = "positionLevelCode") String positionLevelCode) {
        return Result.ok(userVoService.findUserListByOrgCode(orgCode, positionLevelCode));
    }


    @ApiOperation(value = "通过组织和其他条件查询下级所有用户")
    @PostMapping("findUserListByOrgCodesAndCondition")
    public Result<List<UserVo>> findUserListByOrgCodesAndCondition(@RequestBody UserStatisticsDto dto) {
        return Result.ok(userVoService.findUserListByOrgCodesAndCondition(dto));
    }


    @ApiOperation(value = "发送消息给对应人员到公众号")
    @PostMapping("sendWxPublicMsgToUser")
    public Result sendWxPublicMsgToUser(@RequestBody UserWxPublicMsgVo vo) {
        userVoService.sendWxPublicMsgToUser(vo);
        return Result.ok();
    }

    @ApiOperation(value = "发送待确认订单模版消息")
    @PostMapping("sendOrderWaitConfirmTemplateMsg")
    public Result sendOrderWaitConfirmTemplateMsg(@RequestBody UserWxTemplateMsgDto vo) {
        userVoService.sendOrderWaitConfirmTemplateMsg(vo);
        return Result.ok();
    }
}
