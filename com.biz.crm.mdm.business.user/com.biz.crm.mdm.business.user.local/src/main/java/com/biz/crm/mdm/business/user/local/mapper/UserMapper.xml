<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.crm.mdm.business.user.local.mapper.UserMapper">
    <resultMap id="userEntity" type="com.biz.crm.mdm.business.user.local.entity.UserEntity"/>
    <resultMap id="userVo" type="com.biz.crm.mdm.business.user.sdk.vo.UserVo"/>
    <resultMap id="userUnderlingVo" type="com.biz.crm.mdm.business.user.sdk.vo.UserUnderVo"/>

    <!--分页查询-->
    <select id="findByConditions" resultMap="userVo">
        select * from mdm_user
        where del_flag=#{dto.delFlag} and tenant_code=#{dto.tenantCode}
        <if test="dto.fullName != null and dto.fullName != ''">
            <bind name="likeFullName" value="'%' + dto.fullName + '%'"/>
            and full_name like #{likeFullName}
        </if>
        <if test="dto.enableStatus !=null and dto.enableStatus != '' ">
            AND enable_status = #{dto.enableStatus}
        </if>
    </select>
    <select id="findUserNamesByUserConditionDto" resultType="java.lang.String">
        SELECT
        user_name
        FROM
        mdm_user
        WHERE
        tenant_code=#{dto.tenantCode}
        <if test="dto.fullName != null and dto.fullName != ''">
            <bind name="likeFullName" value="'%' + dto.fullName + '%'"/>
            and full_name like #{likeFullName}
        </if>
        <if test="dto.delFlag != null and dto.delFlag != ''">
           and del_flag = #{dto.delFlag}
        </if>
    </select>

    <!--查询用户下属-->
    <select id="findUnderByConditions" resultMap="userUnderlingVo">
        select a40.id,
        a10.user_code as userCode,
        a10.user_name as userName,
        a10.full_name as fullName,
        a10.user_phone as userPhone,
        a10.user_head_url as userHeadUrl,
        a30.position_code as positionCode,
        a30.position_name as positionName,
        a40.position_code as underlingPositionCode,
        a40.position_name as underlingPositionName,
        a60.user_code as underlingUserCode,
        a60.user_name as underlingUserName,
        a60.full_name as underlingFullName,
        a60.user_phone as underlingUserPhone,
        a70.org_code as underlingOrgCode,
        a70.org_name as underlingOrgName,
        a80.position_level_code as underlingPositionLevelCode,
        a80.position_level_name as underlingPositionLevelName
        from mdm_user a10
        left join mdm_user_position a20 on a10.tenant_code = a20.tenant_code and a10.user_name = a20.user_name
        left join mdm_position a30 on a30.tenant_code = a20.tenant_code and a20.position_code = a30.position_code and
        a30.del_flag = #{dto.delFlag}
        left join mdm_position a40 on a40.tenant_code = a30.tenant_code and a30.position_code = a40.parent_code and
        a40.del_flag = #{dto.delFlag}
        left join mdm_user_position a50 on a50.tenant_code = a40.tenant_code and a40.position_code = a50.position_code
        left join mdm_user a60 on a60.tenant_code = a50.tenant_code and a50.user_name = a60.user_name and a60.del_flag =
        #{dto.delFlag}
        LEFT JOIN mdm_org_position a41 ON a41.tenant_code = a40.tenant_code AND a41.position_code = a40.position_code
        left join mdm_org a70 on a70.tenant_code = a41.tenant_code and a70.org_code = a41.org_code
        left join mdm_position_level a80 on a80.tenant_code = a40.tenant_code and a40.position_level_code =
        a80.position_level_code
        where a40.id is not null and a10.del_flag = #{dto.delFlag} and a60.id is not null
        <if test="dto.id != null and dto.id != ''">
            and a10.id = #{dto.id}
        </if>
        <if test="dto.userName != null and dto.userName != ''">
            and a10.user_name = #{dto.userName}
        </if>
        <if test="dto.positionCode != null and dto.positionCode != ''">
            <bind name="likePositionCode" value="'%' + dto.positionCode + '%'"/>
            and a30.positionCode like #{likePositionCode}
        </if>
        <if test="dto.positionName != null and dto.positionName != ''">
            <bind name="likePositionName" value="'%' + dto.positionName + '%'"/>
            and a30.positionName like #{likePositionName}
        </if>
        <if test="dto.underlingPositionCode != null and dto.underlingPositionCode != ''">
            <bind name="likeUnderlingPositionCode" value="'%' + dto.underlingPositionCode + '%'"/>
            and a40.position_code like #{likeUnderlingPositionCode}
        </if>
        <if test="dto.underlingPositionName != null and dto.underlingPositionName != ''">
            <bind name="likeUnderlingPositionName" value="'%' + dto.underlingPositionName + '%'"/>
            and a40.position_name like #{likeUnderlingPositionName}
        </if>
        <if test="dto.underlingPrimaryFlag != null and dto.underlingPrimaryFlag != ''">
            and a50.primary_flag = #{dto.underlingPrimaryFlag}
        </if>
        <if test="dto.underlingUserName != null and dto.underlingUserName != ''">
            <bind name="likeUnderlingUserName" value="'%' + dto.underlingUserName + '%'"/>
            and a60.user_name like #{likeUnderlingUserName}
        </if>
        <if test="dto.underlingFullName != null and dto.underlingFullName != ''">
            <bind name="likeUnderlingFullName" value="'%' + dto.underlingFullName + '%'"/>
            and a60.full_name like #{likeUnderlingFullName}
        </if>
        <if test="dto.underlingOrgCode != null and dto.underlingOrgCode != ''">
            <bind name="likeUnderlingOrgCode" value="'%' + dto.underlingOrgCode + '%'"/>
            and a70.org_code like #{likeUnderlingOrgCode}
        </if>
        <if test="dto.underlingOrgName != null and dto.underlingOrgName != ''">
            <bind name="likeUnderlingOrgName" value="'%' + dto.underlingOrgName + '%'"/>
            and a70.org_name like #{likeUnderlingOrgName}
        </if>
        <if test="dto.underlingPositionLevelCode != null and dto.underlingPositionLevelCode != ''">
            <bind name="likeUnderlingPositionLevelCode" value="'%' + dto.underlingPositionLevelCode + '%'"/>
            and a80.position_level_code like #{likeUnderlingPositionLevelCode}
        </if>
        <if test="dto.underlingPositionLevelName != null and dto.underlingPositionLevelName != ''">
            <bind name="likeUnderlingPositionLevelName" value="'%' + dto.underlingPositionLevelName + '%'"/>
            and a80.position_level_name like #{likeUnderlingPositionLevelName}
        </if>
        <if test="dto.underlingUserNameOrFullNameOrUserCode != null and dto.underlingUserNameOrFullNameOrUserCode != ''">
            <bind name="underlingUserNameOrFullNameOrUserCodeLike"
                  value="'%' + dto.underlingUserNameOrFullNameOrUserCode + '%'"/>
            and
            (
            a60.full_name like #{underlingUserNameOrFullNameOrUserCodeLike}
            or
            a60.user_name like #{underlingUserNameOrFullNameOrUserCodeLike}
            or
            a60.user_code like #{underlingUserNameOrFullNameOrUserCodeLike}
            )
        </if>
        order by a30.position_code asc, a40.position_code asc
    </select>

    <select id="findOrgAllChildrenUser" resultType="com.biz.crm.mdm.business.user.sdk.vo.UserVo">
        SELECT
        a.user_name,
        a.full_name,
        c.org_code,
        d.org_name
        FROM
        mdm_user_position b
        LEFT JOIN mdm_user a ON a.user_name = b.user_name
        AND b.primary_flag = '1'
        LEFT JOIN mdm_org_position c ON b.position_code = c.position_code
        LEFT JOIN mdm_org d ON c.org_code = d.org_code
        WHERE
        a.enable_status = '${@<EMAIL>()}'
        AND a.tenant_code = #{tenantCode}
        and a.employee_type = 'inside'
        AND c.org_code in
        <foreach collection="orgCodes" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="findOrgAllChildrenUserExcludePositionLevelCode"
            resultType="com.biz.crm.mdm.business.user.sdk.vo.UserVo">
        SELECT
        a.user_name,
        a.full_name,
        a.employee_type,
        c.org_code,
        d.org_name,
        e.position_name,
        e.position_code,
        a.user_phone
        FROM
        mdm_user_position b
        LEFT JOIN mdm_user a ON a.user_name = b.user_name
        AND b.primary_flag = '1'
        LEFT JOIN mdm_org_position c ON b.position_code = c.position_code
        LEFT JOIN mdm_org d ON c.org_code = d.org_code
        LEFT JOIN mdm_position e ON c.position_code = e.position_code
        WHERE
        a.enable_status = '009'
        AND a.del_flag = '009'
        AND a.tenant_code = #{tenantCode}
        <if test="positionLevelCode != null and positionLevelCode != ''">
            AND e.position_level_code != #{positionLevelCode}
        </if>
        AND c.org_code in
        <foreach collection="orgCodes" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="findUserListByOrgCodesAndCondition" resultType="com.biz.crm.mdm.business.user.sdk.vo.UserVo">
        SELECT
        a.user_name,
        a.full_name,
        c.org_code,
        d.org_name,
        e.position_name,
        e.position_code,
        a.user_phone,
        d.rule_code
        FROM
        mdm_user_position b
        LEFT JOIN mdm_user a ON a.user_name = b.user_name
        AND b.primary_flag = '1'
        LEFT JOIN mdm_org_position c ON b.position_code = c.position_code
        LEFT JOIN mdm_org d ON c.org_code = d.org_code
        LEFT JOIN mdm_position e ON c.position_code = e.position_code
        WHERE
        a.enable_status = '009'
        AND a.del_flag = '009'
        AND a.tenant_code = #{tenantCode}
        <if test="dto.employeeType != null and dto.employeeType !=''">
            AND a.employee_type = #{dto.employeeType}
        </if>
        <if test="dto.excludePositionLevelCode != null and dto.excludePositionLevelCode != ''">
            AND e.position_level_code != #{dto.excludePositionLevelCode}
        </if>
        <if test="dto.orgCodes != null and dto.orgCodes.size()>0">
            AND c.org_code in
            <foreach collection="dto.orgCodes" separator="," item="item" index="index" open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
