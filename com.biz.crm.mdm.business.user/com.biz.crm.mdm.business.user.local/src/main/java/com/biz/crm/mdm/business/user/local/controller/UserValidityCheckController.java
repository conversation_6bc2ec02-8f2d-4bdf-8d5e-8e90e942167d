package com.biz.crm.mdm.business.user.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.user.sdk.service.UserValidityCheckService;
import com.biz.crm.mdm.business.user.sdk.vo.UserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 企业用户有效性检查
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "企业用户：UserVo: 用户有效性检查")
@RestController
@RequestMapping(value = {"/v1/userValidityCheck/userValidityCheck"})
public class UserValidityCheckController {

  @Autowired(required = false)
  private UserValidityCheckService userValidityCheckService;

  /**
   * 根据账号校验【管理端】用户有效性
   *
   * @param account 账号
   * @return UserVo 用户信息
   */
  @ApiOperation(value = "根据账号校验【管理端】用户有效性")
  @GetMapping(value = {"/findByAccount"})
  public Result<UserVo> findByAccount(@RequestParam("account") String account) {
    try {
      UserVo result = this.userValidityCheckService.verificationManageByAccount(account);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据手机号校验【管理端】用户有效性
   *
   * @param phone 手机号
   * @return UserVo 用户信息
   */
  @ApiOperation(value = "根据手机号校验【管理端】用户有效性")
  @GetMapping(value = {"/findByPhone"})
  public Result<UserVo> findByPhone(@RequestParam("phone") String phone) {
    try {
      UserVo result = this.userValidityCheckService.verificationManageByPhone(phone);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }

  /**
   * 根据openid校验【管理端】用户有效性
   *
   * @param openid openid
   * @return UserVo 用户信息
   */
  @ApiOperation(value = "根据openid校验【管理端】用户有效性")
  @GetMapping(value = {"/findByOpenid"})
  public Result<UserVo> findByOpenid(@RequestParam("openid") String openid) {
    try {
      UserVo result = this.userValidityCheckService.verificationManageByOpenid(openid);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
