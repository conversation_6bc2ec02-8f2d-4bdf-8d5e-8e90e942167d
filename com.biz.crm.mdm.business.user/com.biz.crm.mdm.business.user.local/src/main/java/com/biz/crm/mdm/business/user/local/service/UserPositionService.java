package com.biz.crm.mdm.business.user.local.service;

import com.biz.crm.mdm.business.user.local.entity.UserPositionEntity;

import com.biz.crm.mdm.business.user.sdk.vo.UserPositionVo;
import java.util.Collection;
import java.util.List;

/**
 * 职位-用户关联表(UserPosition)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-05 15:11:43
 */
public interface UserPositionService {

  /**
   * 根据职位编码查询
   *
   * @param positionCodes
   * @return
   */
  List<UserPositionEntity> findByPositionCode(Collection<String> positionCodes);

  /**
   * 根据用户名称查询
   * @param userName
   * @return
   */
  List<UserPositionVo> findByUserName(String userName);
}
