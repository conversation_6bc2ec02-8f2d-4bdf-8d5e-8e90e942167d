package com.biz.crm.mdm.business.region.local.service;

import com.biz.crm.mdm.business.region.local.entity.RegionLabel;
import com.biz.crm.mdm.business.region.sdk.dto.RegionLabelDto;

import java.util.List;

/**
 * <AUTHOR>
 * @describe:
 * @createTime 2022年08月02日 14:45:00
 */
public interface RegionLabelService {

  /**
   * 绑定标签
   * @param dto
   */
  void create(RegionLabelDto dto);

  /**
   * 删除标签
   * @param regionCodes
   */
  void delete(List<String> regionCodes);

  /**
   * 通过编码查询
   * @param regionCodes
   * @return
   */
  List<RegionLabel> findByRegionCodes(List<String> regionCodes);

  /**
   * 通过标签集合查询，只返回与标签集合一致的结果
   * @param labelList
   * @return
   */
  List<String> findRuleCodeByLabelList(List<String> labelList);
}
