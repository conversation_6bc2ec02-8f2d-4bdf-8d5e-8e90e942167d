package com.biz.crm.mdm.business.region.local.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantEntity;
import com.biz.crm.business.common.local.entity.TenantOpEntity;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.biz.crm.business.common.local.entity.UuidOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @describe: 行政区域标签子表
 * @createTime 2022年08月02日 14:13:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("mdm_region_label")
@ApiModel(value = "RegionLabel", description = "行政区域")
@Table(
    name = "mdm_region_label",
    indexes = {
        @Index(name = "mdm_region_index1", columnList = "region_code, tenant_code")
    })
@org.hibernate.annotations.Table(appliesTo = "mdm_region_label", comment = "行政区域")
public class RegionLabel extends TenantFlagOpEntity {

  /**
   * 关联行政区域编码
   */
  @TableField(value = "region_code")
  @Column(name = "region_code", length = 16, columnDefinition = "VARCHAR(16) COMMENT '关联行政区域编码'")
  @ApiModelProperty("关联行政区域编码")
  private String regionCode;

  /**
   * 降维码
   */
  @TableField(value = "rule_code")
  @Column(name = "rule_code", length = 50, columnDefinition = "VARCHAR(50) COMMENT '降维码'")
  @ApiModelProperty("降维码")
  private String ruleCode;

  /**
   * 标签
   */
  @TableField(value = "label")
  @Column(name = "label", length = 128, columnDefinition = "VARCHAR(128) COMMENT '标签'")
  @ApiModelProperty("标签")
  private String label;
}
