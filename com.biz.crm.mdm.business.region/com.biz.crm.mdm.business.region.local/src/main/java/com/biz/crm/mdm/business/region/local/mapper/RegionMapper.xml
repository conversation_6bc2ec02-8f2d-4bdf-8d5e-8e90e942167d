<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.biz.crm.mdm.business.region.local.mapper.RegionMapper">

  <sql id="queryHasChildFlag">
    (select count(1) from (select 1 from mdm_region b where b.parent_code = a.region_code  limit 1) t) as hasChildFlag
  </sql>

  <select id="findByParentCodeAndRegionNameLike"
    resultType="com.biz.crm.mdm.business.region.local.entity.Region">
    select a.*,
    <include refid="queryHasChildFlag"></include>
    from mdm_region a
    where a.tenant_code = #{tenantCode} and a.del_flag = '${@<EMAIL>()}' and a.parent_code =#{parentCode}
    <if test="regionName != null and regionName != ''">
      <bind name="likeRegionName" value="'%' + regionName + '%'"/>
      and a.region_name like #{likeRegionName}
    </if>
    order by a.rule_code asc
  </select>

  <select id="findByParentCodeAndRegionNameLikeForOrder"
          resultType="com.biz.crm.mdm.business.region.local.entity.Region">
    select a.*,
    <include refid="queryHasChildFlag"></include>
    from mdm_region a
    where a.tenant_code = #{tenantCode} and a.del_flag = '${@<EMAIL>()}'
      <choose>
        <when test="parentCode != null and parentCode != ''">
          and a.parent_code =#{parentCode}
        </when>
        <when test="sapProvinceCode != null and sapProvinceCode != ''">
          and exists(select 1 from mdm_region t where a.parent_code = t.region_code and t.sap_province_code = #{sapProvinceCode})
        </when>
        <otherwise>
          and (a.parent_code is null or a.parent_code = '')
        </otherwise>
      </choose>
    <if test="regionName != null and regionName != ''">
      <bind name="likeRegionName" value="'%' + regionName + '%'"/>
      and a.region_name like #{likeRegionName}
    </if>
    order by a.rule_code asc
  </select>

  <select id="findChildByRuleCodes"
    resultType="com.biz.crm.mdm.business.region.local.entity.Region">
    select a.*,
    <include refid="queryHasChildFlag"></include>
    from mdm_region a
    where a.tenant_code = #{tenantCode} and a.del_flag = '${@<EMAIL>()}'
    and a.rule_code in
    <foreach  item="item" collection="ruleCodeList" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
    <if test="regionCode!=null and regionCode!=''">
      and region_code = #{regionCode}
    </if>
    <if test="regionLevel!=null and regionLevel!=''">
      and region_level = #{regionLevel}
    </if>
    <if test="regionName!=null and regionName!=''">
      <bind name="likeRegionName" value="'%' + regionName + '%'"/>
      and region_name like #{likeRegionName}
    </if>
    order by a.rule_code asc
  </select>


  <select id="findChildByParentCode"
    resultType="com.biz.crm.mdm.business.region.local.entity.Region">
    select a.*,
    <include refid="queryHasChildFlag"></include>
    from mdm_region a
    where a.tenant_code = #{tenantCode} and a.del_flag = '${@<EMAIL>()}'
    and a.parent_code = #{parentCode}
    order by a.region_code asc
  </select>



  <select id="findByConditions"
    resultType="com.biz.crm.mdm.business.region.local.entity.Region">
    select * from mdm_region a
    where 2=2
    and a.tenant_code = #{tenantCode} and  a.del_flag = '${@<EMAIL>()}'
    <if test="dto.regionCode != null and dto.regionCode != ''">
      <bind name="likeRegionCode" value="'%' + dto.regionCode + '%'"/>
      and a.region_code like #{likeRegionCode}
    </if>
    <if test="dto.regionLevel != null and dto.regionLevel != ''">
      and a.region_level =  #{dto.regionLevel}
    </if>
    <if test="dto.regionName != null and dto.regionName != ''">
      <bind name="likeRegionName" value="'%' + dto.regionName + '%'"/>
      and a.region_name like #{likeRegionName}
    </if>
    order by a.rule_code asc
  </select>

  <select id="findRuleCodeListByParams" resultType="java.lang.String">
    select distinct a.rule_code1
    from (
    <foreach collection="list" item="item" separator=" union " open="(" close=")">
      select substr(b.rule_code, 1, 3 * (b.region_level+1)) as rule_code1
      from mdm_region b
      where 1=1 and tenant_code = #{tenantCode} and del_flag = '${@<EMAIL>()}'
      and rule_code like concat(#{item},'%')
      <if test="item!=null and item!=''">
        and rule_code!=#{item}
      </if>
      <if test="regionName!=null and regionName!=''">
        <bind name="likeRegionName" value="'%' + regionName + '%'"/>
        and region_name like #{likeRegionName}
      </if>
      <if test="regionCode!=null and regionCode!=''">
        and region_code = #{regionCode}
      </if>
      <if test="regionLevel!=null and regionLevel!=''">
        and region_level = #{regionLevel}
      </if>
    </foreach>
    ) a
    order by a.rule_code1 asc
  </select>
  <select id="findSelfAndLowerByRuleCode" resultType="com.biz.crm.mdm.business.region.local.entity.Region">
    select * from mdm_region a
    <where>
      a.tenant_code=#{tenantCode}
      and del_flag = '${@<EMAIL>()}'
      <if test="code!=null and code!=''">
        <bind name="likeCode" value=" code + '%'"/>
        and rule_code like #{likeCode}
      </if>
    </where>
  </select>



  <select id="findLazyTreeList" resultType="com.biz.crm.mdm.business.region.sdk.vo.LazyTreeVo">
    select a1.id, a1.region_code as code, a1.region_name as name, a1.parent_code,
    case when a2.parent_code is null then 0
    else 1 end as hasChildFlag
    from (select id, region_code, region_name, parent_code from mdm_region
    where 1=1
    AND del_flag = #{delFlag.code}
    AND tenant_code = #{tenantCode}
    <if test="parentCode != null and parentCode != '' ">
      and parent_code = #{parentCode}
    </if>
    <if test="enableStatus != null and enableStatus != '' ">
      and enable_status = #{enableStatus}
    </if>
    <if test="topOnly != null and topOnly == true">
      and (parent_code is null or parent_code = '')
    </if>
    <if test="excludeRuleCode != null and excludeRuleCode != '' ">
      <bind name="likeRightExcludeRuleCode" value="excludeRuleCode + '%'"/>
      and rule_code not like #{likeRightExcludeRuleCode}
    </if>
    <if test="codeList != null and codeList.size > 0">
      and region_code in
      <foreach collection="codeList" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </if>
    <if test="ruleCodeList != null and ruleCodeList.size > 0">
      and rule_code in
      <foreach collection="ruleCodeList" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </if>
    ) a1
    left join (select distinct parent_code from mdm_region where 1=1
    AND del_flag = #{delFlag.code}
    AND tenant_code = #{tenantCode}
    <if test="enableStatus != null and enableStatus != '' ">
      and enable_status = #{enableStatus}
    </if>
    <if test="excludeRuleCode != null and excludeRuleCode != '' ">
      <bind name="likeRightExcludeRuleCode" value="excludeRuleCode + '%'"/>
      and rule_code not like #{likeRightExcludeRuleCode}
    </if>
    ) a2 on a1.region_code = a2.parent_code
    order by a1.region_code asc
  </select>
</mapper>