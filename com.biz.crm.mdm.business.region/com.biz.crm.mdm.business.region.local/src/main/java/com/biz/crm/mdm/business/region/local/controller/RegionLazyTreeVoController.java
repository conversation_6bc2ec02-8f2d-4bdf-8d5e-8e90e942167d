package com.biz.crm.mdm.business.region.local.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.mdm.business.region.sdk.dto.TreeDto;
import com.biz.crm.mdm.business.region.sdk.service.RegionLazyTreeVoService;
import com.biz.crm.mdm.business.region.sdk.vo.LazyTreeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @title RegionLazyTreeVoController
 * @date 2023/2/24 14:54
 * @description 行政区域: LazyTreeVo : 行政区域懒加载树
 */
@Slf4j
@RestController
@RequestMapping("/v1/region/region")
@Api(tags = "行政区域: LazyTreeVo : 行政区域懒加载树")
public class RegionLazyTreeVoController {

  @Autowired(required = false)
  private RegionLazyTreeVoService regionLazyTreeVoService;

  @ApiOperation(
      value = "行政区域懒加载树",
      notes =
          "销售区域关联行政区域过滤,过滤已绑定销售区域的行政区域,"
              + "若未传parentCode,则以'00'编码分割,降维查询国家下一级,"
              + "若传递parentCode,则查询当前的下一级",
      httpMethod = "GET")
  @GetMapping("/findLazyTree")
  public Result<List<LazyTreeVo>> findLazyTree(
      @ApiParam(name = "treeDto", value = "查询Dto") TreeDto treeDto) {
    try {
      List<LazyTreeVo> result = this.regionLazyTreeVoService.findLazyTree(treeDto);
      return Result.ok(result);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.error(e.getMessage());
    }
  }
}
