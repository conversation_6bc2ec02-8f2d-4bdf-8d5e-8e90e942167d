package com.biz.crm.mdm.business.region.local.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.sdk.dto.TreeDto;
import com.biz.crm.business.common.sdk.enums.DelFlagStatusEnum;
import com.biz.crm.business.common.sdk.enums.EnableStatusEnum;
import com.biz.crm.business.common.sdk.service.TreeRuleCodeStrategy;
import com.biz.crm.business.common.sdk.service.TreeRuleCodeStrategyHolder;
import com.biz.crm.business.common.sdk.utils.TreeRuleCode;
import com.biz.crm.mdm.business.region.local.entity.Region;
import com.biz.crm.mdm.business.region.local.entity.RegionLabel;
import com.biz.crm.mdm.business.region.local.repository.RegionRepository;
import com.biz.crm.mdm.business.region.local.service.RegionLabelService;
import com.biz.crm.mdm.business.region.local.service.RegionService;
import com.biz.crm.mdm.business.region.sdk.constant.RegionConstant;
import com.biz.crm.mdm.business.region.sdk.dto.RegionPaginationDto;
import com.biz.crm.mdm.business.region.sdk.enums.RegionLevelEnum;
import com.biz.crm.mdm.business.region.sdk.event.RegionEventListener;
import com.biz.crm.mdm.business.region.sdk.vo.RegionVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.bizunited.nebula.common.util.tenant.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 行政区域接口实现
 *
 * <AUTHOR>
 * @date 2021/10/8
 */
@Service(value = "regionService")
public class RegionServiceImpl implements RegionService {

  private static final int PROVINCE_SIZE = 4;
  private static final int MAX_RULECODE_IN_ONE_Level = 999;


  @Autowired(required = false)
  private RegionRepository regionRepository;

  @Autowired(required = false)
  private NebulaToolkitService nebulaToolkitService;

  @Autowired(required = false)
  @Lazy
  private List<RegionEventListener> regionEventListenerList;

  @Autowired(required = false)
  private TreeRuleCodeStrategyHolder treeRuleCodeStrategyHolder;

  @Autowired
  private RegionLabelService regionLabelService;

  /**
   * 行政区域分页信息
   *
   * @param pageable
   * @param regionPaginationDto
   * @return
   */
  @Override
  public Page<Region> findByConditions(Pageable pageable, RegionPaginationDto regionPaginationDto) {
    regionPaginationDto =
            Optional.ofNullable(regionPaginationDto).orElse(new RegionPaginationDto());
    //获取多租户编码
    String tenantCode = TenantUtils.getTenantCode();
    tenantCode = StringUtils.isBlank(tenantCode) ? "default" : tenantCode;
    //下拉菜单
    if(StringUtils.isNotBlank(regionPaginationDto.getParentCode())){
      return this.getPageRegionByList(this.findChildByParentCode(regionPaginationDto.getParentCode()));
    }

    regionPaginationDto.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    regionPaginationDto.setTenantCode(TenantUtils.getTenantCode());//增加租户编号条件
    Validate.isTrue(StringUtils.isBlank(regionPaginationDto.getRegionCode()) ? true : regionPaginationDto.getRegionCode().length() < 64, "行政区域编码，在进行搜索时填入值超过了限定长度(64)，请检查!");
    Validate.isTrue(StringUtils.isBlank(regionPaginationDto.getRegionName()) ? true : regionPaginationDto.getRegionName().length() < 128, "行政区域名称，在进行搜索时填入值超过了限定长度(128)，请检查!");
    String regionLevelReqVo = regionPaginationDto.getRegionLevel();
    int levelNum = StringUtils.isNotBlank(regionLevelReqVo) ? Integer.parseInt(regionLevelReqVo)+1 : 1;
    String ruleCode = "";
    if( StringUtils.isBlank(regionLevelReqVo) &&  StringUtils.isBlank(regionPaginationDto.getRegionCode()) &&  StringUtils.isBlank(regionPaginationDto.getRegionName())){
      regionPaginationDto.setRegionLevel("1");
    }

    levelNum = Integer.min(levelNum, 5);

    // 携带 regionCode, regionName, regionLevel 进行搜索
    List<String> ruleCodeList =
            this.regionRepository.findRuleCodeListByParams(
                    levelNum, Lists.newArrayList(ruleCode), regionPaginationDto.getRegionName(), regionPaginationDto.getRegionCode(), regionPaginationDto.getRegionLevel(), tenantCode);
    //如果存在标签条件
    if (CollectionUtils.isNotEmpty(regionPaginationDto.getLabelList())) {
      List<String> labelList = regionPaginationDto.getLabelList();
      List<String> ruleCodeByLabelList = this.regionLabelService.findRuleCodeByLabelList(labelList);
      Integer length = 3 * levelNum;
      List<String> collect = ruleCodeByLabelList.stream().filter(e -> e.length() >= (length)).collect(Collectors.toList());
      List<String> newRuleCodeByLabelList = new ArrayList<>();
      //根据级别裁剪
      if (CollectionUtils.isNotEmpty(collect)) {
        for (String code : collect) {
          code = code.substring(0, 3 * levelNum);
          newRuleCodeByLabelList.add(code);
        }
      }
      ruleCodeList.retainAll(newRuleCodeByLabelList);
    }

    if (CollectionUtils.isEmpty(ruleCodeList)) {
      return this.getPageRegionByList(null);
    }
    ruleCodeList =
            ruleCodeList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
    if (CollectionUtils.isEmpty(ruleCodeList)) {
      return this.getPageRegionByList(null);
    }

    List<Region> list = this.regionRepository.findChildByRuleCodes(ruleCodeList, regionPaginationDto.getRegionName(), regionPaginationDto.getRegionCode(), regionPaginationDto.getRegionLevel(),tenantCode);
    if (CollectionUtils.isNotEmpty(list)) {
      list = this.selectLabel(list);
      //补充标签
      list.stream()
              .peek(a-> a.setHasChild(a.getHasChildFlag()==1))
              .collect(Collectors.toList());
    }
    return this.getPageRegionByList(list);
  }

  /**
   * 查询关联标签
   *
   * @param list
   */
  private List<Region> selectLabel(List<Region> list) {
    List<String> regionCodes = list.stream().map(Region::getRegionCode).collect(Collectors.toList());
    List<RegionLabel> regionLabels = this.regionLabelService.findByRegionCodes(regionCodes);
    if (CollectionUtils.isEmpty(regionLabels)) {
      return list;
    }
    Map<String, List<RegionLabel>> map = regionLabels.stream().collect(Collectors.groupingBy(RegionLabel::getRegionCode));
    for (Region region : list) {
      region.setLableList(map.get(region.getRegionCode()));
    }
    return list;
  }

  /**
   * 根据行政区域id或编码获取详情
   *
   * @param id
   * @param regionCode
   * @return
   */
  @Override
  public Region findDetailByIdOrCode(String id, String regionCode) {
    if (StringUtils.isBlank(id) && StringUtils.isBlank(regionCode)) {
      return null;
    }
    //获取多租户编码
    String tenantCode = TenantUtils.getTenantCode();
    tenantCode = StringUtils.isBlank(tenantCode) ? "default" : tenantCode;
    if (StringUtils.isNotBlank(id)) {
      Region region =  regionRepository.findById(id);
      //补充parentCode  provinceCode cityCode districtCode streetCode
      Validate.notNull(region, "信息不存在,请刷新!");
      List<String> codeList = new ArrayList<>(PROVINCE_SIZE);
      codeList.add(region.getRegionCode());
      if (StringUtils.isNotBlank(region.getParentCode())) {
        Region parentRegion = regionRepository.lambdaQuery()
            .eq(Region::getRegionCode, region.getParentCode())
            .eq(Region::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(Region::getTenantCode,tenantCode)
            .one();
        if (Objects.nonNull(parentRegion)) {
          region.setParentName(parentRegion.getRegionName());
        }
        String tempParentCode = region.getParentCode();
        for (int i = 1; i < region.getRegionLevel(); i++) {
          Region tempRegion = regionRepository.lambdaQuery()
              .eq(Region::getRegionCode, tempParentCode)
              .eq(Region::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
              .eq(Region::getTenantCode, tenantCode)
              .one();
          tempParentCode = tempRegion.getParentCode();
          if (!StringUtils.equals("00", tempRegion.getRegionCode())) {
            codeList.add(tempRegion.getRegionCode());
          }
        }
      }

      //数据库不存下面字段(省市区街道) 只展示给前端使用
      Collections.reverse(codeList);
      for (int i = 0; i < codeList.size(); i++) {
        switch (String.valueOf(i+1)){
          case "1":
            region.setProvinceCode(codeList.get(i));
            break;
          case "2":
            region.setCityCode(codeList.get(i));
            break;
          case "3":
            region.setDistrictCode(codeList.get(i));
            break;
          case "4":
            region.setStreetCode(codeList.get(i));
            break;
        }
      }

      return region;
    }
    return regionRepository.findByRegionCode(regionCode);
  }

  /**
   * 新增行政区域
   *
   * @param region
   * @return
   */
  @Override
  @Transactional
  public Region create(Region region) {
    Validate.isTrue(StringUtils.isNotBlank(region.getRegionCode()), "区域编码不能为空");
    Validate.isTrue(StringUtils.isNotBlank(region.getRegionName()), "区域名称不能为空");
    Validate.isTrue(region.getRegionCode().length() < 64, "行政区域编码，在进行输入时填入值超过了限定长度(64)，请检查!");
    Validate.isTrue(region.getRegionName().length() < 128, "行政区域名称，在进行输入时填入值超过了限定长度(128)，请检查!");
    Region cur = this.regionRepository.findByRegionCode(region.getRegionCode());
    Validate.isTrue(Objects.isNull(cur), "已存在当前行政区域编码的数据");
    region.setTenantCode(TenantUtils.getTenantCode());
    region.setDelFlag(DelFlagStatusEnum.NORMAL.getCode());
    region.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
    region.setParentCode(Optional.ofNullable(region.getParentCode()).orElse("00"));
    // 根据父级设置层级
    int levelNum = 1;
    if (StringUtils.isNotEmpty(region.getParentCode())) {
      Region parent = this.regionRepository.findByRegionCode(region.getParentCode());
      levelNum = parent.getLevelNum() + 1;
    }
    // 设置规则（降维）编码
    String ruleCode = null;
    //国家不允许创建 省份创建默认为1级ruleCode 1*3
    if(RegionLevelEnum.ONE.getDictCode().equals(String.valueOf(region.getRegionLevel()))) {
      ruleCode = this.getRuleCodeByProvince();
    }else if(!RegionLevelEnum.ZERO.getDictCode().equals(String.valueOf(region.getRegionLevel()))){
     ruleCode = this.getRuleCodeByParentCode(region.getParentCode());
    }
    region.setRuleCode(ruleCode);
    region.setLevelNum(levelNum);
    regionRepository.save(region);
    return region;
  }

  /**
   * 编辑行政区域
   *
   * @param region
   * @return
   */
  @Override
  @Transactional
  public Region update(Region region) {
    Validate.isTrue(StringUtils.isNotBlank(region.getId()), "区域id不能为空");
    Validate.isTrue(StringUtils.isNotBlank(region.getRegionCode()), "区域编码不能为空");
    Validate.isTrue(StringUtils.isNotBlank(region.getRegionName()), "区域名称不能为空");
    Validate.isTrue(region.getRegionCode().length() < 64, "行政区域编码，在进行输入时填入值超过了限定长度(64)，请检查!");
    Validate.isTrue(region.getRegionName().length() < 128, "行政区域名称，在进行输入时填入值超过了限定长度(128)，请检查!");
    region.setParentCode(Optional.ofNullable(region.getParentCode()).orElse("00"));
    String currentId = region.getId();
    //重构查询方法
    Region current = this.regionRepository.findByIdAndTenantCode(currentId,TenantUtils.getTenantCode());
    Validate.notNull(current, "未发现指定的原始模型对象信");
    Validate.isTrue(region.getRegionCode().equals(current.getRegionCode()), "行政区域编码不能编辑");
    // 2.根据父级设置层级
    int levelNum = 1;
    if (StringUtils.isNotBlank(region.getParentCode())) {
      Region parent = this.regionRepository.findByRegionCode(region.getParentCode());
      levelNum = parent.getLevelNum() + 1;
    }
    region.setLevelNum(levelNum);
    region.setTenantCode(TenantUtils.getTenantCode());
    this.regionRepository.updateByIdAndTenantCode(region,TenantUtils.getTenantCode());

    if (!region.getParentCode().equals(current.getRegionCode())) {
      String ruleCode = getRuleCodeByParentCode(region.getParentCode());
      updateCurAndChildrenRuleCode(region.getRegionCode(), ruleCode, levelNum);
    }
    return current;
  }

  /**
   * 删除
   *
   * @param ids
   */
  @Override
  @Transactional
  public void delete(List<String> ids) {
    Validate.isTrue(CollectionUtils.isNotEmpty(ids), "id集合不能为空");
    List<Region> regionList = this.regionRepository.findByIds(ids);
    for(Region item : regionList){
      //找到子集
      int parentSize = this.regionRepository.lambdaQuery()
              .eq(Region::getParentCode, item.getRegionCode())
              .eq(Region::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
              .count();
      Validate.isTrue(parentSize <= 0 , "区域名称:【" + item.getRegionName() +"】存在【"+parentSize+"】条下级关系，禁止删除!");
    }
    this.regionRepository.updateDeleteStatusByIds(ids);
    if (CollectionUtils.isNotEmpty(regionEventListenerList)) {
      List<Region> regions = this.regionRepository.findByIds(ids);
      List<RegionVo> voList =
              (List<RegionVo>)
                      this.nebulaToolkitService.copyCollectionByWhiteList(
                              regions, Region.class, RegionVo.class, HashSet.class, ArrayList.class);
      for (RegionEventListener event : regionEventListenerList) {
        event.onDelete(voList);
      }
    }
  }

  /**
   * 启用
   *
   * @param ids
   */
  @Override
  @Transactional
  public void enableBatch(List<String> ids) {
    Validate.isTrue(CollectionUtils.isNotEmpty(ids), "id集合不能为空");
    this.regionRepository.updateEnableStatusByIds(ids, EnableStatusEnum.ENABLE);
  }

  /**
   * 禁用
   *
   * @param ids
   */
  @Override
  @Transactional
  public void disableBatch(List<String> ids) {
    Validate.isTrue(CollectionUtils.isNotEmpty(ids), "id集合不能为空");
    this.regionRepository.updateEnableStatusByIds(ids, EnableStatusEnum.DISABLE);
    if (CollectionUtils.isNotEmpty(regionEventListenerList)) {
      List<Region> regions = this.regionRepository.findByIds(ids);
      List<RegionVo> voList =
              (List<RegionVo>)
                      this.nebulaToolkitService.copyCollectionByWhiteList(
                              regions, Region.class, RegionVo.class, HashSet.class, ArrayList.class);
      for (RegionEventListener event : regionEventListenerList) {
        event.onDisable(voList);
      }
    }
  }

  @Override
  public List<Region> findRegionSelect(String parentCode, String regionName) {
    return regionRepository.findByParentCodeAndRegionNameLike(parentCode, regionName);
  }

  @Override
  public List<Region> findByParentCodeAndRegionNameLikeForOrder(String parentCode, String sapProvinceCode, String regionName) {
    return regionRepository.findByParentCodeAndRegionNameLikeForOrder(parentCode, sapProvinceCode, regionName);
  }

  @Override
  public List<Region> findDetailByCodes(List<String> regionCodeList) {
    if (CollectionUtils.isEmpty(regionCodeList)) {
      return new ArrayList<>(0);
    }
    return regionRepository.findDetailByCodes(regionCodeList);
  }

  @Override
  public List<Region> findSelfAndLowerByRuleCode(String ruleCode) {
    if (StringUtils.isBlank(ruleCode)) {
      return new ArrayList<>(0);
    }
    return regionRepository.findSelfAndLowerByRuleCode(ruleCode);
  }

  /**
   * 根据当前组织编码查询下级组织信息
   *
   * @param parentCode
   * @return
   */
  @Override
  public List<Region> findChildByParentCode(String parentCode) {
    String tenantCode = TenantUtils.getTenantCode();
    tenantCode = StringUtils.isBlank(tenantCode) ? "default" : tenantCode;
    List<Region> list = this.regionRepository.findChildByParentCode(parentCode, tenantCode);
    list.stream()
            .peek(a -> a.setHasChild(a.getHasChildFlag()==1))
            .collect(Collectors.toList());

    return list;
  }

  /**
   * 更新自己及子集的降维编码
   *
   * @param regionCode
   * @param ruleCode
   * @param levelNum
   */
  @Transactional
  public void updateCurAndChildrenRuleCode(String regionCode, String ruleCode, int levelNum) {
    // 更新当前
    Region region = this.regionRepository.findByRegionCode(regionCode);
    region.setRuleCode(ruleCode);
    region.setLevelNum(levelNum);
    //重构修改方法
    this.regionRepository.updateByIdAndTenantCode(region,TenantUtils.getTenantCode());
    // 查询下一层
    List<Region> childrenList = regionRepository.findByParentCode(region.getRegionCode());
    if (org.springframework.util.CollectionUtils.isEmpty(childrenList)) {
      return;
    }
    // 遍历下级
    TreeRuleCodeStrategy treeRuleCodeStrategy = this.treeRuleCodeStrategyHolder.getStrategy(null);
    for (int i = 0; i < childrenList.size(); i++) {
      // 递归调用
      updateCurAndChildrenRuleCode(
              childrenList.get(i).getRegionCode(),
              ruleCode + treeRuleCodeStrategy.generateByNum(RegionConstant.RULE_CODE_LENGTH, i + 1),
              levelNum + 1);
    }
  }

  /**
   * 根据集合返回page信息
   *
   * @param list
   * @return
   */
  private Page<Region> getPageRegionByList(List<Region> list) {
    if (CollectionUtils.isEmpty(list)) {
      list = Lists.newLinkedList();
    }
    Page<Region> page = new Page<>();
    page.setTotal(list.size());
    page.setSize(list.size());
    page.setRecords(list);
    return page;
  }

  /**
   * 省份不需要获取上级ruleCode 默认依次排序
   * @return
   */
  private String getRuleCodeByProvince(){
    String ruleCode = null;
    List<Region> regionList = this.regionRepository.lambdaQuery()
            .eq(Region::getRegionLevel, RegionLevelEnum.ONE.getDictCode())
            .eq(Region::getDelFlag, DelFlagStatusEnum.NORMAL.getCode())
            .eq(Region::getTenantCode, TenantUtils.getTenantCode())
            .orderByAsc(Region::getRuleCode)
            .list();
    //获取当前层级的ruleCode 去重
    List<String> codeList = regionList.stream().map(Region::getRuleCode).collect(Collectors.toList());
    Set<Integer> set = new HashSet<>();
    for(String item : codeList){
      set.add(Integer.parseInt(item));
    }
    //ruleCode 在当前层级最大值下 寻找空位
    for (int i = 1; i <= MAX_RULECODE_IN_ONE_Level; i++) {
      Validate.isTrue(i != MAX_RULECODE_IN_ONE_Level, "降维编码越界，请联系管理员处理");
      if (!set.contains(i)) {
        ruleCode =  String.format("%03d", i);
        break;
      }
    }
    return ruleCode;
  }



  /**
   * 根据父级编码获得当前降维编码
   *
   * @param parentCode
   * @return
   */
  private String getRuleCodeByParentCode(String parentCode) {
    parentCode = Optional.ofNullable(parentCode).orElse(StringUtils.EMPTY);
    String parentRuleCode = null;
    if (StringUtils.isNotEmpty(parentCode)) {
      Region parent = this.regionRepository.findByRegionCode(parentCode);
      Validate.notNull(parent, "上级行政区域不存在");
      parentRuleCode = parent.getRuleCode();
    }
    List<Region> childrenListByParentCode = this.regionRepository.findByParentCode(parentCode);
    List<TreeDto> childrenDto =
            Lists.newArrayList(
                    nebulaToolkitService.copyCollectionByWhiteList(
                            childrenListByParentCode,
                            Region.class,
                            TreeDto.class,
                            HashSet.class,
                            ArrayList.class));
    TreeRuleCodeStrategy treeRuleCodeStrategy = this.treeRuleCodeStrategyHolder.getStrategy(null);
    return treeRuleCodeStrategy.generate(
            RegionConstant.RULE_CODE_LENGTH, parentRuleCode, childrenDto);
  }
}
