package com.biz.crm.mdm.business.region.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 行政区域分页查询DTO
 *
 * <AUTHOR>
 * @date 2021/10/8
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "行政区域分页查询DTO")
public class RegionPaginationDto extends TenantFlagOpDto {

  @ApiModelProperty("CRM行政区域编码")
  private String regionCode;

  @ApiModelProperty("CRM行政区域名称")
  private String regionName;

  @ApiModelProperty("CRM上级行政区域编码")
  private String parentCode;

  @ApiModelProperty("标签集合")
  private List<String> labelList;

  @ApiModelProperty("区域等级")
  private String regionLevel;
}
