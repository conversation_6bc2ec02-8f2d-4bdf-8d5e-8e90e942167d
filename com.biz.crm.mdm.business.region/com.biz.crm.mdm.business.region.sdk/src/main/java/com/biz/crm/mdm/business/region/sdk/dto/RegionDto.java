package com.biz.crm.mdm.business.region.sdk.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 行政区域dto
 *
 * <AUTHOR>
 * @date 2021/10/8
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "RegionDto", description = "行政区域dto")
public class RegionDto extends TenantFlagOpDto {

  @ApiModelProperty("CRM行政区域编码")
  private String regionCode;

  @ApiModelProperty("CRM上级行政区域编码")
  private String parentCode;

  @ApiModelProperty("CRM上级行政区域名称")
  private String parentName;

  @ApiModelProperty("行政区域名称")
  private String regionName;

  @ApiModelProperty("行政区域层级")
  private Integer regionLevel;

  @ApiModelProperty("区域区域层级名称")
  private String regionLevelName;

  @ApiModelProperty("邮编")
  private String email;

  @ApiModelProperty("经度")
  private BigDecimal longitude;

  @ApiModelProperty("纬度")
  private BigDecimal latitude;

  @ApiModelProperty("是否有子节点")
  private Boolean hasChild;

  @ApiModelProperty("是否有子节点 1是 0否")
  private Integer hasChildFlag;

  @ApiModelProperty("国家统计局行政区域编码")
  private String govRegionCode;

  @ApiModelProperty("国家统计局上级行政区域编码")
  private String govParentCode;

  @ApiModelProperty("国家统计局URL")
  private String govUrl;

  @ApiModelProperty("SAP省编码")
  private String sapProvinceCode;
}
