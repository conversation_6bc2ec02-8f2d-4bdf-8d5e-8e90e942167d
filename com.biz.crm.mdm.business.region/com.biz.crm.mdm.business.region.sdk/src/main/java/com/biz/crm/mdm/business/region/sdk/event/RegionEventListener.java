package com.biz.crm.mdm.business.region.sdk.event;

import com.biz.crm.mdm.business.region.sdk.vo.RegionVo;
import java.util.List;

/**
 * 行政区域信息变更事件通知接口
 *
 * <AUTHOR>
 * @date 2021/10/9
 */
public interface RegionEventListener {

  /**
   * 当行政区域信息修改时触发
   *
   * @param oldVo
   * @param newVo
   */
  default void onChange(RegionVo oldVo, RegionVo newVo) {}

  /**
   * 当禁用时触发
   *
   * @param voList
   */
  default void onDisable(List<RegionVo> voList) {}

  /**
   * 当禁用时触发
   *
   * @param voList
   */
  default void onDelete(List<RegionVo> voList) {}
}
